import React from 'react';
import { Box, Button } from '@mui/material';
import './customButton.scss';

const CustomButton = ({
  children,
  variant = 'contained',
  color = 'primary',
  size = 'medium',
  fullWidth = false,
  disabled = false,
  startIcon,
  endIcon,
  onClick,
  className = '',
  type = 'button',
  title,
  isIconOnly = false,
  ...props
}) => {
  return (
    <Box className="custom-button-wrapper">
      <Button
        variant={variant}
        color={color}
        size={size}
        fullWidth={fullWidth}
        disabled={disabled}
        startIcon={startIcon}
        endIcon={endIcon}
        onClick={onClick}
        className={`custom-btn ${className} ${isIconOnly ? 'icon-only' : ''}`}
        type={type}
        {...props}
      >
        {title || children}
      </Button>
    </Box>
  );
};

export default CustomButton;
