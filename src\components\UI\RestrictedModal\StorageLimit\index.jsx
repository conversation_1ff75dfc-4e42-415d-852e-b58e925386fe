// src/pages/storage-limit-dialog/index.jsx
import React from 'react';
import Storagefull from '../../../../../public/images/Storagefull.jpg';
import Image from 'next/image';
import CustomButton from '@/components/UI/CustomButton';
import { Box, Typography } from '@mui/material';
import { useRouter } from 'next/navigation';
import './storagelimit.scss';

const StorageLimitDialog = ({
  isOrgView,
  setRestrictedModal,
  restrictedLimitModal,
}) => {
  const router = useRouter();
  const handleUpgradeStorage = () => {
    if (isOrgView) {
      setRestrictedModal(false);
      router.push('/org/organization?tab=2');
    } else {
      setRestrictedModal(false);
    }
  };
  const handleGoBack = () => {
    setRestrictedModal(false);
  };
  // Calculate storage percentage
  const usedStorage = restrictedLimitModal?.usedStorage || 0;
  const totalStorage = restrictedLimitModal?.totalStorage || 0;
  const storagePercentage =
    totalStorage > 0 ? Math.min((usedStorage / totalStorage) * 100, 100) : 0;

  return (
    <Box className="storage-limit-container">
      {/* Dialog Overlay */}
      <Box className={`dialog-overlay  visible`}>
        <Box className="dialog-container">
          <Box className={`dialog-content visible`}>
            <Box className="dialog-body pt0">
              {/* Image */}
              <Box className="dialog-image-container mb0">
                <Box className="dialog-image-wrapper">
                  <Image
                    src={Storagefull}
                    alt="Cloud storage and data management"
                    className="dialog-image"
                  />
                </Box>
              </Box>

              {/* Text Content */}
              <Box className="dialog-text">
                <Typography className="title-text fw600 dialog-message">
                  You've reached your storage limit of{' '}
                  {restrictedLimitModal?.totalStorage || 0} GB.
                </Typography>
                <Typography className="title-text dialog-description">
                  Free up space by deleting old files or upgrade for more
                  storage.
                </Typography>
                <Typography className="title-text dialog-description">
                  Your work can continue without interruption once space is
                  available.
                </Typography>
              </Box>

              {/* Storage Progress */}
              <Box className="storage-status">
                <Box className="storage-status-header">
                  <span className="title-text fw600 storage-status-label">
                    Storage Usage
                  </span>
                  <span className="sub-title-text fw600 storage-status-value">
                    {restrictedLimitModal?.usedStorage || 0} GB /{' '}
                    {restrictedLimitModal?.totalStorage || 0} GB
                  </span>
                </Box>
                <Box className="storage-status-progress-container">
                  <Box
                    className="storage-status-progress-bar"
                    style={{ width: `${storagePercentage}%` }}
                  ></Box>
                </Box>
              </Box>

              {/* Action Buttons */}
              <Box className="dialog-actions profile-incomplete-actions">
                <CustomButton
                  variant="contained"
                  title={isOrgView ? 'Upgrade Plan' : 'Go Back'}
                  fullWidth
                  onClick={handleUpgradeStorage}
                />
                {isOrgView && (
                  <CustomButton
                    variant="outlined"
                    fullWidth
                    title="Go Back"
                    onClick={handleGoBack}
                  />
                )}
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default StorageLimitDialog;
