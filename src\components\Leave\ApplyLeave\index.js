'use client';
import React, { useEffect, useState, useContext, useRef } from 'react';
import { useSearchParams } from 'next/navigation';
import { Box, Chip, Divider, Typography } from '@mui/material';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomButton from '@/components/UI/CustomButton';
import CustomDatePicker from '@/components/UI/CustomDatePicker';
import dayjs from 'dayjs';
import { differenceInDays } from 'date-fns';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import AuthContext from '@/helper/authcontext';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { useRouter } from 'next/navigation';
import { setApiMessage } from '@/helper/common/commonFunctions';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import CustomDoubleCalender from '@/components/UI/DoubleCalender';
import moment from 'moment';
import ApplyLeaveCard from '@/components/Leave/MyLeave/ApplyLeaveCard';
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';
import utc from 'dayjs/plugin/utc';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import CustomSelect from '@/components/UI/CustomSelect';
import {
  checkValidations,
  getDatesInRange,
  checkHoliday,
  getHolidaysWeekends,
  checkWeekends,
} from './leavefunctions';
import './applyleave.scss';

export default function ApplyLeave({ isLoader }) {
  const formikRef = useRef(null);
  const { authState } = useContext(AuthContext);
  const [roleList, setRoleList] = useState([]);
  const [leaveList, setLeaveList] = useState([]);
  const [selectedLeave, setSelectedLeave] = useState();
  const currentYear = moment().year();
  const [showAll, setShowAll] = useState(false);
  const [filteredEvents, setFilteredEvents] = useState([]);
  const [selectedDate, setSelectedDate] = useState(null);
  const [searchValue, setSearchValue] = useState('');
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [leaveBalanceDetails, setLeaveBalanceDetails] = useState([]);
  // const [setLeavePolicyEndDate] = useState(null);
  const [yearFilterdata, setYearFilterData] = useState(currentYear);
  const searchParams = useSearchParams();
  const [selectedWeekOff, setSelectedWeekOff] = useState({});
  const [leaveHours, setLeaveHours] = useState('total');
  const [dateRange, setDateRange] = useState([]);
  // const [isSubmit, setIsSubmit] = useState(false);
  const [userHolidayList, setUserHolidayList] = useState([]);
  // const [sandwichRule, setSandwichRule] = useState([]);
  // const [leaveDurationType, setLeaveDurationType] = useState('Days');
  // const [loader, setLoader] = useState(false);

  const isRotas = searchParams.get('is_rota');

  const daysOfWeek = [
    {
      name: 'Sunday',
      dayOff: authState?.user_week_day?.['sunday'] === 'dayoff',
    },
    {
      name: 'Monday',
      dayOff: authState?.user_week_day?.['monday'] === 'dayoff',
    },
    {
      name: 'Tuesday',
      dayOff: authState?.user_week_day?.['tuesday'] === 'dayoff',
    },
    {
      name: 'Wednesday',
      dayOff: authState?.user_week_day?.['wednesday'] === 'dayoff',
    },
    {
      name: 'Thursday',
      dayOff: authState?.user_week_day?.['thursday'] === 'dayoff',
    },
    {
      name: 'Friday',
      dayOff: authState?.user_week_day?.['friday'] === 'dayoff',
    },
    {
      name: 'Saturday',
      dayOff: authState?.user_week_day?.['saturday'] === 'dayoff',
    },
  ];
  const dayMenuOptions = [
    { label: 'Full Day', value: 'full_day' },
    { label: '1st Half', value: 'first_half' },
    { label: '2nd Half', value: 'second_half' },
  ];
  const [calenderData, setCalenderData] = useState({
    coloredEvents: [],
    holidayEvents: [],
    allEvents: [],
  });
  const [visibleItems, setVisibleItems] = useState(
    window.innerWidth > 1600 ? 3 : 2
  );
  const router = useRouter();
  dayjs.extend(utc);
  dayjs.extend(localizedFormat);

  const leaveId = searchParams.get('leaveId');
  const leavePeriodType = searchParams.get('leavePeriod');
  const leave_type = authState?.generalSeetings?.leave_period_type
    ? authState?.generalSeetings?.leave_period_type
    : 'day';
  // List of Leaves
  const getLeaveList = async (id) => {
    // setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.USER_LEAVE_POLICIES_BALANCE + `${id}?leave_period_type=` //dType === 'Days' ? 'day' : dType === 'Hours' ? 'hour' : ''
      );

      if (status === 200) {
        // setLoader(false);
        let filterUserList = data?.data?.map((user) => ({
          ...user,
          label: user?.name,
          value: user?.id,
        }));
        filterUserList && filterUserList?.length > 0
          ? setLeaveList(filterUserList)
          : setLeaveList([]);
      }
    } catch (error) {
      // setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const getSingleUserBalance = async (id, year) => {
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.LEAVE_BALANCE + `${id}?year=${year}&leave_period_type=`
      );
      if (status === 200) {
        // const transformedData = data?.data?.map((item) => ({
        //   leave_policy_end_date:
        //     item.leave_accural_policy?.leave_policy_end_date || null,
        // }));

        setLeaveBalanceDetails(data?.data);
        // setLeavePolicyEndDate(transformedData);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const isLeaveStartDateValid = (leaveType) => {
    // Convert startDate (assuming it's in YYYY-MM-DD format) to a Day.js object
    if (!leaveType) {
      return false;
    }
    const startDateTime = dayjs(startDate, 'YYYY-MM-DD');
    const endDateTime = dayjs(endDate, 'YYYY-MM-DD');

    // Convert leave_policy_end_date to a Day.js object in local time
    const localLeavePolicyEndDate = dayjs
      .utc(selectedLeave?.leave_accural_policy?.leave_policy_end_date)
      .local(); // Converts from UTC to local time

    return (
      startDateTime.isAfter(localLeavePolicyEndDate) ||
      endDateTime.isAfter(localLeavePolicyEndDate)
    ); // Returns true if startDate is greater
  };

  const getFormattedEndDate = () => {
    return dayjs
      .utc(selectedLeave?.leave_accural_policy?.leave_policy_end_date)
      .local()
      .format('DD/MM/YYYY'); // Format to 'DD/MM/YYYY'
  };

  const getLeaveDetails = async (view) => {
    if (!view && !calenderSearch) return;
    try {
      let apiUrl = `${URLS?.CALENDER_WISE_LEAVE}?list_type=staff&search=`;

      if (view?.Start && view?.End) {
        apiUrl += `&start_date=${view?.Start}&end_date=${view?.End}`;
      } else if (view?.Start) {
        apiUrl += `&start_date=${view?.Start}`;
      } else if (view?.End) {
        apiUrl += `&end_date=${view?.End}`;
      }

      const { status, data } = await axiosInstance.get(apiUrl);

      if (status === 200) {
        // const coloredEvents = data?.data?.calenderLeaves?.map((event) => ({
        //   ...event,
        //   start: moment.utc(event?.start_date).local().format('YYYY-MM-DD'),
        //   end: event?.end_date
        //     ? moment.utc(event?.start_date).local().format('YYYY-MM-DD') ===
        //       moment.utc(event?.end_date).local().format('YYYY-MM-DD')
        //       ? moment.utc(event?.end_date).local().format('YYYY-MM-DD')
        //       : event?.duration_type === 'Hours'
        //         ? moment.utc(event?.end_date).local().format('YYYY-MM-DD')
        //         : moment
        //             .utc(event?.end_date)
        //             .add(1, 'day')
        //             .local()
        //             .format('YYYY-MM-DD')
        //     : moment.utc(event?.start_date).local().format('YYYY-MM-DD'),
        //   allDay: true,
        //   eventType: 'leave',
        // }));
        const coloredEvents = data?.data?.calenderLeaves?.map((event) => {
          return {
            ...event,
            start: moment(event?.start_date).format('YYYY-MM-DD'),
            end: event?.end_date
              ? moment(event?.end_date).format('YYYY-MM-DD')
              : moment(event?.start_date).format('YYYY-MM-DD'),
            allDay: true,
            eventType: 'leave',
          };
        });
        const holidayEvents = data?.data?.holidayList?.map((event) => {
          return {
            ...event,
            id: `holiday-${event?.id}`,
            start: moment(event?.holiday_policy_start_date).format(
              'YYYY-MM-DD'
            ),
            end: moment(event?.holiday_policy_end_date).format('YYYY-MM-DD'),
            title: event?.holiday_policy_name,
            backgroundColor: event?.holiday_policy_colour,
            borderColor: event?.holiday_policy_colour,
            allDay: true,
            eventType: 'holiday',
          };
        });

        setCalenderData({
          coloredEvents,
          holidayEvents,
          allEvents: [...coloredEvents, ...holidayEvents],
        });
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const Inputype = (e, types) => {
    let value = e.target.value;
    if (types === 'hour') {
      {
        // Remove non-numeric characters
        if (value === '' || /^\d*\.?\d{0,2}$/.test(value)) {
          e.target.value = value;
        } else {
          e.target.value = value.slice(0, -1);
        }
      }
    } else {
      {
        e.target.value = e.target.value.replace(/[^0-9]/g, ''); // Remove non-numeric characters
      }
      // {
      //   if (value === '' || /^\d*\.?\d{0,2}$/.test(value)) {
      //     e.target.value = value;
      //   } else {
      //     e.target.value = value.slice(0, -1);
      //   }
      // }
    }
  };

  const getUserHolidayList = async (id, sdate, edate) => {
    try {
      const { status, data } = await axiosInstance.get(
        `${URLS?.GET_USER_HOLIDAY_POLICY}?user_id=${id}&search=&start_date=${dayjs(sdate)?.format('YYYY-MM-DD')}&end_date=${dayjs(edate)?.format('YYYY-MM-DD')}`
      );
      if (status === 200) {
        setUserHolidayList(data?.data || []);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  useEffect(() => {
    if (startDate && endDate) {
      const dateRanges = getDatesInRange(startDate, endDate);
      if (
        dayjs(startDate).format('YYYY-MM-DD') !== dateRange?.[0] ||
        dayjs(endDate).format('YYYY-MM-DD') !==
          dateRanges?.[dateRanges?.length - 1]
      ) {
        getUserHolidayList(authState?.id, startDate, endDate);
      }
      const workingHoursPerDay =
        authState?.generalSeetings?.working_hours_per_day;
      formikRef?.current?.setFieldValue('leaveday', '');
      if (leave_type === 'hour' && workingHoursPerDay) {
        let weeks = {};
        dateRanges?.map((date) => {
          if (
            !checkWeekends(date, daysOfWeek) &&
            !checkHoliday(date, userHolidayList)
          ) {
            weeks[date] = workingHoursPerDay;
          }
          return date;
        });
        setSelectedWeekOff(weeks);
        formikRef?.current?.setFieldValue('selectedWeekOff', weeks);
        //  const getWeekOfHoliday = getHolidaysWeekends(dateRanges);
        // const workingdays = dateRanges?.length - getWeekOfHoliday?.length;
        // formikRef?.current?.setFieldValue(
        //   'leaveday',
        //   workingdays * workingHoursPerDay
        // );
      } else if (leave_type === 'day') {
        let weeks = {};
        dateRanges?.map((date) => {
          if (
            !checkWeekends(date, daysOfWeek) &&
            !checkHoliday(date, userHolidayList)
          ) {
            weeks[date] = 'full_day';
          }
          return date;
        });
        setSelectedWeekOff(weeks);
        formikRef?.current?.setFieldValue('selectedWeekOff', weeks);
      }
      setDateRange(dateRanges);
    } else {
      setDateRange([]);
    }
  }, [startDate, endDate, leaveHours]);
  // useEffect(() => {
  //   if (startDate && endDate && selectedLeave) {
  //     const dateRanges = getDatesInRange(startDate, endDate);
  //     //SandwichRules;
  //     const result = applySandwichRule(
  //       // [dateRanges?.[0], dateRanges?.[dateRanges?.length - 1]],
  //       dateRanges,
  //       getHolidays(dateRanges),
  //       JSON.parse(
  //         selectedLeave?.leave_accural_policy?.holiday_between_leave_period_data
  //       )?.holiday_accompaying_days,
  //       JSON.parse(
  //         selectedLeave?.leave_accural_policy?.holiday_between_leave_period_data
  //       )?.holiday_accompaying_days,
  //       JSON.parse(
  //         selectedLeave?.leave_accural_policy?.holiday_between_leave_period_data
  //       )?.apply_sandwich_before_holiday,
  //       JSON.parse(
  //         selectedLeave?.leave_accural_policy?.holiday_between_leave_period_data
  //       )?.apply_sandwich_after_holiday,
  //       JSON.parse(
  //         selectedLeave?.leave_accural_policy?.holiday_between_leave_period_data
  //       )?.apply_sandwich_between_holiday
  //     );
  //     setSandwichRule(result);
  //   } else {
  //     setSandwichRule([]);
  //   }
  // }, [startDate, endDate, selectedLeave]);
  useState(() => {
    if (formikRef && leaveId) {
      // getLeaveList(
      //   authState?.id,
      //   leavePeriodType === 'day'
      //     ? 'Days'
      //     : leavePeriodType === 'hour'
      //       ? 'Hours'
      //       : 'Days'
      // );
      formikRef?.current?.setFieldValue(
        'leavetype',
        Number(leaveId) ? Number(leaveId) : ''
      );
      // formikRef?.current?.setFieldValue(
      //   'leave_dur_type',
      //   leavePeriodType === 'day'
      //     ? 'Days'
      //     : leavePeriodType === 'hour'
      //       ? 'Hours'
      //       : 'Days'
      // );

      setShowAll(true);
    }
  }, [leaveId, leavePeriodType, formikRef?.current]);

  useEffect(() => {
    if (authState?.id) {
      getSingleUserBalance(authState?.id, yearFilterdata);
      getLeaveList(
        authState?.id,
        leavePeriodType === 'day'
          ? 'Days'
          : leavePeriodType === 'hour'
            ? 'Hours'
            : 'Days'
      );
      setRoleList(authState?.user_roles);
    }
    if (formikRef && authState) {
      formikRef.current.setFieldValue(
        'role',
        authState && authState?.web_user_active_role_id
          ? authState?.web_user_active_role_id
          : ''
      );
    }
  }, [authState?.id, authState?.user_roles]);
  useEffect(() => {
    const handleResize = () => {
      setVisibleItems(window.innerWidth > 1600 ? 3 : 2);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  return (
    <Box>
      <Box className="apply-leave-center">
        <Formik
          innerRef={formikRef}
          initialValues={{
            subject: '',
            leavetype: Number(leaveId) ? Number(leaveId) : '',
            remark: '',
            sdate: '',
            edate: '',
            leaveday: '',
            role:
              authState && authState?.web_user_active_role_id
                ? authState?.web_user_active_role_id
                : '',
            selectedWeekOff: {},
            // leave_dur_type:
            //   leavePeriodType === 'day'
            //     ? 'Days'
            //     : leavePeriodType === 'hour'
            //       ? 'Hours'
            //       : 'Days',
          }}
          enableReinitialize={false}
          validationSchema={Yup.object().shape({
            subject: Yup.string().trim().required('This field is required'),
            leavetype: Yup.string().trim().required('This field is required'),
            sdate: Yup.string().trim().required('This field is required'),
            edate: Yup.date()
              .required('This field is required')
              .test(
                'end-date-balance',
                'The selected period exceeds your available balance',
                function (value) {
                  const { sdate } = this.parent;
                  if (!sdate || !value) return true;

                  const startDate = new Date(sdate);
                  const endDate = new Date(value);
                  if (leave_type === 'day') {
                    // const allowedDays =
                    //   differenceInDays(endDate, startDate) + 1;
                    const allowedDayss =
                      differenceInDays(endDate, startDate) + 1;
                    const getWeekOfHoliday = getHolidaysWeekends(
                      dateRange,
                      daysOfWeek,
                      userHolidayList
                    );
                    const allowedDays = allowedDayss - getWeekOfHoliday?.length;
                    if (selectedLeave?.has_leave_unlimited) {
                      return true;
                    }
                    if (
                      allowedDays > selectedLeave?.leave_balance &&
                      !selectedLeave?.has_leave_unlimited
                    ) {
                      return this.createError({
                        message: `Your leave balance is only ${selectedLeave?.leave_balance} days.`,
                      });
                    }
                    return true;
                  } else if (leave_type === 'hour') {
                    const allowedHourss = endDate - startDate;
                    const allowedHoursss = allowedHourss / (1000 * 60 * 60);
                    const getWeekOfHoliday = getHolidaysWeekends(
                      dateRange,
                      daysOfWeek,
                      userHolidayList
                    );
                    const allowedHours =
                      allowedHoursss - getWeekOfHoliday?.length * 24;
                    if (selectedLeave?.has_leave_unlimited) {
                      return true;
                    }
                    if (
                      allowedHours > selectedLeave?.leave_balance &&
                      !selectedLeave?.has_leave_unlimited
                    ) {
                      return this.createError({
                        message: `Your leave balance is only ${selectedLeave?.leave_balance} hours.`,
                      });
                    }
                    return true;
                  }

                  return true;
                }
              ),
            remark: Yup.string().trim().required('This field is required'),
            role: Yup.string().trim().required('This field is required'),
            leaveday:
              leaveHours === 'total' &&
              Yup.string()
                .trim()
                .required('This field is required')
                .test(
                  'leave-day-match',
                  'Please select valid time',
                  function (value) {
                    const { sdate, edate } = this.parent;
                    // selectedWeekOff const hasSelectedWeekOff =
                    //   selectedWeekOff &&
                    //   Object.keys(selectedWeekOff).length > 0;

                    // if (hasSelectedWeekOff) return true;
                    // if (!hasSelectedWeekOff && !value) {
                    //   return this.createError({
                    //     message: 'This field is required',
                    //   });
                    // }
                    if (value === '0') {
                      return this.createError({
                        message: `You can't apply for leave with 0 ${leave_type} selected.`,
                      });
                    }
                    if (!sdate || !edate || !value) return true;

                    const startDate = new Date(sdate);
                    const endDate = new Date(edate);
                    if (leave_type === 'day') {
                      const leaveDays = parseFloat(value);
                      const allowedDayss =
                        differenceInDays(endDate, startDate) + 1;
                      const getWeekOfHoliday = getHolidaysWeekends(
                        dateRange,
                        daysOfWeek,
                        userHolidayList
                      );
                      const allowedDays =
                        allowedDayss - getWeekOfHoliday?.length;
                      // if (selectedLeave?.has_leave_unlimited) {
                      //   return true;
                      // }
                      if (
                        leaveDays > selectedLeave?.leave_balance &&
                        !selectedLeave?.has_leave_unlimited
                      ) {
                        return this.createError({
                          message: `Your leave balance is only ${selectedLeave?.leave_balance} days.`,
                        });
                      }
                      if (leaveDays > allowedDays) {
                        if (allowedDayss > 0 && allowedDays === 0) {
                          return this.createError({
                            message: `You cannot apply for leave on a holiday or scheduled day off.`,
                          });
                        }
                        return this.createError({
                          message: `You can only take up to ${allowedDays} days. Please ensure holidays and day offs are not included.`,
                        });
                      }
                      return true;
                    } else if (leave_type === 'hour') {
                      const leaveHours = parseFloat(value);
                      const allowedHourss = endDate - startDate;
                      const allowedHoursss =
                        allowedHourss / (1000 * 60 * 60) + 24;
                      const getWeekOfHoliday = getHolidaysWeekends(
                        dateRange,
                        daysOfWeek,
                        userHolidayList
                      );
                      const allowedHours =
                        allowedHoursss - getWeekOfHoliday?.length * 24;

                      // if (selectedLeave?.has_leave_unlimited) {
                      //   return true;
                      // }
                      if (
                        leaveHours > selectedLeave?.leave_balance &&
                        !selectedLeave?.has_leave_unlimited
                      ) {
                        return this.createError({
                          message: `Your leave balance is only ${selectedLeave?.leave_balance} hours.`,
                        });
                      }
                      if (leaveHours > allowedHours) {
                        if (allowedHoursss > 0 && allowedHours === 0) {
                          return this.createError({
                            message: `You cannot apply for leave on a holiday or scheduled day off.`,
                          });
                        }
                        return this.createError({
                          message: `You can only take up to ${parseFloat(
                            allowedHours.toFixed(2)
                          )} hours. Please ensure holidays and day offs are not included.`,
                        });
                      }
                    }
                    return true;
                  }
                ),
            selectedWeekOff:
              leaveHours === 'daily' &&
              Yup.object(
                dateRange?.reduce((acc, date) => {
                  acc[date] =
                    checkWeekends(date, daysOfWeek) ||
                    checkHoliday(date, userHolidayList)
                      ? Yup.string().nullable()
                      : leave_type === 'day' &&
                          selectedLeave?.leave_balance === 0 &&
                          !selectedLeave?.has_leave_unlimited
                        ? Yup.string()
                            .required('This field is required')
                            .test(
                              'max-hours',
                              `Your leave balance is only ${selectedLeave?.leave_balance} days.`,
                              (val) => {
                                if (!val) return true; // required handles empty

                                const num = parseFloat(7);
                                return num <= selectedLeave?.leave_balance;
                              }
                            )
                        : leave_type === 'hour' &&
                            selectedLeave?.leave_balance < 24 &&
                            !selectedLeave?.has_leave_unlimited &&
                            (selectedLeave?.leave_balance ||
                              selectedLeave?.leave_balance === 0)
                          ? Yup.string()
                              .required('This field is required')
                              .test(
                                'max-hours',
                                `Your leave balance is only ${selectedLeave?.leave_balance} hours.`,
                                (val) => {
                                  if (!val) return true; // required handles empty
                                  const num = parseFloat(val);

                                  return (
                                    !isNaN(num) &&
                                    num <= selectedLeave?.leave_balance
                                  );
                                }
                              )
                          : leave_type === 'hour'
                            ? Yup.string()
                                .required('This field is required')
                                .test(
                                  'max-hours',
                                  'Cannot exceed 24hrs',
                                  (val) => {
                                    if (!val) return true; // required handles empty
                                    const num = parseFloat(val);
                                    return !isNaN(num) && num <= 24;
                                  }
                                )
                            : Yup.string().required('This field is required');
                  return acc;
                }, {})
              ),
          })}
          onSubmit={async (requestData) => {
            const datesarray = dateRange?.map((date) => {
              const isWeekend = checkWeekends(date, daysOfWeek);
              const isHoliday = checkHoliday(date, userHolidayList);

              if (leave_type === 'hour') {
                return {
                  date,
                  total_hours:
                    isWeekend || isHoliday
                      ? 0
                      : isNaN(parseFloat(requestData?.selectedWeekOff[date]))
                        ? parseFloat(requestData?.selectedWeekOff[date])
                        : requestData?.selectedWeekOff[date],
                  ...((isWeekend || isHoliday) && {
                    type:
                      isWeekend && isHoliday
                        ? 'day_off_holiday'
                        : isWeekend
                          ? 'day_off'
                          : 'holiday',
                  }),
                };
              }

              return {
                date,
                type:
                  isWeekend && isHoliday
                    ? 'day_off_holiday'
                    : isWeekend
                      ? 'day_off'
                      : isHoliday
                        ? 'holiday'
                        : requestData?.selectedWeekOff[date],
              };
            });
            const payload = {
              subject: requestData?.subject ? requestData?.subject : '',
              request_reason: requestData?.remark ? requestData?.remark : '',
              leave_request_type: requestData?.leavetype
                ? requestData?.leavetype
                : '',
              ...((requestData?.leaveday || requestData?.leaveday === 0) &&
                leaveHours === 'total' && {
                  leave_days: requestData?.leaveday,
                }),
              start_date:
                // requestData?.leave_dur_type === 'Hours' && requestData?.sdate
                //   ? dayjs(requestData?.sdate)?.format('YYYY-MM-DD HH:mm:ss')
                //   :
                requestData?.sdate
                  ? dayjs(requestData?.sdate)?.format('YYYY-MM-DD')
                  : '',
              end_date:
                // requestData?.leave_dur_type === 'Hours' && requestData?.edate
                //   ? dayjs(requestData?.edate)?.format('YYYY-MM-DD HH:mm:ss')
                //   :
                requestData?.edate
                  ? dayjs(requestData?.edate)?.format('YYYY-MM-DD')
                  : '',
              role_id: requestData?.role,
              // duration_type: requestData?.leave_dur_type,
              has_unlimited: selectedLeave?.has_leave_unlimited ? true : true,
              ...(leaveHours === 'daily' && {
                leave_days_obj: JSON.stringify(datesarray),
              }),
            };
            try {
              const { status, data } = await axiosInstance.post(
                URLS?.APPLY_LEAVE,
                payload
              );
              if (status === 200) {
                setApiMessage('success', data?.message);
                if (isRotas === 'true') {
                  router.back();
                } else {
                  router.push('/own-leave');
                }
              }
            } catch (error) {
              // setLoader(false);
              setApiMessage('error', error?.response?.data?.message);
            }
          }}
        >
          {({
            errors,
            touched,
            handleBlur,
            values,
            setFieldValue,
            handleSubmit,
            handleChange,
          }) => (
            <Form onSubmit={handleSubmit}>
              <Box className="apply-leave-title-wrap d-flex align-center">
                <ArrowBackIosIcon
                  className="cursor-pointer"
                  onClick={() => {
                    if (isRotas === 'true') {
                      router.back();
                    } else {
                      router.push('/own-leave');
                    }
                  }}
                />
                <Typography className="title-sm">Apply Leave</Typography>
              </Box>
              <Divider />

              <Box>
                {isLeaveStartDateValid(values?.leavetype) && (
                  <Typography className="title-text leave-policy-end-date d-flex align-center gap-5">
                    <FiberManualRecordIcon className="dot-wrap" />
                    {`The Leave Application date should be smaller than or equal to ${getFormattedEndDate()}`}
                  </Typography>
                )}
                <Box className="leave-form-wrap d-flex">
                  <Box className="left-side-leave-form-wrap">
                    <Box className="pt24">
                      <CustomTextField
                        fullWidth
                        id="subject"
                        name="subject"
                        value={values?.subject}
                        label="Subject"
                        required
                        placeholder="Enter subject"
                        error={Boolean(touched.subject && errors.subject)}
                        helperText={touched.subject && errors.subject}
                        onBlur={handleBlur}
                        onChange={(e) => {
                          if (event.target.value.length <= 60) {
                            handleChange(e);
                            // setIsSubmit(false);
                          }
                        }}
                      />
                      <Typography className="sub-title-text text-align-end">
                        {values?.subject?.length + '/60'}
                      </Typography>
                    </Box>
                    <Divider />

                    <Box className="pt8 pb8">
                      <CustomSelect
                        placeholder="System access"
                        options={roleList}
                        value={
                          roleList?.find((opt) => {
                            return opt?.value === values?.role;
                          }) || ''
                        }
                        name="role"
                        error={touched?.role && errors?.role}
                        helperText={touched?.role && errors?.role}
                        onChange={(e) => {
                          setFieldValue('role', e?.value);
                        }}
                        label={<span>System access</span>}
                        required
                      />
                    </Box>
                    <Divider />

                    <Box className="chip-select pt8 pb8">
                      <Typography
                        variant="body1"
                        className={`field-label ${touched?.leavetype && errors?.leavetype && !values?.leavetype && 'Mui-error'}`}
                      >
                        Leave type<span className="required"> *</span>
                      </Typography>

                      {leaveList?.length === 0 ? (
                        <>
                          <Typography
                            variant="body2"
                            color="error"
                            className="ml0 other-field-error-text  "
                          >
                            Please contact admin for assign leave policies.
                          </Typography>
                        </>
                      ) : (
                        <>
                          <Box className="chip-container d-flex justify-space-between">
                            <Box className="chip-wrap d-flex flex-wrap">
                              {leaveList
                                ?.slice(
                                  0,
                                  showAll ? leaveList?.length : visibleItems
                                ) // Dynamically adjust slice limit
                                .map((leave) => (
                                  <Chip
                                    key={leave?.value}
                                    label={leave?.label}
                                    clickable
                                    variant="outlined"
                                    onClick={() => {
                                      setFieldValue('leavetype', leave?.value);
                                      const dateRanges = getDatesInRange(
                                        startDate,
                                        endDate
                                      );
                                      const workingHoursPerDay =
                                        authState?.generalSeetings
                                          ?.working_hours_per_day;
                                      const weeks = {};

                                      // Build the weeks object
                                      dateRanges?.forEach((date) => {
                                        const isWeekend = checkWeekends(
                                          date,
                                          daysOfWeek
                                        );
                                        const isHoliday = checkHoliday(
                                          date,
                                          userHolidayList
                                        );

                                        if (!isWeekend && !isHoliday) {
                                          weeks[date] =
                                            leave_type === 'hour' &&
                                            workingHoursPerDay
                                              ? workingHoursPerDay
                                              : '';
                                        }
                                      });

                                      // Update form state
                                      setSelectedWeekOff(weeks);
                                      setFieldValue('selectedWeekOff', weeks);
                                      setDateRange(dateRanges);

                                      // Calculate leave days for hour-based leave type
                                      // if (
                                      //   leave_type === 'hour' &&
                                      //   workingHoursPerDay
                                      // ) {
                                      //   const nonWorkingDays =
                                      //     getHolidaysWeekends(dateRanges)
                                      //       ?.length || 0;
                                      //   const workingDays =
                                      //     dateRanges.length - nonWorkingDays;

                                      //   setFieldValue(
                                      //     'leaveday',
                                      //     workingDays * workingHoursPerDay
                                      //   );
                                      // } else {
                                      //   setFieldValue('leaveday', '');
                                      // }
                                      let leavs = { ...leave };
                                      leavs.leave_accural_policy = {
                                        ...leave?.leave_accural_policy,
                                      };
                                      leavs.leave_accural_policy.allow_leave_request_data =
                                        leave?.leave_accural_policy
                                          ?.allow_leave_request_data
                                          ? JSON.parse(
                                              leave.leave_accural_policy
                                                .allow_leave_request_data
                                            )
                                          : null;
                                      setSelectedLeave(leavs);
                                      // setIsSubmit(false);
                                    }}
                                    className={`chip ${touched?.leavetype && errors?.leavetype && !values?.leavetype ? 'chip-error' : ''} 
            ${values?.leavetype === leave?.value ? 'chip-selected' : ''}`}
                                  />
                                ))}
                            </Box>

                            <Box>
                              {leaveList?.length > visibleItems && (
                                <CustomButton
                                  onClick={() => setShowAll((prev) => !prev)}
                                  title={showAll ? 'Less' : 'More'}
                                  variant="outlined"
                                  endIcon={
                                    showAll ? (
                                      <ExpandLessIcon />
                                    ) : (
                                      <ExpandMoreIcon />
                                    )
                                  }
                                />
                              )}
                            </Box>
                          </Box>
                        </>
                      )}
                    </Box>
                    <Divider />
                    <Box className="date-picker-wrap pt24 pb8">
                      <Box>
                        <CustomDatePicker
                          label={<span>Start date (DD/MM/YYYY)</span>}
                          required
                          name="sdate"
                          error={Boolean(touched.sdate && errors.sdate)}
                          helperText={touched.sdate && errors.sdate}
                          value={dayjs(values?.sdate)}
                          onBlur={handleBlur}
                          onChange={(date) => {
                            setFieldValue('sdate', date); // Set Start Date
                            setStartDate(dayjs(date));
                            // setIsSubmit(false);
                            if (
                              values?.edate &&
                              dayjs(values?.edate).isBefore(date)
                            ) {
                              setFieldValue('edate', null); // Reset End Date only if it's before the new Start Date
                              setEndDate(null);
                            }
                          }}
                          // disablePast
                          inputVariant="outlined"
                        />
                      </Box>

                      <Box>
                        <CustomDatePicker
                          label={<span>End date (DD/MM/YYYY)</span>}
                          required
                          name="edate"
                          value={values?.edate ? dayjs(values?.edate) : null}
                          onBlur={handleBlur}
                          error={Boolean(touched.edate && errors.edate)}
                          helperText={touched.edate && errors.edate}
                          onChange={(date) => {
                            setFieldValue('edate', date);
                            setEndDate(dayjs(date));
                            // setIsSubmit(false);
                          }}
                          minDate={values?.sdate ? dayjs(values?.sdate) : null}
                          // disablePast
                          inputVariant="outlined"
                        />
                      </Box>
                    </Box>

                    <Divider />
                    {values?.sdate && values?.edate ? (
                      <>
                        <Box className="day-based-container d-flex justify-space-between">
                          <Box className="day-based-wrap d-flex flex-wrap">
                            <Chip
                              key={'total'}
                              label={
                                leave_type === 'hour'
                                  ? 'Total leave hours'
                                  : 'Total leave days'
                              }
                              clickable
                              variant="outlined"
                              onClick={() => {
                                setLeaveHours('total');
                              }}
                              className={`day-chip ${leaveHours === 'total' && 'selected-chip'}`}
                            />
                            <Chip
                              key={'day-based'}
                              label={
                                leave_type === 'hour'
                                  ? 'Daily leave hours'
                                  : 'Daily leave'
                              }
                              clickable
                              variant="outlined"
                              onClick={() => {
                                setLeaveHours('daily');
                              }}
                              className={`day-chip ${leaveHours === 'daily' && 'selected-chip'}`}
                            />
                          </Box>
                        </Box>
                        {leaveHours === 'total' ? (
                          <>
                            {' '}
                            <Box className="pt8 pb8">
                              <CustomTextField
                                fullWidth
                                id="leaveday"
                                name="leaveday"
                                value={values?.leaveday}
                                label={`How many ${
                                  leave_type === 'hour' ? 'hours' : 'days'
                                } do you want to leave?`}
                                required
                                placeholder={`Enter ${
                                  leave_type === 'hour' ? 'hours' : 'days'
                                }`}
                                error={Boolean(
                                  touched.leaveday && errors.leaveday
                                )}
                                helperText={touched.leaveday && errors.leaveday}
                                onBlur={handleBlur}
                                onChange={(e) => {
                                  handleChange(e);
                                  // setIsSubmit(false);
                                }}
                                onInput={(e) => Inputype(e, leave_type)}
                              />
                            </Box>
                          </>
                        ) : leave_type === 'day' ? (
                          <>
                            {dateRange && dateRange?.length > 0 && (
                              <Box className="leave-add-inputs-wrap mb8">
                                {dateRange?.map((date) => {
                                  const isWeekend = checkWeekends(
                                    date,
                                    daysOfWeek
                                  );
                                  const isHoliday = checkHoliday(
                                    date,
                                    userHolidayList
                                  );
                                  const optionList =
                                    !isWeekend && !isHoliday
                                      ? dayMenuOptions
                                      : isWeekend && isHoliday
                                        ? [
                                            {
                                              label: 'Day off with Holiday',
                                              value: 'both',
                                              disabled: true,
                                            },
                                          ]
                                        : isWeekend
                                          ? [
                                              {
                                                label: 'Day off',
                                                value: 'dayoff',
                                                disabled: true,
                                              },
                                            ]
                                          : isHoliday
                                            ? [
                                                {
                                                  label: 'Holiday',
                                                  value: 'holiday',
                                                  disabled: true,
                                                },
                                              ]
                                            : [];
                                  const valuesOption =
                                    isWeekend && isHoliday
                                      ? 'both'
                                      : isHoliday
                                        ? 'holiday'
                                        : isWeekend
                                          ? 'dayoff'
                                          : selectedWeekOff?.[date] || '';
                                  return (
                                    <Box
                                      key={date}
                                      className="leave-add-inputs"
                                    >
                                      <Box className="date-text-wrap">
                                        <Typography className="field-label">
                                          {dayjs(date).format('dddd D MMMM')}
                                        </Typography>
                                      </Box>
                                      <Box className="leave-input-wrap">
                                        <CustomSelect
                                          placeholder={leave_type}
                                          options={optionList}
                                          value={
                                            optionList?.find((opt) => {
                                              return (
                                                opt?.value === valuesOption
                                              );
                                            }) || ''
                                          }
                                          name={`selectedWeekOff.${date}`}
                                          error={
                                            errors?.selectedWeekOff?.[date]
                                          }
                                          helperText={
                                            errors?.selectedWeekOff?.[date]
                                          }
                                          onChange={(e) => {
                                            setSelectedWeekOff({
                                              ...selectedWeekOff,
                                              [date]: e?.value,
                                            });
                                            setFieldValue('selectedWeekOff', {
                                              ...selectedWeekOff,
                                              [date]: e?.value,
                                            });
                                            // setIsSubmit(false);
                                          }}
                                          disabled={isWeekend || isHoliday}
                                        />
                                      </Box>
                                    </Box>
                                  );
                                })}
                              </Box>
                            )}
                          </>
                        ) : leave_type === 'hour' ? (
                          <>
                            {dateRange && dateRange?.length > 0 && (
                              <Box className="leave-add-inputs-wrap mb8">
                                {dateRange?.map((date) => {
                                  const isWeekend = checkWeekends(
                                    date,
                                    daysOfWeek
                                  );
                                  const isHoliday = checkHoliday(
                                    date,
                                    userHolidayList
                                  );
                                  return (
                                    <Box
                                      key={date}
                                      className="leave-add-inputs"
                                    >
                                      <Box className="input-hours pt16">
                                        <CustomTextField
                                          fullWidth
                                          id={`selectedWeekOff.${date}`}
                                          name={`selectedWeekOff.${date}`}
                                          value={
                                            isHoliday && isWeekend
                                              ? 'Day off with Holiday'
                                              : isHoliday
                                                ? 'Holiday'
                                                : isWeekend
                                                  ? 'Day-off'
                                                  : selectedWeekOff?.[date] ||
                                                    ''
                                          }
                                          label={dayjs(date).format(
                                            'dddd D MMMM'
                                          )}
                                          placeholder={`Enter ${
                                            leave_type === 'hour'
                                              ? 'hours'
                                              : 'days'
                                          }`}
                                          error={Boolean(
                                            // !selectedWeekOff?.[date] &&
                                            errors?.selectedWeekOff?.[date]
                                          )}
                                          helperText={
                                            // !selectedWeekOff?.[date] &&
                                            errors?.selectedWeekOff?.[date]
                                          }
                                          onBlur={handleBlur}
                                          onChange={(e) => {
                                            const selectweeks = {
                                              ...selectedWeekOff,
                                              [date]: e.target.value,
                                            };
                                            setSelectedWeekOff(selectweeks);
                                            setFieldValue(
                                              'selectedWeekOff',
                                              selectweeks
                                            );
                                          }}
                                          onInput={(e) =>
                                            Inputype(e, leave_type)
                                          }
                                          disabled={isWeekend || isHoliday}
                                        />
                                      </Box>
                                    </Box>
                                  );
                                })}
                              </Box>
                            )}
                          </>
                        ) : (
                          <></>
                        )}
                      </>
                    ) : (
                      <></>
                    )}

                    <Divider />
                    <Box className="pt8 pb8">
                      <CustomTextField
                        fullWidth
                        id="remark"
                        name="remark"
                        value={values?.remark}
                        label="Reason"
                        required
                        rows={2}
                        placeholder="Enter reason"
                        error={Boolean(touched?.remark && errors?.remark)}
                        helperText={touched?.remark && errors?.remark}
                        onBlur={handleBlur}
                        onChange={(e) => {
                          if (event.target.value.length <= 160) {
                            handleChange(e);
                            // setIsSubmit(false);
                          }
                        }}
                        multiline
                      />
                      <Typography className="sub-title-text text-align-end">
                        {values?.remark?.length + '/ 160'}
                      </Typography>
                    </Box>
                    {checkValidations(
                      startDate,
                      endDate,
                      selectedLeave,
                      authState
                    ) &&
                      selectedLeave &&
                      !isLeaveStartDateValid(values?.leavetype) && (
                        <Typography
                          variant="body2"
                          color="error"
                          className="other-field-error-text "
                        >
                          {checkValidations(
                            startDate,
                            endDate,
                            selectedLeave,
                            authState
                          )}
                        </Typography>
                      )}
                    <Box className="create-cancel-button mt16 justify-end">
                      <CustomButton
                        variant="contained"
                        title={isLoader ? 'Applying...' : 'Apply'}
                        disabled={
                          isLoader ||
                          leaveList?.length === 0 ||
                          checkValidations(
                            startDate,
                            endDate,
                            selectedLeave,
                            authState
                          ) ||
                          isLeaveStartDateValid(values?.leavetype)
                        }
                        onClick={(e) => {
                          handleSubmit(e);
                          // setIsSubmit(true);
                        }}
                      />
                      <CustomButton
                        variant="outlined"
                        title="Cancel"
                        onClick={() => {
                          if (isRotas === 'true') {
                            router.back();
                          } else {
                            router.push('/own-leave');
                          }
                        }}
                      />
                    </Box>
                  </Box>
                  <Box className="right-side-leave-form-wrap">
                    <ApplyLeaveCard
                      authId={authState?.id}
                      userDetails={leaveBalanceDetails}
                      getSingleUserBalance={getSingleUserBalance}
                      yearFilterdata={yearFilterdata}
                      setYearFilterData={setYearFilterData}
                    />
                    <Box className="calendar-sec-wrap">
                      <>
                        <CustomDoubleCalender
                          selectedDate={selectedDate}
                          setSelectedDate={setSelectedDate}
                          filteredEvents={filteredEvents}
                          setFilteredEvents={setFilteredEvents}
                          searchValue={searchValue}
                          setSearchValue={setSearchValue}
                          getLeaveDetails={getLeaveDetails}
                          calenderData={{ calenderData, type: 'own' }}
                          userCanView={
                            selectedLeave?.leave_accural_policy
                              ?.allow_view_for_same_leave_type === 1
                          }
                          selectedLeave={selectedLeave}
                          userDetails={startDate}
                          type="apply-leave"
                        />
                        <Divider />
                      </>
                    </Box>
                  </Box>
                </Box>
              </Box>
            </Form>
          )}
        </Formik>
      </Box>
    </Box>
  );
}
