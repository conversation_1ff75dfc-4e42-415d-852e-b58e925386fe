'use client';

import React, { useEffect, useRef, useState } from 'react';
import { Divider } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import CustomButton from '../../CustomButton';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import SearchIcon from '@mui/icons-material/Search';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import FreeBreakfastOutlinedIcon from '@mui/icons-material/FreeBreakfastOutlined';
import BusinessCenterOutlinedIcon from '@mui/icons-material/BusinessCenterOutlined';
import ArrowCircleUpIcon from '@mui/icons-material/ArrowCircleUp';
import ChatOutlinedIcon from '@mui/icons-material/ChatOutlined';
import LightDarkSwitch from '@/components/UI/Switch';
import { DepartmentIcon } from '@/helper/common/images';
import { convertShiftTimeToDate } from '../ShiftUtility';
import { Formik, Form, Field } from 'formik';
import * as Yup from 'yup';
import moment from 'moment';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { ROTA_URLS } from '@/helper/constants/urls';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import LocationOnOutlinedIcon from '@mui/icons-material/LocationOnOutlined';
import { DesktopDatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import PreLoader from '@/components/UI/Loader';
import dayjs from 'dayjs';
import ShiftTabs from '../ShiftTabs/ShiftTabs';
import { ShiftSwapRequest } from '../ShiftSwapActions/ShiftSwapRequest';
import { ShiftDropRequest } from '../ShiftSwapActions/ShiftDropRequest';
import './AddShift.scss';

// Validation schema using Yup
const AddShiftSchema = Yup.object().shape({
  selectedEmployee: Yup.object()
    .shape({
      id: Yup.string().required(),
      user_full_name: Yup.string().required(),
    })
    .required('Employee is required'),
  selectedRole: Yup.object()
    .shape({
      value: Yup.string().required(),
      label: Yup.string().required(),
    })
    .required('Role is required'),
  selectedDepartment: Yup.object()
    .shape({
      value: Yup.string().required(),
      label: Yup.string().required(),
    })
    .required('Department is required'),
  shiftTime: Yup.string()
    .required('Shift time is required')
    .test(
      'is-valid-time-format',
      'Please enter a valid time format (e.g. 9am - 5pm or 09 - 17)',
      (value) => {
        if (!value) return false;

        // Pattern for 12-hour format: 9am - 5pm, 9:30am - 5:45pm
        const pattern12Hour =
          /^(1[0-2]|0?[1-9])(:[0-5][0-9])?\s*(am|pm)\s*-\s*(1[0-2]|0?[1-9])(:[0-5][0-9])?\s*(am|pm)$/i;

        // Pattern for 24-hour format: 09:00 - 17:00, 9:00 - 17:30
        const pattern24Hour =
          /^([01]?[0-9]|2[0-3])(:([0-5][0-9]))?\s*-\s*([01]?[0-9]|2[0-3])(:([0-5][0-9]))?$/;

        return pattern12Hour.test(value) || pattern24Hour.test(value);
      }
    ),
  shiftBreak: Yup.number()
    .min(0, 'Break time cannot be negative')
    .max(480, 'Break time cannot exceed 8 hours (480 minutes)')
    .required('Break time is required'),
  isPublished: Yup.boolean(),
  shiftNote: Yup.string().max(1500, 'Note cannot exceed 1500 characters'),
});

export const AddShiftTooltip = ({
  staffListOptions,
  rolesList,
  departmentList,
  locationList,
  rowUserData,
  handleAddShiftTooltipClose,
  currentCellDate,
  selectedShiftData,
  getRotaShiftList,
  viewAccessOnly,
  setDeleteShiftModal,
  setShiftHistoryModal,
}) => {
  const hoverUserDetails = rowUserData
    ? {
        user_full_name: rowUserData?.user_full_name,
        id: rowUserData?.id,
      }
    : null;

  // const hoverUserRoleDetails = rowUserData?.user_roles
  //   ? {
  //       label: rowUserData?.user_roles?.find(
  //         (role) => role?.role_id === rowUserData?.web_user_active_role_id
  //       )?.role_name,
  //       value: rowUserData?.user_roles?.find(
  //         (role) => role?.role_id === rowUserData?.web_user_active_role_id
  //       )?.role_id,
  //     }
  //   : null;

  const hoverUserRoleDetails =
    Array.isArray(rowUserData?.user_roles) && rowUserData.user_roles.length > 0
      ? {
          label: rowUserData.user_roles[0]?.role_name,
          value:
            rowUserData.user_roles[0]?.role_id || rowUserData.user_roles[0]?.id,
        }
      : null;
  const hoverUserDepartmentDetails = rowUserData?.department
    ? {
        label: rowUserData?.department?.department_name,
        value: rowUserData?.department?.id,
      }
    : null;

  const hoverUserLocationDetails = rowUserData?.branch
    ? {
        label: rowUserData?.branch?.branch_name,
        value: rowUserData?.branch?.id,
      }
    : null;

  const isSwapActive =
    selectedShiftData?.swapRequests?.some(
      (swap) => swap?.status === 'active' && swap?.adminStatus === 'active'
    ) ?? false;

  const isSwapRejected =
    selectedShiftData?.swapRequests?.some(
      (swap) => swap?.status === 'rejected' || swap?.adminStatus === 'rejected'
    ) ?? false;

  const isUserSwap =
    selectedShiftData?.isSwap === 1 &&
    selectedShiftData?.swapRequests?.length > 0 &&
    !isSwapRejected &&
    !isSwapActive;

  const isDropRejected =
    selectedShiftData?.drops?.some((drop) => drop?.status === 'deleted') ??
    false;

  const isUserDrop =
    selectedShiftData?.isDropped === 1 &&
    selectedShiftData?.drops?.length > 0 &&
    !isDropRejected;

  const isNoSwapWithViewAccess = viewAccessOnly;
  const [isShiftDropdownOpen, setIsShiftDropdownOpen] = useState(false);
  const [isRoleDropdownOpen, setIsRoleDropdownOpen] = useState(false);
  const [isDepartmentDropdownOpen, setIsDepartmentDropdownOpen] =
    useState(false);
  const [isLocationDropdownOpen, setIsLocationDropdownOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchRole, setSearchRole] = useState('');
  const [searchDepartment, setSearchDepartment] = useState('');
  const [searchLocation, setSearchLocation] = useState('');
  const [anchorEl, setAnchorEl] = useState(null);
  const [openDatePicker, setOpenDatePicker] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [shiftActiveTab, setShiftActiveTab] = useState(
    isUserSwap || isUserDrop ? '2' : '1'
  );

  const shiftDropdownRef = useRef(null);
  const roleDropdownRef = useRef(null);
  const departmentDropdownRef = useRef(null);
  const locationDropdownRef = useRef(null);

  const shift_time = selectedShiftData?.startTime
    ? `${moment(selectedShiftData?.startTime).utc().format('H:mm')} - ${moment(
        selectedShiftData?.endTime
      )
        .utc()
        .format('H:mm')}`
    : '';
  const selected_role = selectedShiftData?.role
    ? {
        label: selectedShiftData?.role?.role_name,
        value: selectedShiftData?.role?.id,
      }
    : null;

  const selected_department = selectedShiftData?.department
    ? {
        label: selectedShiftData?.department?.department_name,
        value: selectedShiftData?.department?.id,
      }
    : null;

  const selected_location = selectedShiftData?.branch
    ? {
        label: selectedShiftData?.branch?.branch_name,
        value: selectedShiftData?.branch?.id,
      }
    : null;

  // Initial form values
  const initialValues = {
    selectedEmployee: hoverUserDetails || staffListOptions?.[0],
    selectedRole: selected_role || hoverUserRoleDetails || rolesList?.[0],
    selectedDepartment:
      selected_department || hoverUserDepartmentDetails || departmentList?.[0],
    selectedLocation:
      selected_location || hoverUserLocationDetails || locationList?.[0],
    shiftTime: shift_time,
    shiftBreak: selectedShiftData?.minutesBreak
      ? selectedShiftData?.minutesBreak
      : 0,
    isPublished: selectedShiftData?.isPublished ? true : false,
    shiftNote: selectedShiftData?.notes ? selectedShiftData?.notes : '',
    selectedDate: selectedShiftData?.startTime
      ? dayjs(selectedShiftData?.startTime, 'YYYY-MM-DD')
      : null,
  };

  // Filter staffListOptions based on search
  const filteredStaffList = staffListOptions?.filter((emp) =>
    emp?.user_full_name?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Filter rolesList based on search
  const filteredRoleList = rolesList?.filter((role) =>
    role?.label?.toLowerCase().includes(searchRole.toLowerCase())
  );

  // Filter departmentList based on search
  const filteredDepartmentList = departmentList?.filter((role) =>
    role?.label?.toLowerCase().includes(searchDepartment.toLowerCase())
  );

  // Filter locationList based on search
  const filteredLocationList = locationList?.filter((role) =>
    role?.label?.toLowerCase().includes(searchLocation.toLowerCase())
  );

  // Close shift dropdown on outside click
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        shiftDropdownRef.current &&
        !shiftDropdownRef.current.contains(event.target)
      ) {
        setIsShiftDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Close role dropdown on outside click
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        roleDropdownRef.current &&
        !roleDropdownRef.current.contains(event.target)
      ) {
        setIsRoleDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Close department dropdown on outside click
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        departmentDropdownRef.current &&
        !departmentDropdownRef.current.contains(event.target)
      ) {
        setIsDepartmentDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Close location dropdown on outside click
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        locationDropdownRef.current &&
        !locationDropdownRef.current.contains(event.target)
      ) {
        setIsLocationDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleSubmit = async (values, { setSubmitting }) => {
    const date_time = convertShiftTimeToDate(
      values?.selectedDate ? dayjs(values?.selectedDate) : currentCellDate,
      values?.shiftTime
    );

    const sendCheckAvailabilityData = {
      id: selectedShiftData ? selectedShiftData?.id : null,
      userId:
        values?.selectedEmployee?.id === 'open_shift'
          ? null
          : values?.selectedEmployee?.id,
      startTime: date_time?.start_date,
      endTime: date_time?.end_date,
      status: 'active',
      isOpen: values?.selectedEmployee?.id === 'open_shift' ? true : false,
    };

    const sendData = {
      userId:
        values?.selectedEmployee?.id === 'open_shift'
          ? null
          : values?.selectedEmployee?.id,
      startTime: date_time?.start_date,
      endTime: date_time?.end_date,
      status: 'active',
      minutesBreak: values?.shiftBreak,
      branchId: values?.selectedLocation?.value,
      departmentId: values?.selectedDepartment?.value
        ? values?.selectedDepartment?.value
        : null,
      role: values?.selectedRole?.value ? values?.selectedRole?.value : null,
      isOpen: values?.selectedEmployee?.id === 'open_shift' ? true : false,
      isPublished: values?.isPublished,
      isSwap: false,
      acknowledged: true,
      notes: values?.shiftNote,
    };

    setIsLoading(true);
    try {
      const { status } = await axiosInstance.post(
        ROTA_URLS?.SHIFT_CHECK_AVILABILITY,
        sendCheckAvailabilityData
      );

      if (status === 200 || status === 201) {
        if (selectedShiftData) {
          try {
            const { status, data } = await axiosInstance.put(
              ROTA_URLS?.SHIFT_URL + `/${selectedShiftData?.id}`,
              sendData
            );
            if (status === 200 || status === 201) {
              setApiMessage('success', data?.message);
              setIsLoading(false);
              getRotaShiftList();
              handleAddShiftTooltipClose();
              setIsLoading(false);
            }
          } catch (error) {
            setApiMessage('error', error?.response?.data?.message);
            setIsLoading(false);
          }
        } else {
          try {
            const { status, data } = await axiosInstance.post(
              ROTA_URLS?.SHIFT_URL,
              sendData
            );
            if (status === 200 || status === 201) {
              setApiMessage('success', data?.message);
              setIsLoading(false);
              getRotaShiftList();
              handleAddShiftTooltipClose();
              setIsLoading(false);
            }
          } catch (error) {
            setApiMessage('error', error?.response?.data?.message);
            setIsLoading(false);
          }
        }
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setIsLoading(false);
    }

    setSubmitting(false);
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={AddShiftSchema}
      onSubmit={handleSubmit}
    >
      {({ values, errors, touched, setFieldValue, isSubmitting, dirty }) => {
        const isUpdated =
          JSON.stringify(values) !== JSON.stringify(initialValues);

        return (
          <Form className="add-shift-container">
            {/* Employee Dropdown */}
            <div>{isLoading && <PreLoader />}</div>
            <div className="add-shift-header-container">
              <div className="dropdown-wrapper" ref={shiftDropdownRef}>
                <div
                  className="dropdown-header"
                  onClick={() =>
                    !isNoSwapWithViewAccess
                      ? null
                      : setIsShiftDropdownOpen(!isShiftDropdownOpen)
                  }
                >
                  <span>{values.selectedEmployee?.user_full_name}</span>

                  {isNoSwapWithViewAccess && (
                    <span className="arrow">
                      {isShiftDropdownOpen ? (
                        <ExpandLessIcon />
                      ) : (
                        <ExpandMoreIcon />
                      )}
                    </span>
                  )}
                </div>
                {isShiftDropdownOpen && (
                  <div className="dropdown-content">
                    <div className="search-input-wrapper">
                      <SearchIcon className="search-icon" />
                      <input
                        type="text"
                        placeholder="Search employee"
                        className="search-input"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                    </div>
                    <ul>
                      {filteredStaffList
                        .filter((item) =>
                          values?.selectedRole?.value
                            ? item?.user_roles?.some(
                                (role) =>
                                  role?.role_id ===
                                    values?.selectedRole?.value ||
                                  role?.id === values?.selectedRole?.value
                              )
                            : item
                        )
                        .filter((obj) =>
                          values?.selectedDepartment?.value
                            ? obj?.department?.id ===
                              values?.selectedDepartment?.value
                            : obj
                        )
                        ?.map((emp) => (
                          <li
                            key={emp?.id}
                            className={
                              values.selectedEmployee?.id === emp?.id
                                ? 'selected'
                                : ''
                            }
                            onClick={() => {
                              setFieldValue('selectedEmployee', emp);
                              setIsShiftDropdownOpen(false);
                            }}
                          >
                            {emp?.user_full_name}
                          </li>
                        ))}
                    </ul>
                  </div>
                )}
              </div>
              {errors.selectedEmployee && touched.selectedEmployee && (
                <div className="other-field-error-text">
                  {errors.selectedEmployee}
                </div>
              )}

              <div className="add-shift-header-actions">
                {!selectedShiftData ? (
                  <CustomButton
                    variant="contained"
                    title={isSubmitting ? 'Adding...' : 'Add'}
                    type="submit"
                    disabled={isSubmitting || !dirty}
                  />
                ) : (
                  <div className="update-shift-btn">
                    {isUpdated ? (
                      <>
                        <CustomButton
                          variant="outlined"
                          title="Cancel"
                          disabled={isSubmitting || !dirty}
                          onClick={handleAddShiftTooltipClose}
                        />

                        <CustomButton
                          title="Update"
                          type="submit"
                          disabled={isSubmitting || !dirty}
                        />
                      </>
                    ) : viewAccessOnly ? (
                      <div
                        className="delete-shift-btn"
                        onClick={() => setDeleteShiftModal(true)}
                      >
                        <DeleteOutlineIcon />
                      </div>
                    ) : null}
                  </div>
                )}
                <div
                  className="close-shift-tooltip"
                  onClick={handleAddShiftTooltipClose}
                >
                  <CloseIcon />
                </div>
              </div>
            </div>

            {(isUserSwap || isUserDrop) && (
              <div>
                <ShiftTabs
                  swapList={selectedShiftData?.swapRequests}
                  isTabType="userSwap"
                  setShiftActiveTab={setShiftActiveTab}
                  viewAccessOnly={viewAccessOnly}
                  setShiftHistoryModal={setShiftHistoryModal}
                />
              </div>
            )}
            <Divider />
            {shiftActiveTab === '1' && (
              <div className="add-shift-details-container">
                {/* Update Shift date Input */}
                {selectedShiftData && isNoSwapWithViewAccess && (
                  <>
                    <div className="d-center-start shift-date-row">
                      <div className="icon">
                        <CalendarMonthIcon />
                      </div>
                      <div
                        className="shift-date-input-text"
                        onClick={(event) => {
                          setAnchorEl(event.currentTarget);
                          setOpenDatePicker(true);
                        }}
                      >
                        {dayjs(
                          values?.selectedDate
                            ? values?.selectedDate
                            : currentCellDate
                        ).format('dddd DD MMMM YYYY')}
                      </div>
                      <div className="shift-date-input-wrapper">
                        <LocalizationProvider
                          dateAdapter={AdapterDayjs}
                          dateFormats={{ monthShort: 'MMMM' }}
                        >
                          <DesktopDatePicker
                            open={openDatePicker}
                            onClose={() => setOpenDatePicker(false)}
                            value={
                              values?.selectedDate
                                ? dayjs(values?.selectedDate, 'YYYY-MM-DD')
                                : null
                            }
                            onChange={(date) => {
                              setFieldValue(
                                'selectedDate',
                                dayjs(date).format('YYYY-MM-DD')
                              );
                              // setOpenDatePicker(false);
                            }}
                            slotProps={{
                              textField: { style: { display: 'none' } }, // Hide input field
                              popper: {
                                anchorEl,
                                placement: 'bottom',
                                className: 'calendar-year-month-select-field',
                              },
                            }}
                          />
                        </LocalizationProvider>
                      </div>
                    </div>
                    <Divider className="mb8" />
                  </>
                )}

                {/* Shift Timing Input */}
                <div className="d-center-start shift-input-row">
                  <div className="icon">
                    <AccessTimeIcon />
                  </div>
                  <Field
                    name="shiftTime"
                    type="text"
                    placeholder="e.g. 9am - 5pm or 09 - 17"
                    className={`shift-input ${errors.shiftTime && touched.shiftTime ? 'error-input' : ''}`}
                    disabled={!isNoSwapWithViewAccess}
                  />
                  <div className="d-center-start shift-break-input-wrapper">
                    <div className="icon">
                      <FreeBreakfastOutlinedIcon />
                    </div>
                    <div className="shift-input-break">
                      <Field
                        name="shiftBreak"
                        type="number"
                        className={`break-input ${errors.shiftBreak && touched.shiftBreak ? 'error-input' : ''}`}
                        disabled={!isNoSwapWithViewAccess}
                      />
                      <span>min</span>
                    </div>
                  </div>
                </div>
                {(errors.shiftTime && touched.shiftTime) ||
                (errors.shiftBreak && touched.shiftBreak) ? (
                  <div className="error-message-row">
                    <div className="icon">&nbsp;</div>
                    <div className="error-messages">
                      {errors.shiftTime && touched.shiftTime && (
                        <div className="other-field-error-text">
                          {errors.shiftTime}
                        </div>
                      )}
                      {errors.shiftBreak && touched.shiftBreak && (
                        <div className="other-field-error-text">
                          {errors.shiftBreak}
                        </div>
                      )}
                    </div>
                  </div>
                ) : null}

                {/* Shift Role Dropdown */}
                <div className="d-center-start shift-role-input-row">
                  <div className="icon">
                    <BusinessCenterOutlinedIcon />
                  </div>
                  <div className="dropdown-wrapper" ref={roleDropdownRef}>
                    <div
                      className={`dropdown-header ${errors.selectedRole && touched.selectedRole ? 'error-input' : ''}`}
                      onClick={() =>
                        isNoSwapWithViewAccess
                          ? setIsRoleDropdownOpen(!isRoleDropdownOpen)
                          : null
                      }
                    >
                      <p>
                        <span className="role-left-border"></span>
                        {values.selectedRole?.label}
                      </p>
                    </div>

                    {isRoleDropdownOpen && (
                      <div className="dropdown-content">
                        <div className="search-input-wrapper">
                          <SearchIcon className="search-icon" />
                          <input
                            type="text"
                            placeholder="Search role"
                            className="search-input"
                            value={searchRole}
                            onChange={(e) => setSearchRole(e.target.value)}
                          />
                        </div>
                        <ul>
                          {filteredRoleList?.map((role) => (
                            <li
                              key={role?.value}
                              className={
                                values.selectedRole?.value === role?.value
                                  ? 'selected'
                                  : ''
                              }
                              onClick={() => {
                                setFieldValue('selectedRole', role);
                                setIsRoleDropdownOpen(false);
                              }}
                            >
                              <span className="role-left-border"></span>
                              {role?.label}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
                {errors.selectedRole && touched.selectedRole && (
                  <div className="error-message-row">
                    <div className="icon">&nbsp;</div>
                    <div className="other-field-error-text">
                      {errors.selectedRole}
                    </div>
                  </div>
                )}

                {/* Shift Department Dropdown */}
                {isNoSwapWithViewAccess && (
                  <div className="d-center-start shift-role-input-row">
                    <div className="icon">
                      <DepartmentIcon />
                    </div>
                    <div
                      className="dropdown-wrapper"
                      ref={departmentDropdownRef}
                    >
                      <div
                        className={`dropdown-header ${errors.selectedDepartment && touched.selectedDepartment ? 'error-input' : ''}`}
                        onClick={() =>
                          setIsDepartmentDropdownOpen(!isDepartmentDropdownOpen)
                        }
                      >
                        <p>
                          <span className="role-left-border"></span>
                          {values.selectedDepartment?.label}
                        </p>
                      </div>

                      {isDepartmentDropdownOpen && (
                        <div className="dropdown-content">
                          <div className="search-input-wrapper">
                            <SearchIcon className="search-icon" />
                            <input
                              type="text"
                              placeholder="Search department"
                              className="search-input"
                              value={searchDepartment}
                              onChange={(e) =>
                                setSearchDepartment(e.target.value)
                              }
                            />
                          </div>
                          <ul>
                            {filteredDepartmentList?.map((role) => (
                              <li
                                key={role?.value}
                                className={
                                  values.selectedDepartment?.value ===
                                  role?.value
                                    ? 'selected'
                                    : ''
                                }
                                onClick={() => {
                                  setFieldValue('selectedDepartment', role);
                                  setIsDepartmentDropdownOpen(false);
                                }}
                              >
                                <span className="role-left-border"></span>
                                {role?.label}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                )}
                {errors.selectedDepartment && touched.selectedDepartment && (
                  <div className="error-message-row">
                    <div className="icon">&nbsp;</div>
                    <div className="other-field-error-text">
                      {errors.selectedDepartment}
                    </div>
                  </div>
                )}
                <Divider />

                {/* Shift Location Dropdown */}
                {selectedShiftData && isNoSwapWithViewAccess && (
                  <>
                    <div className="d-center-start shift-role-input-row">
                      <div className="icon">
                        <LocationOnOutlinedIcon />
                      </div>
                      <div
                        className="dropdown-wrapper"
                        ref={locationDropdownRef}
                      >
                        <div
                          className={`dropdown-header ${errors.selectedLocation && touched.selectedLocation ? 'error-input' : ''}`}
                          onClick={() =>
                            setIsLocationDropdownOpen(!isLocationDropdownOpen)
                          }
                        >
                          <p>
                            <span className="role-left-border"></span>
                            {values.selectedLocation?.label}
                          </p>
                        </div>

                        {isLocationDropdownOpen && (
                          <div className="dropdown-content">
                            <div className="search-input-wrapper">
                              <SearchIcon className="search-icon" />
                              <input
                                type="text"
                                placeholder="Search location"
                                className="search-input"
                                value={searchLocation}
                                onChange={(e) =>
                                  setSearchLocation(e.target.value)
                                }
                              />
                            </div>
                            <ul>
                              {filteredLocationList?.map((role) => (
                                <li
                                  key={role?.value}
                                  className={
                                    values.selectedLocation?.value ===
                                    role?.value
                                      ? 'selected'
                                      : ''
                                  }
                                  onClick={() => {
                                    setFieldValue('selectedLocation', role);
                                    setIsLocationDropdownOpen(false);
                                  }}
                                >
                                  <span className="role-left-border"></span>
                                  {role?.label}
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    </div>
                    <Divider />
                  </>
                )}
                {/* Publish Toggle */}
                {isNoSwapWithViewAccess && (
                  <div className="d-center-start shift-toggle-row">
                    <div className="icon">
                      <ArrowCircleUpIcon />
                    </div>

                    <div
                      className="publish-toggle-container"
                      onClick={() =>
                        setFieldValue('isPublished', !values.isPublished)
                      }
                    >
                      <p className="publish-toggle-title">
                        Publish and send notification?
                      </p>
                      <LightDarkSwitch
                        checked={values.isPublished}
                        onChange={() =>
                          setFieldValue('isPublished', !values.isPublished)
                        }
                      />
                    </div>
                  </div>
                )}
                {isNoSwapWithViewAccess && <Divider />}
                {/* Shift Note */}
                <div className="d-center-start shift-note-row">
                  <div className="icon">
                    <ChatOutlinedIcon />
                  </div>
                  <div className="shift-note-input-wrapper">
                    <Field
                      as="input"
                      name="shiftNote"
                      placeholder="Shift note (optional)"
                      className={`shift-note-input ${errors.shiftNote && touched.shiftNote ? 'error-input' : ''}`}
                      maxLength={1500}
                      disabled={!isNoSwapWithViewAccess}
                    />
                  </div>
                </div>
                {/* Shift Note Char Count */}
                {isNoSwapWithViewAccess && (
                  <div className="d-center-start shift-note-char-count">
                    <div className="icon">&nbsp;</div>
                    <div className="shift-note-visibility-container">
                      <p className="shift-note-visibility-text">
                        This note is visible to{' '}
                        {rowUserData
                          ? rowUserData?.user_full_name
                          : 'everybody'}
                      </p>
                      <p className="char-count">
                        {values.shiftNote.length} / 1500
                      </p>
                      {errors.shiftNote && touched.shiftNote && (
                        <div className="other-field-error-text">
                          {errors.shiftNote}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}
            {shiftActiveTab === '2' && (
              <>
                <ShiftDropRequest
                  selectedShiftData={selectedShiftData}
                  viewAccessOnly={viewAccessOnly}
                  getRotaShiftList={getRotaShiftList}
                  handleAddShiftTooltipClose={handleAddShiftTooltipClose}
                />

                {isUserSwap && (
                  <ShiftSwapRequest
                    selectedShiftData={selectedShiftData}
                    viewAccessOnly={viewAccessOnly}
                    getRotaShiftList={getRotaShiftList}
                    handleAddShiftTooltipClose={handleAddShiftTooltipClose}
                  />
                )}
              </>
            )}
          </Form>
        );
      }}
    </Formik>
  );
};
