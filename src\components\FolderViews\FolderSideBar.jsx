import React, { useState } from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Drawer,
  Divider,
} from '@mui/material';
import CustomButton from '../UI/CustomButton';
import { FolderIcon, MenuIcon } from '../../helper/common/images';
import AddIcon from '@mui/icons-material/Add';
import { useRouter } from 'next/navigation';
import './folder.scss';

const FolderSideBar = ({ sideBarMenu, setSelectedSideBarData }) => {
  const router = useRouter();
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [selectedMenu, setSelectedMenu] = useState(sideBarMenu[0]?.id);

  const handleListItemClick = (event, item) => {
    setSelectedMenu(item?.id);
    setSelectedSideBarData(item);
  };

  const toggleDrawer = () => {
    setIsDrawerOpen(!isDrawerOpen);
  };

  const handleRedirect = () => {
    router.push('/create-emp-contract');
  };

  const drawerContent = (
    <Box className="folder-side-menu" role="presentation">
      <Box className="folder-create-btn">
        <CustomButton
          fullWidth
          title="Create New Contract"
          startIcon={<AddIcon />}
          onClick={handleRedirect}
        />
      </Box>
      <Divider />
      <Box className="folder-name-list-container">
        <List className="folder-list-wrap">
          {sideBarMenu?.map((menu, index) => (
            <ListItem className="folder-list-item" key={index}>
              <ListItemButton
                className="folder-list-btn"
                selected={menu?.id === selectedMenu}
                onClick={(event) => handleListItemClick(event, menu)}
              >
                <FolderIcon className="sidebar-list-icon" />
                <ListItemText
                  className="sidebar-list-menu-name"
                  primary={menu?.name}
                  classes={{
                    primary: 'body-text',
                  }}
                />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
      </Box>
    </Box>
  );

  return (
    <Box className="sidebar-folder-container">
      <Box className="mobile-view-menu" onClick={toggleDrawer}>
        <MenuIcon />
        <Drawer anchor="left" open={isDrawerOpen} onClose={toggleDrawer}>
          {drawerContent}
        </Drawer>
      </Box>

      {drawerContent}
    </Box>
  );
};

export default FolderSideBar;
