'use client';

import React, { useContext, useEffect, useState } from 'react';
import { Box } from '@mui/material';
import AuthContext from '@/helper/authcontext';
import { removeFromStorage, fetchFromStorage } from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import DSRPage from '@/components/DSR/DSRs';
import '../dsr.scss';
import dayjs from 'dayjs';

export default function DSRView() {
  const { authState, userdata, setUserdata, AllListsData } =
    useContext(AuthContext);

  const [dsrData, setdsrData] = useState([]);
  const [loaderUser, setLoaderUser] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const [filterData, setFilterData] = useState({
    branch: '',
    sdate: '',
    edate: '',
  });
  const [filterDataApplied, setFilterDataApplied] = useState({
    branch: '',
    sdate: '',
    edate: '',
  });

  const [rowsPerPage, setRowsPerPage] = useState(10);

  // List of DSR
  const getDsrData = async (search, pageNo, branch, sdate, edate, Rpp) => {
    setLoaderUser(true);
    const sdatef = sdate
      ? dayjs(sdate)?.format('YYYY-MM-DD')
      : edate
        ? dayjs(edate)?.format('YYYY-MM-DD')
        : '';
    const edatef = edate
      ? dayjs(edate)?.format('YYYY-MM-DD')
      : sdate
        ? dayjs(sdate)?.format('YYYY-MM-DD')
        : '';
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_DSR_LIST +
          `?search=${search}&page=${pageNo}&size=${
            Rpp ? Rpp : rowsPerPage
          }&branch_id=${branch}&submitted_start_date=${sdatef}&submitted_end_date=${edatef}`
      );
      if (status === 200) {
        let dsr =
          data?.data &&
          data?.data?.length > 0 &&
          data?.data?.map((d) => {
            return {
              ...d,
              branch: d?.dsr_branch,
              submitedby: d?.dsr_user?.user_full_name,
              updatedby: d?.dsr_detail_updated_by?.user_full_name,
            };
          });
        dsr ? setdsrData(dsr) : setdsrData([]);
        removeFromStorage(identifiers?.RedirectData);
        setUserdata();
        setTotalCount(data?.count);
        setLoaderUser(false);
      }
    } catch (error) {
      setdsrData([]);
      setLoaderUser(false);
      setTotalCount(0);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  useEffect(() => {
    if (
      authState?.UserPermission?.dsr === 1 ||
      authState?.UserPermission?.dsr === 2
    ) {
      if (
        fetchFromStorage(identifiers?.RedirectData)?.dsr === undefined ||
        userdata?.dsr === undefined
      ) {
        getDsrData('', 1, '', '', '');
        removeFromStorage(identifiers?.RedirectData);
        setUserdata();
      } else if (
        !fetchFromStorage(identifiers?.RedirectData) &&
        userdata?.page === undefined &&
        fetchFromStorage(identifiers?.RedirectData)?.IsFromUser === undefined &&
        userdata?.IsFromUser === undefined
      ) {
        getDsrData('', 1, '', '', '');
        removeFromStorage(identifiers?.RedirectData);
        setUserdata();
      }
    }
  }, [authState?.UserPermission?.dsr]);

  return (
    <>
      <Box className="dsr-page-section">
        <DSRPage
          authState={authState}
          branchList={AllListsData?.ActiveBranchList}
          loader={loaderUser}
          rowsPerPage={rowsPerPage}
          setRowsPerPage={setRowsPerPage}
          getDsrData={getDsrData}
          dsrData={dsrData}
          totalCount={totalCount}
          setFilterData={setFilterData}
          setFilterDataApplied={setFilterDataApplied}
          filterData={filterData}
          filterDataApplied={filterDataApplied}
        />
      </Box>
    </>
  );
}
