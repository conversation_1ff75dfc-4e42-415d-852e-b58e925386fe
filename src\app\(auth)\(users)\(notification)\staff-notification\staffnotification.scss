.staff-notification-container {
  background-color: var(--color-white);
  padding: var(--spacing-xxl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-xs);
  height: 100%;
  overflow: auto;
  .staff-notification-sec {
    margin-top: var(--spacing-xl);
    .staff-notification-details {
      border-radius: var(--border-radius-md);
      border: 1px solid var(--border-color-primary);
      background-color: var(--color-white);
      width: 100%;
      border-inline-start: 3px solid var(--border-color-primary);
      padding: var(--spacing-xl);
      margin-top: var(--spacing-xl);
      .notification-accordion {
        padding: 0;
        .MuiAccordionSummary-content {
          margin: 0;
        }
        .notification-card {
          width: 100%;
          .notification-list-user-icon {
            .MuiAvatar-root {
              height: var(--icon-size-xl);
              width: var(--icon-size-xl);
            }
          }
          .notification-card-content {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            width: 100%;
            @media (max-width: 599px) {
              flex-direction: column;
              align-items: flex-start;
              margin-bottom: var(--spacing-xl);
            }
            .notification-date {
              width: max-content;
            }
          }
        }
      }
      .MuiAccordionDetails-root {
        padding: 0;
        .accordion-details {
          margin-left: 55px;
        }
      }
    }
  }
}
.noti-field-grid-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-lg);
  margin-top: var(--spacing-lg);
  @media (max-width: 599px) {
    grid-template-columns: repeat(1, 1fr);
  }
}
.noti-desc-divider {
  border-bottom: var(--normal-sec-border);
  margin: 16px 0;
}
