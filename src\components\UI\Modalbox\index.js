import React from 'react';
import {
  Dialog,
  Grow,
  DialogContent,
  DialogTitle,
  Box,
  Typography,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import './modal.scss';

const DialogBox = ({
  open,
  handleClose,
  title,
  content,
  closeIconShow,
  className,
  onCloseStatus,
  titleClassName,
  contentClassName,
  dividerClass,
  contenttext,
}) => {
  return (
    <>
      <Dialog
        className={`dialog-box ${className} top-dialog`}
        open={open}
        onClose={onCloseStatus ? handleClose : null}
        TransitionComponent={Grow}
        // scroll={'body'}
      >
        <Box className="d-flex align-center dialog-title-wrap">
          <DialogTitle>
            <Typography className={`title-sm fw600 ${titleClassName}`}>
              {title}
            </Typography>
          </DialogTitle>
          {!closeIconShow && (
            <CloseIcon className={`cursor-pointer`} onClick={handleClose} />
          )}
        </Box>
        <Box>{contenttext}</Box>
        <Box className={`divider ${dividerClass}`} />
        <DialogContent className={`dialog-content ${contentClassName}`}>
          {content}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default DialogBox;
