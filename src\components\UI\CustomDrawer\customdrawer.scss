body {
  .custom-drawer-sec {
    .MuiPaper-root {
      width: 100%;
      min-width: 280px;
      max-width: 340px;
      background-color: var(--color-white);
      transform-origin: left center;
      transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
      will-change: transform;

      &.MuiDrawer-paperAnchorLeft {
        transform: translateX(-100%);
        &.MuiDrawer-paperAnchorLeft.MuiDrawer-paperOpen {
          transform: translateX(0);
        }
      }

      &.MuiDrawer-paperAnchorRight {
        transform: translateX(100%);
        &.MuiDrawer-paperAnchorRight.MuiDrawer-paperOpen {
          transform: translateX(0);
        }
      }

      .custom-drawer-header {
        padding: var(--spacing-sm) var(--spacing-xxl);
        min-height: 82px;
        border-bottom: var(--normal-sec-border);

        &:hover {
          .close-icon-wrap {
            path {
              fill: var(--text-charcoal-gray);
            }
          }
          .close-title {
            color: var(--text-charcoal-gray);
          }
          .brown-text {
            color: var(--text-charcoal-gray);
          }
        }
        @media (max-width: 767px) {
          min-height: 68px;
        }
      }
      .custom-drawer-content {
        display: flex;
        flex-direction: column;
        flex-grow: 1;
      }
    }
  }
}
