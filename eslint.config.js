module.exports = [
  {
    files: ['**/*.{js,jsx}'], // Apply ESLint to JS and JSX files
    languageOptions: {
      ecmaVersion: 'latest', // Use latest ECMAScript version
      sourceType: 'module',
      parser: require('@babel/eslint-parser'),
      parserOptions: {
        ecmaFeatures: { jsx: true }, // Enable JSX support
        requireConfigFile: false, // Disable Babel config requirement
      },
    },
    plugins: {
      prettier: require('eslint-plugin-prettier'),
      react: require('eslint-plugin-react'),
      '@next/next': require('@next/eslint-plugin-next'),
    },
    rules: {
      'no-console': ['error', { allow: ['error'] }],
      'prettier/prettier': ['error', { endOfLine: 'auto' }],
      'react/react-in-jsx-scope': 'off', // Disables the requirement to import React in JSX files (React 17+)
      'no-empty-pattern': 'error', // Allow empty object patterns (e.g., destructuring {} in function arguments)
      'no-empty': 'error', // Allow empty code blocks (e.g., `if` or `try-catch` with no body)
      'no-unused-vars': 'error', // Disable the warning for unused variables (useful when you're declaring but not using a variable, e.g., in function signatures)
      'no-constant-condition': 'error', // Allow constant conditions (e.g., `if (true) {}` for debugging purposes)
      'no-undef': 'off', // Disable the check for undefined variables (useful if you want to use global or external variables that ESLint may not recognize)
      'react/jsx-uses-react': 'error',
      'react/jsx-uses-vars': 'error',
    },
    settings: {
      react: {
        version: 'detect', // Auto-detect React version
      },
    },
  },
];
