import React, { useContext } from 'react';
import {
  Box,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import AuthContext from '@/helper/authcontext';
import './sideMenuList.scss';

function SideMenuList({ menuItem, activeId, onSelect }) {
  const { authState } = useContext(AuthContext);
  return (
    <Box className="side-menu-list">
      <List>
        {menuItem?.map(
          (item) =>
            item?.permission &&
            (authState?.UserPermission?.[item?.permission] === 2 ||
              authState?.UserPermission?.[item?.permission] === 1) && (
              <ListItemButton
                key={item.id}
                selected={activeId === item?.id}
                onClick={() => onSelect(item)}
                className={`side-menu-item ${activeId === item?.id ? 'active' : ''}`}
              >
                <ListItemIcon className="side-menu-icon">
                  {item?.icon}
                </ListItemIcon>
                <ListItemText className="side-menu-name" primary={item?.name} />
              </ListItemButton>
            )
        )}
      </List>
    </Box>
  );
}

export default SideMenuList;
