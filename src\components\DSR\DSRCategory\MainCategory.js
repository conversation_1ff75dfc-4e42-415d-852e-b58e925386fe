'use client';

import React, { useEffect, useState } from 'react';
import { Box, Typography, Tooltip } from '@mui/material';
import CancelIcon from '@mui/icons-material/Cancel';
import { staticOptions } from '@/helper/common/staticOptions';
import { identifiers } from '@/helper/constants/identifier';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import {
  DSRDayIcon,
  DSRWeekIcon,
  DSRPayrollIcon,
  Dragdropicon,
} from '@/helper/common/images';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomSelect from '@/components/UI/CustomSelect';
import CustomCheckbox from '@/components/UI/CustomCheckbox';
import CustomButton from '@/components/UI/CustomButton';
import DialogBox from '@/components/UI/Modalbox';
import DeleteModal from '@/components/UI/DeleteModal';

export default function AddMainCategory({
  AddDSR,
  AddPayment,
  ReorderPayment,
  deleteMainCategory,
}) {
  const [categoryList, setCatList] = useState([]);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteId, setDeleteId] = useState(null);

  const handleOpenDeleteDialog = (id) => {
    setDeleteId(id);
    setDeleteDialogOpen(true);
  };
  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setDeleteId(null);
  };

  const handleConfirmDelete = () => {
    deleteMainCategory(deleteId);
    setDeleteDialogOpen(false);
    setDeleteId(null);
  };

  const onDragEnd = (result) => {
    const { source, destination } = result;
    const newData = categoryList;
    const [movedRow] = newData.splice(source?.index, 1);
    newData.splice(destination?.index, 0, movedRow);
    setCatList(newData);
    const CuurectCat = categoryList[destination?.index];
    ReorderPayment(destination?.index, CuurectCat?.id);
  };
  useEffect(() => {
    AddDSR &&
      AddDSR?.CategoryList &&
      AddDSR?.CategoryList?.length > 0 &&
      setCatList(AddDSR?.CategoryList);
  }, [AddDSR?.CategoryList]);
  return (
    <Box className="category-level-0-section">
      {AddDSR?.mainCatgory ? (
        <Box className="category-list-details">
          <DragDropContext onDragEnd={onDragEnd}>
            <Droppable droppableId="droppable">
              {(provided) => (
                <Box {...provided.droppableProps} ref={provided.innerRef}>
                  {categoryList?.map((item, index) => (
                    <Draggable
                      key={item?.id}
                      draggableId={item?.id.toString()}
                      index={index}
                    >
                      {(provided) => (
                        <Box
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          hover
                          //   role="checkbox"
                          className="main-level-cards-screen"
                          tabIndex={-1}
                          key={item?.id}
                        >
                          <Box className="card-details cursor-pointer">
                            <Box className="card-left">
                              <Box>
                                <Box className="d-flex align-center">
                                  <Typography className="title-sm fw400 d-flex align-center">
                                    {item?.payment_type_title}
                                  </Typography>
                                </Box>
                                <Box className="d-flex align-start">
                                  <span className="category-status d-flex align-center ml0">
                                    {item?.payment_type_usage === 'income' ? (
                                      <span className="sub-title-text category-accepted  fw600 text-capital">
                                        {' '}
                                        {item?.payment_type_usage}{' '}
                                      </span>
                                    ) : item?.payment_type_usage ===
                                      'expense' ? (
                                      <span className="sub-title-text category-draft fw600 text-capital">
                                        {' '}
                                        {item?.payment_type_usage}
                                      </span>
                                    ) : item?.payment_type_usage === 'other' ? (
                                      <span className="sub-title-text category-ongoing fw600 text-capital">
                                        {' '}
                                        {item?.payment_type_usage}{' '}
                                      </span>
                                    ) : (
                                      <>
                                        {/* <span className="sub-title-text success fw600 text-capital">
                                          {item?.payment_type_usage}
                                        </span> */}
                                      </>
                                    )}
                                  </span>
                                  {(item?.payment_type_usage === 'income' ||
                                    item?.payment_type_usage === 'other') &&
                                    !item?.has_weekly_use && (
                                      <span className="card-icon d-flex align-start">
                                        <Tooltip
                                          arrow
                                          classes={{
                                            tooltip: 'info-tooltip-container',
                                          }}
                                          title={<Typography>DSR</Typography>}
                                        >
                                          {DSRDayIcon()}
                                        </Tooltip>{' '}
                                      </span>
                                    )}
                                  {(item?.payment_type_usage === 'income' ||
                                    item?.payment_type_usage === 'other') &&
                                    item?.has_weekly_use && (
                                      <Tooltip
                                        arrow
                                        classes={{
                                          tooltip: 'info-tooltip-container',
                                        }}
                                        title={<Typography>WSR</Typography>}
                                      >
                                        {DSRWeekIcon()}
                                      </Tooltip>
                                    )}
                                  {item?.payment_type_usage === 'expense' && (
                                    <span className="card-icon d-flex align-start">
                                      <Tooltip
                                        arrow
                                        classes={{
                                          tooltip: 'info-tooltip-container',
                                        }}
                                        title={<Typography>Expense</Typography>}
                                      >
                                        {DSRPayrollIcon()}
                                      </Tooltip>{' '}
                                    </span>
                                  )}
                                </Box>
                              </Box>
                            </Box>
                            <Box className="card-right">
                              {item?.payment_type_status === 'active' ? (
                                <Typography className="sub-title-text success text-capital fw600">
                                  {item?.payment_type_status}
                                </Typography>
                              ) : item?.payment_type_status === 'inactive' ? (
                                <Typography className="sub-title-text failed text-capital fw600">
                                  {' '}
                                  {'In-Active'}
                                </Typography>
                              ) : (
                                <Typography className="sub-title-text draft text-capital fw600">
                                  {' '}
                                  {item?.payment_type_status}
                                </Typography>
                              )}
                            </Box>
                          </Box>
                          <Box>
                            <Box
                              {...provided.dragHandleProps}
                              className="drag-icon"
                            >
                              {Dragdropicon()}
                            </Box>
                          </Box>
                          <CancelIcon
                            className="close-icon"
                            onClick={() => {
                              handleOpenDeleteDialog(item?.id);
                            }}
                          />
                        </Box>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </Box>
              )}
            </Droppable>
          </DragDropContext>
        </Box>
      ) : (
        <Box>
          <Formik
            initialValues={{
              title: AddDSR?.list?.payment_type_title || '',
              type: AddDSR?.list?.payment_type_usage || '',
              status: AddDSR?.list?.payment_type_status || 'active',
              isWeekly:
                AddDSR?.isUpdate && AddDSR?.list?.has_weekly_use ? true : false,
              has_include_amount:
                AddDSR?.isUpdate && AddDSR?.list?.has_include_amount
                  ? true
                  : false,
              has_field_currency: AddDSR?.isUpdate
                ? AddDSR?.list?.has_field_currency
                  ? true
                  : false
                : true,
            }}
            enableReinitialize={true}
            validationSchema={Yup.object().shape({
              title: Yup.string().trim().required('This field is required'),
              type: Yup.string().trim().required('This field is required'),
              status: Yup.string().trim().required('This field is required'),
            })}
            onSubmit={async (requestData) => {
              AddPayment(requestData, AddDSR?.isUpdate, AddDSR?.list?.id);
            }}
          >
            {({
              errors,
              touched,
              handleBlur,
              values,
              handleSubmit,
              handleChange,
              setFieldValue,
              dirty,
              isValid,
            }) => (
              <Form onSubmit={handleSubmit}>
                <Box className="pt16">
                  <CustomTextField
                    fullWidth
                    id="title"
                    name="title"
                    label="Tender"
                    placeholder="Enter Tender"
                    value={values?.title}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    required
                    error={Boolean(touched.title && errors.title)}
                    helperText={touched.title && errors.title}
                  />
                </Box>
                <Box className="pt16">
                  <CustomSelect
                    name="type"
                    label="Category type"
                    placeholder="Category type"
                    options={staticOptions?.DSR_CATEGORY_TYPE}
                    value={
                      staticOptions?.DSR_CATEGORY_TYPE?.find((opt) => {
                        return opt?.value === values?.type;
                      }) || ''
                    }
                    onChange={(e) => {
                      setFieldValue('type', e?.value);
                    }}
                    error={Boolean(touched.type && errors.type)}
                    helperText={touched.type && errors.type}
                    required
                    isDisabled={AddDSR?.isUpdate}
                    isClearable={false}
                  />
                </Box>
                {(values?.type === 'income' || values?.type === 'other') && (
                  <Box className="pt8">
                    <CustomCheckbox
                      disableRipple
                      name="weekly"
                      checked={values?.isWeekly}
                      onChange={(e) => {
                        setFieldValue('isWeekly', e.target.checked);
                      }}
                      label={
                        <Typography className="sub-content-text">
                          Add Category into WSR
                        </Typography>
                      }
                    />
                  </Box>
                )}

                <Box className="pt16">
                  <CustomSelect
                    name="status"
                    label="Category status"
                    placeholder="Category status"
                    options={identifiers?.CARD_STATUS}
                    value={
                      staticOptions?.CARD_STATUS?.find((opt) => {
                        return opt?.value === values?.status;
                      }) || ''
                    }
                    error={Boolean(touched.status && errors.status)}
                    helperText={touched.status && errors.status}
                    onChange={(e) => {
                      setFieldValue('status', e?.value);
                    }}
                    required
                    isClearable={false}
                  />
                </Box>

                <Box className="pt8">
                  <CustomCheckbox
                    disableRipple
                    name="weekly"
                    checked={values?.has_field_currency}
                    onChange={(e) => {
                      setFieldValue('has_field_currency', e.target.checked);
                      setFieldValue('has_include_amount', false);
                    }}
                    label={
                      <Typography className="sub-content-text">
                        Currency Field
                      </Typography>
                    }
                  />
                </Box>
                {values?.has_field_currency && (
                  <Box className="pt8">
                    <CustomCheckbox
                      disableRipple
                      name="weekly"
                      checked={values?.has_include_amount}
                      onChange={(e) => {
                        setFieldValue('has_include_amount', e.target.checked);
                      }}
                      label={
                        <Typography className="sub-content-text">
                          Include Category in Total
                        </Typography>
                      }
                    />
                  </Box>
                )}

                <CustomButton
                  className="mt16"
                  title="Save category"
                  type="submit"
                  disabled={!dirty || !isValid}
                />
              </Form>
            )}
          </Formik>
        </Box>
      )}
      <DialogBox
        open={deleteDialogOpen}
        handleClose={handleCloseDeleteDialog}
        className="delete-modal"
        dividerClass="delete-modal-divider"
        title="Confirmation"
        content={
          <DeleteModal
            handleCancel={handleCloseDeleteDialog}
            handleConfirm={handleConfirmDelete}
            text="Are you sure you want to delete?"
          />
        }
      />
    </Box>
  );
}
