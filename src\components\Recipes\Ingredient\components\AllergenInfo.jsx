import { Box, Typography } from '@mui/material';
import { IngredientIconSize } from '@/helper/common/commonFunctions';
import CustomCheckbox from '@/components/UI/CustomCheckbox';
import RecipesIcon from '@/components/UI/RecipePlaceholderIcon/RecipesIcon';
import NoDataView from '@/components/UI/NoDataView';

const AllergenInfo = ({ values, setFieldValue, isDefault, allergens }) => {
  return (
    <>
      <Typography className="sub-header-text pt16 pb8">Allergens</Typography>
      {!allergens || allergens.length === 0 ? (
        <NoDataView
          title="No Allergen Fields Available"
          description="There are no Allergen fields configured at the moment."
        />
      ) : (
        <Box className="ingredient-allergens-grid-container">
          {allergens?.map((allergen) => {
            return (
              <CustomCheckbox
                key={allergen?.id}
                name={`allergen_${allergen?.id}`}
                checked={values[`allergen_${allergen?.id}`]}
                label={
                  <Box className="d-flex align-center gap-sm">
                    {allergen?.iconItem?.iconUrl && (
                      <RecipesIcon
                        iconUrl={allergen?.iconItem?.iconUrl}
                        altText={allergen?.attribute_title}
                        imgWidth={IngredientIconSize}
                        imgHeight={IngredientIconSize}
                      />
                    )}
                    <Typography className="sub-title-text">
                      {allergen?.attribute_title}
                    </Typography>
                  </Box>
                }
                onChange={(e) =>
                  setFieldValue(`allergen_${allergen?.id}`, e.target.checked)
                }
                disabled={isDefault}
              />
            );
          })}
        </Box>
      )}
    </>
  );
};

export default AllergenInfo;
