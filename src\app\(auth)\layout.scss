@import '@/styles/variable.scss';

.header-bar {
  width: calc(100% - 100px);
  margin-left: auto;
  position: fixed;
  top: 0px;
  right: 0px;
  z-index: 1200;

  @media (max-width: 899px) {
    width: 100%;
  }

  .header-app-bar {
    background-color: var(--color-white);
    border-bottom: var(--normal-sec-border);
    box-shadow: none;

    .MuiToolbar-root {
      // min-height: 50px;
      padding: 0px 40px 0px 24px;
      border-left: var(--normal-sec-border);

      @media (max-width: 899px) {
        padding: var(--spacing-none) var(--spacing-lg);
      }

      .header-content {
        justify-content: space-between;
        svg {
          fill: var(--icon-color-black);
        }
        .page-title {
          @media (max-width: 899px) {
            display: none;
          }
        }

        .select-form {
          max-width: 184px;
          width: 100%;
          @media (max-width: 899px) {
            width: auto;
            margin-left: 10px;
          }

          .profile-img {
            border-radius: 50%;
            .MuiAvatar-root,
            svg {
              width: 30px;
              height: 30px;
            }

            img {
              border-radius: 50%;
            }
          }

          .administrator-text {
            flex-direction: column;
            justify-content: center;
            margin-left: 8px;
            margin-right: 8px;

            @media (max-width: 899px) {
              display: none;
            }

            .person-position {
              color: var(--text-color-slate-gray);
            }
          }

          .dropdownIcon {
            margin-left: 8px;
            padding: 0px;

            @media (max-width: 899px) {
              display: none;
            }

            &:hover {
              background-color: transparent;
            }
          }
        }
      }
    }
    .administrator-box {
      .notification-icon-wrap {
        line-height: 0px;
        .notification-icon {
          fill: var(--icon-color-primary);
          font-size: var(--font-size-xl);
        }
        .logout-icon {
          fill: var(--icon-bold-red-color);
          font-size: var(--font-size-lg);
        }
      }
    }
  }

  .bredcrumn-wrap {
    border-left: var(--normal-sec-border);
    padding: 2px 24px 2px;
    background-color: var(--color-white);
  }
}
.high-priority-class {
  margin-left: auto;
  position: fixed;
  top: var(--banner-height);
  right: 0px;
  z-index: 1200;
  transition: all 0.1s ease;
}

.layout-wrap {
  .top-open-notification-banner {
    width: calc(100% - 254px) !important;
    transition: all 0.5s ease;
  }
  .bottom-notification-banner {
    position: sticky;
    top: 100px;
    width: calc(100% - 69px);
    margin-left: auto;
    z-index: 1200;
    @media (max-width: 899px) {
      width: calc(100% - 0px);
    }
  }
}

.search-bar {
  max-width: calc(100% - 194px) !important;
  width: 100% !important;

  // @media (max-width: 1024px) {
  //   max-width: 300px;
  // }

  .MuiInputBase-root {
    border-radius: 8px !important;
    font-size: 14px !important;
    line-height: 21px !important;
    letter-spacing: -0.5px !important;
    font-family: $PrimaryFont !important;
    font-weight: 500 !important;
    padding-right: 16px !important;
    padding-left: 5px !important;
    color: $color-Dark-80 !important;

    .MuiInputBase-input {
      padding: 9.5px 0px 9.5px 16px !important;

      @media (max-width: 479px) {
        padding: 9.5px 0px 9.5px 16px !important;
      }
    }

    & ::placeholder {
      color: $color-Dark-20 !important;
      opacity: 1 !important;
      font-weight: 400 !important;
    }

    .MuiOutlinedInput-notchedOutline {
      border-color: $color-Black !important;
    }
  }
}
.Menu-bar {
  .menu-list-open {
    // width: 257px !important;
    // overflow: visible;

    .menu-name {
      opacity: 1 !important;
    }

    .sub-menu {
      .MuiMenuItem-root {
        margin-left: 25px;
        margin-bottom: 8px;
      }
    }

    .tth-logo {
      opacity: 1;
    }
  }

  .notification-banner {
    top: var(--banner-height) !important;
    transition: all 0.1s ease !important;
  }

  .sub-menu {
    .MuiMenuItem-root {
      margin-left: 0px;
      margin-bottom: 8px;
    }
  }

  .tth-logo {
    opacity: 0;
  }

  .mb24 {
    margin-bottom: 24px;
  }

  .header-close-section {
    position: relative;
    margin-bottom: 22px;
    .close-icon {
      margin-top: var(--spacing-lg);
      position: absolute;
      right: 0px;
      // top: -18px;
      top: 0px;
      svg {
        color: var(--btn-color-primary);
      }
    }
  }
  .menu-icon-drawer {
    svg {
      color: var(--btn-color-primary);
    }
  }

  .menu-list {
    position: fixed;
    top: 0px;
    height: calc(100% - var(--banner-height));
    left: 0;
    width: 100px;
    overflow-y: scroll;
    overflow-x: hidden;
    transition: all 0.5s ease;
    z-index: 1299;
    background-color: var(--color-white);
    padding: 0px 5px 10px 0px;
    border-right: var(--normal-sec-border);
    scrollbar-width: none;
    text-align: center;

    @media (max-width: 899px) {
      display: none;
    }
  }

  .menu-list::-webkit-scrollbar {
    width: 0px;
  }

  .menu-list::-webkit-scrollbar-thumb {
    display: none;
  }
  .info-icon-wrap {
    height: 21px;
    width: 21px;
    margin-top: 0px;
  }
  .app-logo-wrap {
    .side-menu-logo {
      width: 65px;
      height: auto;
    }
  }
}

.menu-drawer-sec {
  .MuiDrawer-paper {
    width: 257px;
    box-shadow: none;
  }

  .sub-menu {
    .MuiMenuItem-root {
      margin-left: 25px;
      margin-bottom: 8px;
    }
  }
  .menu-ul-list {
    .active {
      background: var(--color-primary-opacity);
      border-radius: var(--field-radius-right);
    }
    .MuiMenuItem-root {
      padding: 12px;
      .MuiListItemText-root {
        .MuiTypography-root {
          flex-direction: row;
          .menu-name {
            // opacity: 1;
            font-size: 14px;
            line-height: 20px;
          }
        }
      }
    }
  }
}

.menu-ul-list {
  padding: 0px;
  height: calc(100vh - 94px);
  overflow: auto;
  @media (max-width: 899px) {
    padding: 16px 12px !important;
  }

  .MuiMenuItem-root {
    padding: 10px 5px;

    .dsr-icon {
      path {
        stroke-width: 1.4px;
        stroke: var(--icon-color-black);
      }
    }
    border-left: 4px solid transparent;
    &:hover {
      // background-color: var(--color-primary); // $color-Primary-100;
      border-left: 4px solid var(--color-primary);
      // border-radius: 12px;
      -webkit-transition: background-color 0.4s ease-out;
      -ms-transition: background-color 0.4s ease-out;
      transition: background-color 0.4s ease-out;
      background-color: transparent;
      .menu-icon {
        svg {
          stroke: var(--icon-color-primary) !important;
          fill: var(--icon-color-primary) !important;

          path {
            stroke: var(--icon-color-primary) !important;
            fill: var(--icon-color-primary) !important;
          }
        }

        .resign-path {
          fill: var(--icon-color-primary) !important;
        }

        .dsr-icon {
          path {
            fill: none !important;
          }
        }

        .icon-tabler-report {
          fill: none !important;
          path {
            stroke: var(--icon-color-primary) !important;
            fill: none !important;
          }
        }

        .icon svg.icon-tabler-mail-forward {
          fill: none !important;

          path {
            fill: none !important;
          }
        }

        .icon svg.icon-tabler-door-exit path {
          fill: none !important;
        }
      }

      .menu-name {
        color: var(--text-color-primary) !important;
        // opacity: 0;
      }
      .info-icon-wrap {
        line-height: 0px;
        .MuiSvgIcon-root {
          fill: var(--icon-color-white);
        }
      }
      .info-icon-wrap {
        line-height: 0px;
        .MuiSvgIcon-root {
          fill: var(--icon-color-white);
        }
      }
    }

    &:not(:last-child) {
      margin-bottom: 8px;
    }

    .MuiListItemText-root {
      .MuiTypography-root {
        display: flex;
        align-items: center;
        flex-direction: column;
        gap: 5px;
        width: 100%;

        .menu-icon {
          min-width: 21px;
        }

        .icon-tabler-report {
          fill: none !important;
          stroke: var(--icon-color-black) !important;
        }

        .menu-name {
          // margin-left: 8px;
          // opacity: 0;
          font-size: 11.42px;
          line-height: 14px;
          font-weight: var(--font-weight-medium);
        }
      }
    }

    .MuiTouchRipple-root {
      display: block;
    }
  }
  .info-icon-wrap {
    line-height: 0px;
    .MuiSvgIcon-root {
      height: 21px;
      width: 21px;
    }
  }
  .active {
    // background-color: var(--color-primary);
    // border-radius: var(--border-radius-lg);
    border-left: 4px solid var(--color-primary);
    .menu-icon {
      svg {
        path {
          stroke: var(--icon-color-primary) !important;
          fill: var(--icon-color-primary) !important;
        }
      }

      .resign-path {
        fill: var(--icon-color-primary) !important;
      }

      .dsr-icon {
        path {
          fill: none !important;
        }
      }

      .icon-tabler-report {
        fill: none !important;
        path {
          stroke: var(--icon-color-primary) !important;
          fill: none !important;
        }
      }

      .icon svg.icon-tabler-mail-forward {
        fill: none !important;

        path {
          fill: none !important;
        }
      }

      .icon svg.icon-tabler-door-exit path {
        fill: none !important;
      }

      .icon {
        stroke: var(--icon-color-white) !important;
      }
    }
    .menu-name {
      color: var(--text-color-primary) !important;
    }
    .info-icon-wrap {
      line-height: 0px;
      .MuiSvgIcon-root {
        fill: var(--icon-color-white);
      }
    }
  }

  .menu-icon {
    .icon {
      display: flex;
    }

    .icon svg {
      width: 21px;
      height: 21px;
      fill: var(--icon-color-black) !important;
    }

    .userlist-icon {
      width: 20px !important;
      height: 20px !important;
    }

    .icon svg.icon-tabler-mail-forward {
      fill: none !important;
    }

    .icon svg.icon-tabler-door-exit {
      fill: none !important;
    }
  }
}

.page-container {
  padding: 98px 18px 30px 100px;
  height: calc(100vh - var(--banner-height));
  min-height: calc(100vh - var(--banner-height));
  overflow: auto;
  box-sizing: border-box;
  background-color: var(--color-light-blue);

  @media (max-width: 899px) {
    padding: 98px 30px 30px 30px;
  }

  @media (max-width: 599px) {
    padding: 98px 15px 15px 15px;
  }
}

.opened-layout {
  // .page-container {
  //   padding: 98px 30px 30px 287px !important;
  //   transition: all 0.5s ease;
  // }
}

.open-header-bar {
  // width: calc(100% - 254px) !important;
  // transition: all 0.5s ease;
}

.actions,
.actions-staff {
  padding: 12px 0;
  gap: 8px;

  svg {
    width: 20px;
    height: 20px;
    margin-left: 5px;
    cursor: pointer;
  }

  svg:first-child {
    margin-left: 0;
  }

  @media (max-width: 899px) {
    padding: 6px 0;
  }
}

.additional-textfeild {
  .MuiFilledInput-root {
    padding-top: 5px !important;
  }

  textarea {
    padding: 0 !important;
    // height: 80px !important;
  }
}

.department-search-section {
  display: flex;
  column-gap: 25px;
  align-items: center;

  .department-search {
    max-width: calc(100% - 97px - 50px - 202px) !important;
  }

  @media (max-width: 599px) {
    flex-direction: column;
    row-gap: 15px;

    .department-search,
    button {
      width: 100% !important;
      max-width: 100% !important;
    }
  }
}

.department-search-section.deparment-permission {
  .department-search {
    max-width: calc(100% - 97px - 25px) !important;
  }
}

.admin-user-section {
  display: flex;
  column-gap: 25px;
  align-items: center;

  .admin-user-search {
    max-width: calc(100% - 97px - 50px - 167px) !important;
  }

  @media (max-width: 599px) {
    flex-direction: column;
    row-gap: 15px;

    .admin-user-search,
    button {
      width: 100% !important;
      max-width: 100% !important;
    }
  }
}

.branch-search-section {
  display: flex;
  column-gap: 25px;
  align-items: center;

  .branch-search {
    max-width: calc(100% - 97px - 50px - 168px) !important;
  }

  @media (max-width: 599px) {
    flex-direction: column;
    row-gap: 15px;

    .branch-search,
    button {
      width: 100% !important;
      max-width: 100% !important;
    }
  }
}

.branch-search-section.branch-permission {
  .branch-search {
    max-width: calc(100% - 97px - 25px) !important;
  }
}

.error-text {
  color: $error !important;
  font-family: $regulerFont !important;
  font-size: 12px !important;
  line-height: 27px !important;
  font-weight: 400 !important;
  // text-align: left;
  // margin-top: 3px;
  // margin-right: 14px;
  // margin-bottom: 0;
  // margin-left: 14px;
}

.select-wrap {
  .MuiFormControl-root {
    .select-label-error {
      color: $error !important;
    }
  }
}

.create-admin-section {
  form {
    overflow-x: hidden !important;
  }

  .display-grid {
    display: grid;
    grid-template-columns: 49% 49%;
    column-gap: 15px;
    row-gap: 15px;

    @media (max-width: 899px) {
      display: block;
    }
  }
}

.additional-textfeild {
  .MuiInputBase-root {
    padding: 0 !important;
    padding-top: 0 !important;

    .MuiInputBase-inputMultiline {
      padding: 5px 12px 8px !important;
    }
  }
}

.resignation-button {
  width: 239px;
  max-width: 100%;
}

.actions-branch {
  width: 45px;
}

.sidebar-list-tooltip {
  background: var(--color-primary) !important;

  .MuiTooltip-arrow {
    color: var(--color-primary);
  }
}

.header-menu-options {
  // display: flex;
  // gap: 24px;
  width: calc(100% - 184px - 25px);

  // overflow-x: scroll;
  // &::-webkit-scrollbar {
  //   display: none !important;
  // }
  .MuiTabScrollButton-horizontal.Mui-disabled {
    display: none;
  }

  .header-menu-options-tab {
    min-height: 0;
  }

  .MuiTabs-scroller {
    min-height: 0;
  }

  .Mui-selected {
    color: var(--text-color-black) !important;
  }

  .header-menu-detail {
    display: flex;
    color: var(--text-color-black);
    align-items: center;
    gap: 6px;
    background-color: var(--action-icon-bg-color);
    padding: 4px 16px 4px 8px;
    border-radius: 8px;
    text-transform: capitalize;
    min-height: 0;
    margin: 0px 16px 0px 0px;
    border: 0;

    svg {
      width: 21px;
      height: 21px;
      fill: var(--icon-color-black);
      stroke-width: 1.3px;
    }

    .icon-tabler-report-search {
      fill: none !important;
      stroke-width: 1.8px;
    }

    .invite-icon {
      fill: none !important;

      path {
        stroke: unset !important;
        fill: none !important;
      }
    }

    .categories {
      width: 21px;
      height: 21px;
      fill: none;

      path {
        stroke: none !important;
      }
    }

    span {
      font-family: var(--font-family-primary);
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-regular);
      color: var(--text-color-black);
      line-height: var(--line-height-base);
      text-wrap: nowrap;
    }

    .personal-view-icon {
      path {
        stroke: none !important;
      }
    }
  }

  .MuiTabs-indicator {
    display: none;
  }

  .selected-menu {
    svg {
      path {
        fill: var(--icon-color-white) !important;
      }
    }
  }

  .selected-header-menu {
    background-color: var(--color-primary);
    color: var(--text-color-white);

    span {
      font-weight: 600;
      color: var(--text-color-white) !important;
    }

    svg {
      stroke: var(--icon-color-white) !important;
      fill: var(--icon-color-white) !important;

      path {
        stroke: var(--icon-color-white) !important;
      }
    }
    .selected-mail-icon {
      fill: none !important;

      path {
        stroke: none !important;
        fill: none;
      }
    }

    .selected-personal-view-icon {
      stroke: none !important;

      path {
        stroke: none !important;
      }
    }

    .invite-icon {
      fill: none !important;

      path {
        stroke: unset !important;
        fill: none !important;
      }
    }
    .dsr-icon {
      path {
        fill: none !important;
      }
    }
    .MuiSvgIcon-root {
      stroke: none !important;

      path {
        stroke: none !important;
      }
    }
    .icon-tabler-report-search {
      path {
        stroke: var(--icon-color-white) !important;
        fill: var(--color-primary) !important;
      }
    }
  }
}

.save-btn-box {
  .save-btn {
    width: 239px;
    padding: 16px 0px;
  }
}
.custom-btns {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
  @media (max-width: 540px) {
    flex-wrap: wrap;
  }
  .Mui-disabled {
    opacity: 0.6;
  }
  .coloums-btn,
  .filter-btn {
    color: $color-primary !important;
    border: 1px solid #9ca9ac;
    width: 100%;
    padding: 2px 10px !important;
    cursor: pointer;
    max-width: 105px;

    .MuiButton-icon {
      .MuiSvgIcon-root {
        fill: $color-primary !important;
        width: 18px;
        height: 18px;
      }
    }

    .MuiButton-startIcon {
      margin-right: 5px;
    }

    &:hover {
      box-shadow: none !important;
      background-color: $color-primary !important;
      .MuiSvgIcon-root {
        fill: $color-White !important;
      }
    }
  }

  // Specific styles for each button
  .coloums-btn {
    padding: 2px 20px !important;
  }
  .primary-btn {
    color: $color-White !important;
    border: 1px solid $color-primary;

    .MuiButton-icon {
      .MuiSvgIcon-root {
        fill: $color-White !important;
      }
    }

    &:hover {
      color: $color-primary !important;
      background-color: $color-White !important;
      border: 1px solid $color-primary;
      .MuiSvgIcon-root {
        fill: $color-primary !important;
      }
    }
  }
}

.hierarchy-wrap {
  // height: 200px;
  // width: 200px;
  width: 100%;
  max-width: 600px !important;
  min-width: 688px;
  height: 600px;
  .hierarchy-image-wrap {
    height: 650px;
    width: 650px;
    object-fit: fill;
  }
}

.main-page-container {
  //   padding-top: 68px;
  //   padding-left: 257px;
  padding: 10px 10px 10px 110px;
  height: calc(100vh - var(--header-height) - var(--banner-height));
  min-height: calc(100vh - var(--header-height) - var(--banner-height));
  overflow: auto;

  // margin-top: 24px;
  background-color: #eef0fc;
  ::-webkit-scrollbar-thumb {
    background-color: var(--color-primary);
  }
  @media (max-width: 899px) {
    padding: 10px 10px 30px 10px;
  }
}

.opened-layout {
  // .main-page-container {
  //   padding: 60px 10px 30px 268px;
  //   transition: all 0.5s ease;
  // }
}

body {
  .other-field-error-text {
    font-family: var(--font-family-primary);
    color: var(--text-error);
    margin-top: var(--spacing-xxs);
    font-size: var(--font-size-xs);
    line-height: var(--line-height-xs);
  }
  .form-actions-btn {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: var(--normal-sec-border);
  }
  .info-icon {
    height: var(--icon-size-xsm);
    width: var(--icon-size-xsm);
    fill: var(--icon-color-black);
  }
  .MuiTooltip-popper {
    z-index: 9999;
  }
  .custom-chip {
    font-family: var(--font-family-primary);
    border: var(--field-border);
    &.chip-selected {
      background-color: var(--color-primary);
      color: var(--text-color-white);
      border: var(--field-border-primary);
      &:hover {
        background-color: var(--color-primary);
      }
    }
  }
  .color-picker-section {
    position: relative;
    .color-picker-icon {
      cursor: pointer;
      width: var(--icon-size-sm);
      height: var(--icon-size-sm);
    }
    .Mui-disabled {
      cursor: auto;
      color: var(--text-color-black);
      -webkit-text-fill-color: var(--text-color-black);
    }
    .chrome-picker {
      position: absolute;
      z-index: 1;
    }
  }
  .dialog-box-container {
    .MuiDialog-paperScrollPaper {
      max-width: 800px;
    }
  }
  .small-dialog-box-container {
    .MuiDialog-paperScrollPaper {
      width: 100%;
    }
  }
  .dialog-box-container,
  .small-dialog-box-container {
    .MuiDialog-paperScrollPaper {
      padding: var(--spacing-xl);
      top: 0px;
      bottom: 30px;
      max-height: calc(100vh - 60px);
      position: relative;
      left: 0;
      transform: translate(0px);
      .divider {
        border-bottom: var(--normal-sec-border);
        margin: var(--spacing-md) 0px var(--spacing-lg) 0px;
      }
      .dialog-content {
        padding-bottom: var(--spacing-xs);
      }
      @media (max-width: 899px) {
        max-width: calc(100% - 60px);
      }
      @media (max-width: 399px) {
        max-width: calc(100% - 24px);
      }
    }
    .recipe-modal-form {
      padding: var(--spacing-none) var(--spacing-sm);
    }
  }

  .upload-drag-drop-container {
    border: 2px dashed var(--border-color-primary);
    width: 200px;
    height: 110px;
    border-radius: var(--border-radius-lg);
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: var(--spacing-sm);
    cursor: pointer;
    svg {
      width: var(--icon-size-md);
      height: var(--icon-size-md);
      fill: var(--icon-color-primary) !important;
    }

    .upload-text {
      color: var(--text-color-slate-gray);
      .blue-text {
        color: var(--text-color-primary);
      }
    }
  }
  .upload-preview-container {
    max-width: max-content;
    max-height: max-content;
    position: relative;
    margin-top: var(--spacing-md);
    img {
      max-height: 110px;
      object-fit: cover;
      border-radius: var(--border-radius-lg);
    }
    .cancel-icon {
      position: absolute;
      top: -10px;
      right: -10px;
      width: var(--icon-size-sm);
      height: var(--icon-size-sm);
      cursor: pointer;
    }
  }
  .user-avatar-details {
    .user-name-wrap {
      max-width: calc(100% - 35px);
    }
  }
}
