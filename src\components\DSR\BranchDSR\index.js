'use client';

import React, { useState, useEffect, useContext } from 'react';
import { Box, Typography, CircularProgress, Avatar } from '@mui/material';
import UpdateCategory from './Category';
import DSRCategoryMap from './Categorymap';
import CheckBoxIcon from '@mui/icons-material/CheckBox';

export default function BranchDSRCategoryPage({
  dsrCatData,
  BranchId,
  AddSubCategory,
  AddDSR,
  setAddDSR,
  ReorderBranchPayment,
  getCategoriesPaymentList,
  updateBranchActive,
}) {
  return (
    <>
      <Box className="d-flex align-center pb24 branch-check-info">
        <CheckBoxIcon className="checkbox" />
        <Typography className="p14">
          {' '}
          When adding DSR, WSR, or Payroll, the default open will be displayed.
          You must save first.
        </Typography>
      </Box>
      <Box className="dsr-category-details">
        <Box className="dsr-category-list">
          {dsrCatData && dsrCatData?.length > 0 && (
            <DSRCategoryMap
              dsrCatData={dsrCatData}
              AddDSR={AddDSR}
              setAddDSR={setAddDSR}
              updateBranchActive={updateBranchActive}
            />
          )}
        </Box>
        <Box className="dsr-category-list-details">
          <Typography className="p20 fw600 active-role">
            {AddDSR?.list?.name}
          </Typography>
          <UpdateCategory
            AddDSR={AddDSR}
            ReorderBranchPayment={ReorderBranchPayment}
            getCategoriesPaymentList={getCategoriesPaymentList}
            AddSubCategory={AddSubCategory}
          />
        </Box>
      </Box>
    </>
  );
}
