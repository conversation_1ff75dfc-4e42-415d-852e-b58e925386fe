'use client';

import React from 'react';
import { Box, Typography, InputAdornment, Tooltip } from '@mui/material';
import CustomTextField from '@/components/UI/CustomTextField';
import {
  TotalOfDSRObj,
  TotalOfDSRArray,
  removeVAT,
} from '@/helper/common/commonFunctions';
import InfoIcon from '@mui/icons-material/Info';

export default function DSRCategoryReq({
  DsrDataIncome,
  VatIsView,
  IsRemarkView,
  setIsSubmit,
  setDsrDataIncome,
  setDsrDataOther,
  DsrDataOther,
  currency,
  VAT,
  VatValue,
  ValueOfTotal,
  OldVAT,
}) {
  //Total without VAT Old
  const TotalWithoutVATOld = () => {
    return ValueOfTotal(
      parseFloat(
        removeVAT(
          ValueOfTotal(parseFloat(OldVAT?.VAT2)),
          VatValue && VatValue?.[1] ? VatValue?.[1] : 0
        )?.netAmount +
          ValueOfTotal(parseFloat(OldVAT?.VAT1)) +
          ValueOfTotal(parseFloat(OldVAT?.NoneVat))
      )
    ).toFixed(2);
  };
  //Total without VAT
  const TotalWithoutVAT = () => {
    return ValueOfTotal(
      parseFloat(
        removeVAT(
          ValueOfTotal(parseFloat(VAT?.VAT2)),
          VatValue && VatValue?.[1] ? VatValue?.[1] : 0
        )?.netAmount +
          ValueOfTotal(parseFloat(VAT?.VAT1)) +
          ValueOfTotal(parseFloat(VAT?.NoneVat))
      )
    ).toFixed(2);
  };
  return (
    <Box>
      {' '}
      {DsrDataIncome && DsrDataIncome?.length > 0 && (
        <>
          <Box className="dsr-add-view pt16 dsr-request-view">
            {DsrDataIncome &&
              DsrDataIncome?.length > 0 &&
              DsrDataIncome?.map((item, dindex) => {
                return (
                  <Box className="dsr-main-category income">
                    <Typography className="body-text fw600 text-capital main-category-text income-text">
                      {item?.payment_type_title}
                    </Typography>
                    <span className="category-status income">
                      <span className="sub-title-text category-accepted  fw600 text-capital">
                        {' '}
                        Income
                      </span>
                    </span>
                    {item?.payment_type_category?.length > 0 &&
                      item?.payment_type_category?.map((citem) => {
                        return (
                          <Box className="">
                            {' '}
                            {citem?.payment_type_category_pattern ===
                              'multiple' && (
                              <Box className="d-flex align-center gap-5 pt16">
                                <Typography className="title-text fw600 text-capital ">
                                  {citem?.payment_type_category_title}
                                </Typography>
                                {citem?.payment_type_category_remarks &&
                                citem?.payment_type_category_remarks?.trim() !==
                                  '' ? (
                                  <Tooltip
                                    arrow
                                    classes={{
                                      tooltip: 'info-tooltip-container ',
                                    }}
                                    title={citem?.payment_type_category_remarks}
                                  >
                                    <InfoIcon className="info-icon-wrap cursor-pointer" />
                                  </Tooltip>
                                ) : (
                                  <></>
                                )}
                              </Box>
                            )}
                            {citem?.payment_type_category_pattern ===
                            'multiple' ? (
                              <>
                                {citem?.categoryBranchValue &&
                                  citem?.categoryBranchValue?.length > 0 &&
                                  citem?.categoryBranchValue?.map((csitem) => {
                                    return (
                                      <>
                                        <Box>
                                          <Typography className="title-text">
                                            {csitem?.first_field_value}
                                          </Typography>
                                        </Box>
                                        <Box className="card-details-section card-request-sec">
                                          <Box className="old-value">
                                            {(csitem?.old_dsr_amount ||
                                              csitem?.old_dsr_amount === 0) &&
                                            currency?.symbol ? (
                                              <Typography className="title-text">
                                                {item?.has_field_currency ? (
                                                  <span className="symbol">
                                                    {currency?.symbol}
                                                  </span>
                                                ) : (
                                                  <></>
                                                )}
                                                <span>
                                                  {csitem?.old_dsr_amount}
                                                </span>
                                              </Typography>
                                            ) : csitem?.old_dsr_amount ||
                                              csitem?.old_dsr_amount === 0 ? (
                                              <Typography className="title-text">
                                                {item?.has_field_currency ? (
                                                  <span className="symbol">
                                                    £
                                                  </span>
                                                ) : (
                                                  <></>
                                                )}
                                                <span>
                                                  {csitem?.old_dsr_amount}
                                                </span>
                                              </Typography>
                                            ) : csitem?.dsr_amount ||
                                              csitem?.dsr_amount === 0 ? (
                                              <>
                                                {item?.has_field_currency ? (
                                                  <span className="symbol">
                                                    {currency?.symbol
                                                      ? currency?.symbol
                                                      : '£'}
                                                  </span>
                                                ) : (
                                                  <></>
                                                )}
                                                <span>
                                                  {csitem?.dsr_amount}
                                                </span>
                                              </>
                                            ) : (
                                              <></>
                                            )}
                                            {(csitem?.old_dsr_amount ||
                                              csitem?.dsr_amount ||
                                              csitem?.old_dsr_amount === 0) &&
                                              (csitem?.old_dsr_amount ||
                                                csitem?.old_dsr_amount === 0) &&
                                              csitem?.old_dsr_amount !==
                                                csitem?.dsr_amount && (
                                                <Typography className="sub-title-text">
                                                  Old
                                                </Typography>
                                              )}
                                          </Box>
                                          <Box className="amount-field">
                                            {(csitem?.old_dsr_amount ||
                                              csitem?.old_dsr_amount === 0) &&
                                              csitem?.old_dsr_amount !==
                                                csitem?.dsr_amount && (
                                                <Box className="new-value">
                                                  {(csitem?.dsr_amount ||
                                                    csitem?.dsr_amount === 0) &&
                                                  currency?.symbol ? (
                                                    <Typography className="title-text">
                                                      {item?.has_field_currency ? (
                                                        <span className="symbol">
                                                          {currency?.symbol}
                                                        </span>
                                                      ) : (
                                                        <></>
                                                      )}
                                                      <span>
                                                        {csitem?.dsr_amount}
                                                      </span>
                                                    </Typography>
                                                  ) : csitem?.dsr_amount ||
                                                    csitem?.dsr_amount === 0 ? (
                                                    <Typography className="title-text">
                                                      {item?.has_field_currency ? (
                                                        <span className="symbol">
                                                          £
                                                        </span>
                                                      ) : (
                                                        <></>
                                                      )}
                                                      <span>
                                                        {csitem?.dsr_amount}
                                                      </span>
                                                    </Typography>
                                                  ) : (
                                                    <></>
                                                  )}
                                                  {(csitem?.dsr_amount ||
                                                    csitem?.dsr_amount ===
                                                      0) && (
                                                    <Typography className="sub-title-text new">
                                                      {' '}
                                                      New{' '}
                                                    </Typography>
                                                  )}
                                                </Box>
                                              )}
                                          </Box>
                                        </Box>
                                      </>
                                    );
                                  })}
                              </>
                            ) : (
                              <>
                                <Box className="d-flex align-center gap-5">
                                  <Typography className="title-text fw600 text-capital">
                                    {citem?.payment_type_category_title}
                                  </Typography>
                                  {citem?.payment_type_category_remarks &&
                                  citem?.payment_type_category_remarks?.trim() !==
                                    '' ? (
                                    <Tooltip
                                      arrow
                                      classes={{
                                        tooltip: 'info-tooltip-container ',
                                      }}
                                      title={
                                        citem?.payment_type_category_remarks
                                      }
                                    >
                                      <InfoIcon className="info-icon-wrap cursor-pointer" />
                                    </Tooltip>
                                  ) : (
                                    <></>
                                  )}
                                </Box>
                                <Box className="card-details-section card-request-sec">
                                  <Box className="old-value">
                                    {(citem?.old_dsr_amount ||
                                      citem?.old_dsr_amount === 0) &&
                                    currency?.symbol ? (
                                      <Typography className="title-text">
                                        {item?.has_field_currency ? (
                                          <span className="symbol">
                                            {currency?.symbol}
                                          </span>
                                        ) : (
                                          <></>
                                        )}
                                        <span>{citem?.old_dsr_amount}</span>
                                      </Typography>
                                    ) : citem?.old_dsr_amount ||
                                      citem?.old_dsr_amount === 0 ? (
                                      <Typography className="title-text">
                                        {item?.has_field_currency ? (
                                          <span className="symbol">£</span>
                                        ) : (
                                          <></>
                                        )}
                                        <span>{citem?.old_dsr_amount}</span>
                                      </Typography>
                                    ) : citem?.dsr_amount ||
                                      citem?.dsr_amount === 0 ? (
                                      <>
                                        {item?.has_field_currency ? (
                                          <span className="symbol">
                                            {currency?.symbol
                                              ? currency?.symbol
                                              : '£'}
                                          </span>
                                        ) : (
                                          <></>
                                        )}
                                        <span>{citem?.dsr_amount}</span>
                                      </>
                                    ) : (
                                      <></>
                                    )}
                                    {(citem?.old_dsr_amount ||
                                      citem?.dsr_amount ||
                                      citem?.old_dsr_amount === 0) &&
                                      (citem?.old_dsr_amount ||
                                        citem?.old_dsr_amount === 0) &&
                                      citem?.old_dsr_amount !==
                                        citem?.dsr_amount && (
                                        <Typography className="sub-title-text">
                                          Old
                                        </Typography>
                                      )}
                                  </Box>
                                  <Box className="amount-field">
                                    {(citem?.old_dsr_amount ||
                                      citem?.old_dsr_amount === 0) &&
                                      citem?.old_dsr_amount !==
                                        citem?.dsr_amount && (
                                        <Box className="new-value">
                                          {(citem?.dsr_amount ||
                                            citem?.dsr_amount === 0) &&
                                          currency?.symbol ? (
                                            <Typography className="title-text">
                                              {item?.has_field_currency ? (
                                                <span className="symbol">
                                                  {currency?.symbol}
                                                </span>
                                              ) : (
                                                <></>
                                              )}
                                              <span>{citem?.dsr_amount}</span>
                                            </Typography>
                                          ) : citem?.dsr_amount ||
                                            citem?.dsr_amount === 0 ? (
                                            <Typography className="title-text">
                                              {item?.has_field_currency ? (
                                                <span className="symbol">
                                                  £
                                                </span>
                                              ) : (
                                                <></>
                                              )}
                                              <span>{citem?.dsr_amount}</span>
                                            </Typography>
                                          ) : (
                                            <></>
                                          )}
                                          {(citem?.dsr_amount ||
                                            citem?.dsr_amount === 0) && (
                                            <Typography className="sub-title-text new">
                                              {' '}
                                              New{' '}
                                            </Typography>
                                          )}
                                        </Box>
                                      )}
                                  </Box>
                                </Box>
                              </>
                            )}
                          </Box>
                        );
                      })}
                    <>
                      <Box className="card-details-section pt16 totol-dsr">
                        <Box>
                          <Typography className="title-text fw600">
                            Total
                          </Typography>
                        </Box>
                        <Box className="amount-field">
                          <CustomTextField
                            value={TotalOfDSRObj(DsrDataIncome, dindex)}
                            name={`amount `}
                            disabled={true}
                            placeholder={
                              item?.has_field_currency
                                ? 'Amount'
                                : 'Enter value'
                            }
                            className={'amount-textfield'}
                            InputProps={{
                              ...(item?.has_field_currency && {
                                startAdornment: (
                                  <InputAdornment position="start">
                                    <Typography className="title-text currency">
                                      {currency?.symbol
                                        ? currency?.symbol
                                        : '£'}
                                    </Typography>{' '}
                                  </InputAdornment>
                                ),
                              }),
                            }}
                          />
                        </Box>
                      </Box>
                      {IsRemarkView && (
                        <Box className="card-details-section  card-details-remark-section pt16">
                          <Box>
                            <Typography className="title-text fw600">
                              Remark
                            </Typography>
                          </Box>
                          <Box className="amount-field">
                            <CustomTextField
                              multiline
                              rows={2}
                              value={item?.payment_type_remark}
                              onChange={(e) => {
                                if (
                                  e.target.value === '' ||
                                  e.target.value?.length < 161
                                ) {
                                  setIsSubmit(false);
                                  const income = DsrDataIncome;
                                  income[dindex].payment_type_remark =
                                    e.target.value;
                                  setDsrDataIncome(income);
                                  setRandom(Math.random());
                                }
                              }}
                              onPaste={(e) => {
                                if (
                                  e.target.value === '' ||
                                  e.target.value?.length < 161
                                ) {
                                  setIsSubmit(false);
                                  const income = DsrDataIncome;
                                  income[dindex].payment_type_remark =
                                    e.target.value;
                                  setDsrDataIncome(income);
                                  setRandom(Math.random());
                                }
                              }}
                              disabled={true}
                              name={`amount ${dindex}`}
                              placeholder={'Enter value'}
                              className={
                                'amount-textfield additional-textfeild'
                              }
                            />
                          </Box>
                          <Typography className="sub-title-text text-align-end">
                            {(item?.payment_type_remark?.length
                              ? item?.payment_type_remark?.length
                              : 0) + ' / 160'}
                          </Typography>
                        </Box>
                      )}
                    </>
                  </Box>
                );
              })}
          </Box>
          {VatIsView && (
            <>
              <Box className="vat-calculation-section vat-calculation-req-section">
                <Box className="vat-calculation">
                  <Typography className="body-text fw600 text-capital vat-text text-underline">
                    VAT
                  </Typography>
                  <Box className="card-details-section pt16">
                    <Box>
                      <Typography className="title-text fw600">
                        Total of All Income
                      </Typography>
                    </Box>
                    <Box>
                      <Typography className="title-text fw600 text-align-end">
                        {TotalOfDSRArray(DsrDataIncome)}
                      </Typography>
                    </Box>
                  </Box>
                  <Box>
                    <Typography className="title-text">{`Non vatable Amount`}</Typography>
                  </Box>
                  <Box className="card-details-section  card-request-sec">
                    <Box className="old-value">
                      {(OldVAT?.NoneVat || OldVAT?.NoneVat === 0) &&
                      currency?.symbol ? (
                        <Typography className="title-text">
                          <span className="symbol">{currency?.symbol}</span>
                          <span>{OldVAT?.NoneVat}</span>
                        </Typography>
                      ) : OldVAT?.NoneVat || OldVAT?.NoneVat === 0 ? (
                        <Typography className="title-text">
                          <span className="symbol">£</span>
                          <span>{OldVAT?.NoneVat}</span>
                        </Typography>
                      ) : VAT?.NoneVat ? (
                        <Typography className="title-text">
                          <span className="symbol">
                            {currency?.symbol ? currency?.symbol : '£'}
                          </span>
                          <span>0</span>
                        </Typography>
                      ) : (
                        <></>
                      )}
                      {(OldVAT?.NoneVat ||
                        OldVAT?.NoneVat === 0 ||
                        VAT?.NoneVat) &&
                      VAT?.NoneVat !== OldVAT?.NoneVat ? (
                        <Typography className="sub-title-text">
                          {' '}
                          Old{' '}
                        </Typography>
                      ) : (
                        <></>
                      )}
                    </Box>
                    <Box className="amount-field">
                      {OldVAT?.NoneVat !== VAT?.NoneVat && (
                        <Box className="new-value">
                          {(VAT?.NoneVat || VAT?.NoneVat === 0) &&
                          currency?.symbol ? (
                            <Typography className="title-text">
                              <span className="symbol">{currency?.symbol}</span>
                              <span>{VAT?.NoneVat}</span>
                            </Typography>
                          ) : VAT?.NoneVat || VAT?.NoneVat === 0 ? (
                            <Typography className="title-text">
                              <span className="symbol">£</span>
                              <span>{VAT?.NoneVat}</span>
                            </Typography>
                          ) : (
                            <></>
                          )}
                          {VAT?.NoneVat || VAT?.NoneVat === 0 ? (
                            <Typography className="sub-title-text new">
                              {' '}
                              New{' '}
                            </Typography>
                          ) : (
                            <></>
                          )}
                        </Box>
                      )}
                    </Box>
                  </Box>
                  <Box>
                    <Typography className="title-text">{`${
                      VatValue && VatValue?.[0] ? VatValue?.[0] : 0
                    } % VAT Amount`}</Typography>
                  </Box>
                  <Box className="card-details-section card-request-sec">
                    <Box className="old-value">
                      {(OldVAT?.VAT1 || OldVAT?.VAT1 === 0) &&
                      currency?.symbol ? (
                        <Typography className="title-text">
                          <span className="symbol">{currency?.symbol}</span>
                          <span>{OldVAT?.VAT1}</span>
                        </Typography>
                      ) : OldVAT?.VAT1 || OldVAT?.VAT1 === 0 ? (
                        <Typography className="title-text">
                          <span className="symbol">£</span>
                          <span>{OldVAT?.VAT1}</span>
                        </Typography>
                      ) : VAT?.VAT1 ? (
                        <Typography className="title-text">
                          <span className="symbol">
                            {currency?.symbol ? currency?.symbol : '£'}
                          </span>
                          <span>0</span>
                        </Typography>
                      ) : (
                        <></>
                      )}
                      {(OldVAT?.VAT1 || VAT?.VAT1 || OldVAT?.VAT1 === 0) &&
                      OldVAT?.VAT1 !== VAT?.VAT1 ? (
                        <Typography className="sub-title-text">
                          {' '}
                          Old{' '}
                        </Typography>
                      ) : (
                        <></>
                      )}
                    </Box>
                    <Box className="amount-field">
                      {OldVAT?.VAT1 !== VAT?.VAT1 && (
                        <Box className="new-value">
                          {(VAT?.VAT1 || VAT?.VAT1 === 0) &&
                          currency?.symbol ? (
                            <Typography className="title-text">
                              <span className="symbol">{currency?.symbol}</span>
                              <span>{VAT?.VAT1}</span>
                            </Typography>
                          ) : VAT?.VAT1 || VAT?.VAT1 === 0 ? (
                            <Typography className="title-text">
                              <span className="symbol">£</span>
                              <span>{VAT?.VAT1}</span>
                            </Typography>
                          ) : (
                            <></>
                          )}
                          {VAT?.VAT1 || VAT?.VAT1 === 0 ? (
                            <Typography className="sub-title-text new">
                              {' '}
                              New{' '}
                            </Typography>
                          ) : (
                            <></>
                          )}
                        </Box>
                      )}
                    </Box>
                  </Box>
                  <Box>
                    <Typography className="title-text">{`${
                      VatValue && VatValue?.[1] ? VatValue?.[1] : 0
                    } % VAT Amount`}</Typography>
                  </Box>
                  <Box className="card-details-section card-request-sec">
                    <Box className="old-value">
                      {(OldVAT?.VAT2 || OldVAT?.VAT2 === 0) &&
                      currency?.symbol ? (
                        <Typography className="title-text">
                          <span className="symbol">{currency?.symbol}</span>
                          <span>{OldVAT?.VAT2}</span>
                        </Typography>
                      ) : OldVAT?.VAT2 || OldVAT?.VAT2 === 0 ? (
                        <Typography className="title-text">
                          <span className="symbol">£</span>
                          <span>{OldVAT?.VAT2}</span>
                        </Typography>
                      ) : VAT?.VAT2 ? (
                        <Typography className="title-text">
                          <span className="symbol">
                            {currency?.symbol ? currency?.symbol : '£'}
                          </span>
                          <span>0</span>
                        </Typography>
                      ) : (
                        <></>
                      )}
                      {(OldVAT?.VAT2 || VAT?.VAT2 || OldVAT?.VAT2 === 0) &&
                      OldVAT?.VAT2 !== VAT?.VAT2 ? (
                        <Typography className="sub-title-text">
                          {' '}
                          Old{' '}
                        </Typography>
                      ) : (
                        <></>
                      )}
                    </Box>
                    <Box className="amount-field">
                      {OldVAT?.VAT2 !== VAT?.VAT2 && (
                        <Box className="new-value">
                          {(VAT?.VAT2 || VAT?.VAT2 === 0) &&
                          currency?.symbol ? (
                            <Typography className="title-text">
                              <span className="symbol">{currency?.symbol}</span>
                              <span>{VAT?.VAT2}</span>
                            </Typography>
                          ) : VAT?.VAT2 || VAT?.VAT2 === 0 ? (
                            <Typography className="title-text">
                              <span className="symbol">£</span>
                              <span>{VAT?.VAT2}</span>
                            </Typography>
                          ) : (
                            <></>
                          )}
                          {VAT?.VAT2 || VAT?.VAT2 === 0 ? (
                            <Typography className="sub-title-text new">
                              {' '}
                              New{' '}
                            </Typography>
                          ) : (
                            <></>
                          )}
                        </Box>
                      )}
                    </Box>
                  </Box>
                  <Box>
                    <Typography className="title-text fw600">
                      Total VAT amount
                    </Typography>
                  </Box>
                  <Box className="card-details-section card-request-sec">
                    <Box className="old-value">
                      <Typography className="title-text fw600  ">
                        {(ValueOfTotal(parseFloat(OldVAT?.VAT2)) ||
                          ValueOfTotal(parseFloat(OldVAT?.VAT2)) === 0) &&
                        VatValue &&
                        VatValue?.[1]
                          ? removeVAT(
                              ValueOfTotal(parseFloat(OldVAT?.VAT2)),
                              VatValue?.[1]
                            )?.vatAmount
                          : 0}
                      </Typography>
                      {(ValueOfTotal(parseFloat(VAT?.VAT2)) ||
                        ValueOfTotal(parseFloat(VAT?.VAT2)) === 0) &&
                      (ValueOfTotal(parseFloat(OldVAT?.VAT2)) ||
                        ValueOfTotal(parseFloat(OldVAT?.VAT2)) === 0) &&
                      VatValue &&
                      VatValue?.[1] &&
                      removeVAT(
                        ValueOfTotal(parseFloat(OldVAT?.VAT2)),
                        VatValue?.[1]
                      )?.vatAmount !==
                        removeVAT(
                          ValueOfTotal(parseFloat(VAT?.VAT2)),
                          VatValue?.[1]
                        )?.vatAmount ? (
                        <Typography className="sub-title-text">
                          {' '}
                          Old{' '}
                        </Typography>
                      ) : (
                        <></>
                      )}
                    </Box>
                    <Box className="new-value">
                      {(ValueOfTotal(parseFloat(VAT?.VAT2)) ||
                        ValueOfTotal(parseFloat(VAT?.VAT2)) === 0) &&
                      (ValueOfTotal(parseFloat(OldVAT?.VAT2)) ||
                        ValueOfTotal(parseFloat(OldVAT?.VAT2)) === 0) &&
                      VatValue &&
                      VatValue?.[1] &&
                      removeVAT(
                        ValueOfTotal(parseFloat(OldVAT?.VAT2)),
                        VatValue?.[1]
                      )?.vatAmount !==
                        removeVAT(
                          ValueOfTotal(parseFloat(VAT?.VAT2)),
                          VatValue?.[1]
                        )?.vatAmount ? (
                        <>
                          <Typography className="title-text fw600 text-align-end">
                            {(ValueOfTotal(parseFloat(VAT?.VAT2)) ||
                              ValueOfTotal(parseFloat(VAT?.VAT2)) === 0) &&
                            VatValue &&
                            VatValue?.[1]
                              ? removeVAT(
                                  ValueOfTotal(parseFloat(VAT?.VAT2)),
                                  VatValue?.[1]
                                )?.vatAmount
                              : 0}
                          </Typography>
                          <Typography className="sub-title-text new text-align-end">
                            {' '}
                            New{' '}
                          </Typography>{' '}
                        </>
                      ) : (
                        <></>
                      )}
                    </Box>
                  </Box>
                  <Box>
                    <Typography className="title-text fw600">
                      Difference VAT amount
                    </Typography>
                  </Box>
                  <Box className="card-details-section card-request-sec card-diff-request-sec">
                    <Box className="difference-grid ">
                      <Box className="old-value old-diff-value">
                        {OldVAT?.diff1 || OldVAT?.diff1 === 0 ? (
                          <Typography className="title-text">
                            <span>{OldVAT?.diff1}</span>
                          </Typography>
                        ) : VAT?.diff1 ? (
                          <Typography className="title-text">
                            <span>0</span>
                          </Typography>
                        ) : (
                          <></>
                        )}
                        {(OldVAT?.diff1 || VAT?.diff1 || OldVAT?.diff1 === 0) &&
                          OldVAT?.diff1 !== VAT?.diff1 && (
                            <Typography className="sub-title-text">
                              {' '}
                              Old{' '}
                            </Typography>
                          )}
                      </Box>

                      <Box className="amount-field">
                        {OldVAT?.diff1 !== VAT?.diff1 && (
                          <Box className="new-value new-diff-value">
                            {VAT?.diff1 || VAT?.diff1 === 0 ? (
                              <Typography className="title-text">
                                <span>{VAT?.diff1}</span>
                              </Typography>
                            ) : (
                              <></>
                            )}
                            {(VAT?.diff1 || VAT?.diff1 === 0) && (
                              <Typography className="sub-title-text new  text-align-end">
                                {' '}
                                New{' '}
                              </Typography>
                            )}
                          </Box>
                        )}
                      </Box>
                    </Box>
                  </Box>
                  <Box className="card-details-section card-request-sec card-diff-request-sec ">
                    <Box className="difference-grid ">
                      <Box className="old-value old-diff-value">
                        {OldVAT?.diff2 || OldVAT?.diff2 === 0 ? (
                          <Typography className="title-text">
                            <span>{OldVAT?.diff2}</span>
                          </Typography>
                        ) : VAT?.diff2 ? (
                          <Typography className="title-text">
                            <span>
                              {Math.abs(
                                (
                                  removeVAT(
                                    ValueOfTotal(parseFloat(VAT?.VAT2)),
                                    VatValue?.[1]
                                  )?.vatAmount -
                                  ValueOfTotal(parseFloat(VAT?.diff1))
                                ).toFixed(2)
                              )}
                            </span>
                          </Typography>
                        ) : (
                          <></>
                        )}
                        {(OldVAT?.diff2 || VAT?.diff2 || OldVAT?.diff2 === 0) &&
                          OldVAT?.diff2 !==
                            Math.abs(
                              (
                                removeVAT(
                                  ValueOfTotal(parseFloat(VAT?.VAT2)),
                                  VatValue?.[1]
                                )?.vatAmount -
                                ValueOfTotal(parseFloat(VAT?.diff1))
                              ).toFixed(2)
                            ) && (
                            <Typography className="sub-title-text">
                              {' '}
                              Old{' '}
                            </Typography>
                          )}
                      </Box>
                      <Box className="amount-field right-grid">
                        {OldVAT?.diff2 !==
                          Math.abs(
                            (
                              removeVAT(
                                ValueOfTotal(parseFloat(VAT?.VAT2)),
                                VatValue?.[1]
                              )?.vatAmount -
                              ValueOfTotal(parseFloat(VAT?.diff1))
                            ).toFixed(2)
                          ) && (
                          <Box className="new-value new-diff-value right-grid">
                            {VAT?.diff1 && VAT?.diff1 !== '' ? (
                              <>
                                <Typography className="title-text">
                                  <span>
                                    {Math.abs(
                                      (
                                        removeVAT(
                                          ValueOfTotal(parseFloat(VAT?.VAT2)),
                                          VatValue?.[1]
                                        )?.vatAmount -
                                        ValueOfTotal(parseFloat(VAT?.diff1))
                                      ).toFixed(2)
                                    )}
                                  </span>
                                </Typography>
                                <Typography className="sub-title-text new  text-align-end">
                                  {' '}
                                  New{' '}
                                </Typography>
                              </>
                            ) : (
                              <></>
                            )}
                          </Box>
                        )}
                      </Box>
                    </Box>
                  </Box>
                  <Box>
                    <Typography className="title-text fw600">
                      Total without VAT
                    </Typography>
                  </Box>
                  <Box className="card-details-section card-request-sec">
                    <Box className="old-value">
                      <Typography className="title-text fw600">
                        {(ValueOfTotal(parseFloat(OldVAT?.VAT2)) ||
                          ValueOfTotal(parseFloat(OldVAT?.VAT2)) === 0 ||
                          ValueOfTotal(parseFloat(OldVAT?.VAT1)) ||
                          ValueOfTotal(parseFloat(OldVAT?.VAT1)) === 0 ||
                          ValueOfTotal(parseFloat(OldVAT?.NoneVat)) ||
                          ValueOfTotal(parseFloat(OldVAT?.NoneVat)) === 0) &&
                        VatValue &&
                        VatValue?.[1]
                          ? TotalWithoutVATOld()
                          : 0}
                      </Typography>
                      {(ValueOfTotal(parseFloat(OldVAT?.VAT2)) ||
                        ValueOfTotal(parseFloat(OldVAT?.VAT2)) === 0 ||
                        ValueOfTotal(parseFloat(OldVAT?.VAT1)) ||
                        ValueOfTotal(parseFloat(OldVAT?.VAT1)) === 0 ||
                        ValueOfTotal(parseFloat(OldVAT?.NoneVat)) ||
                        ValueOfTotal(parseFloat(OldVAT?.NoneVat)) === 0) &&
                      VatValue &&
                      VatValue?.[1] &&
                      (ValueOfTotal(parseFloat(VAT?.VAT2)) ||
                        ValueOfTotal(parseFloat(VAT?.VAT2)) === 0) &&
                      TotalWithoutVATOld() !== TotalWithoutVAT() ? (
                        <Typography className="sub-title-text">
                          {' '}
                          Old{' '}
                        </Typography>
                      ) : (
                        <></>
                      )}
                    </Box>
                    <Box className="new-value">
                      {(ValueOfTotal(parseFloat(OldVAT?.VAT2)) ||
                        ValueOfTotal(parseFloat(OldVAT?.VAT2)) === 0 ||
                        ValueOfTotal(parseFloat(OldVAT?.VAT1)) ||
                        ValueOfTotal(parseFloat(OldVAT?.VAT1)) === 0 ||
                        ValueOfTotal(parseFloat(OldVAT?.NoneVat)) ||
                        ValueOfTotal(parseFloat(OldVAT?.NoneVat)) === 0) &&
                      VatValue &&
                      VatValue?.[1] &&
                      (ValueOfTotal(parseFloat(VAT?.VAT2)) ||
                        ValueOfTotal(parseFloat(VAT?.VAT2)) === 0 ||
                        ValueOfTotal(parseFloat(VAT?.VAT1)) ||
                        ValueOfTotal(parseFloat(VAT?.VAT1)) === 0 ||
                        ValueOfTotal(parseFloat(VAT?.NoneVat)) ||
                        ValueOfTotal(parseFloat(VAT?.NoneVat)) === 0) &&
                      TotalWithoutVATOld() !== TotalWithoutVAT() ? (
                        <>
                          <Typography className="title-text fw600  text-align-end">
                            {ValueOfTotal(parseFloat(VAT?.VAT2)) ||
                            ValueOfTotal(parseFloat(VAT?.VAT2)) === 0 ||
                            ValueOfTotal(parseFloat(VAT?.VAT1)) ||
                            ValueOfTotal(parseFloat(VAT?.VAT1)) === 0 ||
                            ValueOfTotal(parseFloat(VAT?.NoneVat)) ||
                            ValueOfTotal(parseFloat(VAT?.NoneVat)) === 0
                              ? TotalWithoutVAT()
                              : 0}
                          </Typography>
                          <Typography className="sub-title-text new  text-align-end">
                            {' '}
                            New{' '}
                          </Typography>
                        </>
                      ) : (
                        <></>
                      )}
                    </Box>
                  </Box>
                </Box>
              </Box>{' '}
            </>
          )}
        </>
      )}
      {DsrDataOther && DsrDataOther?.length > 0 && (
        <Box className="dsr-add-view pt16 mb32 dsr-request-view">
          {DsrDataOther &&
            DsrDataOther?.length > 0 &&
            DsrDataOther?.map((item, dindex) => {
              return (
                <Box className="dsr-main-category other">
                  <Typography className="body-text fw600 text-capital main-category-text other-text">
                    {item?.payment_type_title}
                  </Typography>
                  <span className="category-status other">
                    <span className="sub-title-text category-ongoing fw600 text-capital">
                      Other
                    </span>
                  </span>
                  {item?.payment_type_category?.length > 0 &&
                    item?.payment_type_category?.map((citem) => {
                      return (
                        <Box className="">
                          {' '}
                          {citem?.payment_type_category_pattern ===
                            'multiple' && (
                            <Box className="d-flex align-center gap-5 pt16">
                              <Typography className="title-text fw600 text-capital pt16">
                                {citem?.payment_type_category_title}
                              </Typography>
                              {citem?.payment_type_category_remarks &&
                              citem?.payment_type_category_remarks?.trim() !==
                                '' ? (
                                <Tooltip
                                  arrow
                                  classes={{
                                    tooltip: 'info-tooltip-container ',
                                  }}
                                  title={citem?.payment_type_category_remarks}
                                >
                                  <InfoIcon className="info-icon-wrap cursor-pointer" />
                                </Tooltip>
                              ) : (
                                <></>
                              )}
                            </Box>
                          )}
                          {citem?.payment_type_category_pattern ===
                          'multiple' ? (
                            <>
                              {citem?.categoryBranchValue &&
                                citem?.categoryBranchValue?.length > 0 &&
                                citem?.categoryBranchValue?.map((csitem) => {
                                  return (
                                    <>
                                      <Box>
                                        <Typography className="title-text">
                                          {csitem?.first_field_value}
                                        </Typography>
                                      </Box>
                                      <Box className="card-details-section card-request-sec">
                                        <Box className="old-value">
                                          {(csitem?.old_dsr_amount ||
                                            csitem?.old_dsr_amount === 0) &&
                                          currency?.symbol ? (
                                            <Typography className="title-text">
                                              {item?.has_field_currency ? (
                                                <span className="symbol">
                                                  {currency?.symbol}
                                                </span>
                                              ) : (
                                                <></>
                                              )}
                                              <span>
                                                {csitem?.old_dsr_amount}
                                              </span>
                                            </Typography>
                                          ) : csitem?.old_dsr_amount ||
                                            csitem?.old_dsr_amount == 0 ? (
                                            <Typography className="title-text">
                                              {item?.has_field_currency ? (
                                                <span className="symbol">
                                                  £
                                                </span>
                                              ) : (
                                                <></>
                                              )}
                                              <span>
                                                {csitem?.old_dsr_amount}
                                              </span>
                                            </Typography>
                                          ) : csitem?.dsr_amount ||
                                            csitem?.dsr_amount === 0 ? (
                                            <>
                                              {item?.has_field_currency ? (
                                                <span className="symbol">
                                                  {currency?.symbol
                                                    ? currency?.symbol
                                                    : '£'}
                                                </span>
                                              ) : (
                                                <></>
                                              )}
                                              <span>{csitem?.dsr_amount}</span>
                                            </>
                                          ) : (
                                            <></>
                                          )}
                                          {(csitem?.old_dsr_amount ||
                                            csitem?.dsr_amount ||
                                            csitem?.old_dsr_amount == 0) &&
                                            (csitem?.old_dsr_amount ||
                                              csitem?.old_dsr_amount === 0) &&
                                            csitem?.old_dsr_amount !==
                                              csitem?.dsr_amount && (
                                              <Typography className="sub-title-text">
                                                Old
                                              </Typography>
                                            )}
                                        </Box>
                                        <Box className="amount-field">
                                          {(csitem?.old_dsr_amount ||
                                            csitem?.old_dsr_amount === 0) &&
                                            csitem?.old_dsr_amount !==
                                              csitem?.dsr_amount && (
                                              <Box className="new-value">
                                                {(csitem?.dsr_amount ||
                                                  csitem?.dsr_amount === 0) &&
                                                currency?.symbol ? (
                                                  <Typography className="title-text">
                                                    {item?.has_field_currency ? (
                                                      <span className="symbol">
                                                        {currency?.symbol}
                                                      </span>
                                                    ) : (
                                                      <></>
                                                    )}
                                                    <span>
                                                      {csitem?.dsr_amount}
                                                    </span>
                                                  </Typography>
                                                ) : csitem?.dsr_amount ||
                                                  csitem?.dsr_amount == 0 ? (
                                                  <Typography className="title-text">
                                                    {item?.has_field_currency ? (
                                                      <span className="symbol">
                                                        £
                                                      </span>
                                                    ) : (
                                                      <></>
                                                    )}
                                                    <span>
                                                      {csitem?.dsr_amount}
                                                    </span>
                                                  </Typography>
                                                ) : (
                                                  <></>
                                                )}
                                                {(csitem?.dsr_amount ||
                                                  csitem?.dsr_amount == 0) && (
                                                  <Typography className="sub-title-text new">
                                                    {' '}
                                                    New{' '}
                                                  </Typography>
                                                )}
                                              </Box>
                                            )}
                                        </Box>
                                      </Box>
                                    </>
                                  );
                                })}
                            </>
                          ) : (
                            <>
                              <Box className="d-flex align-center gap-5">
                                <Typography className="title-text fw600 text-capital">
                                  {citem?.payment_type_category_title}
                                </Typography>
                                {citem?.payment_type_category_remarks &&
                                citem?.payment_type_category_remarks?.trim() !==
                                  '' ? (
                                  <Tooltip
                                    arrow
                                    classes={{
                                      tooltip: 'info-tooltip-container ',
                                    }}
                                    title={citem?.payment_type_category_remarks}
                                  >
                                    <InfoIcon className="info-icon-wrap cursor-pointer" />
                                  </Tooltip>
                                ) : (
                                  <></>
                                )}
                              </Box>
                              <Box className="card-details-section card-request-sec">
                                <Box className="old-value">
                                  {(citem?.old_dsr_amount ||
                                    citem?.old_dsr_amount === 0) &&
                                  currency?.symbol ? (
                                    <Typography className="title-text">
                                      {item?.has_field_currency ? (
                                        <span className="symbol">
                                          {currency?.symbol}
                                        </span>
                                      ) : (
                                        <></>
                                      )}
                                      <span>{citem?.old_dsr_amount}</span>
                                    </Typography>
                                  ) : citem?.old_dsr_amount ||
                                    citem?.old_dsr_amount === 0 ? (
                                    <Typography className="title-text">
                                      {item?.has_field_currency ? (
                                        <span className="symbol">£</span>
                                      ) : (
                                        <></>
                                      )}
                                      <span>{citem?.old_dsr_amount}</span>
                                    </Typography>
                                  ) : citem?.dsr_amount ||
                                    citem?.dsr_amount === 0 ? (
                                    <>
                                      {item?.has_field_currency ? (
                                        <span className="symbol">
                                          {currency?.symbol
                                            ? currency?.symbol
                                            : '£'}
                                        </span>
                                      ) : (
                                        <></>
                                      )}
                                      <span>{citem?.dsr_amount}</span>
                                    </>
                                  ) : (
                                    <></>
                                  )}
                                  {(citem?.old_dsr_amount ||
                                    citem?.dsr_amount ||
                                    citem?.old_dsr_amount === 0) &&
                                    (citem?.old_dsr_amount ||
                                      citem?.old_dsr_amount === 0) &&
                                    citem?.dsr_amount !==
                                      citem?.old_dsr_amount && (
                                      <Typography className="sub-title-text">
                                        Old
                                      </Typography>
                                    )}
                                </Box>
                                <Box className="amount-field">
                                  {(citem?.old_dsr_amount ||
                                    citem?.old_dsr_amount === 0) &&
                                    citem?.dsr_amount !==
                                      citem?.old_dsr_amount && (
                                      <Box className="new-value">
                                        {(citem?.dsr_amount ||
                                          citem?.dsr_amount === 0) &&
                                        currency?.symbol ? (
                                          <Typography className="title-text">
                                            {item?.has_field_currency ? (
                                              <span className="symbol">
                                                {currency?.symbol}
                                              </span>
                                            ) : (
                                              <></>
                                            )}
                                            <span>{citem?.dsr_amount}</span>
                                          </Typography>
                                        ) : citem?.dsr_amount ||
                                          citem?.dsr_amount === 0 ? (
                                          <Typography className="title-text">
                                            {item?.has_field_currency ? (
                                              <span className="symbol">£</span>
                                            ) : (
                                              <></>
                                            )}
                                            <span>{citem?.dsr_amount}</span>
                                          </Typography>
                                        ) : (
                                          <></>
                                        )}
                                        {(citem?.dsr_amount ||
                                          citem?.dsr_amount === 0) && (
                                          <Typography className="sub-title-text new">
                                            {' '}
                                            New{' '}
                                          </Typography>
                                        )}
                                      </Box>
                                    )}
                                </Box>
                              </Box>
                            </>
                          )}
                        </Box>
                      );
                    })}
                  <>
                    <Box className="card-details-section pt16 totol-dsr">
                      <Box>
                        <Typography className="title-text fw600">
                          Total
                        </Typography>
                      </Box>
                      <Box className="amount-field">
                        <CustomTextField
                          value={TotalOfDSRObj(DsrDataOther, dindex)}
                          disabled={true}
                          name={`amount `}
                          placeholder={
                            item?.has_field_currency ? 'Amount' : 'Enter value'
                          }
                          className={'amount-textfield'}
                          InputProps={{
                            ...(item?.has_field_currency && {
                              startAdornment: (
                                <InputAdornment position="start">
                                  <Typography className="title-text currency">
                                    {currency?.symbol ? currency?.symbol : '£'}
                                  </Typography>{' '}
                                </InputAdornment>
                              ),
                            }),
                          }}
                        />
                      </Box>
                    </Box>
                    {IsRemarkView && (
                      <Box className="card-details-section  card-details-remark-section pt16">
                        <Box>
                          <Typography className="title-text fw600">
                            Remark
                          </Typography>
                        </Box>
                        <Box className="amount-field">
                          <CustomTextField
                            value={item?.payment_type_remark}
                            onChange={(e) => {
                              if (
                                e.target.value === '' ||
                                e.target.value?.length < 161
                              ) {
                                setIsSubmit(false);
                                const income = DsrDataOther;
                                income[dindex].payment_type_remark =
                                  e.target.value;
                                setDsrDataOther(income);
                              }
                            }}
                            onPaste={(e) => {
                              if (
                                e.target.value === '' ||
                                e.target.value?.length < 161
                              ) {
                                setIsSubmit(false);
                                const income = DsrDataIncome;
                                income[dindex].payment_type_remark =
                                  e.target.value;
                                setDsrDataIncome(income);
                                setRandom(Math.random());
                              }
                            }}
                            multiline
                            rows={2}
                            disabled={true}
                            name={`amount ${dindex}`}
                            placeholder={'Enter value'}
                            className={'amount-textfield additional-textfeild'}
                          />
                        </Box>
                        <Typography className="sub-title-text text-align-end">
                          {(item?.payment_type_remark?.length
                            ? item?.payment_type_remark?.length
                            : 0) + ' / 160'}
                        </Typography>
                      </Box>
                    )}
                  </>
                </Box>
              );
            })}
        </Box>
      )}
    </Box>
  );
}
