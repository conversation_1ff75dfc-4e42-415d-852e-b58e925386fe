import React from 'react';
import { Box, Typography } from '@mui/material';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import BusinessIcon from '@mui/icons-material/Business';
import GroupsIcon from '@mui/icons-material/Groups';
import CommentIcon from '@mui/icons-material/Comment';
import { DateFormat } from '@/helper/common/commonFunctions';
import './resignationdetail.scss';

const ResignationDetails = ({ resignationDetails, customClassName = '' }) => {
  return (
    <Box className={`resignation-page ${customClassName}`}>
      <Box className="User-verify-details">
        <Box className="user-deatils">
          <Box className="deatils">
            <Box className="d-flex align-center gap-sm">
              <Typography className="sub-header-text user-name">
                {resignationDetails?.resign_user?.user_full_name}
              </Typography>
              <Typography className="title-text fw400 user-date">
                <span className="active-role">
                  {resignationDetails?.resign_user?.role?.role_name}
                </span>
              </Typography>
              <Box className="d-flex align-center">
                <Typography className="title-text fw400 user-date">
                  {resignationDetails?.resignation_status === 'rejected' ? (
                    <span className="sub-title-text failed fw600 text-capital">
                      {resignationDetails?.resignation_status}
                    </span>
                  ) : resignationDetails?.resignation_status === 'cancelled' ? (
                    <span className="sub-title-text cancelled fw600 text-capital">
                      {resignationDetails?.resignation_status}
                    </span>
                  ) : resignationDetails?.resignation_status ===
                    'in-discussion' ? (
                    <span className="sub-title-text success fw600">
                      {resignationDetails?.resignation_status}
                    </span>
                  ) : resignationDetails?.resignation_status === 'pending' ? (
                    <span className="sub-title-text draft fw600 text-capital">
                      {resignationDetails?.resignation_status}
                    </span>
                  ) : (
                    <span className="sub-title-text active-onboarding fw600 text-capital">
                      {resignationDetails?.resignation_status}
                    </span>
                  )}
                </Typography>
              </Box>
            </Box>
            <Box className="d-flex justify-space-between pt4">
              <Box>
                {resignationDetails?.createdAt && (
                  <Typography className="title-text fw400 user-date d-flex align-center">
                    <CalendarTodayIcon className="mr8 res-remark-icon" />
                    <span>
                      {resignationDetails?.createdAt
                        ? DateFormat(
                            resignationDetails?.createdAt,
                            'datesWithhour'
                          )
                        : ''}
                    </span>
                  </Typography>
                )}
                {resignationDetails?.resign_user?.branch?.branch_name && (
                  <Typography className="title-text fw400 user-date d-flex align-center">
                    <BusinessIcon className="mr8 res-remark-icon" />
                    <span>
                      {resignationDetails?.resign_user?.branch?.branch_name}
                    </span>
                  </Typography>
                )}
                {resignationDetails?.resign_user?.department
                  ?.department_name && (
                  <Typography className="title-text fw400 user-date d-flex align-center">
                    <GroupsIcon className="mr8 res-remark-icon" />
                    <span>
                      {
                        resignationDetails?.resign_user?.department
                          ?.department_name
                      }
                    </span>
                  </Typography>
                )}
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>
      <Box className="User-verify-details pt16">
        <Box className="user-deatils">
          <Box className="deatils">
            <Typography className="sub-header-text pb8 text-underline">
              Application Details
            </Typography>
            <Typography className="title-text fw400 user-date ">
              <span className="fw600">Subject : </span>
              <span> {resignationDetails?.resignation_subject}</span>
            </Typography>
            <Typography className="title-text fw400 user-date ">
              <span className="fw600">Reason : </span>
              <span className="resignation-reason">
                {resignationDetails?.resignation_reason}
              </span>
            </Typography>
          </Box>
        </Box>
      </Box>
      {resignationDetails?.resignation_remarks &&
        resignationDetails?.resignation_remarks?.length > 0 && (
          <Box className="User-verify-details pt16 resignation-remarks-staff">
            <Box className="user-deatils">
              <Box className="deatils">
                <Typography className="title-text fw400 user-date">
                  <span className="fw600">Resignation Remarks : </span>
                </Typography>
                <Box className="remark-section">
                  {resignationDetails?.resignation_remarks &&
                    resignationDetails?.resignation_remarks.map((remark) => {
                      return (
                        <Box className="remark-text" key={remark.id}>
                          <Box>
                            <CommentIcon className="res-remark-icon" />
                          </Box>
                          <Box>
                            <Typography className="title-text fw400 user-date d-flex align-center gap-sm">
                              {remark?.remarks}
                            </Typography>
                            <Box className="action-by-date">
                              <Typography className="sub-title-text fw600 user-date">
                                Action by
                                <span className="pl4">
                                  {remark?.resign_remark_user?.user_full_name}
                                </span>
                              </Typography>
                              <Typography className="sub-title-text fw400 user-date ">
                                {remark?.createdAt
                                  ? DateFormat(
                                      remark?.createdAt,
                                      'datesWithhour'
                                    )
                                  : ''}
                              </Typography>
                            </Box>
                          </Box>
                        </Box>
                      );
                    })}
                </Box>
              </Box>
            </Box>
          </Box>
        )}
    </Box>
  );
};

export default ResignationDetails;
