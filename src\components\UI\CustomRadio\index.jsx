import { FormControlLabel, Radio } from '@mui/material';
import { styled } from '@mui/system';
import RadioButtonUncheckedIcon from '@mui/icons-material/RadioButtonUnchecked';
import RadioButtonCheckedIcon from '@mui/icons-material/RadioButtonChecked';

const StyledRadio = styled(Radio)(() => ({
  color: 'var(--radio-btn-uncheck-color-primary)',
  padding: 'var(--radio-btn-spacing)',
  '&.Mui-checked': {
    color: 'var(--radio-btn-check-color-primary)',
  },
  '&.Mui-disabled': {
    color: 'var(--radio-btn-disabled-color-primary)',
  },
  '& .MuiSvgIcon-root': {
    width: 'var(--radio-btn-icon-size)',
    height: 'var(--radio-btn-icon-size)',
  },
}));

const StyledFormControlLabel = styled(FormControlLabel)(() => ({
  marginLeft: '0px',
  marginRight: '0px',
  gap: 'var(--radio-btn-gap)',
  '.MuiTypography-root': {
    fontSize: 'var(--radio-btn-font-size)',
    fontFamily: 'var(--font-family-primary)',
    fontWeight: 'var(--font-weight-regular)',
    lineHeight: 'var(--line-height-base)',
    letterSpacing: 'var(--letter-spacing-normal)',
    color: 'var(--text-color-black)',
  },
}));

export default function CustomRadio({ label, disabled = false, ...props }) {
  return (
    <StyledFormControlLabel
      disabled={disabled}
      control={
        <StyledRadio
          icon={<RadioButtonUncheckedIcon />}
          checkedIcon={<RadioButtonCheckedIcon />}
          {...props}
        />
      }
      label={label}
    />
  );
}
