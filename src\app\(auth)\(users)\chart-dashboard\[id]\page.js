import React from 'react';
import { Box } from '@mui/material';
import { generateMetadata } from '@/helper/common/commonFunctions';
import DashboardById from '@/components/Dashboard/DashboardMain/DashboardById/index';

export const metadata = generateMetadata({
  pageTitle: 'Dashboard',
});

const DashboardMainPage = ({ params }) => {
  return (
    <Box className="main-page-container">
      <DashboardById params={params} />
    </Box>
  );
};

export default DashboardMainPage;
