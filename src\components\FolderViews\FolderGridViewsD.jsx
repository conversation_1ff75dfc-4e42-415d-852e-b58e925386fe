import React, { useState } from 'react';
import {
  Box,
  Checkbox,
  FormControlLabel,
  FormGroup,
  Menu,
  MenuItem,
  Typography,
  ListItemIcon,
} from '@mui/material';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import {
  FolderCheck,
  FolderDocGridIcon,
  FolderUncheck,
  FolderDocGridTrainingIcon,
} from '@/helper/common/images';
import { useRouter } from 'next/navigation';
import HeaderImage from '@/components/UI/ImageSecurity';
import { DateFormat } from '@/helper/common/commonFunctions';
import DeleteModal from '@/components/UI/DeleteModal';
import DialogBox from '@/components/UI/Modalbox';
import './folder.scss';

const FolderGridViews = ({
  folderData,
  menuList,
  setCreateModalTitle,
  setCreateModal,
  isOwn,
  selectedCat,
  setSelectedCat,
  Id,
  deleteDocument,
  CategoryDetails,
}) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteId, setDeleteId] = useState([]);
  const router = useRouter();
  const open = Boolean(anchorEl);
  const handleClick = (event) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
  };
  const handleOpenDeleteDialog = (id) => {
    setDeleteId(id);
    setDeleteDialogOpen(true);
  };
  const handleConfirmDelete = () => {
    deleteDocument(deleteId);
    setDeleteDialogOpen(false);
    setDeleteId(null);
  };
  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setDeleteId([]);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const onclickAction = (option, item) => {
    setAnchorEl(null);
    if (option?.id === 1) {
      let deleteItem = [];
      deleteItem.push(item?.id);
      // deleteDocument(deleteItem);
      handleOpenDeleteDialog(deleteItem);
    } else if (option?.id === 2 || option?.id === 3) {
      setCreateModal(true);
      option?.id === 2
        ? setCreateModalTitle({ title: 'Copy Items', id: item?.id })
        : option?.id === 3
          ? setCreateModalTitle({ title: 'Move Items', id: item?.id })
          : setCreateModalTitle('');
    } else if (option?.id === 4) {
      item?.category_type === 'folder'
        ? router.push(
            `/create-category?id=${Id}&UpdateId=${item?.id}&type=${
              item?.category_use
                ? item?.category_use
                : CategoryDetails?.category_use
            }`
          )
        : router.push(
            `/create-content?id=${Id}&UpdateId=${item?.id}&type=${
              item?.category_use
                ? item?.category_use
                : CategoryDetails?.category_use
            }`
          );
    }
  };
  const handleCheckBoxClick = (event, id) => {
    const selectedIndex = selectedCat?.indexOf(id);
    let newSelected = [];

    if (selectedIndex === -1) {
      newSelected = newSelected?.concat(selectedCat, id);
    } else if (selectedIndex === 0) {
      newSelected = newSelected?.concat(selectedCat?.slice(1));
    } else if (selectedIndex === selectedCat?.length - 1) {
      newSelected = newSelected?.concat(selectedCat?.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelected = newSelected?.concat(
        selected?.slice(0, selectedIndex),
        selected?.slice(selectedIndex + 1)
      );
    }
    setSelectedCat(newSelected);
  };

  return (
    <>
      <Box
        className={
          isOwn ? 'folder-conatiner cursor-pointer' : 'folder-conatiner'
        }
        onClick={() => {
          if (isOwn) {
            folderData?.category_type === 'file'
              ? router.push(
                  `/document-own/${
                    folderData?.parent_id ? folderData?.parent_id : 'all'
                  }?details=true&subId=${folderData?.id}`
                )
              : router.push(
                  `/document-own/${folderData?.id ? folderData?.id : 'all'}`
                );
          }
        }}
      >
        <Box className="folder-details">
          {!isOwn && (
            <Box className="menu-icons d-flex justify-space-between cursor-pointer">
              <Box className="d-flex align-center gap-sm">
                <FormGroup className="">
                  <FormControlLabel
                    control={
                      <Checkbox
                        className=""
                        icon={<FolderUncheck />}
                        checkedIcon={<FolderCheck />}
                      />
                    }
                    name="foldercheck"
                    className="folder-checkbox"
                    checked={selectedCat?.indexOf(folderData?.id) !== -1}
                    onChange={() => handleCheckBoxClick(event, folderData?.id)}
                    // label={'Display in Dashboard'}
                  />
                </FormGroup>
                {folderData?.category_status === 'draft' && (
                  <Typography className="sub-title-text draft fw600">
                    Draft
                  </Typography>
                )}
              </Box>
              <Box onClick={handleClick}>
                <MoreVertIcon />
              </Box>
              <Menu
                id="long-menu"
                className="Action-menu-popover"
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
                sx={{
                  '& .MuiPaper-root': {
                    maxWidth: '448px',
                    minWidth: '180px',
                  },
                }}
              >
                {menuList?.map((option) => (
                  <>
                    {option?.id === 3 && Id === 'all' ? (
                      <></>
                    ) : (
                      <MenuItem
                        key={option?.id}
                        onClick={() => onclickAction(option, folderData)}
                        className={option?.name}
                      >
                        <ListItemIcon>{option?.icon}</ListItemIcon>
                        {option?.name}
                      </MenuItem>
                    )}
                  </>
                ))}
              </Menu>
            </Box>
          )}
          {isOwn && <Box className="mb24" />}
          <Box
            className={`d-flex justify-start align-center flex-col ${
              folderData?.category_status === 'inactive'
                ? 'inactive-folder'
                : ''
            }`}
          >
            <Box>
              {folderData?.category_type === 'file' ? (
                <Box
                  className={`grid-view-file ${!isOwn && 'cursor-pointer'}`}
                  onClick={() => {
                    if (!isOwn && folderData?.category_type === 'file') {
                      router.push(
                        `/document-staff/${
                          folderData?.parent_id ? folderData?.parent_id : 'all'
                        }?details=true&subId=${folderData?.id}`
                      );
                    }
                  }}
                >
                  {/* <LazyLoadImage
                  src={folderData?.category_image_url}
                  alt="not found"
                  className="grid-view-file-image"
                /> */}
                  <HeaderImage
                    imageUrl={folderData?.category_image_url}
                    alt="not found"
                    className="grid-view-file-image"
                    type="lazyload"
                  />
                </Box>
              ) : (
                <Box
                  className={`grid-view-folder ${!isOwn && 'cursor-pointer'}`}
                  onClick={() => {
                    if (!isOwn && folderData?.category_type === 'folder') {
                      router.push(
                        `/document-staff/${
                          folderData?.id ? folderData?.id : 'all'
                        }`
                      );
                    }
                  }}
                >
                  {folderData?.category_use === 'training'
                    ? FolderDocGridTrainingIcon()
                    : FolderDocGridIcon()}
                </Box>
              )}
            </Box>
            <Typography
              className={
                !isOwn
                  ? 'title-text fw600 link-text-name cursor-pointer text-ellipsis-line'
                  : isOwn
                    ? 'title-text fw600 link-text-name link-text-name-active text-ellipsis-line'
                    : 'title-text fw600 link-text-name text-ellipsis-line'
              }
              onClick={() => {
                if (isOwn) {
                  folderData?.filetype !== 'File'
                    ? router.push(
                        `/document-own/${folderData?.id}?details=true`
                      )
                    : router.push(`/document-own/${folderData?.id}`);
                } else if (folderData?.category_type === 'folder') {
                  router.push(
                    `/document-staff/${folderData?.id ? folderData?.id : 'all'}`
                  );
                } else if (folderData?.category_type === 'file') {
                  router.push(
                    `/document-staff/${
                      folderData?.parent_id ? folderData?.parent_id : 'all'
                    }?details=true&subId=${folderData?.id}`
                  );
                }
              }}
            >
              {folderData?.category_name}
            </Typography>
            {/* <Box className="drag-icon">{Dragdropicon()}</Box> */}
          </Box>
        </Box>
        <Box className="d-flex justify-space-between align-center gap-sm user-details">
          <Typography className="sub-title-text user-date">
            {folderData?.size}
          </Typography>
          <Typography className="sub-title-text user-date">
            {DateFormat(folderData?.updatedAt, 'dates')}
          </Typography>
        </Box>
      </Box>
      <DialogBox
        open={deleteDialogOpen}
        handleClose={handleCloseDeleteDialog}
        className="delete-modal"
        dividerClass="delete-modal-divider"
        title="Confirmation"
        content={
          <DeleteModal
            handleCancel={handleCloseDeleteDialog}
            handleConfirm={handleConfirmDelete}
            text="Are you sure you want to delete?"
          />
        }
      />
    </>
  );
};

export default FolderGridViews;
