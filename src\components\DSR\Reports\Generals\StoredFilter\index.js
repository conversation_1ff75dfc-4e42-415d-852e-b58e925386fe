import React, { useState, useContext } from 'react';
import { Box, Tooltip, Typography } from '@mui/material';
import CancelIcon from '@mui/icons-material/Cancel';
import axiosInstance from '@/helper/axios/axiosInstance';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { URLS } from '@/helper/constants/urls';
import AuthContext from '@/helper/authcontext';
import './storedfilter.scss';
import '../generalpage.scss';

export default function StoredFilter({
  ReportList,
  getReportsList,
  getReportByID,
  tab,
}) {
  const [selectedFilters, setSelectedFilters] = useState();
  // const [loader, setLoader] = useState(false);
  const { authState } = useContext(AuthContext);
  // Handles checkbox selection and deselection for a filter option
  const handleCheckboxChange = (id) => {
    setSelectedFilters(id);
    getReportByID(id);
  };

  // Remove Filter from List
  const deleteReport = async (id) => {
    try {
      // setLoader(true);
      const { status, data } = await axiosInstance.delete(
        URLS?.REMOVE_FILTER + id
      );
      if (status === 200 || status === 201) {
        if (data.status) {
          setApiMessage('success', data?.message);
          authState &&
            authState?.id &&
            getReportsList(
              authState?.id,
              tab === 1 || tab === '1' ? 'general' : 'day',
              true,
              id
            );
        } else {
          setApiMessage('error', data?.message);
        }
      } else {
        setApiMessage('error', data?.message);
      }
      // setLoader(false);
    } catch (error) {
      // setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  return (
    <>
      <Box className="All-filter-list pt16">
        <Box>
          {ReportList?.map((item, index) => (
            <Box
              key={item?.id}
              className={
                selectedFilters === item?.id
                  ? 'Filter-list selected-list'
                  : 'Filter-list'
              }
            >
              <Typography
                className="title-text filtername text-ellipsis-line"
                onClick={() => handleCheckboxChange(item?.id)}
              >
                {item?.filter_name}
              </Typography>

              <Tooltip
                title={<Typography>Delete filter</Typography>}
                placement="top"
                arrow
                key={index}
                classes={{
                  tooltip: 'info-tooltip-container ',
                }}
              >
                <CancelIcon onClick={() => deleteReport(item?.id)} />
              </Tooltip>
            </Box>
          ))}
        </Box>
      </Box>
    </>
  );
}
