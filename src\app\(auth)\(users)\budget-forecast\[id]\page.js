import React from 'react';
import { Box } from '@mui/material';
import { generateMetadata } from '@/helper/common/commonFunctions';
import BudgetHistory from '@/components/Budget/BudgetHistory/index';

export const metadata = generateMetadata({
  pageTitle: 'Budget',
});

export default function BudgetForecastByID({ params }) {
  const BudgetId = params?.id;

  return (
    <>
      <Box className="main-page-container">
        <BudgetHistory BudgetId={BudgetId} />
      </Box>
    </>
  );
}
