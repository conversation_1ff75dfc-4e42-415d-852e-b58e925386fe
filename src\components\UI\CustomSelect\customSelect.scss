@import '../../../styles/variable.scss';
.custom-select-wrapper {
  width: 100%;

  .field-label {
    font-family: var(--font-family-primary);
    margin-bottom: var(--spacing-xxs);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-bright-blue);
    display: flex;
    align-items: center;
    &.error-label {
      color: var(--text-error);
    }

    .required {
      color: var(--text-error);
      margin-left: var(--spacing-xxs);
    }
    .field-info {
      display: flex;
      align-items: center;
      max-width: max-content;
      .info-icon {
        width: var(--font-size-sm);
        height: var(--font-size-sm);
        cursor: pointer;
      }
    }
  }

  .select-container {
    width: 100%;
    font-family: var(--font-family-primary);

    &.error-border {
      .select__control {
        border-color: var(--border-color-error);
      }
    }
  }

  .select-field-error-text {
    font-family: var(--font-family-primary);
    color: var(--text-error);
    margin-top: var(--spacing-xxs);
    font-size: var(--font-size-xs);
    line-height: var(--line-height-xs);
  }
}
// Custom styles for react-select
body {
  .select {
    &__control {
      border-radius: var(--field-radius);
      border: var(--field-border);
      box-shadow: none;
      transition: border-color 0.2s ease-in-out;
      background: var(--field-background);
      cursor: pointer;
      min-height: 36px;
      &:hover {
        border: var(--field-border-primary);
      }
      &--is-focused {
        border: var(--field-border-primary);
        box-shadow: none;
      }
      &--is-disabled {
        cursor: not-allowed;
      }
    }
    &--is-disabled {
      cursor: not-allowed;
      pointer-events: auto;
      .select__dropdown-indicator {
        color: var(--text-color-muted);
      }
    }
    &__menu-portal {
      z-index: 9999;
    }
    &__menu {
      border-radius: var(--border-radius-xs);
      box-shadow: var(--box-shadow-xs);
      margin-top: var(--spacing-xs);
      z-index: 9999;
      border: var(--normal-sec-border);
    }
    &__option {
      padding: 8px 16px;
      cursor: pointer;
      transition: background-color 0.2s ease-in-out;
      font-size: var(--font-size-sm);
      line-height: var(--line-height-sm);

      &--is-selected {
        background-color: var(--color-primary);
        color: var(--text-color-white);
      }

      &:hover {
        background-color: var(--color-primary);
        color: var(--text-color-white);
      }

      &--is-disabled {
        cursor: not-allowed;
      }
    }
    &__value-container {
      padding: 7px 12px;
      max-height: 69px;
      overflow: auto;
    }
    &__input {
      margin: var(--spacing-none);
      padding: var(--spacing-none);
      color: var(--text-color-black);
      font-size: var(--font-size-sm);
      line-height: var(--line-height-sm);
    }
    &__input-container {
      margin: var(--spacing-none);
      padding: var(--spacing-none);
      line-height: 20px;
    }
    &__placeholder {
      color: var(--field-placeholder);
      font-size: var(--font-size-sm);
      line-height: var(--line-height-sm);
      margin: var(--spacing-none);
      letter-spacing: 0px;
      opacity: 0.8;
    }
    &__single-value {
      color: var(--text-color-black);
      font-size: var(--font-size-sm);
      line-height: var(--line-height-sm);
      margin: var(--spacing-none);
      &--is-disabled {
        color: var(--text-color-muted);
      }
    }
    &__multi-value {
      background-color: var(--color-primary);
      color: var(--text-color-white);
      border-radius: var(--border-radius-xs);
      margin: var(--spacing-xxs);
      padding: var(--spacing-xxs) var(--spacing-sm);
      font-size: var(--font-size-sm);
      line-height: var(--line-height-sm);
      &--is-disabled {
        color: var(--text-color-muted);
      }
    }
    &__multi-value__label {
      color: var(--text-color-white);
    }
    &__multi-value__remove {
      &:hover {
        background-color: var(--text-color-white);
        color: var(--color-primary);
      }
    }
    &__indicator-separator {
      display: none;
    }
    &__dropdown-indicator {
      color: var(--icon-color-primary);
      padding: var(--spacing-none) var(--spacing-sm) var(--spacing-none)
        var(--spacing-xs);
      transition: all 0.2s ease-in-out;
      transform-origin: center;
      display: flex;
      align-items: center;

      &:hover {
        color: var(--text-color-slate-gray);
      }
    }
    &__control--menu-is-open {
      .select__dropdown-indicator {
        transform: scaleY(-1);
      }
    }
    &__clear-indicator {
      color: var(--icon-color-slate-gray);
      padding: var(--spacing-none);

      &:hover {
        color: var(--text-color-slate-gray);
      }
    }
  }
}
