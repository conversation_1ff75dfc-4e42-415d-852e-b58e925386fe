import { ShiftCell, StaffNameCell } from './ShiftCell';
import moment from 'moment';

// ========== Staff Open Shift Row Components ==========
export const StaffOpenShiftRow = ({
  openShiftData,
  monthDays,
  hoveredCell,
  setHoveredCell,
  menuOpen,
  setMenuOpen,
  shiftMenuOpen,
  setShiftMenuOpen,
  staffListData,
  rolesList,
  departmentList,
  locationList,
  filterData,
  getRotaShiftList,
  isViewType,
  viewAccessOnly,
  setIsUpdatedShift,
}) => {
  return (
    <tr key="open-shift">
      <StaffNameCell
        name="Open Shift"
        shifts={openShiftData}
        isViewType={isViewType}
        staff={null}
      />
      {monthDays?.map((day) => (
        <ShiftCell
          key={day.format('YYYY-MM-DD')}
          shifts={openShiftData}
          day={day}
          rowKey="open"
          hoveredCell={hoveredCell}
          setHoveredCell={setHoveredCell}
          menuOpen={menuOpen}
          setMenuOpen={setMenuOpen}
          shiftMenuOpen={shiftMenuOpen}
          setShiftMenuOpen={setShiftMenuOpen}
          staffListData={staffListData}
          rolesList={rolesList}
          departmentList={departmentList}
          locationList={locationList}
          filterData={filterData}
          rowUserData={null}
          getRotaShiftList={getRotaShiftList}
          isViewType={isViewType}
          dayOffItem={null}
          dayOffList={null}
          setDayOffList={null}
          viewAccessOnly={viewAccessOnly}
          availabilityItem={null}
          setIsUpdatedShift={setIsUpdatedShift}
        />
      ))}
    </tr>
  );
};

// ========== Staff Shift Row Components ==========
export const StaffShiftRow = ({
  staff,
  userShifts,
  monthDays,
  hoveredCell,
  setHoveredCell,
  menuOpen,
  setMenuOpen,
  shiftMenuOpen,
  setShiftMenuOpen,
  staffListData,
  rolesList,
  departmentList,
  locationList,
  filterData,
  getRotaShiftList,
  isViewType,
  dayOffList,
  setDayOffList,
  viewAccessOnly,
  availabilityList,
  setIsUpdatedShift,
  userLeaveList,
}) => {
  return (
    <tr key={staff?.id} id={`search-staff-${staff?.id}`}>
      <StaffNameCell
        name={staff?.user_full_name}
        shifts={userShifts}
        isViewType={isViewType}
        staff={staff}
      />
      {monthDays?.map((day) => {
        const dayOffItem =
          dayOffList?.find(
            (d) =>
              d?.date === day?.format('YYYY-MM-DD') && d?.userId === staff?.id
          ) || null;
        const availabilityItem =
          availabilityList?.find(
            (a) =>
              moment(a?.date)?.format('YYYY-MM-DD') ===
                day?.format('YYYY-MM-DD') && a?.userId === staff?.id
          ) || null;
        const userLeaveItem =
          userLeaveList?.find(
            (a) =>
              moment(a?.start)?.format('YYYY-MM-DD') ===
                day?.format('YYYY-MM-DD') && a?.from_user_id === staff?.id
          ) || null;
        return (
          <ShiftCell
            key={day.format('YYYY-MM-DD')}
            shifts={userShifts}
            day={day}
            rowKey={staff?.id}
            hoveredCell={hoveredCell}
            setHoveredCell={setHoveredCell}
            menuOpen={menuOpen}
            setMenuOpen={setMenuOpen}
            shiftMenuOpen={shiftMenuOpen}
            setShiftMenuOpen={setShiftMenuOpen}
            staffListData={staffListData}
            rolesList={rolesList?.filter(
              (r) =>
                staff?.user_roles?.some(
                  (role) => role?.role_id === r?.value || role?.id === r?.value
                ) || r?.value === 0
            )}
            departmentList={departmentList?.filter(
              (r) => r?.value === staff?.department?.id || r?.value === 0
            )}
            locationList={locationList}
            filterData={filterData}
            rowUserData={staff}
            getRotaShiftList={getRotaShiftList}
            isViewType={isViewType}
            dayOffItem={dayOffItem}
            dayOffList={dayOffList}
            setDayOffList={setDayOffList}
            viewAccessOnly={viewAccessOnly}
            availabilityItem={availabilityItem}
            setIsUpdatedShift={setIsUpdatedShift}
            userLeaveItem={userLeaveItem}
          />
        );
      })}
    </tr>
  );
};
