.dashboard-section {
  height: 100%;
}
.dashboard-page {
  background-color: var(--color-white);
  padding: var(--spacing-xxl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-xs);

  .default-dashboard {
    svg {
      fill: var(--color-primary);
      width: 18px;
      height: 18px;
      margin-right: 6px;
    }
  }
  .dashboard-list {
    display: flex;
    justify-content: space-between;
  }

  .Dashboard-template-models {
    .dashboard-model-view {
      position: relative;
      border: var(--normal-sec-border);
      padding: 12px;
      border-radius: 20px;
      margin-top: 24px;
      .Edit-drag-icon {
        display: flex;
        gap: 10px;
        position: absolute;
        top: 10px;
        right: 20px;
        z-index: 1;
      }
    }

    .dashboard-numbers-section {
      max-width: calc(30% - 12px);
      width: 400px;
      max-height: max-content;
      @media (max-width: 1300px) {
        max-width: calc(50% - 12px);
      }
      @media (max-width: 768px) {
        max-width: calc(60% - 12px);
      }
      @media (max-width: 540px) {
        max-width: 100%;
      }
    }
    .dashboard-chart-section {
      width: 100%;
    }
    .dashboard-piechart-section {
      width: calc(50% - 12px);
      @media (max-width: 1300px) {
        width: 100%;
      }
    }
  }
  .Dashboard-template-models-empty {
    height: calc(100% - 100px);
  }
  .blank-dashboard {
    height: calc(100% - 10px);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    .graph-icon {
      width: 100px;
      height: 100px;
      // max-width: 40%;
      // max-height: 30%;
      opacity: 0.3;
      @media (max-width: 768px) {
        width: 100px;
        height: 100px;
      }
      @media (max-width: 599px) {
        width: 100px;
        height: 100px;
      }
    }
    .blank-text {
      color: var(--color-primary);
    }
  }
}
