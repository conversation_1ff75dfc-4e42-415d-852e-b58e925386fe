'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Box, Divider, Typography } from '@mui/material';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import CustomSelect from '@/components/UI/selectbox';
import CustomButton from '@/components/UI/button';
import { CustomTextField } from '@/components/UI/CommonField/index';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import { useParams, useRouter } from 'next/navigation';
import _ from 'lodash';
import { identifiers } from '@/helper/constants/identifier';
import PreLoader from '../UI/Loader';

const AddEditContractType = ({
  isEdit,
  ContractName,
  handleCloseContract,
  getContractTypeDetailss,
}) => {
  const router = useRouter();
  const { contractId } = useParams();
  const formikRef = useRef(null);
  const [loader, setLoader] = useState(false);
  const [contractTypeData, setContractTypeData] = useState('');
  const [wageType, setWageType] = useState('fixed');

  // GET CONTRACT TYPE DETAILS
  const getContractTypeDetails = async () => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_CONTRACT_TYPE + `?id=${contractId}`
      );

      if (status === 200) {
        setLoader(false);
        const contractData = data?.data;
        setContractTypeData(contractData?.[0]);
      }
    } catch (error) {
      setLoader(false);
      setContractTypeData('');
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  useEffect(() => {
    if (isEdit) {
      getContractTypeDetails();
    }
  }, [isEdit]);

  useEffect(() => {
    contractTypeData?.wage_type && setWageType(contractTypeData?.wage_type);
  }, [contractTypeData?.wage_type]);

  return (
    <>
      {loader && <PreLoader />}
      <Formik
        innerRef={formikRef}
        initialValues={{
          contractName: ContractName
            ? ContractName
            : contractTypeData?.name
              ? contractTypeData?.name
              : '',
          contractStatus: contractTypeData
            ? contractTypeData?.status
            : 'active',
          duration_type: contractTypeData?.duration_type
            ? contractTypeData?.duration_type
            : 'week',
          working_hours: contractTypeData?.working_hours
            ? contractTypeData?.working_hours
            : '',
          wage_type: contractTypeData?.wage_type
            ? contractTypeData?.wage_type
            : 'fixed',
          wage_amount_type: contractTypeData?.fixed_types
            ? contractTypeData?.fixed_types
            : '',
          wage_per_hour: contractTypeData?.wage_per_hour
            ? contractTypeData?.wage_per_hour
            : '',
          remark: contractTypeData?.remark ? contractTypeData?.remark : '',
        }}
        enableReinitialize={true}
        validationSchema={Yup.object().shape({
          contractName: Yup.string().trim().required('This field is required'),
          contractStatus: Yup.string()
            .trim()
            .required('This field is required'),
          duration_type: Yup.string().trim().required('This field is required'),
          working_hours: Yup.number()
            .required('This field is required')
            .typeError('Must be a number'),
          wage_type: Yup.string().trim().required('This field is required'),
          wage_amount_type:
            wageType === 'fixed' &&
            Yup.string().trim().required('This field is required'),
          // .test(
          //   'wage_type_check',
          //   'This field is required',
          //   function (value) {
          //     const { wage_type } = this.parent;
          //     return wage_type !== 'fixed' || !!value;
          //   }
          // ),
          wage_per_hour: Yup.number()
            .required('This field is required')
            .typeError('Must be a number'),
        })}
        onSubmit={async (requestData) => {
          const sendData = {
            name: requestData?.contractName,
            status: requestData?.contractStatus,
            duration_type: requestData?.duration_type,
            working_hours: requestData?.working_hours,
            wage_type: requestData?.wage_type,
            ...(requestData?.wage_type === 'fixed' && {
              fixed_types: requestData?.wage_amount_type,
            }),
            wage_per_hour: requestData?.wage_per_hour,
            remark: requestData?.remark,
          };
          let url = '';
          if (isEdit) {
            url = URLS.CONTRACT_TYPE + `/${contractId}`;
          } else {
            url = URLS.CONTRACT_TYPE;
          }
          try {
            setLoader(true);
            const { status, data } = await axiosInstance.post(url, sendData);

            if (status === 200 || status === 201) {
              if (data?.status) {
                setApiMessage('success', data?.message);
                if (ContractName) {
                  handleCloseContract();
                  getContractTypeDetailss(requestData?.contractName);
                }
                setTimeout(() => {
                  if (ContractName) {
                  } else {
                    router.push('/contract-type');
                  }
                }, 4000);
              } else {
                setApiMessage('error', data?.message);
              }
              setLoader(false);
            }
          } catch (error) {
            setLoader(false);
            setApiMessage('error', error?.response?.data?.message);
          }
        }}
      >
        {({
          errors,
          touched,
          handleBlur,
          values,
          handleSubmit,
          handleChange,
        }) => (
          <Form onSubmit={handleSubmit}>
            <Box className={ContractName === undefined ? 'pt24 pb24' : 'pb24'}>
              {ContractName === undefined && <Divider className="mb8" />}

              <Box className="display-grid">
                <Box className="pt24">
                  <CustomTextField
                    InputLabelProps={{
                      shrink: true,
                    }}
                    fullWidth
                    id="contractName"
                    name="contractName"
                    value={values?.contractName}
                    label="Name *"
                    variant="filled"
                    placeholder="Name"
                    error={Boolean(touched.contractName && errors.contractName)}
                    helperText={touched.contractName && errors.contractName}
                    onBlur={handleBlur}
                    onChange={handleChange}
                  />
                </Box>

                <Box className="select-box pt24">
                  <CustomSelect
                    placeholder="Status"
                    options={identifiers?.CARD_STATUS}
                    value={values?.contractStatus}
                    className={
                      touched.contractStatus && errors.contractStatus
                        ? 'textfeild-error'
                        : ''
                    }
                    name="contractStatus"
                    error={touched.contractStatus && errors.contractStatus}
                    onChange={handleChange}
                    label={<span>Status *</span>}
                  />
                  {touched.contractStatus && errors.contractStatus && (
                    <Typography
                      variant="body2"
                      color="error"
                      className="field-error"
                    >
                      {errors.contractStatus}
                    </Typography>
                  )}
                </Box>
              </Box>
              <Box className="display-grid pt32">
                <Box className="select-box">
                  <CustomSelect
                    placeholder="Duration Type"
                    options={identifiers?.DURATION_TYPE}
                    value={values?.duration_type}
                    className={
                      touched.duration_type && errors.duration_type
                        ? 'textfeild-error'
                        : ''
                    }
                    name="duration_type"
                    error={touched.duration_type && errors.duration_type}
                    onChange={handleChange}
                    label={<span>Duration Type *</span>}
                  />
                  {touched.duration_type && errors.duration_type && (
                    <Typography
                      variant="body2"
                      color="error"
                      className="field-error"
                    >
                      {errors.duration_type}
                    </Typography>
                  )}
                </Box>
                <Box className="">
                  <CustomTextField
                    InputLabelProps={{
                      shrink: true,
                    }}
                    fullWidth
                    id="working_hours"
                    name="working_hours"
                    value={values?.working_hours}
                    label="Duration Working Hours *"
                    variant="filled"
                    placeholder="Duration Working Hours"
                    error={Boolean(
                      touched.working_hours && errors.working_hours
                    )}
                    helperText={touched.working_hours && errors.working_hours}
                    onBlur={handleBlur}
                    onChange={handleChange}
                    onInput={(e) => {
                      e.target.value = e.target.value.replace(/[^0-9.]/g, '');
                    }}
                  />
                </Box>
              </Box>
              <Box className="display-grid pt32">
                <Box className="select-box">
                  <CustomSelect
                    placeholder="Wage Type"
                    options={identifiers?.WAGE_TYPE}
                    value={values?.wage_type}
                    className={
                      touched.wage_type && errors.wage_type
                        ? 'textfeild-error'
                        : ''
                    }
                    name="wage_type"
                    error={touched.wage_type && errors.wage_type}
                    onChange={(e) => {
                      handleChange(e);
                      setWageType(e.target.value);
                    }}
                    label={<span>Wage Type *</span>}
                  />
                  {touched.wage_type && errors.wage_type && (
                    <Typography
                      variant="body2"
                      color="error"
                      className="field-error"
                    >
                      {errors.wage_type}
                    </Typography>
                  )}
                </Box>
                <Box className="">
                  <CustomTextField
                    InputLabelProps={{
                      shrink: true,
                    }}
                    fullWidth
                    id="wage_per_hour"
                    name="wage_per_hour"
                    value={values?.wage_per_hour}
                    label={
                      values?.wage_type === 'fixed'
                        ? 'Fixed amount *'
                        : 'Wage Per Hours *'
                    }
                    variant="filled"
                    placeholder={
                      values?.wage_type === 'fixed'
                        ? 'Fixed amount'
                        : 'Wage Per Hours'
                    }
                    error={Boolean(
                      touched.wage_per_hour && errors.wage_per_hour
                    )}
                    helperText={touched.wage_per_hour && errors.wage_per_hour}
                    onBlur={handleBlur}
                    onChange={(e) => {
                      handleChange(e);
                    }}
                    onInput={(e) => {
                      e.target.value = e.target.value.replace(/[^0-9.]/g, '');
                    }}
                  />
                </Box>
                {values?.wage_type === 'fixed' && (
                  <Box className="select-box">
                    <CustomSelect
                      placeholder="Amount per"
                      options={identifiers?.WAGE_AMOUNT_TYPE}
                      value={values?.wage_amount_type}
                      className={
                        touched.wage_amount_type && errors.wage_amount_type
                          ? 'textfeild-error'
                          : ''
                      }
                      name="wage_amount_type"
                      error={
                        touched.wage_amount_type && errors.wage_amount_type
                      }
                      onChange={handleChange}
                      label={<span>Amount per *</span>}
                    />
                    {touched.wage_amount_type && errors.wage_amount_type && (
                      <Typography
                        variant="body2"
                        color="error"
                        className="field-error"
                      >
                        {errors.wage_amount_type}
                      </Typography>
                    )}
                  </Box>
                )}
              </Box>
              <Box className="">
                <Box className="pt24">
                  <CustomTextField
                    InputLabelProps={{
                      shrink: true,
                    }}
                    fullWidth
                    id="remark"
                    name="remark"
                    value={values?.remark}
                    label="Remark"
                    variant="filled"
                    placeholder="Remark"
                    onBlur={handleBlur}
                    onChange={handleChange}
                  />
                </Box>
              </Box>
            </Box>

            <Box className="save-btn-box create-cancel-button">
              <CustomButton
                variant="contained"
                fontWeight="600"
                className="p16 secondary-button "
                background="#FFFFFF"
                backgroundhover="#39596e"
                colorhover="#FFFFFF"
                title={`${loader ? 'Saving...' : 'Save'}`}
                disabled={loader}
                fullWidth={false}
                type="submit"
              />
            </Box>
          </Form>
        )}
      </Formik>
    </>
  );
};

export default AddEditContractType;
