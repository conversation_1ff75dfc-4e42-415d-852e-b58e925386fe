import React, { useEffect, useState } from 'react';
import { Box, InputAdornment, TextField, InputLabel } from '@mui/material';
import DatePicker from 'react-datepicker';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import 'react-datepicker/dist/react-datepicker.css';
import KeyboardArrowLeftIcon from '@mui/icons-material/KeyboardArrowLeft';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import '../ReactWeekDatePicker/datepicker.scss';
import './datepicker.scss';

const CustomMonthRangePicker = ({
  onChange,
  value,
  className,
  disablePast,
  disabled,
  minDate,
  maxDate,
  placeholder,
  isClearable,
  label,
}) => {
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState(new Date());

  const handleChange = ([newStartDate, newEndDate]) => {
    console.log(newStartDate, 'ffffffffffff');
    setStartDate(newStartDate);
    setEndDate(newEndDate);
    onChange([newStartDate, newEndDate]);
  };

  useEffect(() => {
    if (value) {
      setStartDate(value[0]);
      setEndDate(value[1]);
    } else {
      setStartDate(new Date());
      setEndDate(new Date());
    }
  }, [value]);

  return (
    <Box
      className={
        className
          ? `${className} date-wrap react-datepicker-sec w100 react-Monthpicker-sec  `
          : 'date-wrap w100 react-datepicker-sec react-Monthpicker-sec '
      }
    >
      <InputLabel className="input-label">{label}</InputLabel>
      <Box className="date-picker-container">
        <DatePicker
          selected={startDate}
          startDate={startDate}
          endDate={endDate}
          onChange={handleChange}
          dateFormat="MM/yyyy"
          placeholderText="Select months"
          showMonthYearPicker
          selectsRange
          customInput={
            <TextField
              className="input_field"
              variant="outlined"
              fullWidth
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <CalendarMonthIcon sx={{ cursor: 'pointer' }} />
                  </InputAdornment>
                ),
              }}
            />
          }
        />
      </Box>
    </Box>
  );
};

export default CustomMonthRangePicker;
