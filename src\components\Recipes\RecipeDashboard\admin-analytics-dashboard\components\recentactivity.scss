.recent-activity {
  background-color: var(--color-white);
  border: var(--border-width-xs) solid #e5e7eb;
  border-radius: var(--border-radius-lg);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  // Match Category Performance height exactly
  height: auto;
  display: flex;
  flex-direction: column;
  
  // Total height should match ChartWidget including header, content, and footer
  min-height: calc(16rem + 60px); // 16rem content + header padding
  
  @media (min-width: 640px) {
    min-height: calc(18rem + 70px); // 18rem content + header padding
  }

  @media (min-width: 1024px) {
    min-height: calc(20rem + 80px); // 20rem content + header padding
  }



  // Header Section - Reduced padding to match Conversion Analytics
  &__header {
    padding: var(--spacing-md); // Reduced from lg to md
    border-bottom: var(--border-width-xs) solid #f3f4f6;
  }

  &__header-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
  }

  &__title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-lg);
    color: var(--text-color-primary);
  }

  // Filter Tabs
  &__filters {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
  }

  &__filter-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-md);
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    border: none;
    cursor: pointer;
    transition: all 0.15s ease-out;

    &--active {
      background-color: var(--color-primary);
      color: var(--text-color-white);
    }

    &--inactive {
      color: var(--text-color-slate-gray);
      background-color: transparent;

      &:hover {
        background-color: var(--color-primary-opacity);
        color: var(--color-primary);
      }
    }
  }

  // Content Section - Fixed height to match Category Performance exactly
  &__content {
    padding: var(--spacing-md);
    height: 16rem; // Match ChartWidget content height exactly
    overflow: hidden;
    display: flex;
    flex-direction: column;
    
    @media (min-width: 640px) {
      padding: var(--spacing-lg);
      height: 18rem; // Match ChartWidget tablet height
    }

    @media (min-width: 1024px) {
      height: 20rem; // Match ChartWidget desktop height
    }
  }

  // Empty state styling - centered without absolute positioning
  &__empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1; // Take all available space in flex container
    width: 100%;
    text-align: center;
  }

  // Loading State
  &__loading {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
  }

  &__loading-item {
    animation: pulse 2s infinite;
  }

  &__loading-content {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
  }

  &__loading-avatar {
    width: 32px;
    height: 32px;
    background-color: #e5e7eb;
    border-radius: var(--border-radius-full);
  }

  &__loading-text {
    flex: 1;
  }

  &__loading-line {
    background-color: #e5e7eb;
    border-radius: var(--border-radius-sm);

    &--title {
      height: 16px;
      width: 75%;
      margin-bottom: var(--spacing-sm);
    }

    &--subtitle {
      height: 12px;
      width: 50%;
    }
  }

  // Empty State
  &__empty {
    text-align: center;
    padding: var(--spacing-3xl) var(--spacing-lg);
  }

  &__empty-icon {
    color: var(--text-color-slate-gray);
    margin: 0 auto var(--spacing-md);
  }

  &__empty-text {
    font-family: var(--font-family-primary);
    color: var(--text-color-slate-gray);
  }

  // Activity List - Fixed height with vertical scrolling, no horizontal scroll
  &__list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    flex: 1; // Take available space within fixed content height
    overflow-y: auto; // Vertical scroll only
    overflow-x: hidden; // Prevent horizontal scroll
    padding-right: var(--spacing-xs);
    
    // Ensure content fits and scrolls properly within fixed height
    height: 100%;
    min-height: 0; // Allow flex item to shrink

    // Custom scrollbar styling
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: var(--border-radius-sm);
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: var(--border-radius-sm);

      &:hover {
        background: #a8a8a8;
      }
    }
    
    // For Firefox
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;
  }

  // Activity Item
  &__item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    transition: all 0.15s ease-out;

    &:hover {
      .recent-activity__item-content {
        background-color: #f9fafb;
        border-radius: var(--border-radius-md);
        padding: var(--spacing-sm);
        margin: calc(-1 * var(--spacing-sm));
      }
    }
  }

  &__item-icon {
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    // Activity type colors
    &--recipe-created {
      color: var(--color-success);
      background-color: var(--color-success-opacity);
    }

    &--user-registered {
      color: var(--color-primary);
      background-color: var(--color-primary-opacity);
    }

    &--recipe-approved {
      color: var(--color-success);
      background-color: var(--color-success-opacity);
    }

    &--comment-posted {
      color: var(--color-orange);
      background-color: rgba(253, 126, 20, 0.1);
    }

    &--recipe-featured {
      color: var(--color-warning);
      background-color: var(--color-warning-opacity);
    }

    &--user-upgraded {
      color: var(--text-color-slate-gray);
      background-color: var(--color-secondary);
    }

    &--recipe-reported {
      color: var(--color-danger);
      background-color: var(--color-danger-opacity);
    }

    &--bulk-import {
      color: var(--text-color-slate-gray);
      background-color: #f3f4f6;
    }

    // New API activity types
    &--recipe-view {
      color: var(--color-primary);
      background-color: var(--color-primary-opacity);
    }

    &--cta-click {
      color: var(--color-orange);
      background-color: rgba(253, 126, 20, 0.1);
    }

    &--contact-submission {
      color: var(--color-success);
      background-color: var(--color-success-opacity);
    }

    &--default {
      color: var(--text-color-slate-gray);
      background-color: #f3f4f6;
    }
  }

  &__item-content {
    flex: 1;
    min-width: 0; // Allow shrinking
    max-width: 100%; // Prevent horizontal overflow
    overflow: hidden; // Hide any overflow
    transition: all 0.15s ease-out;
  }

  &__item-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    gap: var(--spacing-md);
    width: 100%; // Ensure full width
    max-width: 100%; // Prevent overflow
  }

  &__item-main {
    flex: 1;
    min-width: 0; // Allow shrinking
    max-width: calc(100% - var(--spacing-xl)); // Leave space for timestamp
    overflow: hidden; // Prevent overflow
  }

  // User Info
  &__user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-xs);
  }

  &__user-avatar {
    width: 20px;
    height: 20px;
    border-radius: var(--border-radius-full);
    overflow: hidden;
    flex-shrink: 0;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  &__user-placeholder {
    width: 20px;
    height: 20px;
    background-color: #d1d5db;
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    .icon {
      color: #6b7280;
    }
  }

  &__user-name {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-primary);
    word-wrap: break-word; // Break long words
    overflow-wrap: break-word; // Ensure text wraps
    hyphens: auto; // Add hyphens for better wrapping
  }

  &__user-role {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    color: var(--text-color-slate-gray);
  }

  // Activity Description
  &__description {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--text-color-slate-gray);
    margin-bottom: var(--spacing-xs);
    word-wrap: break-word; // Break long words
    overflow-wrap: break-word; // Ensure text wraps
    hyphens: auto; // Add hyphens for better wrapping
    line-height: 1.4; // Better line spacing for wrapped text
  }

  &__target {
    font-weight: var(--font-weight-medium);
    color: var(--text-color-primary);
  }

  // Metadata
  &__metadata {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    color: var(--text-color-slate-gray);
  }

  // Timestamp
  &__timestamp {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    color: var(--text-color-slate-gray);
    white-space: nowrap;
    flex-shrink: 0;
  }
}

// Animations
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
