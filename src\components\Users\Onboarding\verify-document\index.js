'use client';

import React, { useEffect, useState } from 'react';
import {
  Box,
  Typography,
  FormGroup,
  FormControlLabel,
  Checkbox,
} from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import CustomTextField from '@/components/UI/CustomTextField';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CancelIcon from '@mui/icons-material/Cancel';
import CustomButton from '@/components/UI/CustomButton';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import LaunchIcon from '@mui/icons-material/Launch';
import FileUpload from '@/components/Users/<USER>/right-to-work/uploadFile';
import DialogBox from '@/components/UI/Modalbox';
import VerifyInfo from './Verifyinfo';
import HeaderImage from '@/components/UI/ImageSecurity';

export default function VerifyDocument({
  isUserScreen,
  UserId,
  getOnboardingChecklist,
  getCheckList,
  ViewAccessOnly,
  UserDetails,
}) {
  const [loader, setLoader] = useState(false);
  const [checked, setChecked] = useState([]);
  const [approveReject, setApproveReject] = useState();
  const [verifiedDoc, setVerifiedDoc] = useState();
  const [remark, setRemark] = useState('');
  const [isSubmit, setIsSubmit] = useState(false);
  const [createModal, setCreateModal] = useState(false);

  const VerifiedAccess = UserDetails?.user_status === 'verified';
  const RejectedAccess = UserDetails?.user_status === 'rejected';
  const handleChangeData = (dataValue) => {
    setVerifiedDoc(dataValue);
  };
  const UserVerification = async () => {
    setLoader(true);
    const body = new FormData();
    body.append('user_id', UserId);
    body.append('verification_status', approveReject);
    approveReject === 'verified' &&
      body.append('user_verification_doc', verifiedDoc?.[0]);
    // approveReject === 'rejected' && body.append(`checklist_ids`, checked);
    // approveReject === 'rejected' && body.append('last_reject_remark', remark);
    const sendData = {
      user_id: UserId,
      verification_status: approveReject,
      checklist_ids: checked,
      last_reject_remark: remark,
    };
    const config = {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    };

    try {
      const { status, data } = await axiosInstance.post(
        URLS.USER_VERIFICATION,
        approveReject === 'verified' ? body : sendData,
        approveReject === 'verified' ? config : {}
      );

      if (status === 200) {
        if (data?.status) {
          setApiMessage('success', data?.message);
          getOnboardingChecklist(UserId);
        } else {
          setApiMessage('error', data?.message);
        }
        setLoader(false);
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const handleCheck = (value) => {
    const currentIndex = checked.indexOf(value);
    const newChecked = [...checked];
    if (currentIndex === -1) {
      newChecked.push(value);
    } else {
      newChecked.splice(currentIndex, 1);
    }
    setChecked(newChecked);
  };
  const removeValue = async (state) => {
    if (state?.isApiData) {
      // DeleteUploadedFile(state?.name, setState);
    } else {
      setVerifiedDoc();
    }
  };
  const download = async (name, Docurl) => {
    const response = await fetch(Docurl);
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', name);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };
  useEffect(() => {
    if (UserDetails?.user_status === 'verified') {
      setApproveReject('verified');
      setVerifiedDoc({
        isApiData: true,
        name: UserDetails?.user_verification_doc,
        Docname: 'VERIFIED DOC',
        url: UserDetails?.user_verification_doc_link,
      });
    }
    if (UserDetails?.user_status === 'rejected') {
      setApproveReject('rejected');
      setRemark(UserDetails?.last_reject_remark);
      const rejectedList =
        getCheckList &&
        getCheckList?.length > 0 &&
        getCheckList?.filter((f) => f?.is_last_rejected);
      const rejectIds =
        rejectedList &&
        rejectedList?.length > 0 &&
        rejectedList?.map((r) => r?.id);
      rejectIds && setChecked(rejectIds);
    }
  }, [UserDetails, getCheckList]);
  return (
    <>
      <Box className={!isUserScreen && 'page-container'}>
        <Box
          className={
            isUserScreen
              ? 'right-to-work-page'
              : 'page-section right-to-work-page'
          }
        >
          <Typography className="title-text fw600 rtwc-info ">
            Review and Verify User Details{' '}
            <InfoIcon
              className="info-icon"
              onClick={() => setCreateModal(true)}
            />
          </Typography>
          <Typography className="title-text">
            Please review the details of the new onboarded users. You can accept
            or reject the user based on the provided documents and information.
            If rejecting, specify the reason and the form(s) to be corrected.
          </Typography>
          <Box className="form-checkbox">
            <FormGroup id="question">
              <FormControlLabel
                control={
                  <Checkbox
                    className="check-box "
                    icon={<CheckBoxOutlineBlankIcon className="uncheck-icon" />}
                    checkedIcon={<CheckBoxIcon className="check-icon" />}
                  />
                }
                className={
                  approveReject === 'verified'
                    ? 'check-box-form form-row title-text approved-checkbox'
                    : 'check-box-form form-row title-text'
                }
                onChange={() => {
                  setApproveReject('verified');
                  setVerifiedDoc();
                  setChecked([]);
                  setRemark('');
                }}
                name="ph_health"
                checked={approveReject === 'verified'}
                value={approveReject}
                label={'Approve'}
                disabled={ViewAccessOnly || RejectedAccess || VerifiedAccess}
              />
            </FormGroup>
            <FormGroup>
              <FormControlLabel
                control={
                  <Checkbox
                    className="check-box "
                    icon={<CheckBoxOutlineBlankIcon className="uncheck-icon" />}
                    checkedIcon={<CheckBoxIcon className="check-icon" />}
                    onChange={() => {
                      setApproveReject('rejected');
                      setVerifiedDoc();
                      setChecked([]);
                      setRemark('');
                    }}
                  />
                }
                className={
                  approveReject === 'rejected'
                    ? 'check-box-form form-row  title-text reject-checkbox'
                    : 'check-box-form form-row  title-text'
                }
                name="ph_health"
                checked={approveReject === 'rejected'}
                value={approveReject}
                label={'Reject'}
                disabled={ViewAccessOnly || RejectedAccess || VerifiedAccess}
              />
            </FormGroup>
          </Box>
          {approveReject === 'verified' ? (
            <Box className="employee-contract-checklist rtwc-upload-grid mt16">
              {verifiedDoc ? (
                <Box className="upload-download-sec rtwc-upload h100">
                  <Box className="title-section">
                    <Box className="title">
                      <Typography className="body-text fw600">
                        Verified document
                      </Typography>
                    </Box>
                    <CustomButton
                      variant="contained"
                      className="green-button title-text"
                      title={'Download'}
                      fullWidth={false}
                      disabled={!verifiedDoc?.isApiData}
                      onClick={() => {
                        download(verifiedDoc?.name, verifiedDoc?.url);
                      }}
                    />
                  </Box>

                  <Box className="action-sec">
                    {/* <Typography
                      className={
                        verifiedDoc?.isApiData
                          ? 'title-text fw400  cursor-pointer' //text-underline
                          : 'title-text fw400'
                      }
                      onClick={() =>
                        verifiedDoc?.isApiData &&
                        window.open(verifiedDoc?.url, '_blank')
                      }
                    >
                      {verifiedDoc?.[0]?.name
                        ? verifiedDoc?.[0]?.name
                        : verifiedDoc?.Docname}
                    </Typography> */}
                    <HeaderImage
                      type="url"
                      imageUrl={verifiedDoc?.isApiData && verifiedDoc?.url}
                      Content={
                        <Typography
                          className={
                            verifiedDoc?.isApiData
                              ? 'title-text fw600  cursor-pointer' //text-underline
                              : 'title-text fw600'
                          }
                        >
                          {verifiedDoc?.[0]?.name
                            ? verifiedDoc?.[0]?.name
                            : verifiedDoc?.Docname}
                        </Typography>
                      }
                    />
                    {verifiedDoc?.isApiData && (
                      <>
                        <HeaderImage
                          type="url"
                          imageUrl={verifiedDoc?.url}
                          Content={<LaunchIcon className="view-icon" />}
                          className="d-flex align-center"
                        />{' '}
                        {/* <LaunchIcon
                          className="view-icon"
                          onClick={() =>
                            window.open(verifiedDoc?.url, '_blank')
                          }
                        /> */}
                      </>
                    )}
                    {!verifiedDoc?.isApiData && (
                      <CancelIcon
                        className="delete-icon"
                        onClick={() => removeValue(verifiedDoc)}
                      />
                    )}
                  </Box>
                </Box>
              ) : (
                <FileUpload
                  name={'Verified document'}
                  handleChangeData={handleChangeData}
                  ViewAccessOnly={
                    ViewAccessOnly || RejectedAccess || VerifiedAccess
                  }
                />
              )}
            </Box>
          ) : approveReject === 'rejected' ? (
            <>
              {' '}
              {getCheckList &&
                getCheckList.length > 0 &&
                getCheckList.map((item) => {
                  if (item?.id === 3) {
                    return <></>;
                  }
                  return (
                    <FormGroup
                      className={'form-checkbox reject-checkbox-details'}
                    >
                      <FormControlLabel
                        control={
                          <Checkbox
                            className="check-box"
                            icon={
                              <CheckBoxOutlineBlankIcon className="uncheck-icon" />
                            }
                            checkedIcon={
                              <CheckBoxIcon className="check-icon" />
                            }
                          />
                        }
                        className="check-box-form form-row max-content title-text"
                        name="confirmation"
                        checked={checked?.includes(item?.id)}
                        onChange={() => {
                          handleCheck(item?.id);
                          setIsSubmit(false);
                        }}
                        disabled={
                          ViewAccessOnly || RejectedAccess || VerifiedAccess
                        }
                        label={item?.checkList_name}
                      />
                    </FormGroup>
                  );
                })}{' '}
              {checked?.length === 0 && isSubmit && (
                <Typography variant="body2" className="other-field-error-text">
                  This field is required
                </Typography>
              )}
              <Box className="pt8">
                <CustomTextField
                  id="remark"
                  name="remark"
                  multiline
                  rows={2}
                  onChange={(e) => {
                    setIsSubmit(false);
                    setRemark(e.target.value);
                  }}
                  fullWidth
                  error={Boolean(!remark && isSubmit)}
                  helperText={!remark && isSubmit}
                  placeholder="Remark"
                  value={remark}
                  disabled={RejectedAccess || ViewAccessOnly || VerifiedAccess}
                  className="additional-textfeild"
                  label={<>Remark</>}
                  required
                />
              </Box>
            </>
          ) : (
            <></>
          )}
          {!RejectedAccess && !VerifiedAccess && (
            <Box className={`pt32 ${!isUserScreen ? 'text-align' : ''}`}>
              <CustomButton
                variant="contained"
                className="red-button"
                title={`${loader ? 'Saving...' : 'Save'}`}
                onClick={() => {
                  if (approveReject === 'verified' && verifiedDoc) {
                    UserVerification();
                  } else if (
                    approveReject === 'rejected' &&
                    remark &&
                    checked?.length > 0
                  ) {
                    UserVerification();
                  }
                  if (approveReject === 'rejected') {
                    setIsSubmit(true);
                  }
                }}
                fullWidth={false}
                disabled={
                  ViewAccessOnly ||
                  loader ||
                  !approveReject ||
                  (approveReject === 'verified' && !verifiedDoc) ||
                  (approveReject === 'rejected' &&
                    (!remark || checked?.length === 0))
                }
              />
            </Box>
          )}
        </Box>
      </Box>
      <DialogBox
        open={createModal}
        handleClose={() => {
          setCreateModal(!createModal);
        }}
        title="Home Office online right to work check"
        className="resignation-dialogbox"
        content={<VerifyInfo />}
      />
    </>
  );
}
