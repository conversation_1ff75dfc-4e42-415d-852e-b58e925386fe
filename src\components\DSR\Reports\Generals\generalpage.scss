.dsr-tabs-content {
  .filters-icon {
    background-color: var(--color-primary);
    color: var(--color-white);
    border: 1px solid var(--color-primary);
    border-radius: 50px;
    width: 100%;
    max-width: 83px;
    padding: 3px 8px;
    cursor: pointer;
  }

  .filter-container {
    .subcategories-wrap {
      width: 100%;
      background-color: var(--color-white);
      max-height: calc(100vh - 193px - var(--banner-height));
      overflow: auto;
      display: flex;
      gap: 10px;
      padding-bottom: 2px;
      align-items: center;
      padding-left: 0px !important;
      @media (max-width: 1399px) {
        display: grid;
        grid-template-columns: calc(33% - 7px) calc(33% - 7px) calc(33% - 7px);
        row-gap: 15px;
      }
      @media (max-width: 1199px) {
        display: grid;
        grid-template-columns: calc(50% - 7px) calc(50% - 7px);
        row-gap: 15px;
      }
      @media (max-width: 768px) {
        display: grid;
        grid-template-columns: 100%;
        row-gap: 15px;
      }

      .category-section {
        display: flex;
        gap: var(--spacing-sm);
        .MuiButtonBase-root {
          padding: 5px 8px !important;
          svg {
            width: 25px;
            height: 25px;
          }
        }
      }
      .MuiBox-root {
        .MuiCheckbox-root {
          padding: 7px;

          .MuiSvgIcon-root {
            height: 20px;
            width: 20px;
          }
        }

        .MuiSelect-select {
          padding: 10.5px 14px;
        }
      }

      .css-1yuhvjn {
        margin-top: 0px;
      }

      li {
        list-style: none;
      }
    }
  }

  .dates-wrap {
    .show-date-wrap {
      font-size: 14px;
      font-weight: 400;
    }
  }

  .custom-date-fields {
    padding-top: 10px;
    align-items: flex-end;
  }
  .react-Monthpicker-sec {
    margin: 0px !important;
    .MuiFormLabel-root {
      margin-bottom: 0;
    }
    .date-picker-container {
      margin-top: 0 !important;
      .react-datepicker-wrapper:first-child {
        margin-right: 5px;
      }
    }
    .MuiInputBase-root {
      padding: 7px 0;
      background: var(--field-background);
    }
    .react-datepicker__month-text--in-selecting-range {
      background-color: var(--color-primary);
    }
    .react-datepicker__month-text:hover {
      background-color: var(--color-primary);
      color: var(--color-white);
    }
    .MuiFormControl-root {
      width: 100%;
      max-width: 150px;
      // max-width: 225px;
      input {
        padding: 0px 10px;
        font-size: 14px;
      }
      fieldset {
        height: 38px;
        min-height: 38px;
        border-radius: 5px !important;
        border: var(--field-border);
        margin-top: 0 !important;
      }
    }
    .MuiInputBase-root {
      display: flex;
      align-items: center;

      .MuiInputAdornment-root {
        padding: 5px;

        svg {
          height: 20px;
          width: 20px;
        }
      }
    }
  }
}

.select-input-wrap {
  z-index: 99999 !important;
  animation-fill-mode: none !important;
  .select-all-wrap {
    padding: 5px;
    color: var(--color-primary);
    font-size: 14px;
    cursor: pointer;
  }

  .apply-btn {
    background-color: var(--color-white) !important;
  }
}
