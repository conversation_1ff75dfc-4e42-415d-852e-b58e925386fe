// src/pages/user-limit-exceeded-dialog/index.jsx
import React from 'react';
import ProfileInComplete from '../../../../../public/images/ProfileInComplete.jpg';
import CustomButton from '@/components/UI/CustomButton';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { Box, Typography } from '@mui/material';
import '../StorageLimit/storagelimit.scss';

const ProfileInCompleteDialog = ({ isOrgView, setRestrictedModal }) => {
  const router = useRouter();
  const handleUpgradePlan = () => {
    setRestrictedModal(false);
    isOrgView
      ? router.push('/org/organization?tab=1')
      : router.push('/myprofile');
  };

  return (
    <Box className="user-limit-container storage-limit-container">
      {/* Dialog Overlay */}
      <Box className={`dialog-overlay visible`}>
        <Box className="dialog-container">
          <Box className={`dialog-content visible`}>
            {/* Dialog Content */}
            <Box className="dialog-body pt0">
              {/* Image */}
              <Box className="dialog-image-container">
                <Box className="dialog-image-wrapper">
                  <Image
                    src={ProfileInComplete}
                    alt="profile photo"
                    className="dialog-image"
                  />
                </Box>
              </Box>

              {/* Text Content */}
              <Box className="dialog-text">
                <Typography className="title-text fw600 dialog-message">
                  Your profile information is incomplete.
                </Typography>
                <Typography className="title-text dialog-description">
                  Almost there! Just complete your profile to unlock more
                  features.
                </Typography>
              </Box>

              {/* Action Buttons */}
              <Box className="dialog-actions profile-incomplete-actions">
                <CustomButton
                  variant="contained"
                  title="Back to Profile"
                  fullWidth
                  onClick={handleUpgradePlan}
                />
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default ProfileInCompleteDialog;
