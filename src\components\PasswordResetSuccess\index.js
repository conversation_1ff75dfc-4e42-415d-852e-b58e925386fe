'use client';
import { Box, Typography } from '@mui/material';
import Image from 'next/image';
import React from 'react';
import CustomButton from '../UI/button';
import ResetPasswordIcon from '../../../public/images/reset_Icon.svg';
import { useRouter } from 'next/navigation';
import './resetpassword.scss';

export default function PassResetSuccess() {
  const router = useRouter();

  const handleNavigate = () => {
    router.push('/login');
  };
  return (
    <Box className="reset-password-wrap">
      <Box className="reset-password">
        <Image
          src={ResetPasswordIcon}
          height={128}
          width={128}
          alt="Reset Password Icon"
          className="reset-icon-wrap"
        />
        <Box className="reset-text-wrap">
          <Typography className="text-wrap heading-text" variant="h5">
            Password Successfully Reset
          </Typography>

          <Typography className="sub-heading-text reset-text">
            Your password has been updated successfully. You can now log in with
            your new password.
          </Typography>
        </Box>
        <Box className="back-to-login-btn-wrap" textAlign="center">
          <CustomButton
            className="back-to-login-btn"
            fontWeight="600"
            variant="contained"
            background="#39596e"
            backgroundhover="#39596e"
            colorhover="#FFFFFF"
            title="Back To Login"
            type="submit"
            onClick={() => handleNavigate()}
          />
        </Box>
      </Box>
    </Box>
  );
}
