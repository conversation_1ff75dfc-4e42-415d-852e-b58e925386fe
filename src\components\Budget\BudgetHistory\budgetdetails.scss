.budget-history-container {
  overflow: hidden;
  padding-right: 0;
  .right-sidebar-container {
    max-width: 300px;
    width: 100%;
    height: calc(100vh - 69px - var(--banner-height));
    background-color: var(--color-white);
    padding-bottom: 20px;
    border-radius: 0px;
    margin-left: 20px;
    margin-right: -18px;
    margin-bottom: -30px;
    // overflow: auto;
    @media (max-width: 899px) {
      margin-right: -30px;
    }
    @media (max-width: 599px) {
      // height: calc(100vh - 79px);
      margin-right: -15px;
    }
    .contract-sidebar-header {
      padding: 24px 16px;
    }
    .history-log {
      padding: 10px 20px;
    }
    .history-log.active {
      background-color: var(--color-dark-lavender);
    }
  }
  .budget-version-section {
    width: calc(100% - 320px);
    overflow: scroll;
    height: calc(100vh - 130px - var(--banner-height));
  }
  .back-icon {
    margin-right: 12px;
  }
  .info-icon {
    width: 20px;
    height: 20px;
    fill: var(--color-primary);
  }
}
