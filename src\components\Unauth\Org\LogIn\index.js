'use client';

import React, { useContext, useState, useEffect } from 'react';
import * as Yup from 'yup';
import AuthContext from '@/helper/authcontext';
import VerifiedIcon from '@mui/icons-material/Verified';
import {
  Box,
  Typography,
  InputAdornment,
  IconButton,
  CircularProgress,
  TextField,
} from '@mui/material';
import CustomButton from '@/components/UI/button';
import { useRouter, useSearchParams } from 'next/navigation';
import { Formik, Form } from 'formik';
import {
  saveToStorage,
  removeFromStorage,
  fetchFromStorage,
} from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { INFO_LINKS, ORG_URLS } from '@/helper/constants/urls';
import {
  setApiMessage,
  checkOrganizationRole,
} from '@/helper/common/commonFunctions';
import {
  EyeCloseIcon,
  EyeShowIcon,
  LogInLeftVector,
  LogInRightVector,
  ProfileRoundIcon,
} from '@/helper/common/images';
import PreLoader from '@/components/UI/Loader';
import OneSignal from 'react-onesignal';
import Link from 'next/link';
import './login.scss';

export default function LogIn() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const UserId = searchParams.get('data');
  const { setAuthState, setOrgDetails } = useContext(AuthContext);
  const [showEnterPassword, setShowCurrentPassword] = useState(false);
  const [loader, setLoader] = useState(false);
  const [loaderVerify, setLoaderVerify] = useState(false);
  let isDeviceId =
    typeof window !== 'undefined' && fetchFromStorage(identifiers?.DEVICEID);

  const deviceDetails =
    typeof window !== 'undefined' && fetchFromStorage(identifiers?.DEVICEDATA);

  // GET IP ADDRESS
  const getIPAddress = async () => {
    try {
      const response = await fetch('https://api.ipify.org?format=json');
      if (response.ok) {
        const data = await response.json();
        const deviceData = { ip: data?.ip };

        const locationResp = await axiosInstance.get(
          `https://ipinfo.io/${data?.ip}/geo?token=f958fa1525009d`
        );
        if (locationResp?.data) {
          const locationData = locationResp?.data;
          deviceData.address =
            `${locationData?.city},${locationData?.region},${locationData?.country},${locationData?.postal}` ||
            '';
          deviceData.location = locationData?.loc || '';
        }
        saveToStorage(identifiers?.DEVICEDATA, deviceData);
      } else {
        // console.error('Failed to fetch IP address:', response.statusText);
      }
    } catch (error) {
      console.error('Error fetching IP address:', error);
    }
  };
  useEffect(() => {
    if (deviceDetails === undefined || deviceDetails === null) {
      getIPAddress();
    }
  }, []);
  const VerifyAuthToken = async (id) => {
    let sendData = {
      userId: id,
    };
    try {
      setLoaderVerify(true);
      const { status, data } = await axiosInstance.put(
        ORG_URLS.VERIFY_AUTH_TOKEN,
        sendData
      );
      if (status === 200) {
        if (data.status) {
          setApiMessage('success', data?.message);
        } else {
          setApiMessage('error', data?.message);
        }
        setLoaderVerify('verified');
      }
    } catch (error) {
      setLoaderVerify(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const updateOnesignalToken = async (playerId) => {
    // setLoader(true);
    const sendData = {
      webAppToken: playerId,
    };
    try {
      const { status } = await axiosInstance.post(
        URLS?.UPDATE_ONESIGNAL_TOKEN,
        sendData
      );

      if (status === 200) {
        // setLoader(false);
      }
    } catch (error) {
      console.error(error);
      // setLoader(false);
    }
  };
  const getOrgbyID = async (id) => {
    try {
      const { status, data } = await axiosInstance.get(
        ORG_URLS.GET_ORGANIZATION_BY_ID + `${id}`
      );
      if (status === 200 || status === 201) {
        if (data?.status) {
          setOrgDetails(data?.data);
          if (
            !data?.data?.attributes?.email ||
            !data?.data?.attributes?.contact_person
          ) {
            router.push('/org/organization');
          } else {
            router.push('/chart-dashboard');
          }
        } else {
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  useEffect(() => {
    if (UserId) {
      VerifyAuthToken(UserId);
    }
  }, [UserId]);
  const handleNavigate = () => {
    router.push('/sign-up');
  };
  return (
    <Box className="login-page-wrap">
      {loader && <PreLoader />}
      <Box className="login-wrap">
        <Box className="login-pages">
          <Box className="login-blocks">
            {loaderVerify && loaderVerify !== 'verified' ? (
              <>
                <Box className="content-loader">
                  <CircularProgress className="loader" color="inherit" />
                  <Typography className="p14 pt8 main-heading">
                    Verifying...Please wait
                  </Typography>
                </Box>
              </>
            ) : (
              loaderVerify === 'verified' && (
                <Typography className="p16 email-verified-content fw600 text-align">
                  <VerifiedIcon className="verified-icon" />
                  <span className="email-text">
                    Your email has been verified successfully. You may log in.
                  </span>
                </Typography>
              )
            )}
            {/* <Box className='login-screen'>
              <Image
                src={NVlogo}
                className='header-logo cursor-pointer '
                alt='img'
                onClick={() => router.push('/login')}
              />
            </Box> */}
            <Typography variant="h2" className="main-heading heading-text">
              Log In to Your Account
            </Typography>
            <Formik
              initialValues={{
                user_name: '',
                password: '',
              }}
              enableReinitialize={true}
              validationSchema={Yup.object().shape({
                user_name: Yup.string().required('Username is required'),
                password: Yup.string()
                  .trim()
                  .required('Password is required')
                  .min(8, 'Password must be at least 8 characters')
                  .matches(
                    /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*?&#])[A-Za-z\d@$!%*?&#]+$/,
                    'Password must include at least one uppercase letter, one lowercase letter, one number, and one special character'
                  ),
              })}
              onSubmit={async (requestData) => {
                let sendData;
                sendData = {
                  username: requestData?.user_name,
                  password: requestData?.password,
                };

                try {
                  setLoader(true);
                  const { status, data } = await axiosInstance.post(
                    ORG_URLS.USER_LOGIN,
                    sendData
                  );

                  if (status === 200) {
                    if (data?.status) {
                      setApiMessage('success', data?.message);
                      saveToStorage(identifiers?.AUTH_DATA, data?.data);
                      saveToStorage(identifiers?.NORMAL_LOGIN, false);
                      saveToStorage(identifiers?.USER_ID, {
                        ...data?.data,
                        id: data?.data?.user_id,
                      });
                      isDeviceId && updateOnesignalToken(isDeviceId);
                      setTimeout(() => {
                        const playerId = JSON.parse(
                          JSON.stringify(OneSignal?.User?.PushSubscription)
                        )?.id;
                        if (playerId && playerId !== undefined) {
                          console.error('error', playerId);
                          saveToStorage(identifiers?.DEVICEID, playerId);
                          updateOnesignalToken(playerId);
                        }
                      }, 2000);
                      setTimeout(() => {
                        if (
                          data?.data?.user_status === 'pending' &&
                          checkOrganizationRole('staff')
                        ) {
                          setAuthState({ Email: requestData?.email });
                          saveToStorage(identifiers?.EMAIL, requestData?.email);
                          router.push('/reset-password');
                        } else if (checkOrganizationRole('org_master')) {
                          getOrgbyID(data?.data?.organizationId);
                        } else if (checkOrganizationRole('super_admin')) {
                          router.push('/sorg/organization');
                        } else if (checkOrganizationRole('staff')) {
                          router.push('/myprofile');
                          saveToStorage(identifiers?.LOGIN_ORG, true);
                        } else {
                          router.push('/chart-dashboard');
                          saveToStorage('IsjustLogin', true);
                          removeFromStorage(identifiers?.EMAIL);
                        }
                      }, 1000);
                    } else {
                      setApiMessage('error', data?.message);
                    }
                    setLoader(false);
                  }
                } catch (error) {
                  setApiMessage('error', error?.response?.data?.message);
                  setLoader(false);
                }
              }}
            >
              {({
                errors,
                touched,
                handleBlur,
                values,
                handleSubmit,
                handleChange,
                // dirty,
                // isValid,
              }) => (
                <Form className="login-form-wrap" onSubmit={handleSubmit}>
                  <Box className="input-wrap">
                    <Typography variant="h6" className="un-auth-label-wrap">
                      Username<span className="primary-color">*</span>
                    </Typography>
                    <TextField
                      InputLabelProps={{
                        shrink: true,
                      }}
                      id="user_name"
                      name="user_name"
                      value={values?.user_name}
                      className="w100"
                      variant="standard"
                      placeholder="Username"
                      error={Boolean(touched?.user_name && errors?.user_name)}
                      helperText={touched?.user_name && errors?.user_name}
                      onBlur={handleBlur}
                      onChange={handleChange}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton disableRipple>
                              <Box className="eye-wrap">
                                <ProfileRoundIcon />
                              </Box>
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>

                  <Box className="input-wrap">
                    <Typography variant="h6" className="un-auth-label-wrap">
                      Password<span className="primary-color">*</span>
                    </Typography>
                    <TextField
                      InputLabelProps={{
                        shrink: true,
                      }}
                      id="password"
                      name="password"
                      value={values?.password}
                      placeholder="password"
                      type={showEnterPassword ? 'text' : 'password'}
                      variant="standard"
                      className={
                        touched?.password && errors?.password
                          ? 'w100 password-textfield password-error'
                          : 'w100 password-textfield'
                      }
                      error={Boolean(touched?.password && errors?.password)}
                      helperText={touched?.password && errors?.password}
                      onBlur={handleBlur}
                      onChange={handleChange}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end" className="eye-icon">
                            <IconButton
                              disableRipple
                              onClick={() =>
                                setShowCurrentPassword(!showEnterPassword)
                              }
                            >
                              <Box className="eye-wrap">
                                {!showEnterPassword ? (
                                  <EyeShowIcon className="eye-icon-wrap" />
                                ) : (
                                  <EyeCloseIcon className="eye-icon-wrap" />
                                )}
                              </Box>
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>
                  <Typography
                    variant="h6"
                    className="forgot-wrap cursor-pointer "
                    textAlign="end"
                  >
                    <Typography
                      variant="span"
                      className="forgot-text"
                      onClick={() => {
                        saveToStorage(identifiers?.EMAIL, values?.user_name);
                        setAuthState({ Email: values?.user_name });
                        router.push('/forgot-password');
                      }}
                    >
                      Forgot Password?
                    </Typography>
                  </Typography>

                  <Box className="log-in-btn-wrap" textAlign="center">
                    <CustomButton
                      fullWidth
                      className="log-in-btn"
                      type="submit"
                      fontWeight="600"
                      variant="contained"
                      background="#39596e"
                      backgroundhover="#39596e"
                      colorhover="#FFFFFF"
                      title="Log In"
                      isLogin={true}
                    />
                  </Box>

                  <Typography className="dont-have-acc-text">
                    Don’t have an account?
                    <span className="sign-in" onClick={() => handleNavigate()}>
                      Sign Up
                    </span>
                  </Typography>

                  <Typography className="dont-have-acc-text need-help-text">
                    Need help?
                    <span className="sign-in">
                      <Link
                        href={INFO_LINKS?.contactUs}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="contact-us-wrap"
                      >
                        Contact Us
                      </Link>
                    </span>
                  </Typography>
                </Form>
              )}
            </Formik>
          </Box>
        </Box>
      </Box>
      <Box className="left-vector">
        <LogInLeftVector />
      </Box>
      <Box className="right-vector">
        <LogInRightVector />
      </Box>
    </Box>
  );
}
