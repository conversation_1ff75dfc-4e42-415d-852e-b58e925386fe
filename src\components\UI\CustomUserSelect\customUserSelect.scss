// Custom User Select Component Styles - Based on CustomSelect
@import '../../../styles/variable.scss';

.custom-user-select-wrapper {
  width: 100%;

  .field-label {
    font-family: var(--font-family-primary);
    margin-bottom: var(--spacing-xxs);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-bright-blue);
    display: flex;
    align-items: center;

    &.error-label {
      color: var(--text-error);
    }

    .required {
      color: var(--text-error);
      margin-left: var(--spacing-xxs);
    }
  }

  .user-select-container {
    width: 100%;
    font-family: var(--font-family-primary);

    &.error-border {
      .user-select__control {
        border-color: var(--border-color-error);
      }
    }
  }

  .select-field-error-text {
    font-family: var(--font-family-primary);
    color: var(--text-error);
    margin-top: var(--spacing-xxs);
    font-size: var(--font-size-xs);
    line-height: var(--line-height-xs);
  }
}

// User Option Styles - Based on CustomSelect patterns
.user-option-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  width: 100%;

  .user-avatar-wrapper {
    flex-shrink: 0;

    .user-avatar-image {
      width: 20px;
      height: 20px;
      border-radius: var(--border-radius-full);
      object-fit: cover;
      border: 1px solid var(--border-color-light-gray);
    }

    .user-avatar-fallback {
      width: 20px;
      height: 20px;
      font-size: var(--font-size-xs);
      font-weight: var(--font-weight-medium);
      background-color: var(--color-primary);
      color: white;
    }
  }

  .user-info {
    flex: 1;
    min-width: 0; // Allow text truncation

    .user-name {
      font-family: var(--font-family-primary);
      font-size: var(--font-size-sm);
      line-height: var(--line-height-sm);
      color: inherit;
      margin: 0;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .selected-icon {
    flex-shrink: 0;
    color: inherit;
    display: flex;
    align-items: center;

    svg {
      width: 16px;
      height: 16px;
    }
  }
}

// Single Value Styles (Selected item display) - CustomSelect pattern
.user-single-value-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);

  .user-avatar-wrapper {
    line-height: 0px;
    .user-avatar-image {
      width: 18px;
      height: 18px;
      border-radius: var(--border-radius-full);
      object-fit: cover;
      border: 1px solid var(--border-color-light-gray);
    }

    .user-avatar-fallback {
      width: 18px;
      height: 18px;
      font-size: 10px;
      font-weight: var(--font-weight-medium);
      background-color: var(--color-primary);
      color: white;
    }
  }

  .user-name {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-sm);
    color: var(--text-color-black);
    margin: var(--spacing-none);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

// React Select Override Styles - Based on CustomSelect
body {
  .user-select {
    &__control {
      border-radius: var(--field-radius);
      border: var(--field-border);
      box-shadow: none;
      transition: border-color 0.2s ease-in-out;
      background: var(--field-background);
      cursor: pointer;
      min-height: 36px;
      height: 36px;

      &:hover {
        border: var(--field-border-primary);
      }

      &--is-focused {
        border: var(--field-border-primary);
        box-shadow: none;
      }

      &--is-disabled {
        cursor: not-allowed;
      }
    }

    &--is-disabled {
      cursor: not-allowed;
      pointer-events: auto;

      .user-select__dropdown-indicator {
        color: var(--text-color-muted);
      }
    }

    &__menu-portal {
      z-index: 9999;
    }

    &__menu {
      border-radius: var(--border-radius-xs);
      box-shadow: var(--box-shadow-xs);
      margin-top: var(--spacing-xs);
      z-index: 9999;
      border: var(--normal-sec-border);
      animation: dropdownFadeIn 0.2s ease-out;
      min-width: 100%;
      width: 100%;
    }

    &__option {
      padding: 8px 16px;
      cursor: pointer;
      transition: background-color 0.2s ease-in-out;
      font-size: var(--font-size-sm);
      line-height: var(--line-height-sm);

      &--is-selected {
        background-color: var(--color-primary);
        color: var(--text-color-white);
      }

      &:hover {
        background-color: var(--color-primary);
        color: var(--text-color-white);
      }

      &--is-disabled {
        cursor: not-allowed;
      }
    }

    &__value-container {
      padding: 7px 12px;
      max-height: 69px;
      overflow: auto;
    }

    &__input {
      margin: var(--spacing-none);
      padding: var(--spacing-none);
      color: var(--text-color-black);
      font-size: var(--font-size-sm);
      line-height: var(--line-height-sm);
    }

    &__input-container {
      margin: var(--spacing-none);
      padding: var(--spacing-none);
      line-height: 20px;
    }

    &__placeholder {
      color: var(--field-placeholder);
      font-size: var(--font-size-sm);
      line-height: var(--line-height-sm);
      margin: var(--spacing-none);
      letter-spacing: 0px;
      opacity: 0.8;
    }

    &__single-value {
      color: var(--text-color-black);
      font-size: var(--font-size-sm);
      line-height: var(--line-height-sm);
      margin: var(--spacing-none);

      &--is-disabled {
        color: var(--text-color-muted);
      }
    }

    &__dropdown-indicator {
      color: var(--icon-color-primary);
      padding: var(--spacing-none) var(--spacing-sm) var(--spacing-none)
        var(--spacing-xs);
      transition: all 0.2s ease-in-out;
      transform-origin: center;
      display: flex;
      align-items: center;

      &:hover {
        color: var(--text-color-slate-gray);
      }
    }

    &__control--menu-is-open {
      .user-select__dropdown-indicator {
        transform: scaleY(-1);
      }
    }

    &__clear-indicator {
      color: var(--icon-color-slate-gray);
      padding: var(--spacing-none);

      &:hover {
        color: var(--text-color-slate-gray);
      }
    }

    &__indicator-separator {
      display: none;
    }
  }
}

// Smooth dropdown animation
@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Avatar display styles
.user-avatar-image {
  display: block;
}

.user-avatar-fallback {
  display: flex;
  align-items: center;
  justify-content: center;
}
