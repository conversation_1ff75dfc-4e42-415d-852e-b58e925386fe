@import '@/styles/variable.scss';

.forgot-pass-wrap {
  position: relative;
  padding: 0px 20px;

  .forgot-pass-container {
    width: 100%;
    max-width: 550px;
    margin: 0 auto;

    .forgot-pass {
      border-top-left-radius: 22px;
      border-top-right-radius: 22px;
      background-color: var(--color-white);
      padding: 52px;
      height: 100%;
      min-height: 550px;
      position: relative;
      z-index: 1;

      .forgot-pass-text {
        padding-bottom: 22px;

        @media (max-width: 425px) {
          text-align: center;
        }
      }

      .pass-sub-text {
        padding-bottom: 32px;
      }

      .forgot-pass-input-wrap {
        .forgot-pass-input {
          .MuiInputBase-root {
            display: flex;
            align-items: center;

            .MuiInputBase-input {
              font-size: 16px;
              line-height: 26px;
              font-weight: 400;
              margin: 5px 0px 10px;
              height: 100%;
              min-height: 26px;
              padding: 0px;
              color: #1d1e25 !important;
              font-family: 'PolySansTrial-Bulky', sans-serif !important;

              .image-wrap {
                height: 24px;
                width: 24px;
              }

              &::placeholder {
                font-size: 16px !important;
                line-height: 26px !important;
                font-weight: 400 !important;
                margin: 5px 0px 10px !important;
                padding: 0px !important;
                font-family: var(--font-family-poly-slim) !important;
                text-transform: capitalize !important;
              }
            }

            &::before {
              border-bottom: 1px solid #e2e2ea;
            }
          }

          .Mui-error {
            font-family: 'PolySansTrial-Bulky', sans-serif !important;

            &::before {
              border-bottom: 1px solid var(--color-danger);
            }

            .email-icon-wrap {
              svg {
                path {
                  stroke: var(--color-danger);
                }
              }
            }
          }
        }
      }

      .submit-btn-wrap {
        margin: 32px 0px 22px;

        .submit-btn {
          border-radius: 12px !important;
          background-color: var(--color-primary) !important;
          font-size: 16px !important;
          font-weight: 300 !important;
          line-height: 20px !important;
          padding: 16px !important;
          border: none !important;
          color: var(--text-color-white) !important;
          font-family: var(--font-family-poly-median) !important;

          &:hover {
            background-color: var(--color-primary) !important;
            color: var(--text-color-white) !important;
            box-shadow: none !important;
            font-family: var(--font-family-poly-median) !important;
          }
        }
      }

      .remember-pass {
        font-size: 16px;
        font-weight: 300;
        line-height: 26px;
        text-align: center;
        color: var(--text-lavender-gray);
        font-family: var(--font-family-poly-slim) !important;

        .login-wrap {
          cursor: pointer;
          margin-left: 6px;
          font-weight: 300;
          color: var(--text-color-primary) !important;
          font-family: var(--font-family-poly-median) !important;
        }
      }

      @media (max-width: 767px) {
        min-height: 100%;
      }

      @media (max-width: 575px) {
        padding: 35px 25px;
      }

      @media (max-width: 375px) {
        padding: 35px 14px;
      }
    }

    .left-vector-wrap {
      height: 392px;
      width: 297px;
      position: absolute;
      top: 140px;
      left: 52px;
      z-index: 0;

      @media (max-width: 1360px) {
        left: 20px;
      }

      @media (max-width: 1199px) {
        left: -40px;
      }

      @media (max-width: 767px) {
        display: none;
      }
    }

    .right-vector-wrap {
      position: absolute;
      top: 140px;
      right: 52px;
      z-index: 0;

      @media (max-width: 1360px) {
        right: 20px;
      }

      @media (max-width: 1199px) {
        right: -30px;
      }

      @media (max-width: 767px) {
        display: none;
      }
    }
  }

  @media (max-width: 375px) {
    padding: 0px 14px;
  }
}
