.folder-section-wrapper {
  display: flex;
  gap: var(--spacing-md);
  align-items: flex-start;
  height: 100%;
  @media (max-width: 768px) {
    flex-direction: column;
  }
  .folder-section-left {
    max-width: 250px;
    width: 100%;
    background-color: var(--color-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-xs);
    height: 100%;
    overflow: hidden;
    @media (max-width: 768px) {
      flex-direction: column;
      height: 60px;
      max-width: 100%;
    }
    .folder-section-left-title {
      padding: var(--spacing-md);
    }
    .folder-side-menu-list {
      padding: var(--spacing-none) var(--spacing-lg);
      height: calc(100% - 49px);
      overflow: auto;
    }
  }
  .folder-section-right {
    width: 100%;
    background-color: var(--color-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-xs);
    height: 100%;
    overflow: hidden;
    .folder-section-right-content {
      padding: var(--spacing-xxl);
      height: calc(
        100% - 24px
      ); // change claculate base on tab height and scroll height
      overflow: auto;
      .emp-contract-page {
        .emp-contract-folder-sec {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
          gap: 22px;
          @media (max-width: 1200px) {
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
          }
        }
        .emp-contract-folder-sec > div,
        .emp-contract-folder-sec > div > div {
          width: 100%;
          max-width: 250px !important;
        }
        .action-btn-icon {
          svg,
          path {
            // stroke: var(--icon-color-white);
            stroke: var(--icon-color-black);
          }
          &.active {
            svg,
            path {
              stroke: var(--icon-color-primary);
            }
          }
        }
      }
      .grid-view-folder {
        width: 50px;
        height: 50px;
      }
    }
  }
}

.folder-all-action {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  .folder-action-tooltip {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: var(--spacing-lg);
    padding: var(--field-padding);
    border-radius: var(--border-radius-md);
    width: 100%;
    background-color: var(--color-primary-opacity);
    .selected-category {
      border-right: 1px solid var(--border-color-primary);
      padding-right: var(--spacing-md);
      color: var(--text-color-primary);
      svg {
        width: var(--icon-size-md);
        height: var(--icon-size-md);
        fill: var(--icon-color-black);
      }
    }
    .action-section.action-reset {
      svg {
        fill: none;
        stroke: var(--icon-color-primary);
      }
    }
    .action-section:last-child {
      border-right: 0;
    }
    .action-section {
      display: flex;
      align-items: center;
      svg {
        margin-right: var(--spacing-xsm);
        cursor: pointer;
        fill: var(--icon-color-primary);
      }
      .svg-icon {
        width: var(--icon-size-md);
        height: var(--icon-size-md);
      }
    }
  }
}
.move-to-list {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px;
  .move-btn {
    display: none;
  }
  &:hover {
    background: var(--color-primary-opacity);
    border-radius: var(--border-radius-md);
    .move-btn {
      display: block;
      border: 1px solid var(--border-color-primary);
      border-radius: var(--border-radius-md);
      padding: 2px 8px;
      font-size: var(--font-size-xs);
      cursor: pointer;
    }
  }
}
