'use client';

import React, { useState } from 'react';
import { Divider, Tooltip } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import BusinessCenterOutlinedIcon from '@mui/icons-material/BusinessCenterOutlined';
import LocationOnOutlinedIcon from '@mui/icons-material/LocationOnOutlined';
import ChatOutlinedIcon from '@mui/icons-material/ChatOutlined';
import CustomButton from '../../CustomButton';
import { Formik, Form, Field } from 'formik';
import * as Yup from 'yup';
import moment from 'moment';
import axiosInstanceOrg from '@/helper/axios/axiosInstanceOrg';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { ROTA_URLS } from '@/helper/constants/urls';
import PreLoader from '@/components/UI/Loader';
import UserAvatar from '../../Avatar/UserAvatar';
import './shiftDropTooltip.scss';

// Validation schema using Yup
const AddShiftSchema = Yup.object().shape({
  shiftNote: Yup.string().max(300, 'Note cannot exceed 300 characters'),
});

export const ShiftDropTooltip = ({
  handleAddShiftTooltipClose,
  selectedShiftData,
  getRotaShiftList,
}) => {
  const [isLoading, setIsLoading] = useState(false);

  // Initial form values
  const initialValues = {
    shiftNote: '',
  };

  const start_time = moment(selectedShiftData?.startTime).utc().format('HH:mm');
  const end_time = moment(selectedShiftData?.endTime).utc().format('HH:mm');
  const role_name = selectedShiftData?.role?.role_name || 'No Role';
  const shift_location =
    selectedShiftData?.branch?.branch_name || 'No Location';

  const handleSubmit = async (values, { setSubmitting }) => {
    const sendData = {
      reason: values?.shiftNote,
    };
    setIsLoading(true);
    try {
      const { status, data } = await axiosInstanceOrg.put(
        ROTA_URLS?.DROP_SHIFT_URL + `/${selectedShiftData?.id}`,
        sendData
      );
      if (status === 200 || status === 201) {
        setApiMessage('success', data?.message);
        setIsLoading(false);
        getRotaShiftList();
        handleAddShiftTooltipClose();
        setIsLoading(false);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setIsLoading(false);
    }

    setSubmitting(false);
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={AddShiftSchema}
      onSubmit={handleSubmit}
    >
      {({ values, isSubmitting, dirty }) => {
        return (
          <Form className="shift-drop-container">
            <div>{isLoading && <PreLoader />}</div>
            <div className="shift-drop-header-container">
              <div>
                <Tooltip
                  title={
                    <p className="p12">
                      Shift drops allow you to request not to work a shift
                      you've been assigned.
                    </p>
                  }
                  placement="top"
                  arrow
                  classes={{
                    tooltip:
                      'calendar-event-list-tooltip-custom calendar-event-left-shift-tooltip',
                  }}
                >
                  <p className="shift-drop-header-title">Request shift drop</p>
                </Tooltip>
              </div>

              <div className="shift-drop-header-actions">
                <CustomButton
                  title={isSubmitting ? 'Confirm...' : 'Confirm'}
                  type="submit"
                  disabled={isSubmitting || !dirty}
                />

                <div
                  className="close-shift-tooltip"
                  onClick={handleAddShiftTooltipClose}
                >
                  <CloseIcon />
                </div>
              </div>
            </div>
            <Divider />
            <div className="shift-drop-details-container">
              <div className="shift-drop-details-wrapper">
                <div className="shift-drop-details">
                  <div className="shift-drop-user-details">
                    <div>
                      <UserAvatar
                        name={selectedShiftData?.user?.user_full_name}
                        src={selectedShiftData?.user?.user_avatar_link}
                        classname="shift-drop-avatar"
                      />
                    </div>
                    <p className="shift-drop-name">Your shift</p>
                  </div>
                  <div className="shift-drop-shift-details">
                    <div className="shift-drop-shift-time-role">
                      <p className="shift-drop-shift-date">
                        {moment(
                          selectedShiftData?.startTime || new Date()
                        ).format('ddd DD MMM YYYY')}
                      </p>
                      <p className="shift-drop-shift-time">
                        {start_time} - {end_time}
                      </p>
                      <p className="shift-drop-shift-icon-text">
                        <BusinessCenterOutlinedIcon />
                        <span>{role_name}</span>
                      </p>
                      <p className="shift-drop-shift-icon-text">
                        <LocationOnOutlinedIcon />
                        <span>{shift_location}</span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                {/* Shift Note */}
                <div className="d-center-start shift-drop-note-row">
                  <div className="icon">
                    <ChatOutlinedIcon />
                  </div>
                  <div className="shift-drop-note-input-wrapper">
                    <Field
                      as="input"
                      name="shiftNote"
                      placeholder="Leave a message"
                      className="shift-drop-note-input"
                      maxLength={300}
                    />
                  </div>
                </div>
                {/* Shift Note Char Count */}

                <div className="d-center-start shift-drop-note-char-count">
                  <div className="icon">&nbsp;</div>
                  <div className="shift-drop-note-visibility-container">
                    <p className="char-count">
                      {values.shiftNote.length} / 300
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </Form>
        );
      }}
    </Formik>
  );
};
