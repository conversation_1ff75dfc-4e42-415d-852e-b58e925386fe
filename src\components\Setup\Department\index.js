'use client';

import React, { useContext, useEffect, useState, useCallback } from 'react';
import { Box, Typography, Tooltip } from '@mui/material';
import AuthContext from '@/helper/authcontext';
import { DataGrid, gridClasses } from '@mui/x-data-grid';
import AddIcon from '@mui/icons-material/Add';
import DialogBox from '@/components/UI/Modalbox';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import EditIcon from '@/components/ActionIcons/EditIcon';
import DeleteIcon from '@/components/ActionIcons/DeleteIcon';
import DeleteModal from '@/components/UI/DeleteModal';
import CustomButton from '@/components/UI/CustomButton';
import AddEditDeaprtment from './AddEditDeaprtment';
import NoDataView from '@/components/UI/NoDataView';
import PreLoader from '@/components/UI/Loader';
import ContentLoader from '@/components/UI/ContentLoader';
import CustomSearch from '@/components/UI/CustomSearch';
import CustomOrgPagination from '@/components/UI/customPagination';
import { branchService } from '@/services/branchService';
import './department.scss';

// Constants
const DEFAULT_PAGE_SIZE = 10;
const INITIAL_PAGE = 1;

// Helper functions
const convertToHtml = (str) => {
  return str
    ?.replace(/(\r\n|\n|\r)/gm, '<br/>')
    ?.replace(/\\n/g, '<br/>')
    ?.replace(/\\r/g, '');
};

const getStatusTypography = (status) => {
  const statusClasses = {
    inactive: 'failed',
    draft: 'draft',
    active: 'success',
  };

  return (
    <Typography className={`p12 ${statusClasses[status] || 'success'} fw600`}>
      {status}
    </Typography>
  );
};

export default function Department() {
  const { authState, setAllListsData, AllListsData } = useContext(AuthContext);
  const [createModal, setCreateModal] = useState(false);
  const [isUpdate, setIsUpdate] = useState(false);
  const [updateItem, setUpdateItem] = useState(null);
  const [departmentList, setDepartmentList] = useState([{ id: '' }]);
  const [loader, setLoader] = useState(true);
  const [actionLoader, setActionLoader] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [page, setPage] = useState(INITIAL_PAGE);
  const [totalCount, setTotalCount] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(DEFAULT_PAGE_SIZE);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteId, setDeleteId] = useState(null);

  const getDepartmentList = useCallback(
    async (search, pageNo, Rpp, isLoader = true) => {
      isLoader && setLoader(true);
      try {
        const { status, data } = await axiosInstance.get(
          URLS?.GET_DEPARTMENT_LIST +
            `?search=${search}&page=${pageNo}&size=${Rpp || rowsPerPage}`
        );

        if (status === 200) {
          const formattedData = data?.data.map((b) => ({
            ...b,
            department_remark: b?.department_remark || '-',
          }));

          setLoader(false);
          setTotalCount(data?.count);
          setDepartmentList(formattedData);
        }
      } catch (error) {
        setLoader(false);
        setTotalCount(0);
        setDepartmentList([]);
        setApiMessage('error', error?.response?.data?.message);
      }
    },
    [rowsPerPage]
  );
  const getDepartmentLists = async () => {
    try {
      const branchAndDeptData = await branchService.getActiveDepartmentList();

      setAllListsData({
        ...AllListsData,
        SelectDepartmentList: branchAndDeptData.selectDepartmentList,
        ActiveDepartmentList: branchAndDeptData.activeDepartmentList,
      });
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const deleteDepartment = async (id) => {
    try {
      setActionLoader(true);
      const { status, data } = await axiosInstance.delete(
        URLS?.DELETE_DEPARTMENT + id
      );

      if (status === 200) {
        if (data.status) {
          setApiMessage('success', data?.message);
          handleCloseDeleteDialog();

          const newPage =
            departmentList?.length === 1 && page !== 1 ? page - 1 : page;
          setPage(newPage);
          getDepartmentList(searchValue, newPage, '', false);
          getDepartmentLists();
        } else {
          setApiMessage('error', data?.message);
        }
      } else {
        setApiMessage('error', data?.message);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setDeleteDialogOpen(false);
    } finally {
      setActionLoader(false);
    }
  };

  const handleEdit = (item) => {
    if (!item?.row) {
      setApiMessage('error', 'Invalid department data');
      return;
    }

    setIsUpdate(true);
    setUpdateItem(item.row);
    setCreateModal(true);
  };

  const handleOpenDeleteDialog = (id) => {
    setDeleteId(id);
    setDeleteDialogOpen(true);
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setDeleteId(null);
  };

  const handleClose = () => {
    setCreateModal(false);
    setTimeout(() => {
      setUpdateItem(null);
      setIsUpdate(false);
    }, 100);
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      setPage(1);
      getDepartmentList(searchValue, 1);
    }
  };

  const onPageChange = (newPage) => {
    setPage(newPage);
    getDepartmentList(searchValue, newPage);
  };

  const OnRowPerPage = (newPage) => {
    setRowsPerPage(newPage);
    setPage(1);
    getDepartmentList(searchValue, 1, newPage);
  };

  const columns = [
    {
      field: 'id',
      headerName: 'ID',
      width: 48,
      minWidth: 48,
      flex: 0,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
    },
    {
      field: 'department_name',
      headerName: 'Department name',
      width: 200,
      minWidth: 200,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
    },
    {
      field: 'department_remark',
      headerName: 'Remark',
      width: 300,
      minWidth: 300,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => (
        <Box className="d-flex align-center justify-start h100">
          {params?.value !== '-' ? (
            <Tooltip
              title={
                <div
                  dangerouslySetInnerHTML={{
                    __html: convertToHtml(params?.value),
                  }}
                />
              }
              arrow
              classes={{ tooltip: 'info-tooltip-container' }}
              placement="bottom-start"
            >
              <Typography className="p14 text-ellipsis">
                <span>{params?.value}</span>
              </Typography>
            </Tooltip>
          ) : (
            '-'
          )}
        </Box>
      ),
    },
    {
      field: 'department_status',
      headerName: 'Status',
      width: 200,
      minWidth: 200,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => (
        <Box className="d-flex align-center justify-center h100 text-capital">
          {getStatusTypography(params?.value)}
        </Box>
      ),
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => (
        <Box className="d-flex justify-center actions">
          <Box className="d-flex actions-branch gap-sm">
            <Tooltip
              title={<Typography>Edit</Typography>}
              arrow
              classes={{ tooltip: 'info-tooltip-container' }}
            >
              <Box>
                <EditIcon onClick={() => handleEdit(params)} />
              </Box>
            </Tooltip>
            {params?.row?.department_status !== 'inactive' && (
              <Tooltip
                title={<Typography>Delete</Typography>}
                arrow
                classes={{ tooltip: 'info-tooltip-container' }}
              >
                <Box>
                  <DeleteIcon
                    onClick={() => handleOpenDeleteDialog(params?.row?.id)}
                  />
                </Box>
              </Tooltip>
            )}
          </Box>
        </Box>
      ),
    },
  ];

  useEffect(() => {
    if (
      authState?.UserPermission?.department === 1 ||
      authState?.UserPermission?.department === 2
    ) {
      getDepartmentList(searchValue, page);
    }
  }, [authState?.UserPermission?.department, getDepartmentList, page]);

  const hasDepartmentPermission = authState?.UserPermission?.department === 2;

  const handleClearSearch = () => {
    setPage(1);
    getDepartmentList('', 1);
  };

  return (
    <>
      {actionLoader && <PreLoader />}
      <Box className="org-department-wrap">
        <Box className="search-section-wrap">
          <Box className="search-section-fields">
            <CustomSearch
              setSearchValue={setSearchValue}
              searchValue={searchValue}
              onKeyPress={handleKeyPress}
              isClearSearch
              handleClearSearch={() => handleClearSearch()}
            />
          </Box>
          <CustomButton
            variant="outlined"
            title="Search"
            onClick={() => {
              setPage(1);
              getDepartmentList(searchValue, 1);
            }}
          />

          {hasDepartmentPermission && (
            <CustomButton
              variant="contained"
              title="Create Department"
              startIcon={<AddIcon />}
              onClick={() => setCreateModal(true)}
            />
          )}
        </Box>
        <Box className="table-container table-layout">
          {loader ? (
            <ContentLoader />
          ) : departmentList?.length === 0 ? (
            <Box className="no-data d-flex align-center justify-center">
              <NoDataView
                title="No Departments Found"
                description="There is no Departments available at the moment."
              />
            </Box>
          ) : (
            <>
              <DataGrid
                rows={departmentList}
                columns={columns}
                pageSize={rowsPerPage}
                checkboxSelection={false}
                disableSelectionOnClick
                columnVisibilityModel={{
                  actions: hasDepartmentPermission,
                }}
                hideMenuIcon
                sx={{
                  transition: 'none',
                  [`& .${gridClasses.cell}`]: {},
                }}
              />
              <CustomOrgPagination
                currentPage={page}
                totalCount={totalCount}
                rowsPerPage={rowsPerPage}
                onPageChange={onPageChange}
                OnRowPerPage={OnRowPerPage}
              />
            </>
          )}
        </Box>
      </Box>
      <DialogBox
        open={createModal}
        handleClose={handleClose}
        title={isUpdate ? 'Update Department' : 'Create Department'}
        className="small-dialog-box-container"
        content={
          <AddEditDeaprtment
            handleClose={handleClose}
            isUpdate={isUpdate}
            updateItem={updateItem}
            getDepartmentList={getDepartmentList}
            searchValue={searchValue}
            page={page}
            setCreateModal={setCreateModal}
            setActionLoader={setActionLoader}
            getDepartmentLists={getDepartmentLists}
          />
        }
      />
      <DialogBox
        open={deleteDialogOpen}
        handleClose={handleCloseDeleteDialog}
        title="Confirmation"
        className="delete-modal"
        dividerClass="delete-modal-divider"
        content={
          <>
            <DeleteModal
              handleCancel={handleCloseDeleteDialog}
              handleConfirm={() => deleteDepartment(deleteId)}
              text="Are you sure you want to delete this department?"
            />
          </>
        }
      />
    </>
  );
}
