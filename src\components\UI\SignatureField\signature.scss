.signature-field {
  margin-bottom: var(--spacing-md);
  .lable-wrap {
    max-width: 190px;
    margin-bottom: 3px;
    .signature-edit-wrap {
      cursor: pointer;
      line-height: 0px;
      .signature-edit {
        font-size: var(--icon-size-xsm);
      }
    }
  }
  .label-color {
    color: var(--text-color-primary);
    font-family: var(--font-family-primary);
    margin-bottom: var(--spacing-xxs);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-bright-blue);
  }

  .required-field {
    text-decoration: none;
    color: var(--text-error);
    margin-left: var(--spacing-xs);
  }

  .signature-section {
    border: var(--normal-border);
    border-radius: var(--border-radius-xs);
    background-color: var(--color-white);
    width: 100%;
    max-width: 190px;
    height: 60px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .field-error {
    font-family: var(--font-family-primary);
    color: var(--text-error);
    margin-top: var(--spacing-xxs);
    font-size: var(--font-size-xs);
    line-height: var(--line-height-xs);
  }

  .link-text {
    color: var(--text-bright-blue);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    display: inline-block;
    padding: var(--spacing-xs) 0;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  .cursor-pointer {
    cursor: pointer;
    padding: var(--spacing-xs);

    &:hover {
      opacity: 0.8;
    }
  }

  .d-flex {
    display: flex;
  }

  .justify-content-between {
    justify-content: space-between;
  }

  .align-items-center {
    align-items: center;
  }
}

.signatureCanvas {
  border: var(--normal-sec-border);
  width: 100% !important;
  height: 100% !important;
  min-height: unset;
  border-radius: 20px;
  display: block;
  box-sizing: border-box;
}
