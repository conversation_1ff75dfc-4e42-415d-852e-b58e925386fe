'use client';
import React, { useState, useMemo } from 'react';
// import Button from 'intergalactic/button';
import { Box } from 'intergalactic/flex-box';
import Select from 'intergalactic/select';
import { FilterTrigger } from 'intergalactic/base-trigger';
// import Ellipsis from 'intergalactic/ellipsis';
// import ReloadIcon from 'intergalactic/icon/Reload/m';
// import ExpandMoreOutlinedIcon from '@mui/icons-material/ExpandMoreOutlined';
// import ClearIcon from '@mui/icons-material/Clear';
import { Tooltip, Typography } from '@mui/material';
import Checkbox from '@mui/material/Checkbox';
import { DropdownCloseIcon, DropdownIcon } from '@/helper/common/images';
import '../Generals/generalpage.scss';
import './categoryfilter.scss';
export default function CategoryFilter({
  selectedMainCategories,
  selectedSubcategories,
  setSelectedMainCategories,
  setSelectedSubcategories,
  dsrCatData,
  placeholder,
}) {
  const [visible, setVisible] = useState(false);
  // const [filter, setFilter] = useState('');
  // const [loading, setLoading] = useState(false);

  // Filter category data based on search
  const categoryData = useMemo(() => {
    return dsrCatData?.map((paymentType) => ({
      label: paymentType?.payment_type_title,
      value: paymentType?.id,
      catList: paymentType?.catList || [],
    }));
  }, [dsrCatData]);

  // Function to handle visibility change of the dropdown.
  const handleChangeVisible = (visible) => {
    setVisible(visible);
    // if (visible) {
    //   setLoading(true);
    //   setTimeout(() => setLoading(false), 1000);
    // }
  };

  // Function to toggle selection of a main category.
  const handleMainCategoryToggle = (mainCategoryId) => {
    setSelectedMainCategories((prevSelectedMainCategories) => {
      if (prevSelectedMainCategories.includes(mainCategoryId)) {
        return prevSelectedMainCategories?.filter(
          (id) => id !== mainCategoryId
        );
      } else {
        return [...prevSelectedMainCategories, mainCategoryId];
      }
    });

    const selectedMainCategoryData = dsrCatData?.find(
      (paymentType) => paymentType?.id === mainCategoryId
    );

    const getCategoryAndSubcategoryIds = (category) => {
      let categoryAndSubcategoryIds = [category?.id];
      if (category?.catList && category?.catList?.length > 0) {
        category?.catList?.forEach((subcat) => {
          categoryAndSubcategoryIds?.push(subcat?.id);
          if (subcat?.catList && subcat?.catList?.length > 0) {
            subcat?.catList?.forEach((nestedSubcat) => {
              categoryAndSubcategoryIds?.push(nestedSubcat?.id);
            });
          }
        });
      }
      return categoryAndSubcategoryIds;
    };

    const allCategoryAndSubcategoryIds =
      selectedMainCategoryData?.catList?.flatMap((category) =>
        getCategoryAndSubcategoryIds(category)
      ) || [];

    setSelectedSubcategories((prevSelectedSubcategories) => {
      if (selectedMainCategories.includes(mainCategoryId)) {
        const updatedSubcategories = prevSelectedSubcategories?.filter((id) => {
          return !allCategoryAndSubcategoryIds.includes(id);
        });
        return updatedSubcategories;
      } else {
        const updatedSubcategories = [
          ...new Set([
            ...prevSelectedSubcategories,
            ...allCategoryAndSubcategoryIds,
          ]),
        ];
        return updatedSubcategories;
      }
    });
  };
  // Function to toggle the selection of a subcategory.
  const handleSubcategoryToggle = (subcategoryId, mainCategoryId) => {
    setSelectedSubcategories((prevSelectedSubcategories) => {
      const updatedSubcategories = [...prevSelectedSubcategories];
      const selectedMainCategoryData = dsrCatData?.find(
        (paymentType) => paymentType?.id === mainCategoryId
      );

      const getSubcategoryIds = (category) => {
        let subcategoryIds = [category?.id];
        if (category?.catList && category?.catList?.length > 0) {
          category.catList?.forEach((subcat) => {
            subcategoryIds?.push(subcat?.id);
            if (subcat?.catList?.length > 0) {
              subcat.catList?.forEach((nestedSubcat) => {
                subcategoryIds?.push(nestedSubcat?.id);
              });
            }
          });
        }
        return subcategoryIds;
      };

      const allSubcategoryIds =
        selectedMainCategoryData?.catList?.flatMap((category) =>
          getSubcategoryIds(category)
        ) || [];

      if (updatedSubcategories.includes(subcategoryId)) {
        const index = updatedSubcategories?.indexOf(subcategoryId);
        if (index > -1) {
          updatedSubcategories?.splice(index, 1);

          selectedMainCategoryData?.catList?.forEach((category) => {
            category?.catList?.forEach((subcat) => {
              if (subcat?.id === subcategoryId && subcat?.catList) {
                subcat?.catList?.forEach((nestedSubcat) => {
                  const nestedIndex = updatedSubcategories?.indexOf(
                    nestedSubcat?.id
                  );
                  if (nestedIndex > -1) {
                    updatedSubcategories?.splice(nestedIndex, 1);
                  }
                });
              }
            });
          });
        }
      } else {
        updatedSubcategories?.push(subcategoryId);

        selectedMainCategoryData?.catList?.forEach((category) => {
          category.catList?.forEach((subcat) => {
            if (subcat.id === subcategoryId && subcat?.catList) {
              subcat?.catList?.forEach((nestedSubcat) => {
                if (!updatedSubcategories.includes(nestedSubcat?.id)) {
                  updatedSubcategories?.push(nestedSubcat?.id);
                }
              });
            }
          });
        });
      }

      if (updatedSubcategories.includes(subcategoryId)) {
        selectedMainCategoryData?.catList.forEach((category) => {
          if (category?.id === subcategoryId) {
            category?.catList?.forEach((subcat) => {
              if (!updatedSubcategories.includes(subcat?.id)) {
                updatedSubcategories?.push(subcat?.id);
              }
            });
          } else if (
            category?.catList?.some((subcat) => subcat?.id === subcategoryId)
          ) {
            if (!updatedSubcategories.includes(category?.id)) {
              updatedSubcategories?.push(category?.id);
            }
          }
        });
      } else {
        selectedMainCategoryData?.catList?.forEach((category) => {
          if (category?.id === subcategoryId) {
            category?.catList?.forEach((subcat) => {
              const subIndex = updatedSubcategories.indexOf(subcat?.id);
              if (subIndex > -1) {
                updatedSubcategories?.splice(subIndex, 1);
              }
            });
          } else if (
            category?.catList?.some((subcat) => subcat?.id === subcategoryId)
          ) {
            const remainingSelectedChildren = category?.catList?.some(
              (subcat) => updatedSubcategories.includes(subcat?.id)
            );
            if (!remainingSelectedChildren) {
              const parentIndex = updatedSubcategories?.indexOf(category?.id);
              if (parentIndex > -1) {
                updatedSubcategories?.splice(parentIndex, 1);
              }
            }
          }
        });
      }

      setSelectedMainCategories((prevSelectedMainCategories) => {
        const isAllUnchecked = allSubcategoryIds?.every(
          (id) => !updatedSubcategories.includes(id)
        );

        const isAnySelected = updatedSubcategories?.some((id) =>
          allSubcategoryIds.includes(id)
        );

        if (isAllUnchecked) {
          return prevSelectedMainCategories?.filter(
            (id) => id !== mainCategoryId
          );
        } else if (
          isAnySelected &&
          !prevSelectedMainCategories.includes(mainCategoryId)
        ) {
          return [...prevSelectedMainCategories, mainCategoryId];
        }
        return prevSelectedMainCategories;
      });

      return updatedSubcategories;
    });
  };

  // Function to clear the selected main categories and subcategories.
  const handleClearSelection = () => {
    setSelectedMainCategories([]);
    setSelectedSubcategories([]);
  };

  // Function to truncate the text if it exceeds a specified length.
  const truncateText = (text, maxLength) =>
    text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;

  return (
    <Select
      placeholder={placeholder ? placeholder : 'Categories'}
      multiselect
      value={selectedMainCategories}
      onVisibleChange={handleChangeVisible}
      visible={visible}
      onChange={selectedMainCategories}
    >
      <Select.Trigger className="intergalactic-multiselect" tag={FilterTrigger}>
        {selectedMainCategories?.length === 1 ? (
          <Box className="d-flex justify-space-between">
            <Box className="d-flex align-center gap-5">
              <span className="branches-wrap">Category :</span>
              <Tooltip
                title={
                  <Typography>
                    {categoryData.find(
                      (category) =>
                        category?.value === selectedMainCategories[0]
                    )?.label || ''}
                  </Typography>
                }
                arrow
                classes={{
                  tooltip: 'info-tooltip-container',
                }}
              >
                <span className="label-wrap">
                  {truncateText(
                    categoryData.find(
                      (category) =>
                        category?.value === selectedMainCategories[0]
                    )?.label || '',
                    15
                  )}
                </span>
              </Tooltip>
            </Box>
            <Box className="d-flex align-center gap-5">
              <Box className="d-flex">
                <DropdownCloseIcon
                  // fontSize="14px"
                  onClick={handleClearSelection}
                  className="clear-icon-wrap"
                />
              </Box>
              <Box className="d-flex">
                <DropdownIcon
                  className="down-arrow-wrap"
                  // fontSize="small"
                />
              </Box>
            </Box>
          </Box>
        ) : selectedMainCategories && selectedMainCategories?.length > 1 ? (
          <Box className="multiple-category-wrap">
            <span className="categories-wrap">Categories</span>
            <span className="selected-count">
              {selectedMainCategories?.length}
            </span>
            <span className="selected-wrap">selected</span>
            <Box className="d-flex">
              <DropdownCloseIcon
                // fontSize="14px"
                onClick={handleClearSelection}
                className="clear-icon-wrap"
              />
            </Box>
            <Box className="d-flex">
              <DropdownIcon
                className="down-arrow-wrap"
                // fontSize="small"
              />
            </Box>
          </Box>
        ) : (
          'Select Categories'
        )}
      </Select.Trigger>

      <Select.Popper
        className="select-input-wrap"
        aria-label="Options with search"
      >
        {/* {loading && (
          <Typography m="10px 8px" variant="body2">
            Loading...
          </Typography>
        )} */}
        {/* {!loading && ( */}
        <>
          <Select.List id="search-list" className="all-category-wrap">
            {dsrCatData?.map((paymentType) => (
              <Box key={paymentType?.id}>
                <Box className="d-flex align-center pb8">
                  {/* Use MUI Checkbox here */}
                  <Checkbox
                    checked={
                      selectedMainCategories &&
                      selectedMainCategories?.length > 0 &&
                      selectedMainCategories?.includes(paymentType?.id)
                    }
                    onChange={() => handleMainCategoryToggle(paymentType?.id)}
                  />
                  <Typography className="main-category-wrap" variant="h6">
                    {/* Truncate the title if it exceeds 15 characters */}
                    {/* <span className="category-name text-ellipsis-line"> */}
                    {paymentType?.payment_type_title?.length > 20
                      ? `${paymentType?.payment_type_title?.slice(0, 15)}...`
                      : paymentType?.payment_type_title}
                    {/* </span>
                      {paymentType?.payment_type_status &&
                      paymentType?.payment_type_status === 'inactive' ? (
                        <span className="failed Inter12 Inactive-axis ml4">
                          In-Active
                        </span>
                      ) : (
                        ''
                      )} */}
                  </Typography>
                </Box>
                {paymentType?.catList?.length > 0 && (
                  <ul>
                    {paymentType?.catList?.map((category) => (
                      <li key={category?.id}>
                        <Box className="d-flex ml16 align-center pb8">
                          <Checkbox
                            checked={
                              selectedSubcategories &&
                              selectedSubcategories?.length > 0 &&
                              selectedSubcategories?.includes(category?.id)
                            }
                            onChange={() =>
                              handleSubcategoryToggle(
                                category?.id,
                                paymentType?.id
                              )
                            }
                          />
                          <Typography
                            className="sub-category-wrap"
                            variant="body2"
                          >
                            {/* Truncate title if it exceeds 15 characters */}
                            {/* <span> */}
                            {category?.payment_type_category_title?.length > 20
                              ? `${category?.payment_type_category_title?.slice(
                                  0,
                                  15
                                )}...`
                              : category?.payment_type_category_title}
                            {/* </span>
                              {category?.payment_type_category_status &&
                              category?.payment_type_category_status ===
                                'inactive' ? (
                                <span className="failed Inter12 Inactive-axis ml4">
                                  In-Active
                                </span>
                              ) : (
                                ''
                              )} */}
                          </Typography>
                        </Box>

                        {category?.catList?.length > 0 && (
                          <ul>
                            {category.catList.map((subCategory) => (
                              <li key={subCategory?.id}>
                                <Box className="d-flex ml30 align-center pb8">
                                  <Checkbox
                                    checked={selectedSubcategories.includes(
                                      subCategory?.id
                                    )}
                                    onChange={() =>
                                      handleSubcategoryToggle(
                                        subCategory?.id,
                                        paymentType?.id
                                      )
                                    }
                                  />
                                  <Typography
                                    className="sub-sub-category-wrap"
                                    variant="body2"
                                  >
                                    {/* Truncate sub-category if it exceeds 15 characters */}
                                    {subCategory?.first_field_value?.length > 20
                                      ? `${subCategory?.first_field_value?.slice(
                                          0,
                                          15
                                        )}...`
                                      : subCategory?.first_field_value}
                                  </Typography>
                                </Box>
                              </li>
                            ))}
                          </ul>
                        )}
                      </li>
                    ))}
                  </ul>
                )}
              </Box>
            ))}
            {!dsrCatData?.length && (
              <Select.OptionHint>No categories found</Select.OptionHint>
            )}
          </Select.List>
          {/* <Box m="8px">
              <Button
                className="apply-btn"
                use="primary"
                w="100%"
                onClick={() => setVisible(false)}>
                Apply
              </Button>
            </Box> */}
        </>
        {/* )} */}
      </Select.Popper>
    </Select>
  );
}
