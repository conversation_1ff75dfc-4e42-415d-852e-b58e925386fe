@import '@/styles/variable.scss';
body {
  .profile-form-container {
    padding: var(--spacing-md) var(--spacing-lg) var(--spacing-none);
    max-height: calc(100vh - 140px - var(--banner-height));
    overflow: auto;
    .profile-card {
      background: $color-White;
      border-radius: 16px;
      box-shadow: none;
      overflow: visible;
      // padding-top: 20px;
      .profile-header {
        display: flex;
        align-items: center;
        gap: 20px;
        padding-bottom: 20px;
        border-bottom: 1px solid #d9dae2;
        .profile-avatar-section {
          height: 80px;
          width: 80px;
          position: relative;
          .edit-avatar-button {
            position: absolute;
            bottom: 0;
            right: 0;
            background: var(--color-white);
            padding: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            color: var(--text-bright-blue);
            svg {
              width: 18px;
              height: 18px;
            }
          }
        }
      }
      .profile-info {
        .profile-name {
          font-family: $PrimaryFont;
          color: $color-primary;
          font-weight: 600;
          margin-bottom: 10px;
        }

        .profile-subtitle {
          font-family: $PrimaryFont;
          color: $color-Dark-40;
        }
      }
      .profile-avatar {
        width: 80px;
        height: 80px;
        border: 3px solid $color-White;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

        &.MuiAvatar-root {
          background-color: $color-secondary;
          color: $color-Dark-30;
        }
      }

      // padding-top: var(--spacing-lg) !important;
      // // box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.05);
      // border-radius: 12px;
      // padding: 0px 18px;
      .org-form-title {
        font-family: $PrimaryFont;
        color: $color-Black;
        font-size: 16px;
        font-weight: 600;
        margin-top: 16px;
      }
      .form-grid {
        margin-top: 10px;
        display: grid;
        // grid-template-columns: 24% 24% 24% 24%;
        grid-template-columns: 23% 23% 23% 23%;
        gap: 18px;
        .checkbox-error-text {
          font-family: $PrimaryFont;
          color: $error;
          margin-top: 3px;
          font-size: 12px;
          line-height: 14px;
        }

        .linked-icon {
          fill: $linkedin-blue !important;
        }
        .facebook-icon {
          fill: $facebook-blue !important;
        }
        .twitte-icon {
          fill: $vivid-sky-blue !important;
        }
        @media (max-width: 1024px) {
          grid-template-columns: calc(50% - 9px) calc(50% - 9px);
        }
        @media (max-width: 575px) {
          grid-template-columns: 100%;
        }
      }

      .form-actions {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 20px;
        margin-top: var(--spacing-lg);
        padding: var(--spacing-lg) var(--spacing-none);
        border-top: 1px solid $color-Dark-10;
      }

      .MuiCardContent-root {
        padding: 0px;
      }
    }

    @media (max-width: 575px) {
      margin: var(--spacing-lg) var(--spacing-none);
      padding: var(--spacing-none);
    }
  }

  // Responsive styles

  @media (max-width: 768px) {
  }
}
