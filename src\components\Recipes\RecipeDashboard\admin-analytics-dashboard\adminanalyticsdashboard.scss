.admin-analytics-dashboard {
  font-family: var(--font-family-primary);
  position: relative;
  width: 100%;

  // Main Content Area
  &__main {
    width: 100%;
    padding-bottom: 0; // Remove any extra bottom padding
    margin-bottom: 0; // Ensure no bottom margin
  }

  // Container
  &__container {
    width: 100%;
    padding: 0;
    margin-bottom: 0; // Remove any extra bottom margin
  }

  // Header Section
  &__header {
    margin-bottom: var(--spacing-xl);
    position: relative;
  }

  &__header-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);

    @media (min-width: 640px) {
      gap: var(--spacing-lg);
      margin-bottom: var(--spacing-lg);
    }

    @media (min-width: 1024px) {
      flex-direction: row;
      align-items: flex-start;
      justify-content: space-between;
    }
  }

  &__header-info {
    flex: 1;
  }
  &__main-title {
    font-size: var(--font-size-xl);
  }
  &__title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-2xl);
    background: linear-gradient(
      135deg,
      var(--color-primary) 0%,
      var(--color-dark-blue) 100%
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-sm);
    line-height: 1.2;

    @media (min-width: 640px) {
      font-size: var(--font-size-3xl);
    }

    @media (min-width: 1024px) {
      font-size: var(--font-size-4xl);
    }
  }

  &__subtitle {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-regular);
    margin-bottom: var(--spacing-sm);
    color: var(--text-color-slate-gray);
    opacity: 0.8;
  }

  // Live Indicator
  &__live-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-sm);
  }

  &__live-dot {
    width: var(--spacing-sm);
    height: var(--spacing-sm);
    background-color: var(--color-success);
    border-radius: var(--border-radius-full);
    animation: pulse 2s infinite;
  }

  &__live-text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--color-success);
    font-weight: var(--font-weight-medium);
  }

  // Header Actions
  &__header-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: stretch;

    @media (min-width: 640px) {
      flex-direction: row;
      align-items: center;
      gap: var(--spacing-md);
    }

    @media (min-width: 1024px) {
      align-items: flex-end;
      justify-content: flex-end;
    }
  }

  &__export-buttons {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--spacing-sm);
    width: auto;

    @media (min-width: 1024px) {
      gap: var(--spacing-md);
    }
  }

  &__export-btn {
    .custom-btn {
      min-width: 50px;
      width: 50px;
      height: 50px;
      padding: var(--btn-icon-padding-sm);
      border-radius: var(--border-radius-md);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
      background: var(--color-white);
      color: var(--color-primary);
      border-color: var(--color-primary);

      .MuiButton-startIcon {
        margin: 0;

        svg {
          width: 18px;
          height: 18px;
        }
      }

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.2),
          transparent
        );
        transition: left 0.5s;
        z-index: 0;
      }

      &:hover:not(.Mui-disabled) {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        background: var(--color-primary-opacity);
        border-color: var(--color-dark-blue);

        &::before {
          left: 100%;
        }
      }

      &:active {
        transform: translateY(0);
      }

      // Ensure content is above the shine effect
      .MuiButton-startIcon {
        position: relative;
        z-index: 1;
      }
    }
  }

  // Statistics Grid
  &__stats-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);

    @media (min-width: 480px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (min-width: 768px) {
      grid-template-columns: repeat(3, 1fr);
      gap: var(--spacing-lg);
    }

    @media (min-width: 1024px) {
      grid-template-columns: repeat(4, 1fr);
    }

    @media (min-width: 1280px) {
      grid-template-columns: repeat(5, 1fr);
    }
    @media (min-width: 1550px) {
      grid-template-columns: repeat(6, 1fr);
    }
  }

  // Charts Grid
  &__charts-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
    margin-bottom: var(
      --spacing-lg
    ); // Add small spacing between charts and bottom section

    @media (min-width: 768px) {
      gap: var(--spacing-lg);
      margin-bottom: var(--spacing-xl);
    }

    @media (max-width: 1024px) {
      grid-template-columns: repeat(1, 1fr);
    }

    // @media (min-width: 1950px) {
    //   grid-template-columns: repeat(2, 1fr);
    // }
  }

  // Bottom Section - Quick Actions with same width as individual chart widgets
  &__bottom-section {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
    margin-bottom: 0; // Remove extra bottom space

    @media (min-width: 768px) {
      gap: var(--spacing-lg);
    }

    @media (min-width: 1366px) {
      grid-template-columns: 1fr 1fr; // Same as chart grid - Quick Actions takes first column, placeholder takes second
    }
  }

  // Grid Placeholder for layout
  &__placeholder {
    // Invisible placeholder to maintain grid layout
    visibility: hidden;

    @media (max-width: 1365px) {
      display: none; // Hide on smaller screens where grid is single column
    }
  }

  // Filter Placeholder
  &__filter-placeholder {
    background-color: var(--color-light-blue);
    border: var(--border-width-xs) dashed var(--color-primary);
    border-radius: var(--border-radius-md);
    text-align: center;
    padding: var(--spacing-lg);
    margin-top: var(--spacing-lg);

    p {
      margin: 0;
      font-family: var(--font-family-primary);
      font-size: var(--font-size-sm);
      color: var(--text-color-slate-gray);
    }
  }

  .divider-line {
    margin: var(--spacing-lg) 0;
  }
}

// Animations
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Staff Tracking Section
.admin-analytics-dashboard {
  &__staff-tracking {
    margin-top: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: var(--background-color-white);
    border-radius: var(--border-radius-lg);
    border: var(--normal-sec-border);

    .badge {
      display: inline-block;
      padding: 2px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
      text-transform: uppercase;

      &.badge-primary {
        background-color: var(--color-primary);
        color: white;
      }

      &.badge-secondary {
        background-color: var(--color-secondary);
        color: var(--text-color-primary);
      }
    }
  }
}
