import React from 'react';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DesktopDatePicker } from '@mui/x-date-pickers/DesktopDatePicker';
import { Box, InputLabel } from '@mui/material';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import dayjs from 'dayjs';
import './datepicker.scss';

const CustomDatePicker = ({
  onChange,
  label,
  value,
  error,
  helperText,
  format,
  years,
  months,
  days,
  className,
  yearMonth,
  disablePast,
  disabled,
  OpenDialog,
  CloseDialog,
  disableFuture,
  minDate,
  maxDate,
}) => {
  let dateValue = null;

  if (value) {
    if (typeof value === 'string') {
      // Try to parse ISO date strings or other formats
      dateValue = dayjs(value, ['YYYY-MM-DDTHH:mm:ss.SSSZ', 'DD/MM/YYYY']);
    } else {
      // Assume it's a dayjs object or a Date
      dateValue = dayjs(value);
    }
  }

  // Ensure the parsed date is valid
  dateValue = dateValue?.isValid() ? dateValue : null;
  return (
    <>
      <Box className={className ? `${className} date-wrap` : 'date-wrap'}>
        <LocalizationProvider dateAdapter={AdapterDayjs}>
          <InputLabel className="input-label">{label}</InputLabel>
          <DesktopDatePicker
            InputLabelProps={{
              shrink: true,
            }}
            disablePast={disablePast}
            disableFuture={disableFuture}
            format={
              format
                ? format
                : !years && !months && !days
                  ? 'DD/M/YYYY'
                  : undefined
            }
            // label={label}
            onOpen={OpenDialog}
            onClose={CloseDialog}
            value={dateValue || null}
            onChange={onChange}
            views={
              (yearMonth && ['month', 'year']) ||
              (years && ['year']) ||
              (months && ['month', 'year']) ||
              (days && ['day'])
            }
            minDate={minDate}
            maxDate={maxDate}
            slots={{ openPickerIcon: CalendarMonthIcon }}
            slotProps={{
              textField: {
                placeholder: 'Select date',
              },
            }}
            disabled={disabled}
          />
        </LocalizationProvider>
      </Box>
    </>
  );
};

export default CustomDatePicker;
