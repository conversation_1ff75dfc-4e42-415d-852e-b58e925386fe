body {
  .sync-carousel {
    display: flex;
    flex-direction: column;
    gap: 10px;

    &.left,
    &.right {
      flex-direction: row;
    }

    .main-carousel {
      width: 100%;
      .splide__slide {
        border-radius: var(--border-radius-xs);
      }
      img,
      video,
      iframe {
        display: block !important;
        width: 100%;
        height: 100%;
        object-fit: scale-down;
        border-radius: var(--border-radius-md);
        background-color: var(--color-black);
      }
    }

    .thumbnail-carousel {
      display: flex;
      // gap: 10px;

      .splide__slide {
        cursor: pointer;

        img,
        video,
        iframe {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border: 1px solid transparent;
          border-radius: var(--border-radius-xs);
          transition: border 0.3s;
          background-color: var(--color-black);
        }

        &:hover img,
        &:hover video {
          border-color: var(--border-color-primary);
        }
      }

      &.thumbnail-top,
      &.thumbnail-bottom {
        .splide__slide {
          width: 100px;
          &.is-active {
            border-width: var(--border-width-sm);
            border-radius: var(--spacing-xs);
          }
          @media (max-width: 768px) {
            width: 70px;
          }
        }
      }

      &.thumbnail-left,
      &.thumbnail-right {
        // flex-direction: column;

        .splide__slide {
          height: 64px;
          &.is-active {
            border-width: var(--border-width-sm);
            border-radius: var(--spacing-xs);
          }
          @media (max-width: 768px) {
            height: 48px;
          }
        }
      }
    }
  }
  .fullscreen-dialog-box-container {
    .MuiDialog-paperScrollPaper {
      .dialog-content {
        height: 100vh;
        .sync-carousel {
          height: 100%;
          .main-carousel {
            height: 100%;
            .splide__track {
              height: 100%;
              .splide__slide {
                height: 100% !important;
                border-radius: var(--border-radius-xs);
                img,
                video {
                  display: block !important;
                  object-fit: scale-down;
                }
              }
            }
          }
        }
      }
    }
  }
}
