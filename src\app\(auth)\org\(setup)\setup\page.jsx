import React from 'react';
import { Box } from '@mui/material';
import { generateMetadata as generateBaseMetadata } from '@/helper/common/commonFunctions';
import SetUp from '@/components/Setup';
import { setupMenuList } from '@/helper/common/commonMenus';

export function generateMetadata({ searchParams }) {
  const is_setup = Number(searchParams?.is_setup);
  const activeItem = setupMenuList?.find((item) => item?.id === is_setup);
  const seoTitle = activeItem?.name || 'Setup';

  return generateBaseMetadata({ pageTitle: seoTitle });
}

const SetupPage = () => {
  return (
    <Box className="main-page-container">
      <SetUp />
    </Box>
  );
};

export default SetupPage;
