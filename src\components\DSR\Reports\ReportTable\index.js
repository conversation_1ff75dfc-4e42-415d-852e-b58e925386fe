'use client';

import React, { useState } from 'react';
import {
  Box,
  TableHead,
  TableCell,
  TableRow,
  TableBody,
  Table,
} from '@mui/material';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';

export default function DSRTable() {
  const [dsrData] = useState({
    columns: [
      {
        groupId: '123',
        groupLabel: '',
        items: [{ key: 'col123', label: 'id' }],
      },

      {
        groupId: '1223',
        groupLabel: '',
        items: [{ key: 'col1233', label: 'name' }],
      },
      {
        groupId: 'general',
        groupLabel: 'General',
        items: [{ key: 'col1', label: 'Date / Branch' }],
      },
      {
        groupId: 'financial',
        groupLabel: 'Financial',
        items: [
          { key: 'col2', label: 'Cash' },
          { key: 'col3', label: 'Wages' },
          { key: 'col6', label: 'Payroll' },
          { key: 'col10', label: 'Total' },
        ],
      },
      {
        groupId: '12323',
        groupLabel: '',
        items: [{ key: 'col1233', label: 'fomssx' }],
      },
      {
        groupId: 'partners',
        groupLabel: 'Partners',
        items: [
          { key: 'col4', label: 'Bank Details' },
          { key: 'col5', label: 'Booking Partners' },
          { key: 'col7', label: 'Delivery Partner' },
        ],
      },
      // {
      //   groupId: 'vat',
      //   groupLabel: 'VAT',
      //   items: [
      //     { key: 'col11', label: '0% VAT' },
      //     { key: 'col12', label: '20% VAT' },
      //     { key: 'col13', label: 'Total' }
      //   ]
      // }
    ],
    data: [
      {
        col1: '20-11-2012',
        col2: '23211',
        col3: '22000',
        col4: '1323',
        col5: '323232',
        col6: '**************',
        col7: '33333',
        col8: '23211',
        col9: '23211',
        col10: '10000',
        col11: '10000',
        col12: '10000',
        col13: '10000',
      },
      {
        col1: '20-11-2012',
        col2: '23211',
        col3: '22000',
        col4: '1323',
        col5: '323232',
        col6: '**************',
        col7: '33333',
        col8: '23211',
        col9: '23211',
        col10: '10000',
        col11: '10000',
        col12: '10000',
        col13: '10000',
      },
      {
        col1: '20-11-2012',
        col2: '23211',
        col3: '22000',
        col4: '1323',
        col5: '323232',
        col6: '**************',
        col7: '33333',
        col8: '23211',
        col9: '23211',
        col10: '10000',
        col11: '10000',
        col12: '10000',
        col13: '10000',
      },
      {
        col1: '20-11-2012',
        col2: '23211',
        col3: '22000',
        col4: '1323',
        col5: '323232',
        col6: '**************',
        col7: '33333',
        col8: '23211',
        col9: '23211',
        col10: '10000',
        col11: '10000',
        col12: '10000',
        col13: '10000',
      },
      {
        col1: '20-11-2012',
        col2: '23211',
        col3: '22000',
        col4: '1323',
        col5: '323232',
        col6: '**************',
        col7: '33333',
        col8: '23211',
        col9: '23211',
        col10: '10000',
        col11: '10000',
        col12: '10000',
        col13: '10000',
      },
    ],
  });

  const cellWidth = 150;

  const onDragEnd = (result) => {
    const { source, destination, type } = result;
    if (!destination) return;

    const updatedColumns = Array.from(dsrData.columns);
    if (type === 'group') {
      // Check if the destination group has an empty `groupLabel`
      if (updatedColumns[destination.index].groupLabel !== '') {
        // const [movedGroup] = updatedColumns.splice(source.index, 1);
        // Add movedGroup as a separate entry at the destination index
        // const conItems = _.concat(updatedColumns[destination.index].items, [
        //   updatedColumns.splice(source.index, 1)
        // ]);
        // updatedColumns[destination.index].items = conItems;
        updatedColumns.splice(destination.index, 0, movedGroup);
      } else {
        const [movedGroup] = updatedColumns.splice(source.index, 1);
        // Regular behavior: move group to new position in array
        updatedColumns.splice(destination.index, 0, movedGroup);
      }
    } else if (type === 'subItem') {
      const sourceGroupIndex = updatedColumns.findIndex(
        (group) => group.groupId === source.droppableId
      );
      const destinationGroupIndex = updatedColumns.findIndex(
        (group) => group.groupId === destination.droppableId
      );

      const updatColumnsWithout = updatedColumns;
      let sourceItems = Array.from(updatColumnsWithout[sourceGroupIndex].items);
      let [movedItem] = sourceItems.splice(source.index, 1);
      if (sourceGroupIndex === destinationGroupIndex) {
        // Moving within the same group
        sourceItems.splice(destination.index, 0, movedItem);
        updatedColumns[sourceGroupIndex].items = sourceItems;
      } else {
        // Moving to a different group
        const destinationItems = Array.from(
          updatedColumns[destinationGroupIndex].items
        );

        // Check if the destination group has an empty `groupLabel`
        if (updatedColumns[destinationGroupIndex].groupLabel === '') {
          // If `groupLabel` is empty, treat `movedItem` as a new group
          updatedColumns.splice(destinationGroupIndex, 0, {
            groupId: movedItem.key,
            groupLabel: '',
            items: [movedItem],
          });
          // let sourceItemss = Array.from(updatedColumns[sourceGroupIndex].items);
          // let [movedItems] = sourceItemss.splice(source.index, 1);
          // updatedColumns[sourceGroupIndex + 1].items = [movedItems];
        } else {
          // Regular behavior: move item to destination group
          destinationItems.splice(destination.index, 0, movedItem);
          updatedColumns[destinationGroupIndex].items = destinationItems;
        }
        // After moving, update the source group
        if (sourceItems.length === 0) {
          if (
            updatedColumns[sourceGroupIndex + 1].groupLabel !== '' &&
            updatedColumns[destinationGroupIndex].groupLabel === '' &&
            sourceGroupIndex > destinationGroupIndex
          ) {
            updatedColumns.splice(sourceGroupIndex + 1, 1);
          } else {
            updatedColumns.splice(sourceGroupIndex, 1);
          }
        } else {
          if (
            sourceItems.length > 0 &&
            updatedColumns[destinationGroupIndex].groupLabel === ''
          ) {
            // Otherwise, update the source group items
            // updatedColumns[sourceGroupIndex + 1].items = sourceItems;
          } else {
            // Otherwise, update the source group items
            updatedColumns[sourceGroupIndex].items = sourceItems;
          }
        }
      }
    }

    setDsrData((prevData) => ({
      ...prevData,
      columns: updatedColumns,
    }));
  };
  return (
    <Box className="DSR-table">
      <DragDropContext onDragEnd={onDragEnd}>
        <Table className="table-dsr-container">
          <TableHead>
            <Droppable droppableId="groups" type="group" direction="horizontal">
              {(provided) => (
                <TableRow ref={provided.innerRef} {...provided.droppableProps}>
                  {dsrData.columns.map((group, groupIndex) => (
                    <Draggable
                      key={group.groupId}
                      draggableId={group.groupId}
                      index={groupIndex}
                    >
                      {(provided) => (
                        <TableCell
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          {...provided.dragHandleProps}
                          colSpan={group.items.length}
                          align="center"
                          className="table-head"
                          style={{
                            fontWeight: 'bold',
                            border: '1px solid gray',
                            backgroundColor: '#f1f1f1',
                            // width: '100%',
                            // width: `${group?.items?.length * cellWidth}px`,
                            minWidth: `${group?.items?.length * cellWidth}px`,
                            // maxWidth: `${group?.items?.length * cellWidth}px`
                          }}
                        >
                          <Box>{group.groupLabel}</Box>

                          <Droppable
                            droppableId={group.groupId}
                            type="subItem"
                            direction="horizontal"
                          >
                            {(provided) => (
                              <TableRow
                                ref={provided.innerRef}
                                {...provided.droppableProps}
                                style={{
                                  display: 'flex',
                                  // gap: '8px',
                                  marginTop: '8px',
                                  // width: '100%',
                                  // width: `${
                                  //   group?.items?.length * cellWidth
                                  // }px`,
                                  minWidth: `${
                                    group?.items?.length * cellWidth
                                  }px`,
                                  // maxWidth: `${
                                  //   group?.items?.length * cellWidth
                                  // }px`
                                }}
                              >
                                {group.items.map((item, itemIndex) => (
                                  <Draggable
                                    key={item.key}
                                    draggableId={item.key}
                                    index={itemIndex}
                                  >
                                    {(provided) => (
                                      <TableCell
                                        ref={provided.innerRef}
                                        {...provided.draggableProps}
                                        {...provided.dragHandleProps}
                                        className="table-cell"
                                        style={{
                                          border: '1px solid gray',
                                          backgroundColor: '#fff',
                                          padding: '6px',
                                          cursor: 'grab',
                                          flex: 1,
                                          // width: '100%',
                                          // width: `${cellWidth}px`,
                                          minWidth: `${cellWidth}px`,
                                          // maxWidth: `${cellWidth}px`,
                                          overflow: 'hidden',
                                        }}
                                      >
                                        {item.label}
                                      </TableCell>
                                    )}
                                  </Draggable>
                                ))}
                                {provided.placeholder}
                              </TableRow>
                            )}
                          </Droppable>
                        </TableCell>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </TableRow>
              )}
            </Droppable>
          </TableHead>

          <TableBody>
            {dsrData.data.map((dataRow, rowIndex) => (
              <TableRow key={rowIndex}>
                {dsrData.columns.flatMap((group) =>
                  group.items.map((item) => (
                    <TableCell
                      key={item.key}
                      style={{
                        border: '1px solid gray',
                        // width: '100%',
                        // width: `${cellWidth}px`,
                        minWidth: `${cellWidth}px`,
                        // maxWidth: `${cellWidth}px`,
                        overflow: 'hidden',
                      }}
                      className="table-cell table-value"
                    >
                      {dataRow[item?.key] ? dataRow[item?.key] : 'ddddddd'}
                    </TableCell>
                  ))
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </DragDropContext>
    </Box>
  );
}
