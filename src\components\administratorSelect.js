import React, { useState, useContext } from 'react';
import { Box, Menu, Typography } from '@mui/material';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import { useRouter, useSearchParams } from 'next/navigation';
import { fetchFromStorage, removeFromStorage } from '@/helper/context';
import { identifiers } from '@/helper/constants/identifier';
import {
  checkOrganizationRole,
  setApiMessage,
} from '@/helper/common/commonFunctions';
import SyncIcon from '@mui/icons-material/Sync';
import PortraitIcon from '@mui/icons-material/Portrait';
import ExitToAppIcon from '@mui/icons-material/ExitToApp';
import { ResignationIcon, SettingIcon } from '@/helper/common/images';
import axiosInstance from '@/helper/axios/axiosInstance';
import AuthContext from '@/helper/authcontext';
import { URLS } from '@/helper/constants/urls';
import HeaderImage from '@/components/UI/ImageSecurity';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import { handleLogout } from '@/services/authService';

const AdministratorSelect = ({ UserDetails }) => {
  const { setUserdata, setIsDrawer } = useContext(AuthContext);
  const router = useRouter();
  const [menuOpen, setMenuOpen] = useState(false);
  const isDeviceId = fetchFromStorage(identifiers?.DEVICEID);
  const UserDetailsStored = fetchFromStorage(identifiers?.USER_DATA);
  // const authData = fetchFromStorage(identifiers?.AUTH_DATA);
  const searchParams = useSearchParams();
  const IsEdit = searchParams.get('isEdit');
  const handleMenuClick = (event) => {
    setMenuOpen(event?.currentTarget);
  };
  const handleClose = () => {
    setMenuOpen(null);
  };

  const handleLogoutClick = () => {
    handleLogout(router, isDeviceId);
  };

  const switchRole = async (role) => {
    try {
      const { status, data } = await axiosInstance.post(URLS?.SWITH_ROLE, {
        role_id: role?.value,
      });
      if (status === 200) {
        if (data?.status) {
          setApiMessage('success', data?.message);
          removeFromStorage(identifiers?.RedirectData);
          if (
            (window.location.pathname.includes('/change-req') && IsEdit) ||
            window.location.pathname.includes('/change-request/')
          ) {
            router?.push(`/change-request`);
            setTimeout(() => {
              window.location.reload();
            }, 500);
          } else {
            window.location.reload();
          }

          // setUserdata();
          // handleClose();
        } else {
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const getUserName = (user, fname, lname) => {
    return user && fname && lname ? fname + ' ' + lname : fname ? fname : '';
  };
  // console.log(UserDetails, 'UserDetails');
  return (
    <>
      <Box className="select-form">
        <Box
          className="d-flex cursor-pointer align-center"
          onClick={(event) => handleMenuClick(event)}
        >
          <Box
            className={
              UserDetails &&
              UserDetails?.branch &&
              UserDetails?.branch?.branch_name
                ? 'profile-img mt3'
                : 'profile-img'
            }
          >
            {/* <Profile className='w100 h100' /> */}
            {UserDetails?.user_avatar ||
            (UserDetails && UserDetails?.attributes?.userSignature) ? (
              <>
                {/* <Avatar
                src={UserDetails && UserDetails?.user_avatar}
                alt={UserDetails && UserDetails?.user_full_name}
              /> */}
                <HeaderImage
                  imageUrl={
                    UserDetails && UserDetails?.attributes?.userSignature
                      ? UserDetails && UserDetails?.attributes?.userSignature
                      : UserDetails && UserDetails?.user_avatar
                        ? UserDetails?.user_avatar
                        : ''
                  }
                  alt={UserDetails && UserDetails?.user_full_name}
                  type="avtar"
                />
              </>
            ) : (
              <Box>
                <AccountCircleIcon />{' '}
                {/* <Image src={UserCircle} alt="img" /> */}
              </Box>
            )}
          </Box>
          <Box className="administrator-text d-flex">
            <Typography
              variant="p"
              className="title-text fw600 text-ellipsis-line"
            >
              {getUserName(
                UserDetails,
                UserDetails?.user_first_name,
                UserDetails?.user_last_name
              )
                ? getUserName(
                    UserDetails,
                    UserDetails?.user_first_name,
                    UserDetails?.user_last_name
                  )
                : getUserName(
                      UserDetailsStored,
                      UserDetailsStored?.user_first_name,
                      UserDetailsStored?.user_last_name
                    )
                  ? getUserName(
                      UserDetailsStored,
                      UserDetailsStored?.user_first_name,
                      UserDetailsStored?.user_last_name
                    )
                  : getUserName(
                        UserDetails,
                        UserDetails?.firstName,
                        UserDetails?.lastName
                      )
                    ? getUserName(
                        UserDetails,
                        UserDetails?.firstName,
                        UserDetails?.lastName
                      )
                    : 'Login user'}
            </Typography>
            {UserDetails &&
            UserDetails?.web_user_active_role_id &&
            UserDetails?.user_roles &&
            UserDetails?.user_roles?.length > 0 ? (
              <Typography
                variant="p"
                className="sub-title-text fw400 person-position text-ellipsis-line"
              >
                {
                  UserDetails?.user_roles?.find(
                    (r) => r?.value === UserDetails?.web_user_active_role_id
                  )?.label
                }
              </Typography>
            ) : UserDetailsStored &&
              UserDetailsStored?.web_user_active_role_id &&
              UserDetailsStored?.user_roles &&
              UserDetailsStored?.user_roles?.length > 0 ? (
              <Typography
                variant="p"
                className="sub-title-text fw400 person-position text-ellipsis-line"
              >
                {
                  UserDetailsStored?.user_roles?.find(
                    (r) =>
                      r?.value === UserDetailsStored?.web_user_active_role_id
                  )?.label
                }
              </Typography>
            ) : (
              <></>
            )}
            {((UserDetails && UserDetails?.branch?.branch_name) ||
              (UserDetailsStored &&
                UserDetailsStored?.branch?.branch_name)) && (
              <Typography className="sub-title-text fw400 person-position text-ellipsis-line">
                {UserDetails?.branch?.branch_name
                  ? UserDetails?.branch?.branch_name
                  : UserDetailsStored?.branch?.branch_name}
              </Typography>
            )}
          </Box>
          {/* <IconButton onClick={() => handleClose()} className="dropdownIcon"> */}
          {/* {isDropDownOpen === true ? <ChevronUp /> : <ChevronDown />} */}
          {/* </IconButton> */}
          {(checkOrganizationRole('org_master') ||
            checkOrganizationRole('super_admin') ||
            checkOrganizationRole('staff')) && (
            <Box className="notification-icon-wrap">
              <KeyboardArrowDownIcon className="notification-icon cursor-pointer" />
            </Box>
          )}
        </Box>

        <Menu
          id="basic-menu"
          anchorEl={menuOpen}
          open={menuOpen}
          onClose={handleClose}
          // MenuListProps={{
          //   'aria-labelledby': 'basic-button'
          // }}
          classes={{ paper: 'custom-menu-list' }}
        >
          <Box className="profile-roles-section">
            {UserDetails &&
              UserDetails?.user_roles &&
              UserDetails?.user_roles?.length > 1 && (
                <>
                  <Box className="switch-roles">
                    <Box className="switching cursor-pointer">
                      <SyncIcon />
                      <Typography className="title-text fw600 switch-text">
                        Designation Switch
                      </Typography>
                    </Box>

                    <Box className="role-list">
                      {UserDetails?.user_roles.map((role) => {
                        return (
                          <Typography
                            onClick={() => {
                              switchRole(role);
                            }}
                            className={`title-text fw400 mt4 cursor-pointer role ${
                              role?.value ===
                              UserDetails?.web_user_active_role_id
                                ? 'active-role'
                                : ''
                            }`}
                          >
                            {role?.label}
                          </Typography>
                        );
                      })}
                    </Box>
                  </Box>
                  <Box className="folder-desc-divider" />
                </>
              )}

            {checkOrganizationRole('super_admin') ||
            checkOrganizationRole('org_master') ? (
              <Box
                className="d-flex align-center pt8 profile-logout cursor-pointer"
                onClick={() => {
                  if (checkOrganizationRole('super_admin')) {
                    router.push('/sorg/organization');
                  } else if (checkOrganizationRole('org_master')) {
                    router.push('/org/organization');
                  }
                  handleClose();
                  setIsDrawer(true);
                }}
              >
                <SettingIcon />
                <span className="title-text fw600">Settings</span>
              </Box>
            ) : (
              <Box
                onClick={() => {
                  setUserdata();
                  removeFromStorage(identifiers?.RedirectData);
                  router.push('/myprofile');
                  handleClose();
                }}
                className={
                  UserDetails &&
                  UserDetails?.user_roles &&
                  UserDetails?.user_roles?.length > 1
                    ? 'd-flex align-center  profile-logout cursor-pointer'
                    : 'd-flex align-center pt8 profile-logout cursor-pointer'
                }
              >
                <PortraitIcon />
                <span className="title-text fw600 ">View profile</span>
              </Box>
            )}

            {UserDetails?.web_user_active_role_id !== 1 && (
              <Box
                onClick={() => {
                  setUserdata();
                  removeFromStorage(identifiers?.RedirectData);
                  router.push('/resignation');
                  handleClose();
                }}
                className="d-flex align-center pt8 profile-logout cursor-pointer"
              >
                {ResignationIcon()}
                <span className="title-text fw600 ">Resignation</span>
              </Box>
            )}

            <Box
              onClick={() => handleLogoutClick()}
              className="d-flex align-center pt8 profile-logout logout-text cursor-pointer"
            >
              <ExitToAppIcon />
              <span className="title-text fw600 ">Logout</span>
            </Box>
          </Box>
        </Menu>
      </Box>
      {/* VIEW PROFILE */}
      {/* <DialogBox
        open={toggleProfile}
        handleClose={() => setToggleProfile(false)}
        title="View profile"
        content={
          <ProfileView
            handleTogglePassword={handleTogglePassword}
            setToggleProfile={setToggleProfile}
          />
        }
        className="view-profile"
      /> */}
      {/* CHANGE PASSWORD */}
      {/* <DialogBox
        open={togglePassword}
        handleClose={() => setTogglePassword(false)}
        title="Change password"
        content={
          <ChangePassword
            handleTogglePassword={handleTogglePassword}
            closeChangePass={() => setTogglePassword(false)}
          />
        }
        className="view-profile"
      /> */}
    </>
  );
};

export default AdministratorSelect;
