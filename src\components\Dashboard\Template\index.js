'use client';

import React, { useContext, useEffect, useState } from 'react';
import { Box, Typography, CircularProgress, Tooltip } from '@mui/material';
import AuthContext from '@/helper/authcontext';
import { useRouter } from 'next/navigation';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import { setApiMessage, DateFormat } from '@/helper/common/commonFunctions';
import AddModels from '@/components/Dashboard/AddModels/index';
import RightDrawer from '@/components/UI/RightDrawer';
import CustomTextField from '@/components/UI/CustomTextField';
import AddchartIcon from '@mui/icons-material/Addchart';
import CustomButton from '@/components/UI/CustomButton';
import LineCharts from '@/components/UI/LineChart';
import BarChart from '@/components/UI/BarChart';
import PieCharts from '@/components/UI/PieChart';
// import AutoGraphIcon from '@mui/icons-material/AutoGraph';
// import DonutSmallIcon from '@mui/icons-material/DonutSmall';
// import EqualizerIcon from '@mui/icons-material/Equalizer';
// import InboxIcon from '@mui/icons-material/Inbox';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import BookmarkBorderIcon from '@mui/icons-material/BookmarkBorder';
import EditIcon from '@/components/ActionIcons/EditIcon';
import DragIcon from '@/components/ActionIcons/DragIcon';
import DeleteIcon from '@/components/ActionIcons/DeleteIcon';
import DashboardDataView from '@/components/Dashboard/DashDataView';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import DateFilter from '@/components/DSR/Reports/DateFilter';
import MultipleFilter from '@/components/UI/MultipleFilter';
import NoDataView from '@/components/UI/NoDataView';
import InfoIcon from '@mui/icons-material/Info';
import moment from 'moment';
import './template.scss';
import '../DashboardMain/dashboard.scss';

export default function ChartTemplate({ branchList, DashbaordId }) {
  const { setIsDrawer } = useContext(AuthContext);
  const router = useRouter();
  const [openModel, setOpenModel] = useState(false);
  const [IsEditData, setIsEditData] = useState();
  const [loader, setLoader] = useState(false);
  const [axisloader, setaxisLoader] = useState(false);
  const [dashboardName, setDashboardName] = useState('');
  const [modelList, setModelList] = useState();
  const [TimePeriod, setTimePeriod] = useState('monthly');
  const [dateSelectedOption, setDateSelectedOption] = useState('today');
  const [dateFilterList, setDateFilterList] = useState([]);
  const [customStartDate, setCustomStartDate] = useState(null);
  const [customEndDate, setCustomEndDate] = useState(null);
  const [selectedBranches, setSelectedBranches] = useState([]);
  const [AxisList, setAxisList] = useState();
  const [screenHeight, setScreenHeight] = useState(null);
  const [screenWidth, setScreenWidth] = useState(null);
  // const [DashboardDataBYID, setDashboardDataBYID] = useState([]);
  const [DashboardData, setDashboardData] = useState();
  const [DashboardAPIData, setDashboardAPIData] = useState([]);

  const generateRandomValue = (index) =>
    Math.floor(100 + Math.random() * 900) + index;
  const convertToKeyValueArray = (obj) =>
    Object.entries(obj).map(([key, value]) => ({ key, value }));

  const handleOpenModel = () => {
    setOpenModel(true);
    setIsDrawer(true);
  };
  const handleCloseModel = () => {
    setOpenModel(false);
    setIsDrawer(false);
    setIsEditData();
  };

  const reorder = (list, startIndex, endIndex) => {
    const result = Array.from(list);
    const [removed] = result.splice(startIndex, 1);
    result.splice(endIndex, 0, removed);

    return result;
  };
  const onDragEnd = (result) => {
    if (!result.destination) {
      return;
    }
    const items = reorder(
      DashboardData,
      result?.source?.index,
      result?.destination?.index
    );
    const itemsAPI = reorder(
      DashboardAPIData,
      result?.source?.index,
      result?.destination?.index
    );

    setDashboardData(items);
    setDashboardAPIData(itemsAPI);
  };
  const findSmallestMissingId = (ids) => {
    const idSet = new Set(ids);
    let smallestId = 1;
    while (idSet.has(smallestId)) {
      smallestId++;
    }
    return smallestId;
  };
  const replaceById = (array, id, newData) => {
    return array.map((item) =>
      item.id === id ? { ...item, ...newData } : item
    );
  };
  const onModelAdd = (
    modeldata,
    checkXId,
    checkYId,
    IsEdit,
    selectedNumber,
    IsAPIData
  ) => {
    const smallestMissingId = findSmallestMissingId(
      DashboardData?.map((f) => f?.id)
    );

    const xAxisList =
      modeldata?.xaxis_list === 'time'
        ? AxisList?.[TimePeriod ? TimePeriod : 'monthly']
        : modeldata?.model_type === 'number' &&
            AxisList?.[modeldata?.model_type] &&
            AxisList?.[modeldata?.model_type]?.length > 0
          ? AxisList?.[modeldata?.model_type]
          : AxisList &&
              AxisList?.[modeldata?.xaxis_list] &&
              AxisList?.[modeldata?.xaxis_list]?.length > 0
            ? AxisList?.[modeldata?.xaxis_list]
            : [];
    const yAxisList =
      AxisList &&
      AxisList &&
      AxisList?.[modeldata?.yaxis_list] &&
      AxisList?.[modeldata?.yaxis_list]?.length > 0
        ? AxisList?.[modeldata?.yaxis_list]
        : [];
    const seriesFilterData =
      checkXId?.length === 0
        ? xAxisList
        : xAxisList?.filter((f) => checkXId?.includes(f?.key));
    const seriesAllYFilterData = yAxisList
      ?.flatMap((f) => f?.payment_type_category || []) // Extract all children into a flat array
      ?.filter((child) => child?.payment_type_category_id);
    const seriesYFilterData = yAxisList
      ?.flatMap((f) => f?.payment_type_category || []) // Extract all children into a flat array
      ?.filter((child) => checkYId?.includes(child?.payment_type_category_id));

    if (
      modeldata?.model_type === 'line_chart' ||
      modeldata?.model_type === 'bar_chart'
    ) {
      let seriesData = [];
      let getData = [];
      if (
        seriesAllYFilterData?.length === checkYId?.length ||
        checkYId?.length === 0
      ) {
        seriesData = [
          {
            type: modeldata?.model_type === 'bar_chart' ? 'bar' : 'line',
            xKey: 'branches',
            yKey: `cat1`,
            yName: 'Total',
            stroke: null,
            interpolation: {
              type: 'smooth',
            },
          },
        ];
        getData = seriesFilterData?.map((f) => {
          const dataEntry = { branches: f?.value };
          dataEntry[`cat1`] = generateRandomValue(3);
          return dataEntry;
        });
      } else {
        seriesData = seriesYFilterData?.map((f) => ({
          type: modeldata?.model_type === 'bar_chart' ? 'bar' : 'line',
          xKey: 'branches',
          yKey: `cat${f?.payment_type_category_id}`,
          yName: f?.payment_type_category_title,
          stroke: f?.color,
          interpolation: {
            type: 'smooth',
          },
        }));
        getData = seriesFilterData?.map((f, index) => {
          const dataEntry = { branches: f?.value };
          // Add yKey values dynamically
          seriesYFilterData.forEach((series) => {
            dataEntry[`cat${series?.payment_type_category_id}`] =
              generateRandomValue(index);
          });

          return dataEntry;
        });
      }

      const linechartFormat = {
        id: modeldata?.id ? modeldata?.id : smallestMissingId,
        ...(modeldata?.id &&
          (IsAPIData || modeldata?.oldId) && {
            oldId:
              modeldata?.id && IsAPIData ? modeldata?.id : modeldata?.oldId,
          }),
        type: modeldata?.model_type,
        title: {
          text: modeldata?.model_title,
        },
        data: getData,
        series: seriesData,
      };
      if (IsAPIData) {
        return linechartFormat;
      } else {
        if (IsEdit) {
          const updatedData = replaceById(
            DashboardData,
            IsEdit?.id,
            linechartFormat
          );
          setDashboardData(updatedData);
        } else {
          setDashboardData((prev) =>
            prev ? [...prev, linechartFormat] : [linechartFormat]
          );
        }
      }
    } else if (modeldata?.model_type === 'pie_chart') {
      const seriesData = [
        {
          type: 'pie',
          angleKey: 'branchValue',
          calloutLabelKey: 'branches',
          sectorLabelKey: 'branchValue',
          yName: `Total of ${modeldata?.yaxis_list}`,
        },
      ];

      const getData = seriesFilterData?.map((f, index) => {
        const dataEntry = {
          branches: f?.value,
          [`branchValue`]: generateRandomValue(index),
        };

        return dataEntry;
      });

      const piechartFormat = {
        id: modeldata?.id ? modeldata?.id : smallestMissingId,
        ...(modeldata?.id &&
          (IsAPIData || modeldata?.oldId) && {
            oldId:
              modeldata?.id && IsAPIData ? modeldata?.id : modeldata?.oldId,
          }),
        type: 'pie_chart',
        data: getData,
        title: {
          text: modeldata?.model_title,
        },
        series: seriesData,
        width: 600,
        height: 400,
      };
      if (IsAPIData) {
        return piechartFormat;
      } else {
        if (IsEdit) {
          const updatedData = replaceById(
            DashboardData,
            IsEdit?.id,
            piechartFormat
          );
          setDashboardData(updatedData);
        } else {
          setDashboardData((prev) =>
            prev ? [...prev, piechartFormat] : [piechartFormat]
          );
        }
      }
    } else if (modeldata?.model_type === 'number') {
      const NumberFormat = {
        id: modeldata?.id ? modeldata?.id : smallestMissingId,
        ...(modeldata?.id &&
          (IsAPIData || modeldata?.oldId) && {
            oldId:
              modeldata?.id && IsAPIData ? modeldata?.id : modeldata?.oldId,
          }),
        type: 'number',
        name: modeldata?.model_title,
        Numberkey:
          selectedNumber && selectedNumber?.key
            ? selectedNumber?.key
            : seriesFilterData &&
                seriesFilterData?.length > 0 &&
                seriesFilterData?.[0]?.value
              ? seriesFilterData?.[0]?.value
              : '',
        value: 0, // generateRandomValue(1)
      };
      if (IsAPIData) {
        return NumberFormat;
      } else {
        if (IsEdit) {
          const updatedData = replaceById(
            DashboardData,
            IsEdit?.id,
            NumberFormat
          );
          setDashboardData(updatedData);
        } else {
          setDashboardData((prev) =>
            prev ? [...prev, NumberFormat] : [NumberFormat]
          );
        }
      }
    }
    if (!IsAPIData) {
      let apiData = {
        ...modeldata,
        id: modeldata?.id ? modeldata?.id : smallestMissingId,
        xAxisSel: checkXId,
        yAxisSel: checkYId,
      };
      if (IsEdit) {
        const updatedData = replaceById(DashboardAPIData, IsEdit?.id, apiData);
        setDashboardAPIData(updatedData);
      } else {
        setDashboardAPIData((prev) => (prev ? [...prev, apiData] : [apiData]));
      }
    }
  };

  const handleDeleteModel = (item) => {
    const filter = DashboardData?.filter((f) => f?.id !== item?.id);
    setDashboardData(filter);
    const filterAPI = DashboardAPIData?.filter((f) => f?.id !== item?.id);
    setDashboardAPIData(filterAPI);
  };
  // Model List API
  const getModelList = async () => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(URLS?.GET_MODEL_LIST);

      if (status === 200) {
        setLoader(false);

        const transformList = (list) =>
          list?.map((item) => ({
            ...item,
            value: item?.key,
            label: item?.value,
          }));
        setModelList({
          ...data?.data,
          model_list: transformList(data?.data?.model_list),
          xaxis_list: transformList(data?.data?.xaxis_list),
          yaxis_list: transformList(data?.data?.yaxis_list),
          yaxis_pie_chart: transformList(data?.data?.yaxis_pie_chart),
        });
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // Model List Axis API
  const getModelAxisList = async () => {
    //xaxis, yxis
    setLoader(true);
    setaxisLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_MODEL_AXIS_LIST
        // `?xaxis_filter=${xaxis}&yaxis_filter=${yxis}`
      );

      if (status === 200) {
        setLoader(false);
        let axisData = data?.data;
        let filterBranchList =
          data?.data?.branch &&
          data?.data?.branch?.length > 0 &&
          data?.data?.branch?.map((user) => ({
            ...user,
            key: user?.id,
            value: user?.branch_name,
          }));
        axisData = {
          ...axisData,
          branch: filterBranchList,
          daily: data?.data?.daily && convertToKeyValueArray(data?.data?.daily),
          monthly:
            data?.data?.monthly && convertToKeyValueArray(data?.data?.monthly),
          yearly:
            data?.data?.yearly && convertToKeyValueArray(data?.data?.yearly),
          number:
            data?.data?.number && convertToKeyValueArray(data?.data?.number),
          quarterly:
            data?.data?.quarterly &&
            convertToKeyValueArray(data?.data?.quarterly),
          weekly:
            data?.data?.weekly && convertToKeyValueArray(data?.data?.weekly),
        };
        setAxisList(axisData);
        setaxisLoader(false);
      }
    } catch (error) {
      setLoader(false);
      setaxisLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // Fetches the list of date filter
  const getDateFilterListDay = async () => {
    setLoader(true);

    try {
      const { status, data } = await axiosInstance.get(
        `${URLS.DATE_FILTER_LIST}?report_filter_type=day`
      );
      if (status === 200) {
        setLoader(false);
        const filterList = data?.data?.map((user) => ({
          label: user?.value,
          value: user?.key,
        }));
        const filterLast = filterList.filter(
          (f, i) => i !== filterList?.length - 1
        );
        setDateFilterList(filterLast);
      }
    } catch (error) {
      setLoader(false);
      setDateFilterList([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const getDateByFilter = () => {
    if (dateSelectedOption === 'today') {
      const start = moment().startOf('day').format('YYYY-MM-DD');
      const end = moment().endOf('day').format('YYYY-MM-DD');
      return { start, end };
    } else if (dateSelectedOption === 'yesterday') {
      const start = moment()
        .subtract(1, 'day')
        .startOf('day')
        .format('YYYY-MM-DD');
      const end = moment().subtract(1, 'day').endOf('day').format('YYYY-MM-DD');
      return { start, end };
    } else if (dateSelectedOption === 'this_week') {
      const start = moment().startOf('isoWeek').format('YYYY-MM-DD');
      const end = moment().endOf('isoWeek').format('YYYY-MM-DD');
      return { start, end };
    } else if (dateSelectedOption === 'this_month') {
      const start = moment().startOf('month').format('YYYY-MM-DD');
      const end = moment().endOf('month').format('YYYY-MM-DD');
      return { start, end };
    } else if (dateSelectedOption === 'this_quarter') {
      const start = moment().startOf('quarter').format('YYYY-MM-DD');
      const end = moment().endOf('quarter').format('YYYY-MM-DD');
      return { start, end };
    } else if (dateSelectedOption === 'last_week') {
      const start = moment()
        .subtract(1, 'week')
        .startOf('isoWeek')
        .format('YYYY-MM-DD');
      const end = moment()
        .subtract(1, 'week')
        .endOf('isoWeek')
        .format('YYYY-MM-DD');
      return { start, end };
    } else if (dateSelectedOption === 'last_month') {
      const start = moment()
        .subtract(1, 'month')
        .startOf('month')
        .format('YYYY-MM-DD');
      const end = moment()
        .subtract(1, 'month')
        .endOf('month')
        .format('YYYY-MM-DD');
      return { start, end };
    } else if (dateSelectedOption === 'last_7_days') {
      const start = moment()
        .subtract(6, 'days')
        .startOf('day')
        .format('YYYY-MM-DD');
      const end = moment().endOf('day').format('YYYY-MM-DD');
      return { start, end };
    } else if (dateSelectedOption === 'last_14_days') {
      const start = moment()
        .subtract(13, 'days')
        .startOf('day')
        .format('YYYY-MM-DD');
      const end = moment().endOf('day').format('YYYY-MM-DD');
      return { start, end };
    } else if (dateSelectedOption === 'last_30_days') {
      const start = moment()
        .subtract(29, 'days')
        .startOf('day')
        .format('YYYY-MM-DD');
      const end = moment().endOf('day').format('YYYY-MM-DD');
      return { start, end };
    } else if (dateSelectedOption === 'last_60_days') {
      const start = moment()
        .subtract(59, 'days')
        .startOf('day')
        .format('YYYY-MM-DD');
      const end = moment().endOf('day').format('YYYY-MM-DD');
      return { start, end };
    } else if (dateSelectedOption === 'last_90_days') {
      const start = moment()
        .subtract(89, 'days')
        .startOf('day')
        .format('YYYY-MM-DD');
      const end = moment().endOf('day').format('YYYY-MM-DD');
      return { start, end };
    } else {
      return '';
    }
  };

  const SaveTemplateDashboard = async () => {
    const filter = {
      branch_id:
        selectedBranches && selectedBranches?.length > 0
          ? selectedBranches.toString()
          : '',
      end_date: getDateByFilter()?.end ? getDateByFilter()?.end : '',
      start_date: getDateByFilter()?.start ? getDateByFilter()?.start : '',
      date_filter: dateSelectedOption,
      filter_time_period: TimePeriod,
    };
    const modeldata = DashboardAPIData?.map((models, index) => {
      const xAxisList =
        AxisList &&
        AxisList?.[models?.xaxis_list] &&
        AxisList?.[models?.xaxis_list]?.length > 0
          ? AxisList?.[models?.xaxis_list]
          : [];
      const yAxisList =
        AxisList &&
        AxisList &&
        AxisList?.[models?.yaxis_list] &&
        AxisList?.[models?.yaxis_list]?.length > 0
          ? AxisList?.[models?.yaxis_list]
          : [];
      const seriesAllYFilterData = yAxisList
        ?.flatMap((f) => f?.payment_type_category || []) // Extract all children into a flat array
        ?.filter((child) => child?.payment_type_category_id);

      return {
        model_order: parseFloat(index) + 1,
        model_title: models?.model_title,
        model_type: models?.model_type,
        ...(models?.oldId && { id: models?.oldId ? models?.oldId : '' }),
        xaxis_list: models?.xaxis_list ? models?.xaxis_list : '',
        yaxis_list: models?.yaxis_list ? models?.yaxis_list : '',
        xaxis_value:
          xAxisList &&
          xAxisList?.length > 0 &&
          models?.xAxisSel &&
          models?.xAxisSel?.length > 0 &&
          xAxisList?.length === models?.xAxisSel?.length
            ? ''
            : xAxisList &&
                xAxisList?.length > 0 &&
                models?.xaxis_value &&
                xAxisList?.length === models?.xaxis_value?.split(',')?.length
              ? ''
              : models?.xAxisSel && models?.xAxisSel?.length > 0
                ? models?.xAxisSel?.toString()
                : models?.xaxis_value
                  ? models?.xaxis_value
                  : '',
        yaxis_value:
          seriesAllYFilterData &&
          seriesAllYFilterData?.length > 0 &&
          models?.yAxisSel &&
          models?.yAxisSel?.length > 0 &&
          seriesAllYFilterData?.length === models?.yAxisSel?.length
            ? ''
            : seriesAllYFilterData &&
                seriesAllYFilterData?.length > 0 &&
                models?.yaxis_value &&
                seriesAllYFilterData?.length ===
                  models?.yaxis_value?.split(',')?.length
              ? ''
              : models?.yAxisSel && models?.yAxisSel?.length > 0
                ? models?.yAxisSel?.toString()
                : models?.yaxis_value
                  ? models?.yaxis_value
                  : '',
      };
    });
    const sendData = {
      dashboard_name: dashboardName,
      dashboard_filter: JSON.stringify(filter),
      model_list: modeldata,
    };
    const ApiUrl = DashbaordId
      ? URLS.UPDATE_DASHBOARD + DashbaordId
      : URLS.CREATE_DASHBOARD;
    const method = DashbaordId ? 'put' : 'post';
    try {
      const { status, data } = await axiosInstance[method](ApiUrl, sendData);
      if (status === 200) {
        if (data?.status) {
          setApiMessage('success', data?.message);
          DashbaordId
            ? router.push(`/chart-dashboard/${DashbaordId}`)
            : router.push(`/chart-dashboard/${data?.dashboard_id}`);
        } else {
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const getDateByfilter = () => {
    if (dateSelectedOption === 'today') {
      return DateFormat(moment().startOf('day'), 'dates');
    } else if (dateSelectedOption === 'yesterday') {
      return DateFormat(moment().subtract(1, 'day').startOf('day'), 'dates');
    } else if (dateSelectedOption === 'this_week') {
      const start = DateFormat(moment().startOf('isoWeek'), 'dates');
      const end = DateFormat(moment().endOf('isoWeek'), 'dates');
      return `${start} - ${end}`;
    } else if (dateSelectedOption === 'this_month') {
      const start = DateFormat(moment().startOf('month'), 'dates');
      const end = DateFormat(moment().endOf('month'), 'dates');
      return `${start} - ${end}`;
    } else if (dateSelectedOption === 'this_quarter') {
      const start = DateFormat(moment().startOf('quarter'), 'dates');
      const end = DateFormat(moment().endOf('quarter'), 'dates');
      return `${start} - ${end}`;
    } else if (dateSelectedOption === 'last_week') {
      const start = DateFormat(
        moment().subtract(1, 'week').startOf('isoWeek'),
        'dates'
      );
      const end = DateFormat(
        moment().subtract(1, 'week').endOf('isoWeek'),
        'dates'
      );
      return `${start} - ${end}`;
    } else if (dateSelectedOption === 'last_month') {
      const start = DateFormat(
        moment().subtract(1, 'month').startOf('month'),
        'dates'
      );
      const end = DateFormat(
        moment().subtract(1, 'month').endOf('month'),
        'dates'
      );
      return `${start} - ${end}`;
    } else if (dateSelectedOption === 'last_7_days') {
      const start = DateFormat(
        moment().subtract(6, 'days').startOf('day'),
        'dates'
      );
      const end = DateFormat(moment().endOf('day'), 'dates');
      return `${start} - ${end}`;
    } else if (dateSelectedOption === 'last_14_days') {
      const start = DateFormat(
        moment().subtract(13, 'days').startOf('day'),
        'dates'
      );
      const end = DateFormat(moment().endOf('day'), 'dates');
      return `${start} - ${end}`;
    } else if (dateSelectedOption === 'last_30_days') {
      const start = DateFormat(
        moment().subtract(29, 'days').startOf('day'),
        'dates'
      );
      const end = DateFormat(moment().endOf('day'), 'dates');
      return `${start} - ${end}`;
    } else if (dateSelectedOption === 'last_60_days') {
      const start = DateFormat(
        moment().subtract(59, 'days').startOf('day'),
        'dates'
      );
      const end = DateFormat(moment().endOf('day'), 'dates');
      return `${start} - ${end}`;
    } else if (dateSelectedOption === 'last_90_days') {
      const start = DateFormat(
        moment().subtract(89, 'days').startOf('day'),
        'dates'
      );
      const end = DateFormat(moment().endOf('day'), 'dates');
      return `${start} - ${end}`;
    } else {
      return '';
    }
  };
  // Get One Dashboard by ID
  const getOneDashboardList = async (ID) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_DASHBOARD_BY_ID + `${ID}`
      );
      if (status === 200) {
        setLoader(false);
        let oneDashboard = { ...data.data };
        if (oneDashboard) {
          const dashfil = oneDashboard?.dashboard_filter
            ? JSON.parse(oneDashboard?.dashboard_filter)
            : '';
          // setDashboardDataBYID({
          //   ...oneDashboard,
          //   dashboard_filter: dashfil,
          // });
          const modelData =
            data?.data &&
            data?.data?.dashboard_models &&
            data?.data?.dashboard_models?.length > 0 &&
            data?.data?.dashboard_models?.map((item) => {
              const xarray =
                item?.xaxis_value === ''
                  ? []
                  : item?.model_type === 'number'
                    ? item?.xaxis_value
                    : item?.xaxis_list === 'weekly' ||
                        item?.xaxis_list === 'yearly'
                      ? item?.xaxis_value.split(',')
                      : item?.xaxis_value
                            ?.split(',')
                            ?.map((val) => Number(val)) // Convert each value to a number
                            .some((num) => isNaN(num)) // Check if any value is NaN
                        ? item?.xaxis_value.split(',') // Use the original string array if NaN exists
                        : item?.xaxis_value.split(',').map(Number);
              const Yarray =
                item?.yaxis_value === ''
                  ? []
                  : item?.yaxis_value
                        ?.split(',')
                        ?.map((val) => Number(val)) // Convert each value to a number
                        .some((num) => isNaN(num)) // Check if any value is NaN
                    ? item?.yaxis_value.split(',') // Use the original string array if NaN exists
                    : item?.yaxis_value.split(',').map(Number);
              return onModelAdd(item, xarray, Yarray, '', '', true);
            });
          setDashboardData(modelData && modelData?.length > 0 ? modelData : []);
          const modelAPIData =
            data?.data &&
            data?.data?.dashboard_models &&
            data?.data?.dashboard_models?.length > 0 &&
            data?.data?.dashboard_models?.map((item) => {
              return { ...item, oldId: item?.id };
            });
          setDashboardAPIData(modelAPIData);
          setDashboardName(
            oneDashboard?.dashboard_name ? oneDashboard?.dashboard_name : ''
          );

          setSelectedBranches(
            dashfil?.branch_id && dashfil?.branch_id?.length > 0
              ? dashfil?.branch_id.split(',').map(Number)
              : []
          );
          setDateSelectedOption(
            dashfil?.date_filter ? dashfil?.date_filter : ''
          );
          setTimePeriod(
            dashfil?.filter_time_period
              ? dashfil?.filter_time_period
              : 'monthly'
          );
        }
      }
    } catch {
      setLoader(false);
      setDashboardData([]);
      // setDashboardDataBYID([]);
      setDashboardAPIData([]);
    }
  };
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const handleResize = () => {
        setScreenHeight(window.innerHeight);
        setScreenWidth(window.innerWidth);
      };

      window.addEventListener('resize', handleResize);
      handleResize();
      return () => {
        window.removeEventListener('resize', handleResize);
      };
    }
  }, [screenHeight, screenWidth]);
  useEffect(() => {
    getModelList();
    getDateFilterListDay();
    getModelAxisList();
  }, []);
  useEffect(() => {
    if (DashbaordId && AxisList) {
      setLoader(true);
      getOneDashboardList(DashbaordId);
    }
  }, [DashbaordId, AxisList]);

  return (
    <>
      <Box
        className={
          DashboardData === undefined
            ? 'dashboard-page blanck-dashboard-page'
            : DashboardData && DashboardData?.length === 0
              ? 'dashboard-page h100'
              : 'dashboard-page'
        }
      >
        <Box className="dashboard-list">
          <Box className="d-flex align-center">
            <ArrowBackIosIcon
              className="mr8 cursor-pointer"
              onClick={() => router.push('/chart-dashboard')}
            />
            <Box className="dashboard-name">
              <CustomTextField
                fullWidth
                id="ename"
                name="ename"
                value={dashboardName}
                label="Dashboard"
                required
                placeholder="Dashboard Name"
                onChange={(e) => {
                  setDashboardName(e.target.value);
                }}
              />
            </Box>
          </Box>

          <Box className="d-flex gap-sm align-center flex-wrap justify-end">
            <CustomButton
              variant="outlined"
              startIcon={<BookmarkBorderIcon />}
              title={DashbaordId ? 'Update' : 'Save'}
              onClick={() => {
                if (dashboardName) {
                  SaveTemplateDashboard();
                } else {
                  setApiMessage('error', 'Dashboard Name is required.');
                }
              }}
            />
            <CustomButton
              variant="outlined"
              startIcon={<AddchartIcon />}
              title="Add models"
              onClick={() => handleOpenModel()}
            />
          </Box>
        </Box>

        <Box className="dashboard-filter">
          <Box className="subcategories-wrap">
            <Box>
              <DateFilter
                dateSelectedOption={dateSelectedOption}
                setDateSelectedOption={setDateSelectedOption}
                customStartDate={customStartDate}
                setCustomStartDate={setCustomStartDate}
                customEndDate={customEndDate}
                setCustomEndDate={setCustomEndDate}
                dateFilterList={dateFilterList}
                type="dsr"
              />
              {getDateByfilter() ? (
                <span className="title-text">{getDateByfilter()}</span>
              ) : (
                <></>
              )}
            </Box>
            {/* Branch Filter */}

            <MultipleFilter
              selected={selectedBranches}
              setSelected={setSelectedBranches}
              List={branchList}
              placeholder="Branches"
            />
          </Box>

          <Box className="timperiod-filters d-flex align-center justify-end">
            <CustomButton
              variant={TimePeriod === 'yearly' ? 'contained' : 'outlined'}
              onClick={() => {
                setTimePeriod('yearly');
              }}
              title="Year"
            />
            <CustomButton
              variant={TimePeriod === 'quarterly' ? 'contained' : 'outlined'}
              onClick={() => {
                setTimePeriod('quarterly');
              }}
              title="Quarter"
            />
            <CustomButton
              variant={TimePeriod === 'monthly' ? 'contained' : 'outlined'}
              onClick={() => {
                setTimePeriod('monthly');
              }}
              title="Month"
            />
            <CustomButton
              variant={TimePeriod === 'weekly' ? 'contained' : 'outlined'}
              onClick={() => {
                setTimePeriod('weekly');
              }}
              title="Week"
            />
            <CustomButton
              variant={TimePeriod === 'daily' ? 'contained' : 'outlined'}
              onClick={() => {
                setTimePeriod('daily');
              }}
              title="Day"
            />
          </Box>
        </Box>
        {DashboardData && DashboardData?.length > 0 ? (
          <Box className="d-flex align-start pt8">
            <InfoIcon
              sx={{ marginRight: '8px' }}
              className="info-icon cursor-poniter"
            />
            <Typography className="title-text template-text">
              The values shown are placeholders. Real data will appear on the
              dashboard after editing is complete.
            </Typography>
          </Box>
        ) : (
          <></>
        )}

        {loader || (DashboardData === undefined && DashbaordId) ? (
          <Box className="content-loader">
            <CircularProgress className="loader" color="inherit" />
          </Box>
        ) : (
          <Box
            className={
              DashboardData && DashboardData?.length > 0
                ? 'Dashboard-template-models'
                : 'Dashboard-template-models Dashboard-template-models-empty'
            }
          >
            <DragDropContext onDragEnd={onDragEnd}>
              {DashboardData && DashboardData?.length > 0 ? (
                <>
                  <Droppable droppableId="droppable">
                    {(provided) => (
                      <Box
                        {...provided.droppableProps}
                        ref={provided.innerRef}
                        sx={{
                          display: 'flex',
                          flexDirection: 'row',
                          flexWrap: 'wrap',
                          gap: 2,
                        }}
                      >
                        {DashboardData?.map((item, index) => {
                          return (
                            <Draggable
                              key={item.id}
                              draggableId={item.id.toString()}
                              index={index}
                              // isDragDisabled={disable}
                            >
                              {(provided) => {
                                return (
                                  <Box
                                    ref={provided.innerRef}
                                    {...provided.draggableProps}
                                    className={`dashboard-model-view ${
                                      item?.type === 'number'
                                        ? 'dashboard-numbers-section'
                                        : item?.type === 'pie_chart'
                                          ? 'dashboard-piechart-section'
                                          : 'dashboard-chart-section'
                                    }`}
                                  >
                                    <Box className="Edit-drag-icon">
                                      <Tooltip
                                        title={<Typography>Edit</Typography>}
                                        placement="bottom"
                                        arrow
                                        classes={{
                                          tooltip: 'info-tooltip-container',
                                        }}
                                      >
                                        <Box
                                          className="cursor-pointer"
                                          onClick={() => {
                                            handleOpenModel(true);
                                            setIsEditData({
                                              ...DashboardAPIData[index],
                                              isFirst: true,
                                            });
                                          }}
                                        >
                                          <EditIcon />
                                        </Box>
                                      </Tooltip>
                                      <Tooltip
                                        title={<Typography>Delete</Typography>}
                                        placement="bottom"
                                        arrow
                                        classes={{
                                          tooltip: 'info-tooltip-container ',
                                        }}
                                      >
                                        <Box
                                          className="cursor-pointer"
                                          onClick={() =>
                                            handleDeleteModel(item)
                                          }
                                        >
                                          <DeleteIcon />
                                        </Box>
                                      </Tooltip>
                                      <Tooltip
                                        title={<Typography>Drag</Typography>}
                                        placement="bottom"
                                        arrow
                                        classes={{
                                          tooltip: 'info-tooltip-container',
                                        }}
                                      >
                                        <Box {...provided.dragHandleProps}>
                                          <DragIcon />
                                        </Box>
                                      </Tooltip>
                                    </Box>

                                    {item?.type === 'line_chart' ? (
                                      <Box>
                                        <LineCharts item={item} />
                                      </Box>
                                    ) : item?.type === 'bar_chart' ? (
                                      <Box>
                                        <BarChart item={item} />
                                      </Box>
                                    ) : item?.type === 'pie_chart' ? (
                                      <Box>
                                        <PieCharts
                                          item={item}
                                          screenWidth={screenWidth}
                                        />
                                      </Box>
                                    ) : item?.type === 'number' ? (
                                      <Box className="">
                                        <DashboardDataView item={item} />
                                      </Box>
                                    ) : (
                                      <></>
                                    )}
                                  </Box>
                                );
                              }}
                            </Draggable>
                          );
                        })}
                      </Box>
                    )}
                  </Droppable>
                </>
              ) : (
                <Box className="h100">
                  {/* <Box className={'blank-dashboard blank-dahsboard-temp'}>
                    <Box>
                      <AutoGraphIcon
                        className="graph-icon"
                        // style={{
                        //   width: screenWidth > 600 && `${screenHeight / 6}`,
                        //   height: screenWidth > 600 && `${screenHeight / 6}`
                        // }}
                      />
                      <DonutSmallIcon
                        className="graph-icon"
                        // style={{
                        //   width: screenWidth > 600 && `${screenHeight / 6}`,
                        //   height: screenWidth > 600 && `${screenHeight / 6}`
                        // }}
                      />
                    </Box>
                    <Box>
                      <EqualizerIcon
                        className="graph-icon"
                        // style={{
                        //   width: screenWidth > 600 && `${screenHeight / 6}`,
                        //   height: screenWidth > 600 && `${screenHeight / 6}`
                        // }}
                      />
                      <InboxIcon
                        className="graph-icon"
                        // style={{
                        //   width: screenWidth > 600 && `${screenHeight / 6}`,
                        //   height: screenWidth > 600 && `${screenHeight / 6}`
                        // }}
                      />
                    </Box>

                    <Typography className="title-sm blank-text">
                      Dashbaord is empty.
                    </Typography>
                    <Typography className="title-sm blank-text">
                      Click the "Add Models" button to add models.
                    </Typography>
                  </Box> */}
                  <Box className="no-data d-flex align-center justify-center">
                    <NoDataView
                      title="Dashbaord is empty"
                      description='Click the "Add Models" button to add models.'
                    />
                  </Box>
                </Box>
              )}
            </DragDropContext>
          </Box>
        )}
      </Box>

      <RightDrawer
        anchor={'right'}
        open={openModel}
        onClose={handleCloseModel}
        title={IsEditData ? 'Edit Models' : 'Add Models'}
        content={
          <>
            <AddModels
              AxisList={AxisList}
              handleClose={handleCloseModel}
              modelList={modelList}
              onModelAdd={onModelAdd}
              IsEditData={IsEditData}
              axisloader={axisloader}
              setIsEditData={setIsEditData}
            />
          </>
        }
      />
    </>
  );
}
