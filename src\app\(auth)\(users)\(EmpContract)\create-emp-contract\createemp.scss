.create-emp-contract {
  background-color: var(--color-white);
  padding: 26px 15px;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-xs);
  height: 100%;
  overflow: auto;
  .contract-view-btn-list {
    background: var(--color-white);
    height: 100%;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-xs);
    border: var(--normal-sec-border);
    .contract-view-btn {
      padding: var(--spacing-xxs);
      cursor: pointer;
      svg {
        width: var(--icon-size-md);
        height: var(--icon-size-md);
      }
      &:hover {
        background: var(--action-icon-bg-color);
        border-radius: var(--border-radius-xs);
      }
    }
  }
  .display-grid {
    display: grid;
    grid-template-columns: 32% 32%;
    column-gap: 15px;
    row-gap: 15px;
    @media (max-width: 899px) {
      display: block;
    }
  }
}
