.staff-sec-wrap {
  background-color: var(--color-white);
  padding: var(--spacing-xxl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-xs);
  min-height: calc(100vh - 152px - var(--banner-height));
  .selection-staff-count {
    margin-top: 16px;
    .select-all-text {
      color: var(--color-primary);
      cursor: pointer;
      // text-decoration: underline;
      font-weight: var(--font-weight-medium);
    }
  }
  .export-section {
    display: flex;
    // border: 1px solid var(--color-primary);
    // border-radius: var(--border-radius-md);
    .custom-button-wrapper {
      .MuiButtonBase-root {
        border: 0 !important;
        padding: 8px;
        min-width: 0;
      }
    }
  }
  .staff-table {
    padding: 0px;
    margin: 0px;
    box-shadow: none;
    .checkbox svg {
      fill: var(--icon-color-primary);
    }
    .no-data-found {
      min-height: calc(100vh - 210px - var(--banner-height));
      display: flex;
      align-items: center;
      justify-content: center;
    }
    @media (max-width: 899px) {
      .MuiDataGrid-columnHeader {
        height: 40px !important;
        min-height: 40px !important;
      }

      .MuiDataGrid-cell {
        height: 72px !important;
        line-height: 70px !important;
        display: flex;
        align-items: center;
      }

      .MuiDataGrid-row {
        height: 72px !important;
        min-height: 72px !important;
      }
    }
  }
  @media (max-width: 767px) {
    padding: 16px 14px 20px;
  }

  .filter-section {
    .filter-icon-wrap {
      line-height: 0px;
      .MuiButtonBase-root {
        padding: 0px !important;
        .filter-icon {
          height: 16px;
          width: 16px;
          path {
            fill: var(--icon-color-primary) !important;
          }
        }
        &:hover {
          path {
            fill: var(--icon-color-white) !important;
          }
        }
      }
    }
  }
}

.filter-popover {
  .MuiPaper-root {
    width: 330px;
  }
}

.staff-filter {
  // padding: 20px 20px;
  .staff-filter-button {
    display: flex;
    column-gap: 10px;
    margin-top: 0px !important;
  }

  .check-box-form {
    .check-box {
      .check-icon {
        path {
          fill: var(--icon-color-primary);
        }
      }
    }
  }
}
.download-staff-modal {
  overflow: hidden;
  .MuiPaper-root {
    overflow: hidden;
  }
  .download-staff-list {
    // max-width: 300px;
    .download-staff-section {
      margin-top: 8px;
      display: grid;
      padding: 0 5px;
      // grid-template-columns: calc(50% - 8px) calc(50% - 8px);
      grid-template-columns: repeat(auto-fit, minmax(0, 350px));
      gap: var(--spacing-lg);
      @media (max-width: 767px) {
        // grid-template-columns: 100%;
      }
      div {
        width: 100%;
      }
      .checklist-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        // width: 300px;
        padding: 6px 6px;
        border-radius: 8px;
        // box-shadow: var(--box-shadow-xs);
        box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
        .custom-button-wrapper {
          width: 32px;
          height: 32px;
          .MuiButtonBase-root {
            padding: 4px;
            min-width: 0;
          }
        }
      }
    }
  }
}
.download-staff-list {
  .download-staff-section {
    margin-top: 8px;
    display: grid;
    padding: 0 5px;
    // grid-template-columns: calc(50% - 8px) calc(50% - 8px);
    grid-template-columns: repeat(auto-fit, minmax(0, 350px));
    gap: var(--spacing-lg);
    @media (max-width: 767px) {
      // grid-template-columns: 100%;
    }
    div {
      width: 100%;
    }
    .checklist-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 300px;
      padding: 6px 6px;
      border-radius: 8px;
      // box-shadow: var(--box-shadow-xs);
      box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
      .custom-button-wrapper {
        width: 32px;
        height: 32px;
        .MuiButtonBase-root {
          padding: 4px;
          min-width: 0;
        }
      }
    }
  }
}
.actions-staff {
  width: 72px;
  gap: 10px;
}

.create-staff-section {
  background-color: var(--color-white);
  padding: 26px 20px 56px;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-xs);

  form {
    overflow-x: hidden !important;
  }

  .display-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(0, 250px));
    gap: var(--spacing-lg);
    align-items: flex-start;
    width: 100%;
  }
  .space-grid {
    padding-top: var(--spacing-lg);
  }
  .display-grid-50 {
    display: grid;
    grid-template-columns: repeat(
      auto-fit,
      minmax(0, calc(500px + var(--spacing-lg)))
    );
    gap: var(--spacing-lg);
    align-items: flex-start;
    width: 100%;
  }

  .employee-contract-section {
    margin-top: 24px;
    padding-left: 3px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(0, 250px));
    gap: var(--spacing-lg);
    div {
      width: 100%;
    }

    .selected-files {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 250px;
      width: 100%;
      padding: 6px 12px;
      border-radius: 8px;
      margin-bottom: 8px;
      box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;

      svg {
        width: 21px;
        height: 21px;
        cursor: pointer;
      }

      .file-name {
        width: calc(250px - 10px - 24px);

        p {
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden;
          word-break: break-all;
        }
      }
    }
  }

  .holiday-condition {
    .MuiCheckbox-root {
      padding: 0 12px !important;
    }
  }

  .health-condition {
    display: flex;
    .check-box-form {
      flex-direction: row-reverse;
      margin-left: 0;
      .MuiFormControlLabel-label {
        font-family: var(--font-family-primary);
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-regular);
        color: var(--text-color-black);
        line-height: var(--line-height-base);
      }
    }
    .Mui-disabled {
      opacity: 1;
      cursor: not-allowed !important;
      .MuiTypography-root {
        color: var(--text-color-black) !important;
      }
    }
  }
}

.staff-leave-filter {
  .MuiPaper-root {
    width: 300px !important;
    min-width: 300px !important;
    padding: 12px 15px 22px;
  }
  .drawer-header {
    align-items: center !important;
    .cross-icon {
      padding: 0px;
      .svg-icon {
        height: 21px;
        width: 21px;
      }
    }
  }
  .staff-filter {
    .reset-wrap {
      &:hover {
        color: var(--color-primary);
        svg {
          path {
            fill: var(--icon-color-primary) !important;
          }
        }
      }
    }
  }
}
.export-popover {
  .MuiPaper-root {
    width: 120px !important;
    margin-top: 8px;
    padding: 10px 20px;
  }

  .export-option {
    p {
      text-align: center;
    }
  }
}
