// RecipeStaffTrackingTable Component Styles

.recipe-staff-tracking-table {
  // Reset button states
  .reset-button-container {
    display: flex;
    align-items: center;
    justify-content: center;
    
    // Disabled state
    &--disabled {
      opacity: var(--opacity-5);
      cursor: not-allowed;
      pointer-events: none;
      filter: grayscale(1);
      background-color: var(--color-secondary);
      border-radius: var(--border-radius-xs);
      padding: var(--spacing-xs);
      
      .reset-icon {
        opacity: var(--opacity-6);
        cursor: not-allowed;
        pointer-events: none;
        color: var(--icon-color-slate-gray);
      }
    }
    
    // Enabled state
    &--enabled {
      .reset-icon {
        opacity: var(--opacity-10);
        cursor: pointer;
        transition: all 0.2s ease;
        color: var(--icon-color-primary);
        
        &:hover {
          transform: scale(1.1);
          opacity: var(--opacity-8);
          color: var(--icon-color-primary);
        }
      }
    }
  }
} 