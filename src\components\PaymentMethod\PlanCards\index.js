import React, { useEffect, useState, useContext } from 'react';
import { Box, Typography, Radio, Collapse, Divider } from '@mui/material';
// import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import AuthContext from '@/helper/authcontext';
import SwitchToggle from '@/components/UI/ToggleButton';
// import { useRouter } from 'next/navigation';
import moment from 'moment';
import { formatDuration } from '@/helper/common/commonFunctions';
import { identifiers } from '@/helper/constants/identifier';
import GroupOutlinedIcon from '@mui/icons-material/GroupOutlined';
import StorageIcon from '@mui/icons-material/Storage';
import './pricingcards.scss';

const PricingCards = ({ subscriptions, selectedPlan, setSelectedPlan }) => {
  // const router = useRouter();
  const { authState } = useContext(AuthContext);
  const currency =
    authState?.currency_details && authState?.currency_details?.symbol
      ? authState?.currency_details?.symbol
      : '£';
  const [selected, setSelected] = useState('recursive');
  const [groupedPlans, setGroupedPlans] = useState({});

  // Array of objects containing feature details
  // const planFeatures = [
  //   { description: 'Enjoy all the features without restrictions.' },
  //   { description: 'Get unlimited access to every hub and feature.' },
  //   { description: 'Tools to easily manage your team and business.' },
  // ];

  const handleSelectPlan = (plan) => {
    setSelectedPlan(plan);
  };

  // Filter subscriptions based on selected plan type (Monthly/Yearly)
  useEffect(() => {
    const filteredSubscriptions =
      (subscriptions &&
        subscriptions?.length > 0 &&
        subscriptions
          ?.filter(
            (plan) =>
              (plan?.is_creation_repeated === 1 &&
                plan?.subs_plan_type === 'one_time') ||
              (plan?.purchased_plan === false && plan?.sp_status !== 'queued')
          )
          ?.filter(
            (subscription) =>
              subscription?.subs_plan_type === selected.toLowerCase()
          )) ||
      [];

    // Group plans by category
    const grouped = {};
    filteredSubscriptions?.forEach((plan) => {
      const category = plan.subs_plan_category || 'other';
      if (!grouped[category]) {
        grouped[category] = [];
      }
      grouped[category].push(plan);
    });
    setGroupedPlans(grouped);
  }, [selected, subscriptions]);

  useEffect(() => {
    const selectedplan =
      subscriptions &&
      subscriptions?.length > 0 &&
      subscriptions
        ?.filter((plan) => plan?.purchased_plan === false)
        ?.find((subscription) => subscription?.purchased_plan);
    selectedplan && setSelectedPlan(selectedplan);
  }, [subscriptions]);

  return (
    <Box className="card-container">
      <Box className="choose-pln-header-wrap">
        <Typography variant="h6" className="sub-header-text">
          Choose Plan
        </Typography>
        <Box className="toggle-switch-wrap d-flex justify-end">
          <SwitchToggle
            options={identifiers?.SUBSCRIPTION_TYPE}
            selected={selected}
            setSelected={setSelected}
          />
        </Box>
      </Box>
      <Divider />
      {subscriptions && subscriptions?.length > 0 ? (
        <Box className="pricing-card d-flex flex-col">
          {Object.entries(groupedPlans)?.map(([category, plans]) => (
            <>
              <Typography variant="h6" className="category-title">
                {category.charAt(0).toUpperCase() + category.slice(1)} Plans
              </Typography>
              <Box key={category} className="category-group">
                {plans?.map((subscription) => (
                  <Box
                    className={`card-container ${
                      subscription?.purchased_plan ? 'selected' : ''
                    }`}
                    key={subscription?.id}
                    onClick={() => {
                      // !subscription?.purchased_plan &&
                      // ? setTab(3)
                      handleSelectPlan(subscription);
                    }}
                  >
                    <Box className="card-accordian-wrap">
                      <Box className="d-flex card-wrap">
                        <Radio
                          checked={selectedPlan?.id === subscription?.id}
                          onChange={() =>
                            // !subscription?.purchased_plan &&
                            setSelectedPlan(subscription)
                          }
                          className="choose-plan-btn"
                          onClick={(e) => e.stopPropagation()}
                        />
                        <Box className="details">
                          <Typography className="plan-name sub-content-text">
                            {subscription?.subs_plan_name}
                          </Typography>
                          {subscription?.subs_plan_description && (
                            <Typography className="content-text">
                              {subscription?.subs_plan_description}
                            </Typography>
                          )}
                        </Box>
                      </Box>
                      <Box className="pricing">
                        <Typography className="sub-content-text">
                          {currency +
                            ' ' +
                            subscription?.subs_plan_cost.toFixed(2)}
                          {subscription?.subs_total_days > 0 && (
                            <span className="sub-content-text">
                              / {formatDuration(subscription?.subs_total_days)}
                            </span>
                          )}
                        </Typography>
                        <Typography className="content-text">
                          {subscription?.subs_plan_category === 'core' ? (
                            <>
                              <GroupOutlinedIcon className="employees-icon" />
                              <span className="limit-wrap">
                                {subscription?.subs_limit_min}
                                {subscription?.subs_limit_max && ' - '}
                                {subscription?.subs_limit_max} Employees
                              </span>
                            </>
                          ) : (
                            <>
                              <StorageIcon className="employees-icon" />
                              <span className="limit-wrap storage">
                                {subscription?.subs_limit_min}
                                {subscription?.subs_limit_max && ' - '}
                                {subscription?.subs_limit_max}{' '}
                                {subscription?.sub_storage_size}
                              </span>
                            </>
                          )}
                          {subscription?.subs_is_free_trial ? (
                            <span className="limit-wrap">/Free Trial</span>
                          ) : (
                            ''
                          )}
                        </Typography>
                        <Typography className="content-text">
                          {subscription?.subs_plan_category === 'core' ? (
                            <>
                              {/* {' '}
                              | */}
                              <StorageIcon className="employees-icon" />
                              <span className="limit-wrap storage">
                                {subscription?.subs_limit_min}
                                {subscription?.subs_limit_max && ' - '}
                                {subscription?.subs_limit_max}{' '}
                                {subscription?.sub_storage_size}
                              </span>
                            </>
                          ) : (
                            ''
                          )}
                        </Typography>
                      </Box>
                      {subscription?.purchased_plan ? (
                        <span className="active-plan fw600 p12">Active</span>
                      ) : (
                        ''
                      )}
                    </Box>
                    {/* {subscription?.purchased_plan &&
                      subscription?.expiry_date && (
                        <Typography
                          component="p"
                          className="content-text renew-payment-text"
                        >
                          Your current plan will expire on{' '}
                          {moment(subscription?.expiry_date).format(
                            'Do MMMM YYYY'
                          )}
                          . Renew now to avoid any interruption in service.
                        </Typography>
                      )} */}
                    {subscription?.purchased_plan &&
                      subscription?.expiry_date &&
                      (() => {
                        const daysRemaining = moment(
                          subscription?.expiry_date
                        ).diff(moment(), 'days');
                        if (daysRemaining <= 15 && daysRemaining >= 0) {
                          return (
                            <Typography
                              component="p"
                              className="content-text renew-payment-text"
                            >
                              Your current plan will expire on{' '}
                              {moment(subscription?.expiry_date).format(
                                'Do MMMM YYYY'
                              )}
                              . Renew now to avoid any interruption in service.
                            </Typography>
                          );
                        }
                        return null;
                      })()}
                    <Box className="plan-offers-wrap">
                      <Collapse
                        in={selectedPlan?.id === subscription?.id}
                        timeout="auto"
                        unmountOnExit
                      >
                        <Box
                          className="plan-details-section"
                          dangerouslySetInnerHTML={{
                            __html: subscription?.subs_plan_content,
                          }}
                        >
                          {/* {planFeatures?.map((feature, index) => (
                            <Box
                              className="detail-item d-flex align-center"
                              key={index}
                            >
                              <CheckCircleOutlineIcon className="check-icon" />
                              <Typography component="p" className="title-text">
                                {feature?.description}
                              </Typography>
                            </Box>
                          ))} */}
                        </Box>
                      </Collapse>
                    </Box>
                  </Box>
                ))}
              </Box>
            </>
          ))}
        </Box>
      ) : (
        <Box className="no-data-found mt32">
          <Typography className="no-data-text">No data found</Typography>
        </Box>
      )}
    </Box>
  );
};

export default PricingCards;
