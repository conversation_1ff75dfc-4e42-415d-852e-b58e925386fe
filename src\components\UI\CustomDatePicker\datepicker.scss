@import '../../../styles/variable.scss';

.custom-datepicker-wrapper {
  .field-label {
    font-family: var(--font-family-primary);
    margin-bottom: var(--spacing-xxs);
    font-weight: var(--font-weight-medium);
    color: var(--text-bright-blue);
    &.error-label {
      color: var(--text-error);
    }

    .required {
      color: var(--text-error);
      margin-left: var(--spacing-xxs);
    }
  }
  &.Mui-error {
    & .MuiOutlinedInput-notchedOutline {
      border-color: var(--border-color-error);
      &:hover {
        border-color: var(--border-color-error) !important;
      }
    }
    .MuiInputAdornment-root {
      .MuiSvgIcon-root {
        fill: var(--icon-bold-red-color) !important;
      }
    }
  }
  .date-field-error-text {
    font-family: var(--font-family-primary);
    color: var(--text-error);
    margin-top: var(--spacing-xxs);
    font-size: var(--font-size-xs);
    line-height: var(--line-height-xs);
  }
}
