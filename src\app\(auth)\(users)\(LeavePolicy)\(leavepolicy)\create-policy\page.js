'use client';

import React from 'react';
import { Box, Typography } from '@mui/material';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import { useRouter } from 'next/navigation';
import AddEditPolicy from '@/components/Leave/LeavePolicy/AddEditPolicy';
import _ from 'lodash';
import '../leave-policy/leavepolicy.scss';

export default function CreateEMPContract() {
  const router = useRouter();

  return (
    <>
      <Box className="page-container">
        <Box className="create-policy">
          <Box className="d-flex align-center">
            <ArrowBackIosIcon
              className="cursor-pointer"
              onClick={() => {
                router.push('/leave-policy');
              }}
            />
            <Typography className="p16 fw600 pr8">
              Create Leave Policy
            </Typography>
          </Box>
          <AddEditPolicy isEdit={false} />
        </Box>
      </Box>
    </>
  );
}
