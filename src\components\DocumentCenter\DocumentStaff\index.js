'use client';

import React, { useContext, useEffect, useState, useRef } from 'react';
import {
  Box,
  Typography,
  CircularProgress,
  Tooltip,
  Menu,
  MenuItem,
  ListItemIcon,
} from '@mui/material';
import { useRouter, useSearchParams } from 'next/navigation';
import AuthContext from '@/helper/authcontext';
import Searchbar from '@/components/UI/CustomSearch';
import {
  GridIcon,
  MenuIcon,
  FolderDocHomeGridIcon,
  RotateIcon,
} from '@/helper/common/images';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import FlipToFrontIcon from '@mui/icons-material/FlipToFront';
import CancelIcon from '@mui/icons-material/Cancel';
import DriveFileRenameOutlineIcon from '@mui/icons-material/DriveFileRenameOutline';
import CustomSelect from '@/components/UI/CustomSelect';
import TuneIcon from '@mui/icons-material/Tune';
import { ListManager } from 'react-beautiful-dnd-grid';
import MoveItems from '@/components/FolderViews/MoveItems';
import DialogBox from '@/components/UI/Modalbox';
import FolderGridViews from '@/components/FolderViews/FolderGridViewsD';
import FolderListViews from '@/components/FolderViews/FolderListViewsD';
import FolderUserTrack from '@/components/FolderViews/FolderUserTrack';
import FolderSideBar from '@/components/FolderViews/FolderSidebarD';
import CustomButton from '@/components/UI/CustomButton';
import { identifiers } from '@/helper/constants/identifier';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { staticOptions } from '@/helper/common/staticOptions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { fetchFromStorage, removeFromStorage } from '@/helper/context/storage';
import { URLS } from '@/helper/constants/urls';
import FileDetails from '@/components/FolderViews/Filedetails';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import ResetTrack from './ResetTrack';
import _ from 'lodash';
import NoDataView from '@/components/UI/NoDataView';
import CustomTabs from '@/components/UI/CustomTabs';
import '@/app/(auth)/(users)/(EmpContract)/empcontracts/empcontracts.scss';
import '../documentcenter.scss';

export default function DocumentCenterStaff({ params }) {
  const router = useRouter();
  const Id = params?.id;
  const searchParams = useSearchParams();
  const isDetails = searchParams.get('details');
  const CatId = searchParams.get('subId');
  const { userdata, setUserdata, folderdata, setfolderdata, authState } =
    useContext(AuthContext);
  const [DocumentList, setDocumentList] = useState({ isBlank: true });
  const [loader, setLoader] = useState(false);
  const [isViewType, setViewType] = useState('grid');
  const [selectedSideBarData, setSelectedSideBarData] = useState('');
  const [searchValue, setSearchValue] = useState('');
  const [documentMemory, setDocumentMemory] = useState();
  const [random, setRandom] = useState('');
  const divRef = useRef(null);
  const [width, setWidth] = useState(0);
  const [breadcrumb, setBreadcrumb] = useState([]);
  const [openMenu, setOpenMenu] = useState({});
  const [createModal, setCreateModal] = useState(false);
  const [createModalTitle, setCreateModalTitle] = useState('');
  const [branchList, setBranchList] = useState([]);
  const [branchListTrack, setBranchListTrack] = useState([]);
  const [departmentList, setDepartmentList] = useState([]);
  const [departmentListTrack, setDepartmentListTrack] = useState([]);
  const [activeTab, setActiveTab] = useState(1);
  const [filter, setFilter] = useState(false);
  const [selectedCat, setSelectedCat] = useState([]);
  const [CategoryDetails, setCategoryDetails] = useState('');
  const [toggleModal, setToggleModal] = useState(false);
  const [staffList, setStaffList] = useState([]);
  const [filterData, setFilterData] = useState({
    branch: '',
    categorytype: '',
    department: '',
    status: '',
  });
  const [filterDataApplied, setFilterDataApplied] = useState({
    branch: '',
    categorytype: '',
    department: '',
    status: '',
  });
  const [userList, setUserList] = useState([]);
  const [page, setPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [selected, setSelected] = useState();
  const [DocumentListFile, setDocumentListFile] = useState([]);
  const [currentHSIndex, setCurrentHSIndex] = useState(0);
  const [filesId, setFilesId] = useState([]);
  const isFilter =
    searchValue !== '' ||
    filterDataApplied?.branch !== '' ||
    filterDataApplied?.categorytype !== '' ||
    filterDataApplied?.department !== '' ||
    filterDataApplied?.status !== '';
  const options1 = [
    { id: 1, name: 'Delete', icon: <DeleteOutlineIcon /> },
    { id: 2, name: 'Copy', icon: <ContentCopyIcon /> },
    { id: 3, name: 'Move', icon: <FlipToFrontIcon /> },
    { id: 4, name: 'Update', icon: <DriveFileRenameOutlineIcon /> },
  ];
  const [DocumentFolderList, setDocumentFolderList] = useState([]);
  const [DocumentFolderListTrack, setDocumentFolderListTrack] = useState([]);
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);
  const handleClick = (event) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };
  const document_tabs = [
    { id: 1, name: 'Manage document' },
    { id: 2, name: 'Statistics' },
  ];
  const handleTabChange = (newValue) => {
    setActiveTab(newValue);
  };
  const handleViewType = (type) => {
    setViewType(type);
  };
  const handleCloseDialog = () => {
    setCreateModal(false);
  };
  // Reorder Document media
  const ReorderDoctMedia = async (index, id) => {
    const requestData = {
      category_id: id,
      order: Number(index) + 1,
    };
    try {
      const { status } = await axiosInstance.post(
        URLS?.UPDATE_CAT_ITEM_ORDER,
        requestData
      );
      if (status === 200) {
        //
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const onDragEnd = (sourceIndex, destinationIndex) => {
    const newData = DocumentList;
    const [movedRow] = newData.splice(sourceIndex, 1);
    newData.splice(destinationIndex, 0, movedRow);
    const CuurectCat = DocumentList[destinationIndex];
    ReorderDoctMedia(destinationIndex, CuurectCat?.id);
    setDocumentList(newData);
    setRandom(Math.random());
  };
  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      getDocumentList(searchValue, filterDataApplied);
    }
  };
  // List of Documents
  const getDocumentList = async (search, filter) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_DOCUMENT_STAFF_LIST +
          `?search=${search}&page=${1}&size=${''}&branches=${
            filter?.branch
          }&departments=${filter?.department}&category_status=${
            filter?.status
          }&category_use=${filter?.categorytype}&parent_id=${
            Id && Id !== 'all' ? Id : ''
          }`
      );
      if (status === 200) {
        setLoader(false);
        setDocumentList(data?.data);
        // setDocumentMemory(data?.memory_calculation);
        const filesDoc =
          data?.data &&
          data?.data?.length > 0 &&
          data?.data?.filter((f) => f?.category_type !== 'folder');
        filesDoc && setDocumentListFile(filesDoc);
        if (
          (fetchFromStorage(identifiers?.RedirectData) &&
            fetchFromStorage(identifiers?.RedirectData)?.IsFromUser) ||
          (userdata && userdata?.IsFromUser)
        ) {
          // setDocumentMemory(data?.memory_calculation);
        } else {
          if (Id === 'all') {
            setSelected(CategoryDetails?.id);
            getStatisticsUserList(page, CategoryDetails?.id, null, true);
          } else {
            data?.data &&
              data?.data?.length > 0 &&
              data?.data?.[0]?.id !== undefined &&
              setSelected(data?.data?.[0]?.id);
            getStatisticsUserList(page, data?.data?.[0]?.id);
          }
        }
      }
    } catch (error) {
      setLoader(false);
      setDocumentList([]);

      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // Memory of Documents
  const getDocumentListHome = async () => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_DOCUMENT_STAFF_LIST +
          `?search=${''}&page=${1}&size=${''}&branches=${''}&departments=${''}&category_status=${''}&category_use=${''}&parent_id=${''}`
      );
      if (status === 200) {
        setLoader(false);
        setDocumentMemory(data?.memory_calculation);
        setfolderdata({ ...folderdata, memory: data?.memory_calculation });
      }
    } catch (error) {
      setLoader(false);
      setDocumentMemory('');
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  //List of staff
  const getStaffList = async () => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_USER_LIST + `?isAdmin=false`
      );

      if (status === 200) {
        setLoader(false);
        const alloption = [{ label: 'Select all', value: 'all' }];
        let filterUserList = data?.userList?.map((user) => ({
          label: user?.user_full_name,
          value: user?.id,
        }));
        let mergeList = _.concat(alloption, filterUserList);
        setStaffList(mergeList);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setLoader(false);
      setStaffList([]);
    }
  };
  // List of Documents Folder
  const getDocumentFolderList = async () => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_DOCUMENT_FOLDER_STAFF_LIST +
          `?search=${''}&page=${1}&size=${''}`
      );
      if (status === 200) {
        setLoader(false);
        setDocumentFolderList(data?.data);
        setfolderdata({ ...folderdata, staffFolder: data?.data });
      }
    } catch (error) {
      setLoader(false);
      setDocumentFolderList([]);
      setfolderdata({ ...folderdata, staffFolder: [] });
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // List of Documents Folder
  const getDocumentFolderListTrack = async (branch, dep) => {
    // setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_DOCUMENT_FOLDER_STAFF_LIST +
          `?search=${''}&page=${1}&size=${''}&departments=${
            dep && dep?.length > 0 ? dep?.toString() : ''
          }&branches=${branch && branch?.length > 0 ? branch?.toString() : ''}`
      );
      if (status === 200) {
        setLoader(false);
        setDocumentFolderListTrack(data?.data);
      }
    } catch (error) {
      setLoader(false);
      setDocumentFolderListTrack([]);

      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const onSaveTrack = () => {
    getStatisticsUserList(page, selected);
  };
  // List of Category details
  const getDocumentCategory = async (ID) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_ONE_CATEGORY + `${ID}`
      );
      if (status === 200) {
        setLoader(false);
        const branchList = data?.data?.branches?.map((i) => ({
          value: i?.id,
          label: i?.branch_name,
        }));
        const depList = data?.data?.departments?.map((i) => ({
          value: i?.id,
          label: i?.department_name,
        }));
        setCategoryDetails({
          ...data?.data,
          branches: branchList,
          departments: depList,
        });
      }
    } catch (error) {
      setLoader(false);
      setCategoryDetails();
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // Delete Document
  const deleteDocument = async (ids, isDetailsDelete) => {
    const requestData = {
      category_ids: ids,
    };
    try {
      setLoader(true);
      const { status, data } = await axiosInstance.post(
        URLS?.DELETE_DOCUMENT,
        requestData
      );
      if (status === 200) {
        if (data.status) {
          setApiMessage('success', data?.message);
          getDocumentList(searchValue, filterDataApplied);
          getDocumentFolderList();
          setSelectedCat([]);
          isDetailsDelete && router.push(`/document-staff/${Id}`);
        } else {
          setApiMessage('error', data?.message);
        }
      } else {
        setApiMessage('error', data?.message);
      }
      setLoader(false);
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // Copy Document media
  const CopyDoctMedia = async (moveId, id) => {
    const requestData = {
      category_ids: id,
      from_copy_id: moveId,
    };
    try {
      const { status } = await axiosInstance.post(
        URLS?.COPY_CATEGORY,
        requestData
      );
      if (status === 200) {
        handleCloseDialog();
        getDocumentList(searchValue, filterDataApplied);
        getDocumentFolderList();
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // Move Document media
  const MoveDoctMedia = async (moveId, id) => {
    const requestData = {
      to_move_id: id,
      from_move_id: moveId,
    };
    try {
      const { status } = await axiosInstance.post(
        URLS?.MOVE_CATEGORY,
        requestData
      );
      if (status === 200) {
        handleCloseDialog();
        getDocumentList(searchValue, filterDataApplied);
        getDocumentFolderList();
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // List of branches
  const getBranchList = async () => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_BRANCH_LIST +
          `?search=${''}&page=${1}&size=${''}&branchStatus=active`
      );
      if (status === 200) {
        setLoader(false);
        const alloption = [{ label: 'Select all', value: 'all' }];
        let filterUserList = data?.data?.map((user) => ({
          label: user?.branch_name,
          value: user?.id,
          color: user?.branch_color,
        }));
        let mergeList = _.concat(alloption, filterUserList);
        setBranchList(filterUserList);
        setBranchListTrack(mergeList);
      }
    } catch (error) {
      setLoader(false);
      setBranchList([]);
      setBranchListTrack([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // List of department
  const getDepartmentList = async () => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_DEPARTMENT_LIST +
          `?search=${''}&page=${1}&size=${''}&departmentStatus=active`
      );

      if (status === 200) {
        setLoader(false);
        const alloption = [{ label: 'Select all', value: 'all' }];
        let filterUserList = data?.data?.map((user) => ({
          label: user?.department_name,
          value: user?.id,
        }));
        let mergeList = _.concat(alloption, filterUserList);
        setDepartmentList(filterUserList);
        setDepartmentListTrack(mergeList);
      }
    } catch (error) {
      setLoader(false);
      setDepartmentList([]);
      setDepartmentListTrack([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // List of Document center track user
  const getStatisticsUserList = async (pageNo, category, Rpp, isParent) => {
    setLoader(true);
    const parentId = Id !== 'all' ? Id : '';
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_STATISTICS_USER_LIST +
          `?search=''&page=${pageNo}&size=${
            Rpp ? Rpp : rowsPerPage
          }&search_category_id=${
            isParent ? '' : category ? category : ''
          }&category_id=${
            isParent
              ? category && category !== 'all'
                ? category
                : ''
              : parentId
          }`
      );
      // URLS?.GET_STATISTICS_USER_LIST +
      //   `?search=''&page=${pageNo}&size=${
      //     Rpp ? Rpp : rowsPerPage
      //   }&search_category_id=${category ? category : ''}&category_id=${
      //     isParent ? '' : parentId
      //   }`;
      if (status === 200) {
        setUserList(data?.data);
        setTotalCount(data?.count);
        setUserdata();
        removeFromStorage(identifiers?.RedirectData);
        setLoader(false);
      }
    } catch (error) {
      setLoader(false);
      setUserList([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const onclickAction = (id) => {
    setAnchorEl(null);
    if (id === 1) {
      let deleteItem = [];
      deleteItem.push(CategoryDetails?.id);
      deleteDocument(deleteItem, true);
    } else if (id === 2 || id === 3) {
      setCreateModal(true);
      id === 2
        ? setCreateModalTitle({
            title: 'Copy Items',
            id: CategoryDetails?.id,
          })
        : id === 3
          ? setCreateModalTitle({
              title: 'Move Items',
              id: CategoryDetails?.id,
            })
          : setCreateModalTitle('');
    } else if (id === 4) {
      router.push(
        `/create-content?id=${Id}&UpdateId=${
          CategoryDetails?.id
        }&fromDetails=true&type=${
          CategoryDetails?.category_use
            ? CategoryDetails?.category_use
            : 'document'
        }`
      );
    }
  };
  useEffect(() => {
    if (
      authState?.UserPermission?.category === 1 ||
      authState?.UserPermission?.category === 2
    ) {
      setDocumentList(DocumentList);
    }
  }, [random]);
  useEffect(() => {
    if (
      authState?.UserPermission?.category === 1 ||
      authState?.UserPermission?.category === 2
    ) {
      getDepartmentList();
      getBranchList();
      getStaffList();
      getDocumentList(searchValue, filterDataApplied);
      if (folderdata && folderdata?.memory) {
        setDocumentMemory(folderdata?.memory);
      } else {
        getDocumentListHome();
      }

      if (
        folderdata &&
        folderdata?.staffFolder &&
        folderdata?.staffFolder?.length > 0
      ) {
        setDocumentFolderList(folderdata?.staffFolder);
      } else {
        getDocumentFolderList();
      }

      getDocumentFolderListTrack();
    }
  }, [authState?.UserPermission?.category]);
  useEffect(() => {
    if (divRef.current) {
      setWidth(divRef.current.getBoundingClientRect().width);
    }
  }, []);

  const findBreadcrumb = (arr, selectedId, res) => {
    for (const el of arr) {
      if (el?.id === Number(selectedId)) {
        res.unshift(el);
        return true;
      } else {
        let is_parent =
          el?.children && findBreadcrumb(el?.children, selectedId, res);
        if (is_parent) {
          res.unshift(el);
          return true;
        }
      }
    }
  };

  function findMenuById(menu, selectedId, parents = []) {
    for (const item of menu) {
      if (item?.id === Number(selectedId)) {
        const result = {};
        [...parents, item.category_name].forEach(
          (category_name) => (result[category_name] = true)
        );
        return result;
      }
      if (item.children) {
        const result = findMenuById(item.children, selectedId, [
          ...parents,
          item?.category_name,
        ]);
        if (result) return result;
      }
    }
    return null;
  }

  useEffect(() => {
    let res = [];

    DocumentFolderList &&
      DocumentFolderList?.length > 0 &&
      Id &&
      findBreadcrumb(DocumentFolderList, Id, res);
    res && res?.length > 0 && setBreadcrumb(res);
    const result =
      DocumentFolderList &&
      DocumentFolderList?.length > 0 &&
      findMenuById(DocumentFolderList, Id);
    result && setOpenMenu(result);
  }, [Id, DocumentFolderList]);
  useEffect(() => {
    if (isDetails && Id && Id !== 'all' && CatId) {
      getDocumentCategory(CatId);
    } else if (Id && Id !== 'all') {
      getDocumentCategory(Id);
    }
  }, [Id, CatId]);
  useEffect(() => {
    if (
      authState?.UserPermission?.category === 1 ||
      authState?.UserPermission?.category === 2
    ) {
      if (
        fetchFromStorage(identifiers?.RedirectData) &&
        fetchFromStorage(identifiers?.RedirectData)?.IsFromUser
      ) {
        const fdata = fetchFromStorage(identifiers?.RedirectData);
        setActiveTab(2);
        setPage(fdata?.page);
        setRowsPerPage(fdata?.rowsPerPage);
        setSelected(fdata?.selected);
        getStatisticsUserList(fdata?.page, fdata?.selected, fdata?.rowsPerPage);
      } else if (userdata && userdata?.IsFromUser) {
        const fdata = userdata;
        setActiveTab(2);
        setPage(fdata?.page);
        setRowsPerPage(fdata?.rowsPerPage);
        setSelected(fdata?.selected);
        getStatisticsUserList(fdata?.page, fdata?.selected, fdata?.rowsPerPage);
      }
    }
  }, [
    fetchFromStorage(identifiers?.RedirectData)?.IsFromUser,
    userdata?.IsFromUser,
  ]);
  useEffect(() => {
    if (DocumentList && DocumentList?.length > 0 && CatId) {
      const files = DocumentList?.filter((f) => f?.category_type !== 'folder');
      const fid = files && files?.map((f) => f?.id);
      fid && fid?.length > 0 ? setFilesId(fid) : setFilesId([]);
      CatId && setCurrentHSIndex(fid?.indexOf(Number(CatId)));
    }
  }, [CatId, DocumentList]);

  const getCurrentContent = () => {
    switch (activeTab) {
      case 1:
        return (
          <>
            {!isDetails && (
              <Box className="document-filter-view mb16">
                <Box className="document-filter">
                  <Searchbar
                    setSearchValue={setSearchValue}
                    searchValue={searchValue}
                    onKeyPress={handleKeyPress}
                  />

                  <CustomButton
                    variant="outlined"
                    isIconOnly
                    startIcon={
                      <Tooltip
                        title={
                          <Typography className="sub-title-text">
                            Apply Filter
                          </Typography>
                        }
                        classes={{
                          tooltip: 'info-tooltip-container',
                        }}
                        arrow
                      >
                        <TuneIcon />
                      </Tooltip>
                    }
                    onClick={() => {
                      setFilter(!filter);
                    }}
                  />
                  <CustomButton
                    variant="contained"
                    title="Apply filter"
                    fullWidth={false}
                    onClick={() => {
                      getDocumentList(searchValue, filterDataApplied);
                    }}
                  />
                </Box>

                <Box className="d-flex justify-start align-center gap-sm grid-list-view-btn">
                  <CustomButton
                    variant={isViewType === 'grid' ? 'contained' : 'outlined'}
                    isIconOnly
                    startIcon={
                      <Tooltip
                        title={
                          <Typography className="sub-title-text">
                            Grid View
                          </Typography>
                        }
                        classes={{
                          tooltip: 'info-tooltip-container',
                        }}
                        arrow
                      >
                        <GridIcon />
                      </Tooltip>
                    }
                    onClick={() => {
                      handleViewType('grid');
                    }}
                  />

                  <CustomButton
                    variant={isViewType === 'list' ? 'contained' : 'outlined'}
                    isIconOnly
                    startIcon={
                      <Tooltip
                        title={
                          <Typography className="sub-title-text">
                            List View
                          </Typography>
                        }
                        classes={{
                          tooltip: 'info-tooltip-container',
                        }}
                        arrow
                      >
                        <MenuIcon />
                      </Tooltip>
                    }
                    onClick={() => {
                      handleViewType('list');
                    }}
                  />
                </Box>
              </Box>
            )}

            {selectedCat && selectedCat?.length > 0 && (
              <Box className="document-all-action">
                <Box className="document-action-tooltip">
                  <Box className="action-section selected-category">
                    <CancelIcon
                      onClick={() => {
                        setSelectedCat([]);
                      }}
                    />
                    <Typography className="body-text">
                      {selectedCat?.length + ' selected'}
                    </Typography>
                  </Box>
                  <Box className="action-section">
                    <Tooltip title="Delete">
                      <DeleteOutlineIcon
                        onClick={() => deleteDocument(selectedCat)}
                      />
                    </Tooltip>
                  </Box>
                  <Box className="action-section">
                    <Tooltip title="Copy">
                      <ContentCopyIcon
                        className="svg-icon"
                        onClick={() => {
                          setCreateModal(true);
                          setCreateModalTitle({
                            title: 'Copy Items',
                            id: selectedCat,
                          });
                        }}
                      />
                    </Tooltip>
                  </Box>
                </Box>
              </Box>
            )}

            {isDetails ? (
              <FileDetails
                CategoryDetails={CategoryDetails}
                DocumentList={DocumentListFile}
                getDocumentCategory={getDocumentCategory}
                CatId={CatId}
                setCurrentHSIndex={setCurrentHSIndex}
                currentHSIndex={currentHSIndex}
                filesId={filesId}
                Id={Id}
                isStaffView={true}
              />
            ) : DocumentList && DocumentList?.isBlank ? (
              <Box className="content-loader">
                <CircularProgress className="loader" color="inherit" />
              </Box>
            ) : DocumentList && DocumentList?.length === 0 ? (
              <Box className="">
                <Box className="no-data d-flex align-center justify-center">
                  <NoDataView
                    title="No Document Data Found"
                    description="There is no document data available at the moment."
                  />
                </Box>
              </Box>
            ) : isViewType === 'list' ? (
              <FolderListViews
                folderData={DocumentList}
                menuList={options1}
                setCreateModal={setCreateModal}
                setCreateModalTitle={setCreateModalTitle}
                setEmpContractList={setDocumentList}
                selectedCat={selectedCat}
                setSelectedCat={setSelectedCat}
                deleteDocument={deleteDocument}
                CategoryDetails={CategoryDetails}
                ReorderDoctMedia={ReorderDoctMedia}
                Id={Id}
                isFilter={isFilter}
              />
            ) : (
              <Box>
                {isFilter ? (
                  <Box>
                    <Box className={'emp-contract-folder-sec'}>
                      {DocumentList &&
                        DocumentList?.map((item) => (
                          <Box
                            key={item?.id}
                            className="emp-contract-item"
                            ref={divRef}
                            style={{ minWidth: `${width}px` }}
                          >
                            <FolderGridViews
                              folderData={item}
                              menuList={options1}
                              DocumentFolderList={DocumentFolderList}
                              setCreateModal={setCreateModal}
                              setCreateModalTitle={setCreateModalTitle}
                              selectedCat={selectedCat}
                              setSelectedCat={setSelectedCat}
                              deleteDocument={deleteDocument}
                              CategoryDetails={CategoryDetails}
                              Id={Id}
                            />
                          </Box>
                        ))}
                    </Box>
                  </Box>
                ) : (
                  <Box
                    className={
                      createModal
                        ? 'disable-drag emp-contract-folder-sec'
                        : 'emp-contract-folder-sec'
                    }
                  >
                    <ListManager
                      items={DocumentList || []}
                      direction="horizontal"
                      maxItems={1}
                      isDragDisabled={createModal}
                      render={(item) => (
                        <Box
                          key={item?.id}
                          className="emp-contract-item"
                          ref={divRef}
                          style={{ minWidth: `${width}px` }}
                        >
                          <FolderGridViews
                            folderData={item}
                            menuList={options1}
                            DocumentFolderList={DocumentFolderList}
                            setCreateModal={setCreateModal}
                            setCreateModalTitle={setCreateModalTitle}
                            selectedCat={selectedCat}
                            setSelectedCat={setSelectedCat}
                            deleteDocument={deleteDocument}
                            CategoryDetails={CategoryDetails}
                            Id={Id}
                          />
                        </Box>
                      )}
                      onDragEnd={createModal ? () => {} : onDragEnd}
                    />
                  </Box>
                )}
              </Box>
            )}
          </>
        );
      case 2:
        return (
          <FolderUserTrack
            folderData={DocumentList}
            userList={userList}
            selected={selected}
            rowsPerPage={rowsPerPage}
            setPage={setPage}
            page={page}
            totalCount={totalCount}
            setRowsPerPage={setRowsPerPage}
            setSelected={setSelected}
            getStatisticsUserList={getStatisticsUserList}
            Id={Id}
            CategoryDetails={CategoryDetails}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Box className="document-section-wrapper">
      <FolderSideBar
        DocumentFolderList={DocumentFolderList}
        Id={Id}
        setSelectedSideBarData={setSelectedSideBarData}
        selectedSideBarData={selectedSideBarData}
        setOpenMenu={setOpenMenu}
        openMenu={openMenu}
        CategoryDetails={CategoryDetails}
        documentMemory={documentMemory}
      />

      <Box className="folder-section-right document-center-section">
        <Box className="document-center">
          {loader ? (
            <Box className="content-loader">
              <CircularProgress className="loader" color="inherit" />
            </Box>
          ) : (
            <>
              <Box className="d-flex justify-space-between mb8">
                <Box className="custom-breadcrumbs">
                  {Id && Id !== 'all' && (
                    <Box className="breadcrumbs-menu cursor-pointer">
                      <Box
                        onClick={() => router.push('/document-staff/all')}
                        className="grid-icon home-grid-icon"
                      >
                        {FolderDocHomeGridIcon()}
                      </Box>
                    </Box>
                  )}

                  {Id &&
                    breadcrumb &&
                    breadcrumb?.length > 0 &&
                    breadcrumb?.map((item, i) => {
                      return (
                        <Box className="breadcrumbs-menu pointer" key={i}>
                          {i !== 0 && (
                            <ArrowForwardIosIcon className="separator-icon" />
                          )}

                          <Typography
                            className={
                              !isDetails && item?.id === Number(Id)
                                ? 'last-breadcrumb title-text'
                                : 'active-breadcrumb title-text'
                            }
                            id={`breadcrumb-${i}`}
                            onClick={() => {
                              if (item?.id !== Number(Id) || isDetails) {
                                router.push(`/document-staff/${item?.id}`);
                              }
                            }}
                          >
                            {item?.category_name}
                          </Typography>
                        </Box>
                      );
                    })}
                  {isDetails && CategoryDetails && (
                    <Box className="breadcrumbs-menu pointer">
                      <ArrowForwardIosIcon className="separator-icon" />
                      <Typography
                        className={'last-breadcrumb title-text'}
                        id={`breadcrumb`}
                        // onClick={() =>
                        //   item?.id !== Number(Id) &&
                        //   router.push(`/document-own/${item?.id}`)
                        // }
                      >
                        {CategoryDetails?.category_name}
                      </Typography>
                    </Box>
                  )}
                </Box>
                <Box className={isDetails ? 'category-details-action' : ''}>
                  {isDetails && (
                    <>
                      <Box className="d-flex justify-center">
                        <Box
                          onClick={handleClick}
                          className={`Category-actions cursor-pointer`}
                        >
                          <Tooltip
                            title={<Typography>Action</Typography>}
                            classes={{
                              tooltip: 'info-tooltip-container ',
                            }}
                          >
                            <MoreVertIcon className="select-action-icon" />
                          </Tooltip>
                        </Box>
                      </Box>
                      <Menu
                        id="long-menu"
                        className="Action-menu-popover"
                        anchorEl={anchorEl}
                        open={open}
                        onClose={handleClose}
                        sx={{
                          '& .MuiPaper-root': {
                            maxWidth: '448px',
                            minWidth: '180px',
                          },
                        }}
                      >
                        {options1?.map((option) => (
                          <>
                            {option?.id === 3 && Id === 'all' ? (
                              <></>
                            ) : (
                              <MenuItem
                                key={option?.id}
                                onClick={() => onclickAction(option?.id)}
                                className={option?.name}
                              >
                                <ListItemIcon>{option?.icon}</ListItemIcon>
                                {option?.name}
                              </MenuItem>
                            )}
                          </>
                        ))}
                      </Menu>
                    </>
                  )}
                  <Box className="d-flex justify-center ">
                    <Tooltip
                      title={<Typography>Reset track</Typography>}
                      arrow
                      className="action-tooltip"
                      classes={{
                        tooltip: 'info-tooltip-container ',
                      }}
                    >
                      <div
                        className="action-icon d-flex reset-icon cursor-pointer"
                        onClick={() => {
                          setToggleModal(true);
                        }}
                      >
                        <RotateIcon />
                      </div>
                    </Tooltip>
                  </Box>
                </Box>
              </Box>

              <CustomTabs
                tabs={document_tabs.map((tab) => ({
                  id: tab.id,
                  label: tab.name,
                }))}
                initialTab={activeTab}
                onTabChange={handleTabChange}
              />

              <Box className="section-right-content pt32">
                {getCurrentContent()}
              </Box>
            </>
          )}
        </Box>
      </Box>
      <DialogBox
        open={createModal}
        handleClose={() => {
          handleCloseDialog();
        }}
        title={createModalTitle?.title}
        className="staff-dialogbox dialog-box-container"
        content={
          <>
            <MoveItems
              DocumentFolderList={DocumentFolderList}
              isMove={createModalTitle?.title === 'Move Items' ? true : false}
              handleCloseDialog={handleCloseDialog}
              createModalTitle={createModalTitle}
              getDocumentList={getDocumentList}
              getDocumentFolderList={getDocumentFolderList}
              searchValue={searchValue}
              filterDataApplied={filterDataApplied}
              CopyDoctMedia={CopyDoctMedia}
              MoveDoctMedia={MoveDoctMedia}
            />
          </>
        }
      />
      <DialogBox
        open={filter}
        handleClose={() => {
          setFilter(!filter);
        }}
        title={'Document filter'}
        className="small-dialog-box-container"
        content={
          <>
            <Box className="staff-filter">
              <Box className="pt8">
                {(authState?.UserPermission?.branch === 2 ||
                  authState?.UserPermission?.branch === 1) && (
                  <Box>
                    <CustomSelect
                      placeholder="Branch name"
                      options={branchList}
                      value={
                        branchList?.find((opt) => {
                          return opt?.value === filterData?.branch;
                        }) || ''
                      }
                      onChange={(e) => {
                        setFilterData({
                          ...filterData,
                          branch: e?.value || '',
                        });
                      }}
                      label={<span>Branch name</span>}
                    />
                  </Box>
                )}
                {(authState?.UserPermission?.department === 2 ||
                  authState?.UserPermission?.department === 1) && (
                  <Box className="pt8">
                    <CustomSelect
                      placeholder="Department name"
                      options={departmentList}
                      value={
                        departmentList?.find((opt) => {
                          return opt?.value === filterData?.department;
                        }) || ''
                      }
                      onChange={(e) => {
                        setFilterData({
                          ...filterData,
                          department: e?.value || '',
                        });
                      }}
                      label={<span>Department name</span>}
                    />
                  </Box>
                )}
                <Box className="pt8">
                  <CustomSelect
                    placeholder="Content type"
                    options={staticOptions?.CONTENT_TYPE}
                    value={
                      staticOptions?.CONTENT_TYPE?.find((opt) => {
                        return opt?.value === filterData?.categorytype;
                      }) || ''
                    }
                    onChange={(e) => {
                      setFilterData({
                        ...filterData,
                        categorytype: e?.value || '',
                      });
                    }}
                    label={<span>Content type</span>}
                  />
                </Box>

                <Box className="pt8">
                  <CustomSelect
                    placeholder="Status"
                    options={staticOptions?.STATUS} //STATUS //STATUSDELETED
                    value={
                      staticOptions?.STATUS?.find((opt) => {
                        return opt?.value === filterData?.status;
                      }) || ''
                    }
                    onChange={(e) => {
                      setFilterData({
                        ...filterData,
                        status: e?.value || '',
                      });
                    }}
                    label={<span>Status</span>}
                  />
                </Box>
                <Box className="form-actions-btn">
                  <CustomButton
                    fullWidth
                    variant="outlined"
                    title="Clear"
                    onClick={() => {
                      setFilter(false);
                      // setPage(1);
                      const clear = {
                        branch: '',
                        categorytype: '',
                        department: '',
                        status: '',
                      };
                      setFilterData(clear);
                      setFilterDataApplied(clear);
                      getDocumentList(searchValue, clear);
                    }}
                  />
                  <CustomButton
                    fullWidth
                    variant="contained"
                    title="Apply"
                    onClick={() => {
                      setFilter(false);
                      // setPage(1);
                      setFilterDataApplied({
                        branch: filterData?.branch,
                        categorytype: filterData?.categorytype,
                        department: filterData?.department,
                        status: filterData?.status,
                      });
                      getDocumentList(searchValue, filterData);
                    }}
                  />
                </Box>
              </Box>
            </Box>
          </>
        }
      />
      <DialogBox
        open={toggleModal}
        handleClose={() => {
          setToggleModal(!toggleModal);
        }}
        title="Reset Track"
        className="staff-dialogbox dialog-box-container"
        content={
          <ResetTrack
            // handleSendNotification={handleSendNotification}
            isLoader={loader}
            staffList={staffList}
            branchList={branchListTrack}
            departmentList={departmentListTrack}
            // roleList={roleList}
            setToggleModal={setToggleModal}
            setStaffList={setStaffList}
            setBranchList={setBranchListTrack}
            setDepartmentList={setDepartmentListTrack}
            DocumentFolderList={DocumentFolderListTrack}
            getDocumentFolderListTrack={getDocumentFolderListTrack}
            onSaveTrack={onSaveTrack}
            // setRoleList={setRoleList}
          />
        }
      />
    </Box>
  );
}
