'use client';

import React, { useState, useEffect, useContext } from 'react';
import {
  Box,
  Typography,
  CircularProgress,
  Tooltip,
  InputLabel,
} from '@mui/material';
import AuthContext from '@/helper/authcontext';
import axiosInstance from '@/helper/axios/axiosInstance';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { URLS } from '@/helper/constants/urls';
import CustomButton from '@/components/UI/CustomButton';
import BudgetDetails from '@/components/Budget/BudgetDetails/index';
import RightDrawer from '@/components/UI/RightDrawer';
import CreateUpdateBudget from '@/components/Budget/CreateUpdateBudget/index';
import AssignBudget from '@/components/Budget/AssignBudget/index';
import DeletedBudget from '@/components/Budget/DeletedBudget';
import CustomSelect from '@/components/UI/CustomSelect';
import ToggleOffIcon from '@mui/icons-material/ToggleOff';
import ToggleOnIcon from '@mui/icons-material/ToggleOn';
import MultipleFilter from '@/components/UI/MultipleFilter';
import CategoryFilter from '@/components/DSR/Reports/CategoryFilter';
import ReactYearRangePicker from '@/components/UI/ReactYearRangePicker';
import { identifiers } from '@/helper/constants/identifier';
import { staticOptions } from '@/helper/common/staticOptions';
import moment from 'moment';
import CustomTabs from '@/components/UI/CustomTabs';
import NoDataView from '@/components/UI/NoDataView';
import './budget.scss';

export default function BudgetForecast() {
  const { authState, setIsDrawer, AllListsData } = useContext(AuthContext);
  const [loader, setLoader] = useState(false);
  const [dsrCatData, setDsrCatData] = useState([]);
  const [dsrData, setDsrData] = useState([]);
  const [openDrawer, setOpenDrawer] = useState(false);
  const [openAssignDrawer, setAssignOpenDrawer] = useState(false);
  const [view] = useState('table');
  const [selectedMainCategories, setSelectedMainCategories] = useState([]);
  const [selectedSubcategories, setSelectedSubcategories] = useState([]);
  const [ActiveCate, setActiveCate] = useState(false);
  const [staffList, setStaffList] = useState([]);

  const [BudgetData, setBudgetData] = useState([]);
  const [delBudgetData, setDelBudgetData] = useState([]);
  const [pageDel, setPageDel] = useState(1);
  const [RowsPerPageDel, setRowsPerPageDel] = useState(10);
  const [totalCountDel, setTotalCountDel] = useState(0);
  const [editBudget, setEditBudget] = useState();
  const [createBudgetBM, setCrateBudgetBM] = useState();
  const [previousBudget, setPreviousBudget] = useState();
  // const [BudgetByID, setBudgetByID] = useState();
  const [random, setRandom] = useState();
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [seprateBranch] = useState(true);
  function getStartMonth(financialMonth) {
    // Mapping month names to numbers
    const monthMap = {
      january: 1,
      february: 2,
      march: 3,
      april: 4,
      may: 5,
      june: 6,
      july: 7,
      august: 8,
      september: 9,
      october: 10,
      november: 11,
      december: 12,
    };

    // Extract start month name (before ' - ')
    const startMonthName = financialMonth?.toLowerCase().split(' - ')[0];

    // Get corresponding month number
    return monthMap[startMonthName] || null;
  }
  function getAcademicYear() {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1; // getMonth() is zero-based
    const [startYear] = authState?.generalSeetings?.financial_month
      ? authState?.generalSeetings?.financial_month.split(' - ')
      : 'april';
    const monthCount = getStartMonth(startYear);
    // If financial year starts in January, it follows the calendar year
    if (monthCount === 1) {
      return `${currentYear}-${currentYear + 1}`;
    }
    // If the current month is before the financial start month, previous year is the start
    if (currentMonth < monthCount) {
      return `${currentYear - 1}-${currentYear}`;
    } else {
      return `${currentYear}-${currentYear + 1}`;
    }
  }
  function getAcademicYearDefault() {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1; // getMonth() is zero-based
    const [startYear] = authState?.generalSeetings?.financial_month
      ? authState?.generalSeetings?.financial_month.split(' - ')
      : 'april';
    const monthCount = getStartMonth(startYear);
    // If financial year starts in January, it follows the calendar year
    if (monthCount === 1) {
      return `${currentYear}`;
    }
    // If the current month is before the financial start month, previous year is the start
    if (currentMonth < monthCount) {
      return `${currentYear - 1}`;
    } else {
      return `${currentYear}`;
    }
  }
  const [FilterBudget, setFilterBudget] = useState({
    Year: getAcademicYearDefault(),
    ForecastYear: getAcademicYear(),
    Branches: [],
    selectedMainCategories: [],
    selectedSubcategories: [],
    status: '',
    // status: 'approved',
  });
  const [FilterBudgetStored, setFilterBudgetStored] = useState({
    Year: getAcademicYearDefault(),
    ForecastYear: getAcademicYear(),
    Branches: [],
    selectedMainCategories: [],
    selectedSubcategories: [],
    status: '',
    // status: 'approved',
  });
  const [tab, setTab] = useState(1);
  const IsBranchManger =
    authState?.web_user_active_role_id === 7 ||
    authState?.web_user_active_role_id === 14;
  const IsForecastYearJanuary =
    authState?.generalSeetings?.financial_month === 'january - december';
  const IsAccountant = authState?.web_user_active_role_id === 6;
  const reports_tabs = [
    { id: 1, label: 'Active Forecasts' },
    { id: 2, label: 'Deleted Forecasts' },
  ];
  const handleTabChange = (newValue) => {
    setTab(newValue);
  };
  const handleOpenDrawer = () => {
    setOpenDrawer(true);
    setIsDrawer(true);
  };
  const handleOpenDrawerAssign = () => {
    setAssignOpenDrawer(true);
    setIsDrawer(true);
  };
  const handleCloseDrawer = () => {
    setOpenDrawer(false);
    setIsDrawer(false);
    setTimeout(() => {
      setEditBudget();
    }, 500);
  };
  const handleCloseDrawerAssign = () => {
    setAssignOpenDrawer(false);
    setIsDrawer(false);
    setTimeout(() => {
      setEditBudget();
    }, 500);
  };
  const formatCategoryList = (data) => {
    const catmodified = data?.map((paymentType) => ({
      ...paymentType,
      catList:
        paymentType?.payment_type_category?.map((category) => ({
          ...category,
          id: category?.payment_type_category_id,
          catList:
            category?.categoryBranchValue?.map((branchValue) => ({
              ...branchValue,
              id: branchValue?.payment_type_category_branch_id,
            })) || [],
        })) || [],
    }));
    return catmodified;
  };
  // Fetches the categories and payment type list
  const getCategorieList = async (cateActive) => {
    // setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        `${URLS.GET_FILTER_CATEGORY_LIST}?include_inactive=${cateActive}&report_filter_type=general`
      );
      if (status === 200) {
        // setLoader(false);
        const catmodified = data?.data?.map((paymentType) => ({
          ...paymentType,
          catList:
            paymentType?.payment_type_category?.map((category) => ({
              ...category,
              id: category?.payment_type_category_id,
              catList:
                category?.categoryBranchValue?.map((branchValue) => ({
                  ...branchValue,
                  id: branchValue?.payment_type_category_branch_id,
                })) || [],
            })) || [],
        }));
        const filterCate = catmodified?.filter((f) => f?.id !== -1);
        setDsrCatData(filterCate);
      }
    } catch (error) {
      // setLoader(false);
      setDsrCatData([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const getCategorieListCreate = async (branch, isEdit, isOpen) => {
    // setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        `${URLS.GET_FORECAST_CAT_LIST}?branch_id=${
          isEdit ? branch : branch.join(',')
        }`
      );
      if (status === 200) {
        // setLoader(false);
        setDsrData({
          income: formatCategoryList(data?.category_list?.income),
          other: formatCategoryList(data?.category_list?.other),
          expense: formatCategoryList(data?.category_list?.expense),
        });
        isOpen && handleOpenDrawer();
      }
    } catch (error) {
      // setLoader(false);
      setDsrData([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  //List of BranchMangers
  const getStaffList = async (branch, isEdit) => {
    // setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_USER_LIST +
          `?isAdmin=false&branch_id=${isEdit ? branch : branch}&role_id=7,14`
      );

      if (status === 200) {
        setLoader(false);
        // const alloption = [{ label: 'Select all', value: 'all' }];
        let filterUserList = data?.userList?.map((user) => ({
          label: user?.user_full_name,
          value: user?.id,
        }));
        // let mergeList = _.concat(alloption, filterUserList);
        setStaffList(filterUserList);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setLoader(false);
      setStaffList([]);
    }
  };
  const calculatePastOrFuture = (data, forecastYear, value) => {
    const [firstYear] = forecastYear.split('-').map(Number);
    const [firstYearCurrent] = getAcademicYear().split('-').map(Number);
    if (
      forecastYear === getAcademicYear() ||
      value?.group_type === 'Total' ||
      firstYearCurrent < firstYear
    ) {
      return { ...value, isPast: false };
    } else {
      return { ...value, isPast: true };
    }
  };
  // Fetches the budget list
  const getOneBudgetList = async (branch, fyear, filter, finfex) => {
    const filterPass = `branch_id=${branch}&forecast_year=${
      fyear
    }&payment_type_category_id=${
      filter?.selectedSubcategories && filter?.selectedSubcategories?.length > 0
        ? filter?.selectedSubcategories?.toString()
        : ''
    }&view_type=0&search=&page=1&size=1`;
    try {
      const { status, data } = await axiosInstance.get(
        `${URLS.GET_FORECAST_LIST}?${filterPass}`
      );
      if (status === 200) {
        const budgetdata =
          data?.data &&
          data?.data?.map((budget) => {
            if (budget?.forecast_bugdet_data) {
              const coulumnData = [
                ...(budget?.forecast_bugdet_data?.columns_group ?? []).map(
                  (row) =>
                    calculatePastOrFuture(row, budget?.forecast_year, row) //budget?.forecast_year
                ),
              ];
              return {
                ...budget,
                toggleout: 'prior',
                forecast_bugdet_data: {
                  ...budget?.forecast_bugdet_data,
                  columns_group: coulumnData,
                },
              };
            }
            return { ...budget, toggleout: 'prior' };
          });
        let budget = BudgetData;
        budget[finfex] = budgetdata?.[0];
        setBudgetData(budget);
        setRandom(Math.random());
        setLoader(false);
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const SaveBudgetDetails = async (datas, type, hindex) => {
    const forecastBudgetData = datas?.forecast_bugdet_data?.data?.map(
      (item) => {
        let rowValue;
        rowValue = {
          ...rowValue,
          payment_type_id: item?.payment_type_id,
          payment_type_category_id: item?.payment_type_category_id,
          forecast_category_type: item?.forecast_category_type,
          forecast_category_status: item?.forecast_category_status,
        };
        datas?.forecast_bugdet_data?.columns_group?.map((group) => {
          const month = group.content;
          if (group?.children === undefined && group?.key !== 'col1') {
            rowValue = {
              ...rowValue,
              bugdet_target_amount: item?.[group?.key] || 0,
            };
          }
          group?.children?.map((keyitem, findex) => {
            if (monthsMap[month] && findex === 1) {
              rowValue = {
                ...rowValue,
                [monthsMap[month]]: item?.[keyitem?.key] || 0,
              };
            }
          });
        });
        return rowValue;
      }
    );
    const filterBudgetCategoty = forecastBudgetData?.filter(
      (f) => f?.payment_type_id && f?.payment_type_category_id
    );
    const requestData = {
      forecast_id: datas?.id,
      forecast_budget_status: type,
      forecast_budget_data: filterBudgetCategoty,
    };
    try {
      // setLoader(true);
      const { status, data } = await axiosInstance.post(
        URLS.SAVE_FORCAST,
        requestData
      );
      if (status === 200) {
        if (data.status) {
          setApiMessage('success', data?.message);
          if (IsBranchManger) {
            getBudgetListBM(authState?.branch?.id, FilterBudgetStored, '');
          } else {
            // getBudgetList(
            //   authState?.assign_branch_ids,
            //   FilterBudgetStored,
            //   '',
            //   page
            // );
            getOneBudgetList(
              datas?.forecast_branch?.id,
              datas?.forecast_year,
              FilterBudgetStored,
              hindex
            );
          }
        } else {
          setApiMessage('error', data?.message);
        }
      } else {
        setApiMessage('error', data?.message);
      }
      setLoader(false);
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // Fetches the budget list
  const getBudgetList = async (branch, filter, search, pageNumber) => {
    //checked
    pageNumber === 1 && setLoader(true);
    if (branch) {
      // const viewType =
      //   checked !== undefined ? (checked ? 0 : 1) : seprateBranch ? 0 : 1;
    }
    // const viewType =
    //   checked !== undefined ? (checked ? 0 : 1) : seprateBranch ? 0 : 1;
    const filterPass = `branch_id=${
      filter?.Branches && filter?.Branches?.length > 0
        ? filter?.Branches?.toString()
        : ''
    }&forecast_year=${
      filter?.ForecastYear ? filter?.ForecastYear : ''
    }&payment_type_category_id=${
      filter?.selectedSubcategories && filter?.selectedSubcategories?.length > 0
        ? filter?.selectedSubcategories?.toString()
        : ''
    }&forecast_budget_status=${
      filter?.status ? filter?.status : ''
    }&view_type=0&search=${search}&page=${pageNumber}&size=${filter?.selectedSubcategories?.length === 0 ? 1 : filter?.selectedSubcategories?.length > 10 ? 1 : 5}`;
    try {
      const { status, data } = await axiosInstance.get(
        `${URLS.GET_FORECAST_LIST}?${filterPass}`
      );
      if (status === 200) {
        const budgetdata =
          data?.data &&
          data?.data?.map((budget) => {
            if (budget?.forecast_bugdet_data) {
              const coulumnData = [
                ...(budget?.forecast_bugdet_data?.columns_group ?? []).map(
                  (row) =>
                    calculatePastOrFuture(row, budget?.forecast_year, row) //budget?.forecast_year
                ),
              ];
              return {
                ...budget,
                toggleout: 'prior',
                forecast_bugdet_data: {
                  ...budget?.forecast_bugdet_data,
                  columns_group: coulumnData,
                },
              };
            }
            return { ...budget, toggleout: 'prior' };
          });
        if (pageNumber === 1) {
          setBudgetData(budgetdata);
        } else {
          setBudgetData((prevData) => [...prevData, ...budgetdata]);
        }
        setHasMore(data?.total_pages > data?.page);
        setTimeout(() => {
          setLoader(false);
        }, 1000);
      }
    } catch (error) {
      setLoader(false);
      // setBudgetData();
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const getBudgetListDeleted = async (branch, filter, pageNumber, rpp) => {
    pageNumber === 1 && setLoader(true);
    // const branchIds = branch && branch?.map((d) => d?.id);
    //  branchIds ? branchIds.join(',') :
    // const viewType =
    //   checked !== undefined ? (checked ? 0 : 1) : seprateBranch ? 0 : 1;
    const filterPass = `branch_id=${
      filter?.Branches && filter?.Branches?.length > 0
        ? filter?.Branches?.toString()
        : ''
    }&forecast_year=${
      filter?.ForecastYear ? filter?.ForecastYear : ''
    }&page=${pageNumber}&size=${rpp ? rpp : RowsPerPageDel}`;
    try {
      const { status, data } = await axiosInstance.get(
        `${URLS.GET_DELETE_FORECAST_LIST}?${filterPass}`
      );
      if (status === 200) {
        setDelBudgetData(data?.data);
        setTotalCountDel(data?.count);
        setTimeout(() => {
          setLoader(false);
        }, 1000);
      }
    } catch (error) {
      setLoader(false);
      // setDelBudgetData();
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const fetchMoreData = () => {
    setPage((prevPage) => prevPage + 1);
  };
  // Fetches the budget list
  const getBudgetListBM = async (branch, filter, search) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        `${URLS.GET_FORECAST_LIST}?branch_id=${branch.toString()}&payment_type_category_id=${
          filter?.selectedSubcategories &&
          filter?.selectedSubcategories?.length > 0
            ? filter?.selectedSubcategories?.toString()
            : ''
        }&forecast_budget_status=&view_type=0&forecast_year=${filter?.ForecastYear ? filter?.ForecastYear : ''}&search=${search}`
      );
      if (status === 200) {
        const budgetdata =
          data?.data &&
          data?.data?.map((budget) => {
            if (budget?.forecast_bugdet_data) {
              const coulumnData = [
                ...(budget?.forecast_bugdet_data?.columns_group ?? []).map(
                  (row) =>
                    calculatePastOrFuture(row, budget?.forecast_year, row) //budget?.forecast_year
                ),
              ];
              return {
                ...budget,
                toggleout: 'prior',
                forecast_bugdet_data: {
                  ...budget?.forecast_bugdet_data,
                  columns_group: coulumnData,
                },
              };
            }
            return budget;
          });
        // setBudgetData((prevData) => [...prevData, ...budgetdata]);
        setBudgetData(budgetdata);
        setTimeout(() => {
          setLoader(false);
        }, 1000);
      }
    } catch (error) {
      setLoader(false);
      setBudgetData([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // Fetches previous year budget list
  const getPreviousBudget = async (branch, year) => {
    // setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        `${URLS.GET_FORECAST_LIST}?branch_id=${branch}&forecast_year=${year}`
      );
      if (status === 200) {
        // setLoader(false);
        const budgetdata =
          data?.data &&
          data?.data?.map((budget) => {
            if (budget?.forecast_bugdet_data) {
              const coulumnData = [
                ...(budget?.forecast_bugdet_data?.columns_group ?? []).map(
                  (row) =>
                    calculatePastOrFuture(row, budget?.forecast_year, row) //budget?.forecast_year
                ),
              ];
              return {
                ...budget,
                toggleout: 'prior',
                forecast_bugdet_data: {
                  ...budget?.forecast_bugdet_data,
                  columns_group: coulumnData,
                },
              };
            }
            return budget;
          });
        budgetdata && budgetdata?.length > 0
          ? setPreviousBudget(budgetdata?.[0])
          : setPreviousBudget([]);
      }
    } catch (error) {
      // setLoader(false);
      setPreviousBudget();
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // Fetches the budget list by ID
  const getBudgetByID = async (id, type, index) => {
    // setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        `${URLS.GET_FORECAST_BY_ID}${id}`
      );
      if (status === 200) {
        setLoader(false);
        setEditBudget({ ...data?.data, index: index });
        if (type === 'edit') {
          getCategorieListCreate(data?.data?.branch?.id, true, true);
        } else {
          handleOpenDrawerAssign();
        }
      }
    } catch (error) {
      setLoader(false);
      setEditBudget();
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const LockForecast = async () => {
    //  I have removed id, index, and lock from params as they were defined but not being used. So whenever you use this function, please make sure to check that.
  };
  const DeleteForecast = async (id, index, type, datas) => {
    try {
      // setLoader(true);
      const { status, data } = await axiosInstance.delete(
        URLS.DELETE_FORCAST + `${id}`,
        {
          data: {
            forecast_budget_status: type, //revoked, deleted
          },
        }
      );
      if (status === 200) {
        // if (data.status) {
        setApiMessage('success', data?.message);
        if (type === 'deleted') {
          let budget = BudgetData;
          budget = budget?.filter((f, i) => i !== index);
          setBudgetData(budget);
          setRandom(Math.random());
        } else {
          getOneBudgetList(
            datas?.forecast_branch?.id,
            datas?.forecast_year,
            FilterBudgetStored,
            index
          );
        }
      } else {
        setApiMessage('error', data?.message);
      }
      setLoader(false);
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // Export Budget
  const getExportBudget = async (id, type, details) => {
    const filter = IsBranchManger
      ? `forecast_id=${id}&file_type=${type}`
      : seprateBranch
        ? `forecast_id=${id}&view_type=${0}&file_type=${type}`
        : `view_type=${1}&file_type=${type}&branch_id=${
            FilterBudget?.Branches && FilterBudget?.Branches?.length > 0
              ? FilterBudget?.Branches.join(',')
              : ''
          }&forecast_year=${
            FilterBudget?.ForecastYear ? FilterBudget?.ForecastYear : ''
          }`;
    try {
      const { status, data } = await axiosInstance.get(
        `${URLS.EXPORT_FORCAST}?${filter}`,
        {
          responseType: 'blob',
        }
      );
      if (status === 200) {
        // setLoader(false);
        const url = window.URL.createObjectURL(new Blob([data]));
        const link = document.createElement('a');
        link.href = url;
        var filename;
        filename =
          type === 'pdf'
            ? `${identifiers?.APP_NAME}_Forecast__${details?.forecast_year}_${details?.forecast_branch?.branch_name}.pdf`
            : type === 'csv'
              ? `${identifiers?.APP_NAME}_Forecast__${details?.forecast_year}_${details?.forecast_branch?.branch_name}.csv`
              : `${identifiers?.APP_NAME}_Forecast__${details?.forecast_year}_${details?.forecast_branch?.branch_name}.xlsx`;

        link.setAttribute('download', filename);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    } catch (error) {
      // setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const monthsMap = {
    April: 'april_amount',
    May: 'may_amount',
    June: 'june_amount',
    July: 'july_amount',
    August: 'august_amount',
    September: 'september_amount',
    October: 'october_amount',
    November: 'november_amount',
    December: 'december_amount',
    January: 'january_amount',
    February: 'february_amount',
    March: 'march_amount',
  };

  useEffect(() => {
    if (
      authState?.web_user_active_role_id &&
      authState?.web_user_active_role_id !== 7 &&
      authState?.web_user_active_role_id !== 14 &&
      page &&
      page !== 1
    ) {
      getBudgetList(authState?.assign_branch_ids, FilterBudgetStored, '', page);
    }
  }, [page]);
  useEffect(() => {
    if (
      authState?.web_user_active_role_id &&
      (authState?.UserPermission?.forecast === 1 ||
        authState?.UserPermission?.forecast === 2)
    ) {
      if (IsBranchManger) {
        getBudgetListBM(authState?.branch?.id, FilterBudgetStored, '');
      } else if (
        authState?.web_user_active_role_id !== 7 &&
        authState?.web_user_active_role_id !== 14
      ) {
        setPage(1);
        setPageDel(1);
        getBudgetList(authState?.assign_branch_ids, FilterBudgetStored, '', 1);
        getBudgetListDeleted(
          authState?.assign_branch_ids,
          FilterBudgetStored,
          1
        );
        // getBudgetChartList(authState?.assign_branch_ids, FilterBudget, '', 1);
      }
    }
  }, [authState?.UserPermission?.forecast, authState?.web_user_active_role_id]);
  useEffect(() => {
    if (
      AllListsData?.ActiveBranchList &&
      AllListsData?.ActiveBranchList?.length > 0 &&
      dsrData?.length === 0
    ) {
      const AllBranches = AllListsData?.ActiveBranchList?.map((b) => b?.value);
      getCategorieListCreate(AllBranches);
    }
  }, [AllListsData?.ActiveBranchList]);
  useEffect(() => {
    getCategorieList(false);
  }, []);

  const FinancialYear = (monthRange, year) => {
    if (!monthRange || !year) return null; // Handle undefined cases

    const months = monthRange
      .split(' - ')
      .map(
        (month) => month.charAt(0).toUpperCase() + month.slice(1).toLowerCase()
      );

    const [startMonth, endMonth] = months;
    const nextYear = Number(year) + 1;

    return (
      <>
        {startMonth} {year} - {endMonth} {nextYear}
      </>
    );
  };
  const FinancialYearJan = (monthRange, year) => {
    if (!monthRange || !year) return null; // Handle undefined cases

    const months = monthRange
      .split(' - ')
      .map(
        (month) => month.charAt(0).toUpperCase() + month.slice(1).toLowerCase()
      );

    const [startMonth, endMonth] = months;
    return (
      <>
        {startMonth} {year} - {endMonth} {year}
      </>
    );
  };

  const getCurrentContent = () => {
    switch (tab) {
      case 1:
        if (loader) {
          return (
            <Box
              className="Budget-details-section mt16"
              style={{
                height: 'calc(100vh - 300px - var(--banner-height))',
              }}
            >
              <Box className="content-loader">
                <CircularProgress className="loader" color="inherit" />
              </Box>
            </Box>
          );
        }

        if (!loader && BudgetData?.length === 0) {
          return (
            <Box
              className="Budget-details-section mt16 d-flex align-center justify-center"
              style={{
                height: 'calc(100vh - 300px - var(--banner-height))',
              }}
            >
              {/* <Typography className="text-align h6 color-gray">
                {IsBranchManger
                  ? 'No forecast was generated for this year.'
                  : 'No forecast was generated.'}
              </Typography> */}
              <Box className="no-data d-flex align-center justify-center">
                <NoDataView
                  title={
                    IsBranchManger
                      ? 'No forecast was generated for this year.'
                      : 'No forecast was generated.'
                  }
                  // description={
                  //   IsBranchManger
                  //     ? 'No forecast was deleted for this year.'
                  //     : 'No forecast was deleted.'
                  // }
                />
              </Box>
            </Box>
          );
        }

        return (
          <BudgetDetails
            BudgetData={BudgetData}
            setBudgetData={setBudgetData}
            setRandom={setRandom}
            random={random}
            getBudgetByID={getBudgetByID}
            getPreviousBudget={getPreviousBudget}
            previousBudget={previousBudget}
            fetchMoreData={fetchMoreData}
            hasMore={hasMore}
            LockForecast={LockForecast}
            SaveBudgetDetails={SaveBudgetDetails}
            getExportBudget={getExportBudget}
            seprateBranch={seprateBranch}
            DeleteForecast={DeleteForecast}
          />
        );

      case 2:
        if (loader) {
          return (
            <Box
              className="Budget-details-section mt16"
              style={{
                height: 'calc(100vh - 300px - var(--banner-height))',
              }}
            >
              <Box className="content-loader">
                <CircularProgress className="loader" color="inherit" />
              </Box>
            </Box>
          );
        }

        if (delBudgetData?.length === 0) {
          return (
            <Box
              className="Budget-details-section mt16 d-flex align-center justify-center"
              style={{
                height: 'calc(100vh - 300px - var(--banner-height))',
              }}
            >
              {/* <Typography className="text-align h6 color-gray"> */}
              {/* {IsBranchManger
                  ? 'No forecast was deleted for this year.'
                  : 'No forecast was deleted.'} */}
              <Box className="no-data d-flex align-center justify-center">
                <NoDataView
                  title={
                    IsBranchManger
                      ? 'No forecast was deleted for this year.'
                      : 'No forecast was deleted.'
                  }
                  // description={
                  //   IsBranchManger
                  //     ? 'No forecast was deleted for this year.'
                  //     : 'No forecast was deleted.'
                  // }
                />
              </Box>
            </Box>
          );
        }

        return (
          <DeletedBudget
            delBudgetData={delBudgetData}
            pageDel={pageDel}
            setPageDel={setPageDel}
            totalCountDel={totalCountDel}
            setTotalCountDel={setTotalCountDel}
            RowsPerPageDel={RowsPerPageDel}
            setRowsPerPageDel={setRowsPerPageDel}
            seprateBranch={seprateBranch}
            getBudgetListDeleted={getBudgetListDeleted}
            FilterBudgetStored={FilterBudgetStored}
          />
        );

      default:
        return null;
    }
  };
  return (
    <>
      <Box className="budget-section">
        {IsBranchManger ? (
          <>
            <Box
              className={
                authState?.UserPermission &&
                authState?.UserPermission?.forecast &&
                authState?.UserPermission?.forecast === 1
                  ? 'budget-permission budget-search'
                  : 'budget-search'
              }
            >
              <Box className="budget-50-filter">
                <Box className="budget-month-filter">
                  <Box className="budget-custom-date-fields ">
                    {IsForecastYearJanuary ? (
                      <ReactYearRangePicker
                        value={FilterBudget}
                        disableFuture={true}
                        onChange={(date) => {
                          setFilterBudget({
                            ...FilterBudget,
                            Year: date?.Year,
                            ForecastYear: `${date?.Year.getFullYear()}-${
                              date?.Year.getFullYear() + 1
                            }`,
                          });
                        }}
                        inputVariant="outlined"
                        isClearable={false}
                        placeholder="Select Year"
                        label="Choose Forecasting Year"
                        isSingleMonthpicker={true}
                        // maxDate={getEndOfCurrentWeek()}
                      />
                    ) : (
                      <ReactYearRangePicker
                        value={FilterBudget}
                        disableFuture={true}
                        onChange={(date) => {
                          setFilterBudget({
                            ...FilterBudget,
                            Year: date?.Year,
                            ForecastYear: `${date?.Year.getFullYear()}-${
                              date?.Year.getFullYear() + 1
                            }`,
                          });
                        }}
                        inputVariant="outlined"
                        isClearable={false}
                        placeholder="Select Year"
                        label="Choose Forecasting Year"
                        // maxDate={getEndOfCurrentWeek()}
                      />
                    )}
                  </Box>
                  <>
                    {' '}
                    {authState?.generalSeetings?.financial_month &&
                    FilterBudget?.Year ? (
                      <Typography className="year-range pt4">
                        {' '}
                        {IsForecastYearJanuary
                          ? FinancialYearJan(
                              authState?.generalSeetings?.financial_month,
                              moment(FilterBudget?.Year).format('yyyy')
                            )
                          : FinancialYear(
                              authState?.generalSeetings?.financial_month,
                              moment(FilterBudget?.Year).format('yyyy')
                            )}
                      </Typography>
                    ) : (
                      <></>
                    )}
                  </>
                </Box>

                {/* category Filter */}
                <Box className="category-section">
                  <CustomButton
                    variant="outlined"
                    isIconOnly
                    startIcon={
                      <Tooltip
                        title={
                          <Typography className="sub-title-text">
                            {ActiveCate
                              ? 'All Categories'
                              : 'Only Active Categories'}
                          </Typography>
                        }
                        classes={{
                          tooltip: 'info-tooltip-container',
                        }}
                        arrow
                      >
                        {ActiveCate ? <ToggleOnIcon /> : <ToggleOffIcon />}
                      </Tooltip>
                    }
                    onClick={() => {
                      if (ActiveCate) {
                        setActiveCate(false);
                        getCategorieList(false);
                        setSelectedSubcategories([]);
                        setSelectedMainCategories([]);
                      } else {
                        setActiveCate(true);
                        getCategorieList(true);
                        setSelectedSubcategories([]);
                        setSelectedMainCategories([]);
                      }
                    }}
                  />
                  {/* Category Filter */}
                  <CategoryFilter
                    selectedMainCategories={selectedMainCategories}
                    selectedSubcategories={selectedSubcategories}
                    dsrCatData={dsrCatData}
                    setSelectedMainCategories={setSelectedMainCategories}
                    setSelectedSubcategories={setSelectedSubcategories}
                  />
                </Box>
                <Box className="d-flex gap-5">
                  <CustomButton
                    variant="contained"
                    onClick={() => {
                      setFilterBudgetStored({
                        ...FilterBudget,
                        selectedMainCategories: selectedMainCategories,
                        selectedSubcategories: selectedSubcategories,
                      });
                      getBudgetListBM(
                        authState?.branch?.id,
                        {
                          ...FilterBudget,
                          selectedMainCategories: selectedMainCategories,
                          selectedSubcategories: selectedSubcategories,
                        },
                        '',
                        1
                      );
                    }}
                    title="Apply"
                  />
                  <CustomButton
                    variant="outlined"
                    onClick={() => {
                      const filter = {
                        Year: getAcademicYearDefault(),
                        ForecastYear: getAcademicYear(),
                        Branches: [],
                        selectedMainCategories: [],
                        selectedSubcategories: [],
                        status: '',
                      };
                      setSelectedSubcategories([]);
                      setSelectedMainCategories([]);
                      setFilterBudget(filter);
                      setFilterBudgetStored(filter);
                      setPage(1);
                      getBudgetListBM(authState?.branch?.id, filter, '', 1);
                    }}
                    title="Clear"
                  />
                </Box>
              </Box>
              {/* <Box className="d-flex"> */}
              {authState?.UserPermission?.forecast === 2 &&
                BudgetData &&
                BudgetData?.[0]?.forecast_status === 'assigned' &&
                BudgetData?.[0]?.ForecastAssignBudgets?.is_create_budget &&
                BudgetData?.[0]?.forecast_bugdet_data?.columnsArray?.length ===
                  0 &&
                !BudgetData?.[0]?.budget_created && (
                  <Box className="d-flex gap-sm">
                    <Box>
                      <CustomButton
                        variant="contained"
                        title="Create Budget"
                        fullWidth={false}
                        onClick={() => {
                          setCrateBudgetBM(BudgetData?.[0]);
                          handleOpenDrawer();
                        }}
                      />
                    </Box>
                  </Box>
                )}
            </Box>
          </>
        ) : (
          <>
            <Box
              className={
                authState?.UserPermission &&
                authState?.UserPermission?.forecast &&
                authState?.UserPermission?.forecast === 1
                  ? 'budget-permission budget-search'
                  : 'budget-search'
              }
            >
              <Box className="budget-50-filter">
                <Box className="budget-month-filter">
                  <Box className="budget-custom-date-fields ">
                    {IsForecastYearJanuary ? (
                      <ReactYearRangePicker
                        value={FilterBudget}
                        disableFuture={true}
                        onChange={(date) => {
                          setFilterBudget({
                            ...FilterBudget,
                            Year: date?.Year,
                            ForecastYear: `${date?.Year.getFullYear()}-${
                              date?.Year.getFullYear() + 1
                            }`,
                          });
                        }}
                        inputVariant="outlined"
                        isClearable={false}
                        placeholder="Select Year"
                        label="Choose Forecasting Year"
                        isSingleMonthpicker={true}
                        // maxDate={getEndOfCurrentWeek()}
                      />
                    ) : (
                      <ReactYearRangePicker
                        value={FilterBudget}
                        disableFuture={true}
                        onChange={(date) => {
                          setFilterBudget({
                            ...FilterBudget,
                            Year: date?.Year,
                            ForecastYear: `${date?.Year.getFullYear()}-${
                              date?.Year.getFullYear() + 1
                            }`,
                          });
                        }}
                        inputVariant="outlined"
                        isClearable={false}
                        placeholder="Select Year"
                        label="Choose Forecasting Year"
                        // maxDate={getEndOfCurrentWeek()}
                      />
                    )}
                  </Box>
                  <>
                    {authState?.generalSeetings?.financial_month &&
                    FilterBudget?.Year ? (
                      <Typography className="year-range pt4">
                        {' '}
                        {IsForecastYearJanuary
                          ? FinancialYearJan(
                              authState?.generalSeetings?.financial_month,
                              moment(FilterBudget?.Year).format('yyyy')
                            )
                          : FinancialYear(
                              authState?.generalSeetings?.financial_month,
                              moment(FilterBudget?.Year).format('yyyy')
                            )}
                      </Typography>
                    ) : (
                      <></>
                    )}
                  </>
                </Box>

                {/* Branch Filter */}
                <Box className="budget-branch-filter">
                  <InputLabel className="input-label">Branches</InputLabel>

                  <MultipleFilter
                    selected={FilterBudget?.Branches}
                    setSelected={(value) => {
                      setFilterBudget({ ...FilterBudget, Branches: value });
                    }}
                    List={AllListsData?.ActiveBranchList}
                    placeholder="Branches"
                  />
                </Box>

                {/* category Filter */}
                {tab !== 2 ? (
                  <Box className="category-section">
                    <CustomButton
                      variant="outlined"
                      isIconOnly
                      startIcon={
                        <Tooltip
                          title={
                            <Typography className="sub-title-text">
                              {ActiveCate
                                ? 'All Categories'
                                : 'Only Active Categories'}
                            </Typography>
                          }
                          classes={{
                            tooltip: 'info-tooltip-container',
                          }}
                          arrow
                        >
                          {ActiveCate ? (
                            <ToggleOnIcon
                              sx={{ fontWeight: 700, width: 24, height: 23 }}
                            />
                          ) : (
                            <ToggleOffIcon
                              sx={{ fontWeight: 700, width: 24, height: 23 }}
                            />
                          )}
                        </Tooltip>
                      }
                      onClick={() => {
                        if (ActiveCate) {
                          setActiveCate(false);
                          getCategorieList(false);
                          setSelectedSubcategories([]);
                          setSelectedMainCategories([]);
                        } else {
                          setActiveCate(true);
                          getCategorieList(true);
                          setSelectedSubcategories([]);
                          setSelectedMainCategories([]);
                        }
                      }}
                    />
                    {/* Category Filter */}
                    <CategoryFilter
                      selectedMainCategories={selectedMainCategories}
                      selectedSubcategories={selectedSubcategories}
                      dsrCatData={dsrCatData}
                      setSelectedMainCategories={setSelectedMainCategories}
                      setSelectedSubcategories={setSelectedSubcategories}
                    />
                  </Box>
                ) : (
                  <></>
                )}
                {tab !== 2 ? (
                  <Box>
                    <CustomSelect
                      placeholder="Status"
                      options={staticOptions?.FORECAST_OPTIONS}
                      value={
                        staticOptions?.FORECAST_OPTIONS?.find((opt) => {
                          return opt?.value === FilterBudget?.status;
                        }) || ''
                      }
                      onChange={(e) => {
                        // e.target.value === '' && setSeprateBranch(true);
                        setFilterBudget({
                          ...FilterBudget,
                          status: e?.value || '',
                        });
                      }}
                    />
                  </Box>
                ) : (
                  <></>
                )}
                <Box className="d-flex gap-5">
                  <CustomButton
                    variant="contained"
                    onClick={() => {
                      setFilterBudgetStored({
                        ...FilterBudget,
                        selectedMainCategories: selectedMainCategories,
                        selectedSubcategories: selectedSubcategories,
                      });
                      setPage(1);
                      setPageDel(1);
                      getBudgetList(
                        authState?.assign_branch_ids,
                        {
                          ...FilterBudget,
                          selectedMainCategories: selectedMainCategories,
                          selectedSubcategories: selectedSubcategories,
                        },
                        '',
                        1
                      );
                      getBudgetListDeleted(
                        authState?.assign_branch_ids,
                        {
                          ...FilterBudget,
                          selectedMainCategories: selectedMainCategories,
                          selectedSubcategories: selectedSubcategories,
                        },
                        1
                      );
                      // getBudgetChartList(
                      //   authState?.assign_branch_ids,
                      //   FilterBudget,
                      //   '',
                      //   1
                      // );
                    }}
                    title="Apply"
                  />
                  <CustomButton
                    variant="outlined"
                    onClick={() => {
                      const filter = {
                        Year: getAcademicYearDefault(),
                        ForecastYear: getAcademicYear(),
                        Branches: [],
                        selectedMainCategories: [],
                        selectedSubcategories: [],
                        status: '',
                      };
                      setSelectedSubcategories([]);
                      setSelectedMainCategories([]);
                      setFilterBudget(filter);
                      setFilterBudgetStored(filter);
                      getBudgetList(
                        authState?.assign_branch_ids,
                        filter,
                        '',
                        1
                      );
                      setPage(1);
                      setPageDel(1);
                      getBudgetListDeleted(
                        authState?.assign_branch_ids,
                        filter,
                        1
                      );
                      // getBudgetChartList(
                      //   authState?.assign_branch_ids,
                      //   filter,
                      //   '',
                      //   1
                      // );
                    }}
                    title="Clear"
                  />
                </Box>
              </Box>

              {/* <Box className="d-flex"> */}
              {authState?.UserPermission?.forecast === 2 &&
                tab !== 2 &&
                !IsAccountant && (
                  <Box className="d-flex gap-sm">
                    <Box>
                      <CustomButton
                        variant="outlined"
                        title="Assign Budget"
                        fullWidth={false}
                        onClick={() => {
                          handleOpenDrawerAssign();
                        }}
                      />
                    </Box>
                    <Box>
                      <CustomButton
                        variant="contained"
                        title="Create Budget"
                        fullWidth={false}
                        onClick={() => {
                          handleOpenDrawer();
                        }}
                      />
                    </Box>
                  </Box>
                )}
            </Box>
          </>
        )}

        {view === 'graph' ? (
          <></>
        ) : (
          <>
            {IsBranchManger ? (
              <>
                {loader ? (
                  <>
                    <Box
                      className="Budget-details-section mt16"
                      style={{
                        height: `calc(100vh - 250px - var(--banner-height))`,
                      }}
                    >
                      <Box className="content-loader">
                        <CircularProgress className="loader" color="inherit" />
                      </Box>
                    </Box>
                  </>
                ) : BudgetData?.length === 0 ? (
                  <>
                    <Box
                      className="Budget-details-section mt16 d-flex align-center justify-center"
                      style={{
                        height: 'calc(100vh - 250px - var(--banner-height))',
                      }}
                    >
                      {/* <Typography className="text-align h6 color-gray">
                          {'No forecast was generated for this year.'}
                        </Typography> */}
                      <Box className="no-data d-flex align-center justify-center">
                        <NoDataView
                          title={'No forecast was generated for this year.'}
                        />
                      </Box>
                    </Box>
                  </>
                ) : (BudgetData?.[0]?.forecast_status === 'active' &&
                    BudgetData?.[0]?.budget_created) ||
                  (BudgetData?.[0]?.forecast_status === 'assigned' &&
                    BudgetData?.[0]?.ForecastAssignBudgets?.user_id !==
                      authState?.id) ||
                  BudgetData?.[0]?.forecast_status === 'revoked' ||
                  (BudgetData?.[0]?.forecast_status === 'submitted' &&
                    BudgetData?.[0]?.ForecastAssignBudgets?.user_id !==
                      authState?.id) ||
                  (BudgetData?.[0]?.forecast_status === 'approved' &&
                    BudgetData?.[0]?.ForecastAssignBudgets?.user_id !==
                      authState?.id) ? (
                  <>
                    <Box
                      className="Budget-details-section mt16 d-flex align-center justify-center"
                      style={{
                        height: 'calc(100vh - 250px - var(--banner-height))',
                      }}
                    >
                      {/* <Typography className="text-align h6 color-gray">
                          {`The budget for this year has not been assign to you.`}{' '}
                        </Typography> */}
                      <Box className="no-data d-flex align-center justify-center">
                        <NoDataView
                          title={`The budget for this year has not been assign to you.`}
                        />
                      </Box>
                    </Box>
                  </>
                ) : BudgetData?.[0]?.forecast_status === 'assigned' &&
                  BudgetData?.[0]?.forecast_bugdet_data &&
                  BudgetData?.[0]?.forecast_bugdet_data?.columns_group
                    ?.length === 0 &&
                  BudgetData?.[0]?.ForecastAssignBudgets?.is_create_budget &&
                  !BudgetData?.[0]?.budget_created ? (
                  <>
                    <Box
                      className="Budget-details-section mt16 d-flex align-center justify-center"
                      style={{
                        height: 'calc(100vh - 250px - var(--banner-height))',
                      }}
                    >
                      {/* <Typography className="text-align h6 color-gray">
                          {`You've been assigned this year's budget. Click "Create Budget" to proceed.`}{' '}
                          //${BudgetData?.[0]?.forecast_year} 
                        </Typography> */}
                      <Box className="no-data d-flex align-center justify-center">
                        <NoDataView
                          title={`You've been assigned this year's budget. Click "Create Budget" to proceed.`}
                        />
                      </Box>
                    </Box>
                  </>
                ) : (
                  <BudgetDetails
                    BudgetData={BudgetData}
                    setBudgetData={setBudgetData}
                    setRandom={setRandom}
                    random={random}
                    getBudgetByID={getBudgetByID}
                    getPreviousBudget={getPreviousBudget}
                    previousBudget={previousBudget}
                    fetchMoreData={fetchMoreData}
                    hasMore={hasMore}
                    LockForecast={LockForecast}
                    SaveBudgetDetails={SaveBudgetDetails}
                    getExportBudget={getExportBudget}
                    seprateBranch={seprateBranch}
                    DeleteForecast={DeleteForecast}
                  />
                )}
              </>
            ) : (
              <>
                <Box className="section-right">
                  <Box className="section-right-tab-header">
                    <CustomTabs
                      tabs={reports_tabs}
                      initialTab={tab}
                      onTabChange={handleTabChange}
                      isSetup
                    />
                  </Box>
                  <Box className="section-right-content pt8">
                    {getCurrentContent()}
                  </Box>
                </Box>
              </>
            )}
          </>
        )}
      </Box>

      <RightDrawer
        anchor={'right'}
        open={openDrawer}
        onClose={handleCloseDrawer}
        title={editBudget ? 'Update Budget' : 'Create Budget'}
        className="Create-budget-drawer"
        content={
          <CreateUpdateBudget
            dsrCatData={dsrData}
            getCategorieList={getCategorieListCreate}
            handleCloseDrawer={handleCloseDrawer}
            editBudget={editBudget}
            getBudgetList={getBudgetList}
            getBudgetListBM={getBudgetListBM}
            FilterBudget={FilterBudgetStored}
            createBudgetBM={createBudgetBM}
            page={page}
            setPage={setPage}
            IsBranchManger={IsBranchManger}
            getOneBudgetList={getOneBudgetList}
          />
        }
      />
      <RightDrawer
        anchor={'right'}
        open={openAssignDrawer}
        onClose={handleCloseDrawerAssign}
        title={editBudget ? 'Assign Budget' : 'Assign Budget'}
        className="Create-budget-drawer"
        content={
          <AssignBudget
            // dsrCatData={dsrCatData}
            // getCategorieList={getCategorieList}
            handleCloseDrawer={handleCloseDrawerAssign}
            editBudget={editBudget}
            getBudgetList={getBudgetList}
            getOneBudgetList={getOneBudgetList}
            FilterBudget={FilterBudgetStored}
            page={page}
            getStaffList={getStaffList}
            staffList={staffList}
            setStaffList={setStaffList}
          />
        }
      />
    </>
  );
}
