@import '@/styles/variable.scss';

.stopwatch-wrap {
    border: 1px solid $color-Dark-10;

    .play-pause-btn-wrap {
        border-right: 1px solid $color-Dark-20;

        .play-pause-btn {
            border-radius: 0px;
            padding: 0px;
            line-height: 0px;
            padding: 2px;

            .MuiSvgIcon-root {
                color: $color-primary;
            }

            .pause-btn,
            .play-btn {
                width: 17px;
                height: 17px;
            }
        }
    }

    .stop-reset-btn-wrap {
        line-height: 0px;

        .stop-btn-wrap,
        .refresh-btn-wrap {
            border-radius: 0px;
            padding: 0px;
            line-height: 0px;
            padding: 2px;
        }

        .stop-btn-wrap {
            border-right: 1px solid $color-Dark-20;
            border-left: 1px solid $color-Dark-20;
        }

        .stop-icon {
            color: $color-Alert;
            padding: 0px;
            width: 17px;
            height: 17px;
        }

        .refresh-icon {
            color: $color-primary;
            padding: 0px;
            width: 17px;
            height: 17px;
        }



    }

    .time-wrap {
        line-height: 0px;

        .time-show-wrap {
            line-height: 0px;
            font-size: 14px;
            padding: 10px;
        }
    }
}