'use client';

import React, { useContext, useState } from 'react';
import { Box, Typography, Divider, Tooltip } from '@mui/material';
import AuthContext from '@/helper/authcontext';
import ProfileHeader from './UserHeader';
import DialogBox from '@/components/UI/Modalbox';
import UserGeneratePassword from '@/components/Users/<USER>/GeneratePassword';
import UserResetPin from '@/components/Users/<USER>/ResetPin';
import LockResetIcon from '@mui/icons-material/LockReset';
import { GeneratePIN } from '@/helper/common/images';
import moment from 'moment';
import WarningIcon from '@mui/icons-material/Warning';
import PersonalInfo from './PersonalInfo';
import EmpContract from './EmpContract';
import ChangeCircleIcon from '@mui/icons-material/ChangeCircle';

const calculateDaysDifference = (expirationDate) => {
  const today = moment().startOf('day');
  const expiration = moment(expirationDate).startOf('day');
  return expiration.diff(today, 'days');
};

const getContractExpirationMessage = (employeeName, expirationDate) => {
  const daysDifference = calculateDaysDifference(expirationDate);

  if (daysDifference > 0 && daysDifference <= 15) {
    return (
      <Box className={`equal-zero Rejected-details mb8`}>
        <WarningIcon />
        <Box className="rejected">
          <Typography className="p14 fw400">
            {`${employeeName}'s contract is set to`}{' '}
            <span className="fw600">{`expire in ${daysDifference} days `}</span>
            . Please review and take the necessary actions.
          </Typography>
        </Box>
      </Box>
    );
  } else if (daysDifference === 0) {
    return (
      <Box className={`less-zero Rejected-details mb8`}>
        <WarningIcon />
        <Box className="rejected">
          <Typography className="p14 fw400">
            {`${employeeName}'s contract `}{' '}
            <span className="fw600">{`expires today`}</span>. Ensure that all
            formalities are completed.
          </Typography>
        </Box>
      </Box>
    );
  } else if (daysDifference < 0) {
    return (
      <Box className={`less-zero Rejected-details mb8`}>
        <WarningIcon />
        <Box className="rejected">
          <Typography className="p14 fw400">
            {`${employeeName}'s contract `}
            <span className="fw600">
              {`expired ${Math.abs(daysDifference)} days ago`}
            </span>
            . Please confirm the employee's status and complete any remaining
            tasks.
          </Typography>
        </Box>
      </Box>
    );
  } else {
    return null;
  }
};

const ProfileDetails = ({
  profileImage,
  oldStaff,
  ViewAccessOnly,
  setProfileImage,
  UserDetails,
  UserId,
  setLoader,
  formikRef,
  formikRefcontract,
  generalTemplateData,
  roleList,
  isLoader,
  contractTypeData,
  leaveTypeList,
  departmentTemByID,
  departmentTemplateDataByID,
  departmentTemplateData,
  loader,
  onDragEnd,
  isLoaderEmp,
  ContractList,
  setContractList,
  getUserDetails,
  regenerateEmp,
  setIsLoader,
  handleCreateOptionContractPolicy,
  setDepartmentTemplateData,
  leaveTypeListall,
  setLeaveTypeList,
  ResendInvitation,
  isMyProfile,
  countries,
  counties,
  cities,
  setCities,
  setCounties,
  setSelectedCountry,
  setSelectedCounty,
}) => {
  const { authState } = useContext(AuthContext);
  const [PINPassword, setPINPassword] = useState('');
  const [PINPasswordTitle, setPINPasswordTitle] = useState('');
  const [Randompassword, setRandomPassword] = useState('');
  const [RandomPIN, setRandomPIN] = useState('');
  const [isReset, setIsReset] = useState(false);
  const [isResetPin, setIsResetPin] = useState(false);
  const [signValue, setSignValue] = useState(false);
  const [validationFeedback, setValidationFeedback] = useState({
    length: false,
    lowercase: false,
    uppercase: false,
    number: false,
    specialChar: false,
  });
  const handleClosePin = () => {
    setPINPassword(false);
    setPINPasswordTitle('');
  };
  const user_full_name = UserDetails
    ? [
        UserDetails?.user_first_name,
        UserDetails?.user_middle_name,
        UserDetails?.user_last_name,
      ]
        .filter(Boolean)
        .join(' ')
    : 'User name';
  return (
    <>
      <ProfileHeader
        profileImage={profileImage}
        oldStaff={oldStaff}
        ViewAccessOnly={ViewAccessOnly}
        setProfileImage={setProfileImage}
        UserDetails={UserDetails}
        UserId={UserId}
        user_full_name={user_full_name}
        ResendInvitation={ResendInvitation}
        isMyProfile={isMyProfile}
      />
      <Divider className="mb16 mt16" />
      {!isMyProfile && UserDetails?.user_contract?.id && (
        <>
          {getContractExpirationMessage(
            user_full_name,
            UserDetails?.user_contract?.expire_date
          )}
        </>
      )}

      <Box className="d-flex align-center justify-space-between flex-wrap gap-sm">
        <Box>
          <Typography className="title-sm pr8">User Profile </Typography>
        </Box>
        {isMyProfile && UserDetails?.pending_change_request_count ? (
          <Box className="d-flex align-center">
            <Tooltip
              title={<Typography>Pending Change Request</Typography>}
              classes={{
                tooltip: 'info-tooltip-container ',
              }}
            >
              <ChangeCircleIcon className="created-date cursor-pointer" />
            </Tooltip>
            <Typography className="sub-title-text fw400 user-date">
              {UserDetails?.pending_change_request_count} Request pending
            </Typography>
          </Box>
        ) : (
          ''
        )}
        {[1, 2, 3, 4].includes(authState?.web_user_active_role_id) &&
          !isMyProfile && (
            <Box className="Generate-password-link">
              <Box
                className="gen-password"
                onClick={() => {
                  setPINPassword(true);
                  setPINPasswordTitle('Generate Password');
                }}
              >
                <LockResetIcon />
                <Typography className="content-text cursor-pointer primary-link-text">
                  Generate Password
                </Typography>
              </Box>
              <Box
                className="gen-password"
                onClick={() => {
                  setPINPassword(true);
                  setPINPasswordTitle('Generate PIN');
                }}
              >
                {GeneratePIN()}
                <Typography className="content-text cursor-pointer primary-link-text">
                  Generate PIN
                </Typography>
              </Box>
            </Box>
          )}
      </Box>
      <Box className="user-edit-deatils">
        <PersonalInfo
          UserDetails={UserDetails}
          UserId={UserId}
          ViewAccessOnly={ViewAccessOnly}
          roleList={roleList}
          signValue={signValue}
          isLoader={isLoader}
          getUserDetails={getUserDetails}
          setIsLoader={setIsLoader}
          setProfileImage={setProfileImage}
          profileImage={profileImage}
          setSignValue={setSignValue}
          formikRef={formikRef}
          isMyProfile={isMyProfile}
          countries={countries}
          counties={counties}
          cities={cities}
          setCities={setCities}
          setCounties={setCounties}
          setSelectedCountry={setSelectedCountry}
          setSelectedCounty={setSelectedCounty}
        />
        {!isMyProfile &&
          !(
            (UserDetails?.web_user_active_role_id === 1 ||
              UserDetails?.web_user_active_role_id === 2) &&
            UserDetails?.user_roles?.length === 1
          ) && (
            <EmpContract
              UserDetails={UserDetails}
              UserId={UserId}
              formikRef={formikRefcontract}
              generalTemplateData={generalTemplateData}
              ViewAccessOnly={ViewAccessOnly}
              contractTypeData={contractTypeData}
              leaveTypeList={leaveTypeList}
              departmentTemByID={departmentTemByID}
              departmentTemplateDataByID={departmentTemplateDataByID}
              departmentTemplateData={departmentTemplateData}
              loader={loader}
              onDragEnd={onDragEnd}
              isLoader={isLoader}
              isLoaderEmp={isLoaderEmp}
              ContractList={ContractList}
              setContractList={setContractList}
              regenerateEmp={regenerateEmp}
              setIsLoader={setIsLoader}
              handleCreateOptionContractPolicy={
                handleCreateOptionContractPolicy
              }
              setDepartmentTemplateData={setDepartmentTemplateData}
              leaveTypeListall={leaveTypeListall}
              setLeaveTypeList={setLeaveTypeList}
            />
          )}
      </Box>
      <DialogBox
        open={PINPassword}
        handleClose={() => {
          handleClosePin();
        }}
        title={PINPasswordTitle}
        content={
          <>
            <Box className="Reset-password-pin-sec">
              {PINPasswordTitle === 'Generate Password' ? (
                <UserGeneratePassword
                  Randompassword={Randompassword}
                  setRandomPassword={setRandomPassword}
                  isReset={isReset}
                  setIsReset={setIsReset}
                  ViewAccessOnly={ViewAccessOnly}
                  validationFeedback={validationFeedback}
                  setValidationFeedback={setValidationFeedback}
                  UserId={UserId}
                  setLoader={setLoader}
                  handleClosePin={handleClosePin}
                />
              ) : (
                <UserResetPin
                  RandomPIN={RandomPIN}
                  setRandomPIN={setRandomPIN}
                  isReset={isResetPin}
                  setIsReset={setIsResetPin}
                  ViewAccessOnly={ViewAccessOnly}
                  UserId={UserId}
                  setLoader={setLoader}
                  handleClosePin={handleClosePin}
                />
              )}
            </Box>
          </>
        }
      />
    </>
  );
};

export default ProfileDetails;
