'use client';

import React from 'react';
import { Tooltip, Typography } from '@mui/material';
import Icon from '@/components/UI/AppIcon/AppIcon';
import CustomButton from '@/components/UI/CustomButton';
import AutoSaveIndicator from '../AutoSaveIndicator/AutoSaveIndicator';
import './MainFormContent.scss';

export default function MainFormContent({
  formSections,
  activeSection,
  onSectionChange,
  currentSection,
  CurrentSectionComponent,
  formState,
  dispatch,
  validationErrors,
  setValidationErrors,
  isMobileView,
  currency,
  isUpdate,
  onPreview,
  onSaveAndExit,
  checkAllRequiedValidation,
}) {
  const currentIndex = formSections.findIndex((s) => s?.id === activeSection);
  const isDisabled = formSections[currentIndex + 1]?.isDisabled;

  const prevSection = formSections[currentIndex - 1];
  const prevSectionTitle = prevSection?.title || 'previous section';

  const tooltipTitle = isDisabled
    ? `Please complete ${prevSectionTitle} first`
    : '';

  const nextButton = (
    <button
      onClick={() => {
        if (currentIndex < formSections.length - 1) {
          onSectionChange(formSections[currentIndex + 1].id);
        }
      }}
      disabled={isDisabled}
      className="main-form-content__mobile-nav-button"
    >
      <span className="main-form-content__mobile-nav-text">Next</span>
      <Icon name="ChevronRight" size={16} color="currentColor" />
    </button>
  );
  return (
    <div className="main-form-content">
      {/* Header Actions */}
      <div className="main-form-content__header">
        <div className="main-form-content__header-content">
          <div className="main-form-content__header-left">
            <h1 className="main-form-content__header-title">
              {isMobileView
                ? currentSection?.title
                : isUpdate
                  ? 'Update Recipe'
                  : 'Add New'}
            </h1>
            <AutoSaveIndicator
              status={formState?.recipeStatus}
              lastSaved={formState?.lastUpdated}
            />
          </div>

          <div className="main-form-content__header-actions">
            {formState?.recipeStatus === 'publish' && (
              <CustomButton
                onClick={onPreview}
                className="main-form-content__preview-button"
                variant="outlined"
                startIcon={<Icon name="Eye" size={16} />}
              >
                <span className="main-form-content__button-text">Preview</span>
              </CustomButton>
            )}

            <CustomButton
              onClick={onSaveAndExit}
              className="main-form-content__save-button"
              variant="contained"
              startIcon={<Icon name="Save" size={16} />}
            >
              <span className="main-form-content__button-text">
                {checkAllRequiedValidation() ? 'Submit' : 'Save & Next'}
              </span>
            </CustomButton>
          </div>
        </div>
      </div>

      {/* Mobile: Section Selector */}
      {isMobileView && (
        <div className="main-form-content__mobile-selector">
          <div className="main-form-content__mobile-nav">
            <button
              onClick={() => {
                if (currentIndex > 0) {
                  onSectionChange(formSections[currentIndex - 1].id);
                }
              }}
              disabled={
                formSections.findIndex((s) => s.id === activeSection) === 0
              }
              className="main-form-content__mobile-nav-button"
            >
              <Icon name="ChevronLeft" size={16} color="currentColor" />
              <span className="main-form-content__mobile-nav-text">
                Previous
              </span>
            </button>

            <div className="main-form-content__mobile-counter">
              <span className="main-form-content__mobile-counter-text">
                {formSections.findIndex((s) => s.id === activeSection) + 1} of{' '}
                {formSections.length}
              </span>
            </div>
            {isDisabled ? (
              <Tooltip
                title={
                  <Typography className="sub-title-text">
                    {tooltipTitle}
                  </Typography>
                }
                arrow
                classes={{
                  tooltip: 'info-tooltip-container',
                }}
              >
                {nextButton}
              </Tooltip>
            ) : (
              nextButton
            )}
          </div>
        </div>
      )}

      {/* Form Section Content */}
      <div className="main-form-content__form-content">
        {CurrentSectionComponent && (
          <CurrentSectionComponent
            data={formState[activeSection] || formState}
            formData={formState}
            dispatch={dispatch}
            validationErrors={validationErrors}
            setValidationErrors={setValidationErrors}
            isMobileView={isMobileView}
            currency={currency}
          />
        )}
      </div>
    </div>
  );
}
