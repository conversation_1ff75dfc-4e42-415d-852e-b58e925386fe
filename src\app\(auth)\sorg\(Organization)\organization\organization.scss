@import '@/styles/variable.scss';

.organization-wrap {
  background-color: $color-White;
  padding: 20px 40px 56px 40px;
  border-radius: 12px;
  box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.05);
  @media (max-width: 768px) {
    padding: 24px 15px;
  }
  .tabs-wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ededed;

    .report-tabs {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // border-bottom: 1px solid red;
      overflow: auto;

      .tab-list-sec {
        .tab-name {
          text-transform: none;
          font-family: $PrimaryFont;
          font-size: 14px;
          line-height: 20px;
          font-weight: 400;
          color: #000000;
          opacity: 1;
          width: max-content;
          padding: 5px;

          @media (max-width: 1200px) {
            width: max-content;
            // min-width: 50%;
          }
        }

        .MuiTabs-flexContainer {
          align-items: center;
          // justify-content: space-between;
        }

        .MuiTabs-indicator {
          background-color: $color-primary;
          height: 3px;
        }

        .Mui-selected {
          color: $color-primary !important;
        }

        .MuiTabs-scroller {
          display: block !important;
        }

        .MuiTabScrollButton-root {
          display: block !important;

          .MuiSvgIcon-root {
            margin: 14px;
          }
        }
      }
    }
  }
}
.organization-wrap {
  .org-header {
    display: flex;
    justify-content: space-between;

    .filter-input-wrap {
      align-items: flex-end;
      flex-wrap: wrap;
      width: 100%;
      .filter-input {
        .MuiInputBase-root {
          min-height: 30px;

          .MuiInputBase-input {
            padding: 3px 16px;

            &::placeholder {
              font-size: 14px !important;
            }
          }
        }
        @media (max-width: 575px) {
          width: 100%;
        }
      }

      .search-btn {
        padding: 3px 12px 3px 8px !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        max-height: max-content;

        .search-icon {
          height: 19px;
          width: 19px;
        }

        &:hover {
          color: $color-secondary !important;
          box-shadow: none !important;

          .search-icon {
            fill: white !important;
          }
        }
        @media (max-width: 575px) {
          width: 100%;
        }
      }
    }
    .create-sub-plan-btn-wrap {
      width: 100%;
      max-width: 140px;

      .create-sub-plan-btn {
        padding: 3px 20px 3px 12px !important;
        font-size: 14px !important;
        font-weight: 500 !important;

        .add-icon {
          height: 21px;
          width: 21px;
        }

        &:hover {
          color: $color-secondary !important;
          box-shadow: none !important;

          .add-icon {
            fill: white !important;
          }
        }
        @media (max-width: 575px) {
          width: 100%;
        }
      }

      @media (max-width: 575px) {
        display: flex;
        justify-content: flex-end;
        max-width: 100%;
        margin-bottom: 20px;
      }
    }
  }

  .divider {
    // padding-top: 20px;
    margin-bottom: 20px;
  }
}
