'use client';

import React, { useEffect, useState } from 'react';
import { Box, CircularProgress, Typography, Tooltip } from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import CustomPagination from '@/components/UI/customPagination';
import { setApiMessage, DateFormat } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import moment from 'moment';
import DialogBox from '../../UI/Modalbox';
import CustomSelect from '@/components/UI/CustomSelect';
import { findDifferences } from '@/helper/common/commonFunctions';
import ViewIcon from '../../ActionIcons/ViewIcon';
import NoDataView from '@/components/UI/NoDataView';
import './activity.scss';

export default function UserActivity({ userId }) {
  const [activityList, setActivityList] = useState([{ id: '' }]);
  const [loader, setLoader] = useState(true);
  const [toggleModal, setToggleModal] = useState(false);
  const [userAgentValue, setUserAgentValue] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [currentPageper, setCurrentPagePer] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [totalCountPer, setTotalCountPer] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [rowsPerPPage, setRowsPerPPage] = useState(10);
  const [tab, setTab] = useState('own');
  const tabList = [
    { label: 'User Specific', value: 'staff' },
    { label: 'All', value: 'own' },
  ];

  const getActivityDetails = async (pageNo, tabValue, Rpp) => {
    setLoader(true);
    const tabkey = tabValue === 'staff' ? '' : tabValue;
    const rowPer =
      tabValue === 'staff'
        ? Rpp
          ? Rpp
          : rowsPerPage
        : Rpp
          ? Rpp
          : rowsPerPPage;
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.USER_ACTIVITY +
          `/${userId}?page=${pageNo}&size=${rowPer}&tab=${tabkey}`
      );

      if (status === 200) {
        const activityData =
          data?.data &&
          data?.data?.length > 0 &&
          data?.data?.map((a, index) => {
            let newdata = a?.new_data && JSON.parse(a?.new_data);
            let previousdata = a?.previous_data && JSON.parse(a?.previous_data);
            if (a?.activity_action !== 'created' && newdata && previousdata) {
              var diffs = findDifferences(previousdata, newdata);
            }
            return {
              ...a,
              user_id: a?.users?.id,
              id: index,
              user_full_name: a?.users?.user_full_name,
              user_email: a?.users?.user_email,
              differenceData:
                a?.activity_action !== 'created' ? diffs : newdata,
              branch_name: newdata?.branch_name ? newdata?.branch_name : '-',
              ip_address: a?.ip_address ? a?.ip_address : '-',
              new_data: newdata,
              previous_data: previousdata,
              department_name: newdata?.department_name
                ? newdata?.department_name
                : '-',
            };
          });
        tabValue === 'staff'
          ? setCurrentPage(data?.page)
          : setCurrentPagePer(data?.page);
        tabValue === 'staff'
          ? setTotalCount(data?.count)
          : setTotalCountPer(data?.count);
        activityData && activityData?.length > 0
          ? setActivityList(activityData)
          : setActivityList([]);
        setLoader(false);
      }
    } catch (error) {
      setLoader(false);
      setActivityList([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const columns = [
    {
      field: 'user_id',
      headerName: 'ID',
      width: 48,
      minWidth: 48,
      flex: 0,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
    },
    {
      field: 'createdAt',
      headerName: 'Date & Time',
      width: 200,
      minWidth: 200,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-start h100">
            <Typography className="title-text text-ellipsis">
              {DateFormat(params?.value, 'datesWithhourUTC')}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'user_full_name',
      headerName: 'Staff Name',
      width: 200,
      minWidth: 200,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-start h100">
            <Typography className="title-text text-ellipsis">
              {params?.value}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'activity_action',
      headerName: 'Activity actions',
      width: 200,
      minWidth: 200,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-start h100">
            <Typography className="title-text text-ellipsis">
              {params?.row?.activity_table ? params?.row?.activity_table : ''}{' '}
              {params?.row?.activity_action ? params?.row?.activity_action : ''}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'ip_address',
      headerName: 'IP',
      width: 200,
      minWidth: 200,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
    },
    {
      field: 'location',
      headerName: 'Location',
      width: 200,
      minWidth: 200,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box
            className={`d-flex align-center  h100 ${params?.value ? 'justify-start' : 'justify-center'}`}
          >
            <Typography className="title-text d-flex align-center justify-center">
              {params?.value ? params?.value : '-'}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'address',
      headerName: 'address',
      width: 200,
      minWidth: 200,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box
            className={`d-flex align-center  h100 ${params?.value ? 'justify-start' : 'justify-center'}`}
          >
            <Typography
              className={`title-text d-flex align-center justify-center `}
            >
              {params?.value ? params?.value : '-'}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'userAgent',
      headerName: 'Info',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center actions justify-center h100">
            <Tooltip title="Info" arrow className="action-tooltip">
              <Box>
                <ViewIcon
                  onClick={() => {
                    setToggleModal(!toggleModal);
                    setUserAgentValue(params?.row);
                  }}
                />
              </Box>
            </Tooltip>
          </Box>
        );
      },
    },
  ];

  const onPageChange = (newPage) => {
    tab === 'staff' ? setCurrentPage(newPage) : setCurrentPagePer(newPage);
    getActivityDetails(newPage, tab);
  };
  const OnRowPerPage = (newPage) => {
    tab === 'staff' ? setRowsPerPage(newPage) : setRowsPerPPage(newPage);
    tab === 'staff' ? setCurrentPage(1) : setCurrentPagePer(1);
    getActivityDetails(1, tab, newPage);
  };
  useEffect(() => {
    getActivityDetails(1, tab);
  }, []);
  return (
    <Box className="user-activity">
      <Box>
        {' '}
        <CustomSelect
          placeholder="view"
          name="view"
          options={tabList}
          value={tabList?.find((f) => f?.value === tab) || ''}
          onChange={(e) => {
            // getActivityDetails(1, e.target.value);
            e?.value === 'staff'
              ? getActivityDetails(currentPage, e?.value)
              : getActivityDetails(currentPageper, e?.value);
            setTab(e?.value);
          }}
          label={<span></span>}
        />
      </Box>
      {loader ? (
        <Box className="content-loader pt16">
          <CircularProgress className="loader" color="inherit" />
        </Box>
      ) : activityList && activityList?.length === 0 ? (
        <Box className="no-data d-flex align-center justify-center">
          <NoDataView
            title="No Activity Records Found"
            description="There is no Activity data available at the moment."
          />
        </Box>
      ) : (
        <Box className="table-container table-layout mt0 activity-table">
          <DataGrid
            rows={activityList}
            columns={columns}
            pageSize={rowsPerPage}
            checkboxSelection={false}
            disableSelectionOnClick
            hideMenuIcon
          />
          {/* <CustomPagination
            currentPage={currentPage}
            totalCount={totalCount}
            rowsPerPage={rowsPerPage}
            onPageChange={onPageChange}
            OnRowPerPage={OnRowPerPage}
          /> */}
          {tab === 'staff' ? (
            <CustomPagination
              currentPage={currentPage}
              totalCount={totalCount}
              rowsPerPage={rowsPerPage}
              onPageChange={onPageChange}
              OnRowPerPage={OnRowPerPage}
            />
          ) : tab === 'own' ? (
            <CustomPagination
              currentPage={currentPageper}
              totalCount={totalCountPer}
              rowsPerPage={rowsPerPPage}
              onPageChange={onPageChange}
              OnRowPerPage={OnRowPerPage}
            />
          ) : (
            <></>
          )}
        </Box>
      )}
      <DialogBox
        open={toggleModal}
        handleClose={() => {
          setToggleModal(!toggleModal);
          setUserAgentValue('');
        }}
        title="Details"
        className="staff-dialogbox"
        content={
          <Box>
            {userAgentValue?.userAgent && (
              <>
                <Typography className="body-text fw600 text-underline">
                  User Agent
                </Typography>
                <Typography className="title-text pb8 pt8">
                  {userAgentValue?.userAgent}
                </Typography>
              </>
            )}

            {userAgentValue?.differenceData && (
              <Typography className="body-text fw600 text-underline pt16">
                Updated Details
              </Typography>
            )}
            {userAgentValue?.differenceData &&
              Object.entries(userAgentValue?.differenceData) &&
              Object.entries(userAgentValue?.differenceData)?.map(
                ([key, value]) => (
                  <div key={key}>
                    <Typography className="title-text fw600 pb4 pt16">
                      {key ? key?.replace(/_/g, ' ') : ''}
                    </Typography>
                    {value?.oldValue &&
                      userAgentValue?.activity_action !== 'created' && (
                        <Box className="d-flex align-center">
                          <Typography className="title-text user-date">
                            <span className="">Old data: </span>
                            <span>
                              {key === 'updatedAt' || key === 'createdAt'
                                ? value?.oldValue
                                  ? moment(value?.oldValue).format(
                                      'DD/MM/YYYY hh:mm A'
                                    )
                                  : '-'
                                : value?.oldValue
                                  ? value?.oldValue
                                  : '-'}
                            </span>
                          </Typography>
                        </Box>
                      )}

                    {(value?.newValue ||
                      (userAgentValue?.activity_action === 'created' &&
                        value !== null)) && (
                      <Box className="d-flex align-center">
                        <Typography className="title-text user-date">
                          <span className="">New data: </span>
                          {userAgentValue?.activity_action === 'created' ? (
                            <span>
                              {' '}
                              {key === 'updatedAt' || key === 'createdAt'
                                ? value
                                  ? moment(value).format('DD/MM/YYYY hh:mm A')
                                  : '-'
                                : value !== null
                                  ? value
                                  : '-'}
                            </span>
                          ) : (
                            <span>
                              {' '}
                              {key === 'updatedAt' || key === 'createdAt'
                                ? value?.newValue
                                  ? moment(value?.newValue).format(
                                      'DD/MM/YYYY hh:mm A'
                                    )
                                  : '-'
                                : value?.newValue !== null
                                  ? value?.newValue
                                  : '-'}
                            </span>
                          )}
                        </Typography>
                      </Box>
                    )}
                  </div>
                )
              )}
          </Box>
        }
      />
    </Box>
  );
}
