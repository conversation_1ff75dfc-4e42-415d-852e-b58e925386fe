'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  IconButton,
  InputAdornment,
  TextField,
  Typography,
} from '@mui/material';
import { useRouter, useSearchParams } from 'next/navigation';
import CustomButton from '@/components/UI/button';
import Image from 'next/image';
// import axiosInstance from '@/helper/axios/axiosInstance';
import { INFO_LINKS, ORG_URLS } from '@/helper/constants/urls';
import { setApiMessage } from '@/helper/common/commonFunctions';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import VerifyMailIcon from '../../../../public/images/verifyIcon.svg';
import { ProfileRoundIcon } from '@/helper/common/images';
import Link from 'next/link';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import './createaccount.scss';

export default function CreateAccount() {
  const [showResendSection, setShowResendSection] = useState(false);
  const [hideFormSection, setHideFormSection] = useState(false);
  const [showResendEmail, setShowResendEmail] = useState(false);
  const [changeEmailChecked, setChangeEmailChecked] = useState(false);
  const [remainingTime, setRemainingTime] = useState(60); //120
  const router = useRouter();
  // const [loader, setLoader] = useState(false);
  const [Isemail, setIsEmail] = useState(false);
  const [IsSend, setIsSend] = useState();
  const [displayEmail, setDisplayEmail] = useState('');
  const searchParams = useSearchParams();
  const UserId = searchParams.get('data');
  const emails = searchParams.get('email');
  useEffect(() => {
    const timerInterval = setInterval(() => {
      setRemainingTime((prevTime) => {
        if (prevTime <= 1) {
          clearInterval(timerInterval);
          setShowResendSection(true);
          if (showResendEmail) {
            setHideFormSection(true);
            setChangeEmailChecked(false);
          }
          return 0;
        }
        return prevTime - 1;
      });
    }, 1000);

    return () => clearInterval(timerInterval);
  }, [IsSend]);

  //Format remaining time into MM:SS
  const formatTime = (time) => {
    const minutes = Math.floor(time / 60);
    const seconds = time % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const resendEmail = async () => {
    let sendData = {
      userId: UserId,
      email: displayEmail ? displayEmail : emails,
      isUpdateEmail: true,
    };

    try {
      // setLoader(true);
      const { status, data } = await axiosInstance.post(
        ORG_URLS.RESED_AUTH_EMAIL,
        sendData
      );
      if (status === 200) {
        if (data.status) {
          setApiMessage('success', data?.message);
          setRemainingTime(60);
          setShowResendSection(false);
          setIsSend(Math.random());
          setHideFormSection(false);
          setShowResendEmail(true);
          // router.push(
          //   `/email-verification?data=${UserId}&email=${requestData?.email}`
          // );
        } else {
          setApiMessage('error', data?.message);
          router.push('/login');
        }
        // setLoader(false);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      // setLoader(false);
      router.push('/login');
    }
  };

  return (
    <Box className="verify-mail-wrap">
      <Image
        src={VerifyMailIcon}
        height={128}
        width={128}
        alt="Mail Icon"
        className="mail-icon-wrap"
      />
      <Box className="mail-text-wrap">
        <Typography className="text-wrap heading-text" variant="h5">
          Congrats! Account Created Successfully
        </Typography>
      </Box>
      <Typography className="heading-wrap sub-heading-text pb8" variant="h5">
        Please verify your email
      </Typography>
      <Typography
        component="p"
        className="email-text-wrap sub-heading-text pb8"
      >
        You're almost there! We sent an email to
      </Typography>
      <Typography
        component="p"
        className="email-link-wrap sub-heading-text pb8"
      >
        {displayEmail ? displayEmail : emails}
      </Typography>
      <Typography component="p" className="span-text-wrap sub-heading-text pb8">
        Just click on the link in that email to complete your signup. If you
        don't see it, you may need to
        <span className="span-folder-wrap sub-heading-text pb8">
          check your spam
        </span>
        folder.
      </Typography>

      {!showResendSection && (
        <Typography
          component="p"
          className="countdown-wrap sub-heading-text pb8"
        >
          Resend your email in
          <span className="time-wrap">{formatTime(remainingTime)}</span>
        </Typography>
      )}

      {showResendSection && !hideFormSection && (
        <Typography
          component="p"
          className={`email-text-wrap sub-heading-text trouble-text pb8 contact-text cursor-pointer ${
            !Isemail && 'pb8'
          }`}
          onClick={() => {
            setIsEmail(!Isemail);
          }}
        >
          Are you experiencing trouble with the email?
          {/* <span
          className="email"
          onClick={() => {
            setIsEmail(!Isemail);
          }}
        >
          Yes
        </span>
        /
        <span
          className="email"
          onClick={() => {
            setIsEmail(!Isemail);
          }}
        >
          No
        </span> */}
        </Typography>
      )}

      {Isemail && showResendSection && !hideFormSection ? (
        <>
          <Formik
            initialValues={{
              email: '',
              sameMail: false,
            }}
            enableReinitialize={true}
            validationSchema={Yup.object().shape({
              email: Yup.string()
                .required('This field is required')
                .matches(
                  /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i,
                  'Please enter valid email'
                ),
              // .notOneOf([emails], 'This email matches with existing email.'),
              // sameMail: Yup.bool().test(
              //   'is-required-if-email',
              //   'This field is required and must be checked',
              //   function (value) {
              //     const { email } = this.parent;
              //     if (email !== emails) {
              //       return value === true; // Must be checked
              //     }
              //     return true; // Not required for other emails
              //   }
              // ),
            })}
            onSubmit={async (requestData) => {
              let sendData = {
                userId: UserId,
                email: requestData?.email,
                isUpdateEmail: requestData?.sameMail,
              };

              try {
                // setLoader(true);
                const { status, data } = await axiosInstance.post(
                  ORG_URLS.RESED_AUTH_EMAIL,
                  sendData
                );
                if (status === 200) {
                  if (data.status) {
                    setDisplayEmail(requestData?.email);
                    setApiMessage('success', data?.message);
                    setRemainingTime(60);
                    setShowResendSection(false);
                    setIsSend(Math.random());
                    setHideFormSection(false);
                    setShowResendEmail(true);
                    // router.push(
                    //   `/email-verification?data=${UserId}&email=${requestData?.email}`
                    // );
                  } else {
                    setApiMessage('error', data?.message);
                    router.push('/login');
                  }
                  // setLoader(false);
                }
              } catch (error) {
                setApiMessage('error', error?.response?.data?.message);
                // setLoader(false);
                router.push('/login');
              }
            }}
          >
            {({
              errors,
              touched,
              handleBlur,
              values,
              handleSubmit,
              handleChange,
              setFieldValue,
              // dirty,
              // isValid,
            }) => (
              <Form onSubmit={handleSubmit}>
                <Box className="d-flex email-check-section">
                  {values.sameMail ? (
                    <CheckBoxIcon
                      onClick={() => {
                        setFieldValue('sameMail', !values?.sameMail);
                        setChangeEmailChecked(!values?.sameMail);
                      }}
                    />
                  ) : (
                    <CheckBoxOutlineBlankIcon
                      onClick={() => {
                        setFieldValue('sameMail', !values?.sameMail);
                        setChangeEmailChecked(!values?.sameMail);
                      }}
                    />
                  )}
                  <Typography
                    className="sub-heading-text checkbox-text"
                    onClick={() => setFieldValue('sameMail', !values?.sameMail)}
                  >
                    Would you like to change your email?
                  </Typography>
                </Box>
                {values?.sameMail && (
                  <>
                    <Box className="input-wrap pt16">
                      <Typography
                        variant="h6"
                        className="un-auth-label-wrap label-wrap"
                      >
                        Email Address
                      </Typography>

                      <TextField
                        InputLabelProps={{
                          shrink: true,
                        }}
                        id="email"
                        name="email"
                        value={values?.email}
                        className="w100 "
                        placeholder="Enter your email address"
                        variant="filled"
                        error={Boolean(touched?.email && errors?.email)}
                        helperText={touched?.email && errors?.email}
                        onBlur={handleBlur}
                        onChange={handleChange}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">
                              <IconButton disableRipple>
                                <Box className="eye-wrap">
                                  <ProfileRoundIcon />
                                </Box>
                              </IconButton>
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Box>

                    {/* {touched?.sameMail && errors?.sameMail && values?.sameMail && (
                  <Typography
                    variant="body2"
                    color="error"
                    className="field-error samemail-error"
                  >
                    {errors?.sameMail}
                  </Typography>
                )} */}
                    <Box className="get-started-btn-wrap" textAlign="center">
                      <CustomButton
                        fullWidth
                        className="get-started-btn"
                        fontWeight="600"
                        variant="contained"
                        background="#39596e"
                        backgroundhover="#39596e"
                        colorhover="#FFFFFF"
                        title="Resend Email"
                        type="submit"
                      />
                    </Box>
                  </>
                )}
              </Form>
            )}
          </Formik>
        </>
      ) : (
        <></>
      )}
      {remainingTime === 0 && !changeEmailChecked && (
        <Box className="get-started-btn-wrap" textAlign="center">
          <CustomButton
            fullWidth
            className="p16 get-started-btn register-btn"
            type="submit"
            fontWeight="600"
            variant="contained"
            background="#39596e"
            backgroundhover="#39596e"
            colorhover="#FFFFFF"
            title="Resend Email"
            onClick={resendEmail}
          />
        </Box>
      )}
      {/* {hideFormSection && (
        <>
          <Typography component="p" className="sub-heading-text">
            Still can't find the email?
          </Typography>
          <Box className="get-started-btn-wrap" textAlign="center">
            <CustomButton
              fullWidth
              className="p16 get-started-btn register-btn"
              type="submit"
              fontWeight="600"
              variant="contained"
              background="#39596e"
              backgroundhover="#39596e"
              colorhover="#FFFFFF"
              title="Resend Email"
              onClick={resendEmail}
            />
          </Box>
        </>
      )} */}
      <Typography component="p" className="contact-text sub-heading-text">
        Need help?{' '}
        <Link
          href={INFO_LINKS?.contactUs}
          target="_blank"
          rel="noopener noreferrer"
          className="contact-us-wrap"
        >
          Contact Us
        </Link>
      </Typography>
    </Box>
  );
}
