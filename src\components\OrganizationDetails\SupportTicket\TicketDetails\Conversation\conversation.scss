.convesation-wrap {
  .conversation-text {
    font-family: var(--font-family-primary);
  }
}

.conversation-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 600px;

  .conversation-messages {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-md) var(--spacing-md) var(--spacing-md)
      var(--spacing-none);

    .message-item {
      margin-bottom: var(--spacing-lg);
      padding: var(--spacing-md);
      border-radius: var(--border-radius-md);
      background-color: var(--color-white);
      border: 1px solid var(--border-color-light-gray);

      &.internal-note {
        background-color: var(--color-light-champagne);
        border-color: var(--color-warning);
      }

      .message-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-sm);

        .sender-name {
          font-weight: var(--font-weight-medium);
          color: var(--text-color-black);

          .internal-note-badge {
            margin-left: var(--spacing-sm);
            padding: var(--spacing-xs);
            background-color: var(--color-warning);
            color: var(--color-white);
            border-radius: var(--border-radius-xs);
            text-transform: capitalize;
          }
        }

        .message-timestamp {
          color: var(--color-dark-50);
          font-size: var(--font-size-xs);
        }
      }

      .message-content {
        .message-text {
          color: var(--color-dark);
          line-height: var(--line-height-relaxed);
        }

        // Change Request attachment styles
        .message-attachments {
          margin-top: var(--spacing-sm);

          .file-grid-container {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            column-gap: var(--spacing-xl);
            row-gap: var(--spacing-xs);

            .file-grid-item {
              border: var(--normal-sec-border);
              border-radius: var(--border-radius-sm);
              padding: var(--spacing-sm);
            }

            @media (max-width: 991px) {
              grid-template-columns: repeat(2, 1fr);
            }

            @media (max-width: 575px) {
              grid-template-columns: repeat(1, 1fr);
            }
          }

          .selected-files {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 640px;
            margin-bottom: var(--spacing-sm);

            svg {
              width: 24px;
              height: 24px;
              cursor: pointer;
            }

            .file-name {
              width: calc(100% - 10px - 24px);

              p {
                word-break: break-all;
              }
            }
          }

          .selected-view-files {
            margin-bottom: var(--spacing-xxs) !important;

            svg {
              width: 20px;
              height: 20px;
              cursor: pointer;
            }
          }
        }
      }
    }
  }

  .internal-note-checkbox {
    padding: var(--spacing-sm) var(--spacing-none);
    border-top: 1px solid var(--border-color-light-gray);
    background-color: var(--color-white);

    .visibility-icon {
      font-size: var(--font-size-md);
      color: var(--color-dark-50);
      transition: color 0.2s ease;

      &.active {
        color: var(--color-primary);
      }
    }

    .checkbox-label {
      color: var(--color-dark-50);
      margin: 0;
    }
  }

  .conversation-input-wrapper {
    .conversation-input-container {
      width: 100%;
    }
    .conversation-send-button-icon {
      height: 100%;
      min-height: 75px;
      max-height: 75px;
    }
  }
}
