@import '@/styles/variable.scss';

.own-leave-container {
  margin-top: 0px;
  min-height: calc(100vh - 152px - var(--banner-height));
  background-color: var(--color-white);
  padding: var(--spacing-xxl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-xs);
  .icons-container {
    .calender-search-wrap {
      width: 100%;
      max-width: 350px;
    }

    @media (max-width: 767px) {
      flex-wrap: wrap;
      row-gap: 10px;
    }
  }
  @media (max-width: 899px) {
    .p-0 {
      padding: 0px !important;
    }
  }
  .display-grid {
    display: grid;
    grid-template-columns: 49% 49%;
    column-gap: 15px;
    row-gap: 32px;
    @media (max-width: 899px) {
      display: grid;
      grid-template-columns: 100%;
      column-gap: 32px;
      row-gap: 32px;
    }
  }

  .view-list-table {
    margin-top: 20px;
    .table-container {
      background-color: none;
      box-shadow: none;
      padding: 0px;
      margin-top: 20px;
    }
  }

  .calendar-sec-wrap {
    width: 100%;
    height: auto;
  }
  @media (max-width: 575px) {
    padding: 20px 14px 32px;
  }
}
.MuiPickersPopper-root {
  z-index: 99999 !important;
}
.apply-leave-drawer {
  .MuiPaper-root {
    width: 100% !important;
    max-width: 90% !important;
    margin: 10px;
    margin-bottom: 50px !important;
    border-radius: 16px;
    padding: 12px 20px 22px !important;
    max-height: calc(100vh - 20px - var(--banner-height));
    border-radius: 16px;
    &::-webkit-scrollbar {
      display: none !important;
    }
  }
}
