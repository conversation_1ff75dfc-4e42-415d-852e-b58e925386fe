import React, { useEffect, useState } from 'react';
import { Typography, Box, InputAdornment } from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomButton from '@/components/UI/CustomButton';
import { setApiMessage } from '@/helper/common/commonFunctions';
import NoDataView from '@/components/UI/NoDataView';
import CustomCheckbox from '@/components/UI/CustomCheckbox';

const DeleteFilterModal = ({
  listData,
  isFiltertype,
  handleCloseDeleteFilterModal,
  setFormattedSelection,
  formattedSelection,
  handleApplyFilter,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedList, setSelectedList] = useState({});

  useEffect(() => {
    if (listData?.length) {
      setSelectedList(() => {
        // Check if formattedSelection already has data for this isFiltertype
        const existingSelection = formattedSelection?.find(
          (item) => item.type === isFiltertype
        );

        const selectionMap = {};

        if (existingSelection) {
          existingSelection.data.forEach((item) => {
            selectionMap[item.id] = true;
          });
        } else {
          // If not present, select all by default
          listData.forEach((item) => {
            selectionMap[item.id] = true;
          });
        }

        return selectionMap;
      });
    }
  }, [listData, isFiltertype, formattedSelection]);

  const handleListSelect = (selectId) => {
    setSelectedList((prev) => ({
      ...prev,
      [selectId]: !prev[selectId],
    }));
  };

  const handleSelectAll = () => {
    // const allSelected = listData?.every((item) => selectedList[item?.id]);
    const newSelection = {};
    listData.forEach((item) => {
      newSelection[item?.id] = true;
    });
    setSelectedList(newSelection);
  };

  const handleDeselectAll = () => {
    setSelectedList({});
  };

  const filteredLists = listData?.filter((item) =>
    item?.label_name?.toLowerCase()?.includes(searchTerm?.toLowerCase())
  );

  const handleApply = () => {
    const selectedData = listData?.filter((item) => selectedList[item.id]);
    const currentSelection = selectedData.map((item) => ({
      id: item.id,
      label_name: item.label_name,
    }));

    if (selectedData.length === 0) {
      // You can replace this with a toast or snackbar message if needed
      setApiMessage('error', `Please select at least one `);
      return;
    }

    setFormattedSelection((prev) => {
      const existing = [...(prev || [])];
      const updated = existing.filter((entry) => entry.type !== isFiltertype);

      const updateList = [
        ...updated,
        { type: isFiltertype, data: currentSelection },
      ];
      handleApplyFilter(updateList, isFiltertype);
      return updateList;
    });
    handleCloseDeleteFilterModal();
  };

  return (
    <Box className="delete-filter-modal">
      <Box className="delete-filter-section">
        <Box className="search-box">
          <CustomTextField
            name={`search_emp-${isFiltertype}`}
            label=""
            placeholder="Search..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
          />
          <Box className="select-actions">
            <Typography
              component="span"
              className="action-link"
              onClick={handleSelectAll}
            >
              Select All
            </Typography>
            {' • '}
            <Typography
              component="span"
              className="action-link"
              onClick={handleDeselectAll}
            >
              Deselect All
            </Typography>
          </Box>
        </Box>
        {filteredLists?.length !== 0 ? (
          <Box className="employees-grid">
            {filteredLists?.map((item) => (
              <Box key={item?.id} className="employee-item">
                <CustomCheckbox
                  checked={!!selectedList[item?.id]}
                  onChange={() => handleListSelect(item?.id)}
                  label={item?.label_name}
                />
              </Box>
            ))}
          </Box>
        ) : (
          <NoDataView
            title="No Employee Data Found"
            description="There is no Employee Data at the moment."
          />
        )}
      </Box>
      {listData?.length !== 0 && (
        <Box className="form-actions-btn">
          <CustomButton
            variant="outlined"
            title="Cancel"
            onClick={handleCloseDeleteFilterModal}
          />
          <CustomButton
            variant="contained"
            title="Apply"
            onClick={handleApply}
          />
        </Box>
      )}
    </Box>
  );
};

export default DeleteFilterModal;
