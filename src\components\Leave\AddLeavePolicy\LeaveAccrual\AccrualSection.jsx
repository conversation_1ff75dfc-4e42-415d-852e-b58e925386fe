import React from 'react';
import CustomCheckbox from '@/components/UI/CustomCheckbox';
import CustomSelect from '@/components/UI/CustomSelect';
import CustomTextField from '@/components/UI/CustomTextField';
import { Box, Tooltip, Typography } from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import { identifiers } from '@/helper/constants/identifier';
import {
  dayOptions,
  monthsDays,
  monthsOption,
} from '@/helper/common/commonFunctions';
import CustomRadio from '@/components/UI/CustomRadio';
import CustomDropdown from '../CustomDropdown';

export default function AccrualSection({
  values,
  handleChange,
  handleBlur,
  touched,
  errors,
  setFieldValue,
  leavePolicySubmit,
  setLeavePolicySubmit,
  Inputype,
  isAnuual,
  accrualTime,
  setAccrualTime,
  isHour,
  setResetTime,
  leaveType,
}) {
  return (
    <>
      <Box className="mt16">
        <Box className="d-flex align-center gap-10">
          <CustomCheckbox
            checked={values?.accrual}
            onChange={(e) => {
              setFieldValue('accrual', e?.target?.checked);
              setAccrualTime({
                type: 'yearly',
                reset_value: [
                  {
                    on_date: 1,
                    month: 1,
                    days: 0,
                  },
                ],
              });
              setFieldValue('accrualTimewise', 'yearly');
              isAnuual === 'true' && setFieldValue('empcontract', false);
              setFieldValue('stopaccural', false);
              setFieldValue('stopaccurallimit', 0);
              setFieldValue('considerLeaveAccrual', 'yes');
              setFieldValue('accrualBasedLeave', false);
              setFieldValue('restrictLeaveAccrual', false);
              setFieldValue('prorateAccrual', 'pro_rate');
              setLeavePolicySubmit({
                ...leavePolicySubmit,
                leaveAccrual: false,
              });
            }}
            disableRipple
            label={
              <Typography className="sub-content-text">Accrual</Typography>
            }
          />
          <Tooltip
            arrow
            title={
              <Typography>
                Leave accrual will take place considering the employee's
                agreement end date. If an agreement end date is not available,
                it will consider the employee's compensation end date.
                <br />
                <br />
                (i.e.) If leave accrual is set to YEARLY with 12 leave balance
                and the last date of employee's agreement is set to 28th FEB,
                the employee will be credited with 2 leave balance (JAN, FEB) on
                prorate basis. Once the new agreement start and end date will be
                added for the employee, the remaining leave balance for the
                remaining months in a calendar year will be credited to the
                employee's account.
              </Typography>
            }
            classes={{ tooltip: 'info-tooltip-container' }}
          >
            <InfoIcon className="info-icon cursor-pointer" />
          </Tooltip>
        </Box>
        {values?.accrual ? (
          <>
            <Box className="d-flex align-center gap-sm mt8 flex-wrap">
              <Box>
                <CustomCheckbox
                  checked={values?.stopaccural}
                  onChange={(e) => {
                    setFieldValue('stopaccural', e?.target?.checked);
                    setFieldValue('stopaccurallimit', 0);
                    setLeavePolicySubmit({
                      ...leavePolicySubmit,
                      leaveAccrual: false,
                    });
                  }}
                  disableRipple
                  label={
                    <Typography className="sub-content-text">
                      Stop accrual when leave balance reaches
                    </Typography>
                  }
                />
              </Box>
              <Box>
                <CustomTextField
                  fullWidth
                  id="stopaccurallimit"
                  name="stopaccurallimit"
                  placeholder="Days"
                  value={values?.stopaccurallimit}
                  onChange={(e) => {
                    handleChange(e);
                    setLeavePolicySubmit({
                      ...leavePolicySubmit,
                      leaveAccrual: false,
                    });
                  }}
                  error={Boolean(
                    touched?.stopaccurallimit && errors?.stopaccurallimit
                  )}
                  // helperText={
                  //   touched?.stopaccurallimit && errors?.stopaccurallimit
                  // }
                  onBlur={handleBlur}
                  onInput={Inputype}
                  disabled={!values?.stopaccural}
                />
              </Box>
              <Typography className="title-text">maximum limit.</Typography>
              <Box>
                {touched.stopaccurallimit && errors.stopaccurallimit && (
                  <Typography className="other-field-error-text">
                    {errors.stopaccurallimit}
                  </Typography>
                )}
              </Box>
            </Box>
            {isAnuual === 'true' && (
              <Box className="accrual-checkbox">
                <CustomCheckbox
                  checked={values?.empcontract}
                  onChange={(e) => {
                    setFieldValue('empcontract', e?.target?.checked);
                    setAccrualTime({
                      type: 'yearly',
                      reset_value: [
                        {
                          on_date: 1,
                          month: 1,
                          days: 0,
                        },
                      ],
                    });
                    setFieldValue('accrualTimewise', 'yearly');
                    setResetTime({
                      type: 'yearly',
                      reset_value: [
                        {
                          on_date: 1,
                          month: 1,
                        },
                      ],
                    });
                    setFieldValue('resetTimePeriod', 'yearly');
                    setLeavePolicySubmit({
                      ...leavePolicySubmit,
                      leaveAccrual: false,
                    });
                  }}
                  disableRipple
                  label={
                    <Typography className="sub-content-text">
                      Set leave balance based on the employee’s contract
                    </Typography>
                  }
                />
              </Box>
            )}
            <Box className="d-flex align-center gap-20 mt16 ">
              <Box className="d-flex gap-10 flex-wrap">
                <Box>
                  <CustomSelect
                    fullWidth
                    id="accrualTimewise"
                    name="accrualTimewise"
                    placeholder="Accrual Time"
                    options={identifiers?.TIME_PERIOD_OPTIONS}
                    value={
                      identifiers?.TIME_PERIOD_OPTIONS?.find((opt) => {
                        return opt?.value === values.accrualTimewise;
                      }) || ''
                    }
                    className="leave-select-wrap"
                    onChange={(e) => {
                      setFieldValue('accrualTimewise', e?.value);
                      let accrualTimes = accrualTime;
                      if (e?.value === 'yearly' || e?.value === 'one_time') {
                        accrualTimes.type = e?.value;
                        accrualTimes.reset_value = [
                          {
                            on_date: 1,
                            month: 1,
                            days: 0,
                          },
                        ];
                      } else if (e?.value === 'monthly') {
                        accrualTimes.type = e?.value;
                        const montharray = monthsDays?.map((month, index) => ({
                          on_date: 1,
                          month: index + 1,
                          days: 0,
                        }));
                        accrualTimes.reset_value = montharray;
                      } else if (e?.value === 'quarterly') {
                        const montharray = [
                          {
                            on_date: 1,
                            month: 1,
                            months: '1,2,3',
                            days: 0,
                          },
                          {
                            on_date: 1,
                            month: 4,
                            months: '4,5,6',
                            days: 0,
                          },
                          {
                            on_date: 1,
                            month: 7,
                            months: '7,8,9',
                            days: 0,
                          },
                          {
                            on_date: 1,
                            month: 10,
                            months: '10,11,12',
                            days: 0,
                          },
                        ];
                        accrualTimes.type = e?.value;
                        accrualTimes.reset_value = montharray;
                      } else if (e?.value === 'half_yearly') {
                        const montharray = [
                          {
                            on_date: 1,
                            month: 1,
                            days: 0,
                          },
                          {
                            on_date: 1,
                            month: 7,
                            days: 0,
                          },
                        ];
                        accrualTimes.type = e?.value;
                        accrualTimes.reset_value = montharray;
                      }
                      setAccrualTime(accrualTimes);

                      setLeavePolicySubmit({
                        ...leavePolicySubmit,
                        leaveAccrual: false,
                      });
                    }}
                    error={Boolean(
                      touched?.accrualTimewise && errors?.accrualTimewise
                    )}
                    helperText={
                      touched?.accrualTimewise && errors?.accrualTimewise
                    }
                    isClearable={false}
                    isDisabled={values?.empcontract && isAnuual === 'true'}
                  />
                </Box>
                <Box>
                  {(values?.accrualTimewise === 'yearly' ||
                    values?.accrualTimewise === 'one_time') && (
                    <Box className="d-flex align-center gap-10 flex-wrap">
                      <Typography className="title-text">On the</Typography>
                      <Box>
                        <CustomSelect
                          id="accrualDays"
                          name="accrualDays"
                          placeholder="Days"
                          options={dayOptions}
                          value={
                            dayOptions?.find((opt) => {
                              return (
                                opt?.value ===
                                accrualTime?.reset_value?.[0]?.on_date
                              );
                            }) || ''
                          }
                          className="leave-select-wrap"
                          onChange={(e) => {
                            const resetTimes = accrualTime;
                            resetTimes.reset_value[0].on_date = e?.value;
                            setAccrualTime(resetTimes);
                            setLeavePolicySubmit({
                              ...leavePolicySubmit,
                              leaveAccrual: false,
                            });
                          }}
                          error={Boolean(
                            touched?.accrualDays && errors?.accrualDays
                          )}
                          helperText={
                            touched?.accrualDays && errors?.accrualDays
                          }
                          isClearable={false}
                        />
                      </Box>
                      <Box>
                        <CustomSelect
                          id="accrualMonth"
                          name="accrualMonth"
                          placeholder="Month"
                          options={monthsOption}
                          value={
                            monthsOption?.find((opt) => {
                              return (
                                opt?.value ===
                                accrualTime?.reset_value?.[0]?.month
                              );
                            }) || ''
                          }
                          className="leave-select-wrap"
                          onChange={(e) => {
                            const resetTimes = accrualTime;
                            resetTimes.reset_value[0].month = e?.value;
                            setAccrualTime(resetTimes);
                            setLeavePolicySubmit({
                              ...leavePolicySubmit,
                              leaveAccrual: false,
                            });
                          }}
                          error={Boolean(
                            touched?.accrualMonth && errors?.accrualMonth
                          )}
                          helperText={
                            touched?.accrualMonth && errors?.accrualMonth
                          }
                          isClearable={false}
                        />
                      </Box>
                      <Box className="d-flex align-center gap-10">
                        <Typography className="title-text">
                          {isHour ? 'No. of Hours' : 'No. of Days'}
                        </Typography>
                        <Box>
                          <CustomTextField
                            fullWidth
                            id="accrualNoOfDays"
                            name="accrualNoOfDays"
                            placeholder="Days"
                            error={Boolean(
                              touched.accrualNoOfDays && errors.accrualNoOfDays
                            )}
                            helperText={
                              touched.accrualNoOfDays && errors.accrualNoOfDays
                            }
                            onBlur={handleBlur}
                            value={accrualTime?.reset_value?.[0]?.days}
                            onChange={(e) => {
                              const resetTimes = accrualTime;
                              resetTimes.reset_value[0].days = e?.target?.value;
                              setAccrualTime(resetTimes);
                              setLeavePolicySubmit({
                                ...leavePolicySubmit,
                                leaveAccrual: false,
                              });
                            }}
                            onInput={Inputype}
                            disabled={
                              values?.empcontract && isAnuual === 'true'
                            }
                          />
                        </Box>
                      </Box>
                    </Box>
                  )}
                  {values?.accrualTimewise === 'monthly' &&
                    accrualTime &&
                    accrualTime?.reset_value &&
                    accrualTime?.reset_value?.length > 0 &&
                    accrualTime?.reset_value?.map((month, index) => (
                      <Box
                        className="d-flex align-center justify-space-between gap-10 mb8"
                        key={index}
                      >
                        <CustomDropdown
                          type="accrual"
                          months={month} // Use the 'month' variable instead of accessing the array
                          // errors={errors}
                          // touched={touched}
                          accrualTime={accrualTime}
                          setAccrualTime={setAccrualTime}
                          dayOptions={dayOptions}
                          monthOptions={monthsOption}
                          onChange={() => {
                            setLeavePolicySubmit({
                              ...leavePolicySubmit,
                              leaveAccrual: false,
                            });
                          }}
                          index={index}
                          leaveType={leaveType}
                        />
                      </Box>
                    ))}

                  {values?.accrualTimewise === 'quarterly' &&
                    accrualTime &&
                    accrualTime?.reset_value &&
                    accrualTime?.reset_value?.length > 0 &&
                    accrualTime?.reset_value?.map((quarter, index) => {
                      const startIndex = [0, 3, 6, 9][index];
                      const endIndex = [3, 6, 9, 12][index];
                      return (
                        <Box
                          className="d-flex align-center justify-space-between gap-10 mb8"
                          key={index}
                        >
                          <CustomDropdown
                            type="accrual"
                            quarter={true}
                            months={quarter} // Pass index to determine "On the" / "And the"
                            month={{
                              value: monthsOption.slice(startIndex, endIndex),
                            }}
                            // errors={errors}
                            // touched={touched}
                            dayOptions={dayOptions}
                            monthOptions={monthsOption.slice(
                              startIndex,
                              endIndex
                            )}
                            onChange={() => {
                              setLeavePolicySubmit({
                                ...leavePolicySubmit,
                                leaveAccrual: false,
                              });
                            }}
                            index={index}
                            accrualTime={accrualTime}
                            setAccrualTime={setAccrualTime}
                            leaveType={leaveType}
                          />
                        </Box>
                      );
                    })}

                  {values?.accrualTimewise === 'half_yearly' &&
                    accrualTime &&
                    accrualTime?.reset_value &&
                    accrualTime?.reset_value?.length > 0 &&
                    accrualTime?.reset_value?.map((half, index) => {
                      const startIndex = index === 0 ? 0 : 6; // First half (Jan-Jun), Second half (Jul-Dec)
                      const endIndex = index === 0 ? 6 : 12;

                      return (
                        <Box
                          className="d-flex align-center justify-space-between gap-10 mb8"
                          key={index}
                        >
                          <CustomDropdown
                            type="accrual"
                            month={{
                              value: monthsOption.slice(startIndex, endIndex),
                            }}
                            quarter={true}
                            months={half}
                            // errors={errors}
                            // touched={touched}
                            dayOptions={dayOptions}
                            monthOptions={monthsOption.slice(
                              startIndex,
                              endIndex
                            )}
                            onChange={() => {
                              setLeavePolicySubmit({
                                ...leavePolicySubmit,
                                leaveAccrual: false,
                              });
                            }}
                            accrualTime={accrualTime}
                            setAccrualTime={setAccrualTime}
                            index={index}
                            leaveType={leaveType}
                          />
                        </Box>
                      );
                    })}
                </Box>
              </Box>
            </Box>
            <Box className="mt16">
              <Typography variant="h6" className="sub-content-text">
                Do you want to consider leave accrual based on
                Agreement/Compensation End Date?
              </Typography>
              <Box className="mt4">
                <Box>
                  <CustomRadio
                    name="considerLeaveAccrual"
                    value="yes"
                    checked={values?.considerLeaveAccrual === 'yes'}
                    onChange={(e) => {
                      setFieldValue('considerLeaveAccrual', e?.target?.value);
                      setLeavePolicySubmit({
                        ...leavePolicySubmit,
                        leaveAccrual: false,
                      });
                    }}
                    disableRipple
                    label={
                      <Typography className="sub-title-text">Yes</Typography>
                    }
                  />
                </Box>
                <Box>
                  <CustomRadio
                    name="considerLeaveAccrual"
                    value="no"
                    checked={values?.considerLeaveAccrual === 'no'}
                    onChange={(e) => {
                      setFieldValue('considerLeaveAccrual', e?.target?.value);
                      setLeavePolicySubmit({
                        ...leavePolicySubmit,
                        leaveAccrual: false,
                      });
                    }}
                    disableRipple
                    label={
                      <Typography className="sub-title-text">No</Typography>
                    }
                  />
                </Box>
                <Box>
                  <Box className="d-flex align-center gap-sm">
                    <CustomCheckbox
                      checked={values?.accrualBasedLeave}
                      onChange={(e) => {
                        setFieldValue('accrualBasedLeave', e?.target?.checked);
                        setLeavePolicySubmit({
                          ...leavePolicySubmit,
                          leaveAccrual: false,
                        });
                      }}
                      disableRipple
                      label={
                        <Typography className="sub-content-text">
                          Earned leave should be accrual based on working days.
                        </Typography>
                      }
                    />
                    <Tooltip
                      arrow
                      title={
                        <Typography>
                          If you select this option, the leave balance will be
                          accrued based on the previous or current month's
                          working days. Based on the slabs you defined
                        </Typography>
                      }
                      classes={{ tooltip: 'info-tooltip-container' }}
                    >
                      <InfoIcon className="info-icon cursor-pointer" />
                    </Tooltip>
                  </Box>
                  <Box className="d-flex align-center gap-sm">
                    <CustomCheckbox
                      checked={values?.restrictLeaveAccrual}
                      onChange={(e) => {
                        setFieldValue(
                          'restrictLeaveAccrual',
                          e?.target?.checked
                        );
                        setLeavePolicySubmit({
                          ...leavePolicySubmit,
                          leaveAccrual: false,
                        });
                      }}
                      disableRipple
                      label={
                        <Typography className="sub-content-text">
                          Do you want to restrict leave accrual on employee
                          exit?
                        </Typography>
                      }
                    />
                    <Tooltip
                      arrow
                      title={
                        <Typography>
                          Enable this option to restrict leave accrual when an
                          employee is marked for exit. Once enabled, no
                          additional leave will be accrued after the exit date
                          is set.
                        </Typography>
                      }
                      classes={{ tooltip: 'info-tooltip-container' }}
                    >
                      <InfoIcon className="info-icon cursor-pointer" />
                    </Tooltip>
                  </Box>
                </Box>
              </Box>
            </Box>
            <Box className="mt16">
              <Typography className="sub-content-text">
                Prorate Accrual
              </Typography>
              <Box className="mt4">
                <Box className="d-flex align-center gap-sm">
                  <CustomRadio
                    disableRipple
                    name="prorateAccrual"
                    value="pro_rate"
                    checked={values?.prorateAccrual === 'pro_rate'}
                    onChange={(e) => {
                      setFieldValue('prorateAccrual', e?.target?.value);
                      setLeavePolicySubmit({
                        ...leavePolicySubmit,
                        leaveAccrual: false,
                      });
                    }}
                    label={
                      <Typography className="sub-content-text">
                        Pro rate
                      </Typography>
                    }
                  />
                  <Tooltip
                    arrow
                    title={
                      <Typography>
                        If an employee is joining in between a leave calendar
                        year, the leave will be prorated and allocated
                        accordingly. (i.e.) If annual quota is 12 and the
                        employee joins between 1st to 15th of June, the leave
                        allocation will be 7 (100% for a current month), if the
                        employee joins between 16th to 31st, the leave
                        allocation will be 6.5 (50% for a current month).
                      </Typography>
                    }
                    classes={{ tooltip: 'info-tooltip-container' }}
                  >
                    <InfoIcon className="info-icon cursor-pointer" />
                  </Tooltip>
                </Box>
                <Box className="d-flex align-center gap-sm">
                  <CustomRadio
                    disableRipple
                    name="prorateAccrual"
                    value="non_pro_rate"
                    checked={values?.prorateAccrual === 'non_pro_rate'}
                    onChange={(e) => {
                      setFieldValue('prorateAccrual', e?.target?.value);
                      setLeavePolicySubmit({
                        ...leavePolicySubmit,
                        leaveAccrual: false,
                      });
                    }}
                    label={
                      <Typography className="sub-content-text">
                        Do not Pro rate
                      </Typography>
                    }
                  />
                  <Tooltip
                    arrow
                    classes={{ tooltip: 'info-tooltip-container' }}
                    title={
                      <Typography>
                        Employees will be granted all entitled leave regardless
                        of the policy's start date.
                      </Typography>
                    }
                  >
                    <InfoIcon className="info-icon cursor-pointer" />
                  </Tooltip>
                </Box>
              </Box>
            </Box>
          </>
        ) : (
          <></>
        )}
      </Box>
    </>
  );
}
