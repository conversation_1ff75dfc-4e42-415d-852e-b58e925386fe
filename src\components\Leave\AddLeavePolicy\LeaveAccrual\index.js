import React, { useEffect, useContext } from 'react';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { Box, Typography, Tooltip } from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import AuthContext from '@/helper/authcontext';
import CustomCheckbox from '@/components/UI/CustomCheckbox';
import BasicInfoSection from './BasicInfoSection';
import LeaveEntitlementSection from './LeaveEntitlementSection';
import AccrualSection from './AccrualSection';
import ResetSection from './ResetSection';
import './leaveaccrual.scss';

export default function LeaveAccrual({
  leaveTypeList,
  setLeavePolicyDetails,
  leavePolicyDetails,
  formikRefAccrual,
  setLeavePolicySubmit,
  leavePolicySubmit,
  LeaveAccrualPayload,
  random,
  setRandom,
  encashment,
  setEncashment,
  resetTime,
  setResetTime,
  accrualTime,
  setAccrualTime,
  leavePolicyBack,
  setLeavePolicyBack,
  id,
  leaveType,
  isAnuual,
}) {
  const { authState } = useContext(AuthContext);
  const isHour =
    authState?.generalSeetings?.leave_period_type &&
    authState?.generalSeetings?.leave_period_type === 'hour'
      ? true
      : false;
  const leaveAccrual = leavePolicyDetails?.leaveAccrual || {};
  const encashmentValue =
    leaveAccrual?.allow_for_anytime_encashment_value || {};
  const carryForwardDate = leaveAccrual?.carry_forward_date || {};

  useEffect(() => {
    setEncashment(encashment);
  }, [random]);

  useEffect(() => {
    const accrual = leavePolicyDetails?.leaveAccrual;
    if (!accrual) return;

    const stopAccrualTimewiseValue =
      accrual?.stop_policy_accural_timewise_value;
    const leavePolicyResetValue = accrual?.leave_policy_reset_value;
    const encashmentExceedData =
      accrual?.allow_for_anytime_encashment_value?.encashment_exceed_data;

    if (
      Array.isArray(stopAccrualTimewiseValue) &&
      stopAccrualTimewiseValue.length > 0
    ) {
      setAccrualTime({
        type: accrual?.stop_policy_accural_timewise_type || 'yearly',
        reset_value: stopAccrualTimewiseValue,
      });
    }
    if (
      Array.isArray(leavePolicyResetValue) &&
      leavePolicyResetValue.length > 0
    ) {
      setResetTime({
        type: accrual?.leave_policy_reset_type || 'yearly',
        reset_value: leavePolicyResetValue,
      });
    }
    if (
      Array.isArray(encashmentExceedData) &&
      encashmentExceedData.length > 0
    ) {
      setEncashment(encashmentExceedData);
    }
  }, [leavePolicyDetails?.leaveAccrual]);

  useEffect(() => {
    setLeavePolicyBack({
      ...leavePolicyBack,
      leaveAccrual: false,
    });
    setLeavePolicySubmit({
      ...leavePolicySubmit,
      leaveAccrual: false,
    });
  }, []);
  const Inputype = (e) => {
    let value = e.target.value;
    if (isHour) {
      {
        // Remove non-numeric characters
        if (value === '' || /^\d*\.?\d{0,2}$/.test(value)) {
          e.target.value = value;
        } else {
          e.target.value = value.slice(0, -1);
        }
      }
    } else {
      {
        e.target.value = e.target.value.replace(/[^0-9]/g, ''); // Remove non-numeric characters
      }
    }
  };

  return (
    <Formik
      innerRef={formikRefAccrual}
      enableReinitialize
      initialValues={{
        policyName: leaveAccrual.leave_policy_name || '',
        leaveType: id || leaveAccrual.leave_type_id || '',
        leaveCalendarStartYear:
          leaveAccrual.leave_calender_year_start_from || '',
        leaveCalendarEndYear: leaveAccrual.leave_policy_end_date || '',
        description: leaveAccrual.leave_policy_description || '',
        defaultLeavePolicy: leaveAccrual.has_leave_policy_default || false,
        salaryDed: leaveAccrual.salary_deduction_hours ?? 1,
        effectiveDays: leaveAccrual.effective_after_count ?? 0,
        effectiveTimePeriod: leaveAccrual.effective_after_type || 'days',
        effectiveFrom: leaveAccrual.effective_from_type || 'date_of_joining',
        newEmpLeaveDay: leaveAccrual.new_employee_leave_entitlement_count ?? 0,
        newEmpLeaveType:
          leaveAccrual.new_employee_leave_entitlement_type || 'days',
        newEmpLeaveTheirType:
          leaveAccrual.new_employee_leave_entitlement_their_type ||
          'date_of_joining',
        probation: leaveAccrual.has_employee_leave_in_probation || false,
        probationLimit: leaveAccrual.probation_leave_days_count ?? 0,
        accrual:
          leaveAccrual.leave_policy_accural === false ||
          leaveAccrual.leave_policy_accural === true
            ? leaveAccrual.leave_policy_accural
            : true,
        stopaccural: leaveAccrual.stop_policy_accural || false,
        stopaccurallimit: leaveAccrual.stop_policy_accural_limit ?? 0,
        accrualTimewise:
          leaveAccrual.stop_policy_accural_timewise_type || 'yearly',
        considerLeaveAccrual:
          leaveAccrual.leave_accural_based_on_contract === true
            ? 'yes'
            : leaveAccrual.leave_accural_based_on_contract === false
              ? 'no'
              : 'yes',
        prorateAccrual: leaveAccrual.pro_rate_accural || 'pro_rate',
        reset:
          leaveAccrual.leave_policy_reset === false ||
          leaveAccrual.leave_policy_reset === true
            ? leaveAccrual.leave_policy_reset
            : true,
        resetTimePeriod: leaveAccrual.leave_policy_reset_type || 'yearly',
        encashOrCarryForward:
          leaveAccrual.prioritize_encasment_carry_forward || 'encashment',

        // Carry Forward
        carryForwardDays: carryForwardDate.carry_forward_number || 0,
        carryForwardPercentage:
          carryForwardDate.carry_forward_number_by_type || 'percentage',
        carryForwardMaxLimit: carryForwardDate.carry_forward_max_limit ?? 0,
        carryForwardMinLimit: carryForwardDate.carry_forward_min_limit ?? 0,

        // Encashment
        encashmentDays: encashmentValue.encashment_number || 0,
        encashmentPercentage:
          encashmentValue.encashment_number_by_type || 'percentage',
        encashmentMaxLimit: encashmentValue.encashment_max_limit ?? 0,
        encashmentMinLimit: encashmentValue.encashment_min_limit ?? 0,
        fieldArray:
          Array.isArray(encashmentValue.encashment_exceed_data) &&
          encashmentValue.encashment_exceed_data.length > 0
            ? encashmentValue.encashment_exceed_data
            : [
                {
                  encashment_number: '',
                  encashment_max_limit: '',
                  encashment_leave_balance_exceeds: '',
                },
              ],
        deductEncashAmount: leaveAccrual.deduct_encashment_amount ?? 0,
        leavePaymentMethod:
          leaveAccrual.perquisite_value_calculated || 'fixed_amount',
        encashmentMultiplier: leaveAccrual.encashment_multiply_by_number ?? 1,
        encashmentRestriction:
          encashmentValue.restrict_encashment_emp_notice_period || false,
        allowEncashmentAnytime:
          leaveAccrual.allow_for_anytime_encashment || false,
        accrualBasedLeave: leaveAccrual.earned_leave_accural_based || false,
        restrictLeaveAccrual:
          leaveAccrual.restrict_leave_accural_on_emp_exit || false,
        encashMentAllowed: encashmentValue.encashment_allowed || false,
        encashMentAllowedDays: encashmentValue.encashment_count ?? 0,
        encashMentAllowedIn: encashmentValue.encashment_time || 'week',
        encashmentAllowedPeriods:
          encashmentValue.encashment_submitted_between_period || false,
        encashmentAllowedStart:
          encashmentValue.encashment_submitted_start_month ?? 1,
        encashmentAllowedEnd:
          encashmentValue.encashment_submitted_end_month ?? 1,
        employeesInProbation:
          encashmentValue.probation_period_user_cannot_encashment || false,
        employeeCanEncash:
          encashmentValue.encashment_number_by_type || 'percentage',
        encashmentRequestApproval: encashmentValue.encashment_require_approval
          ? 'yes'
          : 'no',
        empcontract:
          leaveAccrual.leave_balance_based_on_emp_contract === true ||
          leaveAccrual.leave_balance_based_on_emp_contract === false
            ? leaveAccrual.leave_balance_based_on_emp_contract
            : isAnuual === 'true',
      }}
      validationSchema={Yup.object().shape({
        policyName: Yup.string().required('This field is required'),
        leaveType: Yup.string().required('This field is required'),
        leaveCalendarStartYear: Yup.string().required('This field is required'),
        stopaccurallimit: Yup.lazy((value, context) => {
          const past = context.parent.stopaccural;
          return past === true
            ? Yup.string()
                .required('This field is required')
                .test(
                  'not-zero',
                  'Leave balance cannot be 0',
                  (val) => !/^[0]+$/.test(val)
                )
            : Yup.string().nullable().notRequired();
        }),
      })}
      onSubmit={(values) => {
        setLeavePolicyDetails((prevState) => ({
          ...prevState,
          leaveAccrual: LeaveAccrualPayload(values),
        }));
      }}
    >
      {({
        values,
        errors,
        touched,
        handleChange,
        handleBlur,
        setFieldValue,
      }) => {
        return (
          <Form>
            <Box className="leave-accrual-container">
              <BasicInfoSection
                values={values}
                handleChange={handleChange}
                handleBlur={handleBlur}
                touched={touched}
                errors={errors}
                setFieldValue={setFieldValue}
                leaveTypeList={leaveTypeList}
                leavePolicySubmit={leavePolicySubmit}
                setLeavePolicySubmit={setLeavePolicySubmit}
                Inputype={Inputype}
                isHour={isHour}
              />
              <LeaveEntitlementSection
                values={values}
                handleChange={handleChange}
                handleBlur={handleBlur}
                touched={touched}
                errors={errors}
                setFieldValue={setFieldValue}
                leavePolicySubmit={leavePolicySubmit}
                setLeavePolicySubmit={setLeavePolicySubmit}
                Inputype={Inputype}
              />
              <AccrualSection
                values={values}
                handleChange={handleChange}
                handleBlur={handleBlur}
                touched={touched}
                errors={errors}
                setFieldValue={setFieldValue}
                leavePolicySubmit={leavePolicySubmit}
                setLeavePolicySubmit={setLeavePolicySubmit}
                Inputype={Inputype}
                isAnuual={isAnuual}
                accrualTime={accrualTime}
                setAccrualTime={setAccrualTime}
                isHour={isHour}
                setResetTime={setResetTime}
                leaveType={leaveType}
              />

              <Box className="mt16">
                <Box className="d-flex align-center gap-10">
                  <CustomCheckbox
                    checked={values?.reset}
                    onChange={(e) => {
                      setFieldValue('reset', e?.target?.checked);
                      setResetTime({
                        type: 'yearly',
                        reset_value: [
                          {
                            on_date: 1,
                            month: 1,
                          },
                        ],
                      });
                      setEncashment([
                        {
                          encashment_number: '',
                          encashment_max_limit: '',
                          encashment_leave_balance_exceeds: '',
                        },
                      ]);
                      setFieldValue('fieldArray', [
                        {
                          encashment_number: '',
                          encashment_max_limit: '',
                          encashment_leave_balance_exceeds: '',
                        },
                      ]);
                      setFieldValue('resetTimePeriod', 'yearly');
                      setFieldValue('encashOrCarryForward', 'encashment');
                      setFieldValue('deductEncashAmount', false);
                      setFieldValue('leavePaymentMethod', 'fixed_amount');
                      setFieldValue('encashmentMultiplier', 1);
                      setFieldValue('encashmentRestriction', false);
                      setFieldValue('allowEncashmentAnytime', false);
                      setFieldValue('encashMentAllowed', false);
                      setFieldValue('encashMentAllowedDays', 0);
                      setFieldValue('encashMentAllowedIn', 'week');
                      setFieldValue('encashmentAllowedPeriods', false);
                      setFieldValue('encashmentAllowedStart', 1);
                      setFieldValue('encashmentAllowedEnd', 1);
                      setFieldValue('employeesInProbation', false);
                      setFieldValue('employeeCanEncash', 'percentage');
                      setFieldValue('encashmentRequestApproval', 'no');
                      setFieldValue('carryForwardDays', 0);
                      setFieldValue('carryForwardPercentage', 'percentage');
                      setFieldValue('carryForwardMaxLimit', 0);
                      setFieldValue('carryForwardMinLimit', 0);
                      setFieldValue('encashmentDays', 0);
                      setFieldValue('encashmentPercentage', 'percentage');
                      setFieldValue('encashmentMinLimit', 0);
                      setFieldValue('encashmentMaxLimit', 0);
                      setLeavePolicySubmit({
                        ...leavePolicySubmit,
                        leaveAccrual: false,
                      });
                    }}
                    disableRipple
                    label={
                      <Typography className="sub-content-text">
                        Reset
                      </Typography>
                    }
                  />
                  <Tooltip
                    arrow
                    title={
                      <Typography>
                        Reset determines the action to reset the leave balance
                        and also sets the intervals for the reset to happen.
                        Enabling reset means you want the leave balance to lapse
                        within the defined interval. The unused balance can be
                        carried forward and/or encashed when the reset is
                        enabled. Disabling reset means the leave balance will
                        accrue and not lapse.
                      </Typography>
                    }
                    classes={{ tooltip: 'info-tooltip-container' }}
                  >
                    <InfoIcon className="info-icon cursor-pointer" />
                  </Tooltip>
                </Box>
                {values?.reset ? (
                  <>
                    <ResetSection
                      values={values}
                      handleChange={handleChange}
                      handleBlur={handleBlur}
                      touched={touched}
                      errors={errors}
                      setFieldValue={setFieldValue}
                      leavePolicySubmit={leavePolicySubmit}
                      setLeavePolicySubmit={setLeavePolicySubmit}
                      Inputype={Inputype}
                      isAnuual={isAnuual}
                      resetTime={resetTime}
                      setResetTime={setResetTime}
                      encashment={encashment}
                      setRandom={setRandom}
                      setEncashment={setEncashment}
                      leaveType={leaveType}
                    />
                  </>
                ) : (
                  <></>
                )}
              </Box>
            </Box>
          </Form>
        );
      }}
    </Formik>
  );
}
