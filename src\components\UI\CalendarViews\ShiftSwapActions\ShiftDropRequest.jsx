'use client';

import React, { Fragment, useState } from 'react';
import { Badge, Divider } from '@mui/material';
import moment from 'moment';
import SupervisorAccountOutlinedIcon from '@mui/icons-material/SupervisorAccountOutlined';
import ChatOutlinedIcon from '@mui/icons-material/ChatOutlined';
import BlockIcon from '@mui/icons-material/Block';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstanceOrg from '@/helper/axios/axiosInstanceOrg';
import { ROTA_URLS } from '@/helper/constants/urls';
import CustomButton from '../../CustomButton';
import UserAvatar from '../../Avatar/UserAvatar';
import PreLoader from '../../Loader';
import { fetchFromStorage } from '@/helper/context';
import { identifiers } from '@/helper/constants/identifier';
import './shiftSwapActions.scss';

export const ShiftDropRequest = ({
  selectedShiftData,
  viewAccessOnly,
  getRotaShiftList,
  handleAddShiftTooltipClose,
}) => {
  const authdata = fetchFromStorage(identifiers?.AUTH_DATA);
  const [isLoading, setIsLoading] = useState(false);
  const [shiftNote, setShiftNote] = useState('');

  const dateFormat = (date) => {
    return moment(date).startOf().fromNow();
    // moment(date).calendar(null, {
    //   sameDay: '[Today at] HH:mm',
    //   nextDay: '[Tomorrow at] HH:mm',
    //   nextWeek: 'dddd [at] HH:mm',
    //   lastDay: '[Yesterday at] HH:mm',
    //   lastWeek: '[Last] dddd [at] HH:mm',
    //   sameElse: 'DD/MM/YYYY HH:mm',
    // });
  };

  const handleShiftDropAction = async (dropId, isStatus) => {
    const sendData = {
      status: isStatus,
    };

    setIsLoading(true);
    try {
      const { status, data } = await axiosInstanceOrg.put(
        ROTA_URLS?.DROP_ACTION_URL + `/${dropId}`,
        sendData
      );
      if (status === 200 || status === 201) {
        setApiMessage('success', data?.message);
        setIsLoading(false);
        getRotaShiftList();
        handleAddShiftTooltipClose();
        setIsLoading(false);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setIsLoading(false);
    }
  };

  return (
    <>
      {isLoading && <PreLoader />}
      {selectedShiftData?.drops
        ?.filter((item) => item?.status === 'pending')
        ?.map((item, index) => {
          const isRequested = authdata?.id === item?.toUserId;

          const isActive =
            item?.status === 'active' && item?.adminStatus === 'active';

          return (
            <Fragment key={index}>
              {isRequested || viewAccessOnly ? (
                <>
                  <div className="swap-request-details-wrapper" key={index}>
                    <div className="swap-request-details">
                      <div className="swap-request-user-details">
                        <div>
                          <Badge
                            overlap="circular"
                            anchorOrigin={{
                              vertical: 'bottom',
                              horizontal: 'right',
                            }}
                            badgeContent={
                              <BlockIcon className="swap-request-badge-icon" />
                            }
                          >
                            <UserAvatar
                              name={selectedShiftData?.user?.user_full_name}
                              src={selectedShiftData?.user?.user_avatar_link}
                              classname="swap-request-avatar"
                            />
                          </Badge>
                        </div>
                        <div className="swap-request-name-time">
                          <p className="swap-request-name">
                            Shift drop request
                          </p>
                          <p className="swap-request-time">
                            {dateFormat(item?.createdAt)}
                          </p>
                        </div>
                      </div>
                      <div className="swap-request-shift-details">
                        <p className="swap-request-shift-text">
                          "{item?.reason}"
                        </p>
                      </div>
                    </div>
                  </div>
                  <div>
                    {/* Shift Drop Request Note */}
                    <div className="shift-drop-request-note-row">
                      <div className="icon">
                        <ChatOutlinedIcon />
                      </div>
                      <div className="shift-drop-request-note-input-wrapper">
                        <input
                          as="input"
                          name="shiftNote"
                          placeholder={`Leave ${item?.createdBy?.user_first_name} a message (optional)`}
                          className="shift-drop-request-note-input"
                          value={shiftNote}
                          onChange={(e) => setShiftNote(e.target.value)}
                          maxLength={1500}
                        />
                      </div>
                    </div>
                    {/* Shift Note Char Count */}

                    <div className="shift-drop-request-note-char-count">
                      <div className="shift-drop-request-note-visibility-container">
                        <p className="char-count">{shiftNote.length} / 1500</p>
                      </div>
                    </div>
                  </div>
                  <div className="swap-request-button">
                    <CustomButton
                      variant="outlined"
                      className="swap-request-btn cancel-cover-request-btn"
                      title="Deny"
                      onClick={() =>
                        handleShiftDropAction(item?.shiftId, 'deleted')
                      }
                    />

                    <CustomButton
                      className="swap-request-btn accept-cover-request-btn"
                      title="Approve"
                      onClick={() =>
                        handleShiftDropAction(item?.shiftId, 'active')
                      }
                    />
                  </div>
                  <Divider />
                  {selectedShiftData?.swapRequests?.length > 0 && (
                    <div className="swap-request-history">
                      <p className="swap-request-history-text">
                        Request history
                      </p>
                      {selectedShiftData?.swapRequests?.map((item, index) => {
                        return (
                          <div
                            className="swap-request-history-details"
                            key={index}
                          >
                            <div>
                              <SupervisorAccountOutlinedIcon
                                className={`swap-request-history-badge-icon ${item?.status === 'rejected' ? 'isRejected' : ''}`}
                              />
                            </div>
                            <div className="swap-request-history-name-time">
                              <p className="swap-request-history-name">
                                {item?.from?.user_first_name}'s shift cover{' '}
                                {item?.status === 'rejected'
                                  ? 'denied'
                                  : item?.status === 'active'
                                    ? 'approved'
                                    : 'pending'}
                              </p>
                              <p className="swap-request-history-time">
                                {dateFormat(item?.createdAt)}
                              </p>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </>
              ) : (
                <>
                  <div className="swap-request-details-wrapper" key={index}>
                    <div className="swap-request-details">
                      <div className="swap-request-user-details">
                        <div>
                          <Badge
                            overlap="circular"
                            anchorOrigin={{
                              vertical: 'bottom',
                              horizontal: 'right',
                            }}
                            badgeContent={
                              <BlockIcon className="swap-request-badge-icon" />
                            }
                          >
                            <UserAvatar
                              name={selectedShiftData?.user?.user_full_name}
                              src={selectedShiftData?.user?.user_avatar_link}
                              classname="swap-request-avatar"
                            />
                          </Badge>
                        </div>
                        <div className="swap-request-name-time">
                          <p className="swap-request-name">
                            Drop request{' '}
                            <span className="swap-request-status">
                              {isActive ? 'active' : 'pending'}
                            </span>
                          </p>
                          <p className="swap-request-time">
                            {dateFormat(item?.createdAt)}
                          </p>
                        </div>
                      </div>
                      <div className="swap-request-shift-details">
                        <p className="swap-request-shift-text">
                          You've asked to drop your shift,{' '}
                          <b>pending admin approval.</b>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="swap-request-button" />
                </>
              )}
            </Fragment>
          );
        })}
    </>
  );
};
