@import '@/styles/variable.scss';

.leave-policy-type {
  .leave-policy-table {
    margin-top: 0px;
    .search-filter-section {
      column-gap: 20px;
      margin-bottom: 20px;
      .filters-wrap {
        gap: 10px;
        width: 50%;
        .staff-leave-search {
          width: 100% !important;
          max-width: 250px !important;
          .MuiInputBase-root {
            min-height: 30px;
            border-radius: 4px !important;
            .MuiInputBase-input {
              padding: 2px 10px !important;
              &::placeholder {
                font-size: 14px;
              }
            }
            .MuiOutlinedInput-notchedOutline {
              border-color: none !important;
              border-color: $color-Dark-10 !important;
            }
          }
          @media (max-width: 575px) {
            max-width: 100% !important;
          }
        }
        .select-box {
          width: 100%;
          max-width: 250px;
          .MuiSelect-select {
            font-size: 15px;
            padding: 2px 7px 2px 15px;
            margin-top: 0px !important;
          }

          fieldset {
            height: 30px !important;
            border-radius: 4px !important;
            margin-top: 3px !important;
          }

          .MuiSvgIcon-root {
            margin-top: 1px;
          }

          .placeholder {
            margin-top: 2px;
            font-family: Inter, sans-serif;
            font-size: 15px;
            font-weight: 300 !important;
          }
          @media (max-width: 575px) {
            max-width: 100%;
          }
        }

        @media (max-width: 1199px) {
          width: 70%;
        }
        @media (max-width: 991px) {
          width: 100%;
        }
        @media (max-width: 575px) {
          flex-wrap: wrap;
        }
      }
      .buttons-wrap {
        gap: 10px;
        .apply-btn {
          padding: 3.5px 16px !important;
          background-color: $color-White !important;
          color: $color-primary !important;
          &:hover {
            background-color: $color-primary !important;
            color: $color-White !important;
          }

          @media (max-width: 1080px) {
            width: 100% !important;
            min-width: 108px !important;
          }
        }
        .add-icon-wrap {
          line-height: 0px;
          padding: 3.5px;
          height: 30px;
          width: 40px;
          border: 1px solid $color-primary;
          border-radius: 8px;
          background-color: $color-primary;
          .add-icon {
            fill: $color-White;
            height: 21px;
            width: 21px;
          }

          &:hover {
            background-color: $color-White;
            box-shadow:
              0px 10px 30px 0px rgba(0, 0, 0, 0.02),
              0px 20px 30px 0px rgba(0, 0, 0, 0.1);
            .add-icon {
              fill: $color-primary;
              height: 21px;
              width: 21px;
            }
          }
        }
        .clear-filter-icon-wrap {
          line-height: 0px;
          padding: 3.5px;
          height: 30px;
          width: 40px;
          border: 1px solid $color-primary;
          border-radius: 8px;
          background-color: $color-White;

          .clear-filter-icon {
            fill: $color-primary;
            height: 21px;
            width: 21px;
          }

          &:hover {
            background-color: $color-primary;
            box-shadow:
              0px 10px 30px 0px rgba(0, 0, 0, 0.02),
              0px 20px 30px 0px rgba(0, 0, 0, 0.1);
            .clear-filter-icon {
              fill: $color-White;
              height: 21px;
              width: 21px;
            }
          }
        }
      }

      .filter-icon {
        border: 1px solid $color-Black;
        border-radius: 3px;
        width: 25px;
        height: 25px;
        cursor: pointer;
      }
      @media (max-width: 1080px) {
      }
      @media (max-width: 799px) {
        column-gap: 15px;
        .staff-leave-search {
          max-width: calc(100% - 350px) !important;
        }
      }
      @media (max-width: 599px) {
        display: flex;
        flex-wrap: wrap;
        row-gap: 25px;
        .staff-leave-search {
          width: 85% !important;
          max-width: 85% !important;
        }
        .filter-icon {
          max-width: 15%;
        }
        button {
          width: 45%;
        }
      }
      @media (max-width: 599px) {
        row-gap: 15px;
        button {
          width: 100%;
        }
      }
    }

    .leave-policy-pagination {
      border-top: none;
    }
  }
}
.create-policy-type {
  background-color: $color-White;
  padding: 26px 15px;
  border-radius: 12px;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.05);
  .display-grid {
    display: grid;
    grid-template-columns: 32% 32% 32%;
    column-gap: 15px;
    row-gap: 15px;
    align-items: flex-start;
    @media (max-width: 899px) {
      display: block;
    }
  }
  .select-wrap {
    .MuiInputBase-root {
      min-height: 48px;
      margin-top: 2px;
      .MuiSelect-select {
        margin-top: 0px;
      }
    }
    fieldset {
      margin-top: 0px;
    }
    .MuiOutlinedInput-notchedOutline {
      top: 0;
    }
  }
  .anunal-leave-checkbox {
    .Mui-disabled {
      color: $color-Black !important;
    }
    .Mui-disabled.MuiCheckbox-root {
      color: $color-primary !important;
    }
  }
  .holiday-leave-check {
    display: flex;
    flex-direction: row;
    align-items: center;
    .info-icon {
      width: 18px;
      height: 18px;
    }
    .MuiFormControlLabel-root {
      margin-right: 0 !important;
    }
  }
  .leave-calendar-input {
    height: fit-content;
    .MuiInputBase-root {
      .MuiInputBase-input {
        font-size: 14px;
        padding: 2px 14px;
        &::placeholder {
          margin-top: 2px;
          font-family: Inter, sans-serif;
          font-size: 14px;
          font-weight: 300 !important;
        }
      }
      fieldset {
        min-height: 36px;
        border-radius: 8px !important;
      }
      .MuiSvgIcon-root {
        margin-top: 2px;
      }
      .MuiButtonBase-root {
        padding: 0 8px;
      }
    }
  }
  .holiday-color-picker {
    .color-picker-box {
      margin-top: 2px;
    }
    .color-box {
      width: 18px;
      height: 18px;
    }
  }
  .Upload-holidays {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
  }
  .chip-selected {
    background-color: #89cefb4d;
    border-color: $color-primary;
  }

  .leave-type {
    line-height: 14px;
    .chip-container {
      gap: 10px;
    }
    .leave-type-text {
      font-size: 13px;
    }
  }
  .save-btn-wrap {
    .save-btn {
      padding: 5px 20px !important;
      width: 100% !important;
      max-width: 80px;
    }
  }
  .add-leave-wrap {
    .input-wrap {
      .MuiInputBase-root {
        min-height: 35px !important;

        .MuiInputBase-input {
          padding: 5px 14px !important;
          font-size: 14px !important;
          &::placeholder {
            font-size: 14px;
          }
        }
      }
    }
    .select-box {
      .MuiInputBase-root {
        min-height: 35px !important;
        margin-top: 0px;
        .MuiInputBase-input {
          padding: 4px 14px !important;
          font-size: 14px;
        }
        .MuiOutlinedInput-notchedOutline {
          max-height: 35px;
        }
      }
    }
    .edit-color-picker-wrap {
      margin-top: 6px;
      .color-picker-box {
        padding: 6px 14px !important;
        .MuiTypography-root {
          font-size: 14px;
          font-family: 'Poppins', sans-serif !important;
          font-weight: 300;
        }
      }
    }
    .chromePicker-fore {
      position: absolute;
      bottom: auto !important;
      z-index: 9999999;
      top: 60px !important;
    }
  }
  .color-picker-box {
    padding: 6px 12px;
  }
}

.anual-status {
  // min-width: 75px;

  margin-left: 10px;
  min-width: 0 !important;

  .category-draft {
    padding: 1px 8px 1.5px 4px;
    border-radius: 3px;
    background-color: $color-primary;
    color: $color-White;
  }
  // .category-ongoing {
  //   // border: 1px solid $color-Success;
  //   padding: 1.5px 8px;
  //   border-radius: 3px;
  //   color: $color-White;
  //   background-color: $color-Success;
  // }
  // .category-accepted {
  //   // border: 1px solid $color-dark-green;
  //   padding: 1.5px 8px;
  //   border-radius: 3px;
  //   color: $color-White;
  //   background-color: $color-dark-green;
  // }
}
.anual {
  width: 100%;
  .anual-leave-text-sec {
    max-width: calc(100% - 65px);
    .anual-leave-text {
      width: calc(100%);
    }
  }
}
