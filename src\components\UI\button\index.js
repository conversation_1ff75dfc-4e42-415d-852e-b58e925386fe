import React from 'react';
import { Button, Tooltip, Typography } from '@mui/material';
import './button.scss';

const CustomButton = ({
  variant,
  title,
  onClick,
  leftIcon,
  rightIcon,
  background,
  color,
  fontWeight,
  className,
  type,
  backgroundhover,
  fullWidth,
  isLogin,
  colorhover,
  disabled,
  tooltipTitle,
  ...props
}) => {
  return (
    <Tooltip title={tooltipTitle} arrow>
      <Button
        className={`custom-button ${className || ''}`}
        fullWidth={fullWidth}
        variant={variant}
        type={type}
        onClick={onClick}
        disabled={disabled ? true : false}
        sx={{
          background: background,
          color: color,
          fontWeight: fontWeight,
          border: `1px solid ${background}`,
          '&:hover': {
            background: backgroundhover,
            color: isLogin
              ? `${colorhover} !important`
              : `${background} !important`,
          },
          '&.Mui-disabled': {
            color: color,
            backgroundColor: background,
          },
        }}
        {...props}
      >
        {leftIcon && leftIcon}
        <Typography variant="span" className="button-text">
          {title}
        </Typography>
        {rightIcon && rightIcon}
      </Button>
    </Tooltip>
  );
};

export default CustomButton;
