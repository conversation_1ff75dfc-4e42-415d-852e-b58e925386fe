'use client';

import React, { useContext, useEffect, useState } from 'react';
import { Box, Typography, Tooltip, Divider } from '@mui/material';
import AuthContext from '@/helper/authcontext';
import { DataGrid } from '@mui/x-data-grid';
import AddIcon from '@mui/icons-material/Add';
import CustomButton from '@/components/UI/CustomButton';
import DialogBox from '@/components/UI/Modalbox';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import { useRouter, useSearchParams } from 'next/navigation';
import { removeFromStorage, fetchFromStorage } from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import EditIcon from '@/components/ActionIcons/EditIcon';
import DeleteIcon from '@/components/ActionIcons/DeleteIcon';
import DeleteModal from '@/components/UI/DeleteModal';
import CustomSearch from '@/components/UI/CustomSearch';
import PreLoader from '@/components/UI/Loader';
import ContentLoader from '@/components/UI/ContentLoader';
import NoDataView from '@/components/UI/NoDataView';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import CustomOrgPagination from '@/components/UI/customPagination';
import AddEditBranch from './AddEditBranch';
import { branchService } from '@/services/branchService';
// import BranchDepartmentDisplay from '@/components/BranchDepartmentDisplay';
// import './branch.scss';

export default function Branch() {
  const router = useRouter();
  const { userdata, setAllListsData, AllListsData } = useContext(AuthContext);
  const searchParams = useSearchParams();
  const isEdit = searchParams.get('is_edit');
  const [actionLoader, setActionLoader] = useState(false);
  const { authState } = useContext(AuthContext);
  const [updateItem, setUpdateItem] = useState();
  const [branchList, setBranchList] = useState([{ id: '' }]);
  const [loader, setLoader] = useState(true);
  const [searchValue, setSearchValue] = useState('');
  const [page, setPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteId, setDeleteId] = useState(null);
  const queryParams = new URLSearchParams(searchParams);

  const handleEdit = (item) => {
    setUpdateItem(item?.row);
    queryParams.set('is_edit', 'true');
    queryParams.set('branch_id', item?.row?.id);
    router.push(`?${queryParams.toString()}`);
  };

  const handleCreateBranch = () => {
    setUpdateItem(null);
    queryParams.set('is_edit', 'false');
    router.push(`?${queryParams.toString()}`);
  };

  const handleOpenDeleteDialog = (id) => {
    setDeleteId(id);
    setDeleteDialogOpen(true);
  };
  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setDeleteId(null);
  };
  const getBranchAndDepartmentLists = async () => {
    try {
      const branchAndDeptData = await branchService.getActiveBranchList();

      setAllListsData({
        ...AllListsData,
        SelectBranchList: branchAndDeptData?.selectBranchList,
        ActiveBranchList: branchAndDeptData?.activeBranchList,
      });
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // delete branch
  const deletebranch = async (id) => {
    try {
      setActionLoader(true);
      const { status, data } = await axiosInstance.delete(
        URLS?.DELETE_BRANCH + id
      );
      if (status === 200) {
        if (data.status) {
          handleCloseDeleteDialog();
          if (branchList?.length === 1 && page !== 1) {
            getBranchList(searchValue, Number(page) - 1);
            setPage(Number(page) - 1);
          } else {
            getBranchList(searchValue, page);
          }
          getBranchAndDepartmentLists();
          setApiMessage('success', data?.message);
        } else {
          setApiMessage('error', data?.message);
        }
      } else {
        setApiMessage('error', data?.message);
      }
      setActionLoader(false);
    } catch (error) {
      setActionLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const convertToHtml = (str) => {
    // Replace \n with <br/> and \r with empty string
    return str
      ?.replace(/(\r\n|\n|\r)/gm, '<br/>')
      ?.replace(/\\n/g, '<br/>')
      ?.replace(/\\r/g, '');
  };

  const columns = [
    {
      field: 'id',
      headerName: 'ID',
      width: 48,
      minWidth: 48,
      flex: 0,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
    },
    {
      field: 'branch_name',
      headerName: 'Branch name',
      width: 200,
      minWidth: 200,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',

      renderCell: (params) => {
        return (
          <Box className="d-flex align-center h100">
            <Typography
              style={{
                background: params?.row?.branch_color
                  ? params?.row?.branch_color
                  : '#006bff',
                width: 'var(--icon-size-xsm)',
                minWidth: 'var(--icon-size-xsm)',
                height: 'var(--icon-size-xsm)',
                marginRight: 'var(--spacing-sm)',
                borderRadius: 'var(--border-radius-xs)',
                border: 'var(--normal-border)',
              }}
            />
            {authState?.UserPermission?.branch === 2 ? (
              <Typography
                className="title-text fw600 cursor-pointer link-text mw100 text-ellipsis"
                onClick={() => handleEdit(params)}
              >
                {params?.value}
              </Typography>
            ) : (
              <Typography className="title-text mw100 text-ellipsis">
                {params?.value}
              </Typography>
            )}

            {/* <BranchDepartmentDisplay
              row={{ branch: params?.row }}
              isBranchOnly={true}
            /> */}
          </Box>
        );
      },
    },
    {
      field: 'branch_remark',
      headerName: 'Remark',
      width: 280,
      minWidth: 280,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-start h100">
            {params?.value !== '-' ? (
              <Tooltip
                title={
                  <div
                    dangerouslySetInnerHTML={{
                      __html: convertToHtml(params?.value),
                    }}
                  />
                }
                arrow
                classes={{ tooltip: 'info-tooltip-container' }}
                placement="bottom-start"
              >
                <Typography className="title-text text-ellipsis">
                  <span>{params?.value}</span>
                </Typography>
              </Tooltip>
            ) : (
              '-'
            )}
          </Box>
        );
      },
    },
    {
      field: 'branch_status',
      headerName: 'Status',
      width: 200,
      minWidth: 200,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100 text-capital">
            {params?.value === 'inactive' ? (
              <Typography className="sub-title-text failed fw600">
                {' '}
                {params?.value}{' '}
              </Typography>
            ) : params?.value === 'draft' ? (
              <Typography className="sub-title-text draft fw600">
                {' '}
                {params?.value}{' '}
              </Typography>
            ) : (
              <Typography className="sub-title-text success fw600">
                {' '}
                {params?.value}{' '}
              </Typography>
            )}
          </Box>
        );
      },
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex justify-center actions">
            <Box className="d-flex actions-branch gap-sm">
              <Tooltip
                title={<Typography>Edit</Typography>}
                arrow
                classes={{ tooltip: 'info-tooltip-container' }}
              >
                <Box>
                  <EditIcon
                    onClick={() => {
                      setUpdateItem(params?.row);
                      queryParams.set('is_edit', 'true');
                      queryParams.set('branch_id', params?.row?.id);
                      router.push(`?${queryParams.toString()}`);
                    }}
                  />
                </Box>
              </Tooltip>
              {params?.row?.branch_status !== 'inactive' && (
                <Tooltip
                  title={<Typography>Delete</Typography>}
                  arrow
                  classes={{ tooltip: 'info-tooltip-container' }}
                >
                  <Box>
                    <DeleteIcon
                      onClick={() => handleOpenDeleteDialog(params?.row?.id)}
                    />
                  </Box>
                </Tooltip>
              )}
            </Box>
          </Box>
        );
      },
    },
  ];
  // List of branches
  const getBranchList = async (search, pageNo, Rpp) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_BRANCH_LIST +
          `?search=${search}&page=${pageNo}&size=${Rpp ? Rpp : rowsPerPage}`
      );
      if (status === 200) {
        setLoader(false);
        const bdata = data?.data.map((b) => {
          return {
            ...b,
            branch_remark: b?.branch_remark ? b?.branch_remark : '-',
          };
        });
        setBranchList(bdata);
        removeFromStorage(identifiers?.RedirectData);
        setTotalCount(data?.count);
      }
    } catch (error) {
      setLoader(false);
      setBranchList([]);
      setTotalCount(0);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      setPage(1);
      getBranchList(searchValue, 1);
    }
  };
  const onPageChange = (newPage) => {
    setPage(newPage);
    getBranchList(searchValue, newPage);
  };
  const OnRowPerPage = (newPage) => {
    setRowsPerPage(newPage);
    setPage(1);
    getBranchList(searchValue, 1, newPage);
  };

  useEffect(() => {
    if (
      authState?.UserPermission?.branch === 1 ||
      authState?.UserPermission?.branch === 2
    ) {
      if (
        !fetchFromStorage(identifiers?.RedirectData) &&
        userdata?.page === undefined &&
        fetchFromStorage(identifiers?.RedirectData)?.IsFromUser === undefined &&
        userdata?.IsFromUser === updateItem
      ) {
        getBranchList(searchValue, page);
      }
    }
  }, [authState?.UserPermission?.branch]);
  useEffect(() => {
    if (
      fetchFromStorage(identifiers?.RedirectData) &&
      fetchFromStorage(identifiers?.RedirectData)?.IsFromUser
    ) {
      const fdata = fetchFromStorage(identifiers?.RedirectData);
      setPage(fdata?.page);
      setSearchValue(fdata?.searchValue);
      getBranchList(fdata?.searchValue, fdata?.page);
    } else if (userdata && userdata?.IsFromUser) {
      const fdata = userdata;
      setPage(fdata?.page);
      setSearchValue(fdata?.searchValue);
      getBranchList(fdata?.searchValue, fdata?.page);
    }
  }, [
    fetchFromStorage(identifiers?.RedirectData)?.IsFromUser,
    userdata?.IsFromUser,
  ]);
  const handleBack = () => {
    router.back();
  };

  const handleClearSearch = () => {
    setPage(1);
    getBranchList('', 1);
  };

  return (
    <>
      {actionLoader && <PreLoader />}
      <Box className="org-branch-sec">
        {isEdit === 'true' || isEdit === 'false' ? (
          <Box>
            <Box className="d-flex align-center pb8">
              <Box
                className="cursor-pointer d-flex align-center"
                onClick={handleBack}
              >
                <ArrowBackIosIcon />
              </Box>
              <Typography className="body-semibold">
                {isEdit === 'true' ? 'Edit Branch' : 'Create Branch'}
              </Typography>
            </Box>
            <Divider className="mb8" />
            <AddEditBranch
              getBranchList={() => getBranchList(searchValue, page)}
              isUpdate={isEdit === 'true' ? true : false}
              handleBack={handleBack}
              getBranchAndDepartmentLists={getBranchAndDepartmentLists}
            />
          </Box>
        ) : (
          <>
            <Box className="d-flex align-center justify-end gap-sm">
              <CustomSearch
                setSearchValue={setSearchValue}
                searchValue={searchValue}
                onKeyPress={handleKeyPress}
                isClearSearch
                handleClearSearch={() => handleClearSearch()}
              />
              <CustomButton
                variant="outlined"
                title="Search"
                onClick={() => {
                  setPage(1);
                  getBranchList(searchValue, 1);
                }}
              />
              {authState?.UserPermission?.branch === 2 && (
                <CustomButton
                  variant="contained"
                  title="Create branch"
                  startIcon={<AddIcon />}
                  fullWidth={false}
                  onClick={handleCreateBranch}
                />
              )}
            </Box>
            <Box className="table-container table-layout">
              {loader ? (
                <ContentLoader />
              ) : (
                <>
                  {branchList && branchList?.length === 0 ? (
                    <Box className="no-data d-flex align-center justify-center">
                      <NoDataView
                        title="No Branch Found"
                        description="There is no Branch available at the moment."
                      />
                    </Box>
                  ) : (
                    <>
                      <DataGrid
                        rows={branchList}
                        columns={columns}
                        pageSize={rowsPerPage}
                        checkboxSelection={false}
                        disableSelectionOnClick
                        columnVisibilityModel={{
                          actions:
                            authState?.UserPermission?.branch === 2
                              ? true
                              : false,
                        }}
                        hideMenuIcon
                      />
                      <CustomOrgPagination
                        currentPage={page}
                        totalCount={totalCount}
                        rowsPerPage={rowsPerPage}
                        onPageChange={onPageChange}
                        OnRowPerPage={OnRowPerPage}
                      />
                    </>
                  )}
                </>
              )}
            </Box>
          </>
        )}
      </Box>
      <DialogBox
        open={deleteDialogOpen}
        handleClose={handleCloseDeleteDialog}
        title="Confirmation"
        className="delete-modal"
        dividerClass="delete-modal-divider"
        content={
          <DeleteModal
            handleCancel={handleCloseDeleteDialog}
            handleConfirm={() => deletebranch(deleteId)}
            text="Are you sure you want to delete this branch?"
          />
        }
      />
    </>
  );
}
