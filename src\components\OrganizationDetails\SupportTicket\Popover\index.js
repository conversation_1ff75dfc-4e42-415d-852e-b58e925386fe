import { useState } from 'react';
import { Box, Tooltip, Typography, Popover } from '@mui/material';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined';
import RemoveRedEyeOutlinedIcon from '@mui/icons-material/RemoveRedEyeOutlined';
import './popover.scss';

export default function Pohover({
  anchorEl,
  setAnchorEl,
  handlePopoverClose,
  handlePreviewClick,
  media,
  handleDownload,
  removeMedia,
  index,
}) {
  const open = Boolean(anchorEl);

  return (
    <Popover
      open={open}
      anchorEl={anchorEl}
      onClose={handlePopoverClose}
      anchorOrigin={{
        vertical: 'bottom',
        horizontal: 'left',
      }}
      transformOrigin={{
        vertical: 'top',
        horizontal: 'left',
      }}
    >
      <Box className="popover-icons-wrap">
        <Tooltip title="Preview" arrow>
          <RemoveRedEyeOutlinedIcon
            onClick={() => handlePreviewClick(media)}
            className="eye-icon"
            sx={{ cursor: 'pointer' }}
          />
        </Tooltip>
        <Tooltip title="Download" arrow>
          <FileDownloadOutlinedIcon
            onClick={() => handleDownload(media?.preview, media?.name)}
            className="download-icon"
            sx={{ cursor: 'pointer' }}
          />
        </Tooltip>

        <Tooltip title="Delete" arrow>
          <DeleteOutlineIcon
            className="delete-icon"
            onClick={() => removeMedia(index)}
            sx={{ cursor: 'pointer' }}
          />
        </Tooltip>
      </Box>
    </Popover>
  );
}
