'use client';

import React, { useEffect, useContext, useState } from 'react';
import { Box, Typography } from '@mui/material';
import CustomTextField from '@/components/UI/CustomTextField';

import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import CustomButton from '@/components/UI/CustomButton';
import { setApiMessage, DateFormat } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import AuthContext from '@/helper/authcontext';
import { useRouter } from 'next/navigation';
import { fetchFromStorage, saveToStorage } from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import WSRRequestByIdPage from './WSRRequestsId';
import WSRCategory from './WsrCatRequest';

export default function RequestWSR({ dsrId }) {
  const { authState, userdata, setUserdata } = useContext(AuthContext);

  const [remark, setRemark] = useState('');
  const [isSubmit, setIsSubmit] = useState(false);
  const router = useRouter();
  const [DsrDetails, setDsrDetails] = useState();
  const [DsrDataIncome, setDsrDataIncome] = useState([]);
  const [DsrDataOther, setDsrDataOther] = useState([]);
  const [VatValue, setVATValue] = useState([0, 20]);
  const [VAT, setVAT] = useState({
    NoneVat: '',
    VAT1: '',
    VAT2: '',
    AmountVAT1: '',
    AmountVAT2: '',
    diff1: '',
    diff2: '',
    TotalIncome: '',
  });
  const [OldVAT, setOldVAT] = useState({
    NoneVat: '',
    VAT1: '',
    VAT2: '',
    AmountVAT1: '',
    AmountVAT2: '',
    diff1: '',
    diff2: '',
    TotalIncome: '',
  });
  const [loader, setLoader] = useState(false);
  const [isRequestTable, setIsRequestTable] = useState(false);
  const [approve, setApprove] = useState('');
  const [dsrReqData, setdsrReqData] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [requestId, setRequestId] = useState();
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [currency, setCurrency] = useState();
  const [VatIsView, setVatIsView] = useState(true);
  const [IsRemarkView, setIsRemarkView] = useState(true);

  // List of WSR request
  const getWsrRequestList = async (pageNo, Rpp) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_WSR_REQUEST_LIST +
          `?wsr_id=${dsrId}&page=${pageNo}&size=${Rpp ? Rpp : rowsPerPage}`
      );
      if (status === 200) {
        setLoader(false);
        let wsr =
          data?.data &&
          data?.data?.length > 0 &&
          data?.data?.map((d) => {
            return {
              ...d,
              branch: d?.wsr_branch?.branch_name,
              submitedby: d?.wsr_user?.user_full_name,
            };
          });
        wsr ? setdsrReqData(wsr) : setdsrReqData([]);

        setTotalCount(data?.count);
      }
    } catch (error) {
      setLoader(false);
      setTotalCount(0);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // List of DSR
  const getWsrRequest = async (id) => {
    setLoader(true);
    setRequestId(id);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_REQUEST_WSR_ID + `${id}`
      );
      if (status === 200 || status === 201) {
        setLoader(false);
        setDsrDetails(data?.data);
        const incomeData =
          data?.data &&
          data?.data?.wsr_items &&
          data?.data?.wsr_items?.length > 0 &&
          data?.data?.wsr_items?.filter(
            (f) => f?.payment_type_usage === 'income'
          );
        incomeData && setDsrDataIncome(incomeData);
        const otherData =
          data?.data &&
          data?.data?.wsr_items &&
          data?.data?.wsr_items?.length > 0 &&
          data?.data?.wsr_items?.filter(
            (f) => f?.payment_type_usage === 'other'
          );
        otherData && setDsrDataOther(otherData);
        if (data?.data?.wsr_amount_total) {
          const vatdata = JSON.parse(data?.data?.wsr_amount_total);
          setVAT(vatdata);
        }
        if (data?.data?.old_wsr_amount_total) {
          const vatdatao = JSON.parse(data?.data?.old_wsr_amount_total);
          setOldVAT(vatdatao);
        }
        data?.vat_per_data &&
          data?.vat_per_data?.length > 0 &&
          setVATValue(data?.vat_per_data);
      }
    } catch (error) {
      setLoader(false);
      setDsrDetails();
      setDsrDataIncome();
      setDsrDataOther();
      setVAT();
      setVATValue();
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const handleDsrRequest = async (status) => {
    setLoader(true);
    let request = {
      wsr_request_id: requestId,
      request_status: status,
      request_remark: remark,
    };
    try {
      const { status, data } = await axiosInstance.post(
        URLS?.APPROVE_REJECT_REQUEST_WSR,
        request
      );
      if (status === 200) {
        setLoader(false);
        if (data.status) {
          // getWsrRequest();
          setIsRequestTable(false);
          getWsrRequestList(1);
          setLoader(false);
          setApiMessage('success', data?.message);
        } else {
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const ValueOfTotal = (value) => {
    const parsedValue = parseFloat(value);
    if (!Number.isNaN(parsedValue)) {
      return parsedValue;
    }
    // if (parseFloat(value) && parseFloat(value) !== NaN) {
    //   return parseFloat(value);
    // }
    return 0;
  };
  const oldDSR = fetchFromStorage(identifiers?.RedirectData)
    ? fetchFromStorage(identifiers?.RedirectData)
    : userdata;

  useEffect(() => {
    if (oldDSR?.IsFromUser === undefined && oldDSR?.page !== undefined) {
      setUserdata({ ...userdata, IsFromUser: true });
      saveToStorage(identifiers?.RedirectData, {
        ...oldDSR,
        IsFromUser: true,
      });
    }
  }, [oldDSR]);
  useEffect(() => {
    if (dsrId) {
      getWsrRequestList(1);
    }
  }, [dsrId]);
  useEffect(() => {
    authState?.currency_details && setCurrency(authState?.currency_details);
  }, [authState]);
  return (
    <Box className="Add-dsr-section">
      <ArrowBackIosIcon
        className="mb8 cursor-pointer"
        onClick={() => {
          setTimeout(() => {
            router?.push('/wsr');
          }, 1000);
        }}
      />
      {isRequestTable ? (
        <>
          <Box>
            {DsrDetails?.wsr_start_date && (
              <Box className="d-flex align-center">
                <Typography className="p14 fw400">
                  <span className="fw600"> Start Date : </span>
                  <span>{DateFormat(DsrDetails?.wsr_start_date, 'dates')}</span>
                </Typography>
              </Box>
            )}
            {DsrDetails?.wsr_end_date && (
              <Box className="d-flex align-center">
                <Typography className="p14 fw400">
                  <span className="fw600"> End Date : </span>
                  <span>{DateFormat(DsrDetails?.wsr_end_date, 'dates')}</span>
                </Typography>
              </Box>
            )}
            {DsrDetails?.wsr_branch && (
              <Box className="d-flex align-center pt4">
                <Typography className="p14 fw400">
                  <span className="fw600"> Branch : </span>
                  <span>{DsrDetails?.wsr_branch}</span>
                </Typography>
              </Box>
            )}
            {DsrDetails?.submitted_user && (
              <Box className="d-flex align-center pt4">
                <Typography className="p14 fw400">
                  <span className="fw600"> Submitted By : </span>
                  <span>{DsrDetails?.submitted_user}</span>
                </Typography>
              </Box>
            )}
            {DsrDetails?.wsr_request_updated_by &&
              DsrDetails?.wsr_request_updated_by?.user_full_name && (
                <Box className="d-flex align-center pt4">
                  <Typography className="p14 fw400">
                    <span className="fw600"> Action By : </span>
                    <span>
                      {DsrDetails?.wsr_request_updated_by?.user_full_name}
                    </span>
                  </Typography>
                </Box>
              )}
            {DsrDetails?.wsr_request_status && (
              <Box className="d-flex align-center pt4">
                <Typography className="p14 fw400">
                  <span className="fw600"> Status : </span>
                  <span>
                    {DsrDetails?.wsr_request_status === 'rejected' ? (
                      <span className="p12 failed fw600 text-capital">
                        {' '}
                        {DsrDetails?.wsr_request_status}{' '}
                      </span>
                    ) : DsrDetails?.wsr_request_status === 'pending' ? (
                      <span className="p12 draft fw600 text-capital">
                        {' '}
                        {DsrDetails?.wsr_request_status}{' '}
                      </span>
                    ) : (
                      <span className="p12 success fw600 text-capital">
                        {' '}
                        {DsrDetails?.wsr_request_status}{' '}
                      </span>
                    )}
                  </span>
                </Typography>
              </Box>
            )}
          </Box>

          <WSRCategory
            DsrDataIncome={DsrDataIncome}
            setVatIsView={setVatIsView}
            VatIsView={VatIsView}
            setIsRemarkView={setIsRemarkView}
            IsRemarkView={IsRemarkView}
            setIsSubmit={setIsSubmit}
            setDsrDataIncome={setDsrDataIncome}
            setDsrDataOther={setDsrDataOther}
            DsrDataOther={DsrDataOther}
            currency={currency}
            VAT={VAT}
            setVAT={setVAT}
            VatValue={VatValue}
            ValueOfTotal={ValueOfTotal}
            isSubmit={isSubmit}
            OldVAT={OldVAT}
          />
          {DsrDetails?.wsr_request_status !== 'approved' &&
            DsrDetails?.wsr_request_status !== 'rejected' && (
              <>
                <Box className="pt64">
                  <CustomTextField
                    id="remark"
                    name="remark"
                    multiline
                    rows={2}
                    onChange={(e) => {
                      setIsSubmit(false);
                      setApprove();
                      setRemark(e.target.value);
                    }}
                    fullWidth
                    error={Boolean(!remark && isSubmit)}
                    helperText={isSubmit && !remark && isSubmit}
                    placeholder="Remark"
                    value={remark}
                    className="additional-textfeild"
                    label="Remark"
                    require
                  />
                  {!remark && isSubmit && (
                    <Typography
                      variant="body2"
                      color="error"
                      className="other-field-error-text"
                    >
                      This field is required
                    </Typography>
                  )}
                </Box>
                <Box className="create-cancel-button mt16">
                  <CustomButton
                    variant="contained"
                    className="red-button"
                    disabled={loader}
                    title={`${
                      loader && approve === 'rejected'
                        ? 'Rejecting...'
                        : 'Reject'
                    }`}
                    onClick={() => {
                      setIsSubmit(true);
                      if (remark) {
                        setApprove('rejected');
                        handleDsrRequest('rejected');
                      }
                    }}
                  />
                  <CustomButton
                    variant="contained"
                    disabled={loader} //|| !remark
                    className="green-button"
                    title={`${
                      loader && approve === 'approved'
                        ? 'Approving...'
                        : 'Approve'
                    }`}
                    onClick={() => {
                      setIsSubmit(true);
                      if (remark) {
                        setApprove('approved');
                        handleDsrRequest('approved');
                      }
                    }}
                  />
                </Box>
              </>
            )}
        </>
      ) : (
        <>
          <Box>
            {dsrReqData && dsrReqData?.length > 0 && (
              <>
                {dsrReqData?.[0]?.wsr_start_date && (
                  <Box className="d-flex align-center">
                    <Typography className="p14 fw400">
                      <span className="fw600">Start Date : </span>
                      <span>
                        {DateFormat(dsrReqData?.[0]?.wsr_start_date, 'dates')}
                      </span>
                    </Typography>
                  </Box>
                )}
                {dsrReqData?.[0]?.wsr_end_date && (
                  <Box className="d-flex align-center">
                    <Typography className="p14 fw400">
                      <span className="fw600"> End Date : </span>
                      <span>
                        {DateFormat(dsrReqData?.[0]?.wsr_end_date, 'dates')}
                      </span>
                    </Typography>
                  </Box>
                )}
                {dsrReqData?.[0]?.wsr_branch &&
                  dsrReqData?.[0]?.wsr_branch?.branch_name && (
                    <Box className="d-flex align-center pt4">
                      <Typography className="p14 fw400">
                        <span className="fw600"> Branch : </span>
                        <span>{dsrReqData?.[0]?.wsr_branch?.branch_name}</span>
                      </Typography>
                    </Box>
                  )}
                {dsrReqData?.[0]?.wsr_user &&
                  dsrReqData?.[0]?.wsr_user?.user_full_name && (
                    <Box className="d-flex align-center pt4">
                      <Typography className="p14 fw400">
                        <span className="fw600"> Submitted By : </span>
                        <span>{dsrReqData?.[0]?.wsr_user?.user_full_name}</span>
                      </Typography>
                    </Box>
                  )}
                {dsrReqData?.[0]?.wsr_request_updated_by &&
                  dsrReqData?.[0]?.wsr_request_updated_by?.user_full_name && (
                    <Box className="d-flex align-center pt4">
                      <Typography className="p14 fw400">
                        <span className="fw600"> Submitted By : </span>
                        <span>
                          {
                            dsrReqData?.[0]?.wsr_request_updated_by
                              ?.user_full_name
                          }
                        </span>
                      </Typography>
                    </Box>
                  )}
                {dsrReqData?.[0]?.wsr_request_status && (
                  <Box className="d-flex align-center pt4">
                    <Typography className="p14 fw400">
                      <span className="fw600"> Status : </span>
                      <span>
                        <span>
                          {dsrReqData?.[0]?.wsr_request_status ===
                          'rejected' ? (
                            <span className="p12 failed fw600 text-capital">
                              {' '}
                              {dsrReqData?.[0]?.wsr_request_status}{' '}
                            </span>
                          ) : dsrReqData?.[0]?.wsr_request_status ===
                            'pending' ? (
                            <span className="p12 draft fw600 text-capital">
                              {' '}
                              {dsrReqData?.[0]?.wsr_request_status}{' '}
                            </span>
                          ) : (
                            <span className="p12 success fw600 text-capital">
                              {' '}
                              {dsrReqData?.[0]?.wsr_request_status}{' '}
                            </span>
                          )}
                        </span>
                      </span>
                    </Typography>
                  </Box>
                )}
              </>
            )}
          </Box>
          <WSRRequestByIdPage
            setIsRequestTable={setIsRequestTable}
            dsrReqData={dsrReqData}
            loader={loader}
            totalCount={totalCount}
            getWsrRequest={getWsrRequest}
            getWsrRequestList={getWsrRequestList}
            setRowsPerPage={setRowsPerPage}
            rowsPerPage={rowsPerPage}
          />
        </>
      )}
    </Box>
  );
}
