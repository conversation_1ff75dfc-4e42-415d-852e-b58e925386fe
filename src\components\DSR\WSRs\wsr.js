'use client';

import React, { useContext, useEffect, useState } from 'react';
import { Box } from '@mui/material';
import AuthContext from '@/helper/authcontext';
import { removeFromStorage, fetchFromStorage } from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import WSRPage from '@/components/DSR/WSRs';
import '../dsr.scss';
import dayjs from 'dayjs';

export default function DSRView() {
  const { authState, userdata, setUserdata, AllListsData } =
    useContext(AuthContext);

  const [dsrData, setdsrData] = useState([]);
  const [loaderUser, setLoaderUser] = useState(true);
  // const [loader, setLoader] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const [filterData, setFilterData] = useState({
    branch: '',
    sdate: '',
    edate: '',
  });
  const [filterDataApplied, setFilterDataApplied] = useState({
    branch: '',
    sdate: '',
    edate: '',
  });

  const [rowsPerPage, setRowsPerPage] = useState(10);

  // List of WSR
  const getWsrData = async (search, pageNo, branch, sdate, edate, Rpp) => {
    // setLoader(true);
    setLoaderUser(true);
    const sDate = sdate ? dayjs(sdate)?.format('YYYY-MM-DD') : '';
    const eDate = edate ? dayjs(edate)?.format('YYYY-MM-DD') : '';
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_WSR_LIST +
          `?search=${search}&page=${pageNo}&size=${
            Rpp ? Rpp : rowsPerPage
          }&branch_id=${branch}&wsr_start_date=${sDate}&wsr_end_date=${eDate}`
      );
      if (status === 200) {
        // setLoader(false);
        let dsr =
          data?.data &&
          data?.data?.length > 0 &&
          data?.data?.map((d) => {
            return {
              ...d,
              branch: d?.wsr_branch,
              submitedby: d?.dsr_user?.user_full_name,
            };
          });
        dsr ? setdsrData(dsr) : setdsrData([]);
        removeFromStorage(identifiers?.RedirectData);
        setUserdata();
        setTotalCount(data?.count);
        setLoaderUser(false);
      }
    } catch (error) {
      // setLoader(false);
      setLoaderUser(false);
      setdsrData([]);
      setTotalCount(0);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  useEffect(() => {
    if (
      authState?.UserPermission?.dsr === 1 ||
      authState?.UserPermission?.dsr === 2
    ) {
      if (
        (!fetchFromStorage(identifiers?.RedirectData) &&
          userdata?.page === undefined &&
          fetchFromStorage(identifiers?.RedirectData)?.IsFromUser ===
            undefined &&
          userdata?.IsFromUser === undefined) ||
        fetchFromStorage(identifiers?.RedirectData)?.wsr === undefined ||
        userdata?.wsr === undefined
      ) {
        getWsrData('', 1, '', '', '');
        removeFromStorage(identifiers?.RedirectData);
        setUserdata();
      }
    }
  }, [authState?.UserPermission?.dsr]);

  return (
    <>
      <Box className="dsr-page-section">
        <WSRPage
          authState={authState}
          branchList={AllListsData?.ActiveBranchList}
          loader={loaderUser}
          rowsPerPage={rowsPerPage}
          setRowsPerPage={setRowsPerPage}
          getDsrData={getWsrData}
          dsrData={dsrData}
          totalCount={totalCount}
          setFilterData={setFilterData}
          setFilterDataApplied={setFilterDataApplied}
          filterData={filterData}
          filterDataApplied={filterDataApplied}
        />
      </Box>
    </>
  );
}
