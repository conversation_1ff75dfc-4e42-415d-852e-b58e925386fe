.leave-policy-type-wrap {
  .leave-accordion {
    border-radius: var(--border-radius-md);
    margin-top: var(--spacing-lg);
    box-shadow: var(--box-shadow-xs);

    .accordion-summary {
      min-height: 40px;
      padding: 0 16px 0 0;
      .MuiAccordionSummary-content {
        padding: var(--spacing-none);
        margin: var(--spacing-none) !important;
        .policy-name {
          padding: 10px 0;
          padding-left: 16px;
        }
      }
    }

    .MuiCollapse-root {
      .MuiAccordionDetails-root {
        padding: 0px 0px 0px;
        .leave-accordian-tab {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          @media (max-width: 575px) {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 10px;
          }
        }
        .create-new-policy-btn-wrap {
          margin-left: 10px;
          .create-new-policy-btn {
            background-color: transparent !important;
            color: var(--text-color-primary) !important;
            border: none !important;
            min-height: 32px;
            padding: 0px !important ;
            .MuiSvgIcon-root {
              fill: var(--icon-color-primary) !important;
            }
            &:hover {
              box-shadow: none !important;
            }
          }
        }
        .action-icon-wrap {
          .action-icons {
            height: 18px;
            width: 18px;
            fill: var(--icon-color-primary);
          }
        }
      }
    }
    &:last-of-type {
      border-bottom-left-radius: var(--border-radius-md);
      border-bottom-right-radius: var(--border-radius-md);
    }
    &::before {
      position: static;
    }
  }
  .holiday-actions {
    margin-right: 5px;
    .action-icon {
      background-color: var(--action-icon-bg-color);
      border-radius: 50%;
    }
  }
  .assign-icons {
    path {
      stroke-width: 1.6px;
    }
  }
  .policy-color-wrap {
    height: 10px;
    width: 10px;
    border-radius: 50%;
  }
}
