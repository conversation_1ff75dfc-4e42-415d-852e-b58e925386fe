'use client';

import React from 'react';
import { Box } from '@mui/material';
import { useSearchParams } from 'next/navigation';
import EditDSR from '@/components/DSR/DSRs/EditDSR';
import ActivityLogs from '@/components/DSR/DSRs/Activitylog';
import RequestDSR from '@/components/DSR/DSRs/RequestDSR';
import RequestDSRTab from '@/components/DSR/DSRs/RequestDSRTab';
import '../dsr.scss';

export default function DSRDetails({ params }) {
  const searchParams = useSearchParams();
  const IsEdit = searchParams.get('isEdit');
  const isRequest = searchParams.get('isRequest');
  const isRequestTab = searchParams.get('isRequests');
  const isActivity = searchParams.get('isActivity');
  const dsrId = params?.id;

  return (
    <>
      <Box>
        <Box className={isActivity ? '' : 'DSR-details-section'}>
          {IsEdit ? (
            <EditDSR dsrId={dsrId} />
          ) : isActivity ? (
            <ActivityLogs dsrId={dsrId} />
          ) : isRequestTab ? (
            <RequestDSRTab dsrId={dsrId} />
          ) : isRequest ? (
            <RequestDSR dsrId={dsrId} />
          ) : (
            <></>
          )}
        </Box>
      </Box>
    </>
  );
}
