'use client';
import React, { useContext } from 'react';
import { Box, Typography } from '@mui/material';
import { useDropzone } from 'react-dropzone';
import CloudUploadOutlinedIcon from '@mui/icons-material/CloudUploadOutlined';
import HeaderImage from '@/components/UI/ImageSecurity';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import AuthContext from '@/helper/authcontext';
import './imageupload.scss';

export default function ImageUpload({
  previewImage,
  setPreviewImage,
  setFieldValue,
  fieldName,
  touched,
  errors,
}) {
  const { planDetail, authState, setRestrictedLimitModal } =
    useContext(AuthContext);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop: (acceptedFiles) => {
      if (acceptedFiles?.length) {
        const file = acceptedFiles[0];

        // Check if storage is full
        const totalStorage = planDetail?.total_storage || 0;
        const usedStorage = authState?.subscriptionUsage?.total_size_gb || 0;
        const fileSizeInGB = file.size / (1024 * 1024 * 1024); // Convert bytes to GB

        if (usedStorage + fileSizeInGB > totalStorage) {
          setRestrictedLimitModal({
            storageLimit: true,
            totalStorage: planDetail?.total_storage,
            usedStorage: authState?.subscriptionUsage?.total_size_gb,
          });
          return;
        }

        setPreviewImage({ url: URL.createObjectURL(file) }); // Generate a preview URL
        // setPreviewImage(file); // Generate a preview URL
        setFieldValue(fieldName, file);
        // onImageUpload(file);
      }
    },
    accept: 'image/*',
  });

  return (
    <Box className="w100">
      <Box className="d-flex logo-section-wrap">
        <Box className="logo-wrap">
          {previewImage?.url ? (
            <HeaderImage
              imageUrl={previewImage?.url}
              alt="users"
              style={{ marginRight: '4px' }}
              className=""
              type="avtar"
            />
          ) : (
            <AccountCircleIcon className="profile-image" />
          )}
        </Box>

        <Box className="upload-sec">
          <Box {...getRootProps()} className="upload-area">
            <CloudUploadOutlinedIcon />
            <input {...getInputProps()} />
            <Typography className="p14 upload-text">
              Drop your image here
            </Typography>
          </Box>
        </Box>
      </Box>

      {!previewImage?.url && touched.fieldName && errors.fieldName && (
        <Typography variant="body2" color="error" className="field-error">
          This field is required
        </Typography>
      )}
    </Box>
  );
}
