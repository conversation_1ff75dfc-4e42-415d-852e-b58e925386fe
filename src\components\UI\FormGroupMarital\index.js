import React from 'react';
import { Box, Typography } from '@mui/material';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomCheckbox from '../CustomCheckbox';
import './maritalstatus.scss';

export default function MaritalStatus({
  setFieldValue,
  isRequire,
  keyName,
  keyValue,
  disable,
}) {
  return (
    <Box className="gender-section">
      <Typography className="field-label" id={keyName}>
        Marital Status
        {isRequire && <span className="required">*</span>}
      </Typography>
      <Box className="gender-option">
        <Box className="">
          <CustomCheckbox
            disabled={disable}
            onChange={() => {
              setFieldValue(keyName, 'single');
            }}
            checked={keyValue === 'single'}
            label="Single"
            labelPlacement="start"
          />
        </Box>
        <Box className="">
          <CustomCheckbox
            checked={keyValue === 'married'}
            disabled={disable}
            onChange={() => {
              setFieldValue(keyName, 'married');
            }}
            labelPlacement="start"
            label="Married"
          />
        </Box>
        <Box className="other-field">
          <Typography className="title-text">Other</Typography>
          <CustomTextField
            id="other"
            name="other"
            className="other-textfield"
            onChange={(e) => {
              setFieldValue(keyName, e.target.value);
            }}
            disabled={disable}
            value={
              keyValue !== 'married' && keyValue !== 'single' ? keyValue : ''
            }
            placeholder="Other"
          />
        </Box>
      </Box>
    </Box>
  );
}
