'use client';

import React, { useContext, useEffect, useState } from 'react';
import { Box, Checkbox, CircularProgress, Typography } from '@mui/material';
import CustomButton from '@/components/UI/CustomButton';
import { setApiMessage, DateFormat } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import LaunchIcon from '@mui/icons-material/Launch';
import HeaderImage from '@/components/UI/ImageSecurity';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined';
import DialogBox from '@/components/UI/Modalbox';
import DeleteModal from '@/components/UI/DeleteModal';
import AuthContext from '@/helper/authcontext';
import NoDataView from '@/components/UI/NoDataView';
import './emp-history.scss';

const ADMIN_ROLE_IDS = [1, 2];

const EmploymentContractHistory = ({ UserId }) => {
  const [loader, setLoader] = useState(false);
  const [templateData, setTemplateData] = useState([]);
  const [selectedContracts, setSelectedContracts] = useState([]);
  const [totalCount, setTotalCount] = useState('');
  const [deleteConfirmation, setDeleteConfirmation] = useState(false);
  const { authState } = useContext(AuthContext);

  const isAdmin = ADMIN_ROLE_IDS.includes(authState?.web_user_active_role_id);

  // GET TEMPLATE DETAILS
  const getTemplateDetails = async () => {
    // templateData?.length === 0 &&
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_RENEWL_LIST + `?user_id=${UserId}`
      );

      if (status === 200) {
        const tempData = data?.data;
        setTemplateData(tempData);
        // setHasMore(tempData?.length > 0);
        setTotalCount(data?.total);
        setLoader(false);
        // setTemplateData(tempData);
      }
    } catch (error) {
      setLoader(false);
      setTemplateData('');
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const handleDeleteCloseModal = () => {
    setDeleteConfirmation(false);
  };

  const deleteUserContract = async () => {
    // templateData?.length === 0 &&
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.delete(
        URLS?.DELETE_USER_CONTRACT + `/?contract_ids=${selectedContracts}`
      );

      if (status === 200) {
        setTemplateData((prevData) => {
          const updatedData = prevData?.filter(
            (item) => !selectedContracts.includes(item?.id)
          );
          setTotalCount(updatedData?.length); // Update total count
          return updatedData;
        });
        setSelectedContracts([]); // Reset selection
        setApiMessage('success', data?.message);
        setLoader(false);
      }
    } catch (error) {
      setLoader(false);
      // setTemplateData([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const handleDeleteUserContart = () => {
    deleteUserContract();
    setDeleteConfirmation(false);
  };

  useEffect(() => {
    getTemplateDetails();
  }, []);

  const handleCheckboxChange = (contarcts) => {
    setSelectedContracts((prevSelected) =>
      prevSelected.includes(contarcts?.id)
        ? prevSelected?.filter((contractId) => contractId !== contarcts?.id)
        : [...prevSelected, contarcts?.id]
    );
  };

  const download = async (contract_link) => {
    const response = await fetch(contract_link);
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'emp_contract.pdf');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };
  const statusStyles = {
    inactive: 'ongoing',
    active: 'success',
  };
  return (
    <>
      <Box className="emp-contract-history">
        {loader ? (
          <Box className="content-loader pt16">
            <CircularProgress className="loader" color="inherit" />
          </Box>
        ) : templateData && templateData?.length === 0 ? (
          <Box className="no-data d-flex align-center justify-center">
            <NoDataView
              title="No Employee Contract History Found"
              description="There is no Employee Contract History data available at the moment."
            />
          </Box>
        ) : (
          <Box>
            <Box className="d-flex align-center justify-space-between pb24 flex-wrap gap-10">
              <Typography className="title-text">
                Total Contracts : {totalCount}
              </Typography>
              {isAdmin && (
                <Box className="d-flex align-center gap-20">
                  <CustomButton
                    variant="contained"
                    className="delete-button"
                    startIcon={<DeleteOutlineOutlinedIcon />}
                    title={'Delete'}
                    disabled={selectedContracts?.length === 0}
                    onClick={() => setDeleteConfirmation(true)}
                  />
                </Box>
              )}
            </Box>
            <Box className="employee-contract-checklist contract-history-container ">
              {templateData &&
                templateData?.length > 0 &&
                templateData?.map((item, i) => {
                  return (
                    <Box className="contract-history-wrap h100" key={i}>
                      <Box className="title-section">
                        <Box>
                          <Box className="d-flex align-center justify-start h100 text-capital">
                            <Typography
                              className={`sub-title-text ${statusStyles[item?.contract_status]} fw600`}
                            >
                              {item?.contract_status === 'inactive'
                                ? 'In-Active'
                                : item?.contract_status}
                            </Typography>
                          </Box>
                          <Box className="d-flex align-center pt8">
                            <Typography className="title-text user-date">
                              <span className="fw600">Created Date : </span>
                              <span>
                                {DateFormat(
                                  item?.createdAt,
                                  'datesWithhourUTC'
                                )}
                              </span>
                            </Typography>
                          </Box>
                          <Box className="d-flex align-center pt8">
                            <Typography className="title-text user-date">
                              <span className="fw600">Expiry Date : </span>
                              <span>
                                {item?.expire_date
                                  ? DateFormat(item?.expire_date, 'datesUTC')
                                  : 'No Expiry'}
                              </span>
                            </Typography>
                          </Box>
                        </Box>
                        <CustomButton
                          variant="contained"
                          title={'Download'}
                          className="download-button"
                          fullWidth={false}
                          onClick={() =>
                            item?.contract_with_sign &&
                            download(item?.contract_with_sign)
                          }
                        />
                      </Box>

                      <Box className="action-sec d-flex justify-space-between">
                        <Box className="d-flex align-center">
                          <HeaderImage
                            type="url"
                            imageUrl={item?.contract_with_sign}
                            Content={
                              <Typography className="title-text cursor-pointer contract-name">
                                EMPLOYMENT CONTRACT
                              </Typography>
                            }
                          />
                          <HeaderImage
                            type="url"
                            imageUrl={item?.contract_with_sign}
                            Content={
                              <Box className="d-flex align-center h100">
                                <LaunchIcon className="view-icon" />
                              </Box>
                            }
                          />
                        </Box>

                        {item?.contract_status === 'inactive' && isAdmin && (
                          <Checkbox
                            className="inactive-check-box"
                            icon={<CheckBoxOutlineBlankIcon />}
                            checkedIcon={<CheckBoxIcon />}
                            checked={selectedContracts?.includes(item?.id)}
                            onChange={() => handleCheckboxChange(item)}
                            disableRipple
                          />
                        )}
                      </Box>
                    </Box>
                  );
                })}
            </Box>
          </Box>
        )}
      </Box>
      <DialogBox
        open={deleteConfirmation}
        handleClose={() => {
          handleDeleteCloseModal();
        }}
        title={'Confirmation'}
        className="delete-modal"
        dividerClass="delete-modal-divider"
        content={
          <>
            <DeleteModal
              handleCancel={handleDeleteCloseModal}
              handleConfirm={handleDeleteUserContart}
              text="Are you sure you want to delete?"
            />
          </>
        }
      />
    </>
  );
};

export default EmploymentContractHistory;
