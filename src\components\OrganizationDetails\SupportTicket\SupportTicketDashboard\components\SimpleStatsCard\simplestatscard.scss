// Simple Stats Card - Using global variables only

.simple-stats-card {
  position: relative;
  padding: var(--spacing-lg);
  border: var(--border-width-xs) solid;
  border-radius: var(--border-radius-lg);
  transition: all 0.3s ease;
  cursor: pointer;
  text-decoration: none;
  display: block;

  &__content {
    width: 100%;
    height: 100%;
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    width: 100%;
    height: 100%;
  }

  &__text-section {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    flex: 1;
  }

  &__value {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xxl);
    font-weight: var(--font-weight-bold);
    color: var(--text-color-primary);
    margin: 0;
    line-height: 1.2;
  }

  &__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius-md);

    svg {
      width: 20px;
      height: 20px;
    }
  }

  // Color variants - using global CSS variables
  &--primary {
    background-color: var(--color-primary-opacity);
    border-color: var(--color-primary);

    .simple-stats-card__value {
      color: var(--color-primary);
    }

    .simple-stats-card__icon {
      background-color: var(--color-white);
      color: var(--color-primary);
    }
  }

  &--success {
    background-color: var(--color-success-opacity);
    border-color: var(--color-success);

    .simple-stats-card__value {
      color: var(--color-success);
    }

    .simple-stats-card__icon {
      background-color: var(--color-white);
      color: var(--color-success);
    }
  }

  &--warning {
    background-color: var(--color-warning-opacity);
    border-color: var(--color-warning);

    .simple-stats-card__value {
      color: var(--color-warning);
    }

    .simple-stats-card__icon {
      background-color: var(--color-white);
      color: var(--color-warning);
    }
  }

  &--accent {
    background-color: var(--color-muted-mustard-opacity);
    border-color: var(--color-orange);

    .simple-stats-card__value {
      color: var(--color-orange);
    }

    .simple-stats-card__icon {
      background-color: var(--color-white);
      color: var(--color-orange);
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .simple-stats-card {
    padding: var(--spacing-md);

    &__header {
      margin-bottom: var(--spacing-sm);
    }

    &__icon {
      width: 32px;
      height: 32px;
    }

    &__value {
      font-size: var(--font-size-xl);
    }
  }
}
