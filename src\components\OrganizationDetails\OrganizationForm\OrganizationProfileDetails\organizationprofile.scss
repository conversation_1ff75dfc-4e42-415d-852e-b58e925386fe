@import '@/styles/variable.scss';

.organization-profile-sec {
  flex-grow: 1;
  .profile-name {
    font-size: var(--font-size-md);
    color: var(--text-bright-blue);
    font-family: var(--font-family-primary);
  }
  .profile-subtitle {
    font-family: var(--font-family-primary);
    color: var(--text-color-slate-gray);
  }
  .profile-avatar-section {
    width: 100%;
    height: 100px;
    margin-bottom: 20px;
    .profile-avatar {
      width: 100px;
      height: 100px;
      border: 3px solid $color-White;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

      &.MuiAvatar-root {
        background-color: $color-secondary;
        color: $color-Dark-30;
      }
      @media (max-width: 1023px) {
        height: 80px;
        width: 80px;
      }
    }
  }
  .admin-profile-sec {
    width: 80px;
    height: 80px;
    position: relative;
    .admin-profile-avatar {
      width: 80px;
      height: 80px;
      border: 3px solid $color-White;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      .MuiSvgIcon-root {
        height: 50%;
        width: 50%;
      }
      &.MuiAvatar-root {
        background-color: $color-secondary;
        color: $color-Dark-30;
      }
    }
    .edit-avatar-button {
      position: absolute;
      bottom: 0;
      right: 0;
      background: var(--color-white);
      padding: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border-radius: 50%;
      color: var(--text-bright-blue);
      svg {
        width: 18px;
        height: 18px;
      }
    }
  }
  .org-navigation-tabs {
    flex-grow: 1;
  }
  .user-details-wrap {
    color: var(--text-color-slate-gray);
    font-weight: var(--font-weight-bold);
    display: flex;
    flex-direction: column;
    align-items: center;
    flex-grow: 1;
    @media (max-width: 991px) {
      flex-direction: row;
    }
    @media (max-width: 640px) {
      flex-direction: column;
    }
    .userOption-wrap {
      padding: var(--spacing-xsm);
      line-height: var(--line-height-xxs) !important;
      margin-bottom: var(--spacing-sm);
      padding: var(--spacing-sm);
      width: 90%;

      .icon-wrap {
        fill: var(--icon-color-primary);
        height: 18px;
        width: 18px;
      }
      @media (max-width: 1199px) {
        width: 100%;
        line-height: 18px !important;
      }
      @media (max-width: 991px) {
        text-align: center;
        justify-content: center;
        .userOption-text-wrap {
          width: 150px;
        }
      }
    }
    .userOption-active {
      color: var(--text-color-white) !important;
      background-color: var(--color-primary);
      // margin-top: var(--spacing-xs);
      border-radius: var(--border-radius-xl);
      .icon-wrap {
        fill: var(--icon-color-white);
      }
    }
  }
  .support-pin-wrap {
    background-color: var(--color-secondary);
    border-radius: var(--border-radius-lg);
    border: var(--normal-dashed-sec-border);
    padding: var(--spacing-md) var(--spacing-none);
    .support-pin-details {
      width: 90%;
      margin: 0 auto;
      padding: var(--spacing-none) var(--spacing-sm);
      @media (max-width: 991px) {
        margin: var(--spacing-none) !important;
      }
      @media (max-width: 991px) {
        margin-bottom: var(--spacing-lg) !important;
      }
    }
    .support-pin {
      border-bottom: var(--normal-dashed-sec-border);
      width: max-content;
    }
    .key-icon {
      font-size: 18px;
    }
    @media (max-width: 991px) {
      display: flex;
      padding: var(--spacing-md);
    }
    @media (max-width: 575px) {
      display: flex;
      flex-direction: column;
      padding: var(--spacing-md);
    }
  }
}
