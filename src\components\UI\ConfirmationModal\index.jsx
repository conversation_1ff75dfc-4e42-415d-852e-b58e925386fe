'use client';

import React from 'react';
import { Box, Typography } from '@mui/material';
import CustomButton from '../CustomButton';
import PreLoader from '../Loader';
import './confirmationModal.scss';

export default function ConfirmationModal({
  text,
  handleCancel,
  handleConfirm,
  confirmText = 'Ok',
  cancelText = 'Cancel',
  isLoading = false,
}) {
  return (
    <Box className="confirmation-modal-wrap">
      {isLoading && <PreLoader />}
      <Typography className="confirmation-text">{text}</Typography>
      <Box className="confirmation-action">
        <CustomButton
          fullWidth
          className=""
          variant="outlined"
          title={cancelText}
          onClick={() => handleCancel()}
        />
        <CustomButton
          fullWidth
          className=""
          variant="contained"
          title={confirmText}
          onClick={() => handleConfirm()}
          disabled={isLoading}
        />
      </Box>
    </Box>
  );
}
