import React, { useEffect, useRef, useState } from 'react';
import { Box, Button, Typography } from '@mui/material';
import { useReactToPrint } from 'react-to-print';
import './pdfGenerate.scss';
import { DateFormat } from '@/helper/common/commonFunctions';
// import axiosInstance from '@/helper/axios/axiosInstance';
// import { URLS } from '@/helper/constants/urls';
// import { setApiMessage } from '@/helper/commonFunctions';
import InsertDriveFileOutlinedIcon from '@mui/icons-material/InsertDriveFileOutlined';

const HealthAndSafetyPDF = ({ userDetails, formDetails }) => {
  const componentRef = useRef();

  const [healthSafechecked, setHealthSafeChecked] = useState([]);

  const handleDownlodPDF = useReactToPrint({
    content: () => componentRef.current,
    documentTitle: 'Health & Safety, Fire saftety Induction',
  });
  function filterFilesId(categories) {
    const result = [];

    categories.forEach((category) => {
      if (category?.category_type === 'file' && category?.is_tracked_by_user) {
        result.push(category?.id);
      }

      if (category?.children && category?.children?.length > 0) {
        result.push(...filterFilesId(category?.children));
      }
    });

    return result;
  }
  useEffect(() => {
    if (formDetails && formDetails?.length > 0) {
      const filesId = filterFilesId(formDetails);
      setHealthSafeChecked(filesId);
    }
  }, [formDetails]);

  const handleSubCate = (item) => {
    return (
      <>
        {item?.children &&
          item?.children?.length > 0 &&
          item?.children?.map((data, i) => {
            if (data?.category_type === 'folder') {
              return <>{handleSubCate(data)}</>;
            }
            return (
              <tr key={i}>
                <td className="w70">
                  <Typography className="pl4 sub-title-text">
                    {data?.category_name}
                  </Typography>
                </td>
                <td className="w30">
                  <Typography className="sub-title-text text-align">
                    {healthSafechecked &&
                    healthSafechecked?.length > 0 &&
                    healthSafechecked?.includes(data?.id)
                      ? '✔'
                      : ''}
                  </Typography>
                </td>
              </tr>
            );
          })}
      </>
    );
  };
  const healthAndSafetyPDFForm = () => {
    return (
      <Box ref={componentRef} className="pdf-generate-main-container">
        <Typography variant="h5" className="body-text pb16 fw600">
          Health & Safety, Fire saftety Induction
        </Typography>
        {/* Health & Safety Details */}
        <Box className="health-safety-information">
          <Box className="health-safety-table">
            <table>
              <tbody>
                <tr>
                  <td className="w70">
                    <Typography className="pl4 sub-title-text fw600">
                      Name:
                      <span variant="h6" className="sub-title-text pl8 fw400">
                        {userDetails?.user_first_name +
                          ' ' +
                          userDetails?.user_middle_name +
                          ' ' +
                          userDetails?.user_last_name}
                      </span>
                    </Typography>
                  </td>
                  <td className="w30">
                    <Typography className="pl4 sub-title-text fw600">
                      Date:
                      <span variant="h6" className="sub-title-text pl8 fw400">
                        {DateFormat(
                          userDetails?.updatedAt
                            ? userDetails?.updatedAt
                            : userDetails?.createdAt,
                          'dates'
                        )}
                      </span>
                    </Typography>
                  </td>
                </tr>
                <tr>
                  <td className="w70">
                    <Typography className="pl4">&nbsp;</Typography>
                  </td>
                  <td className="w30">
                    <Typography className="pl4">&nbsp;</Typography>
                  </td>
                </tr>
                <tr>
                  <td className="w70">
                    <Typography className="pl4">&nbsp;</Typography>
                  </td>
                  <td className="w30">
                    <Typography className="pl4 sub-title-text fw600 text-align">
                      Topics Covered (✔ as done)
                    </Typography>
                  </td>
                </tr>
                {formDetails &&
                  formDetails?.length > 0 &&
                  formDetails?.map((item, i) => {
                    return (
                      <>
                        {i !== 0 && (
                          <tr>
                            <td className="w70">
                              <Typography className="pl4">&nbsp;</Typography>
                            </td>
                            <td className="w30">
                              <Typography className="pl4">&nbsp;</Typography>
                            </td>
                          </tr>
                        )}
                        <tr>
                          <td className="w70">
                            <Typography className="pl4 sub-title-text fw800">
                              {item?.category_name}
                            </Typography>
                          </td>
                          <td className="w30">
                            <Typography className="pl4"></Typography>
                          </td>
                        </tr>
                        <>
                          {item?.children &&
                            item?.children?.length > 0 &&
                            item?.children?.map((data, dindex) => {
                              if (data?.category_type === 'folder') {
                                return (
                                  <>
                                    <tr>
                                      <td className="w70">
                                        <Typography className="pl4">
                                          &nbsp;
                                        </Typography>
                                      </td>
                                      <td className="w30">
                                        <Typography className="pl4">
                                          &nbsp;
                                        </Typography>
                                      </td>
                                    </tr>
                                    <tr>
                                      <td className="w70">
                                        <Typography className="pl4 sub-title-text fw600">
                                          {data?.category_name}
                                        </Typography>
                                      </td>
                                      <td className="w30">
                                        <Typography className="pl4"></Typography>
                                      </td>
                                    </tr>

                                    {handleSubCate(data)}
                                  </>
                                );
                              }
                              return (
                                <tr key={dindex}>
                                  <td className="w70">
                                    <Typography className="pl4 sub-title-text">
                                      {data?.category_name}
                                    </Typography>
                                  </td>
                                  <td className="w30">
                                    <Typography className="sub-title-text text-align">
                                      {healthSafechecked &&
                                      healthSafechecked?.length > 0 &&
                                      healthSafechecked?.includes(data?.id)
                                        ? '✔'
                                        : ''}
                                    </Typography>
                                  </td>
                                </tr>
                              );
                            })}
                        </>
                      </>
                    );
                  })}
              </tbody>
            </table>
          </Box>
        </Box>

        {/* Signature Details */}
        <Box className="signature-wrapper pt16">
          <Box className="signature-content">
            <Typography variant="h6" className="title-text">
              Briefing delivered by:
              <span className="title-text pl8">
                {userDetails &&
                  userDetails?.user_first_name +
                    ' ' +
                    userDetails?.user_middle_name +
                    ' ' +
                    userDetails?.user_last_name}
              </span>
            </Typography>
            <Typography variant="h6" className="title-text">
              Briefing received by:
              <span className="title-text pl8">
                {userDetails && userDetails?.branch?.branch_name}
              </span>
            </Typography>
          </Box>
          <Box className="signature-content mt20">
            <Typography variant="h6" className="title-text">
              Signature:
              {userDetails?.user_signature && (
                <span className="title-text pl8">
                  <img
                    src={userDetails?.user_signature}
                    alt="not found"
                    className="signature"
                  />
                </span>
              )}
            </Typography>
            <Typography variant="h6" className="title-text">
              Signature:
              {userDetails?.branch_sign && (
                <span className="title-text pl8">
                  <img
                    src={userDetails?.branch_sign}
                    alt="not found"
                    className="signature"
                  />
                </span>
              )}
            </Typography>
          </Box>
          <Box className="signature-content mt20">
            <Typography variant="h6" className="title-text">
              Date:
              <span className="pl8 title-text">
                {DateFormat(
                  userDetails?.updatedAt
                    ? userDetails?.updatedAt
                    : userDetails?.createdAt,
                  'dates'
                )}
              </span>
            </Typography>
            <Typography variant="h6" className="title-text">
              Date:
              <span className="pl8 title-text">
                {DateFormat(Date(), 'dates')}
              </span>
            </Typography>
          </Box>
        </Box>
      </Box>
    );
  };

  return (
    <div>
      <div style={{ display: 'none' }}>{healthAndSafetyPDFForm()}</div>
      <Button
        onClick={() => handleDownlodPDF()}
        className="pdf-download-btn"
        disableRipple
      >
        <InsertDriveFileOutlinedIcon />
        <span className="title-text pl4 text-underline">View PDF</span>
      </Button>
    </div>
  );
};

export default HealthAndSafetyPDF;
