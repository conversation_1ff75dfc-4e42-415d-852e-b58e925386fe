import React, { useState, useEffect } from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import CustomButton from '@/components/UI/CustomButton';
import { Typography, Tooltip, Box } from '@mui/material';
import '../styles/ExportProgress.scss';

const ExportStatusIndicator = ({
  status = 'idle', // 'idle', 'processing', 'completed', 'error'
  progress = 0,
  fileName = '',
  onDownload = null,
  onDismiss = null,
  onRetry = null,
  exportError = '',
  estimatedTime = null,
}) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(status !== 'idle');
  }, [status]);

  const getStatusConfig = () => {
    switch (status) {
      case 'processing':
        return {
          icon: 'Loader2',
          iconClass: 'icon-processing',
          bgClass: 'bg-processing',
          title: 'Export Processing',
          description: `Generating ${fileName || 'export file'}...`,
        };
      case 'completed':
        return {
          icon: 'CheckCircle',
          iconClass: 'icon-success',
          bgClass: 'bg-success',
          title: 'Export Complete',
          description: `${fileName || 'Export file'} is ready for download`,
        };
      case 'error':
        return {
          icon: 'AlertCircle',
          iconClass: 'icon-error',
          bgClass: 'bg-error',
          title: 'Export Failed',
          description:
            exportError || 'An error occurred during export generation',
        };
      default:
        return null;
    }
  };

  const handleDownload = () => {
    if (onDownload) {
      onDownload();
    }
  };

  const handleDismiss = () => {
    if (onDismiss) {
      onDismiss();
    }
    setIsVisible(false);
  };

  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    }
  };

  const statusConfig = getStatusConfig();

  if (!isVisible || !statusConfig) {
    return null;
  }

  return (
    <Box
      className={`export-status-indicator ${isVisible ? 'visible' : 'hidden'}`}
    >
      <Box className={`status-container ${statusConfig.bgClass}`}>
        <Box className="status-content">
          <Icon
            name={statusConfig.icon}
            size={20}
            strokeWidth={2}
            className={`status-icon ${statusConfig.iconClass}`}
          />

          <Box className="status-details">
            <h4
              className={`status-title sub-title-text ${
                status === 'completed'
                  ? 'color-green'
                  : status === 'error'
                    ? 'color-red'
                    : ''
              }`}
            >
              {statusConfig.title}
            </h4>
            <p className="status-description content-text">
              {statusConfig.description}
            </p>

            {/* Progress Bar for Processing */}
            {status === 'processing' && (
              <Box className="progress-container">
                <Box className="progress-header">
                  <span className="progress-label content-text">Progress</span>
                  <span className="progress-value content-text-bold">
                    {progress}%
                  </span>
                </Box>
                <Box className="progress-bar-container">
                  <Box
                    className="progress-bar"
                    style={{ width: `${progress}%` }}
                  ></Box>
                </Box>
                {estimatedTime && (
                  <p className="estimated-time content-text">
                    Estimated time: {estimatedTime}
                  </p>
                )}
              </Box>
            )}

            {/* Action Buttons */}
            <Box className="action-buttons">
              {status === 'completed' && onDownload && (
                <>
                  <CustomButton
                    variant="contained"
                    className="download-button green-button"
                    onClick={handleDownload}
                    startIcon={
                      <Icon
                        name="Download"
                        size={14}
                        strokeWidth={2}
                        className="button-icon"
                      />
                    }
                    title={'Download'}
                  />
                </>
              )}

              {status === 'error' && (
                <CustomButton
                  variant="contained"
                  className="download-button red-button"
                  onClick={handleRetry}
                  startIcon={
                    <Icon
                      name="RotateCcw"
                      size={14}
                      strokeWidth={2}
                      className="button-icon"
                    />
                  }
                  title={'Retry'}
                />
              )}

              <CustomButton
                isIconOnly
                className="dismiss-button"
                startIcon={
                  <Tooltip
                    title={<Typography>Close</Typography>}
                    classes={{
                      tooltip: 'info-tooltip-container',
                    }}
                    arrow
                  >
                    <Icon name="X" size={14} strokeWidth={2} />
                  </Tooltip>
                }
                onClick={handleDismiss}
              />
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default ExportStatusIndicator;
