@import '../../../styles/variable.scss';

.custom-button {
  color: $color-White !important;
  border-radius: 8px !important;
  padding: 10px 24px !important;
  text-transform: capitalize !important;
  box-shadow: none !important;

  &:hover {
    box-shadow: none !important;
    box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.02),
      0px 20px 30px 0px rgba(0, 0, 0, 0.1) !important;
    svg {
      fill: $color-Black !important;
    }
  }
  svg {
    fill: $color-White !important;
    width: 21px;
    height: 21px;
  }
}
.secondary-button {
  color: $color-primary !important;
  border: 1px solid $color-primary !important;
}

.custom-button.Mui-disabled {
  // color: $color-White !important;
  box-shadow: none !important;
  opacity: 0.8;
}
