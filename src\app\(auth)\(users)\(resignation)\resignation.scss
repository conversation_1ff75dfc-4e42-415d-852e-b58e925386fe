@import '@/styles/variable.scss';

.User-verify-details {
  display: flex;
  justify-content: space-between;
  @media (max-width: 1200px) {
    display: flex;
    flex-direction: column;
    .verified-user {
      margin-top: var(--spacing-lg);
      justify-content: flex-start;
    }
  }
  @media (max-width: 599px) {
    display: flex;
    flex-direction: column;
    .verified-user {
      margin-top: var(--spacing-lg);
      justify-content: flex-start;
    }
  }
  .deatils {
    margin-left: 0;
  }
  .remark-text {
    // border: 2px solid $color-primary;
    // box-shadow: 4px 4px 6px 6px rgba(0, 0, 0, 0.1);
    box-shadow:
      0 10px 15px -3px rgb(0 0 0 / 0.1),
      0 4px 6px -4px rgb(0 0 0 / 0.1);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-sm);
    background-color: var(--color-white);
    // color: $color-primary;
    width: 100%;
    display: flex;
    margin-top: var(--spacing-base) !important;
    .icon {
      margin-right: var(--spacing-sm);
      margin-top: var(--spacing-xxs);
      // fill: $color-primary !important;
    }
  }
  .w100 {
    width: 100%;
  }
  .mt8 {
    margin-top: var(--spacing-sm);
  }
  .failed-status {
    padding: var(--spacing-none) var(--spacing-sm);
    color: var(--text-error);
  }
  .draft-status {
    padding: var(--spacing-none) var(--spacing-sm);
    color: var(--text-periwinkle-blue);
  }
  .ongoing-status {
    padding: var(--spacing-none) var(--spacing-sm);
    color: var(--text-periwinkle-blue);
  }
  .active-onboarding-status {
    padding: 0px 8px;
    color: $color-green;
  }
  .success-status {
    padding: var(--spacing-none) var(--spacing-sm);
    color: var(--text-olive-green);
  }
}
.resignation-remarks {
  display: block;
}
.resignation-remarks-staff {
  display: block;
  margin-bottom: var(--spacing-sm);
  .remark-section {
    max-height: 200px;
    overflow-y: scroll;
    padding: var(--spacing-md) var(--spacing-xxs);
  }
  .remark-own-section {
    padding: var(--spacing-md) var(--spacing-xxs);
  }

  .remark-text {
    width: 100%;
    // box-shadow: 4px 4px 6px 6px rgba(0, 0, 0, 0.1);
    box-shadow: var(--box-shadow-xs);
    border: var(--normal-sec-border);
  }
}
.actions-width {
  width: 50px;
}
.joining-checklist-details {
  .joining-checklist {
    height: 300px;
    overflow-x: scroll;
  }
  .joining-checkbox {
    display: flex !important;
    flex-direction: row !important;
    align-items: center;
    width: 100%;
    .check-box-text {
      display: flex;
      flex-direction: row-reverse;
      justify-content: flex-start;
      margin-left: var(--spacing-none);
      width: 100%;
      .MuiButtonBase-root {
        padding-top: var(--spacing-none) !important;
        padding-bottom: var(--spacing-none) !important;
      }
      .MuiTypography-root {
        font-family: var(--font-family-primary) !important;
        font-size: var(--font-size-sm) !important;
        line-height: var(--line-height-sm) !important;
        letter-spacing: var(--letter-spacing-sm) !important;
        width: 100%;
      }
      @media (max-width: 768px) {
        margin-right: var(--spacing-none);
      }
    }
  }
  .disabled-joining-checklist {
    .Mui-disabled {
      opacity: 1;
      cursor: not-allowed;
      .MuiTypography-root {
        color: var(--text-color-black) !important;
      }
    }
    .MuiCheckbox-root {
      .MuiSvgIcon-root {
        fill: var(--icon-color-slate-gray) !important;
      }
    }
    .MuiTypography-root {
      opacity: var(--opacity-10);
      color: var(--text-color-black) !important;
      cursor: not-allowed;
    }
  }
  .staff-filter-button {
    display: flex;
    column-gap: var(--spacing-sm);
    margin-top: var(--spacing-2xl);
  }
}
