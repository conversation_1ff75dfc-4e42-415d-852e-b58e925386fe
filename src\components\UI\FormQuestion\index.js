import React from 'react';
import { Box, Typography } from '@mui/material';
import CustomCheckbox from '@/components/UI/CustomCheckbox';

export default function FormQuestion({
  setValue,
  Value,
  ViewAccessOnly,
  question,
  keyName,
}) {
  return (
    <Box className="gender-section">
      <Typography className="title-text" id={keyName}>
        {question}
      </Typography>
      <Box className="form-checkbox">
        <Box id="question">
          <CustomCheckbox
            name={keyName ? keyName : 'ph_health'}
            checked={Value === 'yes'}
            onChange={() => {
              setValue('yes');
            }}
            disabled={ViewAccessOnly}
            labelPlacement="start"
            label="Yes"
            className="check-box-form title-text mr16"
          />
        </Box>
        <Box>
          <CustomCheckbox
            name={keyName ? keyName : 'ph_health'}
            checked={Value === 'no'}
            onChange={() => {
              setValue('no');
            }}
            disabled={ViewAccessOnly}
            labelPlacement="start"
            label="No"
            className="check-box-form title-text"
          />
        </Box>
      </Box>
    </Box>
  );
}
