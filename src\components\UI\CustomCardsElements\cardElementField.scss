.custom-card-element-wrapper {
  .field-label {
    font-family: var(--font-family-primary);
    margin-bottom: var(--spacing-xxs);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-primary);
    &.error-label {
      color: var(--text-error);
    }

    .required {
      color: var(--text-error);
      margin-left: var(--spacing-xxs);
    }
  }

  .card-element-container {
    background-color: var(--field-background);
    border-radius: var(--field-radius);
    border: var(--field-border);
    transition: border 0.3s;
    display: flex;
    align-items: center;
    overflow: hidden;
    &:hover,
    &:focus-within {
      border: var(--field-border-primary);
    }

    .StripeElement {
      width: 100%;
      height: 100%;
      padding: var(--field-padding);
      height: 34px;
    }
    .StripeElement--focus {
      box-shadow: none;
    }

    .StripeElement--invalid {
      border-color: var(--border-color-error);
    }
  }

  &.Mui-error {
    .card-element-container {
      border-color: var(--border-color-error) !important;
      &:hover,
      &:focus-within {
        border-color: var(--border-color-error) !important;
      }
    }
  }

  .field-error-text {
    font-family: var(--font-family-primary);
    color: var(--text-error);
    margin-top: var(--spacing-xxs);
    font-size: var(--font-size-xs);
    line-height: var(--line-height-xs);
  }
}
