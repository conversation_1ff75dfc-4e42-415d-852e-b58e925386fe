import React from 'react';
import { Box, IconButton, Typography, Select, MenuItem } from '@mui/material';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
// import RightArrow from '../../../public/image/icon/chevron-right.svg';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
// import DownArrow from '../../../public/image/icon/chevron-down-small.svg';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import './pagination.scss';

const CustomPagination = ({
  currentPage,
  totalCount,
  rowsPerPage,
  onPageChange,
  className,
  OnRowPerPage,
  isInvited,
}) => {
  const totalPages = Math.ceil(totalCount / rowsPerPage);
  const handlePageSelect = (event) => {
    const selectedPage = parseInt(event?.target?.value, 10);
    onPageChange(selectedPage);
  };

  const pageOptions = Array.from(
    { length: totalPages },
    (_, index) => index + 1
  );
  const pageLimitOptions = isInvited
    ? [12, 24, 50, 100, 250, 500]
    : [10, 25, 50, 100, 250, 500];

  return (
    <>
      <Box
        className={
          className
            ? `pagination-wrap pt24 ${className}`
            : 'pagination-wrap pt24'
        }
      >
        <Box>
          {' '}
          <Select
            value={currentPage}
            onChange={(e) => handlePageSelect(e)}
            className="page-select mr8 p12 fw400"
            IconComponent={ExpandMoreIcon}
            MenuProps={{
              classes: { paper: 'pagination-select' },
            }}
          >
            {pageOptions?.map((page) => (
              <MenuItem key={page} value={page}>
                {page}
              </MenuItem>
            ))}
          </Select>
          <Typography variant="span" className="p14 fw400 page-number">
            of {totalPages} pages
          </Typography>
          <IconButton
            disabled={currentPage === 1}
            onClick={() => onPageChange(currentPage - 1)}
            className="arrow-icon ml24 mr8"
          >
            <ArrowBackIosIcon />
          </IconButton>
          <IconButton
            disabled={currentPage === totalPages}
            onClick={() => onPageChange(currentPage + 1)}
            className="arrow-icon"
          >
            <ArrowForwardIosIcon />
          </IconButton>
          {totalCount ? (
            <Typography variant="span" className="p14 fw400 page-number pl16">
              Total: {totalCount}
            </Typography>
          ) : (
            <></>
          )}
        </Box>

        <Box>
          <Typography
            variant="span"
            className="p14 fw400 page-number per-page-text"
          >
            Rows per page
          </Typography>
          <Select
            value={rowsPerPage}
            onChange={(event) => {
              OnRowPerPage(event?.target?.value);
            }}
            className="page-select mr8 p12 fw400"
            IconComponent={ExpandMoreIcon}
            MenuProps={{
              classes: { paper: 'pagination-select' },
            }}
          >
            {pageLimitOptions?.map((page) => (
              <MenuItem key={page} value={page}>
                {page}
              </MenuItem>
            ))}
          </Select>
        </Box>
      </Box>
    </>
  );
};

export default CustomPagination;
