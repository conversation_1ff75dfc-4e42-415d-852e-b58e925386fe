'use client';
import React, { useState, useEffect, useContext } from 'react';
import { Box, Tab } from '@mui/material';
import { TabContext, TabList, TabPanel } from '@mui/lab';
import SupportTicket from '@/components/OrganizationDetails/SupportTicket';
import PrivacyPolicy from '@/components/OrganizationDetails/PrivacyPolicy';
import OrgList from '@/components/OrganizationDetails/OrgList';
import PaymentProviderHistory from '@/components/PaymentMethod/PymentIdentifier/PaymentProviderHistory';
import SubHistory from '@/components/PaymentMethod/CreateSubPlan/SubPlanHistory';
import { fetchFromStorage } from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import AuthContext from '@/helper/authcontext';
import { useRouter } from 'next/navigation';
import { checkOrganizationRole } from '@/helper/common/commonFunctions';
import './organization.scss';

export default function Organization() {
  const { userdata } = useContext(AuthContext);
  const [tab, setTab] = useState(1);
  const router = useRouter();

  const reports_tabs = [
    { id: 1, name: 'Organizations' },
    { id: 2, name: 'Providers' },
    { id: 3, name: 'Plans' },
    // { id: 4, name: 'Privacy Policy' },
    // { id: 5, name: 'Support Ticket' }
  ];
  const tabChangeHandler = (event, newValue) => {
    setTab(newValue);
  };
  useEffect(() => {
    if (
      fetchFromStorage(identifiers?.RedirectData) &&
      fetchFromStorage(identifiers?.RedirectData)?.planEdit
    ) {
      setTab(3);
    } else if (userdata && userdata?.planEdit) {
      setTab(3);
    }
  }, [
    fetchFromStorage(identifiers?.RedirectData)?.planEdit,
    userdata?.planEdit,
  ]);

  useEffect(() => {
    // const authData = fetchFromStorage(identifiers?.AUTH_DATA);
    if (checkOrganizationRole('org_master')) {
      router.push(`/org/organization`);
    }
  }, []);
  return (
    <Box className="main-page-container">
      <Box className="organization-wrap">
        <TabContext value={String(tab)}>
          <Box className="tabs-wrap">
            <Box className="report-tabs">
              <TabList
                variant="scrollable"
                scrollButtons="auto"
                onChange={tabChangeHandler}
                aria-label="action tabs"
                className="tab-list-sec"
              >
                {reports_tabs?.map((obj, index) => {
                  return (
                    <Tab
                      key={index}
                      label={obj?.name}
                      value={String(obj?.id)}
                      className="tab-name"
                    />
                  );
                })}
              </TabList>
            </Box>
          </Box>
          <TabPanel value="1" className="pl0 pr0 pb0">
            <OrgList />
          </TabPanel>
          <TabPanel value="2" className="pl0 pr0 pb0">
            <PaymentProviderHistory />
          </TabPanel>
          <TabPanel value="3" className="pl0 pr0 pb0">
            <SubHistory />
          </TabPanel>
          <TabPanel value="4" className="pl0 pr0 pb0">
            <PrivacyPolicy />
          </TabPanel>
          <TabPanel value="5" className="pl0 pr0 pb0 pt0">
            <SupportTicket />
          </TabPanel>
        </TabContext>
      </Box>
    </Box>
  );
}
