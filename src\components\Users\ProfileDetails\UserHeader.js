'use client';

import React, { useState, useRef, useContext } from 'react';
import { Box, Typography, Tooltip } from '@mui/material';
import { useRouter } from 'next/navigation';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import MarkEmailReadOutlinedIcon from '@mui/icons-material/MarkEmailReadOutlined';
import VerifiedIcon from '@mui/icons-material/Verified';
import EditIcon from '@mui/icons-material/Edit';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import HeaderImage from '@/components/UI/ImageSecurity';
import { RoleIcon } from '@/helper/common/images';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import { DateFormat } from '@/helper/common/commonFunctions';
// crop image function
import CropImage from '@/components/UI/CropImage';
import DialogBox from '@/components/UI/Modalbox';
import AuthContext from '@/helper/authcontext';

const getStatusClass = (status) => {
  const statusClasses = {
    rejected: 'failed',
    deleted: 'failed',
    cancelled: 'failed',
    ongoing: 'ongoing',
    draft: 'draft',
    pending: 'draft',
    completed: 'active-onboarding',
    verified: 'active-onboarding',
    default: 'success',
  };

  return statusClasses[status] || statusClasses.default;
};
const getInvitationStatusClass = (status) => {
  const statusClasses = {
    invited: 'invited-ongoing',
    reinvited: 'invited-draft',
    accepted: 'invited-accepted',
    default: 'success',
  };

  return `sub-title-text invited-status ${statusClasses[status] || statusClasses.default} text-capital`;
};

const ProfileHeader = ({
  profileImage,
  // oldStaff,
  ViewAccessOnly,
  setProfileImage,
  UserDetails,
  // UserId,
  user_full_name,
  ResendInvitation,
  isMyProfile,
  // isAdmin,
}) => {
  // {/* crop image function */}
  const [open, setOpen] = useState(false);
  const [file, setFile] = useState();
  const router = useRouter();
  const fileInputRef = useRef(null);
  const { planDetail, authState, setRestrictedLimitModal } =
    useContext(AuthContext);

  const verifiedByName = (verifiedName) => {
    if (verifiedName) {
      let name =
        verifiedName?.length > 25
          ? `${verifiedName?.substring(0, 25)}...`
          : verifiedName;
      return name;
    }
  };

  //   /* crop image function
  const onOpenDialog = () => {
    setOpen(true);
  };
  const onhandleclose = () => {
    setOpen(false);
    setFile();
  };
  const getCropData = async (cropper) => {
    var baseText = cropper.getCroppedCanvas().toDataURL();
    const blob = await (await fetch(baseText)).blob();
    const files = new File([blob], file?.preview, {
      type: file?.type,
      lastModified: new Date(),
    });
    // const formData = new FormData();
    // formData.append('data', files);
    setProfileImage({
      url: baseText,
      file: files,
    });
    onhandleclose();
  };

  return (
    <>
      <Box className="User-verify-details">
        <Box className="user-deatils user-all-details">
          {!isMyProfile && (
            <ArrowBackIosIcon
              className="cursor-pointer mt4"
              onClick={() => {
                // if (isAdmin) {
                router.back();
                // } else {
                //   const routeMap = {
                //     IsDocument: `/document-staff/${oldStaff?.docId}`,
                //     isContract: '/contract-renewal',
                //     IsInvite: '/invited-staff',
                //     leaveMark: '/leave-remark',
                //     changeReq: '/change-request',
                //     regignationRemark: '/resignation-remark',
                //     IsAdmin: '/adminuser',
                //     leaveBal: '/leave-reports',
                //     leaveCon: '/leave-reports',
                //   };
                //   const route = Object.keys(routeMap).find(
                //     (key) => oldStaff?.[key]
                //   );
                //   setTimeout(() => {
                //     router?.push(route ? routeMap[route] : '/staff');
                //   }, 1000);
                // }
              }}
            />
          )}

          <Box className="user-box">
            <Box className={'profile-image'}>
              {profileImage?.url ? (
                <HeaderImage
                  imageUrl={profileImage?.url}
                  alt="users"
                  style={{ marginRight: '4px' }}
                  className="w100 h100"
                  type="avtar"
                />
              ) : ViewAccessOnly ? (
                <AccountCircleIcon />
              ) : (
                <AccountCircleIcon />
              )}

              {!ViewAccessOnly && (
                <>
                  <label
                    htmlFor="file-input"
                    className="file-input-label cursor-pointer"
                  >
                    {profileImage?.url ? (
                      <EditIcon className="edit-icon" />
                    ) : (
                      <EditIcon className="edit-icon edit-profile" />
                    )}
                  </label>
                  <input
                    type="file"
                    id="file-input"
                    ref={fileInputRef}
                    onChange={(e) => {
                      if (e.target.files && e.target.files[0]) {
                        const selectedFile = e.target.files[0];

                        // Check if storage is full
                        const totalStorage = planDetail?.total_storage || 0;
                        const usedStorage =
                          authState?.subscriptionUsage?.total_size_gb || 0;
                        const fileSizeInGB =
                          selectedFile.size / (1024 * 1024 * 1024); // Convert bytes to GB

                        if (usedStorage + fileSizeInGB > totalStorage) {
                          setRestrictedLimitModal({
                            storageLimit: true,
                            totalStorage: planDetail?.total_storage,
                            usedStorage:
                              authState?.subscriptionUsage?.total_size_gb,
                          });
                          fileInputRef.current.value = null;
                          return;
                        }

                        // const imageUrl = URL.createObjectURL(e.target.files[0]);
                        // setProfileImage({
                        //   url: imageUrl,
                        //   file: e.target.files[0],
                        // });
                        onOpenDialog();
                        //  /* crop image function */
                        setFile(
                          Object.assign(selectedFile, {
                            preview: URL.createObjectURL(selectedFile),
                          })
                        );
                        fileInputRef.current.value = null;
                        // setIsAnychange(true);
                      }
                    }}
                    hidden
                    accept="image/*"
                  />
                </>
              )}
            </Box>
          </Box>
          <Box className="deatils w100">
            <Typography className="title-sm user-name d-flex align-center">
              <span className="text-ellipsis-line w100">{user_full_name}</span>
            </Typography>
            {UserDetails?.user_roles?.length > 0 && (
              <Box className="d-flex align-center">
                {UserDetails.user_roles.length > 1 ? (
                  <Tooltip
                    classes={{
                      tooltip: 'info-tooltip-container ',
                    }}
                    title={UserDetails.user_roles
                      .filter((role) => {
                        const activeRoleId =
                          UserDetails?.web_user_active_role_id ??
                          UserDetails?.user_active_role_id;
                        return role?.value !== activeRoleId;
                      })
                      .map((role, index, filteredRoles) => (
                        <Typography
                          key={role.value}
                          className="sub-title-text fw600 text-align"
                        >
                          {role.label}
                          {index < filteredRoles.length - 1 ? ',' : ''}
                        </Typography>
                      ))}
                  >
                    <span className="user-roles cursor-pointer">
                      {RoleIcon()}
                    </span>
                  </Tooltip>
                ) : (
                  <span className="user-roles">{RoleIcon()}</span>
                )}

                <Typography className="title-text fw400 user-date">
                  <span className="active-role">
                    {(() => {
                      const activeRoleId =
                        UserDetails?.web_user_active_role_id ??
                        UserDetails?.user_active_role_id;
                      return (
                        UserDetails.user_roles.find(
                          (role) => role.value === activeRoleId
                        )?.label ?? ''
                      );
                    })()}
                  </span>
                </Typography>
              </Box>
            )}
            {(UserDetails?.createdAt || UserDetails?.user_status) && (
              <Box className="d-flex align-center">
                <Tooltip
                  title={<Typography>Created Date</Typography>}
                  classes={{
                    tooltip: 'info-tooltip-container ',
                  }}
                >
                  <CalendarMonthIcon className="created-date cursor-pointer" />
                </Tooltip>
                <Typography className="sub-title-text fw400 user-date">
                  {DateFormat(UserDetails?.createdAt, 'datesWithhourUTC')}
                </Typography>
                <Typography
                  className={`ml16 sub-title-text fw600 text-capital ${getStatusClass(UserDetails?.user_status)}`}
                >
                  {UserDetails?.user_status}{' '}
                </Typography>
              </Box>
            )}
          </Box>
        </Box>
        {isMyProfile ? (
          <></>
        ) : (UserDetails && UserDetails?.user_status === 'pending') ||
          !UserDetails?.confirm_by ? (
          <Box className="invite-icon">
            {UserDetails?.user_invitation_action &&
              UserDetails?.user_invitation_action?.action_by
                ?.user_full_name && (
                <Box className="user-deatils mb8">
                  <MarkEmailReadOutlinedIcon className="mail-icon" />
                  {UserDetails?.user_invitation_action?.action_by
                    ?.user_full_name && (
                    <span className="sub-title-text">
                      {' '}
                      {UserDetails?.user_invitation_action?.action_by
                        ?.user_full_name + ' '}
                    </span>
                  )}
                  {UserDetails?.user_invitation_action?.updatedAt && (
                    <span className="sub-title-text">
                      {' ( '}
                      {DateFormat(
                        UserDetails?.user_invitation_action?.updatedAt,
                        'datesWithhourUTC'
                      )}
                      {' ) '}
                    </span>
                  )}

                  {UserDetails?.user_invitation_action ? (
                    <>
                      <span
                        className={getInvitationStatusClass(
                          UserDetails?.user_invitation_action?.invitation_status
                        )}
                      >
                        {UserDetails?.user_invitation_action?.invitation_status}
                      </span>
                    </>
                  ) : (
                    <></>
                  )}
                </Box>
              )}

            {UserDetails?.user_status === 'pending' &&
              UserDetails?.user_invitation_action?.invitation_status !==
                'accepted' && (
                <Typography
                  className="title-text ongoing fw600 mt8  resend-button cursor-pointer"
                  onClick={() => ResendInvitation(UserDetails?.id)}
                >
                  {' '}
                  Resend invitation
                </Typography>
              )}
          </Box>
        ) : (
          <>
            {UserDetails &&
              UserDetails?.confirm_by &&
              UserDetails?.confirm_by_date && (
                <Box className="invitation-icon">
                  {UserDetails?.user_invitation_action?.action_by
                    ?.user_full_name && (
                    <Box className="user-deatils">
                      <MarkEmailReadOutlinedIcon className="mail-icon" />
                      {UserDetails?.user_invitation_action?.action_by
                        ?.user_full_name && (
                        <span className="sub-title-text">
                          {' '}
                          {UserDetails?.user_invitation_action?.action_by
                            ?.user_full_name + ' '}
                        </span>
                      )}
                      {UserDetails?.user_invitation_action?.updatedAt && (
                        <span className="sub-title-text">
                          {' ( '}
                          {UserDetails?.user_invitation_action?.updatedAt
                            ? DateFormat(
                                UserDetails?.user_invitation_action?.updatedAt,
                                'datesWithhourUTC'
                              )
                            : ''}
                          {' ) '}
                        </span>
                      )}
                      {UserDetails?.user_invitation_action &&
                      UserDetails?.user_invitation_action?.invitation_status ? (
                        <>
                          <span
                            className={getInvitationStatusClass(
                              UserDetails?.user_invitation_action
                                ?.invitation_status
                            )}
                          >
                            {
                              UserDetails?.user_invitation_action
                                ?.invitation_status
                            }
                          </span>
                        </>
                      ) : (
                        <></>
                      )}
                    </Box>
                  )}

                  {UserDetails?.confirm_by && (
                    <Box className="user-deatils mt8 mb8">
                      <VerifiedIcon className="verified-icon" />
                      <span className="sub-title-text">
                        {' '}
                        {verifiedByName(UserDetails?.confirm_by)}
                      </span>
                      <span className="sub-title-text">
                        {' ( '}{' '}
                        {UserDetails?.confirm_by_date
                          ? DateFormat(
                              UserDetails?.confirm_by_date,
                              'datesWithhourUTC'
                            )
                          : ''}
                        {' ) '}
                      </span>
                    </Box>
                  )}
                </Box>
              )}
          </>
        )}
      </Box>
      {/* crop image function */}
      <DialogBox
        handleClose={() => onhandleclose()}
        open={open}
        title={'Change picture'}
        className="small-dialog-box-container"
        content={
          <>
            <CropImage
              file={file}
              getCropData={getCropData}
              onhandleclose={onhandleclose}
              isProfile={true}
            />
          </>
        }
      />
    </>
  );
};

export default ProfileHeader;
