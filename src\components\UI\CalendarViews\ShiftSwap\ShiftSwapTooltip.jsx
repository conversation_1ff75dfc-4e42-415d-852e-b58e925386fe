'use client';

import React, { useRef, useState } from 'react';
import { <PERSON><PERSON><PERSON>wayListener, Divider, Pop<PERSON>, Tooltip } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import BusinessCenterOutlinedIcon from '@mui/icons-material/BusinessCenterOutlined';
import LocationOnOutlinedIcon from '@mui/icons-material/LocationOnOutlined';
import SearchIcon from '@mui/icons-material/Search';
import AccountCircleOutlinedIcon from '@mui/icons-material/AccountCircleOutlined';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import CustomButton from '../../CustomButton';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import moment from 'moment';
import axiosInstanceOrg from '@/helper/axios/axiosInstanceOrg';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { ROTA_URLS } from '@/helper/constants/urls';
import PreLoader from '@/components/UI/Loader';
import UserAvatar from '../../Avatar/UserAvatar';
import './shiftSwapTooltip.scss';

// Validation schema using Yup
const AddShiftSchema = Yup.object().shape({
  selectedEmployee: Yup.object()
    .shape({
      id: Yup.string().required(),
      user_full_name: Yup.string().required(),
    })
    .required('Employee is required'),
});

export const ShiftSwapTooltip = ({
  staffListOptions,
  handleAddShiftTooltipClose,
  selectedShiftData,
  getRotaShiftList,
}) => {
  const [isLoading, setIsLoading] = useState(false);

  const [isShiftDropdownOpen, setIsShiftDropdownOpen] = useState(false);
  const [searchStaff, setSearchStaff] = useState('');

  const shiftDropdownRef = useRef(null);

  // Initial form values
  const initialValues = {
    selectedEmployee: null,
  };

  const start_time = moment(selectedShiftData?.startTime).utc().format('HH:mm');
  const end_time = moment(selectedShiftData?.endTime).utc().format('HH:mm');
  const role_name = selectedShiftData?.role?.role_name || 'No Role';
  const shift_location =
    selectedShiftData?.branch?.branch_name || 'No Location';

  // Filter staffListOptions based on search
  const filteredStaffList = staffListOptions
    ?.slice(1)
    ?.filter((item) =>
      item?.user_roles?.some(
        (role) =>
          role?.role_id === selectedShiftData?.roleId ||
          role?.id === selectedShiftData?.roleId
      )
    )
    ?.filter((dpt) => dpt?.department?.id === selectedShiftData?.departmentId)
    ?.filter((obj) => obj?.id !== selectedShiftData?.userId)
    ?.filter((emp) =>
      emp?.user_full_name?.toLowerCase().includes(searchStaff.toLowerCase())
    );

  const handleSubmit = async (values, { setSubmitting }) => {
    const sendData = {
      userId: selectedShiftData?.userId,
      toUserId: values?.selectedEmployee?.id,
      roleId: selectedShiftData?.roleId,
      shiftId: selectedShiftData?.id,
    };

    setIsLoading(true);
    try {
      const { status, data } = await axiosInstanceOrg.post(
        ROTA_URLS?.SWAP_SHIFT_URL,
        sendData
      );
      if (status === 200 || status === 201) {
        setApiMessage('success', data?.message);
        setIsLoading(false);
        getRotaShiftList();
        handleAddShiftTooltipClose();
        setIsLoading(false);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setIsLoading(false);
    }

    setSubmitting(false);
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={AddShiftSchema}
      onSubmit={handleSubmit}
    >
      {({ values, setFieldValue, isSubmitting, dirty }) => {
        return (
          <Form className="shift-swap-container">
            <div>{isLoading && <PreLoader />}</div>
            <div className="shift-swap-header-container">
              <div>
                <Tooltip
                  title={
                    <p className="p12">
                      Shift swaps allow you to ask a co-worker to cover one of
                      your shifts. You can also offer to work one in return.
                    </p>
                  }
                  placement="top"
                  arrow
                  classes={{
                    tooltip:
                      'calendar-event-list-tooltip-custom calendar-event-left-shift-tooltip',
                  }}
                >
                  <p className="shift-swap-header-title">Request shift swap</p>
                </Tooltip>
              </div>

              <div className="shift-swap-header-actions">
                <CustomButton
                  title={isSubmitting ? 'Confirm...' : 'Confirm'}
                  type="submit"
                  disabled={isSubmitting || !dirty}
                />

                <div
                  className="close-shift-tooltip"
                  onClick={handleAddShiftTooltipClose}
                >
                  <CloseIcon />
                </div>
              </div>
            </div>
            <Divider />
            <div className="shift-swap-details-container">
              <div className="shift-swap-details-wrapper">
                <div className="shift-swap-details">
                  <div className="shift-swap-user-details">
                    <div>
                      <UserAvatar
                        name={selectedShiftData?.user?.user_full_name}
                        src={selectedShiftData?.user?.user_avatar_link}
                        classname="shift-swap-avatar"
                      />
                    </div>
                    <p className="shift-swap-name">Your shift</p>
                  </div>
                  <div className="shift-swap-shift-details">
                    <div className="shift-swap-shift-time-role">
                      <p className="shift-swap-shift-date">
                        {moment(
                          selectedShiftData?.startTime || new Date()
                        ).format('ddd DD MMM YYYY')}
                      </p>
                      <p className="shift-swap-shift-time">
                        {start_time} - {end_time}
                      </p>
                      <p className="shift-swap-shift-icon-text">
                        <BusinessCenterOutlinedIcon />
                        <span>{role_name}</span>
                      </p>
                      <p className="shift-swap-shift-icon-text">
                        <LocationOnOutlinedIcon />
                        <span>{shift_location}</span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                {/* User Dropdown */}
                <div className="d-center-start shift-select-dropdown">
                  <div className="icon">
                    <AccountCircleOutlinedIcon />
                  </div>
                  <div className="dropdown-wrapper" ref={shiftDropdownRef}>
                    <div
                      className="dropdown-header"
                      onClick={() =>
                        setIsShiftDropdownOpen(!isShiftDropdownOpen)
                      }
                    >
                      <p>
                        <span className="role-left-border"></span>
                        {values.selectedEmployee?.user_full_name ? (
                          <span>{values.selectedEmployee?.user_full_name}</span>
                        ) : (
                          <span className="no-select-text">
                            Select employee
                          </span>
                        )}
                      </p>
                      <span className="arrow">
                        {isShiftDropdownOpen ? (
                          <ExpandLessIcon />
                        ) : (
                          <ExpandMoreIcon />
                        )}
                      </span>
                    </div>

                    <Popper
                      open={isShiftDropdownOpen}
                      anchorEl={shiftDropdownRef.current}
                      placement="bottom-start"
                      className="dropdown-popper"
                      modifiers={[
                        {
                          name: 'preventOverflow',
                          options: { boundary: 'window' },
                        },
                        {
                          name: 'flip',
                          options: {
                            fallbackPlacements: ['top-start', 'right-start'],
                          },
                        },
                      ]}
                    >
                      <ClickAwayListener
                        onClickAway={() => setIsShiftDropdownOpen(false)}
                      >
                        <div className="dropdown-content">
                          <div className="search-input-wrapper">
                            <SearchIcon className="search-icon" />
                            <input
                              type="text"
                              placeholder="Search employee"
                              className="search-input"
                              value={searchStaff}
                              onChange={(e) => setSearchStaff(e.target.value)}
                            />
                          </div>
                          <ul>
                            {filteredStaffList?.map((emp) => (
                              <li
                                key={emp?.id}
                                className={
                                  values.selectedEmployee?.id === emp?.id
                                    ? 'selected'
                                    : ''
                                }
                                onClick={() => {
                                  setFieldValue('selectedEmployee', emp);
                                  setIsShiftDropdownOpen(false);
                                }}
                              >
                                <span className="role-left-border"></span>
                                {emp?.user_full_name}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </ClickAwayListener>
                    </Popper>
                  </div>
                </div>
              </div>
            </div>
          </Form>
        );
      }}
    </Formik>
  );
};
