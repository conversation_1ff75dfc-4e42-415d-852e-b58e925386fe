.budget-section {
  position: relative;
  background-color: var(--color-white);
  padding: var(--spacing-xxl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-xs);
  height: 100%;
  .content-loader {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
  }
  .budget-50-filter {
    display: flex;
    gap: 8px;
    align-items: flex-start;
    flex-wrap: wrap;
    // display: grid;
    // grid-template-columns: repeat(auto-fill, minmax(max-content, 1fr));
    // width: 100%;
    [class*='SBaseTrigger']:not([class*='neighborLocation_left']) {
      height: 27px;
    }
    @media (max-width: 576px) {
      display: flex;
      row-gap: 15px;
    }
  }
  .budget-search {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 8px;
    align-items: flex-start;
    margin-top: 16px;
  }
  .category-section {
    display: flex;
    gap: var(--spacing-sm);
    .MuiButtonBase-root {
      padding: 5px 8px !important;
      svg {
        width: 25px;
        height: 25px;
      }
    }
  }
  .check-box-form {
    margin-left: 0;
    padding-bottom: 4px;
    .MuiFormControlLabel-label {
      font-family: var(--font-family-primary);
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-regular);
      color: var(--text-color-black);
      line-height: var(--line-height-base);
    }
    .MuiCheckbox-root {
      padding: 0;
      padding-right: 8px;
      svg {
        fill: var(--color-primary);
      }
    }
  }
  .table-graph-view {
    display: flex;
    justify-content: flex-end;
    // margin-top: 24px;
    .icon-sec {
      display: flex;
      justify-content: center;
      align-items: center;
      // width: 30px;
      height: 32px;
      border: 1px solid var(--color-primary);
      border-collapse: collapse;
      color: var(--color-primary);
      cursor: pointer;
      .view-icon {
        height: max-content;
        display: flex;
        align-items: center;
        padding: 0px 6px;
        span {
          height: max-content;
          display: flex;
          align-items: center;
        }
        // svg {
        // margin: 4px 10px;
        // margin-right: 6px;
        // }
      }
    }
    .graph {
      border-radius: 0 5px 5px 0;
    }
    .table {
      border-radius: 5px 0 0 5px;
    }
    svg {
      width: 24px !important;
      height: 24px !important;
      fill: none !important;
    }
    .active-svg {
      background-color: var(--color-primary);
      color: var(--color-White);
      svg {
        stroke: var(--color-White);
      }
    }
  }

  .filter-name {
    max-width: max-content;
    span,
    svg {
      width: 18px;
      height: 18px;
    }
    svg {
      fill: var(--color-primary);
      margin-left: 4px;
      cursor: pointer;
    }
  }
  .prior-filter {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
    .apply-prior {
      margin-top: -10px;
    }
    .budget-prior-filter {
      width: 255px;
      @media (max-width: 476px) {
        width: 100%;
      }
    }
    .custom-button {
      padding: 2px 12px !important;
    }
  }

  .budget-month-filter {
    display: flex;
    flex-direction: column;
  }
  .budget-branch-filter {
    position: relative;

    .input-label {
      position: absolute;
      left: 0;
      top: -24px;
    }
    @media (max-width: 501px) {
      margin-top: 12px;
    }
  }
  .budget-custom-date-fields {
    min-width: 214px;
    max-width: 214px;
    height: 30px;
    .MuiFormLabel-root {
      top: -8px;
    }
    .react-Monthpicker-sec {
      height: 50px;
      margin: 0px !important;
      margin-top: -15px !important;
      @media (max-width: 767px) {
        // margin-top: -0px !important;
      }
      .date-picker-container {
        margin-top: 0 !important;
        .react-datepicker-wrapper:first-child {
          margin-right: 0px;
        }
      }
      .MuiFormControl-root {
        width: 100%;
        max-width: 100% !important;
      }
    }
    .react-datepicker-popper {
      z-index: 1000;
    }
  }
  .year-range {
    font-size: 14px;
  }
}

.Create-budget-drawer {
  .MuiPaper-root {
    width: 285px !important;
    min-width: 285px !important;
    padding: 12px 17px 0px !important;
    @media (max-width: 576px) {
      width: 100% !important;
      min-width: 100% !important;
    }
  }

  .info-label {
    display: flex;
    align-items: flex-start;
    text-transform: capitalize;
    .info-icon {
      width: 16px;
      height: 16px;
      margin-left: 5px;
      svg {
        width: 13px;
        height: 13px;
        fill: var(--color-primary);
        cursor: pointer;
      }
    }
  }

  .Create-budget {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .budget-filters {
      width: 100%;

      .budget-filter-selection.error-budget-selection {
        border: 1px dashed var(--color-danger);
      }
      .add-budget-section {
        // display: grid;
        // grid-template-columns: calc(50% - 4px) calc(50% - 4px);
        // gap: 8px;
        // align-items: center;
        // // margin-top: 12px;
        // @media (max-width: 576px) {
        // 	display: grid;
        // 	grid-template-columns: 100%;
        // 	row-gap: 15px;
        // }
        .budget-cat-selection {
          border: var(--normal-sec-border);
          padding: 8px;
          display: flex;
          flex-direction: column;
          gap: 8px;
          position: relative;
          height: 100%;

          .cancel-icon {
            position: absolute;
            right: -8px;
            top: -8px;
            width: 18px;
            height: 18px;
            fill: var(--color-primary);
            cursor: pointer;
          }
        }
        .error-budget-selection {
          border: 1px dashed var(--color-danger);
        }
      }
    }

    .create-cancel-button {
      .custom-button {
        padding: 7px 24px !important;
      }
    }

    .date-picker-textfield {
      .MuiInputBase-input {
        padding: 0 12px;
        font-size: 14px;
      }
      .MuiOutlinedInput-notchedOutline {
        height: 30px;
        min-height: 30px;
        border-radius: 5px !important;
      }
      .MuiButtonBase-root {
        padding: 0 6px;
        svg {
          width: 21px;
          height: 21px;
        }
      }
    }
    .budget-custom-date-fields {
      .react-Monthpicker-sec {
        .date-picker-container {
          margin-top: 0 !important;
          .react-datepicker-wrapper:first-child {
            margin-right: 0px;
          }
        }
        .MuiFormControl-root {
          width: 100%;
          max-width: 100% !important;
        }
      }
      .react-datepicker-popper {
        z-index: 1000;
      }
    }
  }

  .noted-text {
    background-color: var(--color-light-grayish-blue);
    padding: 12px 12px;
  }
  .year-range {
    font-size: 14px;
  }
}
.budget-com-info {
  z-index: 99999 !important;
  width: 300px;

  .category-toggle {
    width: 30px;
    height: 16px;
    .MuiFormControlLabel-root {
      margin-right: 0;
      margin-left: 0;
    }
    .MuiSwitch-root {
      width: 30px;
      height: 16px;
    }
    .MuiSwitch-thumb {
      width: 12px;
      height: 12px;
    }

    .MuiSwitch-track {
      background-color: var(--color-primary) !important;
    }
  }
  .checked-toggle {
    .MuiSwitch-switchBase {
      margin: 2px -1px;
    }
  }
  .unchecked-toggle {
    .MuiSwitch-switchBase {
      margin: 2px 3px;
    }
  }
  .category-text {
    margin-left: 8px;
  }
  .sub-text {
    text-wrap: wrap;
    width: 240px;
  }
  .noted-text {
    background-color: var(--color-light-grayish-blue);
    padding: 8px 6px;
    text-wrap: wrap;
    width: 240px;
    margin-top: 8px;
  }
}
.export-popover {
  .MuiPaper-root {
    width: 120px !important;
    margin-top: 8px;
    padding: 10px 20px;
    box-shadow:
      rgba(0, 0, 0, 0.2) 0px 5px 5px -3px,
      rgba(0, 0, 0, 0.14) 0px 8px 10px 1px,
      rgba(0, 0, 0, 0.12) 0px 3px 14px 2px;
  }

  .export-option {
    p {
      text-align: center;
    }
  }
}
.tri-state-toggle-wrap {
  margin-right: 8px;
  .green {
    background: rgb(74 161 129 / 16%);
    input {
      background-color: var(--color-green);
    }
  }
  .gray {
    background: #c3c4c7;
    input {
      background-color: var(--color-light-grayish-blue);
    }
  }
  .blue {
    background: rgb(69 130 195 / 16%);
    input {
      background-color: var(--btn-color-bright-blue);
    }
  }
  .orange {
    background: rgb(227 112 87 / 16%);
    input {
      background-color: var(--color-orange);
    }
  }
  .tri-state-toggle {
    cursor: pointer;
    width: fit-content;
    max-height: 22px;
    border-radius: 12px;
    // padding: 4px;
    padding: 5px 4px;
    width: 40px;
    display: flex;
    justify-content: space-around;
    input {
      height: 12px;
      width: 12px;
      appearance: none;
      border-radius: 50%;
      opacity: 0;
    }
    input:hover {
      cursor: pointer;
    }
    .active {
      opacity: 1;
      margin-bottom: 2px;
    }
  }
}
.budget-custom-date-fields {
  .react-Monthpicker-sec {
    height: 50px;
    margin: 0px !important;
    .MuiFormLabel-root {
      margin-bottom: 0;
    }
    .react-datepicker__year-text--selected {
      background-color: var(--color-primary);
      color: var(--color-white);
    }
    .date-picker-container {
      margin-top: 0 !important;
      .react-datepicker-wrapper:first-child {
        margin-right: 5px;
      }
    }
    .react-datepicker-popper {
      z-index: 2;
    }
    .MuiFormControl-root {
      width: 100%;
      max-width: 225px;
      padding: 2px 0 2px;
      background: var(--field-background);
      border-radius: var(--field-radius) !important;
      input {
        padding: 3px 10px;
        font-size: 14px;
      }
      fieldset {
        height: 35px !important;
        min-height: 35px !important;
        border-radius: var(--field-radius) !important;
        // background: var(--field-background);
        border: var(--field-border) !important;
        legend {
          line-height: 2px !important;
        }
      }
    }
    .MuiInputBase-root {
      display: flex;
      align-items: center;

      .MuiInputAdornment-root {
        padding: 5px;

        svg {
          height: 20px;
          width: 20px;
          fill: var(--color-primary);
        }
      }
    }
  }
}
.previous-budget-details {
  .previous-year {
    display: flex;
    align-items: flex-end;
    gap: 30px;
  }
  .custom-button {
    padding: 4px 24px !important;
    max-height: max-content;
    margin-bottom: -1px;
  }
}
.prior-filter-default,
.prior-filter-default div,
.prior-filter-default input {
  cursor: default !important;
}
.select-input-wrap {
  .all-category-wrap-budget {
    padding: 10px 0px 10px 10px;
    max-width: calc(100% - 20px);

    .main-category-wrap {
      font-size: 16px;
      color: var(--color-primary);
    }
  }
}
