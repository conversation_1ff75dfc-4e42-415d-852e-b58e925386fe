'use client';
import React, { useState } from 'react';
import { Splide, SplideSlide } from '@splidejs/react-splide';
import Icon from '@/components/UI/AppIcon/AppIcon';
import LinkIcon from '@mui/icons-material/Link';
import DialogBox from '@/components/UI/Modalbox';
import { stopAllAudio } from '@/helper/common/audioUtils';
import '@splidejs/splide/css';
import './MediaIconsCarousel.scss';

const MediaIconsCarousel = ({ mediaIcons = [], onAudioClick }) => {
  const [selectedDocument, setSelectedDocument] = useState(null);
  const [showDocumentModal, setShowDocumentModal] = useState(false);

  // Helper function to check if document can be previewed in iframe
  const canPreviewInIframe = (url) => {
    if (!url) return false;

    const lowerUrl = url.toLowerCase();
    // Only PDFs and some text files can be previewed directly in iframe
    return (
      lowerUrl.includes('.pdf') ||
      lowerUrl.includes('application/pdf') ||
      lowerUrl.includes('.txt') ||
      lowerUrl.includes('text/plain')
    );
  };

  // Helper function to get preview URL for documents that can't be directly previewed
  const getPreviewUrl = (url) => {
    if (!url) return null;

    // Use Google Docs Viewer for office documents
    const lowerUrl = url.toLowerCase();
    if (
      lowerUrl.includes('.docx') ||
      lowerUrl.includes('.doc') ||
      lowerUrl.includes('.pptx') ||
      lowerUrl.includes('.ppt') ||
      lowerUrl.includes('.xlsx') ||
      lowerUrl.includes('.xls') ||
      lowerUrl.includes('officedocument')
    ) {
      return `https://docs.google.com/gview?url=${encodeURIComponent(url)}&embedded=true`;
    }

    return url;
  };

  const handleIconClick = (mediaItem) => {
    if (mediaItem.type === 'audio') {
      // Ensure onAudioClick is called with proper data
      if (onAudioClick && typeof onAudioClick === 'function') {
        onAudioClick(mediaItem);
      } else {
        // Fallback: Try to play audio directly
        const audio = new Audio(mediaItem.url);
        audio.play().catch(() => {
          // If direct play fails, open in new tab
          window.open(mediaItem.url, '_blank', 'noopener,noreferrer');
        });
      }
    } else if (mediaItem.type === 'document') {
      // Stop all audio before opening document
      stopAllAudio();

      // Check if document can be previewed
      if (
        canPreviewInIframe(mediaItem.url) ||
        getPreviewUrl(mediaItem.url) !== mediaItem.url
      ) {
        const previewUrl = getPreviewUrl(mediaItem.url);

        setSelectedDocument({
          ...mediaItem,
          previewUrl: previewUrl,
        });
        setShowDocumentModal(true);
      } else {
        // For unsupported file types, download directly
        window.open(mediaItem.url, '_blank', 'noopener,noreferrer');
      }
    } else if (mediaItem.type === 'link' && mediaItem.url) {
      // Stop all audio before opening link
      stopAllAudio();
      window.open(mediaItem.url, '_blank', 'noopener,noreferrer');
    }
  };

  const handleCloseDocumentModal = () => {
    setShowDocumentModal(false);
    setSelectedDocument(null);
  };

  // Function to determine if arrows should be shown based on screen size and item count
  const shouldShowArrows = () => {
    const totalItems = mediaIcons.length;

    // Get current screen width
    const screenWidth =
      typeof window !== 'undefined' ? window.innerWidth : 1024;

    // Determine items per page based on breakpoints (same as Splide config)
    let itemsPerPage = 13; // Default desktop
    if (screenWidth <= 319) {
      itemsPerPage = 4;
    } else if (screenWidth <= 575) {
      itemsPerPage = 6;
    } else if (screenWidth <= 767) {
      itemsPerPage = 10;
    }

    // Show arrows only if total items exceed items per page
    return totalItems > itemsPerPage;
  };

  return (
    <div className="media-icons-carousel-container">
      <Splide
        options={{
          perPage: 13,
          gap: '15px',
          rewind: false,
          pagination: false,
          arrows: shouldShowArrows(),
          perMove: 1,
          breakpoints: {
            767: {
              perPage: 10,
              gap: '15px',
            },
            575: {
              perPage: 6,
              gap: '15px',
            },
            319: {
              perPage: 4,
              gap: '15px',
            },
          },
        }}
        className="media-icons-carousel"
      >
        {mediaIcons.map((mediaItem) => (
          <SplideSlide key={mediaItem.id}>
            <div
              className="media-icon cursor-pointer"
              onClick={() => handleIconClick(mediaItem)}
              title={mediaItem.type === 'link' ? mediaItem.url : ''}
            >
              {mediaItem.type === 'link' ? (
                <LinkIcon
                  style={{
                    fontSize: 30,
                    color: 'black',
                  }}
                  className={`media-icon__${mediaItem.type}`}
                />
              ) : (
                <Icon
                  name={mediaItem.icon}
                  size={30}
                  color="black"
                  className={`media-icon__${mediaItem.type}`}
                />
              )}
            </div>
          </SplideSlide>
        ))}
      </Splide>

      <DialogBox
        open={showDocumentModal}
        handleClose={handleCloseDocumentModal}
        title={selectedDocument?.name || 'Document Preview'}
        closeIconShow={false}
        onCloseStatus={true}
        dividerClass="document-preview-divider"
        contentClassName="document-preview-content"
        titleClassName="document-preview-title"
        contenttext=""
        className="fullscreen-dialog-box-container"
        content={
          <div className="document-preview-container">
            {selectedDocument && (
              <iframe
                src={selectedDocument.previewUrl || selectedDocument.url}
                title="Document Preview"
                className="document-iframe"
                onError={(e) => {
                  console.error('Iframe load error:', e);
                }}
              />
            )}
          </div>
        }
      />
    </div>
  );
};

export default MediaIconsCarousel;
