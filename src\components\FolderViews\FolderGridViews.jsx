import React, { useEffect, useState } from 'react';
import {
  Box,
  Checkbox,
  FormControlLabel,
  FormGroup,
  ListItemIcon,
  Menu,
  MenuItem,
  Typography,
} from '@mui/material';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import VisibilityOutlinedIcon from '@mui/icons-material/VisibilityOutlined';
import {
  DocIcon,
  FolderCheck,
  FolderIcon,
  FolderUncheck,
  PdfIcon,
  XlsIcon,
} from '@/helper/common/images';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import TextSnippetOutlinedIcon from '@mui/icons-material/TextSnippetOutlined';
import moment from 'moment';
import './folder.scss';

const FolderGridViews = ({
  folderData,
  menuList,
  selectedFolderData,
  setSelectedFolderData,
  handleOptionManage,
  handleView,
}) => {
  const [selected, setSelected] = useState(selectedFolderData || []);
  const [anchorEl, setAnchorEl] = useState(null);

  useEffect(() => {
    setSelected(selectedFolderData);
  }, [selectedFolderData]);

  const open = Boolean(anchorEl);
  const handleClick = (event) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleCheckBoxClick = () => {
    const folderId = folderData?.id;
    setSelectedFolderData((prevSelected) =>
      prevSelected.includes(folderId)
        ? prevSelected.filter((id) => id !== folderId)
        : [...prevSelected, folderId]
    );

    setSelected((prevSelected) =>
      prevSelected.includes(folderId)
        ? prevSelected.filter((id) => id !== folderId)
        : [...prevSelected, folderId]
    );
  };

  const isChecked = selected.includes(folderData?.id);
  const isInactive = folderData?.status === 'inactive';

  return (
    <Box className="folder-conatiner">
      <Box className="folder-details">
        <Box className="menu-icons d-flex justify-space-between">
          <Box>
            <FormGroup className="">
              <FormControlLabel
                control={
                  <Checkbox
                    className=""
                    icon={<FolderUncheck />}
                    checkedIcon={<FolderCheck />}
                  />
                }
                name="foldercheck"
                className=""
                checked={isChecked}
                onChange={(e) => handleCheckBoxClick(e)}
              />
            </FormGroup>
          </Box>
          <Box onClick={handleClick} className="cursor-pointer">
            <MoreVertIcon />
          </Box>
          <Menu
            id="long-menu"
            anchorEl={anchorEl}
            open={open}
            onClose={handleClose}
            sx={{
              '& .MuiPaper-root': {
                maxWidth: '350px',
                minWidth: '250px',
              },
            }}
          >
            {menuList?.map((option) => {
              const updatedOption =
                folderData?.status === 'inactive' &&
                option?.value === 'inactive'
                  ? {
                      id: 5,
                      name: 'Active',
                      value: 'active',
                      icon: <VisibilityOutlinedIcon />,
                    }
                  : option;
              if (
                folderData?.emp_contract_category?.type === 'general' &&
                (option?.id == 7 || option?.id == 8)
              ) {
                return <></>;
              } else if (
                folderData?.emp_contract_category?.type !== 'general' &&
                option?.id == 9
              ) {
                return <></>;
              } else if (isInactive && option?.id == 1) {
                return <></>;
              } else if (
                folderData?.department_user_id?.length > 0 &&
                option?.id == 6
              ) {
                return <></>;
              } else {
                return (
                  <MenuItem
                    key={updatedOption?.id}
                    onClick={() => {
                      handleOptionManage(updatedOption, folderData);
                      handleClose();
                    }}
                  >
                    <ListItemIcon>{updatedOption?.icon}</ListItemIcon>
                    {updatedOption?.name}
                  </MenuItem>
                );
              }
            })}
          </Menu>
        </Box>
        <Box
          className={`d-flex justify-start align-center flex-col ${
            isInactive ? 'inactive-folder' : ''
          }`}
          onClick={() => handleView(folderData)}
        >
          {/* <FolderIcon /> */}
          <Box>
            {folderData?.filetype === 'pdf' ? (
              <PdfIcon className="grid-view-folder" />
            ) : folderData?.filetype === 'doc' ? (
              <DocIcon className="grid-view-folder" />
            ) : folderData?.filetype === 'xls' ? (
              <XlsIcon className="grid-view-folder" />
            ) : (
              <FolderIcon className="grid-view-folder" />
            )}
          </Box>
          <Typography className="title-text text-ellipsis-line">
            {folderData?.name}
          </Typography>
        </Box>
      </Box>
      <Box className="d-flex justify-space-between align-center gap-sm user-details mt8">
        <Box className="d-flex justify-space-between align-center">
          <TextSnippetOutlinedIcon className="folder-size-time-icon" />
          <Typography className="sub-title-text user-date pl4">
            {folderData?.filesize}
          </Typography>
        </Box>
        <Box className="d-flex justify-space-between align-center">
          <AccessTimeIcon className="folder-size-time-icon" />
          <Typography className="sub-title-text user-date pl4">
            {moment(folderData?.updateDate).format('DD.MM.YYYY')}
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

export default FolderGridViews;
