.cost-analysis-card {
  background-color: var(--color-white);
  border-radius: var(--border-radius-md);
  border: var(--border-width-xs) solid var(--color-light-gray);
  box-shadow: var(--box-shadow-xs);

  &__header {
    padding: var(--spacing-lg) var(--spacing-xxl);
    border-bottom: var(--border-width-xs) solid var(--color-light-gray);
  }

  &__title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-lg);
    color: var(--text-color-black);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }

  &__content {
    padding: var(--spacing-lg);
  }

  &__breakdown {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
  }

  &__item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    // padding: var(--spacing-sm) 0;

    &--total {
      border-top: var(--border-width-xs) solid var(--color-light-gray);
      padding-top: var(--spacing-md);
      // margin-top: var(--spacing-sm);
    }
  }

  &__label {
    font-family: var(--font-family-primary);
    color: var(--text-color-slate-gray);
    font-size: var(--font-size-base);
    &--total {
      font-weight: var(--font-weight-semibold);
      color: var(--text-color-black);
      font-size: var(--font-size-base);
    }
  }

  &__value {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-black);
    font-size: var(--font-size-base);

    &--total {
      font-weight: var(--font-weight-bold);
      font-size: var(--font-size-base);
    }

    &--accent {
      color: var(--text-color-danger);
    }
  }

  // Note: Highlighting styles are now global classes in globals.scss
}
