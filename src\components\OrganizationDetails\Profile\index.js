'use client';
import React, { useContext, useEffect, useState, useRef } from 'react';
import * as Yup from 'yup';
import { Box, Typography } from '@mui/material';
import { Formik, Form } from 'formik';
//import { CustomTextField } from '@/components/UI/CommonField';
// import CustomButton from '@/components/UI/button';
import CustomButton from '@/components/UI/CustomButton';
import ChangePassword from './ChangePassword';
//import ImageUpload from '@/components/ImageUpload';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { ORG_URLS } from '@/helper/constants/urls';
import {
  setApiMessage,
  TextFieldMaxLength,
} from '@/helper/common/commonFunctions';
import AuthContext from '@/helper/authcontext';
import { fetchFromStorage, saveToStorage } from '@/helper/context';
import { identifiers } from '@/helper/constants/identifier';
import CustomTextField from '@/components/UI/CustomTextField';
import OrganizationProfileDetails from '../OrganizationForm/OrganizationProfileDetails';
import CustomCountryCode from '@/components/UI/CustomCountryCode';
// crop image function
import CropImage from '@/components/UI/CropImage';
import DialogBox from '@/components/UI/Modalbox';
import './profile.scss';

export default function OrganizationProfile() {
  // const [loader, setLoader] = useState(false);
  // const [previewImage, setPreviewImage] = useState();
  const [logo, setLogo] = useState();
  const [selectedCountryId, setSelectedCountryId] = useState(null);
  const { authState, setAuthState, planDetail, setRestrictedLimitModal } =
    useContext(AuthContext);
  const [open, setOpen] = useState(false);
  const [file, setFile] = useState();
  const fileInputRef = useRef(null);
  const authdata = fetchFromStorage(identifiers?.AUTH_DATA);

  const getUserDetailsORG = async (userId) => {
    // setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        ORG_URLS?.GET_USER_DATA_BY_USER_ID + `/${userId}`
      );
      if (status === 200) {
        saveToStorage(identifiers?.USER_DATA, data?.data);
        const userdata = data?.data?.user;
        setAuthState({
          ...authState,
          ...userdata,
          user_first_name: userdata?.firstName,
          user_last_name: userdata?.lastName,
        });
        // setLoader(false);
      }
    } catch (error) {
      // setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  //   /* crop image function
  const onOpenDialog = () => {
    setOpen(true);
  };
  const onhandleclose = () => {
    setOpen(false);
    setFile();
  };
  const getCropData = async (cropper) => {
    var baseText = cropper.getCroppedCanvas().toDataURL();
    const blob = await (await fetch(baseText)).blob();
    const files = new File([blob], file?.preview, {
      type: file?.type,
      lastModified: new Date(),
    });

    setLogo({ url: baseText, file: files });

    onhandleclose();
  };
  const handleLogoChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];

      // Check if storage is full
      const totalStorage = planDetail?.total_storage || 0;
      const usedStorage = authState?.subscriptionUsage?.total_size_gb || 0;
      const fileSizeInGB = selectedFile.size / (1024 * 1024 * 1024); // Convert bytes to GB

      if (usedStorage + fileSizeInGB > totalStorage) {
        setRestrictedLimitModal({
          storageLimit: true,
          totalStorage: planDetail?.total_storage,
          usedStorage: authState?.subscriptionUsage?.total_size_gb,
        });
        fileInputRef.current.value = null;
        return;
      }

      onOpenDialog();
      setFile(
        Object.assign(selectedFile, {
          preview: URL.createObjectURL(selectedFile),
        })
      );
      fileInputRef.current.value = null;
      // const file = event.target.files[0];
      // if (file) {
      //   const reader = new FileReader();
      //   reader.onloadend = () => {
      //     setLogo({ url: reader.result, file });
      //   };
      //   reader.readAsDataURL(file);
      // }
    }
  };

  useEffect(() => {
    if (
      authState?.attributes?.userSignature &&
      authState?.attributes?.userSignature !== '""'
    ) {
      setLogo({ url: authState?.attributes?.userSignature });
    }
  }, [authState?.attributes?.userSignature]);

  return (
    <Box className="profile-sec-wrap d-flex">
      <Box className="registration-page">
        <Formik
          enableReinitialize
          initialValues={{
            first_name: authState?.firstName || '',
            last_name: authState?.lastName || '',
            user_name: authState?.username || '',
            country_code:
              authState?.attributes?.userCountryCode || selectedCountryId,
            phone_number: authState?.attributes?.userPhoneNumber || '',
            email: authState?.email || '',
          }}
          validationSchema={Yup.object().shape({
            first_name: Yup.string().required('First name is required'),
            last_name: Yup.string().required('Last name is required'),
            user_name: Yup.string()
              .required('This field is required')
              .matches(
                /^[a-zA-Z0-9._ ]*$/,
                'Only letters, numbers, underscores, dots, and spaces are allowed'
              )
              .min(3, 'Username must be at least 3 characters'),
            country_code: Yup.string().required('Country code is required'),
            phone_number: Yup.string()
              .required('Phone number is required')
              .matches(/^\d+$/, 'Phone number must contain only digits'),
            email: Yup.string()
              .required('Email is required')
              .email('Invalid email address'),
          })}
          onSubmit={async (requestData) => {
            // setLoader(true);
            let body = new FormData();
            requestData?.first_name &&
              body.append('firstName', requestData?.first_name);
            requestData?.last_name &&
              body.append('lastName', requestData?.last_name);
            requestData?.user_name &&
              body.append('username', requestData?.user_name);
            requestData?.country_code &&
              body.append('userCountryCode', requestData?.country_code);
            requestData?.phone_number &&
              body.append('userPhoneNumber', requestData?.phone_number);
            requestData?.email && body.append('email', requestData?.email);
            body.append('userId', authdata?.user_id);
            body.append(
              'organizationId',
              authState?.attributes?.organizationId
            );
            authState?.attributes?.userStatus &&
              body.append('userStatus', authState?.attributes?.userStatus);
            authState?.attributes?.userToken &&
              body.append('userToken', authState?.attributes?.userToken);
            authState?.attributes?.isLoginpin &&
              body.append('isLoginpin', authState?.attributes?.isLoginpin);
            authState?.attributes?.updatedBy &&
              body.append('updatedBy', authState?.attributes?.updatedBy);
            logo?.file
              ? body.append('userSignature', logo?.file)
              : authState?.attributes?.userSignature
                ? body.append(
                    'userSignature',
                    authState?.attributes?.userSignature.substring(
                      authState?.attributes?.userSignature.lastIndexOf('/') + 1
                    )
                  )
                : '';
            const config = {
              headers: {
                'Content-Type': 'multipart/form-data',
              },
            };
            try {
              const { status, data } = await axiosInstance.put(
                ORG_URLS?.UPDATE_USER_DATA,
                body,
                config
              );
              if (status === 200) {
                if (data?.status) {
                  setApiMessage('success', data?.message);
                  getUserDetailsORG(authdata?.user_id);
                } else setApiMessage('error', data?.message);
              }
            } catch (error) {
              if (error?.response?.data?.message) {
                setApiMessage('error', error?.response?.data?.message);
              } else {
                setApiMessage('error', error?.response?.data?.data);
              }
            }
          }}
        >
          {({
            errors,
            touched,
            handleBlur,
            values,
            handleSubmit,
            handleChange,
            setFieldValue,
          }) => (
            <Form onSubmit={handleSubmit} className="form-wrap">
              <Box className="left-sec-wrap">
                <Box className="org-profile">
                  <OrganizationProfileDetails
                    isAdminProfile={true}
                    handleLogoChange={handleLogoChange}
                    previewImage={logo}
                    fileInputRef={fileInputRef}
                  />
                </Box>
                <Typography variant="h6" className="admin-form-title">
                  Profile Details
                </Typography>
                <Box className="form-grid">
                  <Box>
                    <CustomTextField
                      fullWidth
                      name="first_name"
                      label="First Name"
                      placeholder="Enter first name"
                      required
                      value={values?.first_name}
                      error={Boolean(touched?.first_name && errors?.first_name)}
                      helperText={touched?.first_name && errors?.first_name}
                      onBlur={handleBlur}
                      onChange={handleChange}
                      inputProps={{ maxLength: TextFieldMaxLength }}
                    />
                  </Box>
                  <Box>
                    <CustomTextField
                      fullWidth
                      required
                      name="last_name"
                      label="Last Name"
                      placeholder="Enter last name"
                      value={values?.last_name}
                      error={Boolean(touched?.last_name && errors?.last_name)}
                      helperText={touched?.last_name && errors?.last_name}
                      onBlur={handleBlur}
                      onChange={handleChange}
                      inputProps={{ maxLength: TextFieldMaxLength }}
                    />
                  </Box>
                  <Box>
                    <CustomTextField
                      fullWidth
                      name="user_name"
                      label="User Name"
                      required
                      placeholder="Enter user name"
                      value={values?.user_name}
                      error={Boolean(touched?.user_name && errors?.user_name)}
                      helperText={touched?.user_name && errors?.user_name}
                      onBlur={handleBlur}
                      onChange={handleChange}
                      inputProps={{ maxLength: TextFieldMaxLength }}
                    />
                  </Box>
                  <Box>
                    <CustomCountryCode
                      // value={values?.phone_number}
                      error={Boolean(
                        touched?.phone_number && errors?.phone_number
                      )}
                      helperText={touched?.phone_number && errors?.phone_number}
                      onBlur={handleBlur}
                      onChange={handleChange}
                      fullWidth
                      values={values}
                      setFieldValue={setFieldValue}
                      touched={touched}
                      errors={errors}
                      handleBlur={handleBlur}
                      handleChange={handleChange}
                      selectedCountryId={selectedCountryId}
                      setSelectedCountryId={setSelectedCountryId}
                    />
                  </Box>
                  <Box>
                    <CustomTextField
                      fullWidth
                      name="email"
                      label="Email"
                      placeholder="Enter email address"
                      value={values?.email}
                      required
                      error={Boolean(touched?.email && errors?.email)}
                      helperText={touched?.email && errors?.email}
                      onBlur={handleBlur}
                      onChange={handleChange}
                    />
                  </Box>
                </Box>
                <Box className="pt24 d-flex justify-end">
                  {/* <CustomButton
                    className="p16 register-btn"
                    fontWeight="600"
                    variant="contained"
                    background="#39596e"
                    backgroundhover="#39596e"
                    colorhover="#FFFFFF"
                    title="Update"
                    type="submit"
                  /> */}
                  <CustomButton
                    variant="contained"
                    type="submit"
                    className="update-btn"
                    // startIcon={<Save />}
                    title="Update"
                  />
                </Box>
              </Box>
            </Form>
          )}
        </Formik>
      </Box>
      {/* crop image function */}
      <DialogBox
        handleClose={() => onhandleclose()}
        open={open}
        title={'Change picture'}
        className="small-dialog-box-container"
        content={
          <>
            <CropImage
              file={file}
              getCropData={getCropData}
              onhandleclose={onhandleclose}
              isProfile={true}
            />
          </>
        }
      />
      <ChangePassword />
    </Box>
  );
}
