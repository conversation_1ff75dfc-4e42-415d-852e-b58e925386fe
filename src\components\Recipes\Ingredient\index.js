'use client';
import { useParams } from 'next/navigation';
import IngredientItems from './IngredientItems';
import AddEditIngredient from './AddEditIngredient';

export default function Ingredient() {
  const params = useParams();

  // Get ID from dynamic route parameter (e.g., /recipes/ingredients/chicken-breast)
  const ingredientId = params?.slug || params?.id;

  // If there's an ID in the params, show the edit form
  if (ingredientId) {
    return <AddEditIngredient isUpdate={true} />;
  }

  // Otherwise, show the list view
  return <IngredientItems />;
}
