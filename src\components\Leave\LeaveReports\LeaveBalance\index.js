'use client';
import { Box, CircularProgress, Tooltip, Typography } from '@mui/material';
import React, { useState, useEffect, useContext } from 'react';
import { DataGrid, gridClasses } from '@mui/x-data-grid';
import UserAvatar from '@/components/UI/Avatar/UserAvatar';
import { useRouter } from 'next/navigation';
import Searchbar from '@/components/UI/CustomSearch';
import CustomSelect from '@/components/UI/CustomSelect';
import CustomButton from '@/components/UI/CustomButton';
import { identifiers } from '@/helper/constants/identifier';
import CustomPagination from '@/components/UI/customPagination';
import AuthContext from '@/helper/authcontext';
import { saveToStorage } from '@/helper/context';
import LeavePopOver from '@/components/Leave/LeavePopOver';
import ClearOutlinedIcon from '@mui/icons-material/ClearOutlined';
import CheckIcon from '@mui/icons-material/Check';
import BranchDepartmentDisplay from '@/components/BranchDepartmentDisplay';
import NoDataView from '@/components/UI/NoDataView';
import { staticOptions } from '@/helper/common/staticOptions';

export default function LeaveBalance({
  getLeaveBalanceList,
  leaveFilterData,
  setLeaveFilterData,
  leavefilterDataApplied,
  setLeaveFilterDataApplied,
  branchList,
  departmentList,
  roleList,
  loader,
  leavePage,
  setLeavePage,
  setLeaveCurrentPage,
  leaveTotalCount,
  leaveBalance,
  leaveBalanceRowsPerPage,
  setLeaveBalanceRowsPerPage,
  leave_type,
}) {
  const { authState, setUserdata } = useContext(AuthContext);
  const [branchColumnWidth, setBranchColumnWidth] = useState(200);

  const router = useRouter();

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth <= 899) {
        setBranchColumnWidth(350);
      } else {
        setBranchColumnWidth(200);
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize();
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      setLeavePage(1);
      handleFilterData('apply');
    }
  };
  const onPageChange = (newPage) => {
    setLeavePage(newPage);
    setLeaveCurrentPage(newPage);
    getLeaveBalanceList(
      newPage,
      leavefilterDataApplied?.searchValue,
      leavefilterDataApplied
    );
  };
  const OnRowPerPage = (newPage) => {
    setLeavePage(1);
    setLeaveBalanceRowsPerPage(newPage);
    setLeaveCurrentPage(1);
    getLeaveBalanceList(
      1,
      leavefilterDataApplied?.searchValue,
      leavefilterDataApplied,
      newPage
    );
  };
  const handleFilterData = (type) => {
    if (type === 'apply') {
      getLeaveBalanceList(1, leaveFilterData?.searchValue, leaveFilterData);
      setLeaveFilterDataApplied(leaveFilterData);
    } else {
      const clearFilter = {
        status: '',
        branch: '',
        department: '',
        role: '',
        searchValue: '',
        year: '',
        leavetype: leave_type,
      };
      setLeaveFilterData(clearFilter);
      setLeaveFilterDataApplied(clearFilter);
      getLeaveBalanceList(1, leavefilterDataApplied?.searchValue, clearFilter);
    }
  };
  const columns = [
    {
      field: 'id',
      headerName: 'ID',
      width: 80,
      minWidth: 80,
      flex: 0,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-start h100 gap-sm flex-wrap">
            <Typography className="title-text">
              <span>{params?.row?.employment_number}</span>
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'user_full_name',
      headerName: 'User',
      width: 250,
      minWidth: 250,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',

      renderCell: (params) => {
        let userData = params?.row;
        return (
          <>
            <Box className="d-flex align-center justify-start h100">
              <UserAvatar
                name={userData?.user_full_name}
                src={userData?.user_avatar_link}
                classname="list-user-icon"
              />
              <Box
                className="pl8"
                onClick={() => {
                  setUserdata({
                    id: userData?.id,
                    leaveFilterData: leavefilterDataApplied,
                    searchValue: leavefilterDataApplied?.searchValue,
                    leavePage: leavePage,
                    leaveBal: true,
                  });
                  saveToStorage(identifiers?.RedirectData, {
                    id: userData?.id,
                    leaveFilterData: leavefilterDataApplied,
                    searchValue: leavefilterDataApplied?.searchValue,
                    leavePage: leavePage,
                    leaveBal: true,
                  });
                  router.push(`/user/${userData?.id}`);
                }}
              >
                <Box className="d-flex align-center">
                  <Typography className="title-text fw600 cursor-pointer link-text mw100 text-ellipsis pr4">
                    {userData?.user_full_name}
                  </Typography>
                </Box>
                <Typography className="sub-title-text cursor-pointer text-ellipsis">
                  {userData?.user_email}
                </Typography>
              </Box>
            </Box>
          </>
        );
      },
    },
    {
      field: 'branch?.branch_name',
      headerName: 'Branch / Dep.',
      width: branchColumnWidth,
      minWidth: branchColumnWidth,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return <BranchDepartmentDisplay row={params.row} />;
      },
    },
    {
      field: 'total_leave_days',
      headerName: 'Total Leaves',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        let leaveDetails = params?.row?.leave_details?.map((leave) => {
          return {
            leaveCount: leave?.leave_balance,
            leaveName: leave?.name,
            leaveUnlimited: leave?.has_leave_unlimited,
          };
        });
        return (
          <Box className="d-flex align-center justify-start h100 gap-sm flex-wrap title-text">
            <Box sx={{ boxShadow: 'none' }}>
              <Typography className="title-text">
                <Tooltip
                  className="event-title text-ellipsis"
                  variant="h6"
                  interactive
                  // open
                  title={
                    leaveDetails.length > 0 && (
                      <LeavePopOver
                        title={`Total Leave`} //${leave_type === 'day' ? 'Days' : 'Hours'}
                        Leavedays="Leave Type"
                        leave="No of leaves"
                        leaveDetails={leaveDetails}
                      />
                    )
                  }
                  classes={{
                    tooltip: 'leave-days-popover',
                  }}
                >
                  <span>{params?.row?.leave_balance}</span>
                </Tooltip>
              </Typography>
            </Box>
          </Box>
        );
      },
    },
    {
      field: 'total_used_leave_days',
      headerName: 'Used Leave',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        let leaveDetails = params?.row?.leave_details?.map((leave) => {
          return {
            leaveCount:
              leave?.used_leave?.total +
              (leavefilterDataApplied?.leavetype === 'hour' ? 'h' : 'd'),
            // leaveCountHours: leave?.used_hour_leave?.total,
            leaveName: leave?.name,
          };
        });
        return (
          <Box className="d-flex align-center justify-start h100 gap-sm flex-wrap title-text">
            <Typography className="title-text">
              <Tooltip
                className="event-title text-ellipsis"
                variant="h6"
                interactive
                // open
                title={
                  leaveDetails.length > 0 && (
                    <LeavePopOver
                      title={`Total Leave`} // ${leave_type === 'day' ? 'Days' : 'Hours'}
                      Leavedays="Leave Type"
                      leave="No of leaves"
                      leaveDetails={leaveDetails}
                    />
                  )
                }
                classes={{
                  tooltip: 'leave-days-popover',
                }}
              >
                <span>{params?.row?.total_used_leave_days}</span>
              </Tooltip>
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'available_balance',
      headerName: 'Balance',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        let leaveDetails = params?.row?.leave_details?.map((leave) => {
          return {
            leaveCount: leave?.user_remaining_leave,
            leaveName: leave?.name,
            leaveUnlimited: leave?.has_leave_unlimited,
          };
        });
        return (
          <Box className="d-flex align-center justify-start h100 gap-sm flex-wrap title-text">
            <Typography className="title-text">
              <Tooltip
                className="event-title text-ellipsis"
                variant="h6"
                interactive
                // open
                title={
                  leaveDetails.length > 0 && (
                    <LeavePopOver
                      title={`Total Leave`} // ${leave_type === 'day' ? 'Days' : 'Hours'}
                      Leavedays="Leave Type"
                      leave="No of leaves"
                      leaveDetails={leaveDetails}
                    />
                  )
                }
                classes={{
                  tooltip: 'leave-days-popover',
                }}
              >
                <span>{params?.row?.available_balance}</span>
              </Tooltip>
            </Typography>
          </Box>
        );
      },
    },
  ];
  const columnsDiffType = [
    {
      field: 'id',
      headerName: 'ID',
      width: 80,
      minWidth: 80,
      flex: 0,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-start h100 gap-sm flex-wrap">
            <Typography className="title-text">
              <span>{params?.row?.employment_number}</span>
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'user_full_name',
      headerName: 'User',
      width: 250,
      minWidth: 250,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',

      renderCell: (params) => {
        let userData = params?.row;
        return (
          <>
            <Box className="d-flex align-center justify-start h100">
              <UserAvatar
                name={userData?.user_full_name}
                src={userData?.user_avatar_link}
                classname="list-user-icon"
              />
              <Box
                className="pl8"
                onClick={() => {
                  setUserdata({
                    id: userData?.id,
                    leaveFilterData: leavefilterDataApplied,
                    searchValue: leavefilterDataApplied?.searchValue,
                    leavePage: leavePage,
                    leaveBal: true,
                  });
                  saveToStorage(identifiers?.RedirectData, {
                    id: userData?.id,
                    leaveFilterData: leavefilterDataApplied,
                    searchValue: leavefilterDataApplied?.searchValue,
                    leavePage: leavePage,
                    leaveBal: true,
                  });
                  router.push(`/user/${userData?.id}`);
                }}
              >
                <Box className="d-flex align-center">
                  <Typography className="title-text fw600 cursor-pointer link-text mw100 text-ellipsis pr4">
                    {userData?.user_full_name}
                  </Typography>
                </Box>
                <Typography className="sub-title-text cursor-pointer text-ellipsis">
                  {userData?.user_email}
                </Typography>
              </Box>
            </Box>
          </>
        );
      },
    },
    {
      field: 'branch?.branch_name',
      headerName: 'Branch / Dep.',
      width: branchColumnWidth,
      minWidth: branchColumnWidth,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return <BranchDepartmentDisplay row={params.row} />;
      },
    },
    {
      field: 'total_used_leave_days',
      headerName: 'Used Leave',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        let leaveDetails = params?.row?.leave_details?.map((leave) => {
          return {
            leaveCount:
              leave?.used_leave?.total +
              (leavefilterDataApplied?.leavetype === 'hour' ? ' H' : ' D'),
            // leaveCountHours: leave?.used_hour_leave?.total,
            leaveName: leave?.name,
          };
        });
        return (
          <Box className="d-flex align-center justify-start h100 gap-sm flex-wrap title-text">
            <Typography className="title-text">
              <Tooltip
                className="event-title text-ellipsis"
                variant="h6"
                interactive
                // open
                title={
                  leaveDetails.length > 0 && (
                    <LeavePopOver
                      title={`Total Leave`} // ${leave_type === 'day' ? 'Days' : 'Hours'}
                      Leavedays="Leave Type"
                      leave="No of leaves"
                      leaveDetails={leaveDetails}
                    />
                  )
                }
                classes={{
                  tooltip: 'leave-days-popover',
                }}
              >
                <span>{params?.row?.total_used_leave_days}</span>
              </Tooltip>
            </Typography>
          </Box>
        );
      },
    },
  ];
  return (
    <Box className="leave-balance-sec">
      <Box className="search-section-wrap">
        <Box className="search-section-fields">
          <Searchbar
            setSearchValue={(e) => {
              setLeaveFilterData({
                ...leaveFilterData,
                searchValue: e,
              });
            }}
            searchValue={leaveFilterData?.searchValue}
            onKeyPress={handleKeyPress}
          />
        </Box>

        {/* Select for Branch */}
        {authState?.web_user_active_role_id !== 7 &&
          authState?.web_user_active_role_id !== 14 && (
            <Box className="search-section-fields">
              <CustomSelect
                placeholder="Branches"
                showDot
                options={branchList}
                value={
                  branchList?.find((opt) => {
                    return opt?.value === leaveFilterData?.branch;
                  }) || ''
                }
                onChange={(e) => {
                  setLeaveFilterData({
                    ...leaveFilterData,
                    branch: e?.value ? e?.value : '',
                  });
                }}
              />
            </Box>
          )}
        <Box className="search-section-fields">
          <CustomSelect
            placeholder="Department"
            options={departmentList}
            value={
              departmentList?.find((opt) => {
                return opt?.value === leaveFilterData?.department;
              }) || ''
            }
            onChange={(e) => {
              setLeaveFilterData({
                ...leaveFilterData,
                department: e?.value ? e?.value : '',
              });
            }}
          />
        </Box>
        <Box className="search-section-fields">
          <CustomSelect
            placeholder="System access"
            options={roleList}
            value={
              roleList?.find((opt) => {
                return opt?.value === leaveFilterData?.role;
              }) || ''
            }
            onChange={(e) => {
              setLeaveFilterData({
                ...leaveFilterData,
                role: e?.value ? e?.value : '',
              });
            }}
          />
        </Box>
        <Box className="search-section-fields">
          <CustomSelect
            placeholder="Leave type"
            options={staticOptions?.LEAVE_TYPE}
            value={
              staticOptions?.LEAVE_TYPE?.find((opt) => {
                return opt?.value === leaveFilterData?.leavetype;
              }) || ''
            }
            onChange={(e) => {
              setLeaveFilterData({
                ...leaveFilterData,
                leavetype: e?.value ? e?.value : '',
              });
            }}
          />
        </Box>
        {/* <Box className="select-box">
          <CustomSelect
            placeholder="Year"
            options={yearOption}
            value={leaveFilterData?.year}
            onChange={(e) => {
              setLeaveFilterData({
                ...leaveFilterData,
                year: e?.target?.value,
              });
            }}
          />
        </Box> */}
        <Box className="create-cancel-button d-flex align-center gap-10">
          <CustomButton
            variant="contained"
            isIconOnly
            startIcon={
              <Tooltip
                title={<Typography>Apply Filter</Typography>}
                arrow
                classes={{
                  tooltip: 'info-tooltip-container',
                }}
              >
                <CheckIcon />
              </Tooltip>
            }
            fullWidth={false}
            onClick={() => {
              setLeavePage(1);
              handleFilterData('apply');
            }}
          />
          <CustomButton
            variant="outlined"
            isIconOnly
            startIcon={
              <Tooltip
                title={<Typography>Clear Filter</Typography>}
                arrow
                classes={{
                  tooltip: 'info-tooltip-container',
                }}
              >
                <ClearOutlinedIcon />
              </Tooltip>
            }
            onClick={() => {
              handleFilterData('cancel');
            }}
          />
        </Box>
      </Box>
      <Box className="table-container table-layout">
        {loader ? (
          <Box className="content-loader">
            <CircularProgress className="loader" color="inherit" />
          </Box>
        ) : (
          <>
            {leaveBalance && leaveBalance?.length === 0 ? (
              <Box className="no-data d-flex align-center justify-center">
                <NoDataView
                  // image
                  title="No Leave Balance Found"
                  description="There is no leave balance available at the moment."
                />
              </Box>
            ) : (
              <>
                <Box className="data-grid-wrap">
                  <DataGrid
                    rows={leaveBalance}
                    columns={
                      leave_type !== leavefilterDataApplied?.leavetype
                        ? columnsDiffType
                        : columns
                    }
                    pageSize={leaveBalanceRowsPerPage}
                    checkboxSelection={false}
                    disableSelectionOnClick
                    rowHeight={70}
                    autoHeight
                    // getRowHeight={() => 'auto'}
                    sx={{
                      transition: 'none', // Disables transition effects
                      [`& .${gridClasses.cell}`]: {
                        py: 1,
                      },
                    }}
                  />
                </Box>
                <CustomPagination
                  currentPage={leavePage}
                  totalCount={leaveTotalCount}
                  rowsPerPage={leaveBalanceRowsPerPage}
                  onPageChange={onPageChange}
                  OnRowPerPage={OnRowPerPage}
                />
              </>
            )}
          </>
        )}
      </Box>
    </Box>
  );
}
