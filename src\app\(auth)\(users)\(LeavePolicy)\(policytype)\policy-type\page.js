'use client';
import React, { useEffect, useState } from 'react';
import {
  Box,
  CircularProgress,
  Typography,
  Tooltip,
  Divider,
} from '@mui/material';
import CustomButton from '@/components/UI/button';
import DialogBox from '@/components/UI/Modalbox';
import Searchbar from '@/components/UI/SearchBar';
import CustomSelect from '@/components/UI/selectbox';
import { DataGrid } from '@mui/x-data-grid';
import CustomPagination from '@/components/UI/pagination';
import TuneIcon from '@mui/icons-material/Tune';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import moment from 'moment';
import { identifiers } from '@/helper/constants/identifier';
import EditIcon from '@/components/ActionIcons/EditIcon';
import { useRouter } from 'next/navigation';
import DeleteIcon from '@/components/ActionIcons/DeleteIcon';
import AddIcon from '@mui/icons-material/Add';
import FilterListIcon from '@mui/icons-material/FilterList';
import ClearOutlinedIcon from '@mui/icons-material/ClearOutlined';
import './leavepolicytype.scss';

export default function PolicyType() {
  const router = useRouter();
  const [filter, setFilter] = useState(false);
  const [loader, setLoader] = useState(true);
  const [leaveTypeList, setLeaveTypeList] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [searchValue, setSearchValue] = useState('');
  const [anuualExist, setanuualExist] = useState(false);
  const [filterData, setFilterData] = useState({ status: '' });
  const [filterDataApplied, setFilterDataApplied] = useState({ status: '' });
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const handleRedirect = (id) => {
    router.push(`/edit-policy-type/${id}?anuualExist=${anuualExist}`);
  };
  const columns = [
    {
      field: 'id',
      headerName: 'ID',
      width: 48,
      minWidth: 48,
      flex: 0,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
    },
    {
      field: 'name',
      headerName: 'Name',
      width: 260,
      minWidth: 260,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box
            className="d-flex align-center justify-start h100 cursor-pointer anual"
            onClick={() => handleRedirect(params?.row?.id)}
          >
            <Typography className="d-flex anual-leave-text-sec">
              <span className="anual-leave-text p14 text-ellipsis">
                {params?.value}
              </span>
            </Typography>
            {params?.row?.has_annual_leave && (
              <span className="anual-status income">
                <span className="p12 category-draft  fw600 text-capital">
                  {' '}
                  Annual
                </span>
              </span>
            )}
          </Box>
        );
      },
    },
    {
      field: 'remark',
      headerName: 'Remark',
      width: 300,
      minWidth: 300,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box
            className="d-flex align-center justify-start h100 cursor-pointer"
            onClick={() => handleRedirect(params?.row?.id)}
          >
            {params?.value !== '-' ? (
              <Tooltip
                title={params?.value}
                classes={{
                  tooltip: 'table-list-tooltip',
                }}
                placement="bottom-start"
              >
                <Typography className="p14 text-ellipsis">
                  <span>{params?.value}</span>
                </Typography>
              </Tooltip>
            ) : (
              '-'
            )}
          </Box>
        );
      },
    },
    {
      field: 'created_by',
      headerName: 'Created By',
      width: 150,
      minWidth: 150,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box
            className="d-flex align-center justify-start h100 cursor-pointer"
            onClick={() => handleRedirect(params?.row?.id)}
          >
            <Typography className="p14 text-ellipsis">
              {params?.row?.created_by?.user_full_name}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'createdAt',
      headerName: 'Created Date',
      width: 150,
      minWidth: 150,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box
            className="d-flex align-center justify-start h100 cursor-pointer"
            onClick={() => handleRedirect(params?.row?.id)}
          >
            <Typography className="p14 text-ellipsis">
              {params?.value ? moment(params?.value)?.format('DD-MM-YYYY') : ''}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 150,
      minWidth: 150,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box
            className="d-flex align-center justify-center h100 text-capital cursor-pointer"
            onClick={() => handleRedirect(params?.row?.id)}
          >
            {params?.value === 'inactive' ? (
              <Typography className="p12 failed fw600">
                {params?.value}
              </Typography>
            ) : (
              <Typography className="p12 success fw600">
                {params?.value}
              </Typography>
            )}
          </Box>
        );
      },
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex justify-center actions">
            <Tooltip title="Edit" arrow className="action-tooltip">
              <Box>
                <EditIcon
                  onClick={() => {
                    router.push(
                      `/edit-policy-type/${params?.row?.id}?anuualExist=${anuualExist}`
                    );
                  }}
                />
              </Box>
            </Tooltip>
            {!params?.row?.has_annual_leave ? (
              <Tooltip title="Delete" arrow className="action-tooltip">
                <Box>
                  <DeleteIcon
                    onClick={() => {
                      handleDelete(params?.row);
                    }}
                  />
                </Box>
              </Tooltip>
            ) : (
              <Box style={{ width: '28px' }}></Box>
            )}
          </Box>
        );
      },
    },
  ];

  const getLeaveTypeList = async (page, searchValue, filter, Rpp) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_LEAVE_TYPE +
          `?search=${searchValue}&size=${
            Rpp ? Rpp : rowsPerPage
          }&page=${page}&status=${filter?.status}`
      );
      if (status === 200) {
        setLoader(false);
        setanuualExist(data?.anuualExist);
        const leaveList = data?.data;
        setTotalCount(data?.total);
        setLeaveTypeList(leaveList ? leaveList : []);
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const onPageChange = (newPage) => {
    setCurrentPage(newPage);
    getLeaveTypeList(newPage, searchValue, filterDataApplied);
  };
  const OnRowPerPage = (newPage) => {
    setRowsPerPage(newPage);
    setCurrentPage(1);
    getLeaveTypeList(1, searchValue, filterDataApplied, newPage);
  };
  const handleFilterData = (type) => {
    setFilter(false);
    if (type === 'apply') {
      getLeaveTypeList(1, searchValue, filterData);
      setFilterDataApplied(filterData);
    } else {
      const clearFilter = {
        status: '',
      };
      setFilterData(clearFilter);
      setFilterDataApplied(clearFilter);
      getLeaveTypeList(1, searchValue, clearFilter);
    }
  };
  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      handleFilterData('apply');
    }
  };
  const handleDelete = async (item) => {
    try {
      setLoader(true);
      const { status, data } = await axiosInstance.delete(
        URLS?.LEAVE_TYPE + `${item?.id}`
      );

      if (status === 200) {
        if (data.status) {
          setApiMessage('success', data?.message);
          getLeaveTypeList(currentPage, searchValue, filterDataApplied);
        } else {
          setApiMessage('error', data?.message);
          getLeaveTypeList(currentPage, searchValue, filterDataApplied);
        }
      } else {
        setApiMessage('error', data?.message);
      }
      setLoader(false);
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  useEffect(() => {
    getLeaveTypeList(1, searchValue, filterDataApplied);
  }, []);
  return (
    <>
      <Box className="page-container leave-policy-type ">
        <Box className="table-container table-border-wrap leave-policy-table">
          <Box className="search-filter-section d-flex align-center justify-end">
            <Box className="d-flex align-center justify-end filters-wrap">
              <Box className="select-box">
                <CustomSelect
                  placeholder="Status"
                  options={identifiers?.CARD_STATUS}
                  value={filterData?.status}
                  onChange={(e) => {
                    setFilterData({
                      ...filterData,
                      status: e?.target?.value,
                    });
                  }}
                />
              </Box>
              <Searchbar
                searchclass="staff-leave-search"
                setSearchValue={setSearchValue}
                onKeyPress={handleKeyPress}
              />
            </Box>
            <Box className="buttons-wrap d-flex">
              <Box className="add-icon-wrap d-flex align-center justify-center">
                <Tooltip title="Apply Filter" arrow>
                  <FilterListIcon
                    className="add-icon cursor-pointer"
                    onClick={() => handleFilterData('apply')}
                  />
                </Tooltip>
              </Box>
              <Box className="clear-filter-icon-wrap d-flex align-center justify-center">
                <Tooltip title="Clear Filter" arrow>
                  <ClearOutlinedIcon
                    className="clear-filter-icon cursor-pointer"
                    onClick={() => handleFilterData('clear')}
                  />
                </Tooltip>
              </Box>
              <Box className="add-icon-wrap d-flex align-center justify-center">
                <Tooltip arrow title="Add Leave Type">
                  <AddIcon
                    className="add-icon cursor-pointer"
                    onClick={() => {
                      router.push(
                        `/create-policy-type?anuualExist=${anuualExist}`
                      );
                    }}
                  />
                </Tooltip>
              </Box>
            </Box>
          </Box>
          <Divider />
          {loader ? (
            <Box className="content-loader">
              <CircularProgress className="loader" color="inherit" />
            </Box>
          ) : (
            <>
              {leaveTypeList && leaveTypeList?.length === 0 ? (
                <Box className="">
                  <Typography className="text-align h6">
                    No data found
                  </Typography>
                </Box>
              ) : (
                <>
                  <DataGrid
                    rows={leaveTypeList && leaveTypeList}
                    columns={columns}
                    pageSize={rowsPerPage}
                    checkboxSelection={false}
                    disableSelectionOnClick
                    hideMenuIcon
                  />
                  <CustomPagination
                    currentPage={currentPage}
                    totalCount={totalCount}
                    rowsPerPage={rowsPerPage}
                    onPageChange={onPageChange}
                    OnRowPerPage={OnRowPerPage}
                    className="leave-policy-pagination"
                  />
                </>
              )}
            </>
          )}
        </Box>
        <DialogBox
          open={filter}
          handleClose={() => {
            setFilter(!filter);
          }}
          title="Leave type filter"
          content={
            <>
              <Box className="staff-filter">
                <Box className="pt32">
                  <Box className="select-box pb32">
                    <CustomSelect
                      placeholder="Status"
                      options={identifiers?.CARD_STATUS}
                      value={filterData?.status}
                      onChange={(e) => {
                        setFilterData({
                          ...filterData,
                          status: e.target.value,
                        });
                      }}
                      label={<span>Status</span>}
                    />
                  </Box>
                  <Box className="create-cancel-button">
                    <CustomButton
                      fullWidth
                      className="p12 secondary-button"
                      type="submit"
                      fontWeight="600"
                      variant="contained"
                      background="#FFFFFF"
                      backgroundhover="#39596e"
                      colorhover="#FFFFFF"
                      title="Cancel"
                      onClick={() => {
                        handleFilterData('cancel');
                      }}
                    />
                    <CustomButton
                      fullWidth
                      className="p12"
                      type="submit"
                      fontWeight="600"
                      variant="contained"
                      background="#39596e"
                      backgroundhover="#FFFFFF"
                      colorhover="#000000"
                      title="Apply"
                      onClick={() => {
                        handleFilterData('apply');
                      }}
                    />
                  </Box>
                </Box>
              </Box>
            </>
          }
        />
      </Box>
    </>
  );
}
