.export-status-indicator {
  position: fixed;
  bottom: var(--spacing-xl);
  right: var(--spacing-xl);
  z-index: 9999;
  max-width: 20rem;
  width: 100%;
  transition: all 0.3s;
  background-color: var(--color-white);
  border-radius: var(--border-radius-lg);

  &.visible {
    transform: translateY(0);
    opacity: 1;
  }

  &.hidden {
    transform: translateY(100%);
    opacity: 0;
  }

  .status-container {
    padding: var(--spacing-md);
    border-radius: var(--border-radius-lg);
    border-width: var(--border-width-sm);
    border-style: var(--border-style-solid);
    box-shadow: var(--box-shadow-xs);

    &.bg-processing {
      background-color: var(--color-primary-opacity);
      border-color: var(--border-color-primary);
    }

    &.bg-success {
      background-color: var(--color-success-opacity);
      border-color: var(--border-color-green);
    }

    &.bg-error {
      background-color: var(--color-danger-opacity);
      border-color: var(--border-color-red);
    }

    .status-content {
      display: flex;
      align-items: flex-start;
      gap: var(--spacing-xs);

      .status-icon {
        flex-shrink: 0;
        margin-top: 2px;

        &.icon-processing {
          animation: spin 1s linear infinite;
          color: var(--text-bright-blue);
        }

        &.icon-success {
          color: var(--text-green);
        }

        &.icon-error {
          color: var(--text-error);
        }
      }

      .status-details {
        flex: 1;
        min-width: 0;

        .status-title {
          margin-bottom: var(--spacing-xxs);
          color: var(--text-color-primary);
        }

        .status-description {
          margin-bottom: var(--spacing-xs);
          color: var(--text-color-slate-gray);
        }

        .progress-container {
          margin-bottom: var(--spacing-xs);

          .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-xxs);

            .progress-label {
              color: var(--text-color-slate-gray);
            }

            .progress-value {
              font-weight: var(--font-weight-medium);
              color: var(--text-color-primary);
            }
          }

          .progress-bar-container {
            width: 100%;
            background-color: var(--color-secondary);
            border-radius: var(--border-radius-full);
            height: 8px;

            .progress-bar {
              background-color: var(--color-primary);
              height: 8px;
              border-radius: var(--border-radius-full);
              transition: width 0.3s;
            }
          }

          .estimated-time {
            margin-top: var(--spacing-xxs);
            color: var(--text-color-slate-gray);
          }
        }

        .action-buttons {
          display: flex;
          align-items: center;
          gap: var(--spacing-sm);
          margin-top: var(--spacing-sm);

          .download-button {
            display: flex;
            align-items: center;
            padding: var(--spacing-xs) var(--spacing-sm);
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            // border-radius: var(--border-radius-xs);
            // transition: background-color 0.2s;

            .button-icon {
              margin-right: var(--spacing-xxs);
            }
          }

          .retry-button {
            display: flex;
            align-items: center;
            padding: 0 var(--spacing-xs) 0 var(--spacing-xs);
            height: 24px;
            background-color: var(--color-danger);
            color: var(--color-white);
            font-size: var(--font-size-xxs);
            font-weight: var(--font-weight-medium);
            border-radius: var(--border-radius-xs);
            transition: background-color 0.2s;

            &:hover {
              background-color: var(--text-error);
            }

            &:focus {
              outline: none;
              box-shadow: 0 0 0 2px var(--color-danger-opacity);
            }

            .button-icon {
              margin-right: var(--spacing-xxs);
            }
          }

          .dismiss-button {
            padding: var(--spacing-xs);
            min-width: 30px;
          }
        }
      }
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Add content-text-bold class that doesn't exist in _globals.scss
.content-text-bold {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
  line-height: var(--line-height-base);
}
