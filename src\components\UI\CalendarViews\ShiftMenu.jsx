import React from 'react';
import {
  Checkbox,
  Divider,
  FormControlLabel,
  FormGroup,
  Tooltip,
} from '@mui/material';
import MoreHorizIcon from '@mui/icons-material/MoreHoriz';
import AddIcon from '@mui/icons-material/Add';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import AirplanemodeActiveIcon from '@mui/icons-material/AirplanemodeActive';
import ArrowCircleDownIcon from '@mui/icons-material/ArrowCircleDown';
import ArrowCircleUpIcon from '@mui/icons-material/ArrowCircleUp';
import HistoryToggleOffIcon from '@mui/icons-material/HistoryToggleOff';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import ModeStandbyIcon from '@mui/icons-material/ModeStandby';
import DoDisturbOnOutlinedIcon from '@mui/icons-material/DoDisturbOnOutlined';
import SupervisorAccountOutlinedIcon from '@mui/icons-material/SupervisorAccountOutlined';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import BuildOutlinedIcon from '@mui/icons-material/BuildOutlined';
import TuneOutlinedIcon from '@mui/icons-material/TuneOutlined';
import TipsAndUpdatesOutlinedIcon from '@mui/icons-material/TipsAndUpdatesOutlined';
import SwapHorizIcon from '@mui/icons-material/SwapHoriz';
import BlockIcon from '@mui/icons-material/Block';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import EventAvailableIcon from '@mui/icons-material/EventAvailable';
import EditIcon from '@mui/icons-material/Edit';
import EventBusyIcon from '@mui/icons-material/EventBusy';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { ROTA_URLS } from '@/helper/constants/urls';
import { setApiMessage } from '@/helper/common/commonFunctions';
import moment from 'moment';
import { useRouter } from 'next/navigation';
// import CustomButton from '../CustomButton';

// ========== Menu Components ==========
export const CellMenu = ({
  shiftsForDate = [],
  isOpen = false,
  placement = 'bottom-start',
  rowKey = '',
  onOpen,
  onClose,
  handleAddShiftTooltipOpen,
  dayOffItem,
  handleAddRemoveDayOff,
  viewAccessOnly,
}) => {
  const navigate = useRouter();

  return (
    <Tooltip
      onClose={onClose}
      open={isOpen}
      title={
        viewAccessOnly ? (
          <div className="calendar-menu-list-cotentiner">
            {rowKey !== 'open' && (
              <div
                className="calendar-menu-list"
                onClick={() => {
                  onClose();
                  navigate.push('/apply-leave?is_rota=true');
                }}
              >
                <div className="calendar-menu-list-icon">
                  <AirplanemodeActiveIcon style={{ rotate: '90deg' }} />
                </div>
                <p className="calendar-menu-list-name">Add leave</p>
              </div>
            )}
            {rowKey !== 'open' &&
              (dayOffItem?.dayOff ? (
                <div
                  className="calendar-menu-list"
                  onClick={() => handleAddRemoveDayOff()}
                >
                  <div className="calendar-menu-list-icon">
                    <DoDisturbOnOutlinedIcon />
                  </div>
                  <p className="calendar-menu-list-name">Remove day off</p>
                </div>
              ) : (
                <div
                  className="calendar-menu-list"
                  onClick={() => handleAddRemoveDayOff()}
                >
                  <div className="calendar-menu-list-icon">
                    <DoDisturbOnOutlinedIcon />
                  </div>
                  <p className="calendar-menu-list-name">Add day off</p>
                </div>
              ))}
            <div
              className="calendar-menu-list"
              onClick={handleAddShiftTooltipOpen}
            >
              <div className="calendar-menu-list-icon">
                <AddIcon />
              </div>
              <p className="calendar-menu-list-name">
                {shiftsForDate?.length > 0 ? 'Add another shift' : 'Add shift'}
              </p>
            </div>
          </div>
        ) : (
          <div className="calendar-menu-list-cotentiner">
            <div
              className="calendar-menu-list"
              onClick={() => {
                onClose();
                navigate.push('/apply-leave?is_rota=true');
              }}
            >
              <div className="calendar-menu-list-icon">
                <AirplanemodeActiveIcon style={{ rotate: '90deg' }} />
              </div>
              <p className="calendar-menu-list-name">Request leave</p>
            </div>
          </div>
        )
      }
      placement={placement}
      classes={{
        tooltip: 'calendar-menu-list-tooltip-custom',
      }}
    >
      <div className="shift-menu-icon" onClick={onOpen}>
        <MoreHorizIcon fontSize="small" />
      </div>
    </Tooltip>
  );
};

export const ShiftMenu = ({
  shift,
  rowKey,
  isOpen = false,
  placement = 'bottom-start',
  onOpen,
  onClose,
  handleAddShiftTooltipOpen,
  // getRotaShiftList,
  setMenuTooltipAction,
  handleShiftHistoryModalOpen,
  handleDeleteShiftModalOpen,
  handleAddRemoveDayOff,
  dayOffItem,
  viewAccessOnly,
  isSwap,
  setIsUpdatedShift,
}) => {
  const navigate = useRouter();
  // handle publish and unpublished shift
  const handlePublishAndUnpublished = async (shiftId, isShiftStatus) => {
    const sendData = {
      ids: [shiftId],
      isPublished: !isShiftStatus,
    };

    try {
      const { status, data } = await axiosInstance.put(
        ROTA_URLS?.SHIFT_PUBLISHE_UNPUBLISH,
        sendData
      );
      if (status === 200 || status === 201) {
        setIsUpdatedShift(!isShiftStatus);
        shift.isPublished = isShiftStatus === 0 ? 1 : 0;
        setApiMessage('success', data?.message);
        onClose();
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  //  handle set as open shift
  const handleSetAsOpenShift = async (shiftId) => {
    try {
      const { status, data } = await axiosInstance.put(
        ROTA_URLS?.SET_AS_OPEN_SHIFT + `/${shiftId}`
      );
      if (status === 200 || status === 201) {
        setApiMessage('success', data?.message);
        shift.isOpen = 1;
        shift.userId = null;
        onClose();
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const isFutureDate = moment(shift?.startTime).isAfter(moment(), 'day');

  return (
    <Tooltip
      onClose={onClose}
      open={isOpen}
      title={
        viewAccessOnly ? (
          <div className="calendar-menu-list-cotentiner">
            {!shift?.isPublished ? (
              <div
                className="calendar-menu-list"
                onClick={() => {
                  handlePublishAndUnpublished(shift?.id, shift?.isPublished);
                }}
              >
                <div className="calendar-menu-list-icon">
                  <ArrowCircleUpIcon />
                </div>
                <p className="calendar-menu-list-name">Publish shift</p>
              </div>
            ) : (
              <div
                className="calendar-menu-list"
                onClick={() => {
                  handlePublishAndUnpublished(shift?.id, shift?.isPublished);
                }}
              >
                <div className="calendar-menu-list-icon">
                  <ArrowCircleDownIcon />
                </div>
                <p className="calendar-menu-list-name">Unpublish shift</p>
              </div>
            )}
            {rowKey !== 'open' && (
              <div
                className="calendar-menu-list"
                onClick={() => handleSetAsOpenShift(shift?.id)}
              >
                <div className="calendar-menu-list-icon">
                  <ModeStandbyIcon />
                </div>
                <p className="calendar-menu-list-name">Set as open shift</p>
              </div>
            )}
            <div
              className="calendar-menu-list"
              onClick={() => {
                setMenuTooltipAction('assignTempCover');
                handleAddShiftTooltipOpen();
              }}
            >
              <div className="calendar-menu-list-icon">
                <SupervisorAccountOutlinedIcon />
              </div>
              <p className="calendar-menu-list-name">Assign temporary cover</p>
            </div>
            <div
              className="calendar-menu-list"
              onClick={handleShiftHistoryModalOpen}
            >
              <div className="calendar-menu-list-icon">
                <HistoryToggleOffIcon />
              </div>
              <p className="calendar-menu-list-name">Shift history</p>
            </div>
            <Divider />
            {rowKey !== 'open' && (
              <div
                className="calendar-menu-list"
                onClick={() => {
                  onClose();
                  navigate.push('/apply-leave?is_rota=true');
                }}
              >
                <div className="calendar-menu-list-icon">
                  <AirplanemodeActiveIcon style={{ rotate: '90deg' }} />
                </div>
                <p className="calendar-menu-list-name">Add leave</p>
              </div>
            )}
            {rowKey !== 'open' &&
              (dayOffItem?.dayOff ? (
                <div
                  className="calendar-menu-list"
                  onClick={() => handleAddRemoveDayOff()}
                >
                  <div className="calendar-menu-list-icon">
                    <DoDisturbOnOutlinedIcon />
                  </div>
                  <p className="calendar-menu-list-name">Remove day off</p>
                </div>
              ) : (
                <div
                  className="calendar-menu-list"
                  onClick={() => handleAddRemoveDayOff()}
                >
                  <div className="calendar-menu-list-icon">
                    <DoDisturbOnOutlinedIcon />
                  </div>
                  <p className="calendar-menu-list-name">Add day off</p>
                </div>
              ))}
            <div
              className="calendar-menu-list"
              onClick={handleAddShiftTooltipOpen}
            >
              <div className="calendar-menu-list-icon">
                <AddIcon />
              </div>
              <p className="calendar-menu-list-name">Add shift</p>
            </div>
            <Divider />
            <div
              className="calendar-menu-list delete-shift-list"
              onClick={handleDeleteShiftModalOpen}
            >
              <div className="calendar-menu-list-icon">
                <DeleteOutlineIcon />
              </div>
              <p className="calendar-menu-list-name">Delete shift</p>
            </div>
          </div>
        ) : (
          <div className="calendar-menu-list-cotentiner">
            <div
              className="calendar-menu-list"
              onClick={() => {
                onClose();
                navigate.push('/apply-leave?is_rota=true');
              }}
            >
              <div className="calendar-menu-list-icon">
                <AirplanemodeActiveIcon style={{ rotate: '90deg' }} />
              </div>
              <p className="calendar-menu-list-name">Request leave</p>
            </div>
            {isFutureDate && !isSwap && (
              <>
                <div
                  className="calendar-menu-list"
                  onClick={() => {
                    setMenuTooltipAction('shiftSwap');
                    handleAddShiftTooltipOpen();
                  }}
                >
                  <div className="calendar-menu-list-icon">
                    <SwapHorizIcon />
                  </div>
                  <p className="calendar-menu-list-name">Request shift swap</p>
                </div>
                <div
                  className="calendar-menu-list"
                  onClick={() => {
                    setMenuTooltipAction('shiftDrop');
                    handleAddShiftTooltipOpen();
                  }}
                >
                  <div className="calendar-menu-list-icon">
                    <BlockIcon />
                  </div>
                  <p className="calendar-menu-list-name">Request shift drop</p>
                </div>
              </>
            )}
          </div>
        )
      }
      placement={placement}
      classes={{
        tooltip: 'calendar-menu-list-tooltip-custom',
      }}
    >
      <div className="user-shift-menu-icon" onClick={() => onOpen(shift?.id)}>
        <MoreVertIcon fontSize="small" />
      </div>
    </Tooltip>
  );
};

export const CopyMenu = ({
  isOpen = false,
  onOpen,
  onClose,
  placement = 'bottom-start',
  // isViewType,
  setIsCopyModal,
}) => {
  return (
    <Tooltip
      onClose={onClose}
      open={isOpen}
      title={
        <div className="calendar-menu-list-cotentiner">
          <p className="calendar-menu-list-title">Copy</p>
          {/* {isViewType === 'week' && (
            <div className="calendar-menu-list">
              <p className="calendar-menu-list-name">Add shifts</p>
              <p className="calendar-menu-list-sub-name">
                (from previous week)
              </p>
            </div>
          )} */}
          <div
            className="calendar-menu-list"
            onClick={() => {
              onClose();
              setIsCopyModal({ isCopyRangeModal: true });
            }}
          >
            <p className="calendar-menu-list-name">Copy custom range</p>
          </div>
          <Tooltip
            title={<p className="p12">Comming soon</p>}
            arrow
            placement="top"
            classes={{
              tooltip:
                'calendar-event-list-tooltip-custom calendar-event-other-details-tooltip',
            }}
          >
            <div className="calendar-menu-list comming-soon">
              <p className="calendar-menu-list-name">Templates</p>
            </div>
          </Tooltip>
          <Divider />
          <div className="calendar-menu-tip-container">
            <div className="calendar-menu-tip-icon">
              <TipsAndUpdatesOutlinedIcon />
            </div>
            <p className="calendar-menu-tip-title">
              Copying individual shifts?
              <br />
              <span className="calendar-menu-tip-text">
                Hold the SHIFT key while dragging a shift to duplicate it.
              </span>
            </p>
          </div>
        </div>
      }
      placement={placement}
      classes={{
        tooltip: 'calendar-menu-list-tooltip-custom',
      }}
    >
      <div
        className="filter-options-menu"
        onClick={() => {
          onOpen();
        }}
      >
        <ContentCopyIcon /> <ExpandMoreIcon fontSize="small" />
      </div>
      {/* <CustomButton
        variant="outlined"
        isIconOnly
        startIcon={
          <>
            <ContentCopyIcon /> <ExpandMoreIcon fontSize="small" />
          </>
        }
        onClick={() => {
          onOpen();
        }}
      /> */}
    </Tooltip>
  );
};

export const ToolsMenu = ({
  isOpen = false,
  onOpen,
  onClose,
  placement = 'bottom-start',
  isViewType,
  setIsToolsModal,
  filterData,
  setIsLoader,
  viewAccessOnly,
}) => {
  const navigate = useRouter();

  var weekStart = moment(filterData?.currentDate || new Date())
    .clone()
    .startOf('isoWeek')
    .format('YYYY-MM-DD');
  var weekEnd = moment(filterData?.currentDate || new Date())
    .clone()
    .endOf('isoWeek')
    .format('YYYY-MM-DD');

  var firstDay = moment(filterData?.currentDate)
    .startOf('month')
    .format('YYYY-MM-DD');
  var lastDay = moment(filterData?.currentDate)
    .endOf('month')
    .format('YYYY-MM-DD');

  const handleDownlaodExcel = async () => {
    let filterList = '';

    if (filterData?.dayMonth === 'week') {
      filterList = `?startDate=${weekStart}&endDate=${weekEnd}&branchId=${filterData?.location}`;
    } else if (filterData?.dayMonth === 'month') {
      filterList = `?startDate=${firstDay}&endDate=${lastDay}&branchId=${filterData?.location}`;
    }
    setIsLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        ROTA_URLS?.EXPORT_SHIFT_EXCEL + filterList,
        {
          responseType: 'blob',
        }
      );
      if (status === 200 || status === 201) {
        const url = window.URL.createObjectURL(new Blob([data]));
        const link = document.createElement('a');
        link.href = url;
        const fileName =
          filterData?.dayMonth === 'week'
            ? `shifts_${weekStart}_to_${weekEnd}.xlsx`
            : `shifts_${firstDay}_to_${lastDay}.xlsx`;
        link.setAttribute('download', fileName);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        setIsLoader(false);
        onClose();
      }
    } catch (error) {
      setIsLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  return (
    <Tooltip
      onClose={onClose}
      open={isOpen}
      title={
        <div className="calendar-menu-list-cotentiner">
          <p className="calendar-menu-list-title">Tools</p>
          {viewAccessOnly && (
            <div
              className="calendar-menu-list"
              onClick={() => {
                setIsToolsModal({ isEmpGroupModal: true });
                onClose();
              }}
            >
              <p className="calendar-menu-list-name">Employee grouping</p>
            </div>
          )}
          {viewAccessOnly && (
            <>
              <div
                className="calendar-menu-list"
                onClick={() => {
                  onClose();
                  navigate.push('/rota-employees-order');
                }}
              >
                <p className="calendar-menu-list-name">Reorder employees</p>
              </div>
              <Divider />
            </>
          )}
          <div className="calendar-menu-list" onClick={handleDownlaodExcel}>
            <p className="calendar-menu-list-name">Excel download</p>
          </div>
          <div
            className="calendar-menu-list"
            onClick={() => {
              onClose();
              setIsToolsModal({ isKeyboardModal: true });
            }}
          >
            <p className="calendar-menu-list-name">Keyboard shortcuts</p>
          </div>
          <Divider />
          {viewAccessOnly && (
            <div
              className="calendar-menu-list"
              onClick={() => {
                onClose();
                setIsToolsModal({ isClearShiftModal: true });
              }}
            >
              <p className="calendar-menu-list-name">
                Clear {isViewType === 'week' ? 'week' : 'month'}
              </p>
            </div>
          )}
          {viewAccessOnly && (
            <div
              className="calendar-menu-list"
              onClick={() => {
                onClose();
                navigate.push('/rota-deleted-shifts');
              }}
            >
              <p className="calendar-menu-list-name">See deleted shifts</p>
            </div>
          )}
        </div>
      }
      placement={placement}
      classes={{
        tooltip: 'calendar-menu-list-tooltip-custom',
      }}
    >
      <div
        className="filter-options-menu"
        onClick={() => {
          onOpen();
        }}
      >
        <BuildOutlinedIcon style={{ rotate: '90deg' }} />{' '}
        <ExpandMoreIcon fontSize="small" />
      </div>
      {/* <CustomButton
        variant="outlined"
        isIconOnly
        startIcon={
          <>
          <BuildOutlinedIcon style={{ rotate: '90deg' }} />{' '}
        <ExpandMoreIcon fontSize="small" />
          </>
        }
        onClick={() => {
          onOpen();
        }}
      /> */}
    </Tooltip>
  );
};

export const ShortFilterMenu = ({
  isOpen = false,
  onOpen,
  onClose,
  placement = 'bottom-start',
  setShortFilterChanged,
  shortFilterChanged,
  viewAccessOnly,
}) => {
  return (
    <Tooltip
      onClose={onClose}
      open={isOpen}
      title={
        <div className="calendar-menu-list-cotentiner">
          <p className="calendar-menu-list-title">Show</p>
          <div className="calendar-menu-list">
            <FormGroup className="toltip-list-check-box">
              <FormControlLabel
                control={
                  <Checkbox
                    className="check-box"
                    icon={<CheckBoxOutlineBlankIcon className="uncheck-icon" />}
                    checkedIcon={<CheckBoxIcon className="check-icon" />}
                    disableRipple
                  />
                }
                name="availability"
                className="toltip-list-check-box-label"
                checked={shortFilterChanged?.availability}
                onChange={(e) => {
                  setShortFilterChanged({
                    ...shortFilterChanged,
                    availability: e.target.checked,
                  });
                  onClose();
                }}
                label="Availability"
              />
            </FormGroup>
          </div>
          <Divider />
          {viewAccessOnly && (
            <Tooltip
              title={<p className="p12">Comming soon</p>}
              arrow
              placement="top"
              classes={{
                tooltip:
                  'calendar-event-list-tooltip-custom calendar-event-other-details-tooltip',
              }}
            >
              <div className="calendar-menu-list">
                <FormGroup className="toltip-list-check-box">
                  <FormControlLabel
                    control={
                      <Checkbox
                        className="check-box"
                        icon={
                          <CheckBoxOutlineBlankIcon className="uncheck-icon" />
                        }
                        checkedIcon={<CheckBoxIcon className="check-icon" />}
                        disableRipple
                        disabled
                      />
                    }
                    name="labour_targets"
                    className="toltip-list-check-box-label"
                    // checked={IsRemarkView}
                    // onChange={(e) => {
                    //   setIsRemarkView(e.target.checked);
                    // }}
                    label="Labour targets "
                  />
                </FormGroup>
              </div>
            </Tooltip>
          )}
          {viewAccessOnly && (
            <Tooltip
              title={<p className="p12">Comming soon</p>}
              arrow
              placement="top"
              classes={{
                tooltip:
                  'calendar-event-list-tooltip-custom calendar-event-other-details-tooltip',
              }}
            >
              <div className="calendar-menu-list">
                <FormGroup className="toltip-list-check-box">
                  <FormControlLabel
                    control={
                      <Checkbox
                        className="check-box"
                        icon={
                          <CheckBoxOutlineBlankIcon className="uncheck-icon" />
                        }
                        checkedIcon={<CheckBoxIcon className="check-icon" />}
                        disableRipple
                        disabled
                      />
                    }
                    name="budgets"
                    className="toltip-list-check-box-label"
                    // checked={IsRemarkView}
                    // onChange={(e) => {
                    //   setIsRemarkView(e.target.checked);
                    // }}
                    label="Budgets"
                  />
                </FormGroup>
              </div>
            </Tooltip>
          )}
        </div>
      }
      placement={placement}
      classes={{
        tooltip: 'calendar-menu-list-tooltip-custom',
      }}
    >
      <div
        className="filter-options-menu"
        onClick={() => {
          onOpen();
        }}
      >
        <TuneOutlinedIcon /> <ExpandMoreIcon fontSize="small" />
      </div>

      {/* <CustomButton
          variant="outlined"
          isIconOnly
          startIcon={
            <>
              <TuneOutlinedIcon /> <ExpandMoreIcon fontSize="small" />
            </>
          }
          onClick={() => {
            onOpen();
          }}
        /> */}
    </Tooltip>
  );
};

export const PublishingMenu = ({
  isOpen = false,
  onOpen,
  onClose,
  placement = 'bottom-start',
  isViewType,
  unpublishedShift,
  shiftList,
  handlePublishAndUnpublishedShift,
}) => {
  return (
    <Tooltip
      onClose={onClose}
      open={isOpen}
      title={
        <div className="calendar-menu-list-cotentiner">
          <p className="calendar-menu-list-title">Publishing</p>
          <div
            className={`calendar-menu-list ${unpublishedShift?.length === 0 && 'is-menu-disabled'}`}
            onClick={() => {
              unpublishedShift?.length === 0
                ? null
                : handlePublishAndUnpublishedShift(true);
            }}
          >
            <p className="calendar-menu-list-name">
              Publish {isViewType === 'month' ? 'month' : 'week'}
            </p>
          </div>
          <div
            className={`calendar-menu-list ${shiftList?.length === 0 && 'is-menu-disabled'}`}
            onClick={() => {
              shiftList?.length === 0
                ? null
                : handlePublishAndUnpublishedShift(false);
            }}
          >
            <p className="calendar-menu-list-name">
              Un-publish {isViewType === 'month' ? 'month' : 'week'}
            </p>
          </div>
          <Divider />
          <div className="calendar-menu-tip-container">
            <div className="calendar-menu-tip-icon">
              <TipsAndUpdatesOutlinedIcon />
            </div>
            <p className="calendar-menu-tip-title">
              Publishing individual shifts?
              <br />
              <span className="calendar-menu-tip-text">
                Hold <b>P</b> , then click on a shift to publish/unpublish.
              </span>
            </p>
          </div>
        </div>
      }
      placement={placement}
      classes={{
        tooltip: 'calendar-menu-list-tooltip-custom',
      }}
    >
      <div
        className="shift-publish-other-options-button"
        onClick={() => {
          onOpen();
        }}
      >
        <ExpandMoreIcon fontSize="small" />
      </div>
    </Tooltip>
  );
};

export const AvailabilityShiftMenu = ({
  isOpen = false,
  placement = 'bottom-start',
  onOpen,
  onClose,
  presentEvent,
  handleAddAvailabilityModalOpen,
  handleDeleteAvailabilityModalOpen,
  handleAvailabilityOnOff,
  eventInfo,
}) => {
  const isPresentEvent = presentEvent !== null;
  return (
    <Tooltip
      onClose={onClose}
      open={isOpen}
      title={
        <div className="calendar-menu-list-cotentiner">
          {isPresentEvent && (
            <div
              className="calendar-menu-list"
              onClick={handleAddAvailabilityModalOpen}
            >
              <div className="calendar-menu-list-icon">
                <EditIcon />
              </div>
              <p className="calendar-menu-list-name">Edit</p>
            </div>
          )}
          <div
            className="calendar-menu-list"
            onClick={() =>
              handleAvailabilityOnOff(
                'available',
                presentEvent,
                eventInfo?.date
              )
            }
          >
            <div className="calendar-menu-list-icon">
              <EventAvailableIcon />
            </div>
            <p className="calendar-menu-list-name">Available all-day</p>
          </div>
          <div
            className="calendar-menu-list"
            onClick={() =>
              handleAvailabilityOnOff(
                'unavailable',
                presentEvent,
                eventInfo?.date
              )
            }
          >
            <div className="calendar-menu-list-icon">
              <EventBusyIcon />
            </div>
            <p className="calendar-menu-list-name">Unavailable all-day</p>
          </div>
          {isPresentEvent && (
            <div
              className="calendar-menu-list"
              onClick={() => handleDeleteAvailabilityModalOpen(presentEvent)}
            >
              <div className="calendar-menu-list-icon">
                <DeleteOutlineIcon />
              </div>
              <p className="calendar-menu-list-name">Clear day</p>
            </div>
          )}
        </div>
      }
      placement={placement}
      classes={{
        tooltip: 'calendar-menu-list-tooltip-custom',
      }}
    >
      <div className="availability-menu-icon" onClick={() => onOpen()}>
        <MoreVertIcon fontSize="small" />
      </div>
    </Tooltip>
  );
};
