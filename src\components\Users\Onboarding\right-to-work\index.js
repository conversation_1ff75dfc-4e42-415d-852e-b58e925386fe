'use client';

import React, { useEffect, useState, useContext } from 'react';
import { Box, Typography, Tooltip, Checkbox } from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import AuthContext from '@/helper/authcontext';
import CustomButton from '@/components/UI/CustomButton';
import LaunchIcon from '@mui/icons-material/Launch';
import CancelIcon from '@mui/icons-material/Cancel';
import axiosInstance from '@/helper/axios/axiosInstance';
import FormQuestion from '@/components/UI/FormQuestion';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { staticOptions } from '@/helper/common/staticOptions';
import { URLS } from '@/helper/constants/urls';
import FileUpload from './uploadFile';
import HeaderImage from '@/components/UI/ImageSecurity';
import CustomCheckbox from '@/components/UI/CustomCheckbox';
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import DialogBox from '@/components/UI/Modalbox';
import DeleteModal from '@/components/UI/DeleteModal';
import '../onboarding.scss';
import './right-to-work.scss';

export default function RightToWorkCList({
  isUserScreen,
  UserDetails,
  UserId,
  isMyProfile,
  formRightToWork,
  getOnboardingChecklist,
  ViewAccessOnly,
  getCheckListId,
}) {
  const { authState } = useContext(AuthContext);
  const [UKRTW, setUKRTW] = useState();
  const [UKCitizen, setUKCitizen] = useState();

  const [RightToWorkList, setRightToWorkList] = useState();
  const [loader, setLoader] = useState(false);
  const [isConfirm, setIsConfirm] = useState(false);
  const validateRequiredFields = () => {
    return RightToWorkList?.every((item) => {
      if (item?.isRequire) {
        return item?.data;
      }
      return true;
    });
  };
  const validateRequiredFieldsRemoved = (rtw) => {
    return rtw?.every((item) => {
      if (item?.isRequire) {
        return item?.data;
      }
      return true;
    });
  };
  const IsEnabledConfirm =
    RightToWorkList && RightToWorkList?.length > 0 && validateRequiredFields();
  const EnabledSave = isMyProfile
    ? IsEnabledConfirm && isConfirm
    : IsEnabledConfirm;
  const [deleteConfirmation, setDeleteConfirmation] = useState(false);
  const [fieldName, setFieldName] = useState([]);
  // const userId = UserId ? UserId : authState?.id;
  // const DeleteUploadedFile = async (file, key) => {
  //   try {
  //     const { status, data } = await axiosInstance.delete(
  //       URLS?.DELETE_UPLOADED_FILE +
  //         `?checklist_id=1&from_user_id=${UserId}&file_name=${file}`
  //     );
  //     if (status === 200) {
  //       if (data.status) {
  //         setApiMessage('success', data?.message);
  //         const fRTW = RightToWorkList.filter((f) => f?.key !== key);
  //         setRightToWorkList(fRTW);
  //       } else {
  //         setApiMessage('error', data?.message);
  //       }
  //     } else {
  //       setApiMessage('error', data?.message);
  //     }
  //   } catch (error) {
  //     setApiMessage('error', error?.response?.data?.message);
  //   }
  // };
  const handleChangeData = (dataValue, i) => {
    setIsConfirm(false);
    const rtw =
      RightToWorkList?.length > 0 &&
      RightToWorkList?.map((item, index) => {
        if (index === i) {
          return {
            ...item,
            data: dataValue,
            isRemoved: false,
            isApiData: false,
          };
        } else {
          return { ...item };
        }
      });
    setRightToWorkList(rtw);
  };
  const removeValue = async (data) => {
    // if (data?.isApiData) {
    //   DeleteUploadedFile(data?.data?.name, data?.key);
    // } else {
    // const fRTW = RightToWorkList.filter((f) => f?.key !== data?.key);
    const fRTW =
      RightToWorkList?.length > 0 &&
      RightToWorkList?.map((item) => {
        if (item?.key === data?.key) {
          return { ...item, data: '', isRemoved: true };
        } else {
          return { ...item };
        }
      });
    setRightToWorkList(fRTW);
    setIsConfirm(
      fRTW && fRTW?.length > 0 && validateRequiredFieldsRemoved(fRTW)
    );
    // }
  };
  const onchangeUK = (value) => {
    setUKRTW(value);
    setUKCitizen();
    setIsConfirm(false);
    // value === 'no' && OnboardingFormsRighttoWork();
  };
  const onchangeUKCitizen = (value) => {
    setUKCitizen(value);
    setIsConfirm(false);
    isMyProfile && setIsConfirm(false);
    const rtw =
      value === 'no' ? staticOptions?.RTW_LIST : staticOptions?.RTW_LIST_UK;
    setRightToWorkList(rtw);
    const fRTW =
      rtw?.length > 0 &&
      rtw?.map((item) => {
        delete item.isApiData;
        return { ...item, data: '' };
      });
    setRightToWorkList(fRTW);
  };

  const download = async (urlLink, name) => {
    const response = await fetch(urlLink);
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', name);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };

  //  TODO: WE NEED TO HIDE THEM UNTIL WE COMPLETE THE DOCUMENT HISTORY.

  const onboardingDeleteFile = async () => {
    const params = {
      checklist_id: getCheckListId?.id,
      from_user_id: UserId,
      field_name: fieldName,
    };

    try {
      const { status, data } = await axiosInstance.post(
        URLS?.ONBOARDING_DELETE_FILE,
        params
      );

      if (status === 200) {
        setApiMessage('success', data?.message);
        setRightToWorkList((prevList) => {
          const oldData = [...prevList];
          return oldData?.map((e) => {
            if (fieldName?.includes(e?.key)) {
              return { ...e, data: '' };
            }
            return e;
          });
        });
        getOnboardingChecklist(UserId);
        setFieldName([]);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    } finally {
      setLoader(false); // Ensure loader is set to false even in case of error
    }
  };

  const handleOnBoardingDelete = () => {
    onboardingDeleteFile();
    setDeleteConfirmation(false);
  };

  const handleDeleteCloseModal = () => {
    setDeleteConfirmation(false);
  };

  const OnboardingForms = async () => {
    setLoader(true);
    const body = new FormData();

    RightToWorkList.forEach((item) => {
      if (item?.data?.[0] && !item?.isApiData) {
        body.append(item?.key, item?.data?.[0]);
      } else if (item?.isRemoved && item?.isApiData && !item?.isRequire) {
        body.append(item?.key, '');
      }
    });
    body.append('has_right_to_work_in_uk', UKRTW === 'yes' ? true : false);
    body.append('is_uk_citizen', UKCitizen === 'yes' ? true : false);
    UserDetails && authState && authState?.id === UserDetails?.id
      ? body.append('is_confirm_upload', isConfirm)
      : validateRequiredFields()
        ? body.append('is_confirm_upload', true)
        : body.append('is_confirm_upload', false);

    const method = isMyProfile ? 'post' : 'put';
    const ApiUrl = isMyProfile
      ? URLS.CREATE_ONBOARDING_FORM + `?checklist_id=${1}`
      : URLS.UPDATE_ONBOARDING_FORM +
        `?form_user_id=${UserId}&checklist_id=${1}`;

    const config = {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    };
    try {
      const { status, data } = await axiosInstance[method](
        ApiUrl,
        body,
        config
      );
      if (status === 200) {
        if (data?.status) {
          setApiMessage('success', data?.message);
          getOnboardingChecklist(UserId);
        } else {
          setApiMessage('error', data?.message);
        }
        setLoader(false);
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const OnboardingFormsRighttoWork = async () => {
    setLoader(true);
    const body = new FormData();
    body.append('has_right_to_work_in_uk', false);
    const method = isMyProfile ? 'post' : 'put';
    const ApiUrl = isMyProfile
      ? URLS.CREATE_ONBOARDING_FORM + `?checklist_id=${1}`
      : URLS.UPDATE_ONBOARDING_FORM +
        `?form_user_id=${UserId}&checklist_id=${1}`;

    const config = {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    };
    try {
      const { status } = await axiosInstance[method](ApiUrl, body, config);
      if (status === 200) {
        setLoader(false);
        getOnboardingChecklist(UserId);
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  //  TODO: WE NEED TO HIDE THEM UNTIL WE COMPLETE THE DOCUMENT HISTORY.

  const handleCheckboxChange = (value) => {
    const currentHSIndex = fieldName.indexOf(value);
    const updatedFieldNames = [...fieldName];

    if (currentHSIndex === -1) {
      updatedFieldNames.push(value);
    } else {
      updatedFieldNames.splice(currentHSIndex, 1);
    }

    setFieldName(updatedFieldNames);
  };

  // Helper function to extract filename from URL
  const getFileNameFromUrl = (url) => {
    if (!url) return '';
    return url.split('/').pop() || 'Invalid URL';
  };

  // Helper function to get display name
  const getDisplayName = (item) => {
    if (item?.data?.url && item?.isApiData) {
      return getFileNameFromUrl(item?.data?.url);
    }
    // if (item?.name) return item.name;
    if (item?.data?.name) return item?.data?.name;
    if (item?.data?.[0]?.name) return item?.data?.[0]?.name;
    return getFileNameFromUrl(item?.data?.url);
  };
  useEffect(() => {
    if (formRightToWork) {
      if (
        formRightToWork?.has_right_to_work_in_uk !== null &&
        formRightToWork?.has_right_to_work_in_uk !== undefined
      ) {
        formRightToWork?.has_right_to_work_in_uk
          ? setUKRTW('yes')
          : setUKRTW('no');
      }
      if (
        formRightToWork?.is_uk_citizen !== null &&
        formRightToWork?.is_uk_citizen !== undefined
      ) {
        formRightToWork?.is_uk_citizen
          ? setUKCitizen('yes')
          : setUKCitizen('no');
      }
      const rtw = formRightToWork?.is_uk_citizen
        ? staticOptions?.RTW_LIST_UK
        : staticOptions?.RTW_LIST;
      setRightToWorkList(rtw);
      const updatedList = rtw?.map((item) => {
        const key = item?.key;
        const dataKey = key;
        const linkKey = `${key}_link`;
        if (formRightToWork[dataKey] && formRightToWork[linkKey]) {
          return {
            ...item,
            isApiData: true,
            data: {
              name: formRightToWork[dataKey],
              url: formRightToWork[linkKey],
            },
          };
        } else {
          return item;
        }
      });
      setRightToWorkList(updatedList);
      formRightToWork?.is_confirm_upload
        ? setIsConfirm(true)
        : setIsConfirm(false);
    } else {
      setUKRTW();
      setUKCitizen();
      setRightToWorkList();
    }
  }, [formRightToWork]);

  return (
    <>
      <Box className={!isUserScreen && 'page-container'}>
        {/* {formRightToWork?.pending_change_request_count && (
          <Box className="d-flex align-center">
            <Tooltip
              title={<Typography>Pending Change Request</Typography>}
              classes={{
                tooltip: 'info-tooltip-container ',
              }}
            >
              <ChangeCircleIcon className="created-date cursor-pointer" />
            </Tooltip>
            <Typography className="sub-title-text fw400 user-date">
              {UserDetails?.pending_change_request_count}
            </Typography>
          </Box>
        )} */}
        <FormQuestion
          question="Do you have the right to work in the UK?"
          ViewAccessOnly={ViewAccessOnly}
          setValue={onchangeUK}
          Value={UKRTW}
        />
        {UKRTW === 'yes' ? (
          <>
            <FormQuestion
              question="Are you a UK citizen?"
              ViewAccessOnly={ViewAccessOnly}
              setValue={onchangeUKCitizen}
              Value={UKCitizen}
              getCheckListId={getCheckListId}
            />
            {UKCitizen === 'yes' || UKCitizen === 'no' ? (
              <>
                {' '}
                <Box
                  className={
                    isUserScreen
                      ? 'right-to-work-page'
                      : 'page-section right-to-work-page'
                  }
                >
                  <Box className="d-flex align-center justify-space-between pb16 gap-10 flex-wrap">
                    <Typography className="title-text rtwc-info">
                      Please upload your All Document as per given Instruction &
                      Ahead step 2 for on boarding process from the company.
                      <Tooltip
                        title={'File size should be less than 5MB.'}
                        arrow
                        classes={{
                          tooltip: 'info-tooltip-container',
                        }}
                      >
                        <InfoIcon className="info-icon" />
                      </Tooltip>
                    </Typography>

                    {/* TODO: WE NEED TO HIDE THEM UNTIL WE COMPLETE THE DOCUMENT HISTORY. */}
                    {(authState?.web_user_active_role_id === 1 ||
                      authState?.web_user_active_role_id === 2) &&
                    !isMyProfile ? (
                      <Box className="delete-btn-wrap">
                        <CustomButton
                          className="red-button"
                          variant="contained"
                          startIcon={<DeleteOutlineOutlinedIcon />}
                          title="Delete"
                          onClick={() => setDeleteConfirmation(true)}
                          disabled={fieldName?.length === 0}
                        />
                      </Box>
                    ) : (
                      <></>
                    )}
                  </Box>
                  <Box className="right-to-work-checklist rtwc-upload-grid ">
                    {RightToWorkList &&
                      RightToWorkList?.length > 0 &&
                      RightToWorkList?.map((item, index) => {
                        return (
                          <>
                            {item?.data ? (
                              <Box className="upload-download-sec rtwc-upload h100">
                                <Box className="title-section">
                                  <Box className="title">
                                    <Typography className="body-text fw600">
                                      {item?.name}
                                      {item?.isRequire && (
                                        <span className="color-red">*</span>
                                      )}
                                    </Typography>
                                    {item?.Subname && (
                                      <Typography className="sub-title-text">
                                        {item?.Subname}
                                      </Typography>
                                    )}
                                  </Box>
                                  <CustomButton
                                    variant="contained"
                                    className="green-button"
                                    title={'Download'}
                                    onClick={() => {
                                      download(
                                        item?.data?.url,
                                        getDisplayName(item)
                                      );
                                    }}
                                    fullWidth={false}
                                    disabled={!item?.isApiData}
                                  />
                                </Box>

                                <Box className="action-sec d-flex align-center justify-space-between">
                                  {/* <Typography
                                    className={
                                      item?.isApiData
                                        ? 'title-text fw400  cursor-pointer' //text-underline
                                        : 'title-text fw400'
                                    }
                                    onClick={() =>
                                      item?.isApiData &&
                                      window.open(item?.data?.url, '_blank')
                                    }
                                  >
                                    {item?.data?.name
                                      ? item?.data?.name
                                      : item?.data?.[0]?.name}
                                  </Typography> */}

                                  <Box className="d-flex align-center">
                                    <HeaderImage
                                      type="url"
                                      imageUrl={
                                        item?.isApiData && item?.data?.url
                                      }
                                      Content={
                                        <Typography
                                          className={
                                            item?.isApiData
                                              ? 'title-text fw400  cursor-pointer color-green' //text-underline
                                              : 'title-text fw400 color-green '
                                          }
                                        >
                                          {getDisplayName(item)}
                                        </Typography>
                                      }
                                    />
                                    {item?.isApiData && (
                                      <>
                                        <HeaderImage
                                          type="url"
                                          imageUrl={item?.data?.url}
                                          Content={
                                            <LaunchIcon className="view-icon" />
                                          }
                                          className="d-flex align-center"
                                        />
                                      </>
                                    )}
                                    {/* {!item?.isApiData && ( */}
                                    {/* <CancelIcon
                                      className="delete-icon ml4"
                                      onClick={() => removeValue(item)}
                                    /> */}
                                    {/* )} */}
                                    {(authState?.web_user_active_role_id ===
                                      1 ||
                                      authState?.web_user_active_role_id ===
                                        2) &&
                                    !isMyProfile ? (
                                      !item?.isApiData ? (
                                        <CancelIcon
                                          className="delete-icon ml4"
                                          onClick={() => removeValue(item)}
                                        />
                                      ) : (
                                        <></>
                                      )
                                    ) : (
                                      <CancelIcon
                                        className="delete-icon ml4"
                                        onClick={() => removeValue(item)}
                                      />
                                    )}
                                  </Box>
                                  {/* TODO: WE NEED TO HIDE THEM UNTIL WE COMPLETE THE DOCUMENT HISTORY. */}
                                  {(authState?.web_user_active_role_id === 1 ||
                                    authState?.web_user_active_role_id === 2) &&
                                    !isMyProfile && (
                                      <Box className="delete-check-box">
                                        <Checkbox
                                          className="inactive-check-box"
                                          icon={<CheckBoxOutlineBlankIcon />}
                                          checkedIcon={<CheckBoxIcon />}
                                          checked={fieldName.includes(
                                            item?.key
                                          )}
                                          onChange={() =>
                                            handleCheckboxChange(item?.key)
                                          }
                                          disableRipple
                                        />
                                      </Box>
                                    )}
                                </Box>
                              </Box>
                            ) : (
                              <FileUpload
                                name={item?.name}
                                isRequire={item?.isRequire}
                                Subname={item?.Subname}
                                handleChangeData={handleChangeData}
                                index={index}
                                ViewAccessOnly={ViewAccessOnly}
                                isAccepts={
                                  item?.key === 'brp_front'
                                    ? {
                                        'image/jpeg': ['.jpg', '.jpeg'],
                                        'image/png': ['.png'],
                                        'application/pdf': ['.pdf'],
                                      }
                                    : false
                                }
                              />
                            )}
                          </>
                        );
                      })}
                  </Box>
                  {authState?.id === UserDetails?.id && (
                    <Box className="pt24">
                      <CustomCheckbox
                        name="job"
                        checked={isConfirm}
                        onChange={(e) => {
                          setIsConfirm(e.target.checked);
                        }}
                        disabled={ViewAccessOnly || !IsEnabledConfirm}
                        label="I confirm that I have carried out the right to work check above in compliance with the instructions."
                      />
                    </Box>
                  )}

                  <Box className={`pt32 ${!isUserScreen ? 'text-align' : ''}`}>
                    <CustomButton
                      variant="contained"
                      className="red-button"
                      title={`${loader ? 'Saving...' : 'Save'}`}
                      onClick={OnboardingForms}
                      fullWidth={false}
                      disabled={!EnabledSave || ViewAccessOnly}
                    />
                  </Box>
                </Box>
              </>
            ) : (
              <></>
            )}
          </>
        ) : UKRTW === 'no' ? (
          <Box className="pt8">
            <Typography className="title-sm color-red">
              Please Contact Admin
            </Typography>
            <Box className={isUserScreen ? '' : 'pt8 text-align '}>
              <CustomButton
                variant="contained"
                className="red-button"
                title={`${loader ? 'Saving...' : 'Save'}`}
                onClick={OnboardingFormsRighttoWork}
                fullWidth={false}
                disabled
              />
            </Box>
          </Box>
        ) : (
          <></>
        )}
        {/* TODO: WE NEED TO HIDE THEM UNTIL WE COMPLETE THE DOCUMENT HISTORY. */}
        <DialogBox
          open={deleteConfirmation}
          handleClose={() => {
            handleDeleteCloseModal();
          }}
          title={'Confirmation'}
          className="delete-modal"
          dividerClass="delete-modal-divider"
          content={
            <>
              <DeleteModal
                handleCancel={handleDeleteCloseModal}
                handleConfirm={handleOnBoardingDelete}
                text={
                  <>
                    Are you sure you want to delete?
                    <br /> This action cannot be undone. If you need any data,
                    please download or back it up before continuing.
                  </>
                }
              />
            </>
          }
        />
      </Box>
    </>
  );
}
