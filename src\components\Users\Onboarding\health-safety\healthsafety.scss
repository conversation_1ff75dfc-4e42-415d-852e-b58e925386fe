.health-safety-sec {
  .display-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(0, 250px));
    gap: var(--spacing-lg);
    align-items: flex-start;
    width: 100%;
  }
  .health-safety-checkbox-details {
    .hf-checkbox {
      display: flex !important;
      flex-direction: row !important;
      align-items: center;
      width: 70%;

      .check-box-text {
        display: flex;
        flex-direction: row-reverse;
        justify-content: flex-start;
        margin-left: 0;
        width: 100%;

        .MuiButtonBase-root {
          padding-top: 0 !important;
          padding-bottom: 0 !important;
        }

        .MuiTypography-root {
          font-family: var(--font-family-primary);
          font-size: var(--font-size-sm);
          font-weight: var(--font-weight-regular);
          color: var(--text-color-black);
          line-height: var(--line-height-base);
          width: 100%;
        }
      }

      @media (max-width: 1299px) {
        width: 100%;
      }

      @media (max-width: 768px) {
        .check-box-text {
          margin-right: 0 !important;
        }
      }

      .Mui-disabled {
        opacity: 1;
        cursor: not-allowed;
        .MuiCheckbox-root.Mui-checked {
          color: var(--text-color-primary);
        }
        .MuiTypography-root {
          color: var(--text-color-black);
        }
      }
    }
  }
}
