@import '@/styles/variable.scss';

.Category-level-0-section,
.Category-level-1-section {
  .category-list-details {
    padding-top: 32px;
    .category-details {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px;
      border-radius: 8px;
      margin-top: 20px;
      width: 100%;
      box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
    }
  }
  .Main-level-cards-screen {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border-radius: 8px;
    margin-top: 20px;
    width: 100%;
    box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
    position: relative;
    .card-details {
      display: flex;
      justify-content: space-between;
      width: 100%;
      .card-left {
        display: flex;
        max-width: calc(100% - 24px - 52px - 20px);
        .cards-fileds {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
        }
      }
      .card-right {
        margin-right: 20px;
        display: flex;
        align-items: center;
      }
      @media (max-width: 680px) {
        // display: flex;
        // flex-direction: column;

        .card-right {
          margin-right: 5px;
        }
      }
      @media (max-width: 480px) {
        display: flex;
        flex-direction: column;

        .card-right {
          margin-left: 40px;
          margin-right: 0px;
          margin-top: 10px;
          align-self: flex-start;
        }
      }
    }
    .close-icon {
      position: absolute;
      right: -10px;
      top: -14px;
      width: 30px;
      height: 30px;
      cursor: pointer;
      fill: $color-primary !important;
    }
    .drag-icon {
      svg {
        cursor: grab !important;
      }
    }
  }
  .Main-level-cards-screen:first-child {
    margin-top: 0;
  }
  .weekly-check-box {
    .MuiFormControlLabel-label {
      font-family: $PrimaryFont !important;
      font-size: 14px !important;
      line-height: 21px !important;
      letter-spacing: -0.5px !important;
    }
  }
  .card-icon {
    column-gap: 5px;
  }
  .category-data-grid {
    padding-top: 32px;
    display: grid;
    grid-template-columns: repeat(3, 1fr) 30px;
    align-items: center;
    gap: 22px;
  }
}
.dsr-settings-map {
  position: relative;
  max-height: calc(100vh - 160px - var(--banner-height));
  overflow: scroll;
  svg {
    color: $color-primary;
    margin-right: 5px;
    cursor: pointer;
  }
  .deactive-section {
    display: flex;
    align-items: center;
    position: absolute;
    right: 6px;
    top: 4px;
  }
  @media (max-width: 1000px) {
    max-height: calc(100%);
    overflow: hidden;
  }
}
