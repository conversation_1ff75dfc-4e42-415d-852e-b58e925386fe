'use client';
import React, { useRef, useState, useContext } from 'react';
import { Box, Typography } from '@mui/material';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
// import CustomButton from '@/components/UI/button';
// import Multiselect from '@/components/UI/Multiselect';
import CollectionsIcon from '@mui/icons-material/Collections';
// import ImageIcon from '@mui/icons-material/Image';
import { useDropzone } from 'react-dropzone';
// import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import CustomTextField from '../../UI/CustomTextField';
import MultiSelect from '../../UI/CustomMultiSelect';
import CustomButton from '../../UI/CustomButton';
import CancelIcon from '@mui/icons-material/Cancel';
import HeaderImage from '@/components/UI/ImageSecurity';
import AuthContext from '@/helper/authcontext';

export default function SendNotification({
  handleSendNotification,
  isLoader,
  staffList,
  branchList,
  departmentList,
  roleList,
  setToggleModal,
  setStaffList,
  setBranchList,
  setDepartmentList,
  setRoleList,
}) {
  const formikRef = useRef(null);
  const { planDetail, authState, setRestrictedLimitModal } =
    useContext(AuthContext);
  const [acceptFile, setAcceptedFiles] = useState([]);
  const [error, setError] = useState('');
  const [imagePreview, setImagePreview] = useState('');
  const { getRootProps, getInputProps } = useDropzone({
    accept: {
      // 'application/pdf': [],
      // 'video/*': [],
      // 'audio/mpeg': [],
      'image/*': [],
    },
    multiple: false,
    onDrop: (acceptedFile, rejectedFiles) => {
      // Check if storage is full
      const totalStorage = planDetail?.total_storage || 0;
      const usedStorage = authState?.subscriptionUsage?.total_size_gb || 0;
      const fileSizeInGB = acceptedFile[0]?.size / (1024 * 1024 * 1024); // Convert bytes to GB

      if (usedStorage + fileSizeInGB > totalStorage) {
        setRestrictedLimitModal({
          storageLimit: true,
          totalStorage: planDetail?.total_storage,
          usedStorage: authState?.subscriptionUsage?.total_size_gb,
        });
        setAcceptedFiles([]);
        formikRef.current.setFieldValue('filename', '');
        setImagePreview('');
        return;
      }

      if (rejectedFiles.length > 0) {
        setError('Please upload Image only.');
      } else {
        setError(null);
        setAcceptedFiles(acceptedFile);
        formikRef.current.setFieldValue('filename', acceptedFile[0]);
        setImagePreview(URL.createObjectURL(acceptedFile[0]));
      }
    },
  });
  // const uploadedmedia = (type, file) => {
  //   if (type.startsWith('image')) {
  //     return (
  //       <Box className="image-sec">
  //         <ImageIcon />
  //         <Typography className="title-text file-name">
  //           {acceptedFiles?.[0]?.name
  //             ? acceptedFiles?.[0]?.name
  //             : file?.[0]?.name}
  //         </Typography>
  //       </Box>
  //     );
  //   } else {
  //     return <></>;
  //   }
  // };
  return (
    <Box>
      {imagePreview ? (
        <Box className="upload-preview-container">
          <HeaderImage
            imageUrl={imagePreview || ''}
            alt="not found"
            draggable="false"
            className="cursor-pointer"
            type="lazyload"
          />

          <CancelIcon
            className="cancel-icon"
            onClick={() => {
              setImagePreview('');
              setAcceptedFiles([]);
              formikRef.current.setFieldValue('filename', '');
            }}
          />
        </Box>
      ) : (
        <Box className="upload-drag-drop-container">
          <Box
            {...getRootProps({ className: 'dropzone' })}
            className="upload-area"
          >
            <CollectionsIcon />
            <input {...getInputProps()} />
            <Typography className="title-text upload-text">
              <span className="blue-text">Browse</span> or Drop your image here
            </Typography>
          </Box>
        </Box>
      )}
      {/* {acceptFile && acceptFile?.length === 1 ? (
        <Box className="uploaded-media-sec">
          {uploadedmedia(acceptFile?.[0]?.type, acceptFile)}
          <DeleteOutlineIcon
            className="cursor-pointer"
            onClick={() => {
              setAcceptedFiles([]);
              formikRef.current.setFieldValue('filename', '');
            }}
          />
        </Box>
      ) : (
        <></>
      )} */}
      {error && (
        <Typography className="other-field-error-text">{error}</Typography>
      )}
      <Formik
        innerRef={formikRef}
        initialValues={{
          subject: '',
          remark: '',
          individualuser: [],
          branch: [],
          department: [],
          role: [],
        }}
        enableReinitialize={true}
        validationSchema={Yup.object().shape({
          subject: Yup.string().trim().required('This field is required'),
          remark: Yup.string().trim().required('This field is required'),
        })}
        onSubmit={(requestdata) => {
          const extractIds = (array) => array?.map((item) => item.value);
          const branchList = staffList?.filter((f) => f?.value !== 'all');
          const userValues = extractIds(
            requestdata?.individualuser ? requestdata?.individualuser : []
          );
          const branchValues = extractIds(
            requestdata?.branch ? requestdata?.branch : []
          );
          const departmentValues = extractIds(
            requestdata?.department ? requestdata?.department : []
          );
          const roleValues = extractIds(
            requestdata?.role ? requestdata?.role : []
          );

          let notificationType = [];
          let type =
            requestdata?.individualuser?.length === branchList?.length
              ? 'all'
              : 'individual';

          userValues?.length > 0 && notificationType.push(type);
          // branchValues?.length > 0 && notificationType.push('branch');
          // departmentValues?.length > 0 && notificationType.push('department');
          roleValues?.length > 0 &&
            !departmentValues?.length > 0 &&
            !branchValues?.length > 0 &&
            notificationType.push('role');

          if (branchValues?.length > 0) {
            if (departmentValues?.length > 0) {
              notificationType.push('branch_department');
            } else if (roleValues?.length > 0) {
              notificationType.push('branch_role');
            } else {
              notificationType.push('branch');
            }
          } else {
            departmentValues?.length > 0 && notificationType.push('department');
          }
          // Now payload passed in formdata
          const body = new FormData();
          acceptFile &&
            acceptFile?.[0] &&
            body.append('notification_image', acceptFile?.[0]);
          requestdata?.subject &&
            body.append('notification_subject', requestdata?.subject);
          requestdata?.remark &&
            body.append('notification_content', requestdata?.remark);
          type !== 'all' && userValues && body.append('user_ids', userValues);
          branchValues && body.append('branch_ids', branchValues);
          departmentValues && body.append('department_ids', departmentValues);
          roleValues && body.append('role_ids', roleValues);
          notificationType &&
            body.append('notification_type', notificationType);
          handleSendNotification(body);
        }}
      >
        {({
          errors,
          touched,
          handleBlur,
          values,
          setFieldValue,
          handleSubmit,
          handleChange,
          //   dirty,
          //   isValid,
        }) => (
          <Form onSubmit={handleSubmit}>
            <Box className="pt16">
              <CustomTextField
                fullWidth
                id="subject"
                name="subject"
                label="Subject"
                placeholder="Enter subject"
                value={values?.subject}
                onChange={(e) => {
                  if (e.target.value.length <= 60) {
                    handleChange(e);
                  }
                }}
                onBlur={handleBlur}
                error={Boolean(touched.subject && errors.subject)}
                helperText={touched.subject && errors.subject}
                required
              />
              <Typography className="sub-title-text text-align-end mt4">
                {values?.subject.length + '/60'}
              </Typography>
            </Box>
            <Box className="pt8">
              <CustomTextField
                fullWidth
                id="remark"
                name="remark"
                label="Remark"
                placeholder="Remark"
                value={values?.remark}
                onChange={(e) => {
                  if (e.target.value.length <= 160) {
                    handleChange(e);
                  }
                }}
                onBlur={handleBlur}
                error={Boolean(touched.remark && errors.remark)}
                helperText={touched.remark && errors.remark}
                multiline
                rows={2}
                required
              />
              <Typography className="sub-title-text text-align-end mt4">
                {values?.remark.length + '/160'}
              </Typography>
            </Box>
            <Box className="noti-desc-divider" />
            <Box className="noti-field-grid-container">
              <Box className="">
                <MultiSelect
                  label="Staff user"
                  placeholder="Staff user"
                  options={staffList}
                  value={values?.individualuser}
                  error={Boolean(
                    touched.individualuser && errors.individualuser
                  )}
                  helperText={touched.individualuser && errors.individualuser}
                  onChange={(e) => {
                    const filterValue = e?.find((f) => f?.value === 'all');
                    const branchValue = staffList?.filter(
                      (f) => f?.value !== 'all'
                    );
                    if (filterValue || e?.length === branchValue?.length) {
                      const filterall = staffList?.filter(
                        (b) => b?.value !== 'all'
                      );
                      setStaffList(filterall);
                      const selectedValue = filterValue ? filterall : e;
                      setFieldValue('individualuser', selectedValue);
                    }
                    if (!filterValue) {
                      const isAll = staffList?.find((f) => f?.value === 'all');
                      if (!isAll) {
                        const alloption = [
                          { label: 'Select all', value: 'all' },
                        ];
                        let mergeList = _.concat(alloption, staffList);
                        setStaffList(mergeList);
                      }
                      setFieldValue('individualuser', e);
                    }
                  }}
                  isDisabled={
                    values?.branch?.length > 0 ||
                    values?.department?.length > 0 ||
                    values?.role?.length > 0
                  }
                />
              </Box>
              <Box className="">
                <MultiSelect
                  label="Branch"
                  placeholder="Branch"
                  options={branchList}
                  isOptionWithColor={true}
                  value={values?.branch}
                  error={Boolean(touched.branch && errors.branch)}
                  helperText={touched.branch && errors.branch}
                  onChange={(e) => {
                    const filterValue = e?.find((f) => f?.value === 'all');
                    const branchValue = branchList?.filter(
                      (f) => f?.value !== 'all'
                    );
                    if (filterValue || e?.length === branchValue?.length) {
                      const filterall = branchList?.filter(
                        (b) => b?.value !== 'all'
                      );
                      setBranchList(filterall);
                      const selectedValue = filterValue ? filterall : e;
                      setFieldValue('branch', selectedValue);
                    }
                    if (!filterValue) {
                      const isAll = branchList?.find((f) => f?.value === 'all');
                      if (!isAll) {
                        const alloption = [
                          { label: 'Select all', value: 'all' },
                        ];
                        let mergeList = _.concat(alloption, branchList);
                        setBranchList(mergeList);
                      }
                      setFieldValue('branch', e);
                    }
                  }}
                  isDisabled={values?.individualuser?.length > 0}
                />
              </Box>
            </Box>
            <Box className="noti-field-grid-container">
              <Box className="">
                <MultiSelect
                  label="Department"
                  placeholder="Department"
                  options={departmentList}
                  value={values?.department}
                  error={Boolean(touched.department && errors.department)}
                  helperText={touched.department && errors.department}
                  onChange={(e) => {
                    const filterValue = e?.find((f) => f?.value === 'all');
                    const branchValue = departmentList?.filter(
                      (f) => f?.value !== 'all'
                    );
                    if (filterValue || e?.length === branchValue?.length) {
                      const filterall = departmentList?.filter(
                        (b) => b?.value !== 'all'
                      );
                      setDepartmentList(filterall);
                      const selectedValue = filterValue ? filterall : e;
                      setFieldValue('department', selectedValue);
                    }
                    if (!filterValue) {
                      const isAll = departmentList?.find(
                        (f) => f?.value === 'all'
                      );
                      if (!isAll) {
                        const alloption = [
                          { label: 'Select all', value: 'all' },
                        ];
                        let mergeList = _.concat(alloption, departmentList);
                        setDepartmentList(mergeList);
                      }
                      setFieldValue('department', e);
                    }
                  }}
                  isDisabled={
                    values?.individualuser?.length > 0 ||
                    values?.role?.length > 0
                  }
                />
              </Box>
              <Box className="">
                <MultiSelect
                  label="System Access"
                  placeholder="System Access"
                  options={roleList}
                  value={values?.role}
                  error={Boolean(touched.role && errors.role)}
                  helperText={touched.role && errors.role}
                  onChange={(e) => {
                    const filterValue = e?.find((f) => f?.value === 'all');
                    const branchValue = roleList?.filter(
                      (f) => f?.value !== 'all'
                    );
                    if (filterValue || e?.length === branchValue?.length) {
                      const filterall = roleList?.filter(
                        (b) => b?.value !== 'all'
                      );
                      setRoleList(filterall);
                      const selectedValue = filterValue ? filterall : e;
                      setFieldValue('role', selectedValue);
                    }
                    if (!filterValue) {
                      const isAll = roleList?.find((f) => f?.value === 'all');
                      if (!isAll) {
                        const alloption = [
                          { label: 'Select all', value: 'all' },
                        ];
                        let mergeList = _.concat(alloption, roleList);
                        setRoleList(mergeList);
                      }
                      setFieldValue('role', e);
                    }
                  }}
                  isDisabled={
                    values?.individualuser?.length > 0 ||
                    values?.department?.length > 0
                  }
                />
              </Box>
            </Box>
            <Box className="form-actions-btn">
              <CustomButton
                fullWidth
                variant="outlined"
                title="Cancel"
                onClick={() => setToggleModal(false)}
              />
              <CustomButton
                fullWidth
                type="submit"
                title={isLoader ? 'Sending...' : 'Send'}
                disabled={
                  isLoader ||
                  (values?.individualuser?.length === 0 &&
                    values?.branch?.length === 0 &&
                    values?.department?.length === 0 &&
                    values?.role?.length === 0)
                }
              />
            </Box>
          </Form>
        )}
      </Formik>
    </Box>
  );
}
