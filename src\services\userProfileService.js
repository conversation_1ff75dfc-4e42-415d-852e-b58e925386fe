import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import { setApiMessage } from '@/helper/common/commonFunctions';
import dayjs from 'dayjs';

export const getUserDetails = async (id) => {
  try {
    const { status, data } = await axiosInstance.get(
      URLS?.GET_ONE_USER + `${id}`
    );
    if (status === 200) {
      let filterUserList = data?.data?.user_roles?.map((user) => ({
        label: user?.role_name,
        value: user?.id,
      }));
      let filterBranchList = data?.data?.assign_branch_ids?.map((branch) => ({
        label: branch?.branch_name,
        value: branch?.id,
        color: branch?.branch_color,
      }));
      return {
        success: true,
        data: {
          ...data?.data,
          user_roles: filterUserList,
          assign_branch_ids: filterBranchList,
        },
      };
    }
  } catch (error) {
    setApiMessage('error', error?.response?.data?.message);
    return { success: false, error };
  }
};

export const getGeneralTemplateDetails = async () => {
  try {
    const { status, data } = await axiosInstance.get(
      URLS?.GET_FILE_LIST + `?type=general&status=active`
    );

    if (status === 200) {
      const tempData = data?.data?.map((item) => ({
        ...item,
        label: item?.name,
        value: item?.id,
      }));
      return { success: true, data: tempData };
    }
  } catch (error) {
    setApiMessage('error', error?.response?.data?.message);
    return { success: false, error };
  }
};

export const getDepartmentTemplateDetailsByID = async (id) => {
  try {
    const { status, data } = await axiosInstance.get(
      URLS?.GET_FILE_LIST +
        `?type=department&status=active&department_id=${id ? id : ''}`
    );

    if (status === 200) {
      const tempData = data?.data?.map((item) => ({
        ...item,
        cat_id: item?.emp_contract_category?.id,
        cat_name: item?.emp_contract_category?.name,
        cat_type: item?.emp_contract_category?.type,
        dept_id: item?.emp_contract_category?.department_id,
        label: item?.name,
        value: item?.id,
      }));
      return { success: true, data: tempData };
    }
  } catch (error) {
    setApiMessage('error', error?.response?.data?.message);
    return { success: false, error };
  }
};

export const getDepartmentTemplateDetails = async (is, id, allID) => {
  try {
    const { status, data } = await axiosInstance.get(
      URLS?.GET_FILE_LIST +
        `?type=department&status=active&department_id=${is ? is : ''}`
    );

    if (status === 200) {
      const tempData = data?.data?.map((item) => ({
        ...item,
        cat_id: item?.emp_contract_category?.id,
        cat_name: item?.emp_contract_category?.name,
        cat_type: item?.emp_contract_category?.type,
        dept_id: item?.emp_contract_category?.department_id,
        label: item?.name,
        value: item?.id,
      }));

      let contractList = [];
      let departmentTemplateData = tempData;

      if ((id && !Number.isNaN(Number(id))) || (allID && allID?.length > 0)) {
        const filteredArray =
          tempData &&
          tempData.length > 0 &&
          allID?.length > 0 &&
          allID
            ?.filter((id) => tempData.some((item) => item.id === id))
            .map((id) => tempData.find((item) => item.id === id));

        if (filteredArray && filteredArray?.length > 0) {
          contractList = filteredArray;
        }

        const filterCat = tempData?.filter((f) => f?.id !== Number(id));
        const filterCats = filterCat?.filter((f) => !allID?.includes(f?.id));
        departmentTemplateData = filterCats;
      }

      return {
        success: true,
        data: {
          departmentTemplateData,
          contractList,
          departmentTempByID: tempData,
        },
      };
    }
  } catch (error) {
    setApiMessage('error', error?.response?.data?.message);
    return { success: false, error };
  }
};

export const getContractTypeDetails = async () => {
  try {
    const { status, data } = await axiosInstance.get(
      URLS?.GET_USER_CONTRACT_TYPE
    );

    if (status === 200) {
      const tempData = data?.data?.map((item) => ({
        ...item,
        label: item?.contract_name,
        value: item?.id,
      }));
      return { success: true, data: tempData };
    }
  } catch (error) {
    setApiMessage('error', error?.response?.data?.message);
    return { success: false, error };
  }
};

export const handleCreateOptionContractPolicy = async (inputValue) => {
  try {
    const sendData = { contract_name: inputValue };
    const { status } = await axiosInstance.post(
      URLS?.ADD_USER_CONTRACT_TYPE,
      sendData
    );
    return { success: status === 200 };
  } catch (error) {
    setApiMessage('error', error?.response?.data?.message);
    return { success: false, error };
  }
};

export const getRenewalList = async (
  search,
  pageNo,
  startDate,
  endDate,
  statusValue,
  rowsPerPage
) => {
  try {
    const start_date = startDate ? dayjs(startDate)?.format('YYYY-MM-DD') : '';
    const end_date = endDate ? dayjs(endDate)?.format('YYYY-MM-DD') : '';

    const { status, data } = await axiosInstance.get(
      URLS?.GET_RENEWL_LIST +
        `?page=${pageNo}&size=${rowsPerPage}&search=${search}&status=${
          statusValue ? statusValue : ''
        }&startDate=${start_date}&endDate=${end_date}`
    );

    if (status === 200) {
      return {
        success: true,
        data: {
          list: data?.data,
          total: data?.total,
        },
      };
    }
  } catch (error) {
    setApiMessage('error', error?.response?.data?.message);
    return { success: false, error };
  }
};

export const handleContractAction = async (userId, type) => {
  try {
    const { status, data } = await axiosInstance.get(
      URLS.REGENERATE_EMP_CONTRACT +
        `${userId}${
          type === 'notify'
            ? '?notify=true'
            : type === 'remind'
              ? '?notify=only'
              : ''
        }`
    );

    if (status === 200) {
      setApiMessage('success', data?.message);
      return { success: true };
    }
  } catch (error) {
    setApiMessage('error', error?.response?.data?.message);
    return { success: false, error };
  }
};
