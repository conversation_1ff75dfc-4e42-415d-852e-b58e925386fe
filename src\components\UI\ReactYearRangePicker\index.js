import React, { useEffect, useState } from 'react';
import { Box, InputAdornment, TextField, InputLabel } from '@mui/material';
import DatePicker from 'react-datepicker';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import 'react-datepicker/dist/react-datepicker.css';
import KeyboardArrowLeftIcon from '@mui/icons-material/KeyboardArrowLeft';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import '../ReactWeekDatePicker/datepicker.scss';
import './datepicker.scss';

const CustomYearRangePicker = ({
  onChange,
  value,
  className,
  disablePast,
  disabled,
  minDate,
  maxDate,
  placeholder,
  isClearable,
  label,
  isSingleMonthpicker,
}) => {
  const [Year, setYear] = useState(new Date());

  const handleChange = (date) => {
    setYear(date);
    onChange?.({ Year: date });
  };

  useEffect(() => {
    value?.Year ? setYear(new Date(value?.Year)) : setYear();
  }, [value]);
  const renderCustomHeader = ({ date, decreaseYear, increaseYear }) => (
    <div className="react-datepicker__year-header">
      <button
        onClick={decreaseYear}
        className="react-datepicker__navigation react-datepicker__navigation--previous"
      >
        <KeyboardArrowLeftIcon />
      </button>
      {isSingleMonthpicker ? (
        <>
          {' '}
          <span className="year-headr">{date.getFullYear()}</span>
        </>
      ) : (
        <>
          {' '}
          <span className="year-headr">
            {date.getFullYear()} - {date.getFullYear() + 1}
          </span>
        </>
      )}

      <button
        onClick={increaseYear}
        className="react-datepicker__navigation react-datepicker__navigation--next"
      >
        <KeyboardArrowRightIcon />
      </button>
    </div>
  );
  return (
    <Box
      className={
        className
          ? `${className} date-wrap react-datepicker-sec w100 react-Monthpicker-sec  `
          : 'date-wrap w100 react-datepicker-sec react-Monthpicker-sec '
      }
    >
      <InputLabel className="input-label">{label}</InputLabel>
      <Box className="date-picker-container">
        <DatePicker
          selected={Year}
          onChange={handleChange}
          startDate={Year}
          endDate={Year}
          minDate={disablePast ? new Date() : minDate}
          maxDate={maxDate}
          showYearPicker
          dateFormat="yyyy"
          placeholderText={placeholder || 'Start Month'}
          disabled={disabled}
          isClearable={isClearable}
          renderCustomHeader={renderCustomHeader}
          customInput={
            <TextField
              className="input_field"
              variant="outlined"
              fullWidth
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <CalendarMonthIcon sx={{ cursor: 'pointer' }} />
                  </InputAdornment>
                ),
                value:
                  isSingleMonthpicker && Year
                    ? `${Year.getFullYear()}`
                    : Year
                      ? `${Year.getFullYear()} - ${Year.getFullYear() + 1}`
                      : '',
              }}
            />
          }
        />
      </Box>
    </Box>
  );
};

export default CustomYearRangePicker;
