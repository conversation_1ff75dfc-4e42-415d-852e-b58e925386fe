import React from 'react';
import CustomSelect from '@/components/UI/CustomSelect';
import CustomTextField from '@/components/UI/CustomTextField';
import { Box, Typography } from '@mui/material';
import { identifiers } from '@/helper/constants/identifier';
export default function EncashmentSection({
  values,
  handleChange,
  handleBlur,
  touched,
  errors,
  setFieldValue,
  leavePolicySubmit,
  setLeavePolicySubmit,
  Inputype,
}) {
  return (
    <>
      <Box className="d-flex gap-10 align-center mt16 flex-wrap">
        <Box className="effective-after-start d-flex align-center gap-10 ">
          <Typography className="title-text">Encashment</Typography>
          <Box className="d-flex align-center">
            <Box className="small-text-field">
              <CustomTextField
                fullWidth
                id="encashmentDays"
                name="encashmentDays"
                value={values?.encashmentDays}
                placeholder=""
                className="text-field-right-border-none"
                error={Boolean(
                  touched?.encashmentDays && errors?.encashmentDays
                )}
                helperText={touched?.encashmentDays && errors?.encashmentDays}
                onBlur={handleBlur}
                onChange={(e) => {
                  handleChange(e);
                  setLeavePolicySubmit({
                    ...leavePolicySubmit,
                    leaveAccrual: false,
                  });
                }}
                onInput={Inputype}
              />
            </Box>
            <CustomSelect
              name="encashmentPercentage"
              placeholder="Percentage"
              className="text-field-left-border-none leave-select-wrap"
              options={identifiers?.CARRY_FORWARD_OPTIONS}
              value={
                identifiers?.CARRY_FORWARD_OPTIONS?.find((opt) => {
                  return opt?.value === values.encashmentPercentage;
                }) || ''
              }
              onChange={(e) => {
                setFieldValue('encashmentPercentage', e?.value);
                setLeavePolicySubmit({
                  ...leavePolicySubmit,
                  leaveAccrual: false,
                });
              }}
              error={Boolean(
                touched?.encashmentPercentage && errors?.encashmentPercentage
              )}
              helperText={
                touched?.encashmentPercentage && errors?.encashmentPercentage
              }
              isClearable={false}
            />
          </Box>
        </Box>
        <Box className="effective-after-end d-flex align-center gap-10 flex-wrap">
          <Box className="d-flex align-center gap-sm">
            <Typography className="title-text">Max Limit</Typography>
            <Box>
              <CustomTextField
                fullWidth
                id="encashmentMaxLimit"
                name="encashmentMaxLimit"
                value={values?.encashmentMaxLimit}
                placeholder="Days"
                error={Boolean(
                  touched.encashmentMaxLimit && errors.encashmentMaxLimit
                )}
                helperText={
                  touched.encashmentMaxLimit && errors.encashmentMaxLimit
                }
                onBlur={handleBlur}
                onChange={(e) => {
                  handleChange(e);
                  setLeavePolicySubmit({
                    ...leavePolicySubmit,
                    leaveAccrual: false,
                  });
                }}
                onInput={Inputype}
              />
            </Box>
          </Box>
          <Box className="d-flex align-center gap-sm">
            <Typography className="title-text">Min Limit</Typography>
            <Box>
              <CustomTextField
                fullWidth
                id="encashmentMinLimit"
                name="encashmentMinLimit"
                value={values?.encashmentMinLimit}
                placeholder="Days"
                error={Boolean(
                  touched.encashmentMinLimit && errors.encashmentMinLimit
                )}
                helperText={
                  touched.encashmentMinLimit && errors.encashmentMinLimit
                }
                onBlur={handleBlur}
                onChange={(e) => {
                  handleChange(e);
                  setLeavePolicySubmit({
                    ...leavePolicySubmit,
                    leaveAccrual: false,
                  });
                }}
                onInput={Inputype}
              />
            </Box>
          </Box>
        </Box>
      </Box>
    </>
  );
}
