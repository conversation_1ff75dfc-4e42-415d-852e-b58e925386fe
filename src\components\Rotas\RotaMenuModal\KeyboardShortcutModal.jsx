import KeyboardOptionKeyIcon from '@mui/icons-material/KeyboardOptionKey';
import ForwardOutlinedIcon from '@mui/icons-material/ForwardOutlined';
import KeyboardArrowLeftOutlinedIcon from '@mui/icons-material/KeyboardArrowLeftOutlined';
import KeyboardArrowRightOutlinedIcon from '@mui/icons-material/KeyboardArrowRightOutlined';
import './rotaMenumodal.scss';

const KeyboardShortcutModal = () => {
  const shortcuts = [
    {
      action: 'Copy shift',
      keys: [
        'Hold',
        <ForwardOutlinedIcon style={{ transform: 'rotate(270deg)' }} />,
      ],
    },
    {
      action: 'Delete shift',
      keys: ['Hold', 'Alt', <KeyboardOptionKeyIcon />],
    },
    {
      action: 'Move between months',
      keys: [
        <KeyboardArrowLeftOutlinedIcon />,
        'or',
        <KeyboardArrowRightOutlinedIcon />,
      ],
    },
    {
      action: 'Add/remove days off',
      keys: ['Hold', 'D'],
    },
    {
      action: 'Publish/unpublish shift',
      keys: ['Hold', 'P'],
    },
    {
      action: 'Search employee',
      keys: ['Ctrl', '+', 'K'],
    },
  ];

  return (
    <div className="keyboard-shortcuts-modal">
      <div className="shortcuts-grid">
        {shortcuts.map((shortcut, index) => (
          <div key={index} className="shortcut-row">
            <span className="shortcut-action">{shortcut.action}</span>
            <div className="shortcut-keys">
              {shortcut.keys.map((key, keyIndex) => (
                <span key={keyIndex}>
                  {key === 'or' ? (
                    <span className="separator">{key}</span>
                  ) : (
                    <span className="kbd-key">{key}</span>
                  )}
                </span>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default KeyboardShortcutModal;
