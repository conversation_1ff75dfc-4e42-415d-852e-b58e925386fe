import React from 'react';
import { Box } from '@mui/material';
import { generateMetadata } from '@/helper/common/commonFunctions';
import ExpenseReq from '@/components/DSR/ExpenseRequest/ExpenseReq';

export const metadata = generateMetadata({
  pageTitle: 'Expense',
});

const DSRPage = ({ params }) => {
  return (
    <Box className="main-page-container">
      <ExpenseReq params={params} />
    </Box>
  );
};

export default DSRPage;
