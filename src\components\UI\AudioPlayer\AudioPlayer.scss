.audio-player {
  background-color: var(--color-white);
  padding: var(--spacing-lg) var(--spacing-xl);
  border-radius: var(--border-radius-md);
  margin: var(--spacing-md) 0;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-width: 100%;

  &__content {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
  }

  &__play-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-xs);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: opacity 0.2s ease;
    border-radius: var(--border-radius-full);
    width: 32px;
    height: 32px;

    &:hover {
      opacity: 0.8;
      background-color: var(--color-primary-opacity);
    }
  }

  &__time {
    font-size: var(--font-size-sm);
    color: var(--color-primary);
    font-weight: 500;
    min-width: 40px;
    text-align: center;
  }

  &__progress-container {
    flex: 1;
    position: relative;
    height: 20px;
    display: flex;
    align-items: center;
  }

  &__progress {
    width: 100%;
    height: 4px;
    background: transparent;
    outline: none;
    cursor: pointer;
    -webkit-appearance: none;
    appearance: none;

    // Track (background)
    &::-webkit-slider-track {
      height: 4px;
      background: var(--color-light-gray);
      border-radius: var(--border-radius-xs);
    }

    &::-moz-range-track {
      height: 4px;
      background: var(--color-light-gray);
      border-radius:var(--border-radius-xs) ;
      border: none;
    }

    // Thumb (slider handle)
    &::-webkit-slider-thumb {
      -webkit-appearance: none;
      appearance: none;
      width: 16px;
      height: 16px;
      background: var(--color-primary);
      border-radius: 50%;
      cursor: pointer;
      border: var(--normal-sec-border);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    &::-moz-range-thumb {
      width: 16px;
      height: 16px;
      background: var(--color-primary);
      border-radius: 50%;
      cursor: pointer;
      border:var(--normal-sec-border) ;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    // Progress fill (using a pseudo-element approach)
    background: linear-gradient(
      to right,
      var(--color-primary) 0%,
      var(--color-primary) var(--progress, 0%),
      #e0e0e0 var(--progress, 0%),
      #e0e0e0 100%
    );
    background-size: 100% 4px;
    background-repeat: no-repeat;
    background-position: center;
  }

  &__volume-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-xs);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: opacity 0.2s ease;
    border-radius: var(--border-radius-full);
    width: 32px;
    height: 32px;

    &:hover {
      opacity: 0.8;
      background-color: rgba(0, 0, 0, 0.05);
    }
  }

  &__close {
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-xs);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: opacity 0.2s ease;
    border-radius: var(--border-radius-full);
    width: 32px;
    height: 32px;

    &:hover {
      opacity: 0.8;
      background-color: var(--color-primary-opacity)  ;
    }
  }
}