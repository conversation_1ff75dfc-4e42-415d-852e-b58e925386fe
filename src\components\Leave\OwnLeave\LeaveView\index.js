'use client';
import React, { useContext, useEffect, useState } from 'react';
import { Box, Divider, Typography } from '@mui/material';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import PendingActionsIcon from '@mui/icons-material/PendingActions';
import RemoveCircleIcon from '@mui/icons-material/RemoveCircle';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import { useParams, useRouter } from 'next/navigation';
import CustomDoubleCalender from '@/components/UI/DoubleCalender';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import { setApiMessage } from '@/helper/common/commonFunctions';
import moment from 'moment';
import UserAvatar from '@/components/UI/Avatar/UserAvatar';
import AuthContext from '@/helper/authcontext';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomButton from '@/components/UI/CustomButton';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import LeaveCard from '@/components/Leave/MyLeave/LeaveCard';
import './viewleave.scss';

export default function ViewLeave() {
  const [selectedDate, setSelectedDate] = useState(null); // State for selected date
  const [events, setEvents] = useState([]);
  const [searchValue, setSearchValue] = useState('');
  const [leaveBalanceDetails, setLeaveBalanceDetails] = useState([]);
  const [calenderData, setCalenderData] = useState({
    coloredEvents: [],
    holidayEvents: [],
    allEvents: [],
  });
  const [userDetails, setUserDetails] = useState([]);
  const { authState, setAuthState } = useContext(AuthContext);
  let router = useRouter();

  let params = useParams();
  let userId = params?.id;
  dayjs.extend(utc);
  dayjs.extend(timezone);
  const getSingleUserList = async () => {
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_SINGLE_USER + `/${userId}`
      );
      if (status === 200) {
        setUserDetails(data?.data);
        data?.data?.request_from_users?.id &&
          getSingleUserBalance(
            data?.data?.request_from_users?.id,
            data?.data?.leave_request_type_list?.id,
            data?.data?.start_date
          );
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  useEffect(() => {
    if (userId) {
      getSingleUserList();
    }
  }, [userId]);

  const cancelLeave = async (remark) => {
    const requestData = {
      remark: remark,
      request_id: params?.id,
    };
    try {
      const { status, data } = await axiosInstance.post(
        URLS?.DELETE_LEAVE,
        requestData
      );
      if (status === 200) {
        setApiMessage('success', data?.message);
        router.push('/own-leave');
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const getSingleUserBalance = async (id, leaveId, year) => {
    const leaveYear = moment(year).format('YYYY');
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.LEAVE_BALANCE + `${id}?year=${leaveYear}&leave_period_type=`
      );
      if (status === 200) {
        const userBalance = data?.data?.find((leveTypeId) => {
          return leveTypeId?.id === leaveId;
        });
        setLeaveBalanceDetails(userBalance);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const getLeaveDetails = async (view) => {
    try {
      let apiUrl = `${URLS?.CALENDER_WISE_LEAVE}?list_type=own&search=`;

      if (view?.Start && view?.End) {
        apiUrl += `&start_date=${view?.Start}&end_date=${view?.End}`;
      } else if (view?.Start) {
        apiUrl += `&start_date=${view?.Start}`;
      } else if (view?.End) {
        apiUrl += `&end_date=${view?.End}`;
      }

      const { status, data } = await axiosInstance.get(apiUrl);

      if (status === 200) {
        const coloredEvents = data?.data?.calenderLeaves?.map((event) => ({
          ...event,
          start: moment(event?.start_date).format('YYYY-MM-DD'),
          end: event?.end_date
            ? moment(event?.start_date).format('YYYY-MM-DD') ===
              moment(event?.end_date).format('YYYY-MM-DD')
              ? moment(event?.end_date).format('YYYY-MM-DD')
              : moment(event?.end_date).add(1, 'day').format('YYYY-MM-DD')
            : moment(event?.start_date).format('YYYY-MM-DD'),
          allDay: true,
          eventType: 'leave',
        }));

        const holidayEvents = data?.data?.holidayList?.map((event) => ({
          ...event,
          id: `holiday-${event?.id}`,
          start: moment(event?.holiday_policy_start_date).format('YYYY-MM-DD'),
          end: moment(event?.holiday_policy_end_date).format('YYYY-MM-DD'),
          title: event?.holiday_policy_name,
          backgroundColor: event?.holiday_policy_colour,
          borderColor: event?.holiday_policy_colour,
          allDay: true,
          eventType: 'holiday',
        }));

        setCalenderData({
          coloredEvents,
          holidayEvents,
          allEvents: [...coloredEvents, ...holidayEvents],
        });
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  return (
    <Box>
      <Box className="">
        <Formik
          initialValues={{
            leaveApproval: '', // Leave Approval input
            leaveType: '', // Leave Type input
            reason: '', //singleList?.approved_by_reason ? singleList?.approved_by_reason:""
          }}
          enableReinitialize={true}
          validationSchema={Yup.object().shape({
            leaveApproval: Yup.string().required('Leave Approval is required'),
            leaveType: Yup.string().required('Leave Type is required'),
            reason: Yup.string().trim().required('Remark is required'),
          })}
          onSubmit={async (values) => {
            const payload = {
              action: values?.leaveApproval,
              remark: values?.reason,
              request_id: userId,
              leave_deduction_type: values?.leaveType,
            };

            try {
              const { status, data } = await axiosInstance.post(
                URLS?.LEAVE_ACTION,
                payload
              );
              if (status === 200) {
                setApiMessage('success', data?.message);
              }
            } catch (error) {
              setApiMessage('error', error?.response?.data?.message);
            }
          }}
        >
          {({
            errors,
            touched,
            handleBlur,
            values,
            handleSubmit,
            handleChange,
            validateForm,
            setFieldTouched,
            setFieldError,
          }) => (
            <Form onSubmit={handleSubmit}>
              <Box className="update-leave-wrap">
                <Box className="update-leave-header-text d-flex">
                  <ArrowBackIosIcon
                    className="cursor-pointer"
                    onClick={() => {
                      if (authState?.listViewMode === 'calendar') {
                        router.push('/own-leave');
                        setAuthState({
                          ...authState,
                          listViewMode: 'calendar',
                        });
                      } else {
                        router.push('/own-leave');
                        setAuthState({ ...authState, listViewMode: 'list' });
                      }
                    }}
                  />
                  <Typography className="title-sm">View Leave</Typography>
                </Box>
                <Divider />
                <Box className="leave-container d-flex">
                  <Box className="update-leave">
                    <Box className="leave-left-wrap d-flex align-center justify-space-between mb8 mt8">
                      <Box className="leave-left d-flex">
                        <Box className="leave-icons-wrap">
                          <UserAvatar
                            name={
                              userDetails?.request_from_users?.user_full_name
                            }
                            src={
                              userDetails?.request_from_users?.user_avatar_link
                            }
                            classname=""
                          />
                        </Box>
                        <Box className="leave-left">
                          <Box className="user-detail-wrap">
                            <Typography className="body-text" variant="h6">
                              {userDetails?.request_from_users?.user_full_name}
                            </Typography>
                            <Typography className="user-email sub-title-text">
                              {userDetails?.request_from_users?.user_email}
                            </Typography>
                            <Typography className="sub-title-text">
                              {userDetails?.leave_request_type_list?.name}
                            </Typography>
                            <Typography className="user-branch-name sub-title-text fw400">
                              {
                                userDetails?.request_from_users?.branch
                                  ?.branch_name
                              }
                            </Typography>
                          </Box>
                          <Box className="leave-info">
                            <Typography className="sub-title-text text-capital">
                              {userDetails?.leaveRequest}
                            </Typography>
                            <Typography className="sub-title-text d-flex align-center gap-5">
                              Applied As
                              <span className="role-wrap fw600">
                                {userDetails?.leave_role_request?.role_name}
                              </span>
                            </Typography>
                          </Box>
                        </Box>
                      </Box>
                      <Box className="leave-right">
                        <Typography
                          className={`title-text fw500 leave-status text-capital ${
                            userDetails?.request_status === 'rejected'
                              ? 'leave-rejected'
                              : userDetails?.request_status === 'approved'
                                ? 'leave-success'
                                : userDetails?.request_status === 'cancelled'
                                  ? 'leave-cancelled'
                                  : 'leave-draft'
                          }`}
                        >
                          {userDetails?.request_status}
                        </Typography>
                      </Box>
                    </Box>
                    <Divider />
                    <Box className="body-text fw600 leave-date-wrap mt8 mb8">
                      <Box className="leave-date d-flex align-center">
                        <Box className="start-date-wrap">
                          <Typography className="text-wrap sub-title-text">
                            Start Date
                          </Typography>
                          <Typography className="start-date title-text fw400">
                            {' '}
                            {/* Day & Hours dates */}
                            {userDetails?.leave_period_type === 'hour' ||
                            userDetails?.duration_type === 'Hours'
                              ? dayjs
                                  .utc(userDetails?.start_date)
                                  .format('DD-MM-YYYY') //format('DD-MM-YYYY hh:mm A')
                              : dayjs(userDetails?.start_date).format(
                                  'DD-MM-YYYY'
                                )}
                          </Typography>
                        </Box>
                        <Box className="end-date-wrap">
                          <Typography className="text-wrap sub-title-text">
                            End Date
                          </Typography>
                          <Typography className="start-date title-text fw400">
                            {/* Day & Hours dates */}
                            {userDetails?.leave_period_type === 'hour' ||
                            userDetails?.duration_type === 'Hours'
                              ? dayjs
                                  .utc(userDetails?.end_date)
                                  .format('DD-MM-YYYY') //.format('DD-MM-YYYY hh:mm A')
                              : dayjs(userDetails?.end_date).format(
                                  'DD-MM-YYYY'
                                )}
                          </Typography>
                        </Box>
                      </Box>
                      <Box className="icon-wrap d-flex align-center ">
                        <Typography className="text-wrap sub-title-text">
                          {`${userDetails?.leave_days} ${
                            userDetails?.leave_period_type === 'day'
                              ? userDetails?.leave_days === 1
                                ? 'Day'
                                : 'Days'
                              : userDetails?.leave_period_type === 'hour'
                                ? userDetails?.leave_days === 1
                                  ? 'Hour'
                                  : 'Hours'
                                : userDetails?.duration_type === 'Days'
                                  ? userDetails?.leave_days === 1
                                    ? 'Day'
                                    : 'Days'
                                  : userDetails?.leave_days === 1
                                    ? 'Hour'
                                    : 'Hours'
                          }`}
                        </Typography>
                        {/* <Box className="d-flex align-center gap-5">
                          <CircleIcon className="circle-icon" />
                          <Typography className="text-wrap sub-title-text" component="p">
                            {userDetails?.workingDayStatus}
                          </Typography>
                        </Box> */}
                      </Box>
                    </Box>

                    {userDetails?.request_status === 'pending' && (
                      <>
                        <Divider />
                        <Typography className="subject-wrap title-text mt8 ">
                          <span className="fw500">Subject :</span>{' '}
                          {userDetails?.request_subject}
                        </Typography>
                        <Typography className="reason-wrap title-text mb8">
                          <span className="fw500">Reason :</span>{' '}
                          {userDetails?.request_reason}
                        </Typography>
                        <Divider />
                        <Box className="leave-status-text-wrap mt8 mb8 d-flex gap-5">
                          <Box className="status-icon-wrap">
                            {userDetails?.request_status === 'approved' ? (
                              <CheckCircleIcon className="approved-icon" />
                            ) : userDetails?.request_status === 'rejected' ? (
                              <CancelIcon className="rejected-icon" />
                            ) : userDetails?.request_status === 'pending' ? (
                              <PendingActionsIcon className="pending-icon" />
                            ) : userDetails?.request_status === 'cancelled' ? (
                              <RemoveCircleIcon className="cancelled-icon" />
                            ) : null}
                          </Box>
                          <Typography
                            className="title-text fw400"
                            component="p"
                          >
                            Your Request Is
                            <span
                              className={`fw600 ${
                                userDetails?.request_status === 'approved'
                                  ? 'approved-status'
                                  : userDetails?.request_status === 'rejected'
                                    ? 'rejected-status'
                                    : userDetails?.request_status === 'pending'
                                      ? 'pending-status'
                                      : userDetails?.request_status ===
                                          'cancelled'
                                        ? 'cancelled-status'
                                        : ''
                              }`}
                            >
                              {userDetails?.request_status}
                            </span>
                          </Typography>
                        </Box>
                        <Divider />
                        <Box className="pt24 input-wrap reason-input pb8">
                          <CustomTextField
                            fullWidth
                            id="reason"
                            name="reason"
                            value={values?.reason}
                            label="Remark"
                            required
                            rows={2}
                            placeholder="Enter remark"
                            error={Boolean(touched?.reason && errors?.reason)}
                            helperText={touched?.reason && errors?.reason}
                            onBlur={handleBlur}
                            onChange={(e) => {
                              if (event.target.value.length <= 160) {
                                handleChange(e);
                              }
                            }}
                            multiline
                          />
                          <Typography className="sub-title-text text-align-end">
                            {values?.reason?.length + '/ 160'}
                          </Typography>
                        </Box>
                        <Box className="d-flex justify-end">
                          <CustomButton
                            variant="contained"
                            className="red-button"
                            title="Cancel Leave"
                            onClick={async () => {
                              const errors = await validateForm();
                              if (!errors.reason) {
                                cancelLeave(values.reason);
                              } else {
                                setFieldTouched('reason', true);
                                setFieldError('reason', 'Remark is required');
                              }
                            }}
                          />
                        </Box>
                      </>
                    )}

                    {userDetails?.request_status !== 'pending' && (
                      <>
                        <Divider />
                        <Typography className="subject-wrap title-text mt8">
                          <span className=" fw500">Subject :</span>{' '}
                          {userDetails?.request_subject}
                        </Typography>
                        <Typography className="reason-wrap title-text mb8">
                          <span className=" fw500">Reason :</span>{' '}
                          {userDetails?.request_reason}
                        </Typography>
                        <Divider />
                        <Box className="leave-status-text-wrap mt8 mb8 d-flex gap-5">
                          <Box className="status-icon-wrap">
                            {userDetails?.request_status === 'approved' ? (
                              <CheckCircleIcon className="approved-icon" />
                            ) : userDetails?.request_status === 'rejected' ? (
                              <CancelIcon className="rejected-icon" />
                            ) : userDetails?.request_status === 'pending' ? (
                              <PendingActionsIcon className="pending-icon" />
                            ) : userDetails?.request_status === 'cancelled' ? (
                              <RemoveCircleIcon className="cancelled-icon" />
                            ) : null}
                          </Box>{' '}
                          {userDetails?.request_approved_users
                            ?.user_full_name ? (
                            <Typography
                              className="title-text fw400"
                              component="p"
                            >
                              Your Request Is
                              <span
                                className={`fw600 ${
                                  userDetails?.request_status === 'approved'
                                    ? 'approved-status'
                                    : userDetails?.request_status === 'rejected'
                                      ? 'rejected-status'
                                      : userDetails?.request_status ===
                                          'pending'
                                        ? 'pending-status'
                                        : userDetails?.request_status ===
                                            'cancelled'
                                          ? 'cancelled-status'
                                          : ''
                                }`}
                              >
                                {userDetails?.request_status}
                              </span>
                              On
                              <span className="fw600">
                                {moment(userDetails?.updatedAt).format(
                                  'DD/MM/YYYY hh:mm A'
                                )}
                              </span>
                              By
                              <span className="fw600">
                                {
                                  userDetails?.request_approved_users
                                    ?.user_full_name
                                }
                              </span>
                              With Below Comments
                              <span className="fw600">
                                {userDetails?.approved_by_reason}
                              </span>
                            </Typography>
                          ) : (
                            <Typography
                              className="title-text fw400"
                              component="p"
                            >
                              Your request was auto
                              <span
                                className={`fw600 ${
                                  userDetails?.request_status === 'approved'
                                    ? 'approved-status'
                                    : userDetails?.request_status === 'rejected'
                                      ? 'rejected-status'
                                      : userDetails?.request_status ===
                                          'pending'
                                        ? 'pending-status'
                                        : userDetails?.request_status ===
                                            'cancelled'
                                          ? 'cancelled-status'
                                          : ''
                                }`}
                              >
                                {userDetails?.request_status}
                              </span>
                              On
                              <span className="fw600">
                                {moment(userDetails?.updatedAt).format(
                                  'DD/MM/YYYY hh:mm A'
                                )}
                              </span>
                              By system
                            </Typography>
                          )}
                        </Box>
                      </>
                    )}
                  </Box>
                  <Divider />
                  <Box className="right-side-update-leave-wrap">
                    <LeaveCard userDetails={leaveBalanceDetails} />
                    <Box className="calendar-sec-wrap">
                      <Box className="calendar-sec-wrap">
                        <>
                          <CustomDoubleCalender
                            selectedDate={selectedDate}
                            setSelectedDate={setSelectedDate}
                            events={events}
                            searchValue={searchValue}
                            setSearchValue={setSearchValue}
                            setEvents={setEvents}
                            calenderData={{ calenderData, type: 'own' }}
                            getLeaveDetails={getLeaveDetails}
                            userCanView={true}
                            selectedLeave={true}
                            userDetails={userDetails?.start_date}
                            type="own"
                          />
                          <Divider />
                        </>
                      </Box>
                    </Box>
                  </Box>
                </Box>
              </Box>
            </Form>
          )}
        </Formik>
      </Box>
    </Box>
  );
}
