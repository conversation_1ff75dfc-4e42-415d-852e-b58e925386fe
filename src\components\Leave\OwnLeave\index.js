'use client';
import React, { useEffect, useState } from 'react';
import { Box, Divider, Tooltip, Typography } from '@mui/material';
import CustomButton from '@/components/UI/CustomButton';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import { setApiMessage } from '@/helper/common/commonFunctions';
import CustomFullCalendar from '@/components/UI/Fullcalender';
import MenuIcon from '@mui/icons-material/Menu';
import moment from 'moment';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import AddTwoToneIcon from '@mui/icons-material/AddTwoTone';
import ListView from '@/components/Leave/OwnLeave/ListView';
import { useRouter } from 'next/navigation';
import Searchbar from '@/components/UI/CustomSearch';
import { fetchFromStorage, removeFromStorage } from '@/helper/context';
import { identifiers } from '@/helper/constants/identifier';
import './ownleave.scss';

export default function Ownleave() {
  const [viewMode, setViewMode] = useState('calendar');
  const [calenderSearch, setCalenderSearch] = useState('');
  const [calenderData, setCalenderData] = useState({
    coloredEvents: [],
    holidayEvents: [],
    allEvents: [],
  });
  const [viewData, setViewData] = useState([]);
  let router = useRouter();

  useEffect(() => {
    // getLeaveRemarkList(1, 1);
  }, []);

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      getLeaveDetails(viewData, calenderSearch);
    }
  };
  const getEventColor = (event) => {
    switch (event) {
      case 'pending':
        return '#fef4e6';
      case 'approved':
        return '#f9ffed';
      case 'rejected':
        return '#fce9ea';
      case 'cancelled':
        return '#837f7f';
      default:
        return 'red';
    }
  };
  const getLeaveDetails = async (view, search) => {
    if (!view && !calenderSearch) return;

    try {
      let apiUrl = `${URLS?.CALENDER_WISE_LEAVE}?list_type=own&search=${search || search === '' ? search : calenderSearch}`;

      if (view?.Start && view?.End) {
        apiUrl += `&start_date=${view?.Start}&end_date=${view?.End}`;
      }
      const { status, data } = await axiosInstance.get(apiUrl);

      if (status === 200) {
        const coloredEvents = data?.data?.calenderLeaves?.map((event) => ({
          ...event,
          start: moment.utc(event?.start_date).local().format('YYYY-MM-DD'),
          end: event?.end_date
            ? moment.utc(event?.start_date).local().format('YYYY-MM-DD') ===
              moment.utc(event?.end_date).local().format('YYYY-MM-DD')
              ? moment.utc(event?.end_date).local().format('YYYY-MM-DD')
              : event?.duration_type === 'Hours'
                ? moment.utc(event?.end_date).local().format('YYYY-MM-DD')
                : moment
                    .utc(event?.end_date)
                    .add(1, 'day')
                    .local()
                    .format('YYYY-MM-DD')
            : moment.utc(event?.start_date).local().format('YYYY-MM-DD'),
          allDay: true,
          eventType: 'leave',
        }));

        const holidayEvents = data?.data?.holidayList?.map((event) => ({
          ...event,
          id: `holiday-${event?.id}`,
          start: moment
            .utc(event?.holiday_policy_start_date)
            .format('YYYY-MM-DD'),
          end: moment
            .utc(event?.holiday_policy_end_date)
            .add(1, 'day')
            .format('YYYY-MM-DD'),
          title: event?.holiday_policy_name,
          backgroundColor: event?.holiday_policy_colour,
          borderColor: event?.holiday_policy_colour,
          allDay: true,
          eventType: 'holiday',
        }));

        setCalenderData({
          coloredEvents,
          holidayEvents,
          allEvents: [...holidayEvents, ...coloredEvents],
        });
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const toggleView = (mode) => {
    setViewMode(mode);
  };
  // useEffect(() => {
  //   if (authState?.listViewMode === 'list') {
  //     setViewMode(authState?.listViewMode);
  //   } else {
  //     setViewMode('calendar');
  //   }
  // }, [authState?.listViewMode]);
  useEffect(() => {
    const storedData = fetchFromStorage(identifiers?.RedirectData);
    if (storedData?.viewMode) {
      setViewMode(storedData?.viewMode);
      removeFromStorage(identifiers?.RedirectData);
    } else {
      setViewMode('calendar');
    }
  }, [identifiers]);
  return (
    <>
      <Box>
        <Box className="own-leave-container">
          <Box className="icons-container d-flex align-center justify-space-between pb16">
            <Typography className="title-sm">
              {viewMode === 'calendar' ? 'Calender view' : 'List view'}
            </Typography>
            {viewMode === 'calendar' && (
              <Box className="calender-search-wrap">
                <Searchbar
                  setSearchValue={(e) => {
                    setCalenderSearch(e);
                    getLeaveDetails(viewData, e);
                  }}
                  searchValue={calenderSearch}
                  onKeyPress={handleKeyPress}
                />
              </Box>
            )}
            <Box className={`d-flex align-center gap-sm `}>
              <CustomButton
                variant={viewMode === 'calendar' ? 'contained' : 'outlined'}
                isIconOnly
                startIcon={
                  <Tooltip
                    title={
                      <Typography className="sub-title-text">
                        Calender View
                      </Typography>
                    }
                    classes={{
                      tooltip: 'info-tooltip-container',
                    }}
                    arrow
                  >
                    <CalendarMonthIcon />
                  </Tooltip>
                }
                onClick={() => {
                  toggleView('calendar');
                }}
              />

              <CustomButton
                variant={viewMode === 'list' ? 'contained' : 'outlined'}
                isIconOnly
                startIcon={
                  <Tooltip
                    title={
                      <Typography className="sub-title-text">
                        List View
                      </Typography>
                    }
                    classes={{
                      tooltip: 'info-tooltip-container',
                    }}
                    arrow
                  >
                    <MenuIcon />
                  </Tooltip>
                }
                onClick={() => {
                  toggleView('list');
                }}
              />
              <CustomButton
                variant={'contained'}
                isIconOnly
                startIcon={
                  <Tooltip
                    title={
                      <Typography className="sub-title-text">
                        Apply Leave
                      </Typography>
                    }
                    classes={{
                      tooltip: 'info-tooltip-container',
                    }}
                    arrow
                  >
                    <AddTwoToneIcon />
                  </Tooltip>
                }
                onClick={() => {
                  router.push('./apply-leave');
                }}
              />
            </Box>
          </Box>

          <Divider />
          {viewMode === 'calendar' && (
            <Box className="calendar-sec-wrap calender-view-wrap">
              <CustomFullCalendar
                viewData={viewData}
                setViewData={setViewData}
                getLeaveDetails={getLeaveDetails}
                calenderData={{ calenderData, type: 'own' }}
                getEventColor={getEventColor}
              />
            </Box>
          )}
          {viewMode === 'list' && <ListView />}
        </Box>
      </Box>
    </>
  );
}
