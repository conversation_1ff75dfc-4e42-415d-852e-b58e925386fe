'use client';
import { Box, Typography } from '@mui/material';
import React from 'react';
import { Formik, Form, Field } from 'formik';
import * as Yup from 'yup';
import CustomButton from '@/components/UI/button';
import { CustomTextField } from '@/components/UI/CommonField';
import './verifyphonenumber.scss';

// Validation Schema
const OtpSchema = Yup.object().shape({
  otp: Yup.array()
    .of(
      Yup.string()
        .matches(/^[0-9]{1}$/, 'Must be a single digit')
        .required('Required')
    )
    .min(4, 'OTP must be 4 digits'),
});

export default function VerifyPhoneNumber() {
  return (
    <Formik
      initialValues={{
        otp: ['', '', '', ''],
      }}
      validationSchema={OtpSchema}
      onSubmit={(values) => {
        console.log('Entered OTP:', values.otp.join(''));
      }}
    >
      {({ values, setFieldValue, errors, touched }) => {
        const handleOtpChange = (index, value) => {
          if (/^[0-9]?$/.test(value)) {
            setFieldValue(`otp.${index}`, value);
            if (value && index < 3) {
              document.getElementById(`otp-input-${index + 1}`).focus();
            } else if (!value && index > 0) {
              document.getElementById(`otp-input-${index - 1}`).focus();
            }
          }
        };

        return (
          <Form>
            <Box className="verify-number-wrap">
              <Typography variant="h4" className="heading-wrap">
                OTP Verification
              </Typography>
              <Typography component="p" className="text-wrap">
                Enter the one-time password
              </Typography>
              <Typography component="p" className="phone-text-wrap">
                A one-time password has been sent to your registered mobile
                number.
              </Typography>
              <Typography component="p" className="code-text-wrap">
                Enter the 4-digit code to continue.
              </Typography>

              <Box className="d-flex justify-center gap-5 input-wrap">
                {values?.otp?.map((digit, index) => (
                  <Field
                    key={index}
                    name={`otp.${index}`}
                    render={({ field }) => (
                      <CustomTextField
                        {...field}
                        id={`otp-input-${index}`}
                        value={digit}
                        variant="filled"
                        className="opt-input"
                        placeholder="-"
                        inputProps={{
                          maxLength: 1,
                        }}
                        error={Boolean(
                          touched?.otp &&
                            touched?.otp[index] &&
                            errors?.otp &&
                            errors?.otp[index]
                        )}
                        helperText={
                          touched?.otp &&
                          touched?.otp[index] &&
                          errors?.otp &&
                          errors?.otp[index]
                        }
                        onChange={(e) =>
                          handleOtpChange(index, e?.target?.value)
                        }
                      />
                    )}
                  />
                ))}
              </Box>
              <Typography component="p" className="code-text-wrap">
                Didn't receive a code?{' '}
                <span className="try-again-wrap">Try Again</span>
              </Typography>
              <Box className="pb24 verify-btn-wrap" textAlign="center">
                <CustomButton
                  className="p16 verify-btn"
                  type="submit"
                  fontWeight="600"
                  variant="contained"
                  background="#39596e"
                  backgroundhover="#39596e"
                  colorhover="#FFFFFF"
                  title="Verify"
                />
              </Box>
            </Box>
          </Form>
        );
      }}
    </Formik>
  );
}
