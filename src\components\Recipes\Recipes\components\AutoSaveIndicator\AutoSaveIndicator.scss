// Auto Save Indicator Component Styles
.auto-save-indicator {
  // Desktop version - always visible
  &__desktop {
    display: none;
    align-items: center;
    gap: var(--spacing-sm);

    @media (min-width: 640px) {
      display: flex;
    }
  }

  // Mobile version - temporary indicator
  &__mobile {
    position: fixed;
    top: 80px;
    right: var(--spacing-lg);
    z-index: 150;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease-out;

    @media (min-width: 640px) {
      display: none;
    }

    &--visible {
      transform: translateX(0);
      opacity: 1;
    }
  }

  // Status indicator container
  &__status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--btn-padding-sm);
    border-radius: var(--border-radius-md);
    border: var(--border-width-xs) solid;
    transition: all 0.15s ease-out;
    text-transform: capitalize;
    // Mobile specific styling
    .auto-save-indicator__mobile & {
      padding: var(--spacing-md) var(--spacing-lg);
      box-shadow: var(--box-shadow-xs);
      background-color: var(--color-white);
    }
  }

  // Icon styling
  &__icon {
    transition: all 0.15s ease-out;

    &--spinning {
      animation: spin 1s linear infinite;
    }
  }

  // Text content
  &__text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    transition: color 0.15s ease-out;

    // Mobile specific text styling
    .auto-save-indicator__mobile & {
      font-size: var(--font-size-sm);
    }
  }

  // Timestamp section
  &__timestamp {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-family: var(--font-family-primary);

    &-text {
      font-family: var(--font-family-primary);
      font-size: var(--font-size-xs);
      color: var(--text-color-slate-gray);
    }
  }

  // Mobile content wrapper
  &__mobile-content {
    display: flex;
    flex-direction: column;
    gap: 0;

    .auto-save-indicator__timestamp-text {
      font-size: var(--font-size-xs);
      color: var(--text-color-slate-gray);
    }
  }

  // Status Variants
  &--saving {
    .auto-save-indicator__status {
      background-color: var(--color-muted-mustard-opacity);
      border-color: var(--color-muted-mustard);
    }

    .auto-save-indicator__icon,
    .auto-save-indicator__text {
      color: var(--color-muted-mustard);
    }
  }

  &--saved {
    .auto-save-indicator__status {
      background-color: var(--color-success-opacity);
      border-color: var(--color-success);
    }

    .auto-save-indicator__icon,
    .auto-save-indicator__text {
      color: var(--color-success);
    }
  }

  &--draft {
    .auto-save-indicator__status {
      background-color: var(--color-muted-mustard-opacity);
      border-color: var(--color-muted-mustard);
    }

    .auto-save-indicator__icon,
    .auto-save-indicator__text {
      color: var(--color-muted-mustard);
    }
  }

  &--publish {
    .auto-save-indicator__status {
      background-color: var(--color-primary-opacity);
      border-color: var(--color-primary);
    }

    .auto-save-indicator__icon,
    .auto-save-indicator__text {
      color: var(--color-primary);
    }
  }

  &--unpublished {
    .auto-save-indicator__status {
      background-color: var(--color-danger-opacity);
      border-color: var(--color-danger);
    }

    .auto-save-indicator__icon,
    .auto-save-indicator__text {
      color: var(--color-danger);
    }
  }

  &--archived {
    .auto-save-indicator__status {
      background-color: var(--color-light-success-opacity);
      border-color: var(--color-light-success);
    }

    .auto-save-indicator__icon,
    .auto-save-indicator__text {
      color: var(--color-light-success);
    }
  }

  &--error {
    .auto-save-indicator__status {
      background-color: var(--color-danger-opacity);
      border-color: rgba(211, 47, 47, 0.2);
    }

    .auto-save-indicator__icon,
    .auto-save-indicator__text {
      color: var(--color-danger);
    }
  }
}

// Animations
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
