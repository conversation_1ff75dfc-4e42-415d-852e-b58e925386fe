'use client';
import React, { useState, useEffect } from 'react';
import Select, { components } from 'react-select';
import { Box, FormLabel, Typography, Avatar } from '@mui/material';
import CheckIcon from '@mui/icons-material/Check';
import HeaderImage from '@/components/UI/ImageSecurity';
import './customUserSelect.scss';

const CustomUserSelect = ({
  options = [],
  value,
  onChange,
  placeholder = 'Select user...',
  label,
  error,
  helperText,
  className = '',
  isSearchable = true,
  isClearable = true,
  isDisabled = false,
  required = false,
  name,
  menuPosition = 'absolute',
  menuPortalTarget,
}) => {
  const [preloadedImages, setPreloadedImages] = useState({});

  // Pre-load images when options change
  useEffect(() => {
    const preloadImages = async () => {
      const imagePromises = options
        .filter((option) => option.avatar)
        .map((option) => {
          return new Promise((resolve) => {
            const img = new Image();
            img.onload = () => resolve({ [option.value]: option.avatar });
            img.onerror = () => resolve({ [option.value]: null });
            img.src = option.avatar;
          });
        });

      const results = await Promise.all(imagePromises);
      const newPreloadedImages = results.reduce(
        (acc, result) => ({ ...acc, ...result }),
        {}
      );
      setPreloadedImages(newPreloadedImages);
    };

    if (options.length > 0) {
      preloadImages();
    }
  }, [options]);

  // Pre-load selected value image immediately
  useEffect(() => {
    const preloadSelectedImage = async () => {
      if (value?.avatar && value?.value && !preloadedImages[value.value]) {
        const img = new Image();
        img.onload = () => {
          setPreloadedImages((prev) => ({
            ...prev,
            [value.value]: value.avatar,
          }));
        };
        img.onerror = () => {
          setPreloadedImages((prev) => ({
            ...prev,
            [value.value]: null,
          }));
        };
        img.src = value.avatar;
      }
    };

    if (value?.avatar) {
      preloadSelectedImage();
    }
  }, [value, preloadedImages]);

  // Custom Option Component with Avatar - using preloaded images for dropdown
  const UserOption = (props) => {
    const { data, isSelected } = props;
    const isImagePreloaded = preloadedImages[data.value];

    // Preload image on hover for instant selection experience
    const handleMouseEnter = () => {
      if (data?.avatar && !preloadedImages[data.value]) {
        const img = new Image();
        img.onload = () => {
          setPreloadedImages((prev) => ({
            ...prev,
            [data.value]: data.avatar,
          }));
        };
        img.onerror = () => {
          setPreloadedImages((prev) => ({
            ...prev,
            [data.value]: null,
          }));
        };
        img.src = data.avatar;
      }
    };

    return (
      <components.Option {...props}>
        <Box className="user-option-container" onMouseEnter={handleMouseEnter}>
          <Box className="user-avatar-wrapper">
            {data?.avatar && isImagePreloaded ? (
              <img
                src={data.avatar}
                alt={data?.label}
                className="user-avatar-image"
              />
            ) : (
              <Avatar className="user-avatar-fallback">
                {data?.label?.charAt(0)?.toUpperCase()}
              </Avatar>
            )}
          </Box>
          <Box className="user-info">
            <Typography className="user-name">{data?.label}</Typography>
          </Box>
          {isSelected && (
            <Box className="selected-icon">
              <CheckIcon />
            </Box>
          )}
        </Box>
      </components.Option>
    );
  };

  // Custom SingleValue Component with Avatar - only show image when preloaded
  const UserSingleValue = (props) => {
    const { data } = props;
    const isImagePreloaded = preloadedImages[data?.value];

    return (
      <components.SingleValue {...props}>
        <Box className="user-single-value-container">
          <Box className="user-avatar-wrapper">
            {data?.avatar && isImagePreloaded ? (
              <HeaderImage
                imageUrl={data?.avatar}
                alt={data?.label}
                className="user-avatar-image"
                type="avtar"
                IsExternal={false}
              />
            ) : (
              <Avatar className="user-avatar-fallback">
                {data?.label?.charAt(0)?.toUpperCase()}
              </Avatar>
            )}
          </Box>
          <Typography className="user-name">{data?.label}</Typography>
        </Box>
      </components.SingleValue>
    );
  };

  return (
    <Box className={`custom-user-select-wrapper ${className}`}>
      {label && (
        <FormLabel className={`field-label ${error ? 'error-label' : ''}`}>
          {label}
          {required && <span className="required"> *</span>}
        </FormLabel>
      )}

      <Select
        options={options}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        isSearchable={isSearchable}
        isClearable={isClearable}
        isDisabled={isDisabled}
        name={name}
        aria-labelledby={name}
        className={`user-select-container ${error ? 'error-border' : ''}`}
        classNamePrefix="user-select"
        menuPosition={menuPosition}
        menuPortalTarget={menuPortalTarget}
        isAnimated={false}
        components={{
          Option: UserOption,
          SingleValue: UserSingleValue,
        }}
      />

      {error && helperText && (
        <Typography className="select-field-error-text">
          {helperText}
        </Typography>
      )}
    </Box>
  );
};

export default CustomUserSelect;
