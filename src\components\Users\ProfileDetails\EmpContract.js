'use client';

import React, { useEffect, useState, useContext } from 'react';
import { Box, Typography, Divider, Tooltip } from '@mui/material';
import AuthContext from '@/helper/authcontext';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomButton from '@/components/UI/CustomButton';
import CustomSelect from '@/components/UI/CustomSelect';
import CustomDatePicker from '@/components/UI/CustomDatePicker';
import CustomRadio from '@/components/UI/CustomRadio';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import dayjs from 'dayjs';
import { setApiMessage } from '@/helper/common/commonFunctions';
import CustomCreateSelect from '@/components/UI/CustomCreateSelect';
import MultiselectTitles from '@/components/UI/MultiselectTitles';
import { identifiers } from '@/helper/constants/identifier';
import { ListManager } from 'react-beautiful-dnd-grid';
import CustomEditor from '@/components/UI/CustomEditor';
import CloseIcon from '@mui/icons-material/Close';
import DriveFileRenameOutlineIcon from '@mui/icons-material/DriveFileRenameOutline';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import moment from 'moment';

const EmpContract = ({
  UserDetails,
  UserId,
  formikRef,
  generalTemplateData,
  ViewAccessOnly,
  contractTypeData,
  leaveTypeList,
  departmentTemByID,
  departmentTemplateDataByID,
  departmentTemplateData,
  loader,
  onDragEnd,
  isLoader,
  isLoaderEmp,
  ContractList,
  setContractList,
  regenerateEmp,
  setIsLoader,
  handleCreateOptionContractPolicy,
  setDepartmentTemplateData,
  leaveTypeListall,
  setLeaveTypeList,
  isContractRenewal,
  handleCloseDialog,
}) => {
  const { authState } = useContext(AuthContext);
  const firstId =
    UserDetails &&
    UserDetails?.user_meta &&
    UserDetails?.user_meta?.department_template
      ? UserDetails?.user_meta?.department_template
      : null;
  const [wageType, setWageType] = useState('fixed');
  const [holidayChanged, setHolidayChanged] = useState(false);
  const [AddContract, setAddContract] = useState();
  const [editioContent, setEditorContent] = useState('');
  const getValue = (path, defaultValue = '') => {
    return path ?? defaultValue;
  };

  const getFormattedDate = (date) => {
    return date ? dayjs(date).format('YYYY-MM-DD') : null;
  };
  const safeAppend = (key, value, body, condition = true) => {
    if (condition && value !== undefined && value !== null) {
      body.append(key, value);
    }
  };
  const validationContractSchema = Yup.object().shape({
    contractName: Yup.string().trim().required('This field is required'),
    duration_type: Yup.string().trim().required('This field is required'),
    working_hours: Yup.number()
      .required('This field is required')
      .typeError('Must be a number'),
    wage_type: Yup.string().trim().required('This field is required'),
    wage_per_hour: Yup.number()
      .required('This field is required')
      .typeError('Must be a number'),
    wage_amount_type:
      wageType === 'fixed' &&
      Yup.string().trim().required('This field is required'),
    days: Yup.lazy((value, context) => {
      const has_holiday_entitlement = context.parent.has_holiday_entitlement;
      return has_holiday_entitlement
        ? Yup.number().nullable().required('This field is required')
        : Yup.number().nullable().notRequired();
    }),
    leaveRemark:
      holidayChanged && Yup.string().trim().required('This field is required'),
    expire_date: Yup.lazy((value, context) => {
      const expire_duration = context.parent.expire_duration;
      return expire_duration === 'custom'
        ? Yup.string().nullable().required('This field is required')
        : Yup.string().nullable().notRequired();
    }),
    general_template: Yup.string().trim().required('This field is required'),
    department_template: Yup.string().trim().required('This field is required'),
    probationperiod: Yup.number()
      .nullable()
      .test(
        'probationperiod-less-than-expiry-diff',
        'Probation period must be less than the difference between expiry date and today.',
        function (value) {
          const { expire_duration, expire_date } = this.parent;
          let expiry;

          if (expire_duration === 'custom' && expire_date) {
            expiry = dayjs(expire_date);
          } else if (expire_duration) {
            switch (expire_duration) {
              case '2_month':
                expiry = dayjs().add(2, 'month');
                break;
              case '4_month':
                expiry = dayjs().add(4, 'month');
                break;
              case '6_month':
                expiry = dayjs().add(6, 'month');
                break;
              case '1_year':
                expiry = dayjs().add(1, 'year');
                break;
              default:
                expiry = null;
            }
          }

          if (!expiry || !value) return true; // skip if not enough info
          const today = dayjs().startOf('day');
          const diff = expiry.diff(today, 'day'); // works now
          return value <= diff;
        }
      ),
    // expire_duration: Yup.string().trim().required('This field is required'),
    // working_hours_per_day: Yup.number()
    //   .typeError('Working hours per Day must be a number')
    //   .required('This field is required'),
    // max_limit_per_week: Yup.number()
    //   .typeError('Max Limit per Week must be a number')
    //   .required('This field is required'),
    //  Yup.number()
    //   .required('This field is required')
    //   .typeError('Must be a number'),
    // holidayEntitlement: Yup.string().trim().required('This field is required'),
  });

  const calculateLeave = (value, type) => {
    //, max_limit_per_week
    if (value) {
      const weeklyHours = type === 'month' ? value / 4.33 : value;
      const applicableWeeklyHours = Math.min(
        weeklyHours,
        parseFloat(authState?.max_limit_per_week)
      );
      const totalLeaveHours =
        applicableWeeklyHours * parseFloat(authState?.base_leave);

      // Calculate leave in days, using 7 as divisor or returning total leave hours if 7 is zero
      const leaveInDays = Math.ceil(totalLeaveHours / 7) || totalLeaveHours;

      formikRef.current.setFieldValue(
        'days',
        !Number.isNaN(Number(leaveInDays)) && !isNaN(parseFloat(leaveInDays))
          ? leaveInDays
          : ''
      );
    } else {
      formikRef.current.setFieldValue('days', '');
    }
  };
  useEffect(() => {
    if (UserDetails && UserDetails?.user_meta?.wage_type) {
      setWageType(UserDetails?.user_meta?.wage_type);
    }
  }, [UserDetails && UserDetails?.user_meta?.wage_type]);
  useEffect(() => {
    if (UserDetails && UserDetails?.user_meta?.other) {
      setEditorContent(UserDetails?.user_meta?.other);
    }
  }, [UserDetails && UserDetails?.user_meta?.other]);
  const scrollToError = (errors) => {
    const firstErrorField = Object.keys(errors)[0];

    // Try to find the element by ID first
    let element = document.getElementById(firstErrorField);

    // If not found by ID, try other selectors
    if (!element) {
      element = document.querySelector(`[name="${firstErrorField}"]`);
    }

    // If still not found or it's a hidden input, try to find the parent container
    if (!element || element.type === 'hidden') {
      const selectBox = document.querySelector(
        `.select-box[data-field="${firstErrorField}"]`
      );
      const errorField = document.querySelector(
        `.textfeild-error[data-field="${firstErrorField}"]`
      );
      const customField = document.querySelector(
        `[data-field="${firstErrorField}"]`
      );

      element = selectBox || errorField || customField;
    }
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'center' });
      // For MultiSelect, focus the select input if it exists
      const selectInput = element.querySelector('input') || element;
      selectInput.focus();
    }
  };
  return (
    <>
      <Formik
        innerRef={formikRef}
        initialValues={{
          submitType: '',
          general_template: getValue(
            UserDetails?.user_meta?.general_template ??
              generalTemplateData?.[0]?.value
          ),
          department_template:
            firstId && UserDetails?.user_meta?.department_template
              ? firstId
              : '',
          expire_date: getFormattedDate(UserDetails?.user_meta?.expire_date),
          expire_duration: getValue(UserDetails?.user_meta?.expire_duration),
          tipsgrad: getValue(UserDetails?.user_meta?.tips_grade),
          probationperiod: getValue(UserDetails?.user_meta?.probation_length),
          has_holiday_entitlement:
            UserDetails?.user_meta?.has_holiday_entitlement === 0 ||
            UserDetails?.user_meta?.has_holiday_entitlement === false
              ? false
              : true,
          contractName: getValue(UserDetails?.user_meta?.contract_name_id),
          duration_type: getValue(
            UserDetails?.user_meta?.duration_type,
            'week'
          ),
          working_hours: getValue(UserDetails?.user_meta?.working_hours),
          wage_type: getValue(UserDetails?.user_meta?.wage_type, 'fixed'),
          wage_amount_type: getValue(UserDetails?.user_meta?.fixed_types),
          wage_per_hour: getValue(UserDetails?.user_meta?.wages_hours),
          contractRemark: getValue(UserDetails?.user_meta?.contract_remark),
          days: getValue(UserDetails?.user_meta?.leave_days),
          place_of_work: getValue(UserDetails?.user_meta?.place_of_work),
          leaveRemark: getValue(UserDetails?.user_meta?.leave_remark),
          policyholiday: [],
        }}
        enableReinitialize={true}
        validationSchema={validationContractSchema}
        onSubmit={async (requestData) => {
          setIsLoader(true);
          const body = new FormData();

          const calculateExpiryDate = (startDate, duration) => {
            const date = moment(startDate);

            switch (duration) {
              case '2_month':
                return date.add(2, 'months').format('YYYY-MM-DD');
              case '4_month':
                return date.add(4, 'months').format('YYYY-MM-DD');
              case '6_month':
                return date.add(6, 'months').format('YYYY-MM-DD');
              case '1_year':
                return date.add(1, 'year').format('YYYY-MM-DD');
              case 'custom':
                return null;
              default:
                return date.format('YYYY-MM-DD');
            }
          };
          const getExpDate =
            requestData?.expire_duration === 'custom'
              ? requestData?.expire_date
              : requestData?.expire_duration
                ? calculateExpiryDate(
                    UserDetails?.user_meta?.expire_date
                      ? moment(UserDetails?.user_meta?.expire_date).format(
                          'YYYY-MM-DD'
                        )
                      : moment(UserDetails?.user_joining_date).format(
                          'YYYY-MM-DD'
                        ),
                    requestData?.expire_duration
                  )
                : null;

          safeAppend(
            'general_template',
            requestData?.general_template || '',
            body
          );
          safeAppend(
            'department_template',
            requestData?.department_template,
            body
          );

          const selectedContractIds = ContractList?.map((d) => d?.id);
          if (selectedContractIds?.length > 0) {
            safeAppend('additional_template', selectedContractIds, body);
          }

          safeAppend('place_of_work', requestData?.place_of_work || '', body);
          safeAppend('contract_name_id', requestData?.contractName || '', body);
          safeAppend('duration_type', requestData?.duration_type || '', body);
          safeAppend('working_hours', requestData?.working_hours, body);
          safeAppend(
            'expire_duration',
            requestData?.expire_duration || '',
            body
          );
          safeAppend('wages_hours', requestData?.wage_per_hour || '', body);
          safeAppend(
            'contract_remark',
            requestData?.contractRemark || '',
            body
          );

          if (requestData?.has_holiday_entitlement) {
            safeAppend('leave_days', requestData?.days || '', body);
          } else {
            safeAppend('leave_days', 0, body);
          }

          safeAppend(
            'has_holiday_entitlement',
            requestData?.has_holiday_entitlement,
            body
          );

          if (
            requestData?.wage_type === 'fixed' &&
            requestData?.wage_amount_type
          ) {
            safeAppend('fixed_types', requestData?.wage_amount_type, body);
          } else {
            safeAppend('fixed_types', '', body);
          }

          safeAppend('wage_type', requestData?.wage_type || '', body);
          safeAppend('leave_remark', requestData?.leaveRemark || '', body);
          safeAppend('tips_grade', requestData?.tipsgrad || '', body);
          safeAppend(
            'probation_length',
            requestData?.probationperiod || '',
            body
          );

          if (getExpDate) {
            safeAppend(
              'expire_date',
              dayjs(getExpDate).format('YYYY-MM-DD'),
              body
            );
          }

          const selectedPolicyIds =
            requestData?.policyholiday &&
            requestData?.policyholiday?.length > 0 &&
            requestData?.policyholiday?.map((d) => d?.id);
          if (selectedPolicyIds?.length > 0) {
            safeAppend('leave_policy_ids', selectedPolicyIds, body);
          }

          safeAppend('other', editioContent, body);
          safeAppend('user_id', UserId, body);

          // const config = {
          //   headers: {
          //     'Content-Type': 'multipart/form-data',
          //   },
          // };
          const ApiUrl = URLS.UPDATE_USER_CONTRACT;
          const isConfig = '';

          try {
            const { status, data } = await axiosInstance.put(
              ApiUrl,
              body,
              isConfig
            );

            if (status === 200 || status === 201) {
              if (data?.status) {
                setApiMessage('success', data?.message);
                if (requestData?.submitType === 'regenerate') {
                  isContractRenewal
                    ? regenerateEmp(UserDetails?.id, 'regenerate')
                    : regenerateEmp();
                }
              } else {
                setApiMessage('error', data?.message);
              }
              setIsLoader(false);
            }
          } catch (error) {
            setIsLoader(false);
            setApiMessage('error', error?.response?.data?.message);
          }
        }}
      >
        {({
          errors,
          touched,
          handleBlur,
          values,
          setFieldValue,
          handleSubmit,
          handleChange,
        }) => (
          <Form>
            <Box className={`${!isContractRenewal && 'pt24'}`}>
              {!isContractRenewal && (
                <>
                  <Divider className="mb8" />{' '}
                  <Box className="d-flex justify-start align-center pt8">
                    <Typography className="title-sm pr8">
                      Employee Contract
                    </Typography>
                    {UserDetails?.user_meta?.isDraft === 1 && (
                      <Typography className="body-sm status-yellow pl8 h100">
                        Draft
                      </Typography>
                    )}
                  </Box>
                </>
              )}

              <Box
                alignItems="center"
                className={`display-grid  ${!isContractRenewal && 'pt16'}`}
              >
                <Box>
                  <CustomCreateSelect
                    id="contractName"
                    name="contractName"
                    value={
                      contractTypeData?.find(
                        (option) => option?.value === values?.contractName
                      ) || null
                    }
                    options={contractTypeData}
                    onChange={(inputValue) => {
                      setFieldValue('contractName', inputValue?.value || '');
                    }}
                    onCreateOption={(inputValue) => {
                      handleCreateOptionContractPolicy(inputValue);
                    }}
                    placeholder="Contract Type"
                    onBlur={handleBlur}
                    label="Contract Type"
                    error={Boolean(touched.contractName && errors.contractName)}
                    helperText={touched.contractName && errors.contractName}
                    isDisabled={ViewAccessOnly}
                    required
                  />
                </Box>
                <Box className="">
                  <CustomSelect
                    placeholder="Duration Type"
                    options={identifiers?.DURATION_TYPE}
                    value={
                      identifiers?.DURATION_TYPE?.find((opt) => {
                        return opt?.value === values?.duration_type;
                      }) || ''
                    }
                    name="duration_type"
                    error={touched?.duration_type && errors?.duration_type}
                    helperText={touched?.duration_type && errors?.duration_type}
                    onChange={(selectedOption) => {
                      setFieldValue(
                        'duration_type',
                        selectedOption?.value || ''
                      );
                      calculateLeave(
                        values?.working_hours,
                        selectedOption?.value
                        // values?.max_limit_per_week
                      );
                    }}
                    label={<span>Duration Type</span>}
                    required
                    isDisabled={ViewAccessOnly}
                  />
                </Box>
                <Box>
                  <CustomTextField
                    fullWidth
                    id="working_hours"
                    name="working_hours"
                    value={values?.working_hours}
                    label={<span>Duration Working Hours</span>}
                    required
                    placeholder="Duration Working Hours"
                    error={Boolean(
                      touched.working_hours && errors.working_hours
                    )}
                    helperText={touched.working_hours && errors.working_hours}
                    onBlur={handleBlur}
                    onChange={(e) => {
                      handleChange(e);
                      calculateLeave(
                        e?.target?.value,
                        values?.duration_type
                        // values?.max_limit_per_week
                      );
                    }}
                    onInput={(e) => {
                      e.target.value = e.target.value.replace(/[^0-9.]/g, '');
                    }}
                    disabled={ViewAccessOnly}
                  />
                </Box>{' '}
                <Box>
                  <CustomSelect
                    placeholder="Wage Type"
                    options={identifiers?.WAGE_TYPE}
                    value={
                      identifiers?.WAGE_TYPE?.find((opt) => {
                        return opt?.value === values.wage_type;
                      }) || ''
                    }
                    name="wage_type"
                    isDisabled={ViewAccessOnly}
                    onChange={(selectedOption) => {
                      const selectedValue = selectedOption?.value;
                      setFieldValue('wage_type', selectedValue || '');
                      setWageType(selectedValue);
                    }}
                    error={touched?.wage_type && errors?.wage_type}
                    helperText={touched?.wage_type && errors?.wage_type}
                    required
                    label={<span>Wage Type</span>}
                  />
                </Box>
                <Box>
                  <CustomTextField
                    fullWidth
                    id="wage_per_hour"
                    name="wage_per_hour"
                    value={values?.wage_per_hour}
                    label={
                      values?.wage_type === 'fixed'
                        ? 'Fixed amount'
                        : 'Wage Per Hours'
                    }
                    placeholder={
                      values?.wage_type === 'fixed'
                        ? 'Fixed amount'
                        : 'Wage Per Hours'
                    }
                    required
                    error={Boolean(
                      touched.wage_per_hour && errors.wage_per_hour
                    )}
                    helperText={touched.wage_per_hour && errors.wage_per_hour}
                    onBlur={handleBlur}
                    onChange={(e) => {
                      handleChange(e);
                    }}
                    disabled={ViewAccessOnly}
                    onInput={(e) => {
                      e.target.value = e.target.value.replace(/[^0-9.]/g, '');
                    }}
                  />
                </Box>
                {values?.wage_type === 'fixed' && (
                  <Box>
                    <CustomSelect
                      placeholder="Amount per"
                      options={identifiers?.WAGE_AMOUNT_TYPE}
                      value={
                        identifiers?.WAGE_AMOUNT_TYPE?.find((opt) => {
                          return opt?.value === values?.wage_amount_type;
                        }) || ''
                      }
                      name="wage_amount_type"
                      onChange={(selectedOption) => {
                        setFieldValue(
                          'wage_amount_type',
                          selectedOption?.value || ''
                        );
                      }}
                      label={<span>Amount per</span>}
                      required
                      isDisabled={ViewAccessOnly}
                      error={
                        touched?.wage_amount_type && errors?.wage_amount_type
                      }
                      helperText={
                        touched?.wage_amount_type && errors?.wage_amount_type
                      }
                    />
                  </Box>
                )}
              </Box>

              <Box className="display-grid-50 space-grid">
                <CustomTextField
                  fullWidth
                  id="contractRemark"
                  name="contractRemark"
                  value={values?.contractRemark}
                  label="Contract Remark"
                  placeholder="Contract Remark"
                  onBlur={handleBlur}
                  onChange={handleChange}
                  disabled={ViewAccessOnly}
                />
              </Box>
              <Box className="mt16">
                <Typography className="sub-content-text">
                  Do you want assign holiday entitlement?
                </Typography>
                <Box>
                  <CustomRadio
                    name="considerLeaveAccrual"
                    value={true}
                    disabled={ViewAccessOnly}
                    checked={values?.has_holiday_entitlement}
                    onChange={() =>
                      setFieldValue('has_holiday_entitlement', true)
                    }
                    disableRipple
                    label={
                      <Typography className="sub-title-text">Yes</Typography>
                    }
                  />
                </Box>
                <Box>
                  <CustomRadio
                    name="considerLeaveAccrual"
                    value={false}
                    checked={!values?.has_holiday_entitlement}
                    onChange={() =>
                      setFieldValue('has_holiday_entitlement', false)
                    }
                    disabled={ViewAccessOnly}
                    disableRipple
                    label={
                      <Typography className="sub-title-text">No</Typography>
                    }
                  />
                </Box>
              </Box>
              <Box className="display-grid space-grid">
                {values?.has_holiday_entitlement ? (
                  <Box>
                    <CustomTextField
                      fullWidth
                      id="days"
                      name="days"
                      required
                      value={values?.days}
                      label={<span>Holiday Entitlement Days </span>}
                      labelIcon={
                        <Tooltip
                          title="Change"
                          placement="right"
                          classes={{
                            tooltip: 'info-tooltip-container ',
                          }}
                          arrow
                        >
                          <DriveFileRenameOutlineIcon
                            sx={{ marginLeft: '8px' }}
                            className="label-icon"
                            onClick={() => {
                              setHolidayChanged(true);
                            }}
                          />
                        </Tooltip>
                      }
                      placeholder="Holiday Entitlement Days"
                      error={Boolean(touched.days && errors.days)}
                      helperText={touched.days && errors.days}
                      onBlur={handleBlur}
                      onChange={(e) => {
                        handleChange(e);
                      }}
                      disabled={ViewAccessOnly || !holidayChanged}
                      onInput={(e) => {
                        e.target.value = e.target.value.replace(/[^0-9.]/g, ''); // Restrict input to numbers only
                      }}
                    />
                  </Box>
                ) : (
                  <></>
                )}

                <Box>
                  <CustomTextField
                    fullWidth
                    id="place_of_work"
                    name="place_of_work"
                    value={values.place_of_work}
                    label="Place of work" // Ensure correct type usage
                    placeholder="Place of work"
                    error={Boolean(
                      touched.place_of_work && errors.place_of_work
                    )}
                    helperText={touched.place_of_work && errors.place_of_work}
                    onBlur={handleBlur}
                    onChange={handleChange}
                    disabled={ViewAccessOnly}
                  />
                </Box>
              </Box>
              <Box className="display-grid-50 space-grid">
                <Box>
                  {' '}
                  <CustomTextField
                    fullWidth
                    id="leaveRemark"
                    name="leaveRemark"
                    value={values?.leaveRemark}
                    label={'Leave Policy Remark'}
                    required={holidayChanged}
                    placeholder="Leave Policy Remark"
                    onBlur={handleBlur}
                    onChange={handleChange}
                    disabled={ViewAccessOnly}
                    error={Boolean(touched.leaveRemark && errors.leaveRemark)}
                    helperText={touched.leaveRemark && errors.leaveRemark}
                  />
                </Box>
                <Box>
                  <MultiselectTitles
                    placeholder="Assign policy"
                    className={ViewAccessOnly ? 'disabled-multifield' : ''}
                    options={leaveTypeList}
                    value={values?.policyholiday}
                    error={touched.policyholiday && errors.policyholiday}
                    helperText={touched.policyholiday && errors.policyholiday}
                    onChange={(e) => {
                      setFieldValue('policyholiday', e);
                      if (e && e?.length > 0) {
                        const parentIds = e?.map((policy) => policy.parent_id);
                        const filteredLeaveTypes = leaveTypeListall?.filter(
                          (type) => !parentIds.includes(type?.id)
                        );
                        setLeaveTypeList(filteredLeaveTypes);
                      } else {
                        setLeaveTypeList(leaveTypeListall);
                      }
                    }}
                    name="policyholiday"
                    disabled={ViewAccessOnly}
                    label={<span>Assign policy</span>}
                  />
                </Box>
              </Box>

              <Box className="display-grid space-grid">
                <Box>
                  <CustomTextField
                    fullWidth
                    id="tipsgrad"
                    name="tipsgrad"
                    value={values?.tipsgrad}
                    label={'TipsGrade'}
                    placeholder={'TipsGrade'}
                    disabled={ViewAccessOnly}
                    // error={Boolean(touched.tipsgrad && errors.tipsgrad)}
                    // helperText={touched.tipsgrad && errors.tipsgrad}
                    onBlur={handleBlur}
                    // onChange={handleChange}
                    onChange={(e) => {
                      if (
                        e?.target.value === '' ||
                        e?.target.value?.length < 3
                      ) {
                        handleChange(e);
                      }
                    }}
                  />
                </Box>
                <Box>
                  <CustomSelect
                    placeholder="Expiry Date"
                    options={identifiers?.EXPIRY_DATE_DURATION}
                    value={
                      identifiers?.EXPIRY_DATE_DURATION?.find((opt) => {
                        return opt?.value === values?.expire_duration;
                      }) || ''
                    }
                    isDisabled={ViewAccessOnly}
                    name="expire_duration"
                    error={touched?.expire_duration && errors?.expire_duration}
                    helperText={
                      touched?.expire_duration && errors?.expire_duration
                    }
                    onChange={(e) => {
                      setFieldValue('expire_duration', e?.value || '');
                      setFieldValue('expire_date', null);
                    }}
                    label={<span>Expiry Date </span>}
                  />
                </Box>
                {values?.expire_duration === 'custom' && (
                  <Box>
                    <CustomDatePicker
                      label={<span>Select Expiry Date</span>}
                      name="expire_date"
                      value={dayjs(values?.expire_date)}
                      error={Boolean(touched.expire_date && errors.expire_date)}
                      helperText={touched.expire_date && errors.expire_date}
                      onBlur={handleBlur}
                      onChange={(date) => {
                        setFieldValue('expire_date', date);
                      }}
                      inputVariant="outlined"
                      minDate={dayjs(UserDetails?.user_joining_date)}
                      required
                      format="DD/MM/YYYY"
                    />
                  </Box>
                )}
                <Box>
                  <CustomTextField
                    fullWidth
                    id="probationperiod"
                    name="probationperiod"
                    value={values?.probationperiod}
                    label="Probation Period ( In Days )"
                    placeholder="90 Days"
                    error={Boolean(
                      touched.probationperiod && errors.probationperiod
                    )}
                    helperText={
                      touched.probationperiod && errors.probationperiod
                    }
                    onBlur={handleBlur}
                    onChange={handleChange}
                    onInput={(e) => {
                      e.target.value = e.target.value.replace(/[^0-9.]/g, '');
                    }}
                    disabled={ViewAccessOnly}
                  />
                </Box>
                <Box>
                  <CustomSelect
                    placeholder="General"
                    options={generalTemplateData}
                    value={
                      generalTemplateData?.find((opt) => {
                        return opt?.value === values?.general_template;
                      }) || ''
                    }
                    name="general_template"
                    error={
                      touched?.general_template && errors?.general_template
                    }
                    helperText={
                      touched?.general_template && errors?.general_template
                    }
                    onChange={(e) => {
                      setFieldValue('general_template', e?.value || '');
                    }}
                    label={<span>General</span>}
                    required
                    isDisabled={ViewAccessOnly}
                  />
                </Box>
                <Box>
                  <CustomSelect
                    placeholder="Job Role"
                    options={departmentTemplateDataByID}
                    value={
                      departmentTemplateDataByID?.find((opt) => {
                        return opt?.value === values?.department_template;
                      }) || ''
                    }
                    name="department_template"
                    error={
                      touched?.department_template &&
                      errors?.department_template
                    }
                    helperText={
                      touched?.department_template &&
                      errors?.department_template
                    }
                    onChange={(e) => {
                      setFieldValue('department_template', e?.value || '');
                      const filterCat = departmentTemByID?.filter(
                        (f) => f?.id !== e?.value
                      );
                      setDepartmentTemplateData(filterCat);
                      const selectedCat = ContractList?.filter(
                        (fi) => fi?.id !== e?.value
                      );
                      setContractList(selectedCat);
                    }}
                    label={<span>Job Role</span>}
                    required
                    isDisabled={ViewAccessOnly}
                  />
                </Box>
              </Box>
              <Box className="display-grid space-grid align-end">
                <Box>
                  <CustomSelect
                    placeholder="Additional Duties"
                    options={departmentTemplateData}
                    value={
                      departmentTemplateData?.find((opt) => {
                        return opt?.value === AddContract;
                      }) || ''
                    }
                    name="additional_duties"
                    onChange={(e) => {
                      !ViewAccessOnly && setAddContract(e?.value);
                    }}
                    label={<span>Additional Duties</span>}
                    isDisabled={ViewAccessOnly}
                  />
                </Box>
                <Box>
                  <CustomButton
                    variant="contained"
                    title="Add"
                    disabled={loader || !AddContract || ViewAccessOnly}
                    onClick={() => {
                      const clist = ContractList;
                      const selectedCat = departmentTemplateData?.find(
                        (f) => f?.id === AddContract
                      );
                      clist.push(selectedCat);
                      setContractList(clist);
                      const filterCat = departmentTemplateData?.filter(
                        (f) => f?.id !== AddContract
                      );
                      setDepartmentTemplateData(filterCat);
                      setAddContract();
                    }}
                  />
                </Box>
              </Box>

              <Box className="employee-contract-section">
                <ListManager
                  items={ContractList || []}
                  direction="horizontal"
                  maxItems={1}
                  render={(item) => (
                    <Box key={item?.id} className="selected-files">
                      <Box className="file-name">
                        <Typography className="title-text text-ellipsis-line">
                          {item?.name}
                        </Typography>
                      </Box>
                      <CloseIcon
                        onClick={() => {
                          const files = departmentTemplateData;
                          files?.push(item);

                          setDepartmentTemplateData(files);
                          const selectedCat = ContractList?.filter(
                            (fi) => fi?.id !== item?.id
                          );
                          setContractList(selectedCat);
                        }}
                      />
                    </Box>
                  )}
                  onDragEnd={onDragEnd}
                />
              </Box>

              <Box className="pt16">
                <Typography className="field-label">Content</Typography>
                <CustomEditor
                  content={editioContent}
                  setContent={setEditorContent}
                  height={300}
                />
              </Box>

              <Box
                className={
                  isContractRenewal
                    ? 'form-actions-btn'
                    : 'create-cancel-button'
                }
              >
                {isContractRenewal ? (
                  <CustomButton
                    variant="outlined"
                    // type="submit"
                    title="Cancel"
                    fullWidth={false}
                    disabled={ViewAccessOnly || isLoader}
                    onClick={async () => {
                      handleCloseDialog();
                    }}
                  />
                ) : (
                  <CustomButton
                    variant="outlined"
                    // type="submit"
                    title="Save As Draft"
                    fullWidth={false}
                    disabled={ViewAccessOnly || isLoader}
                    onClick={async () => {
                      setTimeout(() => {
                        handleSubmit();
                      }, [100]);
                      if (formikRef?.current) {
                        const errors = await formikRef.current.validateForm();
                        const allFields = Object.keys(errors);
                        const touchedFields = allFields?.reduce(
                          (acc, field) => {
                            acc[field] = true;
                            return acc;
                          },
                          {}
                        );
                        formikRef.current.setTouched(touchedFields);
                        if (Object.keys(errors).length > 0) {
                          scrollToError(errors);
                          formikRef.current.setSubmitting(false);
                        }
                      }
                    }}
                  />
                )}
                {!(
                  (UserDetails?.web_user_active_role_id === 1 ||
                    UserDetails?.web_user_active_role_id === 2) &&
                  UserDetails?.user_roles?.length === 1
                ) &&
                  UserDetails?.user_status !== 'pending' && (
                    <CustomButton
                      variant="contained"
                      onClick={async () => {
                        setFieldValue('submitType', 'regenerate');
                        setTimeout(() => {
                          handleSubmit();
                        }, [100]);
                        if (formikRef?.current) {
                          const errors = await formikRef.current.validateForm();
                          const allFields = Object.keys(errors);
                          const touchedFields = allFields?.reduce(
                            (acc, field) => {
                              acc[field] = true;
                              return acc;
                            },
                            {}
                          );
                          formikRef.current.setTouched(touchedFields);
                          if (Object.keys(errors).length > 0) {
                            scrollToError(errors);
                            formikRef.current.setSubmitting(false);
                          }
                        }
                      }}
                      title="Save and Regenerate Contract" // employment contract
                      fullWidth={false}
                      disabled={ViewAccessOnly || isLoaderEmp || isLoader}
                    />
                  )}
              </Box>
            </Box>
          </Form>
        )}
      </Formik>
    </>
  );
};

export default EmpContract;
