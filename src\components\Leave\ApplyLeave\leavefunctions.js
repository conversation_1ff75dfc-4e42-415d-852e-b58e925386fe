import moment from 'moment';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import localizedFormat from 'dayjs/plugin/localizedFormat';

dayjs.extend(utc);
dayjs.extend(localizedFormat);

// check weekends from date
export const checkWeekends = (date, daysOfWeek) => {
  const dayName = dayjs(date).format('dddd');
  const isWeekend = daysOfWeek?.find((d) => d.name === dayName)?.dayOff;
  return isWeekend;
};
// check holiday from dates
export const checkHoliday = (date, userHolidayList) => {
  const inputDate = new Date(date);

  return userHolidayList?.some((holiday) => {
    const startDate = new Date(holiday?.holiday_policy_start_date);
    const endDate = new Date(holiday?.holiday_policy_end_date);

    return inputDate >= startDate && inputDate <= endDate;
  });
};
// const getWeekends = (dateRanges) => {
//   const filterWeekends = dateRanges?.filter((date) => {
//     return checkWeekends(date);
//   });
//   return filterWeekends;
// };
// const getHolidays = (dateRanges) => {
//   const filterWeekends = dateRanges?.filter((date) => {
//     return checkHoliday(date);
//   });
//   return filterWeekends;
// };
// Holiday and Weekends
export const getHolidaysWeekends = (
  dateRanges,
  daysOfWeek,
  userHolidayList
) => {
  const filterWeekends = dateRanges?.filter((date) => {
    return (
      checkWeekends(date, daysOfWeek) || checkHoliday(date, userHolidayList)
    );
  });
  return filterWeekends;
};
export const getDatesInRange = (start, end) => {
  let dates = [];
  let currentDate = dayjs(start);

  while (currentDate.isBefore(dayjs(end)) || currentDate.isSame(dayjs(end))) {
    dates.push(currentDate.format('YYYY-MM-DD')); // Store as string
    currentDate = currentDate.add(1, 'day');
  }
  return dates;
};
export const applySandwichRule = (
  leaveDates, // array of leave date strings ['2025-05-01', '2025-05-02']
  holidayDates, // array of holiday date strings
  sandwichBeforeDays = 0,
  sandwichAfterDays = 0,
  applyBefore = false,
  applyAfter = false,
  applyBetween = false
) => {
  const allLeaveDates = new Set(leaveDates);

  const holidaySet = new Set(holidayDates);

  // Check before the leave range
  if (applyBefore && sandwichBeforeDays > 0) {
    const firstLeave = dayjs(leaveDates[0]);
    for (let i = 1; i <= sandwichBeforeDays; i++) {
      const prevDate = firstLeave.subtract(i, 'day').format('YYYY-MM-DD');
      if (holidaySet.has(prevDate)) {
        allLeaveDates.add(prevDate);
      }
    }
  }

  // Check after the leave range
  if (applyAfter && sandwichAfterDays > 0) {
    const lastLeave = dayjs(leaveDates[leaveDates.length - 1]);
    for (let i = 1; i <= sandwichAfterDays; i++) {
      const nextDate = lastLeave.add(i, 'day').format('YYYY-MM-DD');
      if (holidaySet.has(nextDate)) {
        allLeaveDates.add(nextDate);
      }
    }
  }

  // Check between holidays and leave (sandwich)
  if (applyBetween && leaveDates.length >= 2) {
    // const leaveSet = new Set(leaveDates);
    const leaveStart = dayjs(leaveDates[0]);
    const leaveEnd = dayjs(leaveDates[leaveDates.length - 1]);

    for (
      let d = leaveStart.add(1, 'day');
      d.isBefore(leaveEnd);
      d = d.add(1, 'day')
    ) {
      const dayStr = d.format('YYYY-MM-DD');
      if (holidaySet.has(dayStr)) {
        allLeaveDates.add(dayStr);
      }
    }
  }

  return Array.from(allLeaveDates).sort(); // sorted list of leave + sandwich dates
};
export const checkValidations = (
  startDate,
  endDate,
  selectedLeave,
  authState
) => {
  // const sdate = dayjs(startDate)?.format('YYYY-MM-DD HH:mm:ss');
  // const edate = dayjs(endDate)?.format('YYYY-MM-DD HH:mm:ss');
  /** Get hours from leave start and end date */
  const sdate = dayjs(startDate).startOf('day').valueOf(); // Convert to timestamp (milliseconds)
  const edate = dayjs(endDate).startOf('day').valueOf();
  const current_date = dayjs().startOf('day').valueOf();
  // const hoursDifference = (edate - sdate) / (1000 * 60 * 60); // Convert ms to hours
  /** Get days from leave start and end date */
  // const daysDifference = (edate - sdate) / (1000 * 60 * 60 * 24); // Convert ms to day
  if (
    selectedLeave?.leave_accural_policy?.restrict_by_gender &&
    selectedLeave?.leave_accural_policy?.restrict_by_gender_status ==
      authState?.user_gender
  ) {
    return 'Your assigned leave policy does not allow leave request for your gender.';
  }
  if (
    !authState?.user_gender &&
    selectedLeave?.leave_accural_policy?.restrict_by_gender &&
    selectedLeave?.leave_accural_policy?.restrict_by_gender_status ===
      'other' &&
    authState?.user_gender_other
  ) {
    return 'Your assigned leave policy does not allow leave request for your gender.';
  }
  if (
    selectedLeave?.leave_accural_policy?.restrict_by_marital &&
    selectedLeave?.leave_accural_policy?.restrict_by_marital_status ===
      'unmarried' &&
    authState?.marital_status !== 'married'
  ) {
    return 'Your assigned leave policy does not allow leave request for your marital status.';
  }
  if (
    selectedLeave?.leave_accural_policy?.restrict_by_marital &&
    selectedLeave?.leave_accural_policy?.restrict_by_marital_status ==
      authState?.marital_status
  ) {
    return 'Your assigned leave policy does not allow leave request for your marital status.';
  }
  // Half day calculations : Dont remove it
  // if (
  //   selectedLeave?.leave_accural_policy?.allow_emp_for_half_day === 0 &&
  //   selectedLeave?.leave_period_type === 'hour' &&
  //   startDate &&
  //   dayjs(startDate)?.format('YYYY-MM-DD HH:mm:ss') &&
  //   endDate &&
  //   dayjs(endDate)?.format('YYYY-MM-DD HH:mm:ss')
  // ) {
  //   /**check if it's 4 and below 4 then throw error */
  //   if (hoursDifference < 4) {
  //     return 'Your assigned leave policy does not allow half-day leave requests.';
  //   }
  // }
  // Short Leave calculations : Dont remove it
  // if (
  //   selectedLeave?.leave_accural_policy?.allow_emp_for_short_leave == 1 &&
  //   startDate &&
  //   dayjs(startDate)?.format('YYYY-MM-DD HH:mm:ss') &&
  //   endDate &&
  //   dayjs(endDate)?.format('YYYY-MM-DD HH:mm:ss')
  // ) {
  //   /** Get hours from leave start and end date */

  //   // console.log(
  //   //   Number(hoursDifference),
  //   //   Number(
  //   //     selectedLeave?.leave_accural_policy?.allow_emp_for_short_leave_count
  //   //   ),
  //   //   Number(hoursDifference) >=
  //   //     Number(
  //   //       selectedLeave?.leave_accural_policy?.allow_emp_for_short_leave_count
  //   //     ),
  //   //   'hoursDifference'
  //   // );
  //   if (
  //     selectedLeave?.leave_period_type === 'hour' &&
  //     Number(hoursDifference) >=
  //       Number(
  //         selectedLeave?.leave_accural_policy?.allow_emp_for_short_leave_count
  //       )
  //   ) {
  //     return `Your assigned leave policy does not allow more than ${Number(
  //       selectedLeave?.leave_accural_policy?.allow_emp_for_short_leave_count
  //     )} hours of leave requests.`;
  //   }
  //   if (
  //     Number(daysDifference) >=
  //     Number(
  //       selectedLeave?.leave_accural_policy?.allow_emp_for_short_leave_count
  //     )
  //   ) {
  //     return `Your assigned leave policy does not allow more than ${Number(
  //       selectedLeave?.leave_accural_policy?.allow_emp_for_short_leave_count
  //     )} days of leave requests.`;
  //   }
  // }

  if (sdate && sdate === current_date) {
    // for today's date
  } else if (sdate < current_date) {
    if (
      !selectedLeave?.leave_accural_policy?.allow_leave_request_data?.past_date
    ) {
      return 'Your assigned leave policy does not allow past date for your leave request.';
    } else {
      if (
        !selectedLeave?.leave_accural_policy?.allow_leave_request_data
          ?.past_date_status
      ) {
        /** Get leave financial year and employee joining date */
        const leave_financial_year = dayjs(
          selectedLeave?.leave_accural_policy?.leave_calender_year_start_from,
          'YYYY-MM-DD HH:mm:ss'
        ).valueOf();
        const joining_date = dayjs(
          authState?.user_joining_date,
          'YYYY-MM-DD HH:mm:ss'
        ).valueOf();

        /* Check if employee joining date is before leave financial year */
        const isBeforeFinancialYear = joining_date < leave_financial_year;
        if (!isBeforeFinancialYear) {
          /** If no then compare start date with joining date */
          if (sdate < joining_date) {
            return `The Leave Application date should be greater than or equal to the ${moment
              .utc(authState?.user_joining_date)
              .local()
              .format('DD/MM/YYYY hh:mm A')}`;
          }
        } else {
          /** If no then compare start date with financial date */
          if (sdate < leave_financial_year) {
            return `The Leave Application date should be greater than or equal to the ${moment
              .utc(
                selectedLeave?.leave_accural_policy
                  ?.leave_calender_year_start_from
              )
              .local()
              .format('DD/MM/YYYY hh:mm A')}`;
          }
        }
      } else {
        /** If past date is allowed and days are specified */
        const specified_days_date = dayjs()
          .subtract(
            selectedLeave?.leave_accural_policy?.allow_leave_request_data
              ?.past_date_days,
            'days'
          )
          .startOf('day')
          .valueOf();

        const joining_date = dayjs(
          authState?.user_joining_date,
          'YYYY-MM-DD HH:mm:ss'
        ).valueOf();
        const leave_financial_year = dayjs(
          selectedLeave?.leave_accural_policy?.leave_calender_year_start_from,
          'YYYY-MM-DD HH:mm:ss'
        ).valueOf();
        const isFinancialYear =
          joining_date < leave_financial_year
            ? leave_financial_year
            : joining_date;
        const isFinancialYearDate =
          joining_date < leave_financial_year
            ? moment
                .utc(
                  selectedLeave?.leave_accural_policy
                    ?.leave_calender_year_start_from
                )
                .local()
                .format('DD/MM/YYYY hh:mm A')
            : moment
                .utc(authState?.user_joining_date)
                .local()
                .format('DD/MM/YYYY hh:mm A');
        const isBeforeSpecifiedDays = isFinancialYear < specified_days_date;

        if (!isBeforeSpecifiedDays) {
          /** Compare start date with joining date */
          if (sdate < isFinancialYear) {
            return `The Leave Application date should be greater than or equal to the ${isFinancialYearDate}`;
          }
        } else {
          /** Compare start date with specified days date */
          if (sdate < specified_days_date) {
            return `The Leave Application date should be greater than or equal to the ${moment
              .utc(
                moment().subtract(
                  selectedLeave?.leave_accural_policy?.allow_leave_request_data
                    ?.past_date_days,
                  'days'
                )
              )
              .local()
              .format('DD/MM/YYYY hh:mm A')}`;
          }
        }
      }
    }
  } else if (startDate) {
    if (
      !selectedLeave?.leave_accural_policy?.allow_leave_request_data
        ?.future_date
    ) {
      return 'Your assigned leave policy does not allow future date for your leave request.';
    } else {
      if (
        !selectedLeave?.leave_accural_policy?.allow_leave_request_data
          ?.future_date_status
      ) {
        // Get contract expiry date and add 365 days to the current date
        const futureDate = moment().add(365, 'days').valueOf();

        // emp contract not getting
        // if (expiryDate) {
        //   const contractExpiryDate = moment(
        //     expiryDate,
        //     'YYYY-MM-DD HH:mm:ss'
        //   );
        //   const isBeforeFutureDate = contractExpiryDate.isBefore(
        //     futureDate,
        //     'day'
        //   );

        //   if (!isBeforeFutureDate) {
        //     // If contract expiry date is greater than future date, compare start date with future date
        //     if (moment(startDate).isBefore(futureDate, 'second')) {
        //       return `The Leave Application date should be less than or equal to the ${futureDate.format('YYYY-MM-DD')}`;
        //     }
        //   } else {
        //     // If contract expiry date is less than future date, compare start date with contract expiry date
        //     if (moment(startDate).isAfter(contractExpiryDate, 'second')) {
        //       return `The Leave Application date should be less than or equal to the ${contractExpiryDate.format('YYYY-MM-DD')}`;
        //     }
        //   }
        // }
        // else {
        // If no contract expiry date is found, compare start date with future date
        if (sdate > futureDate) {
          return `The Leave Application date should be less than or equal to the ${moment().add(365, 'days').format('YYYY-MM-DD')}`;
        } else if (edate > futureDate) {
          return `The Leave Application date should be less than or equal to the ${moment().add(365, 'days').format('YYYY-MM-DD')}`;
        }
        // }
      } else {
        // If future date is allowed and days are specified, get specified days date
        const specifiedDaysDate = dayjs()
          .add(
            selectedLeave?.leave_accural_policy?.allow_leave_request_data
              ?.future_date_days,
            'days'
          )
          .startOf('day')
          .valueOf();
        // if (getContractExpiryDate?.expire_date) {
        //   const contractExpiryDate = moment(
        //     getContractExpiryDate.expire_date,
        //     'YYYY-MM-DD HH:mm:ss'
        //   );
        //   const isBeforeFutureDate = contractExpiryDate.isBefore(
        //     specifiedDaysDate,
        //     'day'
        //   );

        //   if (!isBeforeFutureDate) {
        //     // If contract expiry date is greater than specified days date, compare start date with it
        //     if (moment(startDate).isBefore(specifiedDaysDate, 'second')) {
        //       return `The Leave Application date should be less than or equal to the ${specifiedDaysDate.format('YYYY-MM-DD')}`;
        //     }
        //   } else {
        //     // If contract expiry date is less than specified days date, compare start date with it
        //     if (moment(startDate).isAfter(contractExpiryDate, 'second')) {
        //       return {
        //         status: false,
        //         message: 'ERROR_LEAVE_LESS_THAN_CONTRACT_DATE_RESTRICTION',
        //         contract_date: contractExpiryDate.format('YYYY-MM-DD'),
        //       };
        //     }
        //   }
        // } else {
        // If no contract expiry date is found, compare start date with specified days date
        if (sdate > specifiedDaysDate) {
          return `The Leave Application date should be less than or equal to the ${moment
            .utc(
              moment().add(
                selectedLeave?.leave_accural_policy?.allow_leave_request_data
                  ?.future_date_days,
                'days'
              )
            )
            .local()
            .format('DD/MM/YYYY hh:mm A')}`;
        } else if (edate > specifiedDaysDate) {
          return `The Leave Application date should be less than or equal to the ${moment
            .utc(
              moment().add(
                selectedLeave?.leave_accural_policy?.allow_leave_request_data
                  ?.future_date_days,
                'days'
              )
            )
            .local()
            .format('DD/MM/YYYY hh:mm A')}`;
        }
        // }
      }
    }
  }

  return false;
};
