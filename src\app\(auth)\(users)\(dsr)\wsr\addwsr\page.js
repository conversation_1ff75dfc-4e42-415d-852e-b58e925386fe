import React from 'react';
import { Box } from '@mui/material';
import { generateMetadata } from '@/helper/common/commonFunctions';
import WSR from '@/components/DSR/WSRs/addwsr';

export const metadata = generateMetadata({
  pageTitle: 'Add WSR',
});

const WSRPage = ({ params }) => {
  return (
    <Box className="main-page-container">
      <WSR params={params} />
    </Box>
  );
};

export default WSRPage;
