.grouping-reorder-section {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  .group-container {
    position: relative;
    background: var(--color-white);
    padding: 5px 10px;
    margin-bottom: 10px;
    border: var(--normal-sec-border);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    // cursor: move;
    // &:hover {
    //   //background: #eeeeee;
    // }
  }
  .empty-group {
    display: flex;
    justify-content: space-between;
    svg {
      width: 21px;
      height: 21px;
      fill: var(--color-primary);
      margin-left: 12px;
    }
  }
  .column-container {
    position: relative;
    background: var(--color-white);
    padding: 5px 10px;
    margin-bottom: 10px;
    border: var(--normal-sec-border);
    border-radius: var(--border-radius-sm);
    padding-left: 33px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    // padding-left: 31px;
    .currency-icon {
      position: absolute;
      left: 9px;
      top: 0px;
      border-right: var(--normal-sec-border);
      display: flex;
      align-items: center;
      height: 100%;
      padding-right: 7px;
    }
    // cursor: move;
  }
  .disable-column-container {
    cursor: default;
    background-color: var(--color-muted);
  }
  .group-column {
    display: flex;
    justify-content: space-between;
    padding-left: 31px;
    svg {
      width: 21px;
      height: 21px;
      fill: var(--icon-color-primary);
      margin-left: 12px;
    }
  }
  .delete-group {
    display: flex;
    align-items: center;
    .delete-icon {
      margin-left: 3px !important;
      width: 21px;
      height: 21px;
      fill: var(--icon-color-primary);
    }
  }

  .selected-column {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .check-box {
      fill: var(--icon-color-primary);
      margin-right: 5px;
    }
    .remove-from-group {
      width: 21px;
      height: 21px;
      fill: var(--icon-color-primary);
      margin-left: 12px;
    }
    .delete-icon {
      margin-left: 3px !important;
      width: 21px;
      height: 21px;
      fill: var(--icon-color-primary);
    }
  }
  .rowname {
    width: calc(100% - 21px - 18px);
    align-items: center;
    .category-name {
      width: 100%;
    }
  }
  .growname {
    width: calc(100% - 21px - 21px - 18px);
    align-items: center;
    .category-name {
      width: 100%;
    }
  }

  .ghost {
    background: var(--color-soft-lavender) !important;
  }
  // /* When dragging, make it look like it's being held */
  // .sortable-drag {
  //   opacity: 0 !important;
  //   display: none !important;
  // }
  .group-options {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;
    .group-selection-section {
      display: flex;
      justify-content: flex-end;
      margin-bottom: 8px;
      cursor: pointer;
      svg {
        width: 21px;
        height: 21px;
        margin-right: 4px;
        fill: var(--icon-color-green);
      }
      p {
        color: var(--text-green);
      }
    }
    .disabled-selection {
      opacity: 0.4;
      cursor: default;
    }
  }
}
.Group-name-selection {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: calc(100% - 16px);
  .list-of-groups {
    max-height: calc(100%);
    overflow-y: scroll;
    margin-top: 16px;
    .group-select {
      cursor: pointer;
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      svg {
        margin-right: 8px;
        fill: var(--icon-color-primary);
      }
    }
  }
}
