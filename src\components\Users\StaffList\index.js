/* eslint-disable no-unused-vars */
'use client';
import React, { useContext, useEffect, useState } from 'react';
import {
  Box,
  Typography,
  CircularProgress,
  Tooltip,
  Popover,
  Checkbox,
} from '@mui/material';
import AuthContext from '@/helper/authcontext';
import { DataGrid, gridClasses } from '@mui/x-data-grid';
import AddIcon from '@mui/icons-material/Add';
import CustomButton from '@/components/UI/CustomButton';
import { setApiMessage, DateFormat } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import { useRouter } from 'next/navigation';
import {
  fetchFromStorage,
  removeFromStorage,
  saveToStorage,
} from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import moment from 'moment';
import EditIcon from '@/components/ActionIcons/EditIcon';
import DeleteIcon from '@/components/ActionIcons/DeleteIcon';
import RequestIcon from '@/components/ActionIcons/RequestIcon';
import _ from 'lodash';
import CheckIcon from '@mui/icons-material/Check';
import CustomSelect from '@/components/UI/CustomSelect';
import ClearOutlinedIcon from '@mui/icons-material/ClearOutlined';
import RightDrawer from '@/components/UI/RightDrawer';
import FilterComponent from '@/components/UI/FilterComponent';
import CustomSearch from '@/components/UI/CustomSearch';
import DeleteModal from '@/components/UI/DeleteModal';
import DialogBox from '@/components/UI/Modalbox';
import NoDataView from '@/components/UI/NoDataView';
import CommonUserDetails from '@/components/UI/CommonUserDetails/index';
import FilterListIcon from '@mui/icons-material/FilterList';
import useRoleList from '@/hooks/useRoleList';
import './staff.scss';
import CustomOrgPagination from '@/components/UI/customPagination';
import BranchDepartmentDisplay from '@/components/BranchDepartmentDisplay';
import DownloadIcon from '@mui/icons-material/Download';
import { staticOptions } from '@/helper/common/staticOptions';
import ConfirmationModal from '@/components/UI/ConfirmationModal';
import SettingsIcon from '@mui/icons-material/Settings';
import DownloadList from './DownloadList';
import ExportStatusIndicator from './DownloadField/components/ExportProgress';

const getStatusClass = (status) => {
  const map = {
    rejected: 'failed',
    deleted: 'failed',
    cancelled: 'failed',
    ongoing: 'ongoing',
    draft: 'draft',
    pending: 'draft',
    completed: 'active-onboarding',
    verified: 'active-onboarding',
  };
  return map[status] || 'success';
};
const getStatusClassOnboard = (status) => {
  const map = {
    ongoing: 'ongoing',
    pending: 'draft',
    completed: 'active-onboarding',
  };
  return map[status] || 'success';
};
export default function Staff() {
  const router = useRouter();
  const { userdata, setUserdata, AllListsData } = useContext(AuthContext);
  const { authState, setRestrictedLimitModal } = useContext(AuthContext);
  const [openFilterDrawer, setOpenFilterDrawer] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState([]);
  const { roleList, fetchRoleList } = useRoleList();
  const [filterData, setFilterData] = useState({
    branch: '',
    role: '',
    department: '',
    status: '',
    training_status: '',
    contract_status: '',
  });
  const [filterDataApplied, setFilterDataApplied] = useState({
    branch: '',
    role: '',
    department: '',
    status: '',
    training_status: '',
    contract_status: '',
  });
  const [userList, setUserList] = useState([]);
  const [loaderUser, setLoaderUser] = useState(true);
  const [searchValue, setSearchValue] = useState('');
  const [page, setPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  // const [branchColumnWidth, setBranchColumnWidth] = useState(200);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteId, setDeleteId] = useState(null);
  const [joinChecklist, setJoiningChecklist] = useState(false);
  const [selectedCat, setSelectedCat] = useState([]);
  const [selectStaffModal, setSelectStaffModal] = useState(false);
  const [selectedFields, setSelectedFields] = useState([]);
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);

  // Export status management
  const [exportStatus, setExportStatus] = useState('idle'); // 'idle', 'processing', 'completed', 'error'
  const [exportProgress, setExportProgress] = useState(0);
  const [exportFileName, setExportFileName] = useState('');
  const [exportError, setExportError] = useState('');
  const [downloadUrl, setDownloadUrl] = useState('');
  const [currentExportType, setCurrentExportType] = useState('');

  const id = open ? 'simple-popper' : undefined;
  const filters = [
    {
      key: 'search',
      label: 'Search',
      options: [],
      permission: true,
    },
    {
      key: 'branch',
      label: 'Branch Name',
      options: AllListsData?.ActiveBranchList,
      permission: authState?.UserPermission?.branch >= 1,
      showDot: true,
    },
    {
      key: 'department',
      label: 'Department Name',
      options: AllListsData?.ActiveDepartmentList,
      permission: authState?.UserPermission?.department >= 1,
    },
    {
      key: 'role',
      label: 'Role',
      options: roleList,
      permission: true,
    },
    {
      key: 'status',
      label: 'Status',
      options: identifiers?.USER_STATUS_OPTIONS,
      permission: true,
    },
    {
      key: 'training_status',
      label: 'Training Status',
      options: identifiers?.TRAINING_FILTER_STATUS,
      permission: true,
    },
    {
      key: 'contract_status',
      label: 'Contract Status',
      options: staticOptions?.CONTRACT_FILTER_STATUS,
      permission: true,
    },
  ];

  // useEffect(() => {
  //   const handleResize = () => {
  //     if (window.innerWidth <= 899) {
  //       setBranchColumnWidth(350);
  //     } else {
  //       setBranchColumnWidth(200);
  //     }
  //   };

  //   window.addEventListener('resize', handleResize);
  //   handleResize();
  //   return () => window.removeEventListener('resize', handleResize);
  // }, []);
  const handleClick = (event) => {
    setAnchorEl(anchorEl ? null : event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const handleOpenDeleteDialog = (id) => {
    setDeleteId(id);
    setDeleteDialogOpen(true);
  };
  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setDeleteId(null);
  };
  // Delete user
  const deleteUser = async (id) => {
    try {
      // setLoader(true);
      const { status, data } = await axiosInstance.delete(
        URLS?.DELETE_USER + id
      );
      if (status === 200) {
        handleCloseDeleteDialog();
        if (userList?.length === 1 && page !== 1) {
          getUserList(
            searchValue,
            Number(page) - 1,
            filterDataApplied?.branch,
            filterDataApplied?.role,
            filterDataApplied?.department,
            filterDataApplied?.status,
            filterDataApplied?.training_status,
            filterDataApplied?.contract_status
          );
          setPage(Number(page) - 1);
        } else {
          getUserList(
            searchValue,
            page,
            filterDataApplied?.branch,
            filterDataApplied?.role,
            filterDataApplied?.department,
            filterDataApplied?.status,
            filterDataApplied?.training_status,
            filterDataApplied?.contract_status
          );
        }
        if (data.status) {
          setApiMessage('success', data?.message);
        } else {
          setApiMessage('error', data?.message);
        }
      } else {
        setApiMessage('error', data?.message);
      }
      // setLoader(false);
    } catch (error) {
      // setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const handleRenewAndNotify = async (renewNotifyData, type) => {
    try {
      // setLoader(true);
      const { status, data } = await axiosInstance.get(
        URLS.REGENERATE_EMP_CONTRACT +
          `${renewNotifyData ? renewNotifyData?.id : ''}${
            type === 'notify'
              ? '?notify=true'
              : type === 'remind'
                ? '?notify=only'
                : ''
          }`
      );

      if (status === 200) {
        // setLoader(false);
        setApiMessage('success', data?.message);
        getUserList(
          searchValue,
          page,
          filterDataApplied?.branch,
          filterDataApplied?.role,
          filterDataApplied?.department,
          filterDataApplied?.status,
          filterDataApplied?.training_status,
          filterDataApplied?.contract_status
        );
      }
    } catch (error) {
      // setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // const handleCheck = (value) => {
  //   const currentHSIndex = selectedCat?.indexOf(value);
  //   const newChecked = [...selectedCat];
  //   if (currentHSIndex === -1) {
  //     newChecked.push(value);
  //   } else {
  //     newChecked.splice(currentHSIndex, 1);
  //   }
  //   setSelectedCat(newChecked);
  // };
  const handleSelectAllClick = (event) => {
    if (event.target.checked) {
      const newSelecteds = userList?.map((n) => n?.id);
      setSelectedCat(newSelecteds);
      return;
    }
    setSelectedCat([]);
  };

  const columns = [
    {
      field: 'id',
      width: 100,
      minWidth: 100,
      flex: 0,
      sortable: false,
      headerAlign: 'start',
      headerName: (
        <Box className="d-flex align-center justify-start">
          <Box className="d-flex justify-start">
            <Checkbox
              className="table-checkbox pl0"
              checked={
                selectedCat === 'all' ||
                (Array.isArray(selectedCat) &&
                  userList?.every((user) => selectedCat?.includes(user?.id)))
              }
              onClick={(event) => handleSelectAllClick(event)}
              // checked={selectedCat?.indexOf(params?.value) !== -1}
            />
          </Box>
          <Typography className="title-text cursor-pointer fw600">
            {'ID'}
          </Typography>
        </Box>
      ),
      align: 'start',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-start h100">
            <Box className="d-flex align-center">
              <Checkbox
                className="table-checkbox checkbox pl0 pr4"
                disabled={true}
                // onClick={() => handleCheck(params?.value)}
                checked={
                  selectedCat === 'all'
                    ? true
                    : selectedCat?.indexOf(params?.value) !== -1
                }
              />
            </Box>
            <Tooltip
              arrow
              title={<Typography>{params?.row?.employment_number}</Typography>}
              classes={{
                tooltip: 'info-tooltip-container ',
              }}
            >
              <Typography className="title-text cursor-pointer">
                {params?.row?.employment_number}
              </Typography>
            </Tooltip>
          </Box>
        );
      },
    },
    // {
    //   field: 'id',
    //   headerName: 'ID',
    //   width: 100,
    //   minWidth: 100,
    //   flex: 0,
    //   sortable: false,
    //   headerAlign: 'start',
    //   align: 'start',
    //   renderCell: (params) => {
    //     return (
    //       <Box className="d-flex align-center justify-start h100">
    //         <Tooltip
    //           arrow
    //           title={<Typography>{params?.row?.employment_number}</Typography>}
    //           classes={{
    //             tooltip: 'info-tooltip-container ',
    //           }}
    //         >
    //           <Typography className="title-text cursor-pointer">
    //             {params?.row?.employment_number}
    //           </Typography>
    //         </Tooltip>
    //       </Box>
    //     );
    //   },
    // },
    {
      field: 'user_full_name',
      headerName: 'User',
      width: 260,
      minWidth: 260,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <CommonUserDetails
            userData={params?.row}
            filterDataApplied={filterDataApplied}
            searchValue={searchValue}
            page={page}
            rowsPerPage={rowsPerPage}
            setUserdata={setUserdata}
            authState={authState}
          />
        );
      },
    },
    {
      field: 'branch?.branch_name',
      headerName: 'Branch / Dep.',
      // width: branchColumnWidth,
      // minWidth: branchColumnWidth,
      width: 220,
      minWidth: 220,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return <BranchDepartmentDisplay row={params?.row} />;
      },
    },
    {
      field: 'user_joining_date',
      headerName: 'Joining date',
      width: 150,
      minWidth: 150,
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      sortable: false,
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100">
            {DateFormat(params?.value, 'date')}
          </Box>
        );
      },
    },
    {
      field: 'user_status',
      headerName: 'Profile status',
      width: 150,
      minWidth: 150,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100 text-capital">
            <Typography
              className={`sub-title-text ${getStatusClass(params?.value)} fw600`}
            >
              {params?.value}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'user_track_status',
      headerName: 'Training status',
      width: 150,
      minWidth: 150,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100 text-capital">
            <Typography
              className={`sub-title-text ${getStatusClassOnboard(params?.value)} fw600`}
            >
              {params?.value}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'user_contract',
      headerName: 'Contract status',
      width: 150,
      minWidth: 150,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        const currentDate = moment(new Date()).startOf('day');
        const expireDate = moment(
          params?.row?.user_contract?.expire_date
        ).startOf('day');

        const daysDifference = expireDate.diff(currentDate, 'days');

        const isExpiry =
          daysDifference <= 0
            ? 'Expired'
            : daysDifference > 0 && daysDifference <= 15
              ? 'Expiry Soon'
              : params?.row?.user_contract?.is_confirm_sign === 1
                ? params?.row?.is_probation === 1
                  ? 'Probation'
                  : params?.row?.is_probation === 0
                    ? 'Confirmed'
                    : ''
                : params?.row?.user_contract?.is_confirm_sign === null
                  ? 'Pending'
                  : 'Awaiting Signature';

        return (
          <Box className="d-flex align-center justify-center h100 text-capital">
            {isExpiry === 'Expired' ? (
              <Typography className="sub-title-text failed fw600">
                {' '}
                {isExpiry}{' '}
              </Typography>
            ) : isExpiry === 'Expiry Soon' ? (
              <Typography className="sub-title-text status-yellow fw600">
                {isExpiry}
              </Typography>
            ) : isExpiry === 'Confirmed' ? (
              <Typography className="sub-title-text active-onboarding fw600">
                {isExpiry}
              </Typography>
            ) : isExpiry === 'Probation' ? (
              <Typography className="sub-title-text success fw600">
                {isExpiry}
              </Typography>
            ) : (
              <Typography className="sub-title-text draft fw600">
                {isExpiry}
              </Typography>
            )}
          </Box>
        );
      },
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 130,
      minWidth: 130,
      flex: 1,
      sortable: false,
      headerAlign:
        authState?.UserPermission?.staff === 1 ? 'center' : 'flex-start',
      align: 'center',
      renderCell: (params) => {
        const currentDate = moment(new Date()).startOf('day');
        const expireDate = moment(
          params?.row?.user_contract?.expire_date
        ).startOf('day');

        const daysDifference = expireDate.diff(currentDate, 'days');

        return (
          <Box className="d-flex justify-start">
            {authState?.UserPermission?.staff === 1 ? (
              <Box className="d-flex actions-staff justify-center">
                <Tooltip
                  arrow
                  title={<Typography>View</Typography>}
                  classes={{
                    tooltip: 'info-tooltip-container ',
                  }}
                >
                  <Box>
                    <EditIcon
                      onClick={() => {
                        setUserdata({
                          id: params?.row?.id,
                          filterData: filterDataApplied,
                          searchValue: searchValue,
                          page: page,
                          rowperpage: rowsPerPage,
                        });
                        saveToStorage(identifiers?.RedirectData, {
                          id: params?.row?.id,
                          filterData: filterDataApplied,
                          searchValue: searchValue,
                          page: page,
                          rowperpage: rowsPerPage,
                        });
                        router.push(`/user/${params?.row?.id}`);
                      }}
                    />
                  </Box>
                </Tooltip>
              </Box>
            ) : authState?.UserPermission?.staff === 2 ? (
              <>
                <Box className="d-flex actions-staff">
                  {/*   <EditIcon onClick={() => handleEdit(params)} /> */}
                  <Tooltip
                    arrow
                    title={<Typography>View</Typography>}
                    classes={{
                      tooltip: 'info-tooltip-container ',
                    }}
                  >
                    <Box>
                      <EditIcon
                        onClick={() => {
                          setUserdata({
                            id: params?.row?.id,
                            filterData: filterDataApplied,
                            searchValue: searchValue,
                            page: page,
                            rowperpage: rowsPerPage,
                          });
                          saveToStorage(identifiers?.RedirectData, {
                            id: params?.row?.id,
                            filterData: filterDataApplied,
                            searchValue: searchValue,
                            page: page,
                            rowperpage: rowsPerPage,
                          });
                          router.push(`/user/${params?.row?.id}`);
                        }}
                      />
                    </Box>
                  </Tooltip>
                  {params?.row?.user_status !== 'deleted' &&
                    authState?.UserPermission?.staff === 2 && (
                      <Tooltip
                        title={<Typography>Delete</Typography>}
                        classes={{
                          tooltip: 'info-tooltip-container ',
                        }}
                      >
                        <Box>
                          <DeleteIcon
                            onClick={() =>
                              handleOpenDeleteDialog(params?.row?.id)
                            }
                          />
                        </Box>
                      </Tooltip>
                    )}

                  {daysDifference > 0 &&
                    params?.row?.user_contract?.is_confirm_sign === 0 && (
                      <Tooltip
                        title={<Typography>Remind</Typography>}
                        classes={{
                          tooltip: 'info-tooltip-container ',
                        }}
                      >
                        <Box>
                          <RequestIcon
                            onClick={() => {
                              handleRenewAndNotify(params?.row, 'remind');
                            }}
                          />
                        </Box>
                      </Tooltip>
                    )}
                </Box>
              </>
            ) : (
              <></>
            )}
          </Box>
        );
      },
    },
  ];

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      setPage(1);
      getUserList(
        searchValue,
        1,
        filterDataApplied?.branch,
        filterDataApplied?.role,
        filterDataApplied?.department,
        filterDataApplied?.status,
        filterDataApplied?.training_status,
        filterDataApplied?.contract_status
      );
    }
  };
  // List of all staff
  const getUserList = async (
    search,
    pageNo,
    branch,
    role,
    department,
    statusValue,
    trainingStatus,
    contractStatus,
    Rpp,
    isExport
  ) => {
    // setLoader(true);
    !isExport && setLoaderUser(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_USER_LIST +
          `?isAdmin=false&search=${search}&page=${pageNo}&size=${
            Rpp ? Rpp : rowsPerPage
          }&branch_id=${branch ? branch : ''}&status=${
            statusValue ? statusValue : ''
          }&user_track_status=${
            trainingStatus ? trainingStatus : ''
          }&contract_status=${contractStatus ? contractStatus : ''}&role_id=${
            role ? role : ''
          }&department_id=${department ? department : ''}`
      );

      if (status === 200) {
        // setApiMessage('success', data?.message);
        // if (
        //   Rpp &&
        //   data?.userList &&
        //   data?.userList?.length > 0 &&
        //   selectedCat !== 'all'
        // ) {
        //   setSelectedCat(data?.userList?.map((item) => item?.id));
        // }
        setUserdata();
        removeFromStorage(identifiers?.RedirectData);
        setUserList(data?.userList);
        setTotalCount(data?.count);
        setTimeout(() => {
          setLoaderUser(false);
        }, 1000);
      }
    } catch (error) {
      setLoaderUser(false);
      setUserList([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // Save field sequence
  const handleSaveConfiguration = async (type) => {
    const requiredFields = [
      'employment_number',
      'username',
      'user_first_name',
      'user_last_name',
      'user_email',
    ];

    // Set export status to processing
    setExportStatus('processing');
    setExportProgress(10);
    setExportFileName(`staff_list.${type === 'excel' ? 'xlsx' : 'csv'}`);
    setCurrentExportType(type);
    setExportError('');

    try {
      const { status, data } = await axiosInstance.post(
        URLS?.STORE_USER_FIELDS_SEQUENCE,
        {
          user_field_order: requiredFields,
        }
      );
      if (status === 200) {
        if (data?.status) {
          setExportProgress(30);
          setTimeout(() => {
            getDownloadUserList(
              searchValue,
              1,
              filterData?.branch,
              filterData?.role,
              filterData?.department,
              filterData?.status,
              filterData?.training_status,
              filterData?.contract_status,
              '',
              type,
              true
            );
          }, 500);
        } else {
          setExportStatus('error');
          setExportError('Failed to save configuration');
        }
      }
    } catch (error) {
      setExportStatus('error');
      setExportError(
        error?.response?.data?.message || 'Failed to save configuration'
      );
    }
  };
  const getStoredField = async () => {
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_STORED_USER_FIELDS
      );

      if (status === 200) {
        setSelectedFields(data?.data?.user_field_order);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // Download staff list
  const getDownloadUserList = async (
    search,
    pageNo,
    branch,
    role,
    department,
    statusValue,
    trainingStatus,
    contractStatus,
    Rpp,
    fileType,
    isExport
  ) => {
    // For export operations, manage progress
    setExportProgress(50);

    try {
      const { status, data } = await axiosInstance.get(
        URLS?.EXPORT_USER_LIST +
          `?isAdmin=false&search=${search}&page=${
            selectedCat === 'all' ? 1 : pageNo
          }&size=${
            selectedCat === 'all' ? totalCount : Rpp ? Rpp : rowsPerPage
          }&branch_id=${branch ? branch : ''}&status=${
            statusValue ? statusValue : ''
          }&user_track_status=${
            trainingStatus ? trainingStatus : ''
          }&contract_status=${contractStatus ? contractStatus : ''}&role_id=${
            role ? role : ''
          }&department_id=${department ? department : ''}&file_type=${
            fileType ? fileType : 'csv'
          }`,
        {
          responseType: 'blob',
        }
      );

      if (status === 200) {
        setExportProgress(90);

        const url = window.URL.createObjectURL(new Blob([data]));
        const filename = `${identifiers?.APP_NAME}_Staff_List.${fileType === 'excel' ? 'xlsx' : 'csv'}`;

        // Store download URL for the download button
        setDownloadUrl(url);
        setExportFileName(filename);
        setExportProgress(100);
        setExportStatus('completed');

        // Auto-download after a short delay
        // setTimeout(() => {
        //   const link = document.createElement('a');
        //   link.href = url;
        //   link.setAttribute('download', filename);
        //   document.body.appendChild(link);
        //   link.click();
        //   document.body.removeChild(link);
        // }, 1000);

        isExport && getStoredField();
      }
    } catch (error) {
      setExportStatus('error');
      setExportError(
        error?.response?.data?.message || 'Failed to generate export file'
      );
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const onPageChange = (newPage) => {
    setPage(newPage);
    getUserList(
      searchValue,
      newPage,
      filterDataApplied?.branch,
      filterDataApplied?.role,
      filterDataApplied?.department,
      filterDataApplied?.status,
      filterDataApplied?.training_status,
      filterDataApplied?.contract_status
    );
  };
  const OnRowPerPage = (newPage) => {
    setRowsPerPage(newPage);
    setPage(1);
    getUserList(
      searchValue,
      1,
      filterDataApplied?.branch,
      filterDataApplied?.role,
      filterDataApplied?.department,
      filterDataApplied?.status,
      filterDataApplied?.training_status,
      filterDataApplied?.contract_status,
      newPage
    );
  };

  const toggleFilter = (key) => {
    setSelectedFilters((prevFilters) => {
      if (prevFilters?.includes(key)) {
        return prevFilters?.filter((item) => item !== key);
      } else {
        const index = filters.findIndex((filter) => filter.key === key);
        const newFilters = [...prevFilters];
        newFilters.splice(index, 0, key);
        return newFilters;
      }
    });
  };
  const getFirstFourFilters = () => {
    setSelectedFilters(selectedFilters?.slice(0, 4));
    saveToStorage('pinnedFilters', selectedFilters?.slice(0, 4));
  };

  const saveLayout = () => {
    saveToStorage('pinnedFilters', selectedFilters);
    setOpenFilterDrawer(false);
  };

  // Export status handlers
  const handleExportDownload = () => {
    if (downloadUrl) {
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.setAttribute('download', exportFileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const handleExportDismiss = () => {
    setExportStatus('idle');
    setExportProgress(0);
    setExportFileName('');
    setExportError('');
    setDownloadUrl('');
    setCurrentExportType('');
  };

  const handleExportRetry = () => {
    if (currentExportType) {
      if (selectedFields?.length > 0) {
        getDownloadUserList(
          searchValue,
          1,
          filterData?.branch,
          filterData?.role,
          filterData?.department,
          filterData?.status,
          filterData?.training_status,
          filterData?.contract_status,
          '',
          currentExportType,
          true
        );
      } else {
        handleSaveConfiguration(currentExportType);
      }
    }
  };

  useEffect(() => {
    const savedFilters = fetchFromStorage('pinnedFilters');

    if (!savedFilters) {
      // First 4 filters ke sirf `key` values extract karke set karo
      setSelectedFilters(filters.slice(0, 4).map((filter) => filter.key));
    } else {
      setSelectedFilters(savedFilters);
    }
  }, []);

  useEffect(() => {
    if (
      fetchFromStorage(identifiers?.RedirectData) &&
      fetchFromStorage(identifiers?.RedirectData)?.IsFromUser
    ) {
      const fdata = fetchFromStorage(identifiers?.RedirectData);
      setPage(fdata?.page);
      setFilterData(fdata?.filterData);
      setFilterDataApplied(fdata?.filterData);
      setSearchValue(fdata?.searchValue);
      setRowsPerPage(fdata?.rowperpage);
      getUserList(
        fdata?.searchValue,
        fdata?.page,
        fdata?.filterData?.branch,
        fdata?.filterData?.role,
        fdata?.filterData?.department,
        fdata?.filterData?.status,
        fdata?.filterData?.training_status,
        fdata?.filterData?.contract_status,
        fdata?.rowperpage
      );
    } else if (userdata && userdata?.IsFromUser) {
      const fdata = userdata;
      setPage(fdata?.page);
      setFilterData(fdata?.filterData);
      setFilterDataApplied(fdata?.filterData);
      setSearchValue(fdata?.searchValue);
      setRowsPerPage(fdata?.rowperpage);
      getUserList(
        fdata?.searchValue,
        fdata?.page,
        fdata?.filterData?.branch,
        fdata?.filterData?.role,
        fdata?.filterData?.department,
        fdata?.filterData?.status,
        fdata?.filterData?.training_status,
        fdata?.filterData?.contract_status,
        fdata?.rowperpage
      );
    } else {
      setUserdata();
      removeFromStorage(identifiers?.RedirectData);
    }
  }, [
    fetchFromStorage(identifiers?.RedirectData)?.IsFromUser,
    userdata?.IsFromUser,
  ]);
  useEffect(() => {
    if (
      authState?.UserPermission?.staff === 1 ||
      authState?.UserPermission?.staff === 2
    ) {
      fetchRoleList();
      getStoredField();
      if (
        !fetchFromStorage(identifiers?.RedirectData) &&
        userdata?.page === undefined &&
        fetchFromStorage(identifiers?.RedirectData)?.IsFromUser === undefined &&
        userdata?.IsFromUser === undefined
      ) {
        getUserList(searchValue, page, '', '', '', '', '', '');
      }
    }
  }, [authState?.UserPermission?.staff]);

  // Cleanup download URL when component unmounts or export status changes
  useEffect(() => {
    return () => {
      if (downloadUrl) {
        URL.revokeObjectURL(downloadUrl);
      }
    };
  }, [downloadUrl]);

  // Auto-dismiss export status after completion
  // useEffect(() => {
  //   if (exportStatus === 'completed') {
  //     const timer = setTimeout(() => {
  //       handleExportDismiss();
  //     }, 10000); // Auto-dismiss after 10 seconds

  //     return () => clearTimeout(timer);
  //   }
  // }, [exportStatus]);

  return (
    <>
      <Box>
        <Box className="staff-sec-wrap">
          <Box className="filter-section search-section-wrap">
            {/* <Box className=""> */}
            {selectedFilters?.map((key) => {
              const filter = filters?.find((f) => f?.key === key);
              return (
                <>
                  {key === 'search' ? (
                    <Box className="search-section-fields">
                      <CustomSearch
                        setSearchValue={setSearchValue}
                        searchValue={searchValue}
                        onKeyPress={handleKeyPress}
                      />
                    </Box>
                  ) : (
                    <Box className="search-section-fields">
                      <CustomSelect
                        showDot={filter?.showDot}
                        placeholder={filter?.label}
                        options={filter?.options}
                        value={
                          filter?.options?.find((opt) => {
                            return opt?.value === filterData[key];
                          }) || ''
                        }
                        onChange={(e) =>
                          setFilterData({
                            ...filterData,
                            [key]: e?.value,
                          })
                        }
                      />
                    </Box>
                  )}
                </>
              );
            })}
            {/* </Box> */}
            <Box>
              <CustomButton
                isIconOnly
                startIcon={
                  <Tooltip
                    title={
                      <Typography className="sub-title-text">
                        Apply Filter
                      </Typography>
                    }
                    classes={{
                      tooltip: 'info-tooltip-container',
                    }}
                    arrow
                  >
                    <FilterListIcon />
                  </Tooltip>
                }
                onClick={() => {
                  setOpenFilterDrawer(true);
                }}
              />
            </Box>
            <Box>
              <CustomButton
                isIconOnly
                startIcon={
                  <Tooltip
                    title={
                      <Typography className="sub-title-text">
                        Apply Filter
                      </Typography>
                    }
                    classes={{
                      tooltip: 'info-tooltip-container',
                    }}
                    arrow
                  >
                    <CheckIcon />
                  </Tooltip>
                }
                onClick={() => {
                  setPage(1);
                  setFilterDataApplied({
                    branch: filterData?.branch,
                    role: filterData?.role,
                    department: filterData?.department,
                    status: filterData?.status,
                    training_status: filterData?.training_status,
                    contract_status: filterData?.contract_status,
                  });
                  getUserList(
                    searchValue,
                    1,
                    filterData?.branch,
                    filterData?.role,
                    filterData?.department,
                    filterData?.status,
                    filterData?.training_status,
                    filterData?.contract_status
                  );
                }}
              />
            </Box>
            <Box>
              <CustomButton
                variant="outlined"
                isIconOnly
                startIcon={
                  <Tooltip
                    title={
                      <Typography className="sub-title-text">
                        Clear Filter
                      </Typography>
                    }
                    classes={{
                      tooltip: 'info-tooltip-container',
                    }}
                    arrow
                  >
                    <ClearOutlinedIcon />
                  </Tooltip>
                }
                onClick={() => {
                  setPage(1);
                  setSearchValue('');
                  setFilterData({
                    branch: '',
                    role: '',
                    department: '',
                    status: '',
                    training_status: '',
                    contract_status: '',
                  });
                  setFilterDataApplied({
                    branch: '',
                    role: '',
                    department: '',
                    status: '',
                    training_status: '',
                    contract_status: '',
                  });
                  getUserList('', 1, '', '', '', '', '', '');
                }}
              />
            </Box>
            {authState?.UserPermission?.staff === 2 && (
              <Box>
                <CustomButton
                  title="Create staff"
                  startIcon={<AddIcon />}
                  onClick={() => {
                    if (authState?.remaining_emp === 0) {
                      // totalCount > authState?.remaining_emp ||
                      setRestrictedLimitModal('staff-list');
                    } else {
                      setUserdata({
                        filterData: filterDataApplied,
                        searchValue: searchValue,
                        page: page,
                        rowperpage: rowsPerPage,
                      });
                      saveToStorage(identifiers?.RedirectData, {
                        filterData: filterDataApplied,
                        searchValue: searchValue,
                        page: page,
                        rowperpage: rowsPerPage,
                      });
                      router.push('/staff/create-staff');
                    }
                  }}
                />
              </Box>
            )}
            <Box className="export-section">
              {authState?.UserPermission?.staff === 2 && (
                <>
                  <CustomButton
                    isIconOnly
                    startIcon={
                      <Tooltip
                        title={<Typography>Export </Typography>}
                        classes={{
                          tooltip: 'info-tooltip-container',
                        }}
                        arrow
                      >
                        <DownloadIcon />
                      </Tooltip>
                    }
                    disabled={
                      selectedCat !== 'all' && selectedCat?.length === 0
                    }
                    onClick={handleClick}
                  />
                  <Popover
                    className="export-popover"
                    id={id}
                    open={open}
                    anchorEl={anchorEl}
                    onClose={handleClose}
                    anchorOrigin={{
                      vertical: 'bottom',
                      horizontal: 'left',
                    }}
                  >
                    <Box className="export-option">
                      <Typography
                        className="title-text fw600 pb8 cursor-pointer"
                        onClick={() => {
                          setPage(1);
                          setFilterDataApplied({
                            branch: filterData?.branch,
                            role: filterData?.role,
                            department: filterData?.department,
                            status: filterData?.status,
                            training_status: filterData?.training_status,
                            contract_status: filterData?.contract_status,
                          });
                          getUserList(
                            searchValue,
                            1,
                            filterData?.branch,
                            filterData?.role,
                            filterData?.department,
                            filterData?.status,
                            filterData?.training_status,
                            filterData?.contract_status,
                            undefined,
                            true
                          );
                          if (selectedFields?.length > 0) {
                            // Set export status for direct download
                            setExportStatus('processing');
                            setExportProgress(10);
                            setExportFileName('staff_list.xlsx');
                            setCurrentExportType('excel');
                            setExportError('');

                            getDownloadUserList(
                              searchValue,
                              1,
                              filterData?.branch,
                              filterData?.role,
                              filterData?.department,
                              filterData?.status,
                              filterData?.training_status,
                              filterData?.contract_status,
                              '',
                              'excel',
                              true
                            );
                          } else {
                            handleSaveConfiguration('excel');
                          }
                          handleClose(); // Close the popover
                        }}
                      >
                        Excel
                      </Typography>
                      <Typography
                        className="title-text fw600 cursor-pointer"
                        onClick={() => {
                          setPage(1);
                          setFilterDataApplied({
                            branch: filterData?.branch,
                            role: filterData?.role,
                            department: filterData?.department,
                            status: filterData?.status,
                            training_status: filterData?.training_status,
                            contract_status: filterData?.contract_status,
                          });
                          getUserList(
                            searchValue,
                            1,
                            filterData?.branch,
                            filterData?.role,
                            filterData?.department,
                            filterData?.status,
                            filterData?.training_status,
                            filterData?.contract_status,
                            undefined,
                            true
                          );
                          if (selectedFields?.length > 0) {
                            // Set export status for direct download
                            setExportStatus('processing');
                            setExportProgress(10);
                            setExportFileName('staff_list.csv');
                            setCurrentExportType('csv');
                            setExportError('');

                            getDownloadUserList(
                              searchValue,
                              1,
                              filterData?.branch,
                              filterData?.role,
                              filterData?.department,
                              filterData?.status,
                              filterData?.training_status,
                              filterData?.contract_status,
                              '',
                              'csv',
                              true
                            );
                          } else {
                            handleSaveConfiguration('csv');
                          }
                          handleClose(); // Close the popover
                        }}
                      >
                        CSV
                      </Typography>
                    </Box>
                  </Popover>
                </>
              )}
              {authState?.UserPermission?.staff === 2 && (
                <>
                  <CustomButton
                    isIconOnly
                    startIcon={
                      <Tooltip
                        title={<Typography>Export Settings</Typography>}
                        classes={{
                          tooltip: 'info-tooltip-container',
                        }}
                        arrow
                      >
                        <SettingsIcon />
                      </Tooltip>
                    }
                    onClick={() => {
                      router.push('/org/setup?is_setup=8&is_tab=1');
                    }}
                  />
                </>
              )}
            </Box>
          </Box>
          <Box className="selection-staff-count">
            {selectedCat === 'all' ? (
              <Typography className="title-text text-align">
                All {totalCount} staff are selected.{' '}
                <span
                  className="select-all-text"
                  onClick={() => {
                    setSelectedCat([]);
                  }}
                >
                  Clear selection
                </span>
              </Typography>
            ) : selectedCat?.length > 0 ? (
              <Typography className="title-text text-align">
                All {selectedCat?.length} staff on this page are selected.{' '}
                <span
                  className="select-all-text"
                  onClick={() => {
                    setSelectedCat('all');
                  }}
                >
                  Select all {totalCount} staff
                </span>
              </Typography>
            ) : (
              <></>
            )}
          </Box>
          <Box className="table-container table-layout staff-table">
            {loaderUser ? (
              <Box className="content-loader no-data-found">
                <CircularProgress className="loader" color="inherit" />
              </Box>
            ) : (
              <>
                {userList && userList?.length === 0 ? (
                  <Box className="no-data d-flex align-center justify-center">
                    <NoDataView
                      title="No Staff Records Found"
                      description="There is no staff data available at the moment."
                    />
                  </Box>
                ) : (
                  <>
                    <DataGrid
                      rows={userList}
                      columns={columns}
                      pageSize={rowsPerPage}
                      checkboxSelection={false} // Disable default checkbox column
                      disableSelectionOnClick // Disable row selection on click
                      // selectedRows={selectedRows}
                      hideMenuIcon
                      // sx={{
                      //   [`& .${gridClasses.cell}`]: {
                      //     py: 1,
                      //   },
                      // }}
                      // getRowHeight={() => 'auto'}
                      rowHeight={72}
                      sx={{
                        transition: 'none', // Disables transition effects
                        [`& .${gridClasses.cell}`]: {
                          py: 1,
                        },
                      }}
                    />
                    <CustomOrgPagination
                      currentPage={page}
                      OnRowPerPage={OnRowPerPage}
                      totalCount={totalCount}
                      rowsPerPage={rowsPerPage}
                      onPageChange={onPageChange}
                    />
                  </>
                )}
              </>
            )}
          </Box>
          <ExportStatusIndicator
            status={exportStatus}
            progress={exportProgress}
            fileName={exportFileName}
            onDownload={handleExportDownload}
            onDismiss={handleExportDismiss}
            onRetry={handleExportRetry}
            exportError={exportError}
            estimatedTime={exportStatus === 'processing' ? '1-2 minutes' : null}
          />
        </Box>
      </Box>

      <RightDrawer
        anchor={'right'}
        open={openFilterDrawer}
        onClose={() => setOpenFilterDrawer(false)}
        title="Filter"
        className="staff-leave-filter"
        content={
          <FilterComponent
            filters={filters}
            filterData={filterData}
            setFilterData={setFilterData}
            selectedFilters={selectedFilters}
            toggleFilter={toggleFilter}
            saveLayout={saveLayout}
            setOpenFilterDrawer={setOpenFilterDrawer}
            setSelectedFilters={setSelectedFilters}
            getFirstFourFilters={getFirstFourFilters}
            setSearchValue={setSearchValue}
            searchValue={setSearchValue}
            handleKeyPress={handleKeyPress}
          />
        }
      />
      <DialogBox
        open={deleteDialogOpen}
        handleClose={handleCloseDeleteDialog}
        title="Confirmation"
        className="delete-modal"
        dividerClass="delete-modal-divider"
        content={
          <DeleteModal
            handleCancel={handleCloseDeleteDialog}
            handleConfirm={() => deleteUser(deleteId)}
            text="Are you sure you want to delete this staff?"
          />
        }
      />
      <DialogBox
        open={joinChecklist}
        handleClose={() => {
          setJoiningChecklist(false);
        }}
        className="dialog-box-container download-staff-modal"
        title={'Download Staff'}
        content={
          <>
            <DownloadList
              searchValue={searchValue}
              filterData={filterDataApplied}
            />
          </>
        }
      />
      <DialogBox
        open={selectStaffModal}
        handleClose={() => {
          setSelectStaffModal(false);
        }}
        title={'Confirmation'}
        className="confirmation-modal "
        dividerClass="confirmation-modal-divider"
        content={
          <>
            <ConfirmationModal
              handleCancel={() => {
                const newSelecteds = userList?.map((n) => n?.id);
                setSelectedCat(newSelecteds);
                setSelectStaffModal(false);
              }}
              handleConfirm={() => {
                setSelectedCat('all');
                setSelectStaffModal(false);
              }}
              confirmText="Select All"
              cancelText="Select current"
              text={
                'Do you want to select every staff member or only the ones visible on this page?'
              }
            />
          </>
        }
      />
    </>
  );
}
