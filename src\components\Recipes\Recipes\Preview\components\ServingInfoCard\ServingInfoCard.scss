.serving-info-card {
  background-color: var(--color-success-opacity);
  border-radius: var(--border-radius-md);
  border: var(--border-width-xs) solid var(--color-light-gray);
  box-shadow: var(--box-shadow-xs);

  &__header {
    padding: var(--spacing-lg) var(--spacing-xxl);
    border-bottom: var(--border-width-xs) solid var(--color-light-gray);
  }

  &__title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-lg);
    color: var(--color-success);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }

  &__content {
    padding: var(--spacing-lg);
  }

  &__info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
  }

  &__item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-tiny);
  }

  &__label {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-slate-gray);
  }

  &__text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--text-color-black);
  }
}
