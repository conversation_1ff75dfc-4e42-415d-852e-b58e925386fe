// src/pages/user-limit-exceeded-dialog/index.jsx
import React from 'react';
import UserLimit from '../../../../../public/images/UserLimit.jpg';
import CustomButton from '@/components/UI/CustomButton';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { Box, Typography } from '@mui/material';
import '../StorageLimit/storagelimit.scss';

const UserLimitExceededDialog = ({ isOrgView, setRestrictedModal }) => {
  const router = useRouter();
  const handleUpgradePlan = () => {
    if (isOrgView) {
      setRestrictedModal(false);
      router.push('/org/organization?tab=2');
    } else {
      setRestrictedModal(false);
    }
  };
  const handleGoBack = () => {
    setRestrictedModal(false);
  };
  return (
    <Box className="user-limit-container storage-limit-container">
      {/* Dialog Overlay */}
      <Box className={`dialog-overlay visible`}>
        <Box className="dialog-container">
          <Box className={`dialog-content visible`}>
            {/* Dialog Content */}
            <Box className="dialog-body">
              {/* Image */}
              <Box className="dialog-image-container">
                <Box className="dialog-image-wrapper">
                  <Image
                    src={UserLimit}
                    alt="profile photo"
                    className="dialog-image"
                  />
                </Box>
              </Box>

              {/* Text Content */}
              <Box className="dialog-text">
                <Typography className="title-text fw600 dialog-message">
                  You've reached your user limit.
                </Typography>
                <Typography className="title-text dialog-description">
                  Upgrade your plan to add more team members or remove inactive
                  users to continue.
                </Typography>
                <Typography className="title-text dialog-description">
                  Contact support if you need assistance managing your team.
                </Typography>
              </Box>

              {/* Action Buttons */}
              <Box className="dialog-actions profile-incomplete-actions">
                <CustomButton
                  variant="contained"
                  title={isOrgView ? 'Upgrade Plan' : 'Contact Admin'}
                  fullWidth
                  onClick={handleUpgradePlan}
                />
                {isOrgView && (
                  <CustomButton
                    variant="outlined"
                    fullWidth
                    title="Go Back"
                    onClick={handleGoBack}
                  />
                )}
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default UserLimitExceededDialog;
