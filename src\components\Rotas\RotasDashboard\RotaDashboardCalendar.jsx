'use client';

import React from 'react';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import moment from 'moment';
import { Box, Tooltip, Typography } from '@mui/material';
import PersonIcon from '@mui/icons-material/Person';
import HelpIcon from '@mui/icons-material/Help';
import UserAvatar from '@/components/UI/Avatar/UserAvatar';

const RotaDashboardCalendar = ({
  filterData,
  handleDateClick,
  shiftList,
  viewAccessOnly,
}) => {
  const filteredEvents = shiftList?.length > 0 ? shiftList : [];

  // render day shift event
  const renderDashboardDayShiftContent = (eventInfo) => {
    const eventData = eventInfo.event.extendedProps;

    const fullName = eventData?.user
      ? eventData?.user?.user_full_name
      : 'No User Assigned';

    return (
      <div className="event-calendar-shifts-day-wrap">
        <div className="event-calendar-shifts-day-details">
          <Tooltip
            title={
              <div>
                <p className="p12">
                  {eventData?.isOpen ? 'open shift' : fullName}
                </p>
              </div>
            }
            arrow
            classes={{
              tooltip:
                'calendar-event-list-tooltip-custom calendar-event-other-details-tooltip',
            }}
          >
            <div className="event-calendar-shifts-avatar">
              <UserAvatar
                name={eventData?.isOpen ? '?' : fullName}
                src={
                  eventData?.user?.user_avatar_link
                    ? eventData?.user?.user_avatar_link
                    : ''
                }
              />
            </div>
          </Tooltip>
          <div className="event-calendar-shifts-day-user-details">
            <p className="event-calendar-shifts-day-time p14 fw600">
              {moment(eventInfo?.event?.start).format('HH:mm') || ''}
              {eventInfo?.event?.end ? ' - ' : ''}
              {moment(eventInfo?.event?.end).format('HH:mm') || ''}
            </p>
            <p className="event-calendar-shifts-day-role-location p14 fw600">
              <span>
                {eventData?.role?.role_name
                  ? eventData?.role?.role_name
                  : 'No Role Assigned'}{' '}
                ({eventData?.branch?.branch_name})
              </span>
            </p>
          </div>
        </div>
      </div>
    );
  };
  // render month shift event
  const renderDashboardMonthShiftContent = (eventInfo) => {
    const dateKey = moment(eventInfo.date).format('YYYY-MM-DD');

    // Filter events for the given date
    const eventsForDate = filteredEvents?.filter((event) =>
      moment(event.start).isSame(dateKey, 'day')
    );

    // Initialize role details and counts
    const roleDetails = {};
    const roleCounts = {};

    // Process shifts
    eventsForDate.forEach(({ shifts }) => {
      shifts?.forEach((shift) => {
        if (shift.isOpen === 1) return;

        const roleName = _.get(shift, 'role.role_name', 'No Role');
        const roleId = _.get(shift, 'role.id', null);
        const locationName = _.get(shift, 'branch.branch_name', '');
        const isRoleIncluded = filterData?.selectedRoles.includes(roleId);
        const start_time = moment(shift?.startTime).utc().format('HH:mm');
        const end_time = moment(shift?.endTime).utc().format('HH:mm');

        const userName = shift?.user
          ? `${_.get(shift, 'user.user_first_name', '')} ${_.get(
              shift,
              'user.user_last_name',
              ''
            )} <span>(${start_time || ''}${
              shift?.endTime ? ' - ' : ''
            } ${end_time || ''})</span>`
          : '';

        // Count shifts per role
        roleCounts[roleName] = (roleCounts[roleName] || 0) + 1;

        // Initialize role details if not present
        if (!roleDetails[roleName]) {
          roleDetails[roleName] = {
            role_id: roleId,
            isIncluded: isRoleIncluded,
            locations: {},
          };
        }

        // Add user to the respective location under the role
        roleDetails[roleName].locations[locationName] = [
          ...(roleDetails[roleName].locations[locationName] || []),
          userName,
        ];
      });
    });

    // Count shifts by open status
    const shiftCounts = _.countBy(_.flatMap(eventsForDate, 'shifts'), 'isOpen');

    return (
      <Box sx={{ position: 'relative' }}>
        <div className="fc-daygrid-day-number">{eventInfo.dayNumberText}</div>
        {viewAccessOnly ? (
          <Box className="event-shifts-count-wrap">
            <Box className="event-count-wrap">
              {_.map(roleCounts, (count, role) => (
                <Tooltip
                  key={role}
                  title={
                    <div>
                      <p className="calendar-event-list-tooltip-role p12">
                        {role}
                      </p>
                      {_.map(
                        roleDetails[role]?.locations,
                        (users, location) => (
                          <div
                            className="calendar-event-list-tooltip-user-details"
                            key={location}
                          >
                            <p className="calendar-event-list-tooltip-location p12">
                              {location}
                            </p>
                            <ul className="calendar-event-list-tooltip-users p10">
                              {_.map(users, (user, index) => (
                                <li
                                  key={index}
                                  dangerouslySetInnerHTML={{ __html: user }}
                                />
                              ))}
                            </ul>
                          </div>
                        )
                      )}
                    </div>
                  }
                  arrow
                  classes={{
                    tooltip: `calendar-event-list-tooltip-custom ${roleDetails[role]?.role_id ? '' : 'calendar-event-list-no-role-tooltip-custom'}`,
                  }}
                >
                  <Box
                    className="event-count"
                    style={{
                      opacity:
                        filterData?.selectedRoles?.length === 0
                          ? 1
                          : roleDetails[role]?.isIncluded
                            ? 1
                            : 0.5,
                    }}
                  >
                    {count}
                  </Box>
                </Tooltip>
              ))}
            </Box>

            <Box className="event-users-open-shifts-count-wrap">
              {shiftCounts[0] ? (
                <Tooltip
                  title={
                    <div>
                      <p className="p12">
                        {shiftCounts[0]} employee{shiftCounts[0] > 1 ? 's' : ''}
                      </p>
                    </div>
                  }
                  arrow
                  classes={{
                    tooltip:
                      'calendar-event-list-tooltip-custom calendar-event-other-details-tooltip',
                  }}
                >
                  <Box className="calendar-event-list-total-users">
                    <PersonIcon />{' '}
                    <span className="calendar-event-list-emp-total">
                      {shiftCounts[0]}
                    </span>
                  </Box>
                </Tooltip>
              ) : null}

              {shiftCounts[1] ? (
                <Tooltip
                  title={
                    <div>
                      <p className="p12">
                        {shiftCounts[1]} open shift
                        {shiftCounts[1] > 1 ? 's' : ''}
                      </p>
                    </div>
                  }
                  arrow
                  classes={{
                    tooltip:
                      'calendar-event-list-tooltip-custom calendar-event-other-details-tooltip',
                  }}
                >
                  <Box className="calendar-event-list-open-shifts">
                    <HelpIcon />{' '}
                    <span className="calendar-event-list-emp-total">
                      {shiftCounts[1]}
                    </span>
                  </Box>
                </Tooltip>
              ) : null}
            </Box>
          </Box>
        ) : (
          <Box className="event-shifts-details-container">
            {viewAccessOnly !== ''
              ? eventsForDate?.map((item) => {
                  return item?.shifts?.map((shift, i) => {
                    return (
                      <Box className="event-shifts-details-wrap" key={i}>
                        <Box className="event-shifts-details-header">
                          <Typography
                            className="event-shifts-details-title"
                            title={shift?.branch?.branch_name}
                          >
                            {shift?.branch?.branch_name}
                          </Typography>
                        </Box>
                        <Box className="event-shifts-details-body">
                          <Typography className="event-shifts-details-time">
                            {moment(shift?.startTime).format('HH:mm') || ''}
                            {shift?.endTime ? ' - ' : ''}
                            {moment(shift?.endTime).format('HH:mm') || ''}
                          </Typography>
                          <Typography
                            className="event-shifts-details-role"
                            title={shift?.role?.role_name || 'No Role'}
                          >
                            {shift?.role?.role_name || 'No Role'}
                          </Typography>
                        </Box>
                      </Box>
                    );
                  });
                })
              : null}
          </Box>
        )}
      </Box>
    );
  };

  return (
    <FullCalendar
      key={`${filterData?.dayMonth} ${filterData?.location} ${filterData?.currentDate} ${filterData?.sortBy}`}
      plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
      initialView={
        filterData?.dayMonth === 'day' ? 'timeGridDay' : 'dayGridMonth'
      }
      initialDate={filterData?.currentDate || moment().format('YYYY-MM-DD')}
      nowIndicator={true}
      headerToolbar={false}
      events={filteredEvents}
      height="auto"
      dayCellContent={
        filterData?.dayMonth === 'day'
          ? undefined
          : (arg) => renderDashboardMonthShiftContent(arg)
      }
      eventContent={
        filterData?.dayMonth === 'day'
          ? (arg) => renderDashboardDayShiftContent(arg)
          : undefined
      }
      dateClick={(info) => handleDateClick(info.dateStr, 'day')}
      allDaySlot={false}
      slotEventOverlap={false}
      slotMinTime="09:00:00"
    />
  );
};

export default RotaDashboardCalendar;
