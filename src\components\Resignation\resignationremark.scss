@import '@/styles/variable.scss';

.resignation-section {
  background-color: var(--color-white);
  padding: var(--spacing-xxl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-xs);
  position: relative;
  height: 100%;
  .User-verify-details {
    display: flex;
    justify-content: space-between;
    @media (max-width: 1200px) {
      display: flex;
      flex-direction: column;
      .verified-user {
        margin-top: var(--spacing-lg);
        justify-content: flex-start;
      }
    }
    @media (max-width: 599px) {
      display: flex;
      flex-direction: column;
      .verified-user {
        margin-top: var(--spacing-lg);
        justify-content: flex-start;
      }
    }
    .deatils {
      margin-left: var(--spacing-none);
    }
    .w100 {
      width: 100%;
    }
    .mt8 {
      margin-top: var(--spacing-sm);
    }
    .failed-status {
      padding: var(--spacing-none) var(--spacing-sm);
      color: var(--text-error);
    }
    .draft-status {
      padding: var(--spacing-none) var(--spacing-sm);
      color: var(--text-periwinkle-blue);
    }
    .ongoing-status {
      padding: var(--spacing-none) var(--spacing-sm);
      color: var(--text-periwinkle-blue);
    }
    .active-onboarding-status {
      padding: var(--spacing-none) var(--spacing-sm);
      color: var(--text-green);
    }
    .success-status {
      padding: var(--spacing-none) var(--spacing-sm);
      color: var(--text-olive-green);
    }
  }
  .resignation-remarks {
    display: block;
  }
  .resignation-remarks-staff {
    display: block;
    margin-bottom: var(--spacing-sm);
    .remark-section {
      max-height: 200px;
      overflow-y: scroll;
      padding: var(--spacing-md) var(--spacing-none);
    }
    .remark-own-section {
      padding: var(--spacing-md) var(--spacing-none);
    }
  }
  .actions-width {
    width: 50px;
  }
  .joining-checklist-details {
    .joining-checklist {
      height: 300px;
      overflow-x: scroll;
    }
    .disabled-joining-checklist {
      .Mui-disabled {
        opacity: var(--opacity-10);
        cursor: not-allowed;
        .MuiTypography-root {
          color: var(--text-color-black) !important;
        }
      }
      .MuiTypography-root {
        opacity: var(--opacity-10);
        color: var(--text-color-black) !important;
        cursor: not-allowed;
      }
    }
  }
}

.resignation-remarks-staff {
  display: block;
  margin-bottom: var(--spacing-sm);
  .remark-section {
    max-height: 200px;
    overflow-y: scroll;
    padding: var(--spacing-xsm) var(--spacing-xxs);
  }
  .remark-own-section {
    padding: var(--spacing-md) var(--spacing-xxs);
  }

  .remark-text {
    width: 100%;
    // box-shadow: var(--box-shadow-xs);
    // border: var(--normal-sec-border);
    // border-radius: var(--border-radius-md);
    // padding: var(--spacing-sm);
    background-color: var(--color-white);
    display: flex;
    gap: var(--spacing-sm);
    align-items: baseline;
  }
}

.user-name {
  text-transform: capitalize;
}

.leaving-checklist-dialog {
  .disabled-joining-checklist {
    .Mui-disabled {
      opacity: 1;
      cursor: not-allowed;
      .MuiTypography-root {
        color: var(--text-color-black);
      }
    }
    .MuiCheckbox-root {
      .MuiSvgIcon-root {
        fill: var(--icon-color-slate-gray);
      }
    }
    .MuiTypography-root {
      opacity: var(--opacity-10);
      color: var(--text-color-black);
      cursor: not-allowed;
    }
  }
}
.resignation-page-container {
  background-color: var(--color-white);
  padding: var(--spacing-xxl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-xs);
  .resignation-form-container {
    width: 50%;
    @media (max-width: 1023px) {
      width: 100%;
    }
  }
}
