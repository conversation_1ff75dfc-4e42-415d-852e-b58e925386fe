import React from 'react';
import { Box } from '@mui/material';
import { generateMetadata } from '@/helper/common/commonFunctions';
import ApplyLeave from '@/components/Leave/ApplyLeave/index';

export const metadata = generateMetadata({
  pageTitle: 'Apply Leave',
});

const ApplyLeavePage = ({ params }) => {
  return (
    <Box className="main-page-container">
      <ApplyLeave params={params} />
    </Box>
  );
};

export default ApplyLeavePage;
