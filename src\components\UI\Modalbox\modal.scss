@import '../../../styles/variable.scss';

.dialog-box {
  z-index: 1300 !important;
  ::-webkit-scrollbar {
    width: 0px;
  }
  .MuiDialog-paperScrollPaper {
    border-radius: var(--border-radius-lg);
    width: 100%;
    margin: 0;
    max-width: 524px;
    padding: 40px;
    box-shadow: none;
    position: fixed;
    top: 72px;
    bottom: 72px;
    left: 50%;
    transform: translateX(-50%);
    max-height: calc(100vh - 144px - var(--banner-height));
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    height: max-content;
    // min-height: fit-content;
    // min-height: max-content;
    @media (max-width: 899px) {
      max-width: calc(100% - 64px);
    }
    @media (max-width: 399px) {
      max-width: calc(100% - 24px) !important;
    }
  }
  .MuiBackdrop-root {
    background: rgba(28, 29, 34, 0.7);
    backdrop-filter: blur(5px);
  }
  .MuiDialogTitle-root {
    padding: 0;
  }
  .dialog-title-wrap {
    justify-content: space-between;
    svg {
      font-size: var(--font-size-2xl);
      border: var(--normal-sec-border);
      border-radius: var(--border-radius-sm);
      padding: var(--spacing-xxs);
      background: var(--color-secondary);
      path {
        fill: var(--icon-color-light-dark);
      }
    }
  }
  .dialog-content {
    padding: 0px 0 0 0;
    color: $color-Dark-80;
    overflow-x: hidden;
  }

  .divider {
    border-bottom: 1px solid $color-Primary-10;
    margin: 20px 0;
  }
  .MuiPaper-root {
    @media (max-width: 899px) {
      padding: 30px;
    }
    @media (max-width: 499px) {
      padding: 20px;
    }
  }
}
.staff-dialogbox {
  .MuiDialog-paperScrollPaper {
    max-width: 824px;
    @media (max-width: 899px) {
      max-width: calc(100% - 64px);
    }
    @media (max-width: 399px) {
      max-width: calc(100% - 24px) !important;
    }
  }
}
.body {
  overflow: hidden;
}
.resignation-dialogbox {
  .MuiDialog-paperScrollPaper {
    max-width: 1224px;
    @media (max-width: 1200px) {
      max-width: 824px;
    }
    @media (max-width: 899px) {
      max-width: calc(100% - 64px);
    }
    @media (max-width: 399px) {
      max-width: calc(100% - 24px) !important;
    }
  }
}
.media-view-dialogbox {
  .MuiPaper-root {
    max-width: 1224px;
    // @media (max-width: 1200px) {
    //   max-width: 824px;
    // }
    @media (max-width: 1200px) {
      max-width: 824px;
    }
    @media (max-width: 899px) {
      max-width: calc(100% - 64px);
    }
    @media (max-width: 399px) {
      max-width: calc(100% - 24px) !important;
    }
  }
}
