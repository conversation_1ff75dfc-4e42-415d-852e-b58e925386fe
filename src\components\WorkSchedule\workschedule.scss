@import '@/styles/variable.scss';

.work-schedule-wrap {
  .work-schedule-select-box-wrap {
    display: grid;
    grid-template-columns: repeat(7, 1fr) !important;
    gap: 15px;
    margin-bottom: 20px;

    @media (max-width: 1199px) {
      grid-template-columns: repeat(6, 1fr) !important;
      row-gap: 30px;
    }
    @media (max-width: 1023px) {
      grid-template-columns: repeat(5, 1fr) !important;
    }
    @media (max-width: 991px) {
      grid-template-columns: repeat(4, 1fr) !important;
    }
    @media (max-width: 767px) {
      grid-template-columns: repeat(3, 1fr) !important;
    }
    @media (max-width: 575px) {
      grid-template-columns: repeat(1, 1fr) !important;
    }
  }
}
