@import '@/app/_globals.scss';

.history-wrap {
    .ticket-history-label{
        width: 100%;
        max-width: max-content;
    }
    .filter-wrap {
        padding: var(--spacing-xs) var(--spacing-5xl);
        border-bottom: var(--normal-sec-border);

        .ticket-history {
            font-size: var(--font-size-md);
            color: var(--text-color-slate-gray);
            width: 100%;
            max-width: max-content;
        }

        .filter-select {
            .MuiInputBase-root {
                font-size: var(--font-size-sm);
            }

            .MuiSelect-select {
                padding: var(--spacing-none) var(--spacing-none) var(--spacing-none) var(--spacing-xs);
                width: 22px;
                text-overflow: clip;
            }

            fieldset {
                width: 50px;
                border: none;
            }

            .MuiSvgIcon-root {
                display: none;
            }

            .down-arrow-wrap {
                height: 18px;
                width: 18px;
            }
        }

        .filter-by-wrap {
            font-size: var(--font-size-sm);
            color: var(--text-color-slate-gray);
            max-width: max-content;
        }

        .filter-by-select {

            .select__control {
                border: none;
                padding: var(--spacing-none);

                .select__value-container {
                    padding: var(--spacing-none);

                    .select__single-value {
                        font-size: 15px;
                        margin-left: var(--spacing-base);
                        margin-top: var(--spacing-xxs);
                    }

                    .select__input-container {
                        margin: var(--spacing-none) var(--spacing-none) var(--spacing-none) var(--spacing-base);
                    }

                    .select__placeholder {
                        padding: var(--spacing-xxs) var(--spacing-none) var(--spacing-none) var(--spacing-base);
                        font-family: var(--font-family-primary);
                    }
                }

                .select__indicators {
                    .select__indicator {
                        svg {
                            color: var(--text-color-black);
                        }
                    }
                }
            }
        }
    }
}