import { useState, useEffect, useRef } from 'react';
import { Avatar } from '@mui/material';
import { fetchFromStorage } from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import { LazyLoadImage } from 'react-lazy-load-image-component';
import ReactPlayer from 'react-player';
import 'react-lazy-load-image-component/src/effects/blur.css';
import ImageIcon from '@mui/icons-material/Image';
// import imageicon from '../../../../public/images/imageicon.png';

const fetchImageWithHeaders = async (imageUrl, headers) => {
  try {
    const response = await fetch(imageUrl, { headers });

    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.statusText}`);
    }

    const blob = await response.blob();
    return URL.createObjectURL(blob); // Return Blob URL
  } catch (error) {
    throw error; // Re-throw error for handling
  }
};
function HeaderImage({
  imageUrl,
  type,
  pagetitle,
  Content,
  IsExternal,
  ...props
}) {
  const [imageSrc, setImageSrc] = useState('');
  const token =
    typeof window !== 'undefined' &&
    fetchFromStorage(identifiers.AUTH_DATA)?.token;
  const headers = { Authorization: `Bearer ${token}` };

  useEffect(() => {
    if (!IsExternal) {
      let isMounted = true; // Prevent setting state on unmounted component

      const loadImage = async () => {
        try {
          // let replaceURL = imageUrl.replace('uploads', 'files');
          const blobUrl = await fetchImageWithHeaders(imageUrl, headers);
          if (isMounted) setImageSrc(blobUrl);
        } catch (err) {
          if (isMounted) console.error(err);
        }
      };

      imageUrl && loadImage();

      return () => {
        isMounted = false;
        if (imageSrc) URL.revokeObjectURL(imageSrc); // Cleanup Blob URL
      };
    }
  }, [imageUrl]);
  const videoRef = useRef(null);
  const [allowedTime, setAllowedTime] = useState(0);

  useEffect(() => {
    if (!props?.controls && type === 'video') {
      const video = videoRef.current;

      if (!video) return;

      // Prevent skipping forward
      const handleTimeUpdate = () => {
        if (video.currentTime > allowedTime + 0.3) {
          video.currentTime = allowedTime;
        } else {
          setAllowedTime(video.currentTime);
        }
      };

      // Disable keyboard seeking (arrow keys, etc.)
      const handleKeyDown = (e) => {
        const keysToBlock = ['ArrowRight', 'ArrowLeft', 'Home', 'End'];
        if (keysToBlock.includes(e.key)) {
          e.preventDefault();
        }
      };

      video.addEventListener('timeupdate', handleTimeUpdate);
      window.addEventListener('keydown', handleKeyDown);

      return () => {
        video.removeEventListener('timeupdate', handleTimeUpdate);
        window.removeEventListener('keydown', handleKeyDown);
      };
    } else {
      setAllowedTime(0);
    }
  }, [allowedTime, imageSrc]);
  function getMimeTypeFromUrl(url) {
    const extension = url?.split('.')?.pop()?.toLowerCase();
    const mimeTypes = {
      jpg: 'image/jpeg',
      jpeg: 'image/jpeg',
      png: 'image/png',
      gif: 'image/gif',
      pdf: 'application/pdf',
      html: 'text/html',
      txt: 'text/plain',
      // Add more extensions and their MIME types as needed
    };
    return mimeTypes[extension] || 'application/octet-stream'; // Default MIME type
  }
  return (
    <>
      {type === 'lazyload' ? (
        <>
          {!imageUrl ? (
            <ImageIcon className="image-placeholder" />
          ) : (
            <LazyLoadImage
              src={
                IsExternal && imageUrl ? imageUrl : imageUrl ? imageSrc : null
              }
              {...props}
              effect={imageUrl ? 'blur' : null}
            />
          )}
        </>
      ) : type === 'avtar' ? (
        <Avatar
          src={imageUrl ? imageSrc : null} // Blob Authorization URL
          // src={imageUrl}
          {...props}
        />
      ) : type === 'url' && Content ? (
        <div
          onClick={() => {
            const isPDF = getMimeTypeFromUrl(imageUrl);
            if (imageSrc && pagetitle && isPDF === 'application/pdf') {
              const htmlContent = `
                <!DOCTYPE html>
                <html>
                <head>
                    <title>${pagetitle}</title> 
                    </head>
                <body style="margin:0">
                   <iframe src="${imageSrc}" style="width:100%; height:100vh; border:none;"></iframe>
                </body>
                </html>
                `;
              const wrapperBlob = new Blob([htmlContent], {
                type: 'text/html',
              });
              const wrapperUrl = URL.createObjectURL(wrapperBlob);
              //imageSrc //imageSrc.substring(imageSrc.lastIndexOf('/') + 1)
              window.open(wrapperUrl, '_blank'); // Open Blob URL in a new tab //imageSrc
            } else if (imageSrc) {
              //imageSrc
              window.open(imageSrc, '_blank'); // Open Blob URL in a new tab //imageSrc
            }
          }}
          {...props}
        >
          {Content}
        </div>
      ) : type === 'video' ? (
        <video
          ref={videoRef}
          src={IsExternal && imageUrl ? imageUrl : imageSrc}
          {...props}
          controls
          controlsList={!props?.controls && 'nodownload noplaybackrate'}
          disablePictureInPicture={!props?.controls}
          onContextMenu={(e) => !props?.controls && e.preventDefault()}
        />
      ) : type === 'audio' ? (
        <ReactPlayer
          className="react-player-audio"
          url={IsExternal && imageUrl ? imageUrl : imageSrc}
          playing={false}
          controls={true}
          config={{
            file: {
              attributes: { controlsList: 'nodownload' },
            },
          }}
        />
      ) : (
        <></>
      )}
    </>
  );
}

export default HeaderImage;
