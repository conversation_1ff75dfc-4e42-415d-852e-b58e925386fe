'use client';
import React, { useEffect, useState } from 'react';
import {
  Box,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  IconButton,
  Tooltip,
  Typography,
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import { CustomTextField } from '@/components/UI/CommonField';
import CustomButton from '@/components/UI/button';
import AddIcon from '@mui/icons-material/Add';
import SearchIcon from '@mui/icons-material/Search';
import PaymentIdentifier from '..';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { ORG_URLS } from '@/helper/constants/urls';
import { setApiMessage } from '@/helper/common/commonFunctions';
import moment from 'moment';
import './paymentprohistory.scss';

export default function PaymentProviderHistory() {
  const [search, setSearch] = useState('');
  const [providerData, setProviderData] = useState([]);
  const [open, setOpen] = useState(false);
  // const [loader, setLoader] = useState(false);
  const [page] = useState(1);
  // const [totalCount, setTotalCount] = useState(0);
  // const [rowsPerPage, setRowsPerPage] = useState(10);
  const [userId, setUserId] = useState('');

  // Formats a date string into a more readable format.
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const day = String(date.getUTCDate()).padStart(2, '0');
    const month = String(date.getUTCMonth() + 1).padStart(2, '0');
    const year = date.getUTCFullYear();
    const hours = String(date.getUTCHours()).padStart(2, '0');
    const minutes = String(date.getUTCMinutes()).padStart(2, '0');
    const seconds = String(date.getUTCSeconds()).padStart(2, '0');
    return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
  };

  // Fetches the payment provider details from the API.
  const getPaymetProviderDetails = async (search) => {
    //page, Rpp
    // setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        ORG_URLS?.GET_ALL_PAYMENT_PROVIDER + `?searchString=${search}` //&page=${page}&size=${Rpp ? Rpp : rowsPerPage}
      );
      if (status === 200) {
        setProviderData(data?.data || []);
        // setTotalCount(data?.data?.length);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // Handles the delete action for a provider.
  const handleDelete = async (planId) => {
    try {
      const { status, data } = await axiosInstance.put(
        `${ORG_URLS.UPDATE_PAYMENT_PROVIDER}/${planId?.id}`,
        { provider_status: 'deleted' }
      );

      if (status === 200) {
        if (data?.status) {
          setApiMessage('success', data?.message);
          getPaymetProviderDetails(search, page);
        } else {
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // useEffect hook to fetch provider details when the component mounts
  useEffect(() => {
    getPaymetProviderDetails('', page);
  }, []);

  // Opens the modal for adding/editing a provider.
  const handleOpen = () => {
    setOpen(true);
  };

  // Closes the modal for adding/editing a provider.
  const handleClose = () => {
    setOpen(false);
    setUserId('');
  };

  // Handles the edit action for a provider.
  const handleEdit = (id) => {
    setUserId(id);
    handleOpen();
  };

  // Updates the current page for pagination.
  // const onPageChange = (newPage) => {
  //   setPage(newPage);
  //   getPaymetProviderDetails(searchQuery, newPage);
  // };

  // Updates the number of rows displayed per page and resets to the first page.
  // const OnRowPerPage = (newRowsPerPage) => {
  //   setRowsPerPage(newRowsPerPage);
  //   setPage(1);
  //   getPaymetProviderDetails(searchQuery, 1, newRowsPerPage);
  // };

  return (
    <Box className="payment-pro-wrap">
      <Box className="org-header">
        {/* <Box className="header-wrap">
          <Typography variant="h4" className="header-text p16 fw600">
              Provider List
          </Typography>
          <Typography component="p" className="header-sub-text p14">
                       View, create, and manage payment providers easily
          </Typography>
        </Box> */}
        <Box />
        <Box className="d-flex gap-5 filter-input-wrap justify-end">
          <CustomTextField
            InputLabelProps={{ shrink: true }}
            size="small"
            placeholder="Search by provider Name"
            value={search}
            onChange={(e) => setSearch(e?.target?.value)}
            className="filter-input"
            variant="filled"
          />
          <CustomButton
            className="p16 search-btn"
            type="submit"
            fontWeight="600"
            variant="contained"
            background="#39596e"
            backgroundhover="#39596e"
            colorhover="#FFFFFF"
            leftIcon={<SearchIcon className="search-icon" />}
            title="search"
            onClick={() => {
              getPaymetProviderDetails(search, 1);
            }}
          />
          <Box className="create-sub-plan-btn-wrap">
            <CustomButton
              className="p16 create-sub-plan-btn"
              type="submit"
              fontWeight="600"
              variant="contained"
              background="#39596e"
              backgroundhover="#39596e"
              colorhover="#FFFFFF"
              leftIcon={<AddIcon className="add-icon" />}
              title="Add Provider"
              onClick={() => handleOpen()}
            />
          </Box>
        </Box>
      </Box>

      <Box className="divider" />

      {providerData?.length > 0 ? (
        <Box className="identifier-table-container">
          <Table className="identifier-table">
            <TableHead>
              <TableRow>
                <TableCell className="p14 fw600">ID</TableCell>
                <TableCell className="p14 fw600">Provider Name</TableCell>
                <TableCell className="p14 fw600">Provider Identifier</TableCell>
                <TableCell className="p14 fw600">Provider Status</TableCell>
                {/* <TableCell>Created By</TableCell>
                <TableCell>Updated By</TableCell> */}
                <TableCell className="p14 fw600">Created At</TableCell>
                <TableCell align="center" className="p14 fw600">
                  Action
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody className="table-body-wrap">
              {providerData?.map((provider) => (
                <TableRow
                  key={provider?.id}
                  title={`Updated At: ${formatDate(provider?.updatedAt)}`}
                >
                  <TableCell className="p14">{provider?.id}</TableCell>
                  <TableCell className="provider-name p14">
                    {provider?.provider_name}
                  </TableCell>
                  <TableCell className="p14">
                    {provider?.provider_identifier}
                  </TableCell>
                  <TableCell align="center">
                    <Typography
                      className={`max-content text-capital p12 fw600 ${
                        provider?.provider_status === 'active'
                          ? 'active-onboarding'
                          : provider?.provider_status === 'disabled'
                            ? 'cancelled'
                            : 'failed'
                      }`}
                    >
                      {provider?.provider_status}
                    </Typography>
                  </TableCell>
                  {/* <TableCell>{provider?.created_by}</TableCell>
                  <TableCell>{provider.updated_by}</TableCell> */}
                  <TableCell className="p14">
                    {/* {formatDate(provider.createdAt)} */}
                    {moment(provider?.createdAt).format('DD/MM/YYYY hh:mm A')}
                  </TableCell>
                  <TableCell align="center" className="d-flex justify-center ">
                    <Box className="action-icons">
                      <IconButton
                        onClick={() => handleEdit(provider?.id)}
                        aria-label="edit"
                        className="icon"
                      >
                        <Tooltip
                          title={<Typography>Edit</Typography>}
                          arrow
                          placement="bottom"
                          classes={{ tooltip: 'info-tooltip-container' }}
                        >
                          <EditIcon className="edit-icon" />
                        </Tooltip>
                      </IconButton>
                      {provider?.provider_status !== 'deleted' ? (
                        <IconButton
                          onClick={() => handleDelete(provider)}
                          aria-label="delete"
                          className="icon"
                        >
                          <Tooltip
                            title={<Typography>Delete</Typography>}
                            arrow
                            placement="bottom"
                            classes={{ tooltip: 'info-tooltip-container' }}
                          >
                            <DeleteIcon className="delete-icon" />
                          </Tooltip>
                        </IconButton>
                      ) : (
                        <></>
                      )}
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          {/* <CustomPagination
            currentPage={page}
            onPageChange={onPageChange}
            totalCount={totalCount}
            rowsPerPage={rowsPerPage}
            OnRowPerPage={OnRowPerPage}
          /> */}
        </Box>
      ) : (
        <Box className="mt32">
          <Typography className="text-align h6 color-gray">
            No data found
          </Typography>
        </Box>
      )}
      <PaymentIdentifier
        open={open}
        handleClose={handleClose}
        handleOpen={handleOpen}
        userId={userId}
        setUserId={setUserId}
        getPaymetProviderDetails={getPaymetProviderDetails}
        search={search}
      />
    </Box>
  );
}

// path : /create-sub-plan
//       /sub-plan-history
//       /org-list
//       /payment-identifier
//       /payment-provider-history
