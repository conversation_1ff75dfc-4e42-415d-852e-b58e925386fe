
.attachment-wrap {
  max-height: calc(100vh - 200px - var(--banner-height));
  overflow-y: auto;
  padding: var(--spacing-md);

  .no-history-message {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    text-align: center;
    color: var(--text-color-slate-gray);
  }

  .attachment-history-wrap {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);

    .history-entry-card {
      display: flex;
      gap: var(--spacing-md);
      position: relative;

      // Timeline connector
      .timeline-connector {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        min-width: 40px;

        .timeline-dot {
          width: 40px;
          height: 40px;
          border-radius: var(--border-radius-full);
          background: var(--color-white);
          border: 2px solid var(--color-primary);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 2;
          box-shadow: var(--shadow-sm);

          .action-icon {
            width: 18px;
            height: 18px;
            color: var(--color-primary);

            &.status-icon {
              color: var(--color-success);
            }

            &.note-icon {
              color: var(--color-info);
            }

            &.edit-icon {
              color: var(--color-warning);
            }

            &.default-icon {
              color: var(--color-slate-gray);
            }
          }
        }

        .timeline-line {
          width: 2px;
          flex: 1;
          background: var(--color-border);
          margin-top: var(--spacing-xs);
          min-height: 20px;
        }
      }

      // Content card
      .entry-content-card {
        flex: 1;
        background: var(--color-white);
        border: 1px solid var(--color-border);
        border-radius: var(--border-radius-md);
        padding: var(--spacing-md);
        box-shadow: var(--shadow-xs);
        transition: all 0.2s ease;

        &:hover {
          box-shadow: var(--shadow-sm);
          border-color: var(--color-primary-light);
        }

        .entry-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: var(--spacing-md);

          .user-info {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);

            .history-user-avatar {
              width: 32px;
              height: 32px;
            }

            .user-details {
              display: flex;
              flex-direction: column;
              gap: var(--spacing-xxs);

              .user-name {
                color: var(--text-color-black);
                line-height: 1.2;
              }

              .action-type {
                color: var(--text-color-slate-gray);
                font-size: var(--font-size-sm);
                line-height: 1.2;
              }
            }
          }

          .timestamp-info {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: var(--spacing-xxs);

            .date-time,
            .time-only {
              color: var(--text-color-slate-gray);
              font-size: var(--font-size-sm);
              line-height: 1.2;
            }
          }
        }

        .entry-body {
          display: flex;
          flex-direction: column;
          gap: var(--spacing-sm);

          .note-section,
          .status-section {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);

            .d-flex {
              display: flex;
            }

            .align-center {
              align-items: center;
            }

            .gap-xs {
              gap: var(--spacing-xs);
            }

            .note-icon,
            .status-icon {
              width: 16px;
              height: 16px;
              color: var(--color-info);
            }

            .status-icon {
              color: var(--color-success);
            }

            .note-label,
            .status-label {
              color: var(--text-color-slate-gray);
              font-size: var(--font-size-sm);
            }

            .note-content {
              color: var(--text-color-black);
              padding-left: var(--spacing-lg);
              line-height: 1.4;
            }

            .status-chip {
              align-self: flex-start;
              margin-left: var(--spacing-lg);
              font-weight: var(--font-weight-medium);
            }
          }
        }
      }
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    padding: var(--spacing-sm);

    .attachment-history-wrap {
      gap: var(--spacing-md);

      .history-entry-card {
        .timeline-connector {
          min-width: 32px;

          .timeline-dot {
            width: 32px;
            height: 32px;

            .action-icon {
              width: 16px;
              height: 16px;
            }
          }
        }

        .entry-content-card {
          padding: var(--spacing-sm);

          .entry-header {
            flex-direction: column;
            gap: var(--spacing-sm);
            align-items: flex-start;

            .timestamp-info {
              align-items: flex-start;
            }
          }
        }
      }
    }
  }
}
