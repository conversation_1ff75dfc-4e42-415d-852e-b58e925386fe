.attachment-wrap {
  max-height: calc(100vh - 200px - var(--banner-height));
  overflow: scroll;

  .no-history-message {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    text-align: center;
    color: var(--text-color-slate-gray);
  }

  .attachment-history-wrap {
    position: relative;

    .devider-wrap {
      padding: var(--spacing-none) var(--spacing-lg) var(--spacing-none) var(--spacing-5xl);
    }

    .attachment-item {
      .header-date-wrap {
        padding: var(--spacing-base) var(--spacing-none);
        position: sticky;
        top: var(--spacing-none);
        background: var(--color-white);

        .header-date {
          font-weight: var(--font-weight-medium);
        }

        .calender-icon {
          background-color: var(--color-white);
          position: absolute;
          left: -24px;
          height: 15px;
          width: 15px;
        }
      }

      .name-text-wrap {
        .file-status-wrap {
          font-weight: var(--font-weight-medium);
        }

        .circle-wrap {
          position: absolute;
          left: 49px;
          height: 5px;
          width: 5px;
        }
      }

      .file-name {
        font-size: var(--font-size-base);

        .content-text {
          color: var(--text-color-slate-gray);
          display: inline-block;
          padding-right: var(--spacing-md);
          font-weight: var(--font-weight-medium);
          font-size: var(--font-size-base);
        }
      }

      .time-wrap {
        font-size: var(--font-size-base);
        color: var(--color-dark-50);
      }

      .attachment-type-wrap {
        font-size: var(--font-size-base);

        .attachment-type-text {
          display: inline-block;
          padding-right: var(--spacing-md);
          color: var(--text-color-slate-gray);
          font-weight: var(--font-weight-medium);
          font-size: var(--font-size-base);
        }
      }
    }
  }
}