
.attachment-wrap {
  max-height: calc(100vh - 200px - var(--banner-height));
  overflow-y: auto;
  padding: var(--spacing-md);

  .no-history-message {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    text-align: center;
    color: var(--text-color-slate-gray);
  }

  .attachment-history-wrap {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);

    .history-entry-card {
      display: flex;
      gap: var(--spacing-md);
      position: relative;

      // Timeline connector - Enhanced
      .timeline-connector {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        min-width: 48px;

        .timeline-dot {
          width: 48px;
          height: 48px;
          border-radius: var(--border-radius-full);
          background: linear-gradient(135deg, var(--color-white) 0%, #f8fafc 100%);
          border: 3px solid var(--color-primary);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 2;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
          transition: all 0.3s ease;
          position: relative;

          &::before {
            content: '';
            position: absolute;
            inset: -2px;
            border-radius: var(--border-radius-full);
            background: linear-gradient(135deg, var(--color-primary), var(--color-primary-light));
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
          }

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15), 0 4px 8px rgba(0, 0, 0, 0.1);

            &::before {
              opacity: 1;
            }
          }

          .action-icon {
            width: 22px;
            height: 22px;
            color: var(--color-primary);
            transition: all 0.3s ease;

            &.status-icon {
              color: var(--color-success);
            }

            &.note-icon {
              color: var(--color-info);
            }

            &.edit-icon {
              color: var(--color-warning);
            }

            &.default-icon {
              color: var(--color-slate-gray);
            }
          }

          // Status-specific dot styling
          &.status-dot {
            border-color: var(--color-success);
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
          }

          &.note-dot {
            border-color: var(--color-info);
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
          }

          &.edit-dot {
            border-color: var(--color-warning);
            background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
          }
        }

        .timeline-line {
          width: 3px;
          flex: 1;
          background: linear-gradient(180deg, var(--color-border) 0%, rgba(226, 232, 240, 0.5) 100%);
          margin-top: var(--spacing-xs);
          min-height: 24px;
          border-radius: 2px;
          position: relative;

          &::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            width: 1px;
            height: 100%;
            background: var(--color-primary-light);
            transform: translateX(-50%);
            opacity: 0.3;
          }
        }
      }

      // Content card - Enhanced
      .entry-content-card {
        flex: 1;
        background: linear-gradient(135deg, var(--color-white) 0%, #fefefe 100%);
        border: 1px solid var(--color-border);
        border-radius: 12px;
        padding: var(--spacing-lg);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.06);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 3px;
          background: linear-gradient(90deg, var(--color-primary), var(--color-primary-light));
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08), 0 4px 12px rgba(0, 0, 0, 0.06);
          border-color: var(--color-primary-light);

          &::before {
            opacity: 1;
          }
        }

        // Status-specific card styling
        &.status-card {
          &::before {
            background: linear-gradient(90deg, var(--color-success), #10b981);
          }
        }

        &.note-card {
          &::before {
            background: linear-gradient(90deg, var(--color-info), #3b82f6);
          }
        }

        &.edit-card {
          &::before {
            background: linear-gradient(90deg, var(--color-warning), #f59e0b);
          }
        }
      }

        .entry-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: var(--spacing-md);

          .user-info {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            transition: all 0.2s ease;

            &:hover {
              .history-user-avatar {
                transform: scale(1.05);
              }
            }

            .history-user-avatar {
              width: 40px;
              height: 40px;
              border: 2px solid var(--color-white);
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              transition: all 0.3s ease;
            }

            .user-details {
              display: flex;
              flex-direction: column;
              gap: var(--spacing-xxs);

              .user-name {
                color: var(--text-color-black);
                line-height: 1.3;
                font-weight: var(--font-weight-semibold);
                transition: color 0.2s ease;

                &:hover {
                  color: var(--color-primary);
                }
              }

              .action-type {
                color: var(--text-color-slate-gray);
                font-size: var(--font-size-sm);
                line-height: 1.2;
                font-weight: var(--font-weight-medium);
                text-transform: capitalize;
                background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
                padding: 2px 8px;
                border-radius: 12px;
                display: inline-block;
                width: fit-content;
              }
            }
          }

          .timestamp-info {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: var(--spacing-xxs);
            padding: var(--spacing-xs);
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
            border-radius: 8px;
            border: 1px solid rgba(226, 232, 240, 0.5);

            .date-time {
              color: var(--text-color-black);
              font-size: var(--font-size-sm);
              line-height: 1.2;
              font-weight: var(--font-weight-semibold);
            }

            .time-only {
              color: var(--text-color-slate-gray);
              font-size: var(--font-size-xs);
              line-height: 1.2;
              font-weight: var(--font-weight-medium);
              opacity: 0.8;
            }
          }
        }

        .entry-body {
          display: flex;
          flex-direction: column;
          gap: var(--spacing-sm);

          .note-section,
          .status-section {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);

            .d-flex {
              display: flex;
            }

            .align-center {
              align-items: center;
            }

            .gap-xs {
              gap: var(--spacing-xs);
            }

            .note-icon,
            .status-icon {
              width: 16px;
              height: 16px;
              color: var(--color-info);
            }

            .status-icon {
              color: var(--color-success);
            }

            .note-label,
            .status-label {
              color: var(--text-color-slate-gray);
              font-size: var(--font-size-sm);
            }

            .note-content {
              color: var(--text-color-black);
              padding: var(--spacing-sm) var(--spacing-md);
              margin-left: var(--spacing-sm);
              line-height: 1.5;
              background: linear-gradient(135deg, #f8fafc, #f1f5f9);
              border-radius: 8px;
              border-left: 3px solid var(--color-info);
              font-style: italic;
              position: relative;

              &::before {
                content: '"';
                position: absolute;
                left: 8px;
                top: 4px;
                font-size: 18px;
                color: var(--color-info);
                opacity: 0.5;
              }

              &::after {
                content: '"';
                position: absolute;
                right: 8px;
                bottom: 4px;
                font-size: 18px;
                color: var(--color-info);
                opacity: 0.5;
              }
            }

            .status-chip {
              align-self: flex-start;
              margin-left: var(--spacing-lg);
              font-weight: var(--font-weight-semibold);
              border-radius: 20px;
              padding: var(--spacing-xs) var(--spacing-sm);
              font-size: var(--font-size-sm);
              text-transform: capitalize;
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
              transition: all 0.2s ease;

              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
              }

              // Custom status colors
              &.MuiChip-colorPrimary {
                background: linear-gradient(135deg, #3b82f6, #2563eb);
                color: white;
              }

              &.MuiChip-colorSuccess {
                background: linear-gradient(135deg, #10b981, #059669);
                color: white;
              }

              &.MuiChip-colorWarning {
                background: linear-gradient(135deg, #f59e0b, #d97706);
                color: white;
              }

              &.MuiChip-colorSecondary {
                background: linear-gradient(135deg, #8b5cf6, #7c3aed);
                color: white;
              }

              &.MuiChip-colorDefault {
                background: linear-gradient(135deg, #6b7280, #4b5563);
                color: white;
              }
            }
          }
        }
      }
    }
  }

@media (max-width: 768px) {
  .attachment-wrap {
    padding: var(--spacing-sm);

    .attachment-history-wrap {
      gap: var(--spacing-md);

      .history-entry-card {
        .timeline-connector {
          min-width: 32px;

          .timeline-dot {
            width: 32px;
            height: 32px;

            .action-icon {
              width: 16px;
              height: 16px;
            }
          }
        }

        .entry-content-card {
          padding: var(--spacing-sm);

          .entry-header {
            flex-direction: column;
            gap: var(--spacing-sm);
            align-items: flex-start;

            .timestamp-info {
              align-items: flex-start;
            }
          }
        }
      }
    }
  }
}

