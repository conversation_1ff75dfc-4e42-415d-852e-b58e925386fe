@import '@/styles/variable.scss';

.payment-publc-link-page {
  height: 100%;
  margin-top: -50px;
  .login-wrap {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .login-page {
    .input-wrap {
      padding-bottom: 16px !important;
    }
  }
  .card-inputs {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-base);
    margin-top: var(--spacing-md);
  }

  .card-details-row {
    display: flex;
    gap: var(--spacing-base);

    .cardholder-field-container {
      flex: 1;
    }
    .cardnumber-field-container {
      flex: 1;
    }
    .card-expiry-container {
      flex: 1;
    }
    .card-cvc-container {
      flex: 1;
    }
  }
  .payment-details {
    border: 1px solid $color-Dark-10;
    padding: 6px 16px;
    border-radius: 8px;
    margin-bottom: 24px;
    .payment-text {
      font-family: var(--font-family-poly-slim);
      font-size: 14px;
      line-height: 21px;
    }
  }
  .card-accordian-wrap {
    box-shadow: var(--box-shadow-xs);
    border: var(--field-border);
    border-radius: 4px;
    margin: 9px 0px;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    @media (max-width: 575px) {
      display: block;
    }
    .card-wrap {
      gap: 15px;
      width: calc(100% - 200px - 15px);
      align-items: flex-start;
      @media (max-width: 575px) {
        width: 100%;
      }
      .choose-plan-btn {
        padding: 0px;
      }

      .details {
        width: 100%;
        .offer {
          color: $color-Dark-20;
        }
      }

      @media (max-width: 575px) {
        gap: 10px;
      }
    }

    .pricing {
      text-align: right;
      width: 200px;
      @media (max-width: 575px) {
        width: 100%;
      }
      .amount {
        .year-wrap {
          color: $color-Dark-30;
        }
      }

      .duration {
        color: var(--text-color-primary);
      }
    }
  }
  .provider-sec {
    padding: 16px 0;
  }
  .card-select {
    .MuiSelect-select {
      padding: 3px 7px 0px 15px;
      min-width: 150px;
      margin-top: 0px;
    }

    fieldset {
      height: 31px;
      margin-top: 4px;
    }

    .placeholder {
      font-weight: 300 !important;
      padding-top: 2px;
    }

    .MuiSvgIcon-root {
      margin-top: 1px;
    }

    @media (max-width: 575px) {
      width: 100%;
    }
  }
}
