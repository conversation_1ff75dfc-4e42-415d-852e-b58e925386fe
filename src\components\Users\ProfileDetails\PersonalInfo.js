'use client';

import React, { useContext, useEffect, useState } from 'react';
import { Box, Typography, Tooltip, FormLabel } from '@mui/material';
import AuthContext from '@/helper/authcontext';
import HeaderImage from '@/components/UI/ImageSecurity';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import CustomDatePicker from '@/components/UI/CustomDatePicker';
import CustomButton from '@/components/UI/CustomButton';
import CustomSelect from '@/components/UI/CustomSelect';
import Gender from '@/components/UI/FormGroupGender';
import MaritalStatus from '@/components/UI/FormGroupMarital';
import MultiSelect from '@/components/UI/CustomMultiSelect';
import dayjs from 'dayjs';
import {
  setApiMessage,
  TextFieldMaxLength,
  HIGH_LEVEL_MANAGER_ROLE_IDS,
} from '@/helper/common/commonFunctions';
import DialogBox from '@/components/UI/Modalbox';
import ConfirmationModal from '@/components/UI/ConfirmationModal';
import CustomTextField from '@/components/UI/CustomTextField';
import { LocationOn } from '@mui/icons-material';
import InfoIcon from '@mui/icons-material/Info';
import SignatureField from '@/components/UI/SignatureField';
import EditIcon from '@mui/icons-material/Edit';
import { removeFromStorage } from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import { userProfileService } from '../../../services/userprofile';

const PersonalInfo = ({
  UserDetails,
  UserId,
  ViewAccessOnly,
  roleList,
  signValue,
  isLoader,
  getUserDetails,
  setIsLoader,
  setProfileImage,
  profileImage,
  setSignValue,
  formikRef,
  isMyProfile,
  countries,
  counties,
  cities,
  setCities,
  setCounties,
  setSelectedCountry,
  setSelectedCounty,
}) => {
  const [isAnyChange, setIsAnychange] = useState(false);

  const { authState, AllListsData, planDetail, setRestrictedLimitModal } =
    useContext(AuthContext);

  const [createModal, setCreateModal] = useState(false);
  const [createModalDep, setCreateModalDep] = useState(false);
  const [createModalDep2, setCreateModalDep2] = useState(false);
  const [addSign, setAddSign] = useState(false);
  const [usernameError, setUsernameError] = useState('');
  const [isSubmit, setIsSubmit] = useState(false);
  const getValue = (path, defaultValue = '') => {
    return path ?? defaultValue;
  };
  const handleCloseDep = () => {
    setCreateModalDep(false);
  };
  const handleClose = () => {
    setCreateModal(false);
  };
  const handleCloseDep2 = () => {
    setCreateModalDep2(false);
  };
  const getFormattedDate = (date) => {
    return date ? dayjs(date).format('YYYY-MM-DD') : null;
  };
  const safeAppend = (key, value, body, condition = true) => {
    if (condition && value !== undefined && value !== null) {
      body.append(key, value);
    }
  };
  const validationUserSchema = Yup.object().shape({
    fname: Yup.string().trim().required('This field is required'),
    username: Yup.string()
      .required('This field is required')
      .matches(
        /^[a-zA-Z0-9._ ]*$/,
        'Only letters, numbers, underscores, dots, and spaces are allowed'
      )
      .min(3, 'Username must be at least 3 characters'),
    lname: Yup.string().trim().required('This field is required'),
    email: Yup.string()
      .required('This field is required')
      .matches(
        /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i,
        'Please enter valid email'
      ),
    birthdate: Yup.string().trim().required('This field is required'),
    phoneNo: Yup.string()
      .trim()
      .required('This field is required')
      .matches(/^[0-9]{10,11}$/, 'Invalid phone number'),
    hphoneNo: Yup.string()
      .trim()
      .required('This field is required')
      .matches(/^[0-9]{10,11}$/, 'Invalid phone number'),
    joiningdate: Yup.string().trim().required('This field is required'),
    nationality: Yup.string().trim().required('This field is required'),
    country: Yup.string().trim().required('This field is required'),
    county:
      counties &&
      counties?.length !== 0 &&
      Yup.string().trim().required('This field is required'),
    city:
      cities &&
      cities?.length !== 0 &&
      Yup.string().trim().required('This field is required'),
    pincode: Yup.string().trim().required('This field is required'),
    homeaddress1: Yup.string().trim().required('This field is required'),
    // homeaddress2: Yup.string().trim().required('This field is required'),
    gender: Yup.string().trim().required('This field is required'),
    marital: Yup.string().trim().required('This field is required'),
    branchname:
      !isMyProfile &&
      Yup.string()
        .trim()
        .test(
          'required-when-not-admin',
          'This field is required',
          function (value) {
            const { designation } = this.parent;
            if (!designation) return true;

            const designationValues = designation.map((d) => d.value);
            const hasAdminRole = designationValues.every((val) =>
              HIGH_LEVEL_MANAGER_ROLE_IDS?.includes(val)
            );

            // If has admin role (1,2,3), not required
            if (hasAdminRole) return true;

            // Otherwise required
            return !!value;
          }
        ),
    departmentname:
      !isMyProfile &&
      Yup.string()
        .trim()
        .test(
          'required-when-not-admin',
          'This field is required',
          function (value) {
            const { designation } = this.parent;
            if (!designation) return true;

            const designationValues = designation.map((d) => d.value);
            const hasAdminRole = designationValues.every((val) =>
              HIGH_LEVEL_MANAGER_ROLE_IDS?.includes(val)
            );

            // If has admin role (1,2,3), not required
            if (hasAdminRole) return true;

            // Otherwise required
            return !!value;
          }
        ),
    designation:
      !isMyProfile && Yup.array().min(1, 'At least one role must be selected'),
    branches:
      !isMyProfile &&
      Yup.array()
        .test(
          'required-when-designation-5',
          'At least one branch must be selected',
          function (value) {
            const { designation, branchname } = this.parent; // Access other field values
            if (
              designation &&
              designation?.map((r) => r?.value).includes(5) &&
              !branchname
            ) {
              return Array.isArray(value) && value.length > 0;
            }
            return true; // Pass validation if the condition is not met
          }
        )
        .required('Branches is required'),
    emp_sign:
      isMyProfile && Yup.string().trim().required('This field is required'),
  });

  const generateInitialValuesFromUser = (UserDetails) => ({
    fname: UserDetails?.user_first_name || '',
    mname: UserDetails?.user_middle_name || '',
    lname: UserDetails?.user_last_name || '',
    email: UserDetails?.user_email || '',
    branchname: UserDetails?.branch?.id || '',
    branches: UserDetails?.assign_branch_ids || [],
    departmentname: UserDetails?.department?.id || '',
    phoneNo: UserDetails?.user_phone_number || '',
    hphoneNo: UserDetails?.emergency_contact || '',
    homeaddress1: UserDetails?.address_line1 || '',
    homeaddress2: UserDetails?.address_line2 || '',
    designation: UserDetails?.user_roles || '',
    country: UserDetails?.geo_country?.place_code || '',
    nationality: UserDetails?.country || '',
    county: UserDetails?.geo_state?.place_code || '',
    city: UserDetails?.geo_city?.place_code || '',
    pincode: UserDetails?.pin_code || '',
    birthdate: UserDetails?.date_of_birth
      ? dayjs(UserDetails?.date_of_birth).format('YYYY-MM-DD')
      : null,
    joiningdate: UserDetails?.user_joining_date
      ? dayjs(UserDetails?.user_joining_date).format('YYYY-MM-DD')
      : null,
    marital:
      UserDetails?.marital_status || UserDetails?.marital_status_other || '',
    gender: UserDetails?.user_gender || UserDetails?.user_gender_other || '',
    emp_sign: UserDetails?.user_signature || '',
  });
  const resetAllUserDetails = () => {
    setSignValue();
    setProfileImage({ url: UserDetails?.user_avatar, IsFromAPi: true });
    formikRef.current.setValues({
      ...formikRef.current.values,
      ...generateInitialValuesFromUser(UserDetails),
    });
  };
  // API for reset all user profile details
  const ResetUserDetails = async () => {
    try {
      const data = await userProfileService.resetUserDetails(UserId);
      if (data) {
        handleClose();
        getUserDetails(UserId);
        setApiMessage('success', data?.message);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  useEffect(() => {
    UserDetails?.user_signature &&
      setSignValue({ url: UserDetails?.user_signature, IsFromAPi: true });
  }, [UserDetails?.user_signature]);
  useEffect(() => {
    UserDetails?.geo_country?.place_code &&
      setSelectedCountry(UserDetails?.geo_country?.place_code);
    UserDetails?.geo_state?.place_code &&
      setSelectedCounty(UserDetails?.geo_state?.place_code);
  }, [UserDetails]);
  const scrollToError = (errors) => {
    const firstErrorField = Object.keys(errors)[0];

    // Try to find the element by ID first
    let element = document.getElementById(firstErrorField);

    // If not found by ID, try other selectors
    if (!element) {
      element = document.querySelector(`[name="${firstErrorField}"]`);
    }

    // If still not found or it's a hidden input, try to find the parent container
    if (!element || element.type === 'hidden') {
      const selectBox = document.querySelector(
        `.select-box[data-field="${firstErrorField}"]`
      );
      const errorField = document.querySelector(
        `.textfeild-error[data-field="${firstErrorField}"]`
      );
      const customField = document.querySelector(
        `[data-field="${firstErrorField}"]`
      );

      element = selectBox || errorField || customField;
    }
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'center' });
      // For MultiSelect, focus the select input if it exists
      const selectInput = element.querySelector('input') || element;
      selectInput.focus();
    }
  };
  // Get Form details by ID
  const getAllUserName = async (username) => {
    try {
      const data = await userProfileService.checkUsernameAvailability(username);
      if (data) {
        setUsernameError(
          data?.available || username === UserDetails?.username
            ? false
            : data?.message
        );
        if (!data?.available && username !== UserDetails?.username) {
          formikRef.current?.setFieldError('username', data?.message);
        }
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      console.error('Error checking username availability:', error);
    }
  };
  return (
    <>
      <Formik
        innerRef={formikRef}
        initialValues={{
          submitType: '',
          fname: getValue(UserDetails?.user_first_name),
          mname: getValue(UserDetails?.user_middle_name),
          lname: getValue(UserDetails?.user_last_name),
          email: getValue(UserDetails?.user_email),
          branchname: getValue(UserDetails?.branch?.id),
          branches: getValue(UserDetails?.assign_branch_ids, []),
          departmentname: getValue(UserDetails?.department?.id),
          phoneNo: getValue(UserDetails?.user_phone_number),
          hphoneNo: getValue(UserDetails?.emergency_contact),
          homeaddress1: getValue(UserDetails?.address_line1),
          homeaddress2: getValue(UserDetails?.address_line2),
          designation: getValue(UserDetails?.user_roles),
          country: getValue(UserDetails?.geo_country?.place_code),
          nationality: getValue(UserDetails?.country),
          county: getValue(UserDetails?.geo_state?.place_code),
          city: getValue(UserDetails?.geo_city?.place_code),
          pincode: getValue(UserDetails?.pin_code),
          birthdate: getFormattedDate(UserDetails?.date_of_birth),
          joiningdate: getFormattedDate(UserDetails?.user_joining_date),
          username: getValue(UserDetails?.username),
          marital: getValue(
            UserDetails?.marital_status ?? UserDetails?.marital_status_other
          ),
          gender: getValue(
            UserDetails?.user_gender ?? UserDetails?.user_gender_other
          ),
          emp_sign: getValue(UserDetails?.user_signature),
        }}
        enableReinitialize={true}
        validationSchema={validationUserSchema}
        onSubmit={async (requestData) => {
          !isMyProfile && setIsLoader(true);
          const body = new FormData();
          const rid = requestData?.designation?.map((item) => item?.value);
          const branchesList = _.concat(
            AllListsData?.ActiveBranchList?.filter(
              (b) => b?.value === requestData?.branchname
            ),
            requestData?.branches?.filter(
              (f) => f?.value !== requestData?.branchname
            )
          );
          const bid =
            branchesList &&
            branchesList?.length > 0 &&
            branchesList?.map((item) => item?.value);

          safeAppend('user_first_name', requestData?.fname?.trim(), body);
          safeAppend('user_last_name', requestData?.lname?.trim(), body);
          safeAppend('user_middle_name', requestData?.mname?.trim(), body);
          safeAppend('user_email', requestData?.email, body);
          safeAppend('address_line1', requestData?.homeaddress1, body);
          safeAppend('address_line2', requestData?.homeaddress2, body);

          safeAppend('geo_country', requestData?.country ?? null, body);
          safeAppend('country', requestData?.nationality ?? null, body);
          safeAppend('geo_state', requestData?.county ?? null, body);
          safeAppend('geo_city', requestData?.city ?? null, body);

          safeAppend('pin_code', requestData?.pincode?.toString(), body);
          safeAppend('username', requestData?.username?.toString(), body);
          safeAppend(
            'user_phone_number',
            requestData?.phoneNo?.toString(),
            body
          );
          safeAppend(
            'emergency_contact',
            requestData?.hphoneNo?.toString(),
            body
          );

          safeAppend(
            'date_of_birth',
            requestData?.birthdate &&
              dayjs(requestData?.birthdate).format('YYYY-MM-DD'),
            body
          );
          !isMyProfile &&
            requestData?.branchname &&
            safeAppend('branch_id', requestData?.branchname, body);
          !isMyProfile &&
            safeAppend(
              'joining_date',
              requestData?.joiningdate &&
                dayjs(requestData?.joiningdate).format('YYYY-MM-DD'),
              body
            );

          if (
            !isMyProfile &&
            requestData?.designation?.length > 0 &&
            rid?.length > 0 &&
            rid?.includes(5) &&
            requestData?.branches &&
            bid?.length > 0
          ) {
            bid.forEach((r, i) => body.append(`assign_branch_ids[${i}]`, r));
          }

          !isMyProfile &&
            requestData?.departmentname &&
            safeAppend('department_id', requestData?.departmentname, body);

          if (!isMyProfile && requestData?.designation && rid?.length > 0) {
            rid.forEach((r, i) => body.append(`role_ids[${i}]`, r));
          }

          if (requestData?.gender) {
            if (['male', 'female'].includes(requestData?.gender)) {
              body.append('user_gender', requestData?.gender);
            } else {
              body.append('user_gender_other', requestData?.gender);
            }
          }

          if (requestData?.marital) {
            if (['single', 'married'].includes(requestData?.marital)) {
              body.append('marital_status', requestData?.marital);
            } else {
              body.append('marital_status_other', requestData?.marital);
            }
          }

          if (profileImage?.file && !profileImage?.IsFromAPi) {
            body.append('user_avatar', profileImage?.file);
          } else {
            body.append('user_avatar', '');
          }

          if (
            signValue?.url &&
            requestData?.emp_sign &&
            !signValue?.IsFromAPi
          ) {
            body.append('user_signature', requestData?.emp_sign);
          }

          try {
            const data = await userProfileService.updateUserProfile(
              UserId,
              body,
              isMyProfile
            );
            if (data?.status) {
              setApiMessage('success', data?.message);
              getUserDetails(UserId);
              isMyProfile && removeFromStorage(identifiers?.LOGIN_ORG);
            } else {
              setApiMessage('error', data?.message);
            }
            !isMyProfile && setIsLoader(false);
          } catch (error) {
            !isMyProfile && setIsLoader(false);
            setApiMessage('error', error?.response?.data?.message);
          }
        }}
      >
        {({
          errors,
          touched,
          handleBlur,
          values,
          setFieldValue,
          handleChange,
        }) => (
          <Form>
            <Box className="display-grid pt16">
              <Box>
                <CustomTextField
                  fullWidth
                  id="ename"
                  name="ename"
                  value={UserDetails?.employment_number || ''}
                  label={<span>Employment Number</span>}
                  required
                  placeholder="Enter Employment Number"
                  onBlur={handleBlur}
                  onChange={(e) => {
                    setIsAnychange(true);
                    handleChange(e);
                  }}
                  disabled={true}
                />
              </Box>
              <Box>
                <CustomTextField
                  fullWidth
                  id="username"
                  name="username"
                  value={values?.username || ''}
                  label={<span>User Name</span>}
                  required
                  placeholder="Enter User Name"
                  error={Boolean(
                    (touched.username && errors.username) || usernameError
                  )}
                  helperText={
                    (touched.username && errors.username) || usernameError
                  }
                  onBlur={handleBlur}
                  onChange={(e) => {
                    setIsAnychange(true);
                    handleChange(e);
                    e?.target?.value && getAllUserName(e?.target?.value);
                  }}
                  inputProps={{ maxLength: TextFieldMaxLength }}
                  disabled={ViewAccessOnly}
                />
              </Box>
              <Box>
                <CustomTextField
                  fullWidth
                  id="fname"
                  name="fname"
                  value={values?.fname}
                  label={<span>First Name</span>}
                  required
                  placeholder="Enter first name"
                  error={Boolean(touched.fname && errors.fname)}
                  helperText={touched.fname && errors.fname}
                  onBlur={handleBlur}
                  onChange={(e) => {
                    setIsAnychange(true);
                    handleChange(e);
                  }}
                  disabled={ViewAccessOnly}
                  inputProps={{ maxLength: TextFieldMaxLength }}
                />
              </Box>
              <Box>
                <CustomTextField
                  fullWidth
                  id="mname"
                  name="mname"
                  value={values?.mname}
                  label={<span>Middle Name</span>}
                  placeholder="Enter Middle Name"
                  error={Boolean(touched.mname && errors.mname)}
                  helperText={touched.mname && errors.mname}
                  onBlur={handleBlur}
                  onChange={(e) => {
                    setIsAnychange(true);
                    handleChange(e);
                  }}
                  disabled={ViewAccessOnly}
                  inputProps={{ maxLength: TextFieldMaxLength }}
                />
              </Box>
              <Box>
                <CustomTextField
                  fullWidth
                  id="lname"
                  name="lname"
                  value={values?.lname}
                  label={<span>Last Name</span>}
                  required
                  placeholder="Enter last name"
                  error={Boolean(touched.lname && errors.lname)}
                  helperText={touched.lname && errors.lname}
                  onBlur={handleBlur}
                  onChange={(e) => {
                    setIsAnychange(true);
                    handleChange(e);
                  }}
                  disabled={ViewAccessOnly}
                  inputProps={{ maxLength: TextFieldMaxLength }}
                />
              </Box>
              <Box>
                <CustomTextField
                  fullWidth
                  id="email"
                  name="email"
                  value={values?.email}
                  label={<span>Email address</span>}
                  required
                  placeholder="Enter Email address"
                  error={Boolean(touched.email && errors.email)}
                  helperText={touched.email && errors.email}
                  onBlur={handleBlur}
                  onChange={(e) => {
                    setIsAnychange(true);
                    handleChange(e);
                  }}
                  disabled={ViewAccessOnly}
                />
              </Box>
              <Box>
                <CustomDatePicker
                  label={<span>Date of birth</span>}
                  error={Boolean(
                    !values?.birthdate && touched.birthdate && errors.birthdate
                  )}
                  helperText={
                    !values?.birthdate && touched.birthdate && errors.birthdate
                  }
                  name="birthdate"
                  value={dayjs(values?.birthdate)}
                  onBlur={handleBlur}
                  format="DD/MM/YYYY"
                  onChange={(date) => {
                    setFieldValue('birthdate', date);
                    setIsAnychange(true);
                  }}
                  disableFuture={true}
                  disabled={ViewAccessOnly}
                  required
                  maxDate={dayjs().subtract(18, 'year')}
                />
              </Box>

              <Box className="phone-number">
                <CustomTextField
                  fullWidth
                  id="phoneNo"
                  name="phoneNo"
                  value={values?.phoneNo}
                  label={<span>Phone number</span>}
                  required
                  placeholder="Enter Phone number"
                  error={Boolean(touched.phoneNo && errors.phoneNo)}
                  helperText={touched.phoneNo && errors.phoneNo}
                  onBlur={handleBlur}
                  onChange={(e) => {
                    if (e.target.value === '' || e.target.value?.length < 12) {
                      handleChange(e);
                      setIsAnychange(true);
                    }
                  }}
                  onInput={(e) => {
                    e.target.value = e.target.value.replace(/[^0-9]/g, ''); // Remove non-numeric characters
                  }}
                  disabled={ViewAccessOnly}
                />
              </Box>
              <Box className="phone-number">
                <CustomTextField
                  fullWidth
                  id="hphoneNo"
                  name="hphoneNo"
                  value={values?.hphoneNo}
                  label={<span>Emergency Contact</span>}
                  required
                  placeholder="Enter Emergency Contact"
                  error={Boolean(touched.hphoneNo && errors.hphoneNo)}
                  helperText={touched.hphoneNo && errors.hphoneNo}
                  onBlur={handleBlur}
                  onChange={(e) => {
                    if (e.target.value === '' || e.target.value?.length < 12) {
                      handleChange(e);
                      setIsAnychange(true);
                    }
                  }}
                  onInput={(e) => {
                    e.target.value = e.target.value.replace(/[^0-9]/g, ''); // Remove non-numeric characters
                  }}
                  disabled={ViewAccessOnly}
                />
              </Box>

              <Box>
                <CustomDatePicker
                  label={<span>Joining date</span>}
                  name="joiningdate"
                  value={dayjs(values?.joiningdate)}
                  error={Boolean(
                    !values?.joiningdate &&
                      touched.joiningdate &&
                      errors.joiningdate
                  )}
                  helperText={
                    !values?.joiningdate &&
                    touched.joiningdate &&
                    errors.joiningdate
                  }
                  onBlur={handleBlur}
                  onChange={(date) => {
                    setFieldValue('joiningdate', date);
                    setFieldValue('expire_date', null);
                    setIsAnychange(true);
                  }}
                  format="DD/MM/YYYY"
                  inputVariant="outlined"
                  disabled={ViewAccessOnly || isMyProfile}
                  required
                />
              </Box>
              <Box>
                <CustomSelect
                  required
                  label={<span>Nationality</span>}
                  name="nationality"
                  placeholder="Nationality"
                  options={countries}
                  value={
                    countries?.find((opt) => {
                      return opt?.value === values?.nationality;
                    }) || ''
                  }
                  onChange={(selectedOption) => {
                    setFieldValue('nationality', selectedOption?.value || '');
                    setIsAnychange(true);
                  }}
                  error={
                    !values?.nationality &&
                    touched?.nationality &&
                    errors?.nationality
                  }
                  helperText={
                    !values?.nationality &&
                    touched?.nationality &&
                    errors?.nationality
                  }
                  isDisabled={ViewAccessOnly}
                  startAdornment={<LocationOn />}
                />
              </Box>
              <Box>
                <CustomSelect
                  required
                  label={
                    <span className="field-info">
                      Country of Residence
                      <Tooltip
                        title="Current Address"
                        placement="right"
                        classes={{
                          tooltip: 'info-tooltip-container ',
                        }}
                        arrow
                      >
                        <InfoIcon
                          sx={{ marginLeft: '8px' }}
                          className="info-icon cursor-poniter"
                        />
                      </Tooltip>
                    </span>
                  }
                  name="country"
                  placeholder="Country of Residence"
                  options={countries}
                  value={
                    countries?.find((opt) => {
                      return opt?.value === values?.country;
                    }) || ''
                  }
                  onChange={(selectedOption) => {
                    setFieldValue('country', selectedOption?.value || '');
                    setFieldValue('county', '');
                    setFieldValue('city', '');
                    setCities([]);
                    setCounties([]);
                    setSelectedCountry(selectedOption?.value);
                    setIsAnychange(true);
                  }}
                  error={
                    !values?.country && touched?.country && errors?.country
                  }
                  helperText={
                    !values?.country && touched?.country && errors?.country
                  }
                  isDisabled={ViewAccessOnly}
                  startAdornment={<LocationOn />}
                />
              </Box>
              <Box>
                <CustomSelect
                  label={<span>County</span>}
                  required={counties && counties?.length !== 0}
                  name="county"
                  placeholder="County"
                  options={counties}
                  value={
                    counties?.find((opt) => {
                      return opt?.value === values?.county;
                    }) || ''
                  }
                  onChange={(selectedOption) => {
                    setFieldValue('county', selectedOption?.value);
                    setFieldValue('city', '');
                    setSelectedCounty(selectedOption?.value);
                    setIsAnychange(true);
                  }}
                  error={!values?.county && touched?.county && errors?.county}
                  helperText={
                    !values?.county && touched?.county && errors?.county
                  }
                  isDisabled={ViewAccessOnly}
                  startAdornment={<LocationOn />}
                />
              </Box>
              <Box>
                <CustomSelect
                  required={cities && cities?.length !== 0}
                  label={<span>City</span>}
                  name="city"
                  placeholder="City"
                  options={cities}
                  value={
                    cities?.find((opt) => {
                      return opt?.value === values?.city;
                    }) || ''
                  }
                  onChange={(selectedOption) => {
                    setFieldValue('city', selectedOption?.value || '');
                    setIsAnychange(true);
                  }}
                  error={!values?.city && touched?.city && errors?.city}
                  helperText={!values?.city && touched?.city && errors?.city}
                  isDisabled={ViewAccessOnly}
                  startAdornment={<LocationOn />}
                />
              </Box>
              <Box>
                <CustomTextField
                  fullWidth
                  id="pincode"
                  name="pincode"
                  value={values?.pincode}
                  label={<span>Post Code</span>}
                  required
                  placeholder="Enter Post code"
                  error={Boolean(touched.pincode && errors.pincode)}
                  helperText={touched.pincode && errors.pincode}
                  onBlur={handleBlur}
                  disabled={ViewAccessOnly}
                  onChange={(e) => {
                    if (
                      (e.target.value && e.target.value?.length < 9) ||
                      e.target.value === ''
                    ) {
                      handleChange(e);
                      setIsAnychange(true);
                    }
                  }}
                />
              </Box>
            </Box>

            <Box className="display-grid-50 space-grid">
              <Box>
                <CustomTextField
                  fullWidth
                  id="homeaddress1"
                  name="homeaddress1"
                  value={values?.homeaddress1}
                  label={<span>Address line 1</span>}
                  required
                  placeholder="Enter Address"
                  error={Boolean(touched.homeaddress1 && errors.homeaddress1)}
                  helperText={touched.homeaddress1 && errors.homeaddress1}
                  onBlur={handleBlur}
                  onChange={(e) => {
                    setIsAnychange(true);
                    handleChange(e);
                  }}
                  disabled={ViewAccessOnly}
                />
              </Box>
              <Box>
                <CustomTextField
                  fullWidth
                  id="homeaddress2"
                  name="homeaddress2"
                  value={values?.homeaddress2}
                  label={<span>Address line 2</span>}
                  placeholder="Enter Address"
                  error={Boolean(touched.homeaddress2 && errors.homeaddress2)}
                  helperText={touched.homeaddress2 && errors.homeaddress2}
                  onBlur={handleBlur}
                  onChange={(e) => {
                    setIsAnychange(true);
                    handleChange(e);
                  }}
                  disabled={ViewAccessOnly}
                />
              </Box>
            </Box>
            <Box className="space-grid">
              <Gender
                keyValue={values?.gender}
                keyName="gender"
                setFieldValue={setFieldValue}
                isRequire={true}
                disable={ViewAccessOnly}
              />
              {touched.gender && errors.gender && (
                <Typography
                  variant="body2"
                  color="error"
                  className="field-error"
                >
                  {errors.gender}
                </Typography>
              )}
            </Box>
            <Box className="space-grid">
              <MaritalStatus
                keyValue={values?.marital}
                keyName="marital"
                setFieldValue={setFieldValue}
                isRequire={true}
                disable={ViewAccessOnly}
              />
              {touched.marital && errors.marital && (
                <Typography
                  variant="body2"
                  color="error"
                  className="field-error"
                >
                  {errors.marital}
                </Typography>
              )}
            </Box>
            <Box className="display-grid space-grid">
              <Box>
                <CustomSelect
                  placeholder="Branch name"
                  options={AllListsData?.ActiveBranchList}
                  value={
                    AllListsData?.ActiveBranchList?.find((opt) => {
                      return opt?.value === values?.branchname;
                    }) || ''
                  }
                  name="branchname"
                  error={
                    !values?.branchname &&
                    touched?.branchname &&
                    errors?.branchname
                  }
                  helperText={
                    !values?.branchname &&
                    touched?.branchname &&
                    errors?.branchname
                  }
                  onChange={(e) => {
                    setIsAnychange(true);
                    setFieldValue('branchname', e?.value || '');
                  }}
                  onBlur={handleBlur}
                  label="Branch name"
                  required={
                    !isMyProfile &&
                    values?.designation?.length > 0 &&
                    !values?.designation
                      ?.map((d) => d?.value)
                      ?.every((val) =>
                        HIGH_LEVEL_MANAGER_ROLE_IDS?.includes(val)
                      )
                  }
                  isDisabled={ViewAccessOnly || isMyProfile}
                  showDot={true}
                />
              </Box>

              <Box>
                <CustomSelect
                  placeholder="Department name"
                  options={AllListsData?.ActiveDepartmentList}
                  value={
                    AllListsData?.ActiveDepartmentList?.find((opt) => {
                      return opt?.value === values?.departmentname;
                    }) || ''
                  }
                  name="departmentname"
                  error={
                    !values?.departmentname &&
                    touched?.departmentname &&
                    errors?.departmentname
                  }
                  helperText={
                    !values?.departmentname &&
                    touched?.departmentname &&
                    errors?.departmentname
                  }
                  onChange={(e) => {
                    setFieldValue('departmentname', e?.value || '');
                  }}
                  onBlur={handleBlur}
                  required={
                    !isMyProfile &&
                    values?.designation?.length > 0 &&
                    !values?.designation
                      ?.map((d) => d?.value)
                      ?.every((val) =>
                        HIGH_LEVEL_MANAGER_ROLE_IDS?.includes(val)
                      )
                  }
                  label="Department name"
                  isDisabled={ViewAccessOnly || isMyProfile}
                />
              </Box>
            </Box>
            <Box className="display-grid-50 space-grid">
              <Box>
                <MultiSelect
                  placeholder="System Access"
                  options={roleList}
                  value={values?.designation}
                  error={touched.designation && errors.designation}
                  helperText={touched.designation && errors.designation}
                  onChange={(e) => {
                    setFieldValue('designation', e);
                    setIsAnychange(true);
                  }}
                  name="designation"
                  isDisabled={ViewAccessOnly || isMyProfile}
                  required
                  label={<span>System Access</span>}
                />
              </Box>
              {values?.designation &&
                values?.designation?.length > 0 &&
                values?.designation?.map((m) => m?.value)?.includes(5) && (
                  <Box>
                    <MultiSelect
                      placeholder="Assign branches"
                      showDot={true}
                      options={AllListsData?.ActiveBranchList}
                      value={_.concat(
                        AllListsData?.ActiveBranchList?.filter(
                          (b) => b?.value === values?.branchname
                        ),
                        values?.branches?.filter(
                          (f) => f?.value !== values?.branchname
                        )
                      )}
                      isAreaManager={true}
                      error={touched.branches && errors.branches}
                      helperText={touched.branches && errors.branches}
                      onChange={(e) => {
                        setFieldValue(
                          'branches',
                          e?.filter((f) => f?.value !== values?.branchname)
                        );
                        setIsAnychange(true);
                      }}
                      name="branches"
                      required
                      isDisabled={ViewAccessOnly || isMyProfile}
                      label={<span>Assign branches</span>}
                    />
                  </Box>
                )}
            </Box>
            {isMyProfile ? (
              <Box className="space-grid">
                <SignatureField
                  signValue={signValue}
                  setSignValue={setSignValue}
                  addSign={addSign}
                  setAddSign={setAddSign}
                  formikRef={formikRef}
                  isSubmit={isSubmit}
                  setIsSubmit={setIsSubmit}
                  title="Signature"
                  ViewAccessOnly={ViewAccessOnly}
                  error={Boolean(touched.emp_sign && errors.emp_sign)}
                  helperText={touched.emp_sign && errors.emp_sign}
                />
              </Box>
            ) : (
              <>
                {signValue && signValue?.url ? (
                  <Box className="sign-sec space-grid">
                    <FormLabel
                      className={`field-label ${touched.emp_sign && errors.emp_sign ? 'error-label' : ''}`}
                    >
                      <span>Signature</span>
                      {!ViewAccessOnly && (
                        <span>
                          <Box
                            className="signature-edit-wrap"
                            onClick={() => {
                              document.getElementById('file-input4').click();
                            }}
                          >
                            <EditIcon className="signature-edit" />
                          </Box>
                        </span>
                      )}
                    </FormLabel>
                    <Box className="signature-section">
                      <HeaderImage
                        imageUrl={signValue?.url ? signValue?.url : null}
                        width={180}
                        height={60}
                        alt="not found"
                        type="lazyload"
                      />
                      <input
                        type="file"
                        id="file-input4"
                        onChange={(e) => {
                          if (
                            !ViewAccessOnly &&
                            e.target.files &&
                            e.target.files[0]
                          ) {
                            const selectedFile = e.target.files[0];

                            // Check if storage is full
                            const totalStorage = planDetail?.total_storage || 0;
                            const usedStorage =
                              authState?.subscriptionUsage?.total_size_gb || 0;
                            const fileSizeInGB =
                              selectedFile.size / (1024 * 1024 * 1024); // Convert bytes to GB

                            if (usedStorage + fileSizeInGB > totalStorage) {
                              setRestrictedLimitModal({
                                storageLimit: true,
                                totalStorage: planDetail?.total_storage,
                                usedStorage:
                                  authState?.subscriptionUsage?.total_size_gb,
                              });
                              e.target.value = null;
                              return;
                            }

                            const imageUrl = URL.createObjectURL(selectedFile);
                            setSignValue({
                              url: imageUrl,
                              file: selectedFile,
                            });
                            setIsAnychange(true);
                            setFieldValue('emp_sign', selectedFile);
                          }
                        }}
                        hidden
                        accept="image/*"
                      />
                    </Box>
                  </Box>
                ) : (
                  <>
                    {ViewAccessOnly ? (
                      <>
                        {' '}
                        <Box className="field-label cursor-pointer primary-link-text pt16">
                          Add Signature
                        </Box>
                      </>
                    ) : (
                      <>
                        {' '}
                        <label
                          htmlFor="file-input1"
                          className="file-input-label max-content"
                        >
                          <Box className="field-label cursor-pointer primary-link-text pt16 max-content">
                            Add Signature
                          </Box>
                        </label>
                        <input
                          type="file"
                          id="file-input1"
                          onChange={(e) => {
                            if (
                              !ViewAccessOnly &&
                              e.target.files &&
                              e.target.files[0]
                            ) {
                              const selectedFile = e.target.files[0];

                              // Check if storage is full
                              const totalStorage =
                                planDetail?.total_storage || 0;
                              const usedStorage =
                                authState?.subscriptionUsage?.total_size_gb ||
                                0;
                              const fileSizeInGB =
                                selectedFile.size / (1024 * 1024 * 1024); // Convert bytes to GB

                              if (usedStorage + fileSizeInGB > totalStorage) {
                                setRestrictedLimitModal({
                                  storageLimit: true,
                                  totalStorage: planDetail?.total_storage,
                                  usedStorage:
                                    authState?.subscriptionUsage?.total_size_gb,
                                });
                                e.target.value = null;
                                return;
                              }

                              const imageUrl =
                                URL.createObjectURL(selectedFile);
                              setSignValue({
                                url: imageUrl,
                                file: selectedFile,
                              });
                              setIsAnychange(true);
                              setFieldValue('emp_sign', selectedFile);
                            }
                          }}
                          hidden
                          accept="image/*"
                        />
                      </>
                    )}
                  </>
                )}
                {touched.emp_sign && errors.emp_sign && (
                  <Typography
                    variant="body2"
                    color="error"
                    className="field-error"
                  >
                    This field is required
                  </Typography>
                )}
              </>
            )}
            <Box className="create-cancel-button">
              {!isMyProfile &&
                [1, 2, 3, 4].includes(authState?.web_user_active_role_id) && (
                  <CustomButton
                    variant="outlined"
                    title="Reset details"
                    fullWidth={false}
                    disabled={ViewAccessOnly}
                    onClick={() => setCreateModal(true)}
                  />
                )}

              {values?.departmentname === UserDetails?.department?.id ? (
                <CustomButton
                  variant="contained"
                  type={!usernameError && 'submit'}
                  title="Save details"
                  fullWidth={false}
                  disabled={ViewAccessOnly || isLoader}
                  onClick={async () => {
                    setIsSubmit(true);
                    if (formikRef?.current) {
                      // Set all fields as touched
                      const errors = await formikRef.current.validateForm();
                      const allFields = Object.keys(errors);
                      const touchedFields = allFields?.reduce((acc, field) => {
                        acc[field] = true;
                        return acc;
                      }, {});
                      formikRef.current.setTouched(touchedFields);

                      if (Object.keys(errors).length > 0) {
                        scrollToError(errors);
                        formikRef.current.setSubmitting(false);
                      }
                    }
                  }}
                />
              ) : (
                <CustomButton
                  variant="contained"
                  title="Save details"
                  fullWidth={false}
                  disabled={ViewAccessOnly || isLoader}
                  onClick={async () => {
                    setIsSubmit(true);
                    if (formikRef?.current) {
                      // Set all fields as touched
                      const errors = await formikRef.current.validateForm();
                      const allFields = Object.keys(errors);
                      const touchedFields = allFields?.reduce((acc, field) => {
                        acc[field] = true;
                        return acc;
                      }, {});
                      formikRef.current.setTouched(touchedFields);

                      if (Object.keys(errors).length > 0) {
                        scrollToError(errors);
                        formikRef.current.setSubmitting(false);
                      } else {
                        setCreateModalDep(true);
                      }
                    }
                  }}
                />
              )}
            </Box>
          </Form>
        )}
      </Formik>
      <DialogBox
        open={createModal}
        handleClose={() => {
          handleClose();
        }}
        title={'Confirmation'}
        className="confirmation-modal"
        dividerClass="confirmation-modal-divider"
        content={
          <>
            <ConfirmationModal
              handleCancel={handleClose}
              handleConfirm={ResetUserDetails}
              text="Are you sure? You want to reset user details?"
              confirmText="Confirm"
            />
          </>
        }
      />{' '}
      <DialogBox
        open={createModalDep}
        handleClose={() => {
          handleCloseDep();
          if (isAnyChange) {
            setCreateModalDep2(true);
          } else {
            if (formikRef && formikRef?.current) {
              formikRef.current.setFieldValue(
                'departmentname',
                UserDetails?.department?.id
              );
            }
          }
        }}
        title={'Confirmation'}
        className="confirmation-modal"
        dividerClass="confirmation-modal-divider"
        content={
          <>
            <ConfirmationModal
              handleCancel={() => {
                handleCloseDep();
                if (isAnyChange) {
                  setCreateModalDep2(true);
                } else {
                  if (formikRef && formikRef?.current) {
                    formikRef.current.setFieldValue(
                      'departmentname',
                      UserDetails?.department?.id
                    );
                  }
                }
              }}
              handleConfirm={() => {
                if (formikRef && formikRef?.current) {
                  handleCloseDep();
                  setTimeout(() => {
                    formikRef?.current.submitForm();
                  }, [300]);
                }
              }}
              confirmText="Confirm"
              text="By updating the user's department, their job role and extra responsibilities will be cleared. You will need to reassign them and regenerate the employment contract accordingly."
            />
          </>
        }
      />
      <DialogBox
        open={createModalDep2}
        handleClose={() => {
          handleCloseDep2();
          resetAllUserDetails();
        }}
        title={'Confirmation'}
        className="confirmation-modal"
        dividerClass="confirmation-modal-divider"
        content={
          <>
            <ConfirmationModal
              handleCancel={() => {
                handleCloseDep2();
                resetAllUserDetails();
              }}
              confirmText="Confirm"
              handleConfirm={() => {
                handleCloseDep2();
                if (formikRef && formikRef?.current) {
                  formikRef.current.setFieldValue(
                    'departmentname',
                    UserDetails?.department?.id
                  );
                  setTimeout(() => {
                    formikRef?.current.submitForm();
                  }, [300]);
                }
              }}
              text="Do you want to save user profile information, excluding the department name?"
            />
          </>
        }
      />
    </>
  );
};

export default PersonalInfo;
