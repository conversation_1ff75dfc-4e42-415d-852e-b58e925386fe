'use client';

import React from 'react';
import { Box } from '@mui/material';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import CustomTextField from '@/components/UI/CustomTextField';
import './recipesettings.scss';

const ContactForm = ({
  formData = {},
  onFormDataChange,
  validationErrors = {},
}) => {
  return (
    <Box className="">
      <Formik
        initialValues={formData}
        enableReinitialize={true}
        validationSchema={Yup.object().shape({
          name: Yup.string().trim().required('This field is required'),
          phone: Yup.string().trim().required('This field is required'),
          email: Yup.string().trim().required('This field is required'),
        })}
        onSubmit={async (requestData) => {
          //   setLoader(true);
          let sendData = {
            name: requestData?.name?.trim(),
            phone: requestData?.phone?.trim(),
            email: requestData?.email?.trim(),
            link: requestData?.link?.trim(),
          };
          console.error('sendData', sendData);
        }}
      >
        {({
          errors,
          touched,
          handleBlur,
          values,
          handleSubmit,
          handleChange,
        }) => (
          <Form onSubmit={handleSubmit}>
            <Box className="recipe-settings-form d-flex align-start gap-sm pb4">
              <Box className="contact-form-fields">
                <CustomTextField
                  fullWidth
                  name="name"
                  value={values?.name}
                  label="Name"
                  placeholder="Enter name"
                  error={Boolean(
                    (touched?.name && errors?.name) || validationErrors?.name
                  )}
                  helperText={
                    (touched?.name && errors?.name) || validationErrors?.name
                  }
                  onBlur={handleBlur}
                  onChange={(e) => {
                    handleChange(e);
                    if (onFormDataChange) {
                      onFormDataChange({
                        ...values,
                        [e.target.name]: e.target.value,
                      });
                    }
                  }}
                  required
                />
              </Box>
              <Box className="contact-form-fields">
                <CustomTextField
                  fullWidth
                  name="phone"
                  value={values?.phone}
                  label="Phone"
                  placeholder="Enter phone"
                  error={Boolean(
                    (touched?.phone && errors?.phone) || validationErrors?.phone
                  )}
                  helperText={
                    (touched?.phone && errors?.phone) || validationErrors?.phone
                  }
                  onBlur={handleBlur}
                  onChange={(e) => {
                    handleChange(e);
                    if (onFormDataChange) {
                      onFormDataChange({
                        ...values,
                        [e.target.name]: e.target.value,
                      });
                    }
                  }}
                  required
                />
              </Box>
              <Box className="contact-form-fields">
                <CustomTextField
                  fullWidth
                  name="email"
                  value={values?.email}
                  label="Email"
                  placeholder="Enter email"
                  error={Boolean(
                    (touched?.email && errors?.email) || validationErrors?.email
                  )}
                  helperText={
                    (touched?.email && errors?.email) || validationErrors?.email
                  }
                  onBlur={handleBlur}
                  onChange={(e) => {
                    handleChange(e);
                    if (onFormDataChange) {
                      onFormDataChange({
                        ...values,
                        [e.target.name]: e.target.value,
                      });
                    }
                  }}
                  required
                />
              </Box>
              <Box className="contact-form-fields">
                <CustomTextField
                  fullWidth
                  name="link"
                  value={values?.link}
                  label="Link"
                  placeholder="Enter link (Optional)"
                  onBlur={handleBlur}
                  onChange={(e) => {
                    handleChange(e);
                    if (onFormDataChange) {
                      onFormDataChange({
                        ...values,
                        [e.target.name]: e.target.value,
                      });
                    }
                  }}
                />
              </Box>
            </Box>
          </Form>
        )}
      </Formik>
    </Box>
  );
};

export default ContactForm;
