import React, { useState } from 'react';
import './shiftTabs.scss'; // Import SCSS

const ShiftTabs = ({
  swapList,
  setShiftActiveTab,
  viewAccessOnly,
  setShiftHistoryModal,
}) => {
  const [activeTab, setActiveTab] = useState('2');

  const handleChangeTab = (tab) => {
    setActiveTab(tab);
    if (tab === '3') {
      setShiftHistoryModal(true);
    } else {
      setShiftActiveTab(tab);
    }
  };

  return (
    <div className="shift-tabs-container">
      {/* shift-Tabs */}
      <div className="shift-tabs">
        <div
          className={`shift-tab ${activeTab === '1' ? 'shift-tab-active' : ''}`}
          onClick={() => handleChangeTab('1')}
        >
          Summary
        </div>
        <div
          className={`shift-tab ${activeTab === '2' ? 'shift-tab-active' : ''}`}
          onClick={() => handleChangeTab('2')}
        >
          Requests{' '}
          {swapList?.length > 0 && (
            <span className="shift-tab-badge">{swapList?.length}</span>
          )}
        </div>
        {viewAccessOnly && (
          <div
            className={`shift-tab ${activeTab === '3' ? 'shift-tab-active' : ''}`}
            onClick={() => handleChangeTab('3')}
          >
            History{' '}
          </div>
        )}
      </div>
    </div>
  );
};

export default ShiftTabs;
