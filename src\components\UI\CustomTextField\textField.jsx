import { TextField } from '@mui/material';
import { styled } from '@mui/material/styles';
export const CommonTextField = styled(TextField)(() => ({
  '& .MuiOutlinedInput-root': {
    backgroundColor: 'var(--color-secondary)',
    color: 'var(--text-color-black)',
    borderRadius: 'var(--field-radius)',
    fontFamily: 'var(--font-family-primary)',

    '& .MuiOutlinedInput-input': {
      padding: 'var(--field-padding)',
      fontSize: 'var(--font-size-sm)',
      lineHeight: 'var(--line-height-xs)',
      height: 'auto',
    },
    '&.Mui-focused fieldset': {
      border: 'var(--field-border-primary)',
    },
    '&:hover fieldset': {
      border: 'var(--field-border-primary)',
    },
    '& .MuiInputAdornment-root': {
      color: 'var(--color-primary)',
      marginRight: '0px',
      marginLeft: '0px',

      '& .MuiSelect-select': {
        border: 'none',
      },
      '& fieldset': {
        border: 'none',
      },
      '&:hover fieldset': {
        border: 'none',
      },
      '& .MuiSvgIcon-root': {
        fontSize: 'var(--field-icon-size)',
        fill: 'var(--icon-color-primary)',
      },
    },
  },
  '& ::placeholder': {
    fontFamily: 'var(--font-family-primary)',
    color: 'var(--field-placeholder)',
  },
  '& .MuiOutlinedInput-notchedOutline': {
    border: 'var(--field-border)',
  },
  '& .Mui-disabled': {
    cursor: 'not-allowed',
    color: 'var(--text-color-muted)',
  },
  '& .MuiInputBase-multiline': {
    padding: 'var(--spacing-none)',
  },
}));
