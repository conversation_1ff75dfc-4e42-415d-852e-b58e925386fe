

.container {
  position: relative;
  max-height: calc(100vh - 200px- var(--banner-height));
  overflow: scroll;

  .time-history-wrap {
    .history-item {
      .header-date-wrap {
        padding: var(--spacing-base) var(--spacing-none);
        position: sticky;
        top: var(--spacing-none);
        background: var(--color-white);

        .header-date {
          font-weight: var(--font-weight-bold);
        }

        .calender-icon {
          background-color: var(--color-white);
          position: absolute;
          left: -28px;
          fill: var(--color-dark-50);
          height: 15px;
          width: 15px;
        }
      }

      .name-text-wrap {
        .name-wrap {
          font-weight: var(--font-weight-bold);
        }

        .circle-wrap {
          position: absolute;
          left: 48px;
          fill: var(--color-dark-50);
          height: 5px;
          width: 5px;
        }
      }

      .time-wrap {
        color: var(--color-dark-50);
      }

      .time-spent {
        display: inline-block;
        color: var(--color-dark-50);
        padding-right: var(--spacing-xsm);
      }

      .exicuted-on {
        display: inline-block;
        color: var(--color-dark-50);
        padding-right: var(--spacing-xsm);
      }
    }

    .devider-wrap {
      padding: var(--spacing-none) var(--spacing-lg) var(--spacing-none) var(--spacing-5xl);
    }
  }
}
