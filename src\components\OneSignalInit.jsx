'use client';
import { identifiers } from '@/helper/constants/identifier';
import { Config, saveToStorage } from '@/helper/context';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import OneSignal from 'react-onesignal';

const OneSignalInit = () => {
  const router = useRouter();

  // NOTIFICATION REDIRECT
  const handleNotificationRedirect = (item) => {
    var notiObj = item?.redirection_type;
    var notiObjData = item?.redirection_object;
    const LEAVE = 'leave';
    const ONBOARDING = 'onboarding';
    const RESIGNATION = 'resignation';
    const MEDIA = 'media';
    const USER = 'profile';
    const SIDE_LETTER = 'side_letter';
    switch (notiObj) {
      case LEAVE:
        router.push(`/leave-remark`);
        return true;
      case RESIGNATION:
        router.push(`/resignation-remark/${notiObjData?.resignation_id}`);
        return true;
      case ONBOARDING:
        router.push(`/user/${notiObjData?.user_id}`);
        return true;
      case MEDIA:
        router.push(`/play-list/${notiObjData?.playlist_id}`);
        return true;
      case USER:
        router.push(`/user/${notiObjData?.user_id}`);
        return true;
      case SIDE_LETTER:
        router.push(`/myprofile`);
        return true;
      default:
        return true;
    }
  };

  const initializeOneSignal = async () => {
    await OneSignal.init({
      appId: Config?.OnesignalAppID,
      allowLocalhostAsSecureOrigin: true,
    });
  };

  const oneSignalHandler = async () => {
    setTimeout(() => {
      const playerId = JSON.parse(
        JSON.stringify(OneSignal?.User?.PushSubscription)
      )?.id;
      if (playerId && playerId !== undefined) {
        console.log('OneSignal PlayerID', playerId);
        saveToStorage(identifiers?.DEVICEID, playerId);
      }
    }, 2000);

    OneSignal.Notifications.addEventListener(
      'foregroundWillDisplay',
      function (event) {
        console.log('OneSignal notification displayed:', event);
      }
    );

    OneSignal.Notifications.addEventListener('dismiss', function (event) {
      console.log('OneSignal notification dismissed:', event);
    });

    OneSignal.Notifications.addEventListener(
      'permissionChange',
      function (event) {
        console.log('OneSignal notification Permission Change:', event);
        setTimeout(() => {
          const playerId = JSON.parse(
            JSON.stringify(OneSignal?.User?.PushSubscription)
          )?.id;
          if (playerId && playerId !== undefined) {
            console.log('OneSignal PlayerID', playerId);
            saveToStorage(identifiers?.DEVICEID, playerId);
          }
        }, 2000);
      }
    );

    OneSignal.Notifications.addEventListener('click', function (event) {
      console.log('OneSignal notification Notification Click', event);
      handleNotificationRedirect(event?.notification?.additionalData?.data);
      // const url = event.notification.additionalData.url || "/";
      // window.location.href = url;
    });
  };
  useEffect(() => {
    initializeOneSignal();
    oneSignalHandler();
  }, []);

  return null;
};

export default OneSignalInit;
