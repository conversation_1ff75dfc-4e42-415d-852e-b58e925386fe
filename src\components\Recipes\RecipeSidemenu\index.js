'use client';
import React, { useState, useEffect } from 'react';
import SideMenuList from '@/components/UI/SideMenuList';
import { recipeMenuList } from '@/helper/common/commonMenus';
import { Divider, Typography } from '@mui/material';
import { useRouter, usePathname } from 'next/navigation';

export default function RecipeSidemenu() {
  const router = useRouter();
  const pathname = usePathname();
  const [activeMenuItem, setActiveMenuItem] = useState(1);

  // useEffect(() => {
  //   // Find the menu item that matches the current pathname
  //   const currentMenuItem = recipeMenuList.find((item) =>
  //     pathname.includes(item.slug)
  //   );
  //   if (currentMenuItem) {
  //     setActiveMenuItem(currentMenuItem.id);
  //   }
  // }, [pathname]);

  useEffect(() => {
    const sortedMenuList = [...recipeMenuList].sort(
      (a, b) => b.slug.length - a.slug.length
    );

    const currentMenuItem = sortedMenuList.find((item) =>
      pathname.startsWith(item.slug)
    );

    if (currentMenuItem) {
      setActiveMenuItem(currentMenuItem.id);
    }
  }, [pathname, recipeMenuList]);

  const handleActiveMenuItem = (item) => {
    const menuItem = recipeMenuList?.find(
      (menuItem) => menuItem?.id === item?.id
    );
    setActiveMenuItem(menuItem?.id);

    if (menuItem) {
      router.push(`${menuItem?.slug}`);
    }
  };
  return (
    <>
      <Typography className="sub-header-text section-left-title">
        Recipe
      </Typography>
      <Divider />
      <SideMenuList
        menuItem={recipeMenuList}
        activeId={activeMenuItem}
        onSelect={handleActiveMenuItem}
      />
    </>
  );
}
