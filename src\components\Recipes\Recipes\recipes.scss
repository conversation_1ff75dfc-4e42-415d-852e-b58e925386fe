body {
  .add-edit-recipe-container {
    padding: var(--spacing-none) !important;
    // Add Edit Recipe Component Styles
    .add-edit-recipe {
      // Main layout
      &__layout {
        display: flex;
      }
    }
  }
}

.recipe-page-header {
  &__actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);

    .staff-tracking-btn,
    .reset-btn {
      min-width: 40px;
      height: 40px;

      .MuiButtonBase-root {
        border-color: var(--color-primary);
        color: var(--color-primary);

        &:hover {
          background-color: var(--color-primary);
          color: var(--color-white);
        }
      }
    }
  }
}
