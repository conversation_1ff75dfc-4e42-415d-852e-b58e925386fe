body {
  .dsr-setting-container {
    display: grid;
    grid-template-columns: 35% calc(100% - 35% - 14px);
    row-gap: 14px;
    justify-content: space-between;
    height: 100%;
    overflow: hidden;
    .dsr-settings-right-container {
      height: 100%;
      overflow: auto;
      .dsr-settings-payment-category {
        position: relative;
        svg {
          color: var(--icon-color-primary);
          cursor: pointer;
        }
        .deactive-section {
          margin-right: var(--spacing-sm);
        }
        .dsr-setting-tree {
          padding: var(--spacing-none);
          list-style: none;
          color: var(--color-primary);
          position: relative;
          li {
            margin: var(--spacing-none);
            font-weight: bold;
            position: relative;
            list-style: none;

            &:last-child {
              &:before {
                background: var(--color-white);
                height: auto;
                // top: 1em;
                bottom: 0;
              }
            }
          }

          ul {
            // margin: 0 0 0 1em;
            padding: 0;
            list-style: none;
            color: var(--color-black);
            position: relative;
            margin-left: var(--spacing-sm);

            li {
              margin: 0;
              padding: 0px 5px 0px 22px;
              position: relative;
              list-style: none;
              cursor: pointer;

              &:before {
                content: '';
                display: block;
                width: 22px;
                border-top: 2px solid var(--border-color-muted);
                margin-top: 0px;
                position: absolute;
                left: 0;
                color: var(--color-muted);
                top: 10px;
              }

              &:last-child {
                &:before {
                  background: var(--color-white);
                  bottom: 0;
                  left: 0;
                  top: 10px;
                }
              }
            }

            &:before {
              content: '';
              display: block;
              width: 0;
              position: absolute;
              top: 0px;
              bottom: 0;
              left: 0;
              border-left: 2px solid var(--border-color-muted);
              color: var(--color-muted);
            }
          }
          .main-category-ul {
            &:before {
              top: -10px;
            }
          }
        }
        .list-view-text {
          // width: 85%;
          // @media (max-width: 1100px) {
          //   width: 100%;
          // }
          .MuiFormControlLabel-root {
            align-items: flex-start;
            margin-top: -5px;
          }
        }
        .branch-list-view-text {
          // width: 85%;
          // @media (max-width: 1100px) {
          //   width: 100%;
          // }
          .MuiFormControlLabel-root {
            align-items: flex-start;
            margin-top: 0px;
            .MuiCheckbox-root {
              margin-right: 5px;
              padding: 0px;
            }
          }
        }
        .list-view-selected {
          color: var(--color-success);
          p {
            color: var(--color-success);
          }
        }
        .reorder-icon.selected-reorder {
          fill: var(--icon-color-green);
        }
        .dsr-list {
          margin-top: 22px;
          max-width: max-content;
        }
        .list-view {
          position: relative;
        }
        .add-dsr-category {
          display: flex;
          align-items: flex-start;
          justify-content: flex-start;
          gap: var(--spacing-sm);
          .add-icon {
            width: var(--icon-size-xs);
            height: var(--icon-size-xs);
            cursor: pointer;
            margin-top: 3px;
          }
          .reorder-icon {
            width: var(--icon-size-xs);
            height: var(--icon-size-xs);
            cursor: pointer;
          }
          .reorder-main {
            width: var(--icon-size-xsm);
            height: var(--icon-size-xsm);
            cursor: pointer;
            margin-top: 6px;
          }
        }
        .active-badge {
          padding: 6px;
          // width: 6px;
          // height: 6px;
          background-color: var(--color-success);
          border-radius: 50%;
          margin-top: 5px;
        }
        .inactive-badge {
          padding: 6px;
          // width: 6px;
          // height: 6px;
          background-color: var(--color-danger);
          border-radius: 50%;
          margin-top: 5px;
        }
      }
    }
    .dsr-settings-left-container {
      height: 100%;
      overflow: auto;
      border-left: var(--field-border);
      padding: 0px 12px 10px 12px;
      // max-height: calc(100vh - 160px - var(--banner-height));
      // overflow: scroll;
      .dsr-settings-category-data-grid {
        margin-top: var(--spacing-lg);
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        align-items: flex-start;
        gap: 22px;
      }
      .category-level-0-section,
      .category-level-1-section {
        .category-list-details {
          padding-top: var(--spacing-lg);
          .category-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-md);
            border-radius: var(--border-radius-md);
            margin-top: var(--spacing-xl);
            width: 100%;
            box-shadow: var(--box-shadow-xs);
          }
        }
        .main-level-cards-screen {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: var(--spacing-md);
          border-radius: var(--border-radius-md);
          margin-top: var(--spacing-xl);
          width: 100%;
          box-shadow: var(--box-shadow-xs);
          border: var(--normal-sec-border);
          position: relative;
          .card-details {
            display: flex;
            justify-content: space-between;
            width: 100%;
            .card-left {
              display: flex;
              max-width: calc(100% - 24px - 52px - 20px);
              .cards-fileds {
                display: flex;
                align-items: center;
                flex-wrap: wrap;
              }
            }
            .card-right {
              margin-right: var(--spacing-xl);
              display: flex;
              align-items: center;
            }
            @media (max-width: 680px) {
              .card-right {
                margin-right: var(--spacing-xsm);
              }
            }
            @media (max-width: 480px) {
              display: flex;
              flex-direction: column;
              .card-right {
                // margin-left: var(--spacing-3xl);
                margin-right: var(--spacing-none);
                margin-top: var(--spacing-md);
                align-self: flex-start;
              }
            }
          }
          .close-icon {
            position: absolute;
            right: -10px;
            top: -14px;
            width: var(--icon-size-md);
            height: var(--icon-size-md);
            cursor: pointer;
            fill: var(--icon-color-primary);
          }
          .drag-icon {
            svg {
              cursor: grab !important;
              stroke: var(--icon-color-primary);
            }
          }
        }
        .main-level-cards-screen:first-child {
          margin-top: 0;
        }
        .card-icon {
          column-gap: 5px;
          color: var(--icon-color-primary);
        }
      }
    }
    @media (max-width: 1100px) {
      display: block;
      grid-template-columns: 100%;
      overflow: auto;
      height: auto;
      .dsr-settings-right-container {
        margin: 0px 5px 0px 0px;
      }
      .dsr-settings-left-container {
        margin: 20px 5px 0px 0px;
        padding-top: 20px;
        border-top: var(--field-border);
        border-left: none;
      }
    }
    @media (max-width: 1024px) {
      .dsr-settings-right-container {
        margin: 0px 5px 0px 0px;
      }
      .dsr-settings-left-container {
        padding: 0px 12px 10px 0px;
        margin-right: 0px;
      }
    }
  }
  // Branch
  .branch-list-select-field {
    width: 250px;
  }

  .category-status {
    min-width: 75px;
    margin-left: var(--spacing-xs);
    .category-draft {
      padding: 2px 8px;
      border-radius: var(--border-radius-xs);
      background: var(--color-primary-opacity);
      color: var(--color-primary);
      border: var(--normal-sec-border);
    }
    .category-ongoing {
      padding: 2px 8px;
      border-radius: var(--border-radius-xs);
      background: var(--color-light-success-opacity);
      color: var(--color-light-success);
      border: var(--normal-sec-border);
    }
    .category-accepted {
      padding: 2px 8px;
      border-radius: var(--border-radius-xs);
      background: var(--color-success-opacity);
      color: var(--color-success);
      border: var(--normal-sec-border);
    }
  }
}
