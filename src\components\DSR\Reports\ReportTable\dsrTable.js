'use client';

import React, { useContext } from 'react';
import {
  Box,
  TableHead,
  TableCell,
  TableRow,
  TableBody,
  Table,
  Typography,
  TableContainer,
  Tooltip,
} from '@mui/material';
import AuthContext from '@/helper/authcontext';

export default function DSRTable({ dsrWsrData }) {
  const { authState } = useContext(AuthContext);

  const cellWidth = 120;
  const FixedcellWidth = 190;
  const isChildren = dsrWsrData?.columns_group?.find(
    (f) => f?.children && f?.children?.length > 0
  );
  const isOneCell = Object.keys(dsrWsrData?.column).length === 2;

  return (
    <Box className="DSR-table">
      <TableContainer className="logbook-table-container">
        <Table className="dsr-table">
          <TableHead>
            {dsrWsrData?.columns_group &&
              dsrWsrData?.columns_group?.length > 0 &&
              dsrWsrData?.columns_group?.map((group) => (
                <TableCell
                  colSpan={
                    group?.children?.length ? group?.children?.length : null
                  }
                  align="center"
                  className={
                    group?.key === 'col1' && isChildren
                      ? 'table-head fixed-header-cell fixed-header-cell-children text-align'
                      : group?.key === 'col1'
                        ? 'table-head fixed-header-cell text-align'
                        : isOneCell
                          ? 'table-head w100'
                          : 'table-head'
                  }
                  style={{
                    minWidth:
                      group?.key === 'col1'
                        ? `${FixedcellWidth}px`
                        : group?.children?.length > 0
                          ? `${group?.children?.length * cellWidth}px`
                          : `${cellWidth}px`,
                    maxWidth:
                      group?.key === 'col1'
                        ? `${FixedcellWidth}px`
                        : group?.children?.length > 0
                          ? `${group?.children?.length * cellWidth}px`
                          : `${cellWidth}px`,
                    width: '100%',
                  }}
                >
                  <Box>
                    <Typography className="title-text fw600 d-flex justify-center">
                      {' '}
                      {group?.type === 'total' ? (
                        <Tooltip
                          title={
                            <Typography>
                              {group?.content +
                                ' ( ' +
                                group?.SelectedData?.toString() +
                                ' ) '}
                            </Typography>
                          }
                          classes={{
                            tooltip: 'info-tooltip-container ',
                          }}
                          arrow
                        >
                          <span className="title-text fw600 text-ellipsis-line">
                            {group?.content +
                              ' ( ' +
                              group?.SelectedData?.toString() +
                              ' ) '}
                          </span>
                        </Tooltip>
                      ) : (
                        <Tooltip
                          title={<Typography>{group?.content}</Typography>}
                          arrow
                          classes={{
                            tooltip: 'info-tooltip-container ',
                          }}
                        >
                          {' '}
                          <span className="title-text fw600 text-ellipsis-line">
                            {group?.content}
                          </span>
                        </Tooltip>
                      )}
                    </Typography>
                  </Box>
                  {group?.children && group?.children?.length > 0 && (
                    <TableRow
                      className="group-table-row"
                      style={{
                        flex: 1,
                        minWidth:
                          group?.children?.length > 0
                            ? `${group?.children?.length * cellWidth}px`
                            : `${cellWidth}px`,
                      }}
                    >
                      {group?.children &&
                        group?.children?.length > 0 &&
                        group?.children?.map((item) => (
                          <TableCell
                            className="table-cell heading-cell text-align"
                            style={{
                              flex: 1,
                              minWidth: `${cellWidth}px`,
                            }}
                          >
                            {item?.type === 'total' &&
                            item?.SelectedData &&
                            item?.SelectedData?.length > 0 ? (
                              <Tooltip
                                title={
                                  <Typography>
                                    {item?.content +
                                      ' ( ' +
                                      item?.SelectedData?.toString() +
                                      ' ) '}
                                  </Typography>
                                }
                                classes={{
                                  tooltip: 'info-tooltip-container ',
                                }}
                                arrow
                              >
                                <span className="title-text fw600 text-ellipsis-line">
                                  {item?.content +
                                    ' ( ' +
                                    item?.SelectedData?.toString() +
                                    ' ) '}
                                </span>
                              </Tooltip>
                            ) : (
                              <Tooltip
                                title={<Typography>{item?.content}</Typography>}
                                arrow
                                classes={{
                                  tooltip: 'info-tooltip-container ',
                                }}
                              >
                                <span className="title-text fw600 text-ellipsis-line">
                                  {item?.content}
                                </span>
                              </Tooltip>
                            )}
                          </TableCell>
                        ))}
                    </TableRow>
                  )}
                </TableCell>
              ))}
          </TableHead>

          <TableBody>
            {dsrWsrData?.data?.map((dataRow, rowIndex) => (
              <TableRow
                key={rowIndex}
                className={
                  dsrWsrData?.data && dsrWsrData?.data?.length - 1 === rowIndex
                    ? 'total-row'
                    : ''
                }
              >
                {dsrWsrData?.columns_group &&
                  dsrWsrData?.columns_group?.length > 0 && (
                    <>
                      {dsrWsrData?.columns_group?.map((group, index) => (
                        <>
                          {group?.children && group?.children?.length > 0 ? (
                            <>
                              {group?.children?.map((item, findex) => (
                                <TableCell
                                  key={item?.key}
                                  style={{
                                    minWidth: `${cellWidth}px`,
                                    maxWidth: `${cellWidth}px`,
                                  }}
                                  className={
                                    index === 1 && findex === 0
                                      ? 'table-cell table-value text-align-end firstValue-cell'
                                      : 'table-cell table-value text-align-end'
                                  }
                                >
                                  <Tooltip
                                    title={
                                      <span className="text-ellipsis-line">
                                        {' '}
                                        {item?.has_field_currency ? (
                                          <span className="pr4 ">
                                            {authState?.currency_details &&
                                            authState?.currency_details?.symbol
                                              ? authState?.currency_details
                                                  ?.symbol
                                              : '£'}
                                          </span>
                                        ) : (
                                          ''
                                        )}
                                        {dataRow[item?.key]
                                          ? dataRow[item?.key]
                                          : '-'}
                                      </span>
                                    }
                                    classes={{
                                      tooltip: 'info-tooltip-container ',
                                    }}
                                    arrow
                                  >
                                    {' '}
                                    <span className="title-text text-ellipsis-line">
                                      {item?.has_field_currency ? (
                                        <span className="title-text pr4 ">
                                          {authState?.currency_details &&
                                          authState?.currency_details?.symbol
                                            ? authState?.currency_details
                                                ?.symbol
                                            : '£'}
                                        </span>
                                      ) : (
                                        ''
                                      )}
                                      {dataRow[item?.key]
                                        ? dataRow[item?.key]
                                        : '-'}
                                    </span>
                                  </Tooltip>
                                </TableCell>
                              ))}
                            </>
                          ) : (
                            <>
                              <TableCell
                                style={{
                                  minWidth:
                                    index === 0
                                      ? `${FixedcellWidth}px`
                                      : `${cellWidth}px`,
                                  maxWidth:
                                    index === 0
                                      ? `${FixedcellWidth}px`
                                      : `${cellWidth}px`,
                                }}
                                className={
                                  isOneCell && index !== 0
                                    ? 'table-cell table-value text-align  firstValue-cell one-cell'
                                    : index === 1
                                      ? 'table-cell table-value text-align-end  firstValue-cell'
                                      : group?.key === 'col1'
                                        ? 'table-cell table-value text-align fixed-value'
                                        : 'table-cell table-value text-align-end '
                                }
                              >
                                <Tooltip
                                  title={
                                    <span className="text-ellipsis-line">
                                      {' '}
                                      {group?.has_field_currency ? (
                                        <span className="pr4">
                                          {authState?.currency_details &&
                                          authState?.currency_details?.symbol
                                            ? authState?.currency_details
                                                ?.symbol
                                            : '£'}
                                        </span>
                                      ) : (
                                        ''
                                      )}
                                      {dataRow[group?.key]
                                        ? dataRow[group?.key]
                                        : '-'}
                                    </span>
                                  }
                                  classes={{
                                    tooltip: 'info-tooltip-container ',
                                  }}
                                  arrow
                                >
                                  {' '}
                                  <span className="title-text text-ellipsis-line">
                                    {' '}
                                    {group?.has_field_currency ? (
                                      <span className="title-text pr4">
                                        {authState?.currency_details &&
                                        authState?.currency_details?.symbol
                                          ? authState?.currency_details?.symbol
                                          : '£'}
                                      </span>
                                    ) : (
                                      ''
                                    )}
                                    {dataRow[group?.key]
                                      ? dataRow[group?.key]
                                      : '-'}
                                  </span>
                                </Tooltip>
                              </TableCell>
                            </>
                          )}
                        </>
                      ))}
                    </>
                  )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
}
