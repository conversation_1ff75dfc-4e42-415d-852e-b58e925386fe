import SearchIcon from '@mui/icons-material/Search';
import './rotaMenumodal.scss';

const SearchModal = ({
  searchTerm,
  setSearchTerm,
  filteredStaffList,
  handleSearchToScroll,
}) => {
  return (
    <div className="header-dropdown-menu">
      <div className="search-input-wrapper">
        <SearchIcon className="search-icon" />
        <input
          type="text"
          placeholder="Search employee"
          className="search-input"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>
      {filteredStaffList?.length > 0 ? (
        <ul>
          {filteredStaffList?.map((emp) => (
            <li key={emp?.id} onClick={() => handleSearchToScroll(emp?.id)}>
              {emp?.user_full_name}
            </li>
          ))}
        </ul>
      ) : (
        <p className="text-align h6 mt16">No data found</p>
      )}
    </div>
  );
};

export default SearchModal;
