@import '@/styles/variable.scss';

.custom-search-bar-sec {
  .custom-search-bar {
    .MuiInputBase-root {
      height: 100%;
      min-height: 30px;
      width: 100%;
      max-width: 240px;
      border-radius: 4px;
      padding-left: 3px;
      .MuiInputBase-input {
        padding: 3.5px 0px 3.5px 5px;
        font-family: Inter, sans-serif;
        &::placeholder {
          font-family: Inter, sans-serif;
          font-size: 14px;
          font-weight: 300;
        }
      }
      .MuiOutlinedInput-notchedOutline {
        min-height: 30px;
        border-color: $color-Dark-10;
      }
      .MuiInputAdornment-root {
        .search-icon {
          height: 21px;
          width: 21px;
        }
      }
      @media (max-width: 575px) {
        max-width: 100%;
      }
    }
    @media (max-width: 575px) {
      width: 100%;
    }
  }
  @media (max-width: 575px) {
    width: 100%;
  }
}
