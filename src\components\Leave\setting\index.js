'use client';

import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Tooltip,
  Typography,
  Checkbox,
  FormControlLabel,
  FormGroup,
} from '@mui/material';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import { setApiMessage } from '@/helper/common/commonFunctions';
import RadioButtonCheckedIcon from '@mui/icons-material/RadioButtonChecked';
import RadioButtonUncheckedIcon from '@mui/icons-material/RadioButtonUnchecked';
import { CustomTextField } from '@/components/UI/CommonField';
import InfoIcon from '@mui/icons-material/Info';
import Multiselect from '@/components/UI/Multiselect';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import CustomButton from '@/components/UI/button';
import _ from 'lodash';
import '@/components/Leave/LeaveReports/LeaveBalance/leavebalance.scss';
import './setting.scss';

export default function LeaveSettings() {
  // const [loader, setLoader] = useState(false);

  const [staffList, setStaffList] = useState([]);
  const [generalSettings, setGeneralSettings] = useState();
  const formikRef = useRef(null);
  // List of all staff
  const getUserList = async () => {
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_USER_LIST + `?isAdmin=false`
      );

      if (status === 200) {
        const alloption = [{ label: 'Select all', value: 'all' }];
        let filterUserList = data?.userList?.map((user) => ({
          label: user?.user_full_name,
          value: user?.id,
        }));
        let mergeList = _.concat(alloption, filterUserList);
        setStaffList(mergeList);
      }
    } catch (error) {
      setStaffList([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // List of all staff
  const getSettingList = async () => {
    try {
      const { status, data } = await axiosInstance.get(URLS?.GET_LEAVE_SETTING);

      if (status === 200) {
        setGeneralSettings(data?.data);
        // formikRef.current.setFieldValue('individualuser', data?.data);
        formikRef.current.setFieldValue(
          'allow_past_date_leave',
          data?.data?.allow_past_date_leave ? true : false
        );
        formikRef.current.setFieldValue(
          'apply_leave_upto_limit',
          data?.data?.apply_leave_upto_limit ? true : false
        );
        formikRef.current.setFieldValue(
          'leave_upto_limit',
          data?.data && data?.data?.leave_upto_limit
            ? data?.data?.leave_upto_limit
            : 0
        );
        formikRef.current.setFieldValue(
          'approvalRequired',
          data?.data && data?.data?.policy_request_require_approval
            ? 'yes'
            : 'no'
        );
        formikRef.current.setFieldValue(
          'display_user_list_in_calender',
          data?.data && data?.data?.display_user_list_in_calender ? true : false
        );
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const validationSchema = Yup.object().shape({
    individualuser: Yup.lazy((value, context) => {
      const approvalRequired = context.parent.approvalRequired;
      return approvalRequired === 'yes'
        ? Yup.array()
            .min(1, 'At least one user is required')
            .required('This field is required')
        : Yup.array().nullable().notRequired();
    }),
  });
  useEffect(() => {
    getUserList('', 1, '', '', '');
    getSettingList();
  }, []);
  return (
    <Box className="leave-general-setting">
      {' '}
      <Formik
        innerRef={formikRef}
        initialValues={{
          // documentProofRequired: '',
          allow_past_date_leave:
            generalSettings && generalSettings?.allow_past_date_leave
              ? true
              : false,
          apply_leave_upto_limit:
            generalSettings && generalSettings?.apply_leave_upto_limit
              ? true
              : false,
          leave_upto_limit:
            generalSettings && generalSettings?.leave_upto_limit
              ? generalSettings?.leave_upto_limit
              : 0,
          approvalRequired:
            generalSettings && generalSettings?.leave_request_require_approval
              ? 'yes'
              : 'no',
          individualuser: [],
          display_user_list_in_calender:
            generalSettings && generalSettings?.display_user_list_in_calender
              ? true
              : false,
        }}
        validationSchema={validationSchema}
        onSubmit={async (values) => {
          let getIdss = values?.individualuser?.map((i) => i?.value);
          const leaveApproval = {
            allow_past_date_leave: values?.allow_past_date_leave,
            apply_leave_upto_limit: values?.apply_leave_upto_limit,
            leave_upto_limit: values?.leave_upto_limit
              ? values?.leave_upto_limit
              : 0,
            policy_request_require_approval:
              values?.approvalRequired === 'yes' ? true : false,
            display_user_list_in_calender:
              values?.display_user_list_in_calender,
            policy_setting_approval_meta_form: [
              {
                policy_setting_leval: 1,
                user_id: getIdss && getIdss?.length > 0 ? getIdss : [],
              },
            ],
          };
          try {
            const { status, data } = await axiosInstance.post(
              URLS?.LEAVE_SETTING,
              leaveApproval
            );
            if (status === 200) {
              getSettingList();
              setApiMessage('success', data?.message);
            }
          } catch (error) {
            setApiMessage('error', error?.response?.data?.message);
          }
        }}
      >
        {({
          values,
          errors,
          touched,
          handleChange,
          setFieldValue,
          handleBlur,
        }) => (
          <Form>
            <Box className="leave-application-rules-wrap">
              <Box className="document-proof-wrap mt16">
                <Box className="allow-aplying-box-wrap mt4">
                  <FormGroup className={''}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          className="check-box allow-aplying-box"
                          icon={
                            <CheckBoxOutlineBlankIcon className="uncheck-icon" />
                          }
                          checkedIcon={<CheckBoxIcon className="check-icon" />}
                          disableRipple
                        />
                      }
                      className="check-box-form p12"
                      disableRipple
                      name="display_user_list_in_calender"
                      checked={values?.display_user_list_in_calender}
                      onChange={() =>
                        setFieldValue(
                          'display_user_list_in_calender',
                          !values?.display_user_list_in_calender
                        )
                      }
                      label={
                        <span className="p12">
                          Would you like to display employee leaves in the leave
                          calendar according to user access levels?
                        </span>
                      }
                    />
                  </FormGroup>
                  <FormGroup className={''}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          className="check-box allow-aplying-box"
                          icon={
                            <CheckBoxOutlineBlankIcon className="uncheck-icon" />
                          }
                          checkedIcon={<CheckBoxIcon className="check-icon" />}
                          disableRipple
                        />
                      }
                      className="check-box-form p12"
                      disableRipple
                      name="allow_past_date_leave"
                      checked={values?.allow_past_date_leave}
                      onChange={() =>
                        setFieldValue(
                          'allow_past_date_leave',
                          !values?.allow_past_date_leave
                        )
                      }
                      label={
                        <span className="p12">
                          Allow employees to submit holiday requests for past
                          dates.
                        </span>
                      }
                    />
                  </FormGroup>
                  <FormGroup className="document-proof-label-wrap">
                    <FormControlLabel
                      control={
                        <Checkbox
                          className="check-box allow-aplying-box"
                          icon={
                            <CheckBoxOutlineBlankIcon className="uncheck-icon" />
                          }
                          checkedIcon={<CheckBoxIcon className="check-icon" />}
                          disableRipple
                        />
                      }
                      className="check-box-form p12"
                      disableRipple
                      name="apply_leave_upto_limit"
                      checked={values?.apply_leave_upto_limit}
                      onChange={() =>
                        setFieldValue(
                          'apply_leave_upto_limit',
                          !values?.apply_leave_upto_limit
                        )
                      }
                    />
                    <Box className="d-flex align-center gap-sm document-proof-input-wrap">
                      <Typography className="p12">
                        Employees can apply for holiday up to
                      </Typography>
                      <Box className="leave-policy-input document-proof-input">
                        <CustomTextField
                          className="calendar-days-input"
                          InputLabelProps={{
                            shrink: true,
                          }}
                          fullWidth
                          id="leave_upto_limit"
                          name="leave_upto_limit"
                          value={values?.leave_upto_limit}
                          variant="filled"
                          placeholder="Days"
                          error={Boolean(
                            touched?.leave_upto_limit &&
                              errors?.leave_upto_limit
                          )}
                          helperText={
                            touched?.leave_upto_limit &&
                            errors?.leave_upto_limit
                          }
                          onBlur={handleBlur}
                          onChange={handleChange}
                          onInput={(e) => {
                            let value = e.target.value;
                            // Remove non-numeric characters
                            if (value === '' || /^\d*\.?\d{0,2}$/.test(value)) {
                              e.target.value = value;
                            } else {
                              e.target.value = value.slice(0, -1);
                            }
                          }}
                          disabled={!values?.apply_leave_upto_limit}
                        />
                      </Box>
                      <Typography className="p12">
                        requests exceeding this limit will not be processed.
                      </Typography>
                    </Box>
                  </FormGroup>
                </Box>
              </Box>
            </Box>
            <Box className="leave-approval-wrap">
              <Box className="allow-aplying-leave">
                <Box className="d-flex align-center gap-5 mt8">
                  <Typography
                    variant="h6"
                    className="Inter12 d-flex align-center gap-5"
                  >
                    Does this holiday request require an approval?
                  </Typography>
                  <Tooltip
                    arrow
                    title={
                      <Typography className="p12">
                        Leave requests will get auto approved without taking any
                        action from the concerned person.
                      </Typography>
                    }
                    classes={{ tooltip: 'show-details' }}
                  >
                    <InfoIcon className="info-icon cursor-pointer" />
                  </Tooltip>
                </Box>
                <Box className="allow-aplying-box-wrap mt4">
                  <FormGroup className={''}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          className="check-box allow-aplying-box"
                          icon={
                            <RadioButtonUncheckedIcon className="uncheck-icon" />
                          }
                          checkedIcon={
                            <RadioButtonCheckedIcon className="check-icon" />
                          }
                          disableRipple
                        />
                      }
                      name="approvalRequired"
                      value="no"
                      className="check-box-form p12"
                      checked={values?.approvalRequired === 'no'}
                      onChange={(e) => {
                        setFieldValue('approvalRequired', e?.target?.value);
                      }}
                      label={
                        <Box className="d-flex align-center gap-5">
                          <span className="p12">No</span>
                          <Tooltip
                            arrow
                            title={
                              <Typography className="p12">
                                Leave requests will be sent to the concerned
                                person and they will take action accordingly.
                              </Typography>
                            }
                            classes={{ tooltip: 'show-details' }}
                          >
                            <InfoIcon className="info-icon cursor-pointer" />
                          </Tooltip>
                        </Box>
                      }
                    />
                  </FormGroup>
                  <FormGroup className={''}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          className="check-box allow-aplying-box"
                          icon={
                            <RadioButtonUncheckedIcon className="uncheck-icon" />
                          }
                          checkedIcon={
                            <RadioButtonCheckedIcon className="check-icon" />
                          }
                          disableRipple
                        />
                      }
                      value="yes"
                      name="approvalRequired"
                      className="check-box-form p12"
                      checked={values?.approvalRequired === 'yes'}
                      onChange={(e) => {
                        setFieldValue('approvalRequired', e?.target?.value);
                      }}
                      label={
                        <Box className="d-flex align-center gap-5">
                          <span className="p12">Yes</span>
                          <Tooltip
                            arrow
                            title={
                              <Typography className="p12">
                                This feature allows leave applicants to notify
                                (via Email) additional individuals about their
                                leave request by selecting and adding them
                                during the application process.
                              </Typography>
                            }
                            classes={{ tooltip: 'show-details' }}
                          >
                            <InfoIcon className="info-icon cursor-pointer" />
                          </Tooltip>
                        </Box>
                      }
                    />
                  </FormGroup>
                </Box>
              </Box>
              {values?.approvalRequired === 'yes' ? (
                <>
                  {' '}
                  <Box className="leave-approval-select pt32">
                    <Multiselect
                      placeholder="Staff user"
                      className={
                        values?.branch?.length > 0 ||
                        values?.department?.length > 0 ||
                        values?.role?.length > 0
                          ? 'disabled-multifield'
                          : touched.individualuser && errors.individualuser
                            ? 'textfeild-error'
                            : ''
                      }
                      options={staffList}
                      value={values?.individualuser}
                      error={touched.individualuser && errors.individualuser}
                      // onChange={(e) => setFieldValue('individualuser', e)}
                      onChange={(e) => {
                        const filterValue = e?.find((f) => f?.value === 'all');
                        const branchValue = staffList?.filter(
                          (f) => f?.value !== 'all'
                        );
                        if (filterValue || e?.length === branchValue?.length) {
                          const filterall = staffList?.filter(
                            (b) => b?.value !== 'all'
                          );
                          setStaffList(filterall);
                          const selectedValue = filterValue ? filterall : e;
                          setFieldValue('individualuser', selectedValue);
                        }
                        if (!filterValue) {
                          const isAll = staffList?.find(
                            (f) => f?.value === 'all'
                          );
                          if (!isAll) {
                            const alloption = [
                              { label: 'Select all', value: 'all' },
                            ];
                            let mergeList = _.concat(alloption, staffList);
                            setStaffList(mergeList);
                          }
                          setFieldValue('individualuser', e);
                        }
                      }}
                      label={
                        <span>
                          Staff user
                          {/* <span className="primary-color"> *</span> */}
                        </span>
                      }
                      disabled={
                        values?.branch?.length > 0 ||
                        values?.department?.length > 0 ||
                        values?.role?.length > 0
                      }
                    />
                    {touched.individualuser && errors.individualuser && (
                      <Typography
                        variant="body2"
                        color="error"
                        className="field-error"
                      >
                        {errors.individualuser}
                      </Typography>
                    )}
                  </Box>
                  <Box>
                    <Typography className="p12 note-text mb8 mt8">
                      <span className="Inter12">Note: </span> Make sure you only
                      assign employees to the approval process who have the
                      rights to approve or reject at their assigned access
                      levels.
                    </Typography>
                    <Typography className="p12 note-text">
                      <span className="Inter12">Note: </span> If you have add
                      Line Manager in Access Level then make sure that Line
                      Manager is available in Custom UAC OR Default Manager UAC
                      OR Admin UAC. So, That Line Manager have Approve & Reject
                      Access.
                    </Typography>
                  </Box>
                </>
              ) : (
                <></>
              )}
            </Box>
            <Box className="btns-wrap d-flex align-center justify-end mt16">
              <CustomButton
                className="p16 apply-btn"
                type="submit"
                fontWeight="600"
                variant="contained"
                background="#39596e"
                backgroundhover="#FFFFFF"
                colorhover="#000000"
                title="Save"
                // disabled={loader}
              />
              {/* <CustomButton
                className="p16 cancel-btn"
                fontWeight="600"
                variant="contained"
                background="#39596e"
                backgroundhover="#FFFFFF"
                colorhover="#000000"
                title="cancel"
                onClick={() => router.push('/own-leave')}
              /> */}
            </Box>
          </Form>
        )}
      </Formik>
    </Box>
  );
}
