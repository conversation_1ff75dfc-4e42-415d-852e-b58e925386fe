import React from 'react';
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material';
import moment from 'moment';
import './leavepopover.scss';

const leave_type = {
  full_day: 'Full Day',
  first_half: '1st Half',
  second_half: '2nd Half',
  day_off: 'Day-off',
  holiday: 'Holiday',
  day_off_holiday: 'Day off with Holiday',
};

export default function LeavePopOver({
  title,
  Leavedays,
  leave,
  startDate,
  endDate,
  leaveTypes,
  leaveDetails,
  leave_days_obj,
  leave_days,
}) {
  // Function to generate dates in the range
  const getDatesInRange = (start_date, end_date) => {
    const start = moment(start_date);
    const end = moment(end_date);
    const dates = [];
    for (let date = start; date <= end; date.add(1, 'day')) {
      dates.push(date.format('ddd DD MMM, YYYY'));
    }
    return dates;
  };

  // function calculateHoursAndMinutes(startDate, endDate) {
  //   const start = moment(startDate, 'YYYY-MM-DD HH:mm');
  //   const end = moment(endDate, 'YYYY-MM-DD HH:mm');

  //   // Calculate total duration
  //   const duration = moment.duration(end.diff(start));
  //   const totalHours = Math.floor(duration.asHours());
  //   const totalMinutes = duration.minutes();

  //   // Format start and end times
  //   const startTimeFormatted = start.format('hh:mmA'); // 12-hour format with AM/PM
  //   const endTimeFormatted = end.format('hh:mmA');

  //   // Final output format
  //   return `Total Hours ${String(totalHours).padStart(2, '0')}H:${String(totalMinutes).padStart(2, '0')}M (${startTimeFormatted} to ${endTimeFormatted})`;
  // }

  // Get the range of dates
  const dateRange = getDatesInRange(startDate, endDate);
  // const getHoursRange = calculateHoursAndMinutes(startDate, endDate);
  return (
    <Box className="leave-table">
      <Typography variant="h6" className="popover-title-wrap p14">
        {title}
      </Typography>
      <Table size="small">
        <TableHead className="table-header">
          <TableRow>
            <TableCell className="days-wrap p14 ">{Leavedays}</TableCell>
            <TableCell className="leave-wrap p14 ">{leave}</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {(startDate || endDate) && leave_days_obj
            ? leave_days_obj?.map((leaveDates, index) => (
                <TableRow key={index}>
                  <TableCell>{leaveDates?.date || '-'}</TableCell>
                  <TableCell>
                    {leave_type[leaveDates?.type] ||
                      leaveDates?.total_hours + 'h' ||
                      '-'}
                  </TableCell>
                </TableRow>
              ))
            : (startDate || endDate) && (
                <TableRow>
                  <TableCell>
                    {dateRange?.[0]} <br />
                    {' To '}
                    <br />
                    {dateRange?.[dateRange?.length - 1]}
                  </TableCell>
                  <TableCell>
                    {/* {leaveTypes === 'day' ? 'Full Day' : getHoursRange || '-'} */}
                    {leave_days + (leaveTypes === 'hour' ? 'h' : 'd')}
                  </TableCell>
                </TableRow>
              )}
          {leaveDetails && leaveDetails?.length > 0 ? (
            leaveDetails?.map((leave) => {
              return (
                <TableRow key={leave?.id}>
                  <TableCell>{leave?.leaveName}</TableCell>
                  <TableCell>
                    {leave?.leaveUnlimited
                      ? 'Unlimited'
                      : (leave?.leaveCount ?? '0')}
                    {/* leave?.leaveCount && leave?.leaveCountHours ?
                    leave?.leaveCount + 'D ' + leave?.leaveCountHours + 'H' :
                    leave?.leaveCount && leave?.leaveCountHours !== undefined ?
                    leave?.leaveCount + 'D' : leave?.leaveCount &&
                    leave?.leaveCountHours === undefined ? leave?.leaveCount :
                    leave?.leaveCountHours ? leave?.leaveCountHours + 'H' : '0'} */}
                  </TableCell>
                </TableRow>
              );
            })
          ) : (
            <></>
          )}
        </TableBody>
      </Table>
    </Box>
  );
}
