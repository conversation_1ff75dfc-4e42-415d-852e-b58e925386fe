.login-page-wrap {
  position: relative;

  .login-wrap {
    position: relative;
    z-index: 1;
    padding: 0px 20px;

    .login-pages {
      width: 100%;
      max-width: 550px;
      margin: 0 auto;
      background-color: var(--color-white);
      box-shadow: 0px 0px 110px 0px#0260E133;
      border-top-left-radius: 22px;
      border-top-right-radius: 22px;

      .login-blocks {
        height: 100%;
        min-height: 550px;
        padding: 52px;

        .main-heading {
          text-align: left;
          padding-bottom: 42px;
        }

        .login-form-wrap {
          .input-wrap {
            padding-bottom: 32px;

            .label-wrap {
              font-size: 14px;
              line-height: 20px;
              font-weight: 300;
              color: var(--text-slate-gray);
              font-family: var(--font-family-poly-slim) !important;
            }

            .MuiInputBase-root {
              display: flex;
              align-items: center;

              .eye-wrap {
                svg {
                  cursor: pointer;
                }
              }

              .MuiInputBase-input {
                font-size: 16px;
                line-height: 26px;
                font-weight: 400;
                margin: 5px 0px 10px;
                height: 100%;
                min-height: 26px;
                padding: 0px;
                color: #1d1e25 !important;
                font-family: var(--font-family-poly-slim) !important;

                .image-wrap {
                  height: 24px;
                  width: 24px;
                }

                &::placeholder {
                  font-size: 16px !important;
                  line-height: 26px !important;
                  font-weight: 400 !important;
                  margin: 5px 0px 10px !important;
                  padding: 0px !important;
                  font-family: var(--font-family-poly-slim) !important;
                  text-transform: capitalize !important;
                }
              }

              &::before {
                border-bottom: 1px solid #e2e2ea;
              }
            }

            .Mui-error {
              font-family: var(--font-family-poly-slim) !important;

              .eye-wrap {
                path {
                  stroke: var(--color-danger);
                }

                svg {
                  fill: none;
                }
              }

              &::before {
                border-bottom: 1px solid var(--color-danger);
              }
            }
          }

          .forgot-wrap {
            font-size: 16px;
            font-weight: 300;
            line-height: 26px;
            text-align: right;
            color: var(--text-color-primary) !important;
            padding-bottom: 32px;
            font-family: var(--font-family-poly-bulky) !important;
          }

          .log-in-btn-wrap {
            .log-in-btn {
              border-radius: 12px !important;
              background-color: var(--color-primary) !important;
              font-size: 16px !important;
              line-height: 20px !important;
              font-weight: 300 !important;
              padding: 16px !important;
              border: none !important;
              color: var(--text-color-white) !important;
              font-family: var(--font-family-poly-median) !important;

              &:hover {
                font-family: var(--font-family-poly-median) !important;
                background-color: var(--color-primary) !important;
                color: var(--text-color-white) !important;
                box-shadow: none !important;
              }
            }
          }

          .dont-have-acc-text {
            font-size: 16px;
            line-height: 26px;
            font-weight: 300;
            text-align: center;
            color: var(--text-lavender-gray);
            padding-top: 22px;
            font-family: var(--font-family-poly-slim) !important;

            .sign-in {
              cursor: pointer;
              margin-left: 6px;
              font-weight: 600;
              color: var(--text-color-primary) !important;
              font-family: var(--font-family-poly-median) !important;
            }
          }
          .need-help-text {
            padding-top: 10px;
          }
        }

        @media (max-width: 767px) {
          min-height: 100%;
        }

        @media (max-width: 575px) {
          padding: 35px 25px 0px;
        }

        @media (max-width: 424px) {
          padding: 35px 19px;
        }
      }
    }

    @media (max-width: 475px) {
      padding: 0px 14px;
    }
  }

  .left-vector {
    position: absolute;
    left: 102px;
    top: 139px;
    z-index: 0;

    @media (max-width: 1380px) {
      left: 50px;
    }

    @media (max-width: 767px) {
      display: none;
    }
  }

  .right-vector {
    position: absolute;
    top: 140px;
    right: 0px;
    z-index: 0;

    @media (max-width: 767px) {
      display: none;
    }
  }
  .email-verified-content {
    background-color: #00bf00;
    color: var(--text-color-white);
    padding: 5px 3px;
    margin-bottom: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
    .verified-icon {
      margin-right: 4px;
    }
    .email-text {
      font-size: 16px;
      line-height: 26px;
      font-weight: 300;
      text-align: center;
      color: var(--text-color-white);
      font-family: var(--font-family-poly-slim) !important;
    }
  }
  .content-loader {
    text-align: center;
    .main-heading {
      text-align: center !important;
    }
    .loader {
      color: var(--color-primary);
      height: 30px !important;
      width: 30px !important;
    }
  }
}
