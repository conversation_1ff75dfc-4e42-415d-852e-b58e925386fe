'use client';

import React, { useContext, useEffect, useState } from 'react';
import { Box } from '@mui/material';
import AuthContext from '@/helper/authcontext';
import { removeFromStorage, fetchFromStorage } from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import WSRRequestPage from '@/components/DSR/WSRRequest/index';
import '../dsr.scss';
import dayjs from 'dayjs';

export default function DSRRequestView() {
  const { authState, userdata, setUserdata, AllListsData } =
    useContext(AuthContext);
  const [dsrReqData, setdsrReqData] = useState([]);
  // const [loader, setLoader] = useState(true);
  const [loaderUser, setLoaderUser] = useState(true);
  const [reqTotalCount, setReqTotalCount] = useState(0);
  const [filterDataReq, setFilterDataReq] = useState({
    branch: '',
    sdate: '',
    edate: '',
    status,
  });
  const [filterDataAppliedReq, setFilterDataAppliedReq] = useState({
    branch: '',
    sdate: '',
    edate: '',
    status,
  });
  const [rowsPerPage] = useState(10);
  const [rowsPerPageReq, setRowsPerPageReq] = useState(10);

  // List of DSR request
  const getDsrRequestList = async (
    search,
    pageNo,
    branch,
    sdate,
    edate,
    statusdata,
    Rpp
  ) => {
    // setLoader(true);
    setLoaderUser(true);
    const sdatef = sdate ? dayjs(sdate)?.format('YYYY-MM-DD') : '';
    const edatef = edate ? dayjs(edate)?.format('YYYY-MM-DD') : '';

    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_WSR_REQUEST_LIST +
          `?search=${search}&page=${pageNo}&size=${
            Rpp ? Rpp : rowsPerPage
          }&branch_id=${branch}&wsr_start_date=${sdatef}&wsr_end_date=${edatef}&request_status=${statusdata}`
      );
      if (status === 200) {
        // setLoader(false);

        let dsr =
          data?.data &&
          data?.data?.length > 0 &&
          data?.data?.map((d) => {
            return {
              ...d,
              branch: d?.wsr_branch,
              submitedby: d?.dsr_user?.user_full_name,
            };
          });
        dsr ? setdsrReqData(dsr) : setdsrReqData([]);
        removeFromStorage(identifiers?.RedirectData);
        setUserdata();
        setReqTotalCount(data?.count);
        setLoaderUser(false);
      }
    } catch (error) {
      // setLoader(false);
      setLoaderUser(false);
      setdsrReqData([]);
      setReqTotalCount(0);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  useEffect(() => {
    if (
      authState?.UserPermission?.dsr === 1 ||
      authState?.UserPermission?.dsr === 2
    ) {
      if (
        (!fetchFromStorage(identifiers?.RedirectData) &&
          userdata?.page === undefined &&
          fetchFromStorage(identifiers?.RedirectData)?.IsFromUser ===
            undefined &&
          userdata?.IsFromUser === undefined) ||
        fetchFromStorage(identifiers?.RedirectData)?.wsrr === undefined ||
        userdata?.wsrr === undefined
      ) {
        getDsrRequestList('', 1, '', '', '', '');
        removeFromStorage(identifiers?.RedirectData);
        setUserdata();
      }
    }
  }, [authState?.UserPermission?.dsr]);

  return (
    <>
      <Box className="dsr-page-section">
        <WSRRequestPage
          authState={authState}
          branchList={AllListsData?.ActiveBranchList}
          getDsrData={getDsrRequestList}
          loader={loaderUser}
          rowsPerPage={rowsPerPageReq}
          setRowsPerPage={setRowsPerPageReq}
          dsrData={dsrReqData}
          totalCount={reqTotalCount}
          setFilterData={setFilterDataReq}
          filterData={filterDataReq}
          setFilterDataAppliedReq={setFilterDataAppliedReq}
          filterDataAppliedReq={filterDataAppliedReq}
        />
      </Box>
    </>
  );
}
