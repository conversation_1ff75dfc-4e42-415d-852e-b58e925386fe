import React, { useState } from 'react';
import { Box, Typography } from '@mui/material';
import Icon from '@/components/UI/AppIcon/AppIcon';
import '../styles/SelectedFieldsList.scss';

const SelectedFieldsList = ({ selectedFields, onReorder, onRemoveField }) => {
  const [draggedIndex, setDraggedIndex] = useState(null);

  const handleDragStart = (e, index) => {
    setDraggedIndex(index);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDragEnter = (e, index) => {
    e.preventDefault();
    if (draggedIndex !== null && draggedIndex !== index) {
      onReorder(draggedIndex, index);
      setDraggedIndex(index);
    }
  };

  const handleDragEnd = () => {
    setDraggedIndex(null);
  };

  const handleMoveUp = (index) => {
    if (index > 0) {
      onReorder(index, index - 1);
    }
  };

  const handleMoveDown = (index) => {
    if (index < selectedFields?.length - 1) {
      onReorder(index, index + 1);
    }
  };

  if (selectedFields?.length === 0) {
    return (
      <Box className="selected-fields-list">
        <Box className="empty-state">
          <Box className="empty-content">
            <Box className="empty-icon">
              <Icon
                name="List"
                size={24}
                color="#64748B"
                strokeWidth={2}
                className="icon"
              />
            </Box>
            <Box className="empty-text">
              <h3 className="empty-title">No Fields Selected</h3>
              <p className="empty-description">
                Select fields from the available categories to configure your
                export
              </p>
            </Box>
          </Box>
        </Box>
      </Box>
    );
  }
  return (
    <Box className="selected-fields-list">
      <Box className="list-header">
        <Box className="header-content">
          <Typography className="body-text header-title">
            Export Field Sequence
          </Typography>
          <Box>
            <Typography className="sub-title-text header-hint text-align-end">
              Drag to reorder or use arrow buttons
            </Typography>
            <Typography className="sub-title-text required-count text-align-end">
              Required: {selectedFields?.filter((f) => f?.required).length}
            </Typography>
          </Box>
        </Box>
      </Box>

      <Box className="fields-container">
        <Box className="fields-list">
          {selectedFields?.map((field, index) => (
            <Box
              key={field?.id}
              draggable
              onDragStart={(e) => handleDragStart(e, index)}
              onDragOver={handleDragOver}
              onDragEnter={(e) => handleDragEnter(e, index)}
              onDragEnd={handleDragEnd}
              className={`field-item ${
                draggedIndex === index ? 'dragging' : ''
              }`}
            >
              <Box className="field-content">
                <Box className="drag-controls">
                  <Box className="drag-handle">
                    <Icon
                      name="GripVertical"
                      size={16}
                      color="#64748B"
                      strokeWidth={2}
                      className="drag-icon"
                    />
                  </Box>
                  <span className="sub-title-text order-badge">
                    {field?.order}
                  </span>
                </Box>

                <Box className="field-info">
                  <Box className="field-header">
                    <span className="title-text field-name">{field?.name}</span>
                    {field?.required && (
                      <span className="sub-title-text required-badge">
                        Required
                      </span>
                    )}
                  </Box>
                  {/* <Typography className="sub-title-text field-category">
                    {field?.categoryName}
                  </Typography> */}
                </Box>
              </Box>

              <Box className="field-actions">
                {/* Mobile: Up/Down arrows */}
                <Box className="mobile-controls">
                  <button
                    onClick={() => handleMoveUp(index)}
                    disabled={index === 0}
                    className="move-button"
                  >
                    <Icon
                      name="ChevronUp"
                      size={16}
                      strokeWidth={2}
                      className="move-icon"
                    />
                  </button>
                  <button
                    onClick={() => handleMoveDown(index)}
                    disabled={index === selectedFields?.length - 1}
                    className="move-button"
                  >
                    <Icon
                      name="ChevronDown"
                      size={16}
                      strokeWidth={2}
                      className="move-icon"
                    />
                  </button>
                </Box>

                {/* Remove button */}
                <button
                  onClick={() => onRemoveField(field?.id)}
                  disabled={field?.required}
                  className="remove-button"
                  title={
                    field?.required
                      ? 'Required field cannot be removed'
                      : 'Remove field'
                  }
                >
                  <Icon
                    name="X"
                    size={16}
                    strokeWidth={2}
                    className="remove-icon"
                  />
                </button>
              </Box>
            </Box>
          ))}
        </Box>
      </Box>

      {/* <Box className="list-footer">
        <Box className="footer-stats">
          <span className="sub-title-text total-count">
            Total fields: {selectedFields?.length}
          </span>
          <span className="sub-title-text required-count">
            Required: {selectedFields?.filter((f) => f?.required).length}
          </span>
        </Box>
      </Box> */}
    </Box>
  );
};

export default SelectedFieldsList;
