import React from 'react';
import { Box, Typography } from '@mui/material';
import UserDetails from '@/components/Leave/AssignEmployee/userdetails';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';

const SelectAllToggle = ({ selected, total, onToggle }) => {
  const allSelected = selected?.length === total;

  return (
    <Typography
      className={`sub-title-text fw600 cursor-pointer ${allSelected ? 'color-red' : 'color-green'}`}
      onClick={onToggle}
    >
      {allSelected ? 'Remove All' : 'Select All'}
    </Typography>
  );
};

const UserListSection = ({
  title,
  userList,
  selectedIds,
  onToggleAll,
  onItemClick,
  rightPadding = false,
  isGeneral,
}) => {
  return (
    <Box className={rightPadding ? 'right-grid' : 'left-grid'}>
      <Box
        className={`assign-role-title d-flex justify-space-between ${rightPadding ? 'pl4' : 'pr4'} ${isGeneral ? '' : 'assign-role-title-grid'}`}
      >
        <Typography className="title-text fw600">{title}</Typography>
        <SelectAllToggle
          selected={selectedIds}
          total={userList?.length}
          onToggle={onToggleAll}
        />
      </Box>

      {userList && userList?.length > 0 ? (
        <>
          {userList?.map((user) => (
            <Box
              key={user?.id}
              className={`d-flex justify-space-between align-center ${rightPadding ? 'pl4' : ''}`}
            >
              <UserDetails user={user} />
              <Box className="d-flex align-center justify-center mr20">
                {selectedIds?.includes(user?.id) ? (
                  <CheckBoxIcon
                    className="svg-icon cursor-pointer"
                    onClick={() => onItemClick(user?.id)}
                  />
                ) : (
                  <CheckBoxOutlineBlankIcon
                    className="svg-icon cursor-pointer"
                    onClick={() => onItemClick(user?.id)}
                  />
                )}
              </Box>
            </Box>
          ))}
        </>
      ) : (
        <>
          <Box className="mt32">
            <Typography className="text-align h6 color-gray">
              Not any user assigned.
            </Typography>
          </Box>
        </>
      )}
    </Box>
  );
};
export default UserListSection;
