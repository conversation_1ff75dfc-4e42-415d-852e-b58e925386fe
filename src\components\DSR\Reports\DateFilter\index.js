'use client';
import React, { useState, useMemo, useEffect } from 'react';
import { Text } from 'intergalactic/typography';
import { Box } from 'intergalactic/flex-box';
import Select from 'intergalactic/select';
import { FilterTrigger } from 'intergalactic/base-trigger';
import Ellipsis from 'intergalactic/ellipsis';
// import ExpandMoreOutlinedIcon from '@mui/icons-material/ExpandMoreOutlined';
// import ClearIcon from '@mui/icons-material/Clear';
import { DropdownCloseIcon, DropdownIcon } from '@/helper/common/images';
import './datefilter.scss';

export default function DateFilter({
  dateSelectedOption,
  setDateSelectedOption,
  dateFilterList,
  setCustomStartDate,
  setCustomEndDate,
  // getDateCustomFilterGeneral,
  // setIsDateCustGEN,
  type,
}) {
  const [visible, setVisible] = useState(false);
  const [filter] = useState('');
  const [loading] = useState(false);

  // Default to selecting "Today" initially if no option is selected
  useEffect(() => {
    setDateSelectedOption(
      dateSelectedOption
        ? dateSelectedOption
        : type === 'customGen'
          ? 'current_month'
          : 'today'
    );
  }, []);

  // Memoized filtered options based on the search filter
  const filteredOptions = useMemo(() => {
    if (filter) {
      return dateFilterList?.filter((option) =>
        option.label.toLowerCase().includes(filter.toLowerCase())
      );
    }
    return dateFilterList;
  }, [dateFilterList]);

  // Handle visibility change of the select dropdown
  const handleChangeVisible = (visible) => {
    setVisible(visible);
    // if (visible) {
    //   setLoading(true);
    //   setTimeout(() => setLoading(false), 1000);
    // }
  };

  // Handle the selection of a time period
  const handleSelectTimePeriod = (periodId) => {
    setDateSelectedOption(periodId);
  };

  // Clear selected dates and reset to "Today"
  const handleClearDates = () => {
    setDateSelectedOption(type === 'customGen' ? 'current_month' : 'today');
    setCustomStartDate(null);
    if (type === 'dsr') {
      setCustomEndDate(null);
    }
  };
  return (
    <Select
      placeholder="Date Filter"
      value={dateSelectedOption || ''}
      onVisibleChange={handleChangeVisible}
      visible={visible}
      onChange={setDateSelectedOption}
      className="date-filter-select"
    >
      <Select.Trigger className="intergalactic-multiselect" tag={FilterTrigger}>
        {dateSelectedOption === 'custom' ? (
          <Box className="d-flex justify-space-between gap-sm w100">
            <Box className="d-flex align-center gap-5">
              <span className="branches-wrap">Date Filter :</span>
              <span className="dates-wrap">
                <span>
                  {
                    dateFilterList.find(
                      (period) => period?.value === dateSelectedOption
                    )?.label
                  }
                </span>
              </span>
            </Box>
            <Box className="d-flex align-center gap-5 ">
              <Box className="d-flex">
                <DropdownCloseIcon
                  className="clear-icon"
                  onClick={handleClearDates}
                  // fontSize="small"
                />
              </Box>
              <Box className="d-flex">
                <DropdownIcon
                  className="down-arrow-wrap"
                  // fontSize="small"
                />
              </Box>
            </Box>
          </Box>
        ) : (
          <Box className="d-flex justify-space-between gap-sm w100">
            <Box className="d-flex align-center gap-5">
              <span className="branches-wrap">Date Filter :</span>
              <span>
                {
                  dateFilterList?.find(
                    (period) => period?.value === dateSelectedOption
                  )?.label
                }
              </span>
            </Box>
            <Box className="d-flex align-center gap-5">
              <DropdownIcon
                className="down-arrow-wrap"
                // fontSize="small"
              />
            </Box>
          </Box>
        )}
      </Select.Trigger>
      <Select.Popper
        className="select-input-wrap"
        aria-label="Options with search"
      >
        {loading && (
          <Text tag="div" m="10px 8px" size={200} use="secondary">
            Loading...
          </Text>
        )}
        {!loading && (
          <>
            <Select.List id="search-list">
              {filteredOptions?.map((option, index) => (
                <Select.Option
                  key={option?.value}
                  value={option?.value}
                  id={`option-${index}`}
                  onClick={() => handleSelectTimePeriod(option?.value)}
                  aria-selected={dateSelectedOption === option?.value}
                >
                  <Ellipsis placement="right">
                    <Ellipsis.Content>
                      <span>{option?.label}</span>
                    </Ellipsis.Content>
                  </Ellipsis>
                </Select.Option>
              ))}
              {!filteredOptions?.length && (
                <Select.OptionHint>No data found</Select.OptionHint>
              )}
            </Select.List>
          </>
        )}
      </Select.Popper>
    </Select>
  );
}
