'use client';

import React from 'react';
import ShiftMonthView from '@/components/UI/CalendarViews/ShiftMonthView';
import ShiftWeekView from '@/components/UI/CalendarViews/ShiftWeekView';

const RotasCalendar = ({
  staffListData,
  filterData,
  shiftList,
  rolesList,
  departmentList,
  locationList,
  getRotaShiftList,
  dayOffList,
  setDayOffList,
  viewAccessOnly,
  availabilityList,
  setIsUpdatedShift,
  isSearchCollapse,
  userLeaveList,
}) => {
  return (
    <>
      {filterData?.dayMonth === 'month' ? (
        <ShiftMonthView
          staffListData={staffListData}
          shiftData={shiftList}
          filterData={filterData}
          rolesList={rolesList}
          departmentList={departmentList}
          getRotaShiftList={getRotaShiftList}
          locationList={locationList}
          dayOffList={dayOffList}
          setDayOffList={setDayOffList}
          viewAccessOnly={viewAccessOnly}
          availabilityList={availabilityList}
          setIsUpdatedShift={setIsUpdatedShift}
          isSearchCollapse={isSearchCollapse}
          userLeaveList={userLeaveList}
        />
      ) : (
        <ShiftWeekView
          staffListData={staffListData}
          shiftData={shiftList}
          filterData={filterData}
          rolesList={rolesList}
          departmentList={departmentList}
          getRotaShiftList={getRotaShiftList}
          locationList={locationList}
          dayOffList={dayOffList}
          setDayOffList={setDayOffList}
          viewAccessOnly={viewAccessOnly}
          availabilityList={availabilityList}
          setIsUpdatedShift={setIsUpdatedShift}
          isSearchCollapse={isSearchCollapse}
          userLeaveList={userLeaveList}
        />
      )}
    </>
  );
};

export default RotasCalendar;
