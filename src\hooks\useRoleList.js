import { useState, useCallback } from 'react';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import { setApiMessage } from '@/helper/common/commonFunctions';

const useRoleList = () => {
  const [roleList, setRoleList] = useState([]);
  const [loading, setLoading] = useState(false);

  const fetchRoleList = useCallback(async (isAdminFilter = false) => {
    setLoading(true);
    try {
      const { status, data } = await axiosInstance.get(URLS?.GET_ROLE_LIST);
      if (status === 200) {
        const adminFilter = isAdminFilter
          ? data?.data?.filter((f) => f?.id !== 2)
          : data?.data;
        const formattedList = adminFilter?.map((user) => ({
          label: user?.role_name,
          value: user?.id,
        }));
        setRoleList(formattedList);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    } finally {
      setLoading(false);
    }
  }, []);

  return { roleList, loading, fetchRoleList };
};

export default useRoleList;
