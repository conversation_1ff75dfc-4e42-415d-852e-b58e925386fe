import React from 'react';
import { Box, Typography } from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import HeaderImage from '@/components/UI/ImageSecurity';
import DialogBox from '@/components/UI/Modalbox';
import CustomSignature from '../CustomSignature';
import './signature.scss';

const SignatureField = ({
  signValue,
  setSignValue,
  addSign,
  setAddSign,
  setIsSubmit,
  formikRef,
  title = "Employer's Signature",
  width = 160,
  height = 50,
  required = true,
  ViewAccessOnly,
  error,
  helperText,
}) => {
  return (
    <Box className="signature-field">
      {signValue && signValue?.url ? (
        <Box>
          <Box className="lable-wrap d-flex justify-content-between align-items-center">
            <Typography className="label-color">
              {title}
              {required && <span className="required-field">*</span>}
            </Typography>
            {!ViewAccessOnly && (
              <Box
                className="signature-edit-wrap"
                onClick={() => {
                  setAddSign(true);
                }}
              >
                <EditIcon className="signature-edit" />
              </Box>
            )}
          </Box>
          <Box className="signature-section">
            <HeaderImage
              imageUrl={signValue?.url}
              width={width}
              height={height}
              alt={title}
              type="lazyload"
            />
          </Box>
        </Box>
      ) : (
        <Box className="d-flex justify-content-between align-items-center">
          {ViewAccessOnly ? (
            <Typography>
              <span className="link-text">Add {title}</span>
              {required && <span className="required-field">*</span>}
            </Typography>
          ) : (
            <Typography
              className="cursor-pointer"
              onClick={() => {
                setAddSign(true);
                setIsSubmit(false);
              }}
            >
              <span className="link-text">Add {title}</span>
              {required && <span className="required-field">*</span>}
            </Typography>
          )}
        </Box>
      )}
      {error && helperText && (
        <Typography className="field-error">{helperText}</Typography>
      )}

      <DialogBox
        open={addSign}
        handleClose={() => {
          setAddSign(false);
        }}
        title={`Add ${title}`}
        content={
          <>
            <CustomSignature
              setSignValue={setSignValue}
              setSignApi={setAddSign}
              isSetapi={true}
              setSign={setAddSign}
              formikRef={formikRef}
            />
          </>
        }
      />
    </Box>
  );
};

export default SignatureField;
