'use client';
import React from 'react';
import { Box } from '@mui/material';
import RecipeCTAAnalyticsTable from './RecipeCTAAnalyticsTable';
import ContactSubmissionsTable from './ContactSubmissionsTable';
import CustomTabs from '@/components/UI/CustomTabs';
import { useState } from 'react';
import CustomButton from '@/components/UI/CustomButton';
import DownloadIcon from '@mui/icons-material/Download';
import { Popover, Tooltip, Typography } from '@mui/material';
import {
  exportContactSubmissions,
  exportCTAAnylytics,
} from '@/services/recipeService';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import './publicrecipedashboard.scss';

const RecipeAnalyticsDashboard = ({ setShowPublicDashboard }) => {
  const [activeTab, setActiveTab] = useState(1);
  const [exportAnchorEl, setExportAnchorEl] = useState(null);
  const exportOpen = Boolean(exportAnchorEl);
  const exportId = exportOpen ? 'export-popover' : undefined;

  const tabs = [
    { id: 1, label: 'Recipe CTA Analytics' },
    { id: 2, label: 'Contact Submissions' },
  ];

  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
  };

  const handleExportClick = (event) => {
    setExportAnchorEl(event.currentTarget);
  };

  const handleExportClose = () => {
    setExportAnchorEl(null);
  };

  const handleExportDownload = async (format) => {
    try {
      let response;
      if (activeTab === 1) {
        // Export CTA Analytics
        response = await exportCTAAnylytics(format);
      } else if (activeTab === 2) {
        // Export Contact Submissions
        response = await exportContactSubmissions(format);
      }
      // Get filename from response headers or fallback
      let filename = `${activeTab === 1 ? 'cta_analytics' : 'contact_submissions'}_${new Date().toISOString()?.split('T')?.[0]}.${format === 'excel' ? 'xlsx' : 'csv'}`;
      const disposition = response?.headers?.['content-disposition'];
      if (disposition) {
        const match = disposition.match(/filename="?([^";]+)"?/);
        if (match) filename = match[1];
      }
      // Create blob and trigger download from API response
      const blob = response?.data;
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', filename);
      document.body.appendChild(link);
      link.click();
      link?.parentNode?.removeChild(link);
      window.URL.revokeObjectURL(url);
      handleExportClose();
    } catch {
      // Optionally show error message
      handleExportClose();
    }
  };

  return (
    <Box className="recipe-analytics-container h100">
      <Box className="recipe-analytics-tables">
        <Box className="recipe-analytics-tabs-container d-flex align-center">
          <Box className="d-flex cursor-pointer">
            <ArrowBackIosIcon onClick={() => setShowPublicDashboard(false)} />
          </Box>
          <Box className="d-flex align-center justify-space-between recipe-analytics-tabs">
            <Box className="recipe-analytics-tabs-wrap">
              <Box className="w100">
                <CustomTabs
                  tabs={tabs}
                  initialTab={activeTab}
                  onTabChange={handleTabChange}
                />
              </Box>
            </Box>
            <CustomButton
              variant="outlined"
              isIconOnly
              startIcon={
                <Tooltip
                  title={
                    <Typography className="sub-title-text">Export</Typography>
                  }
                  arrow
                  classes={{ tooltip: 'info-tooltip-container' }}
                >
                  <DownloadIcon />
                </Tooltip>
              }
              onClick={handleExportClick}
            />
          </Box>
        </Box>
        <Popover
          className="export-popover recipe-analytics-export-popover"
          id={exportId}
          open={exportOpen}
          anchorEl={exportAnchorEl}
          onClose={handleExportClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
        >
          <Box className="export-option" sx={{ p: 2 }}>
            <Typography
              className="title-text pb8 cursor-pointer fw600"
              onClick={() => handleExportDownload('excel')}
            >
              Excel
            </Typography>
            <Typography
              className="title-text cursor-pointer fw600"
              onClick={() => handleExportDownload('csv')}
            >
              CSV
            </Typography>
          </Box>
        </Popover>
        <Box className="tab-content">
          {activeTab === 1 && (
            <Box className="recipe-cta-analytics-table">
              <RecipeCTAAnalyticsTable />
            </Box>
          )}
          {activeTab === 2 && (
            <Box className="contact-submissions-table">
              <ContactSubmissionsTable />
            </Box>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default RecipeAnalyticsDashboard;
