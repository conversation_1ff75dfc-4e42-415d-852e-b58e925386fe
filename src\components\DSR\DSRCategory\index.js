'use client';

import React, { useEffect } from 'react';
import { Box, Typography } from '@mui/material';
import AddMainCategory from './MainCategory';
import DSRCategoryMap from './Categorymap';
import SubCategory from './SubCategory';

export default function DSRCategoryPage({
  paymentCateList,
  AddPayment,
  AddSubPayment,
  ReorderPayment,
  deleteMainCategory,
  ReorderSubPayment,
  deletesubCategory,
  AddDSR,
  setAddDSR,
}) {
  useEffect(() => {
    if (
      paymentCateList &&
      paymentCateList?.length > 0 &&
      AddDSR === undefined
    ) {
      setAddDSR({
        mainCatgory: true,
        payment_type_usage: true,
        CategoryList: paymentCateList,
      });
    }
  }, [paymentCateList]);

  return (
    <>
      <Box className="dsr-category-details">
        <Box className="dsr-category-list">
          {paymentCateList &&
            (paymentCateList?.length > 0 || paymentCateList?.length === 0) && (
              <DSRCategoryMap
                paymentCateList={paymentCateList}
                AddDSR={AddDSR}
                setAddDSR={setAddDSR}
              />
            )}
        </Box>
        <Box className="dsr-category-list-details">
          {AddDSR &&
          AddDSR?.payment_type_usage &&
          paymentCateList?.length === 0 ? (
            <>
              <Typography className="p16 fw600 text-underline color-dark-blue">
                {!AddDSR?.mainCatgory && AddDSR?.isUpdate
                  ? `Update ${AddDSR?.list?.payment_type_title}`
                  : !AddDSR?.mainCatgory
                    ? `Add item into DSR setting`
                    : 'DSR setting'}
              </Typography>
              <Box>
                <AddMainCategory
                  AddDSR={AddDSR}
                  AddPayment={AddPayment}
                  deleteMainCategory={deleteMainCategory}
                />
              </Box>
            </>
          ) : AddDSR &&
            AddDSR?.payment_type_usage &&
            (AddDSR?.payment_type_usage === 'income' ||
              AddDSR?.payment_type_usage === 'other' ||
              AddDSR?.payment_type_usage === 'expense' ||
              AddDSR?.mainCatgory) ? (
            <>
              <Typography className="p16 fw600 text-underline color-dark-blue">
                {!AddDSR?.mainCatgory && AddDSR?.isUpdate
                  ? `Update ${AddDSR?.list?.payment_type_title}`
                  : !AddDSR?.mainCatgory
                    ? `Add item into DSR setting`
                    : 'DSR setting'}
              </Typography>
              <Box>
                <AddMainCategory
                  AddDSR={AddDSR}
                  AddPayment={AddPayment}
                  ReorderPayment={ReorderPayment}
                  deleteMainCategory={deleteMainCategory}
                />
              </Box>
            </>
          ) : AddDSR &&
            AddDSR?.payment_type_usage &&
            (AddDSR?.payment_type_usage === 'single' ||
              AddDSR?.payment_type_usage === 'multiple') ? (
            <>
              <Box>
                <SubCategory
                  AddDSR={AddDSR}
                  AddSubPayment={AddSubPayment}
                  ReorderSubPayment={ReorderSubPayment}
                  deletesubCategory={deletesubCategory}
                />
              </Box>
            </>
          ) : (
            <></>
          )}
        </Box>
      </Box>
    </>
  );
}
