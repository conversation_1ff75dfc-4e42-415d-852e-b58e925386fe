'use client';

import React, { useEffect, useState, useContext } from 'react';
import { Box, Typography } from '@mui/material';
import CustomButton from '@/components/UI/CustomButton';
import AuthContext from '@/helper/authcontext';
import { useRouter } from 'next/navigation';
import { fetchFromStorage, saveToStorage } from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import {
  setApiMessage,
  TotalOfWSRArray,
  removeVAT,
  DateFormat,
} from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import ReactDatePicker from '@/components/UI/ReactWeekDatePicker';
import { URLS } from '@/helper/constants/urls';
import _ from 'lodash';
import moment from 'moment';
import dayjs from 'dayjs';
import WSRCategory from './wsrCategory';

export default function EditWSR({ dsrId }) {
  const getWeekDates = () => {
    const currentDate = new Date();

    const startOfWeek = new Date(currentDate);
    const dayOfWeek = startOfWeek.getDay();
    const diffToMonday = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
    startOfWeek.setDate(startOfWeek.getDate() + diffToMonday);
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);

    return { startDate: startOfWeek, endDate: endOfWeek };
  };
  const { authState, userdata, setUserdata } = useContext(AuthContext);
  const [DsrDetails, setDsrDetails] = useState();
  // const [loader, setLoader] = useState(false);
  const [updateLoader, setUpdateLoader] = useState(false);
  const [random, setRandom] = useState();
  const [randomother, setRandomother] = useState();
  const [randomvat, setRandomvat] = useState();
  const [formated, setFormated] = useState(true);
  const [DsrDataIncome, setDsrDataIncome] = useState([]);
  const [DsrDataOther, setDsrDataOther] = useState([]);
  const [VatValue, setVATValue] = useState([0, 20]);
  const [VAT, setVAT] = useState({
    NoneVat: '',
    VAT1: '',
    VAT2: '',
    AmountVAT1: '',
    AmountVAT2: '',
    diff1: '',
    diff2: '',
    TotalIncome: '',
  });
  const [OldVAT, seOldtVAT] = useState({
    NoneVat: '',
    VAT1: '',
    VAT2: '',
    AmountVAT1: '',
    AmountVAT2: '',
    diff1: '',
    diff2: '',
    TotalIncome: '',
  });
  const [currency, setCurrency] = useState();
  const [isSubmit, setIsSubmit] = useState(false);
  const [WSRDates, setDate] = useState(getWeekDates);
  const [checkloader, setCheckLoader] = useState(false);
  // const [DsrExist, setISDsr] = useState(false);
  const [VatIsView, setVatIsView] = useState(true);
  const [IsRemarkView, setIsRemarkView] = useState(true);
  const router = useRouter();
  const ValueOfTotal = (value) => {
    const parsedValue = parseFloat(value);
    if (!Number.isNaN(parsedValue)) {
      return parsedValue;
    }
    // if (parseFloat(value) && parseFloat(value) !== NaN) {
    //   return parseFloat(value);
    // }
    return 0;
  };
  //Total without VAT
  const TotalWithoutVAT = () => {
    return ValueOfTotal(
      parseFloat(
        removeVAT(
          ValueOfTotal(parseFloat(VAT?.VAT2)),
          VatValue && VatValue?.[1] ? VatValue?.[1] : 0
        )?.netAmount +
          ValueOfTotal(parseFloat(VAT?.VAT1)) +
          ValueOfTotal(parseFloat(VAT?.NoneVat))
      )
    ).toFixed(2);
  };
  const UpdateDSR = async () => {
    setUpdateLoader(true);
    let dsrmerge = _.concat(DsrDataIncome, DsrDataOther);
    let vatCal = VAT;
    vatCal.TotalIncome = TotalOfWSRArray(DsrDataIncome)
      ? TotalOfWSRArray(DsrDataIncome)
      : 0;
    vatCal.AmountVAT1 =
      ValueOfTotal(parseFloat(VAT?.VAT2)) && VatValue && VatValue?.[1]
        ? removeVAT(ValueOfTotal(parseFloat(VAT?.VAT2)), VatValue?.[1])
            ?.vatAmount
        : 0;
    vatCal.AmountVAT2 =
      (ValueOfTotal(parseFloat(VAT?.VAT2)) ||
        ValueOfTotal(parseFloat(VAT?.VAT2)) === 0 ||
        ValueOfTotal(parseFloat(VAT?.VAT1)) ||
        ValueOfTotal(parseFloat(VAT?.VAT1)) === 0 ||
        ValueOfTotal(parseFloat(VAT?.NoneVat)) ||
        ValueOfTotal(parseFloat(VAT?.NoneVat)) === 0) &&
      VatValue &&
      VatValue?.[1]
        ? TotalWithoutVAT()
        : 0;
    vatCal.diff2 =
      VAT?.diff1 && VAT?.diff1 !== ''
        ? Math.abs(
            (
              removeVAT(ValueOfTotal(parseFloat(VAT?.VAT2)), VatValue?.[1])
                ?.vatAmount - ValueOfTotal(parseFloat(VAT?.diff1))
            ).toFixed(2)
          )
        : '';
    const sendData = {
      data: dsrmerge,
      wsr_amount_total: vatCal,
      old_wsr_amount_total: OldVAT,
      ...(authState?.web_user_active_role_id !== 7 &&
        authState?.web_user_active_role_id !== 14 && {
          wsr_start_date: formated
            ? DsrDetails?.wsr_start_date
            : dayjs(WSRDates?.startDate).format('YYYY-MM-DD'),
        }),
      ...(authState?.web_user_active_role_id !== 7 &&
        authState?.web_user_active_role_id !== 14 && {
          wsr_end_date: formated
            ? DsrDetails?.wsr_end_date
            : dayjs(WSRDates?.endDate).format('YYYY-MM-DD'),
        }),
      current_datetime: moment(Date()).format('YYYY-MM-DD HH:mm:ss'),
    };
    try {
      const { status, data } = await axiosInstance.put(
        URLS?.UPDATE_WSR + `${dsrId}`,
        sendData
      );

      if (status === 200) {
        if (data?.status) {
          setUpdateLoader(false);
          router?.push('/wsr');
          setApiMessage('success', data?.message);
        } else {
          setUpdateLoader(false);
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setUpdateLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // Check WSR exist in same date or not
  const WSRCheck = async (branch, wsrDate) => {
    setCheckLoader(true);
    const sendData = {
      branch_id: branch,
      wsr_start_date: dayjs(wsrDate?.startDate).format('YYYY-MM-DD'),
      wsr_end_date: dayjs(wsrDate?.endDate).format('YYYY-MM-DD'),
    };
    try {
      const { status, data } = await axiosInstance.post(
        URLS?.GET_WSR_CHECK,
        sendData
      );
      if (status === 200 || status === 202) {
        if (data?.status) {
          setCheckLoader(false);
          // setISDsr(false);
          // setApiMessage('success', data?.message);
        } else {
          setCheckLoader(false);
          // setISDsr(true);
          setDate({
            startDate: dayjs(DsrDetails?.wsr_start_date).format('DD/MM/YYYY'),
            endDate: dayjs(DsrDetails?.wsr_end_date).format('DD/MM/YYYY'),
          });
          setFormated(true);
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setCheckLoader(false);
      // setISDsr(true);
      setFormated(true);
      setDate({
        startDate: dayjs(DsrDetails?.wsr_start_date).format('DD/MM/YYYY'),
        endDate: dayjs(DsrDetails?.wsr_end_date).format('DD/MM/YYYY'),
      });
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // Find current week of last date
  const getEndOfCurrentWeek = () => {
    const today = new Date();
    const dayOfWeek = today.getDay();
    const diffToSunday = 6 - dayOfWeek; // Calculate days to next Sunday
    const endOfWeek = new Date(today);
    endOfWeek.setDate(today.getDate() + diffToSunday);
    return endOfWeek;
  };
  // get WSR by id
  const getDSRData = async () => {
    // setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_WSR_BY_ID + `${dsrId}`
      );
      if (status === 200) {
        // setLoader(false);
        setDsrDetails(data?.data);
        setDate({
          startDate: dayjs(data?.data?.wsr_start_date).format('DD/MM/YYYY'),
          endDate: dayjs(data?.data?.wsr_end_date).format('DD/MM/YYYY'),
        });
        const incomeData =
          data?.data &&
          data?.data?.wsrItems &&
          data?.data?.wsrItems?.length > 0 &&
          data?.data?.wsrItems?.filter(
            (f) => f?.payment_type_usage === 'income'
          );

        incomeData && incomeData?.length > 0
          ? setDsrDataIncome(incomeData)
          : setDsrDataIncome([]);
        const otherData =
          data?.data &&
          data?.data?.wsrItems &&
          data?.data?.wsrItems?.length > 0 &&
          data?.data?.wsrItems?.filter(
            (f) => f?.payment_type_usage === 'other'
          );

        otherData && otherData?.length > 0
          ? setDsrDataOther(otherData)
          : setDsrDataOther([]);
        if (data?.data?.wsr_amount_total) {
          let vats = JSON.parse(data?.data?.wsr_amount_total);
          let vatdata = JSON.parse(data?.data?.wsr_amount_total);
          vatdata.diff2 = Math.abs(
            (
              removeVAT(ValueOfTotal(parseFloat(vats?.VAT2)), VatValue?.[1])
                ?.vatAmount - ValueOfTotal(parseFloat(vats?.diff1))
            ).toFixed(2)
          );
          setVAT(vatdata);
          let vatss = JSON.parse(data?.data?.wsr_amount_total);
          let vatdatas = JSON.parse(data?.data?.wsr_amount_total);
          vatdata.diff2 = Math.abs(
            (
              removeVAT(ValueOfTotal(parseFloat(vatss?.VAT2)), VatValue?.[1])
                ?.vatAmount - ValueOfTotal(parseFloat(vatss?.diff1))
            ).toFixed(2)
          );
          seOldtVAT(vatdatas);
        }
        data?.vat_per_data &&
          data?.vat_per_data?.length > 0 &&
          setVATValue(data?.vat_per_data);
      }
    } catch (error) {
      // setLoader(false);
      setDsrDetails();
      setDsrDataIncome([]);
      setDsrDataOther([]);
      setVAT();
      setVATValue();

      setApiMessage('error', error?.response?.data?.message);
    }
  };

  //Total of VAT amount
  const totalofALLVat = () => {
    const sum =
      ValueOfTotal(parseFloat(VAT?.NoneVat)) +
      ValueOfTotal(parseFloat(VAT?.VAT1)) +
      ValueOfTotal(parseFloat(VAT?.VAT2));
    return parseFloat(sum.toFixed(2));
  };
  //Total of Difference VAT amount
  // const totalofDiffVat = () => {
  //   const sum =
  //     ValueOfTotal(parseFloat(VAT?.diff1)) +
  //     ValueOfTotal(parseFloat(VAT?.diff2));
  //   return parseFloat(sum.toFixed(2));
  // };
  useEffect(() => {
    setDsrDataIncome(DsrDataIncome);
  }, [random]);
  useEffect(() => {
    setDsrDataOther(DsrDataOther);
  }, [randomother]);
  useEffect(() => {
    setVAT(VAT);
  }, [randomvat]);

  const oldDSR = fetchFromStorage(identifiers?.RedirectData)
    ? fetchFromStorage(identifiers?.RedirectData)
    : userdata;

  useEffect(() => {
    if (oldDSR?.IsFromUser === undefined && oldDSR?.page !== undefined) {
      setUserdata({ ...userdata, IsFromUser: true });
      saveToStorage(identifiers?.RedirectData, {
        ...oldDSR,
        IsFromUser: true,
      });
    }
  }, [oldDSR]);

  useEffect(() => {
    if (dsrId) {
      getDSRData();
    }
  }, [dsrId]);
  useEffect(() => {
    authState?.currency_details && setCurrency(authState?.currency_details);
  }, [authState]);
  return (
    <Box className="Add-dsr-section">
      <ArrowBackIosIcon
        className="mb8 cursor-pointer"
        onClick={() => {
          setTimeout(() => {
            router?.push('/wsr');
          }, 1000);
        }}
      />
      <Box>
        {(authState?.web_user_active_role_id === 7 ||
          authState?.web_user_active_role_id === 14) && (
          <>
            {DsrDetails?.wsr_start_date && (
              <Box className="d-flex align-center">
                <Typography className="title-text fw400">
                  <span className="fw600"> Start Date : </span>
                  <span>{DateFormat(DsrDetails?.wsr_start_date, 'dates')}</span>
                </Typography>
              </Box>
            )}
            {DsrDetails?.wsr_end_date && (
              <Box className="d-flex align-center">
                <Typography className="title-text fw400">
                  <span className="fw600"> End Date : </span>
                  <span>{DateFormat(DsrDetails?.wsr_end_date, 'dates')}</span>
                </Typography>
              </Box>
            )}
          </>
        )}
        {DsrDetails?.wsr_branch && DsrDetails?.wsr_branch?.branch_name && (
          <Box className="d-flex align-center pt4">
            <Typography className="title-text fw400">
              <span className="fw600"> Branch : </span>
              <span>{DsrDetails?.wsr_branch?.branch_name}</span>
            </Typography>
          </Box>
        )}

        {DsrDetails?.wsr_user && DsrDetails?.wsr_user?.user_full_name && (
          <Box className="d-flex align-center pt4">
            <Typography className="title-text fw400">
              <span className="fw600"> Submitted By : </span>
              <span>{DsrDetails?.wsr_user?.user_full_name}</span>
            </Typography>
          </Box>
        )}

        {DsrDetails?.wsr_detail_status && (
          <Box className="d-flex align-center pt4">
            <Typography className="title-text fw400">
              <span className="fw600"> Status : </span>
              <span>
                {
                  <span>
                    {DsrDetails?.wsr_detail_status === 'rejected' ? (
                      <span className="p12 failed fw600 text-capital">
                        {' '}
                        {DsrDetails?.wsr_detail_status}{' '}
                      </span>
                    ) : DsrDetails?.wsr_detail_status === 'pending' ? (
                      <span className="p12 draft fw600 text-capital">
                        {' '}
                        {DsrDetails?.wsr_detail_status}{' '}
                      </span>
                    ) : (
                      <span className="p12 success fw600 text-capital">
                        {' '}
                        {DsrDetails?.wsr_detail_status}{' '}
                      </span>
                    )}
                  </span>
                }
              </span>
            </Typography>
          </Box>
        )}
      </Box>
      {authState?.web_user_active_role_id !== 7 &&
        authState?.web_user_active_role_id !== 14 &&
        authState?.UserPermission?.dsr === 2 && (
          <Box className="display-grid-branch edit-wsr pt32">
            <Box>
              <ReactDatePicker
                label="Week (DD/MM/YYYY - DD/MM/YYYY)"
                name="date"
                value={WSRDates}
                disabled={checkloader}
                disableFuture={true}
                onChange={(date) => {
                  setDate({
                    startDate: date?.startOfWeek,
                    endDate: date?.endOfWeek,
                  });
                  setFormated(false);
                  DsrDetails?.branch_id &&
                    WSRCheck(DsrDetails?.branch_id, {
                      startDate: date?.startOfWeek,
                      endDate: date?.endOfWeek,
                    });
                }}
                inputVariant="outlined"
                isClearable={false}
                showWeekNumbers={true}
                showWeekPicker={true}
                maxDate={getEndOfCurrentWeek()}
                formated={formated}
              />
            </Box>
            <Box></Box>
          </Box>
        )}
      <WSRCategory
        DsrDataIncome={DsrDataIncome}
        setVatIsView={setVatIsView}
        VatIsView={VatIsView}
        setIsRemarkView={setIsRemarkView}
        IsRemarkView={IsRemarkView}
        setIsSubmit={setIsSubmit}
        setDsrDataIncome={setDsrDataIncome}
        setRandom={setRandom}
        setDsrDataOther={setDsrDataOther}
        setRandomother={setRandomother}
        DsrDataOther={DsrDataOther}
        currency={currency}
        VAT={VAT}
        setVAT={setVAT}
        setRandomvat={setRandomvat}
        VatValue={VatValue}
        ValueOfTotal={ValueOfTotal}
        totalofALLVat={totalofALLVat}
        isSubmit={isSubmit}
        isEdit={true}
      />

      <Box className="mt24">
        {' '}
        <CustomButton
          variant="contained"
          onClick={() => {
            setIsSubmit(true);
            if (VatIsView && DsrDataIncome?.length > 0) {
              if (totalofALLVat() === TotalOfWSRArray(DsrDataIncome)) {
                if (
                  !isNaN(parseFloat(VAT?.VAT1)) ||
                  !isNaN(parseFloat(VAT?.VAT2)) ||
                  !isNaN(parseFloat(VAT?.NoneVat)) ||
                  TotalOfWSRArray(DsrDataIncome) === 0
                ) {
                  UpdateDSR();
                }
              }
            } else {
              UpdateDSR();
            }
          }}
          disabled={updateLoader}
          title={`${updateLoader ? 'Updating...' : 'Update WSR'}`}
        />
      </Box>
    </Box>
  );
}
