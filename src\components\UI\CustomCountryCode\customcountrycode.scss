@import '@/styles/variable.scss';
.country-select-adornment {
  .country-select {
    border: none !important;
    border-right: 1px solid #d9dae2 !important;
    // border-bottom: 1px solid #d9dae2 !important;
    border-radius: 0px !important;
    padding: 0.5px 0px 0.5px 0px;
    margin-right: 0px;

    .MuiInputBase-input {
      padding: 0px !important;
    }

    .MuiSelect-select {
      padding: 0px !important;
      margin-right: 25px;
    }

    .MuiInputBase-root {
      background-color: white !important;

      fieldset {
        border: none !important;
      }
    }
  }
}
