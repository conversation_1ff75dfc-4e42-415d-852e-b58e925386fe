// Media Upload Section Component Styles
.media-upload-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2xl);

  // File Upload Zone
  &__upload-zone {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  &__zone-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &__zone-title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    color: var(--text-color-primary);
  }

  &__zone-counter {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xxs);
    color: var(--text-color-slate-gray);
  }

  &__drop-area {
    position: relative;
    border: var(--border-width-sm) dashed var(--border-color-light-gray);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-xxl);
    text-align: center;
    transition: all 0.15s ease-out;
    cursor: pointer;

    &:hover {
      border-color: var(--color-primary);
      background-color: var(--color-off-white);
    }

    &--drag-over {
      border-color: var(--color-primary);
      background-color: var(--color-primary-opacity);
    }

    &--disabled {
      opacity: 0.5;
      pointer-events: none;
    }
  }

  &__drop-input {
    position: absolute;
    inset: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;

    &:disabled {
      cursor: not-allowed;
    }
  }

  &__drop-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
  }

  &__drop-icon {
    color: var(--color-primary);
  }

  &__drop-text {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-tiny);
  }

  &__drop-title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    color: var(--text-color-primary);
  }

  &__drop-description {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xxs);
    color: var(--text-color-slate-gray);
  }

  // Main Image Section
  &__main-image {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  &__main-image-title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    color: var(--text-color-primary);
  }
  &__required {
    color: var(--text-color-danger);
  }

  &__main-image-container {
    position: relative;
    width: max-content;
  }

  &__main-image-preview {
    width: 100px;
    height: 100px;
    background-color: var(--color-off-white);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    border: var(--border-width-xs) solid var(--border-color-light-gray);
  }

  &__main-image-img {
    width: 100%;
    height: 100%;
    object-fit: scale-down;
  }

  &__main-image-upload-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  &__main-image-drop {
    position: relative;
    width: 200px;
    height: 120px;
    border: var(--border-width-sm) dashed var(--border-color-light-gray);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    text-align: center;
    transition: all 0.15s ease-out;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    &:hover {
      border-color: var(--color-primary);
      background-color: var(--color-off-white);
    }

    &--drag-over {
      border-color: var(--color-primary);
      background-color: var(--color-primary-opacity);
    }
  }

  &__remove-button {
    position: absolute;
    top: calc(-1 * var(--spacing-sm));
    right: calc(-1 * var(--spacing-sm));
    background-color: var(--color-danger);
    color: var(--text-color-white);
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--box-shadow-md);
    border: none;
    cursor: pointer;
    transition: all 0.15s ease-out;
    padding: var(--spacing-xxs);

    // &:hover {
    //   background-color: var(--color-danger-dark);
    // }
  }

  // File Preview Grid
  &__preview-grid {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-lg);
    // display: grid;
    // grid-template-columns: repeat(2, 1fr);
    // gap: var(--spacing-lg);

    // @media (min-width: 768px) {
    //   grid-template-columns: repeat(3, 1fr);
    // }

    // @media (min-width: 1024px) {
    //   grid-template-columns: repeat(4, 1fr);
    // }
  }

  &__preview-item {
    position: relative;

    &:hover .media-upload-section__preview-remove {
      opacity: 1;
    }
  }

  &__preview-container {
    width: 100px;
    height: 100px;
    background-color: var(--color-off-white);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    border: var(--border-width-xs) solid var(--border-color-light-gray);
  }

  &__preview-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--color-light-gray);
  }

  &__preview-image {
    width: 100%;
    height: 100%;
    object-fit: scale-down;
  }

  // Error Display
  &__error {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--color-danger);
  }

  &__preview-remove {
    position: absolute;
    top: calc(-1 * var(--spacing-sm));
    right: calc(-1 * var(--spacing-sm));
    // width: var(--spacing-xxl);
    // height: var(--spacing-xxl);
    background-color: var(--color-danger);
    color: var(--text-color-white);
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.15s ease-out;
    border: none;
    cursor: pointer;
    padding: var(--spacing-xxs);

    // &:hover {
    //   background-color: var(--color-danger-dark);
    // }
  }

  &__preview-info {
    margin-top: var(--spacing-sm);
  }

  &__preview-name {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xxs);
    color: var(--text-color-primary);
    // white-space: nowrap;
    // overflow: hidden;
    // text-overflow: ellipsis;
    width: 100px;
    word-break: break-word;
  }

  &__preview-size {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xxs);
    color: var(--text-color-slate-gray);
  }

  // Media Summary
  &__summary {
    padding: var(--spacing-lg);
    background-color: var(--color-primary-opacity);
    border: var(--border-width-xs) solid var(--color-primary);
    border-radius: var(--border-radius-md);
  }

  &__summary-title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-sm);
    color: var(--color-primary);
    margin-bottom: var(--spacing-md);
  }

  &__summary-grid {
    // display: grid;
    // grid-template-columns: repeat(2, 1fr);
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    gap: var(--spacing-lg);
    font-size: var(--font-size-sm);

    // @media (min-width: 768px) {
    //   grid-template-columns: repeat(5, 1fr);
    // }
  }

  &__summary-item {
    text-align: center;
  }

  &__summary-count {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-primary);
  }

  &__summary-label {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xxs);
    color: var(--text-color-slate-gray);
  }

  // Links Section

  &__link-input-row {
    display: flex;
    gap: var(--spacing-sm);
    width: 100%;
  }
  &__link-type-select {
    max-width: 200px;
    width: 100%;
  }
  &__links {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  &__link-input {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
    .custom-textfield-wrapper {
      width: 100%;
    }
  }

  &__link-add-button {
    width: max-content;
  }

  &__links-grid {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  &__link-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background-color: var(--color-primary-opacity);
    border: var(--border-width-xs) solid var(--border-color-light-gray);
    border-radius: var(--border-radius-md);
    transition: all 0.15s ease-out;

    &:hover {
      background-color: var(--color-off-white);
    }
  }

  &__link-icon {
    flex-shrink: 0;
    width: var(--spacing-xl);
    height: var(--spacing-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--color-white);
    color: var(--icon-color-primary);
    border-radius: var(--border-radius-sm);
  }

  &__link-content {
    flex: 1;
    min-width: 0;
  }

  &__link-title {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: var(--spacing-tiny);
  }
  &__link-header {
    margin-bottom: var(--spacing-xsm);
  }
  &__link-type-badge {
    background-color: var(--color-primary);
    color: var(--color-white);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-family: var(--font-family-primary);
  }
  &__link-url {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    color: var(--text-color-slate-gray);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: grid;
  }

  &__link-remove {
    background-color: var(--color-danger);
    color: var(--text-color-white);
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--box-shadow-md);
    border: none;
    cursor: pointer;
    transition: all 0.15s ease-out;
    padding: var(--spacing-xxs);
  }
}
