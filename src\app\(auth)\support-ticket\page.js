import { generateMetadata } from '@/helper/common/commonFunctions';
import SupportTicket from '@/components/OrganizationDetails/SupportTicket';

export const metadata = generateMetadata({
  pageTitle: 'Support Ticket',
});

export default function SupportTicketPage({ searchParams }) {
  // Extract ticket ID from URL parameters
  const selectedTicketId = searchParams?.id;

  return (
    <>
      <SupportTicket selectedTicketId={selectedTicketId} />
    </>
  );
}
