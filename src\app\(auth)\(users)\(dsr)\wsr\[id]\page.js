import React from 'react';
import { Box } from '@mui/material';
import { generateMetadata } from '@/helper/common/commonFunctions';
import WSRDetails from '@/components/DSR/WSRs/updatewsr';

export const metadata = generateMetadata({
  pageTitle: 'WSR',
});

const DSRPage = ({ params }) => {
  return (
    <Box className="main-page-container">
      <WSRDetails params={params} />
    </Box>
  );
};

export default DSRPage;
