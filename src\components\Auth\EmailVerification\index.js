'use client';
import { Box, Typography } from '@mui/material';
import React from 'react';
import CustomButton from '@/components/UI/button';
import Image from 'next/image';
import ComppanyLogo from '../../../../public/images/Companylogo.png';
import Link from 'next/link';
import { INFO_LINKS } from '@/helper/constants/urls';
import './verifymail.scss';
export default function VerifyMail() {
  return (
    <Box className="verify-mail-wrap">
      <Box className="comp-logo-wrap">
        <Image className="company-logo" src={ComppanyLogo} />
      </Box>
      <Typography className="heading-wrap" variant="h5">
        Please verify your email
      </Typography>
      <Typography component="p" className="email-text-wrap">
        You're almost there! We sent an email to
      </Typography>
      <Typography component="p" className="text-wrap">
        Just click on the link in that email to complete you signup. if you dont
        see it, you may need to{' '}
        <span className="span-folder-wrap"> check your spam </span>folder.
      </Typography>
      <Typography component="p" className="question-text-wrap">
        Still can't find the email ?
      </Typography>
      <Box className="pt24 pb24 resend-btn-wrap" textAlign="center">
        <CustomButton
          className="p16 register-btn"
          type="submit"
          fontWeight="600"
          variant="contained"
          background="#39596e"
          backgroundhover="#39596e"
          colorhover="#FFFFFF"
          title="Resend Email"
        />
      </Box>
      <Typography component="p" className="contact-text">
        Need help?{' '}
        <span className="contact-us-wrap">
          <Link
            href={INFO_LINKS?.contactUs}
            target="_blank"
            rel="noopener noreferrer"
            className="contact-us-wrap"
          >
            Contact Us
          </Link>
        </span>
      </Typography>
    </Box>
  );
}
