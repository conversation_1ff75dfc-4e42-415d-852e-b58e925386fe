// Nutrition Section Component Styles
.nutrition-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);

  // Tab Navigation
  &__tabs {
    display: flex;
    gap: var(--spacing-sm);
    background-color: var(--color-primary-opacity);
    border-radius: var(--field-radius);
    padding: var(--spacing-xs);
  }

  &__tab {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--field-padding);
    border-radius: var(--field-radius);
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    transition: all 0.15s ease-out;
    border: none;
    cursor: pointer;
    background: transparent;
    &--active {
      background-color: var(--color-white);
      color: var(--color-primary);
      box-shadow: var(--box-shadow-xs);
    }

    &--inactive {
      color: var(--text-color-slate-gray);

      &:hover {
        color: var(--text-color-primary);
      }
    }
  }

  // Content Area
  &__content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    // padding: var(--spacing-lg) 0;
  }
}
