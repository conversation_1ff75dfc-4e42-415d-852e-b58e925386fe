'use client';

import React, { useContext } from 'react';
import { Box, Typography } from '@mui/material';
import { CustomTextField } from '@/components/UI/CommonField/index';
import * as Yup from 'yup';
import { Formik, Form } from 'formik';
import CustomButton from '@/components/UI/button';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import {
  checkOrganizationRole,
  setApiMessage,
} from '@/helper/common/commonFunctions';
import { ORG_URLS, URLS } from '@/helper/constants/urls';
import AuthContext from '@/helper/authcontext';
import { fetchFromStorage, removeFromStorage } from '@/helper/context';
import { identifiers } from '@/helper/constants/identifier';
import { useRouter } from 'next/navigation';

export default function CloseAccount({ setPINPassword }) {
  const { authState } = useContext(AuthContext);
  const isDeviceId = fetchFromStorage(identifiers?.DEVICEID);
  // const authData = fetchFromStorage(identifiers?.AUTH_DATA);
  const router = useRouter();

  const handleLogout = async () => {
    if (isDeviceId) {
      const sendData = {
        webAppToken: isDeviceId,
      };
      try {
        const { status } = await axiosInstance.post(URLS?.LOGOUT, sendData);

        if (status === 200) {
          setTimeout(() => {
            removeFromStorage(identifiers?.AUTH_DATA);
            removeFromStorage(identifiers?.USER_DATA);
            removeFromStorage(identifiers?.RedirectData);
            removeFromStorage(identifiers?.DEVICEID);
            removeFromStorage(identifiers?.DEVICEDATA);
            if (
              checkOrganizationRole('super_admin') ||
              checkOrganizationRole('org_master') ||
              checkOrganizationRole('staff')
            ) {
              router.push('/login');
            } else {
              router.push('/');
            }
          }, 100);
          setTimeout(() => {
            setApiMessage('success', 'Logout successfully');
          }, 1000);
        }
      } catch (error) {
        setTimeout(() => {
          removeFromStorage(identifiers?.AUTH_DATA);
          removeFromStorage(identifiers?.USER_DATA);
          removeFromStorage(identifiers?.RedirectData);
          removeFromStorage(identifiers?.DEVICEID);
          removeFromStorage(identifiers?.DEVICEDATA);
          if (
            checkOrganizationRole('super_admin') ||
            checkOrganizationRole('org_master') ||
            checkOrganizationRole('staff')
          ) {
            router.push('/login');
          } else {
            router.push('/');
          }
        }, 100);
        setTimeout(() => {
          setApiMessage('success', 'Logout successfully');
        }, 1000);
        console.error(error);
      }
    } else {
      setTimeout(() => {
        removeFromStorage(identifiers?.AUTH_DATA);
        removeFromStorage(identifiers?.USER_DATA);
        removeFromStorage(identifiers?.RedirectData);
        removeFromStorage(identifiers?.DEVICEID);
        removeFromStorage(identifiers?.DEVICEDATA);
        if (
          checkOrganizationRole('super_admin') ||
          checkOrganizationRole('org_master') ||
          checkOrganizationRole('staff')
        ) {
          router.push('/login');
        } else {
          router.push('/');
        }
      }, 100);
      setTimeout(() => {
        setApiMessage('success', 'Logout successfully');
      }, 1000);
    }
  };
  return (
    <>
      {' '}
      <Formik
        enableReinitialize
        initialValues={{
          email: '',
        }}
        validationSchema={Yup.object().shape({
          email: Yup.string()
            .required('Email is required')
            .email('Invalid email address')
            .matches(authState?.email, 'Email not match with your profile'),
        })}
        onSubmit={async () => {
          try {
            const { status, data } = await axiosInstance.put(
              ORG_URLS?.CLOSE_ACCONT +
                `${authState?.attributes?.organizationId}`
            );
            if (status === 200) {
              if (data?.status) {
                setApiMessage('success', data?.message);
                setPINPassword(false);
                handleLogout();
              } else setApiMessage('error', data?.message);
            }
          } catch (error) {
            setApiMessage('error', error?.response?.data?.message);
          }
        }}
      >
        {({
          errors,
          touched,
          handleBlur,
          values,
          handleSubmit,
          handleChange,
          // setFieldValue,
        }) => (
          <Form onSubmit={handleSubmit} className="">
            <Box className="">
              <Typography className="p16 text-align">
                Please enter your email address to verify before closing your
                account
              </Typography>
              <Box className="input-field-wrap">
                <Box className="pt32">
                  <CustomTextField
                    InputLabelProps={{ shrink: true }}
                    id="email"
                    name="email"
                    label="Email*"
                    placeholder="Enter email address"
                    value={values?.email}
                    error={Boolean(touched?.email && errors?.email)}
                    helperText={touched?.email && errors?.email}
                    className="w100 input-field-wrap"
                    variant="filled"
                    onBlur={handleBlur}
                    onChange={handleChange}
                  />
                </Box>
              </Box>

              <Box className="save-btn-box create-cancel-button pt24">
                <CustomButton
                  variant="contained"
                  fontWeight="600"
                  className="p16 secondary-button "
                  background="#FFFFFF"
                  backgroundhover="#39596e"
                  colorhover="#FFFFFF"
                  title="Cancel"
                  fullWidth
                  onClick={() => {
                    setPINPassword(false);
                  }}
                />
                <CustomButton
                  variant="contained"
                  background="#39596e"
                  backgroundhover="#FFFFFF"
                  colorhover="#000000"
                  className="p16 "
                  fontWeight="600"
                  title="Close Account"
                  fullWidth
                  type="submit"
                />
              </Box>
            </Box>
          </Form>
        )}
      </Formik>
    </>
  );
}
