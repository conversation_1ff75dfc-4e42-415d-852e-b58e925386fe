'use client';

import React, { useContext, useEffect, useState } from 'react';
import { Box } from '@mui/material';
import AuthContext from '@/helper/authcontext';
import { removeFromStorage, fetchFromStorage } from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import ExpensePage from '@/components/DSR/Payrolls';
import dayjs from 'dayjs';
import '../dsr.scss';

export default function ExpenseView() {
  const { authState, userdata, setUserdata, AllListsData } =
    useContext(AuthContext);

  const [dsrData, setdsrData] = useState([]);
  // const [loader, setLoader] = useState(true);
  const [loaderUser, setLoaderUser] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const [filterData, setFilterData] = useState({
    branch: '',
    sdate: '',
    edate: '',
  });
  const [filterDataApplied, setFilterDataApplied] = useState({
    branch: '',
    sdate: '',
    edate: '',
  });

  const [rowsPerPage, setRowsPerPage] = useState(10);

  // List of Expenses
  const getExpensesData = async (search, pageNo, branch, sdate, edate, Rpp) => {
    // setLoader(true);
    setLoaderUser(true);
    const smonth = sdate
      ? dayjs(sdate)?.format('MM')
      : edate
        ? dayjs(edate)?.format('MM')
        : '';
    const syear = sdate
      ? dayjs(sdate)?.format('YYYY')
      : edate
        ? dayjs(edate)?.format('YYYY')
        : '';
    const emonth = edate
      ? dayjs(edate)?.format('MM')
      : sdate
        ? dayjs(sdate)?.format('MM')
        : '';
    const eyear = edate
      ? dayjs(edate)?.format('YYYY')
      : sdate
        ? dayjs(sdate)?.format('YYYY')
        : '';
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_EXPENSE_LIST +
          `?search=${search}&page=${pageNo}&size=${
            Rpp ? Rpp : rowsPerPage
          }&branch_id=${branch}&expense_start_month=${smonth}&expense_start_year=${syear}&expense_end_month=${emonth}&expense_end_year=${eyear}`
      );
      if (status === 200) {
        // setLoader(false);
        let dsr =
          data?.data &&
          data?.data?.length > 0 &&
          data?.data?.map((d) => {
            return {
              ...d,
              branch: d?.expense_branch,
              submitedby: d?.dsr_user?.user_full_name,
              updatedby: d?.expense_detail_updated_by?.user_full_name,
            };
          });
        dsr ? setdsrData(dsr) : setdsrData([]);
        removeFromStorage(identifiers?.RedirectData);
        setUserdata();
        setTotalCount(data?.count);
        setLoaderUser(false);
      }
    } catch (error) {
      // setLoader(false);
      setLoaderUser(false);
      setdsrData([]);
      setTotalCount(0);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  useEffect(() => {
    if (
      authState?.UserPermission?.dsr === 1 ||
      authState?.UserPermission?.dsr === 2
    ) {
      if (
        (!fetchFromStorage(identifiers?.RedirectData) &&
          userdata?.page === undefined &&
          fetchFromStorage(identifiers?.RedirectData)?.IsFromUser ===
            undefined &&
          userdata?.IsFromUser === undefined) ||
        fetchFromStorage(identifiers?.RedirectData)?.payroll === undefined ||
        userdata?.payroll === undefined
      ) {
        getExpensesData('', 1, '', '', '');
        removeFromStorage(identifiers?.RedirectData);
        setUserdata();
      }
    }
  }, [authState?.UserPermission?.dsr]);

  return (
    <>
      <Box className="dsr-page-section">
        <ExpensePage
          authState={authState}
          branchList={AllListsData?.ActiveBranchList}
          loader={loaderUser}
          rowsPerPage={rowsPerPage}
          setRowsPerPage={setRowsPerPage}
          getDsrData={getExpensesData}
          dsrData={dsrData}
          totalCount={totalCount}
          setFilterData={setFilterData}
          setFilterDataApplied={setFilterDataApplied}
          filterData={filterData}
          filterDataApplied={filterDataApplied}
        />
      </Box>
    </>
  );
}
