'use client';

import React, { useRef, useState } from 'react';
import { <PERSON><PERSON><PERSON>wayListener, Divider, Popper } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import TodayIcon from '@mui/icons-material/Today';
import TimerOutlinedIcon from '@mui/icons-material/TimerOutlined';
import LightDarkSwitch from '../../Switch';
import AccessTimeOutlinedIcon from '@mui/icons-material/AccessTimeOutlined';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import CustomButton from '@/components/UI/CustomButton';
import AddOutlinedIcon from '@mui/icons-material/AddOutlined';
import RemoveCircleOutlineOutlinedIcon from '@mui/icons-material/RemoveCircleOutlineOutlined';
import { Formik, Form } from 'formik';
// import * as Yup from 'yup';
import moment from 'moment';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { ROTA_URLS } from '@/helper/constants/urls';
import PreLoader from '@/components/UI/Loader';
import dayjs from 'dayjs';
import './addAvailability.scss';

// Validation schema using Yup
// const addShiftSchema = Yup.object().shape({
//   isAllDay: Yup.boolean(),
// });

export const AddAvailabilityTooltip = ({
  handleAddAvailabilityModalClose,
  singleAvailabilityData,
  filterData,
  currentCellDate,
  getDashboardAvailabilityList,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isStartTimeOpen, setIsStartTimeOpen] = useState(null);
  const [isEndTimeOpen, setIsEndTimeOpen] = useState(null);
  const [timeValidationErrors, setTimeValidationErrors] = useState({});
  const [availabilitySlots, setAvailabilitySlots] = useState(() => {
    if (
      singleAvailabilityData?.timeZone &&
      singleAvailabilityData?.timeZone?.length !== 0
    ) {
      return singleAvailabilityData?.timeZone.map((slot) => ({
        selectStartTime: {
          value: slot?.startTime,
          label: moment.utc(slot?.startTime).format('HH:mm'),
        },
        selectEndTime: {
          value: slot?.endTime,
          label: moment.utc(slot?.endTime).format('HH:mm'),
        },
      }));
    }
    return [{ selectStartTime: null, selectEndTime: null }];
  });

  const startTimeDropdownRefs = useRef([]);
  const endTimeDropdownRefs = useRef([]);

  const handleAddSlot = () => {
    setAvailabilitySlots([
      ...availabilitySlots,
      { selectStartTime: null, selectEndTime: null },
    ]);
    // Close any open dropdowns when adding new slot
    setIsStartTimeOpen(null);
    setIsEndTimeOpen(null);
  };

  const handleRemoveSlot = (index) => {
    setAvailabilitySlots(availabilitySlots.filter((_, i) => i !== index));
    // Close any open dropdowns when removing slot
    setIsStartTimeOpen(null);
    setIsEndTimeOpen(null);
  };

  // Initial form values
  const initialValues = {
    isAvailable: singleAvailabilityData?.available ? true : false,
    isAllDay: singleAvailabilityData
      ? singleAvailabilityData?.type === 'full'
        ? true
        : false
      : true,
  };

  const isBlankAvailabilitySlots = availabilitySlots.every(
    (slot) => !slot?.selectStartTime?.value || !slot?.selectEndTime?.value
  );

  const hasValidationErrors = Object.values(timeValidationErrors).some(
    (error) => error !== null
  );

  const handleSubmit = async (values, { setSubmitting }) => {
    const formatDate = currentCellDate
      ? dayjs(currentCellDate).format('YYYY-MM-DD')
      : '';
    const formatTimeArray = availabilitySlots
      ?.map((slot) => {
        return {
          startTime: slot?.selectStartTime?.value ?? '',
          endTime: slot?.selectEndTime?.value ?? '',
        };
      })
      .filter((slot) => slot?.startTime || slot?.endTime);
    const sendData = {
      userId: filterData?.selectedUser,
      date: formatDate,
      timeZone: values?.isAllDay ? null : formatTimeArray,
      available: values?.isAvailable,
      type: values?.isAllDay ? 'full' : 'custom',
      status: 'active',
    };

    setIsLoading(true);

    if (singleAvailabilityData) {
      try {
        const { status, data } = await axiosInstance.put(
          ROTA_URLS?.AVAILABILITY_URL + `/${singleAvailabilityData?.id}`,
          sendData
        );
        if (status === 200 || status === 201) {
          setApiMessage('success', data?.message);
          setIsLoading(false);
          getDashboardAvailabilityList();
          handleAddAvailabilityModalClose();
          setIsLoading(false);
        }
      } catch (error) {
        setApiMessage('error', error?.response?.data?.message);
        setIsLoading(false);
      }
    } else {
      try {
        const { status, data } = await axiosInstance.post(
          ROTA_URLS?.AVAILABILITY_URL,
          sendData
        );
        if (status === 200 || status === 201) {
          setApiMessage('success', data?.message);
          setIsLoading(false);
          getDashboardAvailabilityList();
          handleAddAvailabilityModalClose();
          setIsLoading(false);
        }
      } catch (error) {
        setApiMessage('error', error?.response?.data?.message);
        setIsLoading(false);
      }
    }

    setSubmitting(false);
  };

  const generateTimes = () => {
    const formatDate = currentCellDate
      ? dayjs(currentCellDate).format('YYYY-MM-DD')
      : '';
    const times = [];
    for (let i = 0; i < 24 * 4; i++) {
      const hours = Math.floor(i / 4);
      const minutes = (i % 4) * 15;
      const formattedTime = `${String(hours).padStart(2, '0')}:${String(
        minutes
      ).padStart(2, '0')}`;

      if (formatDate) {
        const dateTimeValue = `${formatDate}T${formattedTime}:00Z`;
        times.push({ value: dateTimeValue, label: formattedTime });
      } else {
        times.push({ value: formattedTime, label: formattedTime });
      }
    }
    return times;
  };

  const validateTimeRange = (startTime, endTime, index) => {
    if (!startTime || !endTime) return { isValid: false, errorType: null };

    const start = moment.utc(startTime);
    const end = moment.utc(endTime);

    // Check if end time is after start time
    if (end.isSameOrBefore(start)) {
      return { isValid: false, errorType: 'invalidRange' };
    }

    // Check if difference is at least 1 hour
    const diffInHours = end.diff(start, 'hours');
    if (diffInHours < 1) {
      return { isValid: false, errorType: 'minDuration' };
    }

    // Check for overlapping with other slots
    const hasOverlap = availabilitySlots.some((slot, i) => {
      if (
        i === index ||
        !slot.selectStartTime?.value ||
        !slot.selectEndTime?.value
      ) {
        return false;
      }

      const slotStart = moment.utc(slot.selectStartTime.value);
      const slotEnd = moment.utc(slot.selectEndTime.value);

      return (
        (start.isSameOrAfter(slotStart) && start.isBefore(slotEnd)) ||
        (end.isAfter(slotStart) && end.isSameOrBefore(slotEnd)) ||
        (start.isBefore(slotStart) && end.isAfter(slotEnd))
      );
    });

    if (hasOverlap) {
      return { isValid: false, errorType: 'overlap' };
    }

    return { isValid: true, errorType: null };
  };

  const handleTimeSelection = (time, index, isStartTime) => {
    const updatedSlots = [...availabilitySlots];
    const currentSlot = updatedSlots[index];

    if (isStartTime) {
      currentSlot.selectStartTime = time;
    } else {
      currentSlot.selectEndTime = time;
    }

    // Validate the time range
    const validationResult = validateTimeRange(
      currentSlot.selectStartTime?.value,
      currentSlot.selectEndTime?.value,
      index
    );

    // Update validation errors
    setTimeValidationErrors((prev) => ({
      ...prev,
      [index]: validationResult.errorType,
    }));

    setAvailabilitySlots(updatedSlots);
  };

  return (
    <Formik
      initialValues={initialValues}
      // validationSchema={addShiftSchema}
      onSubmit={handleSubmit}
    >
      {({ values, setFieldValue, isSubmitting }) => {
        return (
          <Form className="add-availability-container">
            {/* Employee Dropdown */}
            <div>{isLoading && <PreLoader />}</div>
            <div className="add-availability-header-container">
              <div className="add-availability-header-wrap">
                <p className="add-availability-header-text">
                  Create availability
                </p>
                <p className="add-availability-header-date">
                  {moment(currentCellDate).format('ddd DD MMM YYYY')}
                </p>
              </div>

              <div className="add-availability-header-actions">
                <div
                  className="close-availability-tooltip"
                  onClick={handleAddAvailabilityModalClose}
                >
                  <CloseIcon />
                </div>
              </div>
            </div>

            <Divider />

            <div className="add-availability-details-container">
              <div className="d-center-start availability-row">
                <div className="icon">
                  <TodayIcon />
                </div>

                <div className="availability-event-details">
                  <div
                    className={`availability-event ${values?.isAvailable ? '' : 'offDay'}`}
                    onClick={() => setFieldValue('isAvailable', false)}
                  >
                    <HighlightOffIcon />
                    <span>Unavailable</span>
                  </div>

                  <div
                    className={`availability-event ${values?.isAvailable ? 'onDay' : ''}`}
                    onClick={() => setFieldValue('isAvailable', true)}
                  >
                    <CheckCircleOutlineIcon />
                    <span>Available</span>
                  </div>
                </div>
              </div>
              <Divider />
              <div className="d-center-start availability-toggle-row">
                <div className="icon">
                  <TimerOutlinedIcon />
                </div>
                <div
                  className="availability-toggle-container"
                  onClick={() => setFieldValue('isAllDay', !values.isAllDay)}
                >
                  <p className="availability-toggle-title">All day</p>
                  <LightDarkSwitch
                    checked={values.isAllDay}
                    onChange={() => setFieldValue('isAllDay', !values.isAllDay)}
                  />
                </div>
              </div>
              <Divider />
              {!values.isAllDay && (
                <>
                  <div className="availability-start-end-time-container">
                    {availabilitySlots?.map((slot, index) => {
                      return (
                        <div key={index}>
                          <div className="d-center-start availability-row availability-row-wrap">
                            <div className="icon">
                              <AccessTimeOutlinedIcon />
                            </div>
                            <div className="availability-start-end-time-wrapper">
                              <div className="availability-start-time">
                                <div
                                  className="dropdown-wrapper"
                                  ref={(el) =>
                                    (startTimeDropdownRefs.current[index] = el)
                                  }
                                >
                                  <div
                                    className={`dropdown-header ${index}`}
                                    onClick={() =>
                                      setIsStartTimeOpen(
                                        isStartTimeOpen === index ? null : index
                                      )
                                    }
                                  >
                                    <span>
                                      {slot?.selectStartTime?.label || '--:--'}
                                    </span>

                                    <span className="arrow">
                                      {isStartTimeOpen === index ? (
                                        <ExpandLessIcon />
                                      ) : (
                                        <ExpandMoreIcon />
                                      )}
                                    </span>
                                  </div>

                                  <Popper
                                    open={isStartTimeOpen === index}
                                    anchorEl={
                                      startTimeDropdownRefs.current[index]
                                    }
                                    placement="bottom-start"
                                    className="availability-dropdown-popper"
                                    modifiers={[
                                      {
                                        name: 'preventOverflow',
                                        options: { boundary: 'window' },
                                      },
                                      {
                                        name: 'flip',
                                        options: {
                                          fallbackPlacements: [
                                            'top-start',
                                            'right-start',
                                          ],
                                        },
                                      },
                                    ]}
                                  >
                                    <ClickAwayListener
                                      onClickAway={() =>
                                        setIsStartTimeOpen(false)
                                      }
                                    >
                                      <div className="dropdown-content">
                                        <ul>
                                          {generateTimes()?.map((sTime) => (
                                            <li
                                              key={sTime?.value}
                                              className={
                                                slot?.selectStartTime?.value ===
                                                sTime?.value
                                                  ? 'selected'
                                                  : ''
                                              }
                                              onClick={() => {
                                                handleTimeSelection(
                                                  sTime,
                                                  index,
                                                  true
                                                );
                                                setIsStartTimeOpen(null);
                                              }}
                                            >
                                              {sTime?.label}
                                            </li>
                                          ))}
                                        </ul>
                                      </div>
                                    </ClickAwayListener>
                                  </Popper>
                                </div>
                              </div>
                              -
                              <div className="availability-end-time">
                                <div
                                  className="dropdown-wrapper"
                                  ref={(el) =>
                                    (endTimeDropdownRefs.current[index] = el)
                                  }
                                >
                                  <div
                                    className="dropdown-header"
                                    onClick={() =>
                                      setIsEndTimeOpen(
                                        isEndTimeOpen === index ? null : index
                                      )
                                    }
                                  >
                                    <span>
                                      {slot?.selectEndTime?.label || '--:--'}
                                    </span>
                                    <span className="arrow">
                                      {isEndTimeOpen === index ? (
                                        <ExpandLessIcon />
                                      ) : (
                                        <ExpandMoreIcon />
                                      )}
                                    </span>
                                  </div>
                                  <Popper
                                    open={isEndTimeOpen === index}
                                    anchorEl={
                                      endTimeDropdownRefs.current[index]
                                    }
                                    placement="bottom-start"
                                    className="availability-dropdown-popper"
                                    modifiers={[
                                      {
                                        name: 'preventOverflow',
                                        options: { boundary: 'window' },
                                      },
                                      {
                                        name: 'flip',
                                        options: {
                                          fallbackPlacements: [
                                            'top-start',
                                            'right-start',
                                          ],
                                        },
                                      },
                                    ]}
                                  >
                                    <ClickAwayListener
                                      onClickAway={() =>
                                        setIsEndTimeOpen(false)
                                      }
                                    >
                                      <div className="dropdown-content">
                                        <ul>
                                          {generateTimes()?.map((eTime) => (
                                            <li
                                              key={eTime?.value}
                                              className={
                                                slot?.selectEndTime?.value ===
                                                eTime.value
                                                  ? 'selected'
                                                  : ''
                                              }
                                              onClick={() => {
                                                handleTimeSelection(
                                                  eTime,
                                                  index,
                                                  false
                                                );
                                                setIsEndTimeOpen(null);
                                              }}
                                            >
                                              {eTime?.label}
                                            </li>
                                          ))}
                                        </ul>
                                      </div>
                                    </ClickAwayListener>
                                  </Popper>
                                </div>
                              </div>
                              {availabilitySlots.length > 1 && (
                                <div className="availability-remove-time">
                                  <RemoveCircleOutlineOutlinedIcon
                                    onClick={() => handleRemoveSlot(index)}
                                  />
                                </div>
                              )}
                            </div>
                          </div>
                          {timeValidationErrors[index] && (
                            <div className="validation-messages">
                              {timeValidationErrors[index] ===
                                'invalidRange' && (
                                <p className="field-error-msg">
                                  Invalid time range entered.
                                </p>
                              )}
                              {timeValidationErrors[index] ===
                                'minDuration' && (
                                <p className="field-error-msg">
                                  Times must be at least 1hr.
                                </p>
                              )}
                              {timeValidationErrors[index] === 'overlap' && (
                                <p className="field-error-msg">
                                  Times cannot overlap.
                                </p>
                              )}
                            </div>
                          )}
                        </div>
                      );
                    })}
                    <div className="availability-custom-time-add">
                      <AddOutlinedIcon onClick={handleAddSlot} />
                    </div>
                  </div>
                  <Divider />
                </>
              )}
              <div className="availability-row">
                <CustomButton
                  fullWidth
                  variant="contained"
                  title={isSubmitting ? 'Adding...' : 'Add'}
                  type="submit"
                  disabled={
                    isSubmitting ||
                    (!values?.isAllDay &&
                      (isBlankAvailabilitySlots || hasValidationErrors))
                  }
                />
              </div>
            </div>
          </Form>
        );
      }}
    </Formik>
  );
};
