@import '@/styles/variable.scss';
.holiday-accordion.MuiPaper-root {
  border-radius: 8px;
  margin-top: 20px !important;
  box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;

  &:last-of-type {
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
  }
  .accordion-summary {
    min-height: 40px;
    .MuiAccordionSummary-content {
      margin: 8px 0;
    }
  }
  .MuiDataGrid-root {
    margin-top: 0 !important;
  }
  &::before {
    position: inherit !important;
  }
  .holiday-actions {
    margin-right: 5px;
    .action-icon {
      border-radius: 50%;
    }
  }
  .toggle-icon {
    width: 28px;
    height: 28px;
    background: $action-bg-color;
    padding: 0 2px;
    border-radius: 50%;
    path {
      height: 20px;
    }
  }
  .toggle-icon.active {
    fill: var(--color-green);
  }
  .toggle-icon.inactive {
    fill: var(--color-danger);
  }
  .employee-counts {
    &:hover {
      color: var(--color-black);
      font-weight: 500;
    }
  }
  .assign-icons {
    path {
      stroke-width: 1.6px;
    }
  }
}
.holiday-list-wrap {
  .table-container {
    padding: 0px;
    margin-top: 0px;
    box-shadow: none;

    .holiday-color-dot {
      height: 10px;
      width: 10px;
      border-radius: 50px;
    }
    .info-icon {
      height: 18px;
      width: 18px;
      margin-top: 0px;
      &:hover {
        fill: var(--color-primary);
      }
    }
    .more-text-wrap {
      color: var(--color-primary);
    }
  }
}
