import UserAvatar from '@/components/UI/Avatar/UserAvatar';
import CustomPagination from '@/components/UI/customPagination';
import { Box, CircularProgress, Tooltip, Typography } from '@mui/material';
import { DataGrid, gridClasses } from '@mui/x-data-grid';
import React, { useContext, useEffect, useState } from 'react';
import AuthContext from '@/helper/authcontext';
import LeavePopOver from '@/components/Leave/LeavePopOver';
import Searchbar from '@/components/UI/CustomSearch';
import CustomSelect from '@/components/UI/CustomSelect';
import CustomButton from '@/components/UI/CustomButton';
import { identifiers } from '@/helper/constants/identifier';
import ClearOutlinedIcon from '@mui/icons-material/ClearOutlined';
import { ArrowUpward, ArrowDownward } from '@mui/icons-material';
import { saveToStorage } from '@/helper/context';
import CheckIcon from '@mui/icons-material/Check';
import { useRouter } from 'next/navigation';
import BranchDepartmentDisplay from '@/components/BranchDepartmentDisplay';
import NoDataView from '@/components/UI/NoDataView';
import { staticOptions } from '@/helper/common/staticOptions';

export default function LeaveConsumption({
  leaveConsuptionData,
  getLeaveConsuptionList,
  leaveCunsRowsPerPage,
  setLeaveCunsRowsPerPage,
  leaveCunsPage,
  setLeaveCunsPage,
  leaveCunsTotalCount,
  loader,
  consuFilterData,
  setConsuFilterData,
  consuFilterDataApplied,
  setConsuFilterDataApplied,
  departmentList,
  branchList,
  roleList,
  leaveList,
  setSortByDays,
  sortByDays,
  leave_type,
}) {
  const { authState, setUserdata } = useContext(AuthContext);
  const [branchColumnWidth, setBranchColumnWidth] = useState(200);
  let router = useRouter();

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth <= 899) {
        setBranchColumnWidth(350);
      } else {
        setBranchColumnWidth(200);
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize();
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const onPageChange = (newPage) => {
    setLeaveCunsPage(newPage);
    getLeaveConsuptionList(
      newPage,
      consuFilterDataApplied?.searchValue,
      consuFilterDataApplied
    );
  };
  const OnRowPerPage = (newPage) => {
    setLeaveCunsPage(1);
    setLeaveCunsRowsPerPage(newPage);
    getLeaveConsuptionList(
      1,
      consuFilterDataApplied?.searchValue,
      consuFilterDataApplied,
      newPage
    );
  };
  const handleFilterData = (type) => {
    if (type === 'apply') {
      getLeaveConsuptionList(1, consuFilterData?.searchValue, consuFilterData);
      setConsuFilterDataApplied(consuFilterData);
    } else {
      const clearFilter = {
        leaveType: '',
        branch: '',
        department: '',
        role: '',
        searchValue: '',
        year: '',
        leavetype: leave_type,
      };
      setConsuFilterData(clearFilter);
      setConsuFilterDataApplied(clearFilter);
      getLeaveConsuptionList(
        1,
        consuFilterDataApplied?.searchValue,
        clearFilter
      );
    }
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      setLeaveCunsPage(1);
      handleFilterData('apply');
    }
  };

  const columns = [
    {
      field: 'id',
      headerName: 'ID',
      width: 80,
      minWidth: 80,
      flex: 0,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-start h100 gap-sm flex-wrap">
            <Typography className="title-text">
              <span>{params?.row?.employment_number}</span>
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'user_full_name',
      headerName: 'User',
      width: 200,
      minWidth: 200,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        let userData = params?.row;
        return (
          <>
            <Box className="d-flex align-center justify-start h100">
              <UserAvatar
                name={userData?.user_full_name}
                src={userData?.user_avatar_link}
                classname="list-user-icon"
              />
              <Box
                className="pl8"
                onClick={() => {
                  setUserdata({
                    id: userData?.id,
                    consuFilterData: consuFilterDataApplied,
                    searchValue: consuFilterDataApplied?.searchValue,
                    leaveCunsPage: leaveCunsPage,
                    leaveCon: true,
                  });
                  saveToStorage(identifiers?.RedirectData, {
                    id: userData?.id,
                    consuFilterData: consuFilterDataApplied,
                    searchValue: consuFilterDataApplied?.searchValue,
                    leaveCunsPage: leaveCunsPage,
                    leaveCon: true,
                  });
                  router.push(`/user/${userData?.id}`);
                }}
              >
                <Box className="d-flex align-center">
                  <Typography className="title-text fw600 cursor-pointer link-text mw100 text-ellipsis pr4">
                    {userData?.user_full_name}
                  </Typography>
                </Box>
                <Typography className="sub-title-text cursor-pointer">
                  {userData?.user_email}
                </Typography>
              </Box>
            </Box>
          </>
        );
      },
    },
    {
      field: 'branch?.branch_name',
      headerName: 'Branch / Dep.',
      width: branchColumnWidth,
      minWidth: branchColumnWidth,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return <BranchDepartmentDisplay row={params.row} />;
      },
    },
    {
      field: 'total_used_leave_days',
      headerName: 'Used Leave',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderHeader: () => (
        <Box className="d-flex align-center gap-5">
          <Typography variant="title-text">Used Leave</Typography>
          <Box className="arrow-wrap">
            {sortByDays === 'DESC' ? (
              <ArrowDownward
                className="arrow-icon"
                fontSize="small"
                onClick={() => setSortByDays('ASC')}
                style={{ cursor: 'pointer' }}
              />
            ) : (
              <ArrowUpward
                className="arrow-icon"
                fontSize="small"
                onClick={() => setSortByDays('DESC')}
                style={{ cursor: 'pointer' }}
              />
            )}
          </Box>
        </Box>
      ),
      renderCell: (params) => {
        let leaveDetails = params?.row?.leave_details?.map((leave) => {
          return {
            // leaveCount: leave?.used_leave?.total,
            // leaveCountHours: leave?.used_leave?.total,
            leaveCount:
              leave?.used_leave?.total +
              (consuFilterDataApplied?.leavetype === 'hour' ? 'h' : 'd'),
            // leaveCountHours: leave?.used_hour_leave?.total,
            leaveName: leave?.name,
          };
        });
        return (
          <Box className="d-flex align-center justify-start h100 gap-sm flex-wrap">
            <Typography className="title-text">
              <Tooltip
                className="event-title text-ellipsis"
                variant="h6"
                interactive
                // open
                title={
                  leaveDetails.length > 0 && (
                    <LeavePopOver
                      title={`Total Leave`} // ${leave_type === 'day' ? 'Days' : 'Hours'}
                      Leavedays="Leave Type"
                      leave="No of leaves"
                      leaveDetails={leaveDetails}
                    />
                  )
                }
                classes={{
                  tooltip: 'leave-days-popover',
                }}
              >
                <span>{params?.row?.total_used_leave_days}</span>
              </Tooltip>
            </Typography>
          </Box>
        );
      },
    },
  ];

  return (
    <Box className="leave-consuption-wrap">
      <Box className="search-section-wrap">
        <Box className="search-section-fields">
          <Searchbar
            setSearchValue={(e) => {
              setConsuFilterData({ ...consuFilterData, searchValue: e });
            }}
            searchValue={consuFilterData?.searchValue}
            onKeyPress={handleKeyPress}
          />
        </Box>

        {/* Select for Branch */}
        {authState?.web_user_active_role_id !== 7 &&
          authState?.web_user_active_role_id !== 14 && (
            <Box className="search-section-fields">
              <CustomSelect
                placeholder="Branches"
                showDot
                options={branchList}
                value={
                  branchList?.find((opt) => {
                    return opt?.value === consuFilterData?.branch;
                  }) || ''
                }
                onChange={(e) => {
                  setConsuFilterData({
                    ...consuFilterData,
                    branch: e?.value ? e?.value : '',
                  });
                }}
              />
            </Box>
          )}
        {/* Select for Leave Type */}
        <Box className="search-section-fields">
          <CustomSelect
            placeholder="Leave Type"
            options={leaveList}
            value={
              leaveList?.find((opt) => {
                return opt?.value === consuFilterData?.leaveType;
              }) || ''
            }
            onChange={(e) => {
              setConsuFilterData({
                ...consuFilterData,
                leaveType: e?.value ? e?.value : '',
              });
            }}
          />
        </Box>
        <Box className="search-section-fields">
          <CustomSelect
            placeholder="Department"
            options={departmentList}
            value={
              departmentList?.find((opt) => {
                return opt?.value === consuFilterData?.department;
              }) || ''
            }
            onChange={(e) => {
              setConsuFilterData({
                ...consuFilterData,
                department: e?.value ? e?.value : '',
              });
            }}
          />
        </Box>
        <Box className="search-section-fields">
          <CustomSelect
            placeholder="System access"
            options={roleList}
            value={
              roleList?.find((opt) => {
                return opt?.value === consuFilterData?.role;
              }) || ''
            }
            onChange={(e) => {
              setConsuFilterData({
                ...consuFilterData,
                role: e?.value ? e?.value : '',
              });
            }}
          />
        </Box>
        <Box className="search-section-fields">
          <CustomSelect
            placeholder="Leave type"
            options={staticOptions?.LEAVE_TYPE}
            value={
              staticOptions?.LEAVE_TYPE?.find((opt) => {
                return opt?.value === consuFilterData?.leavetype;
              }) || ''
            }
            onChange={(e) => {
              setConsuFilterData({
                ...consuFilterData,
                leavetype: e?.value ? e?.value : '',
              });
            }}
          />
        </Box>
        {/* <Box className="select-box">
          <CustomSelect
            placeholder="Year"
            options={yearOption}
            value={consuFilterData?.year}
            onChange={(e) => {
              setConsuFilterData({
                ...consuFilterData,
                year: e?.target?.value,
              });
            }}
          />
        </Box> */}
        <Box className="create-cancel-button d-flex align-center gap-10">
          <CustomButton
            variant="contained"
            isIconOnly
            startIcon={
              <Tooltip
                title={<Typography>Apply Filter</Typography>}
                arrow
                classes={{
                  tooltip: 'info-tooltip-container',
                }}
              >
                <CheckIcon />
              </Tooltip>
            }
            fullWidth={false}
            onClick={() => {
              setLeaveCunsPage(1);
              handleFilterData('apply');
            }}
          />
          <CustomButton
            variant="outlined"
            isIconOnly
            startIcon={
              <Tooltip
                title={<Typography>Clear Filter</Typography>}
                arrow
                classes={{
                  tooltip: 'info-tooltip-container',
                }}
              >
                <ClearOutlinedIcon />
              </Tooltip>
            }
            onClick={() => {
              handleFilterData('cancel');
            }}
          />
        </Box>
      </Box>
      <Box className="table-container table-layout">
        {loader ? (
          <Box className="content-loader">
            <CircularProgress className="loader" color="inherit" />
          </Box>
        ) : (
          <>
            {leaveConsuptionData && leaveConsuptionData?.length == 0 ? (
              <Box className="no-data d-flex align-center justify-center">
                <NoDataView
                  // image
                  title="No Leave Consumption Found"
                  description="There is no leave consumption available at the moment."
                />
              </Box>
            ) : (
              <>
                <DataGrid
                  rows={leaveConsuptionData}
                  columns={columns}
                  pageSize={leaveCunsRowsPerPage}
                  checkboxSelection={false}
                  disableSelectionOnClick
                  // autoPageSize={true}
                  rowHeight={70}
                  autoHeight
                  // getRowHeight={() => 'auto'}
                  sx={{
                    transition: 'none', // Disables transition effects
                    [`& .${gridClasses.cell}`]: {
                      py: 1,
                    },
                  }}
                />
                <CustomPagination
                  currentPage={leaveCunsPage}
                  totalCount={leaveCunsTotalCount}
                  rowsPerPage={leaveCunsRowsPerPage}
                  onPageChange={onPageChange}
                  OnRowPerPage={OnRowPerPage}
                />
              </>
            )}
          </>
        )}
      </Box>
    </Box>
  );
}
