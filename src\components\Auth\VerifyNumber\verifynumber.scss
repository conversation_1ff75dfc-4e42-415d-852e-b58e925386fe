@import '@/styles/variable.scss';

.verify-number-wrap {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
    box-shadow: 0px 0px 5px lightgray;
    border-radius: 8px;
    text-align: center;
    padding: 30px;

    .heading-wrap {
        padding: 40px 0px 30px 0px;
        color: $color-primary;
        font-weight: 600;

        @media(max-width:424px) {
            font-size: 21px;
        }

        @media(max-width:374px) {
            font-size: 17px;
        }
    }

    .text-wrap {
        color: $color-Dark-50;
        width: 100%;
        max-width: 430px;
        margin: 0 auto;

        @media(max-width:575px) {
            font-size: 15px;
        }

        @media(max-width:374px) {
            font-size: 13px;
        }
    }

    .proceed-btn-wrap {
        .proceed-btn {
            padding: 5px 16px !important;
            font-size: 15px;
            box-shadow: none !important;

            svg {
                width: 27px;
                height: 27px;
            }

            &:hover {
                color: white !important;

                svg {
                    fill: #ffffff !important;
                }
            }

            @media(max-width:575px) {
                padding: 3px 14px !important;

                .button-text {
                    font-size: 14px;
                }
            }
        }

    }

    @media(max-width:575px) {
        padding: 30px 14px;
    }

}