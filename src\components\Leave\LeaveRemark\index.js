'use client';
import React, { useContext, useEffect, useState } from 'react';
import {
  Box,
  CircularProgress,
  Typography,
  Tooltip,
  Divider,
} from '@mui/material';
import AuthContext from '@/helper/authcontext';
import CustomButton from '@/components/UI/CustomButton';
import DialogBox from '@/components/UI/Modalbox';
import CustomSelect from '@/components/UI/CustomSelect';
import { DataGrid, gridClasses } from '@mui/x-data-grid';
import CustomPagination from '@/components/UI/customPagination';
import TuneIcon from '@mui/icons-material/Tune';
import { setApiMessage, DateFormat } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import moment from 'moment';
import { identifiers } from '@/helper/constants/identifier';
import dayjs from 'dayjs';
import ViewIcon from '@/components/ActionIcons/ViewIcon';
import MenuIcon from '@mui/icons-material/Menu';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import Searchbar from '@/components/UI/CustomSearch';
import {
  fetchFromStorage,
  removeFromStorage,
  saveToStorage,
} from '@/helper/context';
import { staticOptions } from '@/helper/common/staticOptions';
import CustomFullCalendar from '@/components/UI/Fullcalender';
import LeavePopOver from '@/components/Leave/LeavePopOver';
import { useRouter } from 'next/navigation';
import ClearOutlinedIcon from '@mui/icons-material/ClearOutlined';
import CheckIcon from '@mui/icons-material/Check';
import DateFilter from '@/components/DSR/Reports/DateFilter';
import CustomDatePicker from '@/components/UI/CustomDatePicker';
import NoDataView from '@/components/UI/NoDataView';
import useRoleList from '@/hooks/useRoleList';
import CommonUserDetails from '@/components/UI/CommonUserDetails/index';
import './staffleave.scss';

const dateOptions = [
  { label: 'Today', value: 'today' },
  { label: 'Yesterday', value: 'yesterday' },
  { label: 'This Week', value: 'this_week' },
  { label: 'This Month', value: 'this_month' },
  { label: 'This Quarter', value: 'this_quarter' },
  { label: 'This Year', value: 'this_year' },
  { label: 'Custom', value: 'custom' },
];

export default function Staffleave() {
  const router = useRouter();
  const { authState, setUserdata, AllListsData } = useContext(AuthContext);

  const [filter, setFilter] = useState(false);
  const [loader, setLoader] = useState(true);
  const [leaveRemarkList, setLeaveRemarkList] = useState([{ id: '' }]);
  const [totalCount, setTotalCount] = useState(0);
  const [dateSelectedOption, setDateSelectedOption] = useState('this_month');
  const [customStartDate, setCustomStartDate] = useState(null);
  const [customEndDate, setCustomEndDate] = useState(null);
  const [viewMode, setViewMode] = useState('calendar');
  const [filterData, setFilterData] = useState({
    year: '',
    status: '',
    branch: '',
    department: '',
    role: '',
    calendarValue: '',
    startDate: '',
    endDate: '',
    searchValue: '',
  });
  const [filterDataApplied, setFilterDataApplied] = useState({
    year: '',
    status: '',
    branch: '',
    department: '',
    role: '',
    calendarValue: '',
    startDate: '',
    endDate: '',
    searchValue: '',
  });
  const { roleList, fetchRoleList } = useRoleList();
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [calenderSearch, setCalenderSearch] = useState('');
  const [calenderData, setCalenderData] = useState({
    coloredEvents: [],
    holidayEvents: [],
    allEvents: [],
  });
  const [viewData, setViewData] = useState([]);
  const getStatusClass = (status) => {
    const statusMap = {
      rejected: 'leave-rejected',
      pending: 'leave-draft',
      cancelled: 'leave-cancelled',
      approved: 'leave-success',
    };
    return statusMap[status] || 'leave-success';
  };
  const columns = [
    {
      field: 'id',
      headerName: 'ID',
      width: 48,
      minWidth: 48,
      flex: 0,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center h100">
            {/* {params?.row?.request_from_users?.employment_number} */}
            {params?.id}
          </Box>
        );
      },
    },
    {
      field: 'user_full_name',
      headerName: 'User',
      width: 200,
      minWidth: 200,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        let userData = params?.row?.request_from_users;
        return (
          <>
            <Tooltip
              arrow
              title={
                <Box className="d-flex align-center flex-col">
                  <Typography className="sub-title-text fw600 cursor-pointer mw100 pr4">
                    {userData?.user_full_name}
                  </Typography>
                  <Typography className="sub-title-text cursor-pointer">
                    {userData?.user_email}
                  </Typography>
                </Box>
              }
              classes={{
                tooltip: 'info-tooltip-container ',
              }}
            >
              <Box className="w100">
                <CommonUserDetails
                  userData={params?.row?.request_from_users}
                  filterDataApplied={filterDataApplied}
                  searchValue={filterDataApplied?.searchValue}
                  page={page}
                  rowsPerPage={rowsPerPage}
                  setUserdata={setUserdata}
                  authState={authState}
                />
              </Box>
            </Tooltip>
          </>
        );
      },
    },
    {
      field: 'Leave Type',
      headerName: 'Leave Type',
      minWidth: 180, // Ensures minimum width
      maxWidth: 300, // Prevents excessive width
      flex: 1, // Adjusts width dynamically
      sortable: false,
      headerAlign: 'center',
      align: 'center',

      renderCell: (params) => {
        let leaveType = params?.row?.leave_request_type_list?.name || '';
        // Min 150px, Max 300px
        return (
          <Box className="d-flex align-center h100 w100">
            {leaveType !== '-' ? (
              <Typography className="title-text">
                {leaveType?.length > 20 ? (
                  <>
                    {leaveType?.substring(0, 15)}
                    <Tooltip
                      title={<Typography>{leaveType}</Typography>}
                      classes={{
                        tooltip: 'info-tooltip-container ',
                      }}
                      placement="bottom"
                      arrow
                    >
                      <span className="more-text-wrap cursor-pointer">
                        ... More
                      </span>
                    </Tooltip>
                  </>
                ) : (
                  leaveType
                )}
              </Typography>
            ) : (
              '-'
            )}
          </Box>
        );
      },
    },

    {
      field: 'start_date',
      headerName: 'From',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100">
            {params?.value !== '-' ? (
              <Tooltip
                arrow
                title={
                  <Typography>
                    {/* Day & Hours dates */}
                    {params?.row?.duration_type === 'Hours' &&
                    params?.row?.start_date
                      ? DateFormat(params?.row?.start_date, 'datesUTC')
                      : params?.row?.start_date
                        ? DateFormat(params?.row?.start_date, 'dates')
                        : ''}
                  </Typography>
                }
                placement="bottom-start"
                classes={{
                  tooltip: 'info-tooltip-container ',
                }}
              >
                <Typography className="title-text text-ellipsis-line cursor-pointer">
                  {' '}
                  {/* Day & Hours dates */}
                  {params?.row?.duration_type === 'Hours' &&
                  params?.row?.start_date
                    ? DateFormat(params?.row?.start_date, 'datesUTC')
                    : params?.row?.start_date
                      ? DateFormat(params?.row?.start_date, 'dates')
                      : ''}{' '}
                </Typography>
              </Tooltip>
            ) : (
              '-'
            )}
          </Box>
        );
      },
    },
    {
      field: 'end_date',
      headerName: 'To',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100">
            {params?.value !== '-' ? (
              <Tooltip
                arrow
                title={
                  <Typography>
                    {/* Day & Hours dates */}
                    {params?.row?.duration_type === 'Hours' &&
                    params?.row?.end_date
                      ? DateFormat(params?.row?.end_date, 'datesUTC')
                      : params?.row?.end_date
                        ? DateFormat(params?.row?.end_date, 'dates')
                        : ''}
                  </Typography>
                }
                placement="bottom-start"
                classes={{
                  tooltip: 'info-tooltip-container ',
                }}
              >
                {/* Day & Hours dates */}
                <Typography className="title-text text-ellipsis-line cursor-pointer">
                  {params?.row?.duration_type === 'Hours' &&
                  params?.row?.end_date
                    ? DateFormat(params?.row?.end_date, 'datesUTC')
                    : params?.row?.end_date
                      ? DateFormat(params?.row?.end_date, 'dates')
                      : ''}
                </Typography>
              </Tooltip>
            ) : (
              '-'
            )}
          </Box>
        );
      },
    },
    {
      field: 'leave_days',
      headerName: 'No. of Days/Hours',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100 no-of-days-wrap">
            <Box sx={{ boxShadow: 'none' }} className="cursor-pointer">
              <Tooltip
                className="event-title"
                arrow
                interactive
                title={
                  <LeavePopOver
                    title={`Total Leave ${params?.row?.leave_period_type === 'day' ? 'Days' : 'Hours'}`}
                    Leavedays={`Dates`} //${params?.row?.leave_period_type === 'day' ? 'Days' : 'Hours'}
                    leave="Leave"
                    startDate={params?.row?.start_date}
                    endDate={params?.row?.end_date}
                    leaveTypes={params?.row?.leave_period_type}
                    leave_days={params?.row?.leave_days}
                    leave_days_obj={
                      params?.row?.leave_days_obj
                        ? JSON.parse(params?.row?.leave_days_obj)
                        : null
                    }
                  />
                }
                classes={{
                  tooltip: 'leave-days-popover',
                }}
              >
                <Typography className="title-text leave-days-wrap">
                  {params?.row?.leave_days}
                </Typography>
              </Tooltip>
            </Box>
          </Box>
        );
      },
    },
    {
      field: 'Added date',
      headerName: 'Added date',
      width: 110,
      minWidth: 110,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100">
            <Typography className="title-text text-ellipsis-line pr4">
              {DateFormat(params?.row?.createdAt, 'dates')}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'request_reason',
      headerName: 'Reason',
      width: 160,
      minWidth: 160,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100">
            {params?.value !== '-' ? (
              <Typography className="title-text">
                {params?.value?.length > 20 ? (
                  <>
                    {params?.value.substring(0, 15)}
                    <Tooltip
                      title={<Typography>{params?.value}</Typography>}
                      classes={{
                        tooltip: 'info-tooltip-container ',
                      }}
                      placement="bottom"
                      arrow
                    >
                      <span className="more-text-wrap cursor-pointer">
                        ... More
                      </span>
                    </Tooltip>
                  </>
                ) : (
                  params?.value
                )}
              </Typography>
            ) : (
              '-'
            )}
          </Box>
        );
      },
    },
    {
      field: 'Action By',
      headerName: 'Action By',
      width: 160,
      minWidth: 160,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return params?.row?.request_status === 'pending' ? (
          <Box className="d-flex align-center justify-center h100 flex-col cursor-pointer">
            <Typography
              variant="h6"
              className="title-text fw400 text-ellipsis-line"
            >
              -
            </Typography>
          </Box>
        ) : (
          <Tooltip
            arrow
            title={
              <Box className="d-flex align-center flex-col justify-center h100">
                {params?.row?.request_approved_users?.user_full_name ? (
                  <Typography className="title-text fw400 text-ellipsis-line">
                    {params?.row?.request_approved_users?.user_full_name}
                  </Typography>
                ) : (
                  <Typography className="title-text fw400 text-ellipsis-line">
                    System Auto-Approval
                  </Typography>
                )}

                {/* updatedAt will always be displayed */}
                <Typography className="title-text fw400 text-ellipsis-line">
                  {DateFormat(params?.row?.updatedAt, 'datesWithhour')}
                </Typography>
              </Box>
            }
            classes={{
              tooltip: 'info-tooltip-container ',
            }}
          >
            <Box className="d-flex align-center justify-center h100 flex-col cursor-pointer">
              {params?.row?.request_approved_users?.user_full_name ? (
                <Typography className="title-text fw400 text-ellipsis-line">
                  {params?.row?.request_approved_users?.user_full_name}
                </Typography>
              ) : (
                <Typography className="title-text fw400 text-ellipsis-line">
                  System Auto-Approval
                </Typography>
              )}

              {/* updatedAt will always be displayed */}
              <Typography className="title-text fw400 text-ellipsis-line">
                {DateFormat(params?.row?.updatedAt, 'datesWithhour')}
              </Typography>
            </Box>
          </Tooltip>
        );
      },
    },
    {
      field: 'request_status',
      headerName: 'Status',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center leave-status-wrap h100 text-capital">
            <Typography
              className={`sub-title-text ${getStatusClass(params?.value)} fw600`}
            >
              {params?.value}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'actions',
      headerName: 'Action',
      width: 90,
      minWidth: 90,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        // Custom actions icons for each row
        return (
          <Box className="d-flex justify-center actions view-icon-wrap">
            <Tooltip
              title={<Typography>View</Typography>}
              arrow
              classes={{
                tooltip: 'info-tooltip-container',
              }}
            >
              <Box className="">
                <ViewIcon
                  onClick={() => {
                    router.push(`/leave-remark/${params?.row?.id}`);
                    saveToStorage(identifiers?.RedirectData, {
                      viewMode: 'list',
                    });
                    // setAuthState({ ...authState, listViewMode: 'list' });
                  }}
                />
              </Box>
            </Tooltip>
          </Box>
        );
      },
    },
  ];
  const getLeaveRemarkList = async (page, filter, startDate, endDate, Rpp) => {
    let calenderStarDate = startDate;
    let calenderEndDate = endDate;
    // setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_LEAVE_REMARK +
          `?search=${filter?.searchValue}&size=${
            Rpp ? Rpp : rowsPerPage
          }&page=${page}&branch_id=${filter?.branch}&department_id=${
            filter?.department
          }&status=${filter?.status}&role_id=${filter?.role}&start_date=${
            calenderStarDate
          }&end_date=${calenderEndDate}`
      );
      if (status === 200) {
        setLoader(false);
        setUserdata();
        removeFromStorage(identifiers?.RedirectData);
        const leaveList = data?.userList;
        setTotalCount(data?.count);
        setLeaveRemarkList(leaveList ? leaveList : []);
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // useEffect(() => {
  //   if (
  //     fetchFromStorage(identifiers?.RedirectData) &&
  //     fetchFromStorage(identifiers?.RedirectData)?.IsFromUser
  //   ) {
  //     const fdata = fetchFromStorage(identifiers?.RedirectData);
  //     setPage(fdata?.page);
  //     setFilterDataApplied(fdata?.filterData);
  //     setSearchValue(fdata?.searchValue);
  //     getLeaveRemarkList(fdata?.page, fdata?.searchValue, fdata?.filterData);
  //   } else if (userdata && userdata?.IsFromUser) {
  //     const fdata = userdata;
  //     setPage(fdata?.page);
  //     setFilterDataApplied(fdata?.filterData);
  //     setSearchValue(fdata?.searchValue);
  //     getLeaveRemarkList(fdata?.page, fdata?.searchValue, fdata?.filterData);
  //   }
  // }, [
  //   fetchFromStorage(identifiers?.RedirectData)?.IsFromUser,
  //   userdata?.IsFromUser,
  // ]);

  useEffect(() => {
    if (
      authState?.UserPermission?.leave_center === 1 ||
      authState?.UserPermission?.leave_center === 2
    ) {
      fetchRoleList();
      // if (
      //   !fetchFromStorage(identifiers?.RedirectData) &&
      //   userdata?.page === undefined &&
      //   fetchFromStorage(identifiers?.RedirectData)?.IsFromUser === undefined &&
      //   userdata?.IsFromUser === undefined
      // ) {
      getLeaveRemarkList(
        page,
        filterDataApplied,
        getDateByfilterApi().start,
        getDateByfilterApi().end
      );
      // }
    }
  }, [authState?.UserPermission?.leave_center]);

  const onPageChange = (newPage) => {
    setPage(newPage);
    getLeaveRemarkList(
      newPage,
      filterDataApplied,
      getDateByfilterApi().start,
      getDateByfilterApi().end
    );
  };
  const OnRowPerPage = (newPage) => {
    setPage(1);
    setRowsPerPage(newPage);
    getLeaveRemarkList(
      1,
      filterDataApplied,
      getDateByfilterApi().start,
      getDateByfilterApi().end,
      newPage
    );
  };

  const handleFilterData = (type) => {
    setFilter(false);
    if (type === 'apply') {
      setFilterDataApplied(filterData);
      getLeaveRemarkList(
        1,
        filterData,
        getDateByfilterApi().start,
        getDateByfilterApi().end
      );
    } else {
      const clearFilter = {
        status: '',
        branch: '',
        department: '',
        role: '',
        startDate: '',
        endDate: '',
        calendarValue: '',
        searchValue: '',
      };
      setDateSelectedOption('this_month');
      setFilterData(clearFilter);
      setFilterDataApplied(clearFilter);
      getLeaveRemarkList(
        1,
        clearFilter,
        moment().startOf('month').format('YYYY-MM-DD'),
        moment().endOf('month').format('YYYY-MM-DD')
      );
      setCustomEndDate(null);
      setCustomStartDate(null);
    }
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      setPage(1);
      handleFilterData('apply');
    }
  };

  const toggleView = (mode) => {
    setViewMode(mode);
  };

  // Resets all filters to default values

  const handleStartDateChange = (date) => {
    setCustomStartDate(date); // Set start date
  };

  const handleEndDateChange = (date) => {
    setCustomEndDate(date); // Set end date
  };

  const getDateByfilter = () => {
    if (dateSelectedOption === 'today') {
      return DateFormat(moment().startOf('day').toDate(), 'dates');
    } else if (dateSelectedOption === 'yesterday') {
      return DateFormat(
        moment().subtract(1, 'day').startOf('day').toDate(),
        'dates'
      );
    } else if (dateSelectedOption === 'this_week') {
      const start = DateFormat(moment().startOf('isoWeek').toDate(), 'dates');
      const end = DateFormat(moment().endOf('isoWeek').toDate(), 'dates');
      return `${start} - ${end}`;
    } else if (dateSelectedOption === 'this_month') {
      const start = DateFormat(moment().startOf('month').toDate(), 'dates');
      const end = DateFormat(moment().endOf('month').toDate(), 'dates');
      return `${start} - ${end}`;
    } else if (dateSelectedOption === 'this_quarter') {
      const start = DateFormat(moment().startOf('quarter').toDate(), 'dates');
      const end = DateFormat(moment().endOf('quarter').toDate(), 'dates');
      return `${start} - ${end}`;
    } else if (dateSelectedOption === 'this_year') {
      const start = DateFormat(moment().startOf('year').toDate(), 'dates');
      const end = DateFormat(moment().endOf('year').toDate(), 'dates');
      return `${start} - ${end}`;
    } else if (dateSelectedOption === 'custom') {
      return '';
    } else {
      return '';
    }
  };

  const getDateByfilterApi = () => {
    if (dateSelectedOption === 'today') {
      return {
        start: moment().startOf('day').format('YYYY-MM-DD'),
        end: moment().startOf('day').format('YYYY-MM-DD'),
      };
    } else if (dateSelectedOption === 'yesterday') {
      return {
        start: moment().subtract(1, 'day').startOf('day').format('YYYY-MM-DD'),
        end: moment().subtract(1, 'day').startOf('day').format('YYYY-MM-DD'),
      };
    } else if (dateSelectedOption === 'this_week') {
      return {
        start: moment().startOf('isoWeek').format('YYYY-MM-DD'),
        end: moment().endOf('isoWeek').format('YYYY-MM-DD'),
      };
    } else if (dateSelectedOption === 'this_month') {
      return {
        start: moment().startOf('month').format('YYYY-MM-DD'),
        end: moment().endOf('month').format('YYYY-MM-DD'),
      };
    } else if (dateSelectedOption === 'this_quarter') {
      return {
        start: moment().startOf('quarter').format('YYYY-MM-DD'),
        end: moment().endOf('quarter').format('YYYY-MM-DD'),
      };
    } else if (dateSelectedOption === 'this_year') {
      return {
        start: moment().startOf('year').format('YYYY-MM-DD'),
        end: moment().endOf('year').format('YYYY-MM-DD'),
      };
    } else if (dateSelectedOption === 'custom') {
      return {
        start: dayjs(customStartDate).format('YYYY-MM-DD'),
        end: dayjs(customEndDate).format('YYYY-MM-DD'),
      };
    } else {
      return '';
    }
  };

  const getEventColor = (event) => {
    switch (event) {
      case 'pending':
        return '#fef4e6';
      case 'approved':
        return '#f9ffed';
      case 'rejected':
        return '#fce9ea';
      case 'cancelled':
        return '#837f7f';
      default:
        return 'red';
    }
  };
  const getLeaveDetails = async (view, search) => {
    if (!view && !calenderSearch) return;

    try {
      let apiUrl = `${URLS?.CALENDER_WISE_LEAVE}?list_type=staff&search=${search || search === '' ? search : calenderSearch}`;

      // if (view?.month) {
      //   const month = moment(view?.month).format('M');
      //   const year = moment(view?.month).format('YYYY');
      //   apiUrl += `&months=${month}&year=${year}`;
      // } else if (view?.year) {
      //   apiUrl += `&year=${view?.year}`;
      // }
      // if (view?.day) {
      //   apiUrl += `&start_date=${view?.Start}&end_date=${view?.Start}`;
      // } else
      if (view?.Start && view?.End) {
        apiUrl += `&start_date=${view?.Start}&end_date=${view?.End}`;
      }

      const { status, data } = await axiosInstance.get(apiUrl);
      if (status === 200) {
        const coloredEvents = data?.data?.calenderLeaves?.map((event) => ({
          ...event,
          start: moment.utc(event?.start_date).local().format('YYYY-MM-DD'),
          end: event?.end_date
            ? moment.utc(event?.start_date).local().format('YYYY-MM-DD') ===
              moment.utc(event?.end_date).local().format('YYYY-MM-DD')
              ? moment.utc(event?.end_date).local().format('YYYY-MM-DD')
              : event?.duration_type === 'Hours'
                ? moment.utc(event?.end_date).local().format('YYYY-MM-DD')
                : moment
                    .utc(event?.end_date)
                    .add(1, 'day')
                    .local()
                    .format('YYYY-MM-DD')
            : moment.utc(event?.start_date).local().format('YYYY-MM-DD'),
          allDay: true,
          eventType: 'leave',
        }));

        const holidayEvents = data?.data?.holidayList?.map((event) => ({
          ...event,
          id: `holiday-${event?.id}`,
          start: moment
            .utc(event?.holiday_policy_start_date)
            .local()
            .format('YYYY-MM-DD'),
          end: moment
            .utc(event?.holiday_policy_end_date)
            .local()
            .add(1, 'day')
            .format('YYYY-MM-DD'),
          title: event?.holiday_policy_name,
          backgroundColor: event?.holiday_policy_colour,
          borderColor: event?.holiday_policy_colour,
          allDay: true,
          eventType: 'holiday',
        }));

        setCalenderData({
          coloredEvents,
          holidayEvents,
          allEvents: [...holidayEvents, ...coloredEvents],
        });
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  useEffect(() => {
    const storedData = fetchFromStorage(identifiers?.RedirectData);
    if (storedData?.viewMode) {
      setViewMode(storedData.viewMode);
      removeFromStorage(identifiers?.RedirectData);
    } else {
      setViewMode('calendar');
    }
  }, [identifiers?.RedirectData]); // Only depend on identifiers.RedirectData, not fetchFromStorage()

  return (
    <>
      <Box>
        <Box className="calender-wrap">
          <Box className="icons-container d-flex justify-space-between align-center">
            <Typography className="title-sm">
              {viewMode === 'calendar' ? 'Calender view' : 'List view'}
            </Typography>
            {viewMode === 'calendar' && (
              <Box className="calender-search-wrap">
                <Searchbar
                  setSearchValue={(e) => {
                    setCalenderSearch(e);
                    getLeaveDetails(viewData, e);
                  }}
                  searchValue={calenderSearch}
                  onKeyPress={handleKeyPress}
                />
              </Box>
            )}
            <Box className={`d-flex align-center gap-sm `}>
              <CustomButton
                variant={viewMode === 'calendar' ? 'contained' : 'outlined'}
                isIconOnly
                startIcon={
                  <Tooltip
                    title={
                      <Typography className="sub-title-text">
                        Calender View
                      </Typography>
                    }
                    classes={{
                      tooltip: 'info-tooltip-container',
                    }}
                    arrow
                  >
                    <CalendarMonthIcon />
                  </Tooltip>
                }
                onClick={() => {
                  toggleView('calendar');
                }}
              />

              <CustomButton
                variant={viewMode === 'list' ? 'contained' : 'outlined'}
                isIconOnly
                startIcon={
                  <Tooltip
                    title={
                      <Typography className="sub-title-text">
                        List View
                      </Typography>
                    }
                    classes={{
                      tooltip: 'info-tooltip-container',
                    }}
                    arrow
                  >
                    <MenuIcon />
                  </Tooltip>
                }
                onClick={() => {
                  toggleView('list');
                }}
              />
            </Box>
          </Box>
          <Divider />
          {viewMode === 'calendar' && (
            <Box className="calendar-sec-wrap calender-view-wrap">
              <CustomFullCalendar
                getLeaveDetails={getLeaveDetails}
                calenderData={{ calenderData, type: 'staff' }}
                viewData={viewData}
                setViewData={setViewData}
                getEventColor={getEventColor}
              />
            </Box>
          )}
          {viewMode === 'list' && (
            <Box className="table-container table-layout pt16">
              {loader ? (
                <Box className="content-loader">
                  <CircularProgress className="loader" color="inherit" />
                </Box>
              ) : (
                <Box>
                  <Box className="search-section-wrap">
                    <Box className="search-section-fields">
                      <Searchbar
                        setSearchValue={(e) => {
                          setFilterData({
                            ...filterData,
                            searchValue: e,
                          });
                        }}
                        searchValue={filterData?.searchValue}
                        onKeyPress={handleKeyPress}
                      />
                    </Box>
                    {/* Select for Branch */}
                    <Box className="search-section-fields">
                      <CustomSelect
                        placeholder="Branches"
                        showDot
                        options={AllListsData?.ActiveBranchList}
                        value={
                          AllListsData?.ActiveBranchList?.find((opt) => {
                            return opt?.value === filterData?.branch;
                          }) || ''
                        }
                        onChange={(e) => {
                          setFilterData({
                            ...filterData,
                            branch: e?.value,
                          });
                        }}
                      />
                    </Box>
                    {/* Select for Leave Status */}
                    <Box className="search-section-fields">
                      <CustomSelect
                        placeholder="Leave Status"
                        options={staticOptions?.STATUS_OPTIONS}
                        value={
                          staticOptions?.STATUS_OPTIONS?.find((opt) => {
                            return opt?.value === filterData?.status;
                          }) || ''
                        }
                        onChange={(e) => {
                          setFilterData({
                            ...filterData,
                            status: e?.value,
                          });
                        }}
                      />
                    </Box>

                    <Box className="d-flex">
                      <DateFilter
                        dateSelectedOption={dateSelectedOption}
                        setDateSelectedOption={setDateSelectedOption}
                        customStartDate={customStartDate}
                        setCustomStartDate={setCustomStartDate}
                        customEndDate={customEndDate}
                        setCustomEndDate={setCustomEndDate}
                        dateFilterList={dateOptions}
                      />
                    </Box>

                    <Box className="d-flex align-center gap-sm">
                      <CustomButton
                        variant="outlined"
                        isIconOnly
                        startIcon={
                          <Tooltip
                            title={<Typography>Apply Filter</Typography>}
                            arrow
                            classes={{
                              tooltip: 'info-tooltip-container',
                            }}
                          >
                            <TuneIcon />
                          </Tooltip>
                        }
                        onClick={() => {
                          setFilter(!filter);
                        }}
                      />
                      <CustomButton
                        variant="contained"
                        isIconOnly
                        startIcon={
                          <Tooltip
                            title={<Typography>Apply Filter</Typography>}
                            arrow
                            classes={{
                              tooltip: 'info-tooltip-container',
                            }}
                          >
                            <CheckIcon />
                          </Tooltip>
                        }
                        disabled={
                          dateSelectedOption === 'custom'
                            ? customStartDate && customEndDate
                              ? false
                              : true
                            : false
                        }
                        onClick={() => {
                          setPage(1);
                          handleFilterData('apply');
                        }}
                      />
                      <CustomButton
                        variant="outlined"
                        isIconOnly
                        startIcon={
                          <Tooltip
                            title={<Typography>Clear Filter</Typography>}
                            arrow
                            classes={{
                              tooltip: 'info-tooltip-container',
                            }}
                          >
                            <ClearOutlinedIcon />
                          </Tooltip>
                        }
                        onClick={() => {
                          handleFilterData('cancel');
                        }}
                      />
                    </Box>
                  </Box>
                  {getDateByfilter() ? (
                    <span className="today-date-wrap d-flex align-center justify-end pt16">
                      {getDateByfilter()}
                    </span>
                  ) : (
                    <></>
                  )}
                  {dateSelectedOption === 'custom' && (
                    <>
                      <Box className="d-flex gap-5 justify-end align-center">
                        <Box className="d-flex" style={{ margin: 0 }}>
                          <Box m="8px">
                            <CustomDatePicker
                              label="Start Date"
                              value={customStartDate}
                              onChange={handleStartDateChange}
                              fullWidth
                              disableFuture={false}
                              error={false}
                              inputVariant="outlined"
                            />
                          </Box>
                          <Box m="8px">
                            <CustomDatePicker
                              label="End Date"
                              value={customEndDate}
                              minDate={customStartDate}
                              onChange={handleEndDateChange}
                              fullWidth
                              error={false}
                              disableFuture={false}
                              inputVariant="outlined"
                            />
                          </Box>
                        </Box>
                      </Box>
                    </>
                  )}
                  {leaveRemarkList && leaveRemarkList?.length === 0 ? (
                    <Box className="no-data d-flex align-center justify-center">
                      <NoDataView
                        title="No leave Records Found"
                        description="There is no leave data available at the moment."
                      />
                    </Box>
                  ) : (
                    <>
                      <DataGrid
                        rows={leaveRemarkList && leaveRemarkList}
                        columns={columns}
                        pageSize={rowsPerPage}
                        checkboxSelection={false} // Disable default checkbox column
                        disableSelectionOnClick // Disable row selection on click
                        columnVisibilityModel={{
                          actions:
                            authState?.UserPermission?.leave_center === 2
                              ? true
                              : false,
                        }}
                        hideMenuIcon
                        rowHeight={70}
                        autoHeight
                        // getRowHeight={() => 'auto'}
                        sx={{
                          transition: 'none', // Disables transition effects
                          [`& .${gridClasses.cell}`]: {
                            py: 1,
                          },
                        }}
                        // sx={{
                        //   [`& .${gridClasses.cell}`]: {
                        //     py: 1,
                        //   },
                        // }}
                      />
                      <CustomPagination
                        currentPage={page}
                        totalCount={totalCount}
                        rowsPerPage={rowsPerPage}
                        onPageChange={onPageChange}
                        OnRowPerPage={OnRowPerPage}
                      />
                    </>
                  )}
                </Box>
              )}
            </Box>
          )}
        </Box>
        <DialogBox
          open={filter}
          handleClose={() => {
            setFilter(!filter);
          }}
          title={'Leave filter'}
          className="small-dialog-box-container"
          content={
            <>
              <Box className="staff-filter pt8">
                <Box>
                  <CustomSelect
                    placeholder="Department"
                    options={AllListsData?.ActiveDepartmentList}
                    value={
                      AllListsData?.ActiveDepartmentList?.find((opt) => {
                        return opt?.value === filterData?.department;
                      }) || ''
                    }
                    onChange={(e) => {
                      setFilterData({
                        ...filterData,
                        department: e?.value,
                      });
                    }}
                    label={<span>Department</span>}
                    menuPortalTarget={document.body}
                    styles={{
                      menuPortal: (base) => ({
                        ...base,
                        zIndex: 9999,
                      }),
                    }}
                  />
                </Box>
                <Box className="pt8">
                  <CustomSelect
                    placeholder="System access"
                    options={roleList}
                    value={
                      roleList?.find((opt) => {
                        return opt?.value === filterData?.role;
                      }) || ''
                    }
                    onChange={(e) => {
                      setFilterData({
                        ...filterData,
                        role: e?.value,
                      });
                    }}
                    label={<span>System access</span>}
                    menuPortalTarget={document.body}
                    styles={{
                      menuPortal: (base) => ({
                        ...base,
                        zIndex: 9999,
                      }),
                    }}
                  />
                </Box>
                <Box className="form-actions-btn">
                  <CustomButton
                    variant="outlined"
                    title="Cancel"
                    onClick={() => {
                      handleFilterData('cancel');
                    }}
                  />
                  <CustomButton
                    variant="contained"
                    title="Apply"
                    onClick={() => {
                      setPage(1);
                      handleFilterData('apply');
                    }}
                  />
                </Box>
              </Box>
            </>
          }
        />
      </Box>
    </>
  );
}
