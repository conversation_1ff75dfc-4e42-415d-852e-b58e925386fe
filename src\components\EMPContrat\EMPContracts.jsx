'use client';

import React, { useEffect, useLayoutEffect, useState } from 'react';
import {
  Box,
  Typography,
  CircularProgress,
  Tooltip,
  Divider,
} from '@mui/material';
import { GridIcon, MenuIcon } from '@/helper/common/images';
import FolderGridViews from '@/components/FolderViews/FolderGridViews';
import FolderListViews from '@/components/FolderViews/FolderListViews';
import FolderSideBar from '@/components/FolderViews/FolderSideBar';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import { setApiMessage } from '@/helper/common/commonFunctions';
import _ from 'lodash';
import { useRouter, useSearchParams } from 'next/navigation';
import DialogBox from '@/components/UI/Modalbox';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import FlipToFrontIcon from '@mui/icons-material/FlipToFront';
import DriveFileRenameOutlineIcon from '@mui/icons-material/DriveFileRenameOutline';
import VisibilityOffOutlinedIcon from '@mui/icons-material/VisibilityOffOutlined';
import VisibilityOutlinedIcon from '@mui/icons-material/VisibilityOutlined';
import HistoryIcon from '@mui/icons-material/History';
import AssignEmployee from '@/components/Leave/AssignEmployee';
import RightDrawer from '@/components/UI/RightDrawer';
import GroupAddIcon from '@mui/icons-material/GroupAdd';
import PersonAddAlt1Icon from '@mui/icons-material/PersonAddAlt1';
import CustomSearch from '@/components/UI/CustomSearch';
import CustomButton from '@/components/UI/CustomButton';
import CancelIcon from '@mui/icons-material/Cancel';
import DeleteModal from '@/components/UI/DeleteModal';
import CheckIcon from '@mui/icons-material/Check';
import NoDataView from '../UI/NoDataView';
// import ClearOutlinedIcon from '@mui/icons-material/ClearOutlined';

export default function EmpContracts() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const options = [
    {
      id: 1,
      name: 'Edit',
      value: 'edit',
      icon: <DriveFileRenameOutlineIcon />,
    },
    { id: 5, name: 'Duplicate', value: 'copy', icon: <ContentCopyIcon /> },
    { id: 6, name: 'Move', value: 'move', icon: <FlipToFrontIcon /> },
    { id: 4, name: 'Version History', value: 'version', icon: <HistoryIcon /> },
    {
      id: 3,
      name: 'Deactive',
      value: 'inactive',
      icon: <VisibilityOffOutlinedIcon />,
    },
    { id: 2, name: 'Delete', value: 'deleted', icon: <DeleteOutlineIcon /> },
    {
      id: 7,
      name: 'Assign Job Role',
      value: 'jobrole',
      icon: <PersonAddAlt1Icon className="assign-icon" />,
    },
    {
      id: 8,
      name: 'Assign Additional Duties',
      value: 'additionduties',
      icon: <GroupAddIcon className="assign-icon" />,
    },
    {
      id: 9,
      name: 'Assign General Contract',
      value: 'general',
      icon: <PersonAddAlt1Icon className="assign-icon" />,
    },
  ];

  const sideMenu = [{ id: 'all', name: 'All Contract' }];
  const [empContractList, setEmpContractList] = useState([]);
  const [loader, setLoader] = useState(false);
  const [isViewType, setViewType] = useState('grid');
  const [searchValue, setSearchValue] = useState('');
  const [selectedFolderData, setSelectedFolderData] = useState([]);
  const [sideBarMenu, setSideBarMenu] = useState(sideMenu);
  const [selectedSideBarData, setSelectedSideBarData] = useState(
    sideBarMenu[0]
  );
  const [moveToogleModal, setMoveToogleModal] = useState(false);
  const [moveDetails, setMoveDetails] = useState('');
  const [assignEmployee, setAssignEmployee] = useState();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteId, setDeleteId] = useState(null);
  // const open = Boolean(anchorEl);

  const queryParams = new URLSearchParams(searchParams);

  const isView = searchParams.get('view_type');

  // LIST OF FOLDER
  const getFolderList = async () => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(URLS?.GET_FOLDER_LIST);

      if (status === 200) {
        setLoader(false);
        const folderList = data?.data;
        let mergeList = _.concat(sideMenu, folderList);
        setSideBarMenu(mergeList);
      }
    } catch (error) {
      setLoader(false);
      setSideBarMenu(sideMenu);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // LIST OF FILE
  const getFileList = async (search, isUpdate) => {
    !isUpdate && setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_FILE_LIST +
          `?category_id=${
            selectedSideBarData?.id === 'all' ? '' : selectedSideBarData?.id
          }&search=${search ? search : ''}`
      );

      if (status === 200) {
        setLoader(false);
        const folderList = data?.data?.map((item) => {
          return {
            ...item,
            updateDate: item?.updatedAt,
            filesize: item?.content_size,
            filetype: item?.type,
            cat_id: item?.emp_contract_category?.id,
            cat_name: item?.emp_contract_category?.name,
          };
        });
        setEmpContractList(folderList);
        setSelectedFolderData([]);
      }
    } catch (error) {
      setLoader(false);
      setEmpContractList([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const AssignEmployees = async (user_ids, assignId, oldId) => {
    const filterIds = user_ids?.filter((id) => !oldId.includes(id));
    const RemovedIds = oldId?.filter((id) => !user_ids.includes(id));
    const sendData = {
      ...(assignEmployee?.assigntype === 'jobrole' && {
        department_user_id: filterIds,
      }),
      ...(assignEmployee?.assigntype === 'general' && {
        general_user_id: filterIds,
      }),
      ...(assignEmployee?.assigntype === 'additionduties' && {
        additional_user_id: filterIds,
      }),
      ...(assignEmployee?.assigntype === 'additionduties' &&
        RemovedIds &&
        RemovedIds?.length > 0 && {
          removed_additional_user_id: RemovedIds,
        }),
      template_id: assignEmployee?.id,
    };
    try {
      const { status, data } = await axiosInstance.put(
        URLS?.REGENERATE_CONTRACT_FORM,
        sendData
      );
      if (status === 200) {
        if (data.status) {
          setApiMessage('success', data?.message);
          setAssignEmployee(false);
          getFileList('', true);
        } else {
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  useEffect(() => {
    getFolderList();
  }, []);

  useEffect(() => {
    if (selectedSideBarData) {
      getFileList(searchValue, false);
    }
  }, [selectedSideBarData]);

  useLayoutEffect(() => {
    setViewType(isView || 'grid');
  }, [isView]);
  const handleViewType = (type) => {
    setViewType(type);
    queryParams.set('view_type', type);
    router.push(`?${queryParams.toString()}`);
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      getFileList(searchValue, true);
    }
  };

  const filteredSideBarMenu =
    selectedSideBarData?.id === sideMenu[0]?.id
      ? sideBarMenu?.filter((menu) => menu?.id !== sideMenu[0]?.id)
      : sideBarMenu?.filter((menu) => menu?.id === selectedSideBarData?.id);

  const handleOptionManage = (option, item) => {
    if (option?.value === 'version') {
      router.push(`contract-view/${item?.id}?history=true`);
    } else if (option?.value === 'edit') {
      router.push(`edit-emp-contract/${item?.id}`);
    } else if (option?.value === 'deleted') {
      // handleStatus(option?.value, item?.id);
      handleOpenDeleteDialog(option?.value, item?.id);
    } else if (option?.value === 'inactive') {
      handleStatus(option?.value, item?.id);
    } else if (option?.value === 'active') {
      handleStatus(option?.value, item?.id);
    } else if (option?.value === 'copy') {
      handleCopy(item?.id);
    } else if (option?.value === 'move') {
      setMoveDetails(item);
      setMoveToogleModal(true);
    } else if (option?.value === 'jobrole') {
      setAssignEmployee({
        ...item,
        assigntype: option?.value,
        title: 'job role',
      });
    } else if (option?.value === 'additionduties') {
      setAssignEmployee({
        ...item,
        assigntype: option?.value,
        title: 'Additional Duties',
      });
    } else if (option?.value === 'general') {
      setAssignEmployee({
        ...item,
        assigntype: option?.value,
        title: 'General Contract',
      });
    }
  };

  const handleStatus = async (type, tempID) => {
    const sendData = {
      template_ids: tempID ? [tempID] : selectedFolderData,
      status: type,
    };

    try {
      // setLoader(true);
      const { status, data } = await axiosInstance.delete(
        URLS?.DELETE_TEMPLATES,
        {
          data: sendData,
        }
      );
      if (status === 200) {
        if (data.status) {
          getFileList(searchValue, true);
          setApiMessage('success', data?.message);
          handleCloseDeleteDialog();
        } else {
          setApiMessage('error', data?.message);
          handleCloseDeleteDialog();
        }
      } else {
        setApiMessage('error', data?.message);
        handleCloseDeleteDialog();
      }
      setLoader(false);
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
      handleCloseDeleteDialog();
    }
  };

  const handleCopy = async (tempID) => {
    try {
      setLoader(true);
      const { status, data } = await axiosInstance.put(
        URLS?.COPY_CONTRACT + `/${tempID}`
      );
      if (status === 200 || status === 201) {
        if (data.status) {
          getFileList(searchValue, true);
          setApiMessage('success', data?.message);
        } else {
          setApiMessage('error', data?.message);
        }
      } else {
        setApiMessage('error', data?.message);
      }
      setLoader(false);
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const handleMove = async (cat_id) => {
    const sendData = {
      category_id: cat_id,
    };
    try {
      setLoader(true);
      const { status, data } = await axiosInstance.put(
        URLS?.MOVE_CONTRACT + `/${moveDetails?.id}`,
        sendData
      );
      if (status === 200 || status === 201) {
        if (data.status) {
          getFileList(searchValue, true);
          setApiMessage('success', data?.message);
        } else {
          setApiMessage('error', data?.message);
        }
        setMoveDetails('');
        setMoveToogleModal(false);
      } else {
        setApiMessage('error', data?.message);
      }
      setLoader(false);
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const handleContractView = (item) => {
    router.push(`contract-view/${item?.id}`);
  };

  // const handleClearFilter = () => {
  //   setSearchValue('');
  //   getFileList('', true);
  // };
  const handleOpenDeleteDialog = (value, id) => {
    setDeleteId({ value, id });
    setDeleteDialogOpen(true);
  };
  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setDeleteId(null);
  };

  const handleClearSearch = () => {
    getFileList('', true);
  };

  return (
    <>
      <Box className="folder-section-wrapper">
        <Box className="folder-section-left">
          <Typography className="sub-header-text folder-section-left-title">
            Folder
          </Typography>
          <Divider />
          <FolderSideBar
            sideBarMenu={sideBarMenu}
            setSelectedSideBarData={setSelectedSideBarData}
          />
        </Box>
        <Box className="folder-section-right">
          <Box className="folder-section-right-content">
            <Box className="emp-contract-page">
              {loader ? (
                <Box className="content-loader">
                  <CircularProgress className="loader" color="inherit" />
                </Box>
              ) : (
                <>
                  <Box className="">
                    <Box className="mb8">
                      <Typography className="title-sm fw600">
                        {selectedSideBarData !== ''
                          ? selectedSideBarData?.name
                          : sideBarMenu?.[0]?.name}
                      </Typography>
                    </Box>
                    <Divider />
                    <Box className="d-flex justify-end align-center gap-sm mt16 mb16">
                      <Box className="d-flex justify-start align-center gap-sm">
                        <CustomSearch
                          setSearchValue={setSearchValue}
                          searchValue={searchValue}
                          onKeyPress={handleKeyPress}
                          isClearSearch
                          handleClearSearch={() => handleClearSearch()}
                        />
                        <Box className="d-flex align-center gap-sm">
                          <CustomButton
                            isIconOnly
                            startIcon={
                              <Tooltip
                                title={
                                  <Typography className="sub-title-text">
                                    Apply Filter
                                  </Typography>
                                }
                                classes={{ tooltip: 'info-tooltip-container' }}
                                arrow
                              >
                                <CheckIcon />
                              </Tooltip>
                            }
                            onClick={() => {
                              getFileList(searchValue, true);
                            }}
                          />

                          {/* <CustomButton
                            variant="outlined"
                            isIconOnly
                            startIcon={
                              <Tooltip
                                title={
                                  <Typography className="sub-title-text">
                                    Clear Filter
                                  </Typography>
                                }
                                classes={{ tooltip: 'info-tooltip-container' }}
                                arrow
                              >
                                <ClearOutlinedIcon />
                              </Tooltip>
                            }
                            onClick={handleClearFilter}
                          /> */}
                        </Box>
                      </Box>
                      <Box className="d-flex justify-start align-center gap-sm">
                        <Box>
                          <CustomButton
                            variant={
                              isViewType === 'grid' ? 'contained' : 'outlined'
                            }
                            isIconOnly
                            startIcon={
                              <Tooltip
                                title={
                                  <Typography className="sub-title-text">
                                    Grid View
                                  </Typography>
                                }
                                classes={{
                                  tooltip: 'info-tooltip-container',
                                }}
                                arrow
                              >
                                <Box>
                                  <GridIcon
                                    className={`action-btn-icon ${isViewType === 'grid' ? 'active' : ''}`}
                                  />
                                </Box>
                              </Tooltip>
                            }
                            onClick={() => handleViewType('grid')}
                          />
                        </Box>
                        <Box>
                          <CustomButton
                            variant={
                              isViewType === 'list' ? 'contained' : 'outlined'
                            }
                            isIconOnly
                            startIcon={
                              <Tooltip
                                title={
                                  <Typography className="sub-title-text">
                                    List View
                                  </Typography>
                                }
                                classes={{
                                  tooltip: 'info-tooltip-container',
                                }}
                                arrow
                              >
                                <Box>
                                  <MenuIcon
                                    className={`action-btn-icon ${isViewType === 'list' ? 'active' : ''}`}
                                  />
                                </Box>
                              </Tooltip>
                            }
                            onClick={() => handleViewType('list')}
                          />
                        </Box>
                      </Box>
                    </Box>
                    {selectedFolderData?.length !== 0 && (
                      <Box className="folder-all-action">
                        <Box className="folder-action-tooltip">
                          <Box className="action-section selected-category">
                            <CancelIcon
                              onClick={() => {
                                setSelectedFolderData([]);
                              }}
                            />
                            <Typography className="body-text">
                              {selectedFolderData?.length + ' selected'}
                            </Typography>
                          </Box>

                          {selectedFolderData?.length > 0 &&
                            !selectedFolderData.some(
                              (id) =>
                                empContractList?.find((item) => item.id === id)
                                  ?.status === 'active'
                            ) && (
                              <Box className="action-section">
                                <Tooltip
                                  title={
                                    <Typography className="sub-title-text">
                                      Active
                                    </Typography>
                                  }
                                  classes={{
                                    tooltip: 'info-tooltip-container',
                                  }}
                                  arrow
                                >
                                  <VisibilityOutlinedIcon
                                    onClick={() =>
                                      handleOptionManage({
                                        id: 1,
                                        name: 'Active',
                                        value: 'active',
                                      })
                                    }
                                  />
                                </Tooltip>
                              </Box>
                            )}

                          {selectedFolderData?.length > 0 &&
                            !selectedFolderData.some(
                              (id) =>
                                empContractList?.find((item) => item.id === id)
                                  ?.status === 'inactive'
                            ) && (
                              <Box className="action-section">
                                <Tooltip
                                  title={
                                    <Typography className="sub-title-text">
                                      Deactive
                                    </Typography>
                                  }
                                  classes={{
                                    tooltip: 'info-tooltip-container',
                                  }}
                                  arrow
                                >
                                  <VisibilityOffOutlinedIcon
                                    onClick={() =>
                                      handleOptionManage({
                                        id: 2,
                                        name: 'Deactive',
                                        value: 'inactive',
                                      })
                                    }
                                  />
                                </Tooltip>
                              </Box>
                            )}

                          <Box className="action-section">
                            <Tooltip
                              title={
                                <Typography className="sub-title-text">
                                  Delete
                                </Typography>
                              }
                              classes={{
                                tooltip: 'info-tooltip-container',
                              }}
                              arrow
                            >
                              <DeleteOutlineIcon
                                onClick={() =>
                                  handleOptionManage({
                                    id: 3,
                                    name: 'Delete',
                                    value: 'deleted',
                                  })
                                }
                              />
                            </Tooltip>
                          </Box>
                        </Box>
                      </Box>
                    )}
                  </Box>
                  {isViewType === 'grid' && empContractList?.length === 0 && (
                    <Box className="">
                      <NoDataView
                        title="No Contract Found"
                        description="There is no Contract available at the moment."
                      />
                    </Box>
                  )}
                  {/* GRID VIEW */}
                  {filteredSideBarMenu?.map((menu, index) => {
                    const groupedData = _.groupBy(
                      empContractList,
                      (item) => item?.emp_contract_category?.name
                    );

                    const folderDataList = groupedData?.[menu?.name] || [];

                    return (
                      isViewType === 'grid' &&
                      folderDataList?.length !== 0 && (
                        <>
                          {menu?.id !== selectedSideBarData?.id && (
                            <Typography
                              className="title-sm fw600 pb16"
                              style={{
                                paddingTop: index === 0 ? '0px' : '16px',
                              }}
                            >
                              {menu?.name}
                            </Typography>
                          )}

                          {folderDataList?.length === 0 ? (
                            <Box className="">
                              <NoDataView
                                title="No Contract Found"
                                description="There is no Contract available at the moment."
                              />
                            </Box>
                          ) : isViewType === 'grid' ? (
                            <Box className="emp-contract-folder-sec">
                              {folderDataList?.map((emp, index) => {
                                return (
                                  <Box key={index}>
                                    <FolderGridViews
                                      folderData={emp}
                                      menuList={options}
                                      selectedFolderData={selectedFolderData}
                                      setSelectedFolderData={
                                        setSelectedFolderData
                                      }
                                      handleOptionManage={handleOptionManage}
                                      handleView={handleContractView}
                                    />
                                  </Box>
                                );
                              })}
                            </Box>
                          ) : null}
                        </>
                      )
                    );
                  })}
                  {/* LIST VIEW */}
                  {isViewType === 'list' && empContractList?.length === 0 ? (
                    <Box className="">
                      <NoDataView
                        title="No Contract Found"
                        description="There is no Contract available at the moment."
                      />
                    </Box>
                  ) : isViewType === 'list' ? (
                    <FolderListViews
                      folderData={empContractList}
                      menuList={options}
                      selectedFolderData={selectedFolderData}
                      setSelectedFolderData={setSelectedFolderData}
                      handleOptionManage={handleOptionManage}
                      handleView={handleContractView}
                    />
                  ) : null}
                </>
              )}
            </Box>
          </Box>
        </Box>
      </Box>
      <DialogBox
        open={moveToogleModal}
        handleClose={() => {
          setMoveToogleModal(!moveToogleModal);
          setMoveDetails('');
        }}
        title={`Move ${moveDetails?.name}`}
        className="staff-dialogbox"
        content={
          <>
            <Box className="mb8">
              <Typography className="body-text">
                Current Location :{' '}
                <span className="fw600">{moveDetails?.cat_name}</span>
              </Typography>
            </Box>
            {/* <Divider /> */}
            <Box className="mt8">
              <Typography className="body-text fw600 mb16">
                Move to :
              </Typography>

              {sideBarMenu
                ?.filter?.(
                  (obj) => obj?.id !== 'all' && obj?.id !== moveDetails?.cat_id
                )
                ?.map((item, index) => {
                  return (
                    <Box className="move-to-list" key={index}>
                      <Typography className="body-text">
                        {item?.name}
                      </Typography>
                      <Typography
                        className="title-text move-btn"
                        onClick={() => handleMove(item?.id)}
                      >
                        Move
                      </Typography>
                    </Box>
                  );
                })}
            </Box>
          </>
        }
      />
      <RightDrawer
        anchor={'right'}
        open={assignEmployee}
        onClose={() => {
          setAssignEmployee(false);
        }}
        title={'Assign Employee'}
        subTitle={assignEmployee?.title}
        className="assign-employee-drawer"
        content={
          <>
            <AssignEmployee
              setClose={setAssignEmployee}
              AssignEmployees={AssignEmployees}
              assignId={[assignEmployee?.id]}
              SelectedID={
                assignEmployee?.assigntype === 'general'
                  ? assignEmployee?.general_user_id?.toString()
                  : assignEmployee?.assigntype === 'jobrole'
                    ? assignEmployee?.department_user_id?.toString()
                    : assignEmployee?.additional_user_id?.toString()
              }
              isJobRole={
                assignEmployee?.assigntype !== 'additionduties' &&
                assignEmployee
              }
              empcontract={assignEmployee}
              isFromEmp={true}
              removeDelete={
                assignEmployee?.assigntype === 'jobrole' ||
                assignEmployee?.assigntype === 'general'
              }
            />
          </>
        }
      />
      <DialogBox
        open={deleteDialogOpen}
        handleClose={handleCloseDeleteDialog}
        title="Confirmation"
        className="delete-modal"
        dividerClass="delete-modal-divider"
        content={
          <DeleteModal
            handleCancel={handleCloseDeleteDialog}
            handleConfirm={() => handleStatus(deleteId?.value, deleteId?.id)}
            text="Are you sure you want to delete this contract?"
          />
        }
      />
    </>
  );
}
