import React from 'react';
import { Box, Tooltip, CircularProgress, Typography } from '@mui/material';
// import BranchFilter from '../BranchFilter';
import DateFilter from '../DateFilter';
import CategoryFilter from '../CategoryFilter';
import TimePeriodFilter from '../TimePeriodsFilter';
import CloseIcon from '@mui/icons-material/Close';
import DSRTable from '../ReportTable/dsrTable';
import DoneOutlinedIcon from '@mui/icons-material/DoneOutlined';
import ToggleOffIcon from '@mui/icons-material/ToggleOff';
import ToggleOnIcon from '@mui/icons-material/ToggleOn';
import CustomMonthRangePicker from '@/components/UI/ReactMonthRangePicker';
import CustomButton from '@/components/UI/CustomButton';
import moment from 'moment';
import NoDataView from '@/components/UI/NoDataView';
import MultipleFilter from '@/components/UI/MultipleFilter';
import './generalpage.scss';

export default function GeneralPage({
  dsrCatData,
  // setDsrCatData,
  branchList,
  // setBranchList,
  selectedBranches,
  setSelectedBranches,
  selectedOption,
  setSelectedOption,
  customMonthGEN,
  setCustomMonthGEN,
  selectedMainCategories,
  setSelectedMainCategories,
  TimePeriodList,
  // setTimePeriodList,
  selectedSubcategories,
  setSelectedSubcategories,
  // dateCustomFilterGEN,
  dateCustomFilterListGEN,
  dateCustSelOptionGEN,
  setDateCustSelOptionGEN,
  loader,
  ActiveCate,
  setActiveCate,
  getCategoriesPaymentList,
  GeneralData,
  getGeneralReportFilter,
  setAppliedFilterGEN,
  AppliedFilterGEN,
  isBM,
  authState,
}) {
  const sameArray = (array1, array2) => {
    return (
      array1.length === array2.length &&
      [...array1]
        .sort()
        .every((value, index) => value === [...array2].sort()[index])
    );
  };
  // Applies all selected filters
  const handleApplyFilter = () => {
    const filterData = {
      dateCustSelOptionGEN,
      customMonthGEN: customMonthGEN,
      selectedBranches,
      selectedMainCategories,
      selectedSubcategories,
      selectedOption,
      isGroup: AppliedFilterGEN?.isGroup,
    };
    setAppliedFilterGEN(filterData);
    getGeneralReportFilter(
      filterData,
      sameArray(
        AppliedFilterGEN?.selectedSubcategories,
        selectedSubcategories
      ) &&
        GeneralData?.data?.length > 0 &&
        AppliedFilterGEN?.isGroup
        ? GeneralData?.columns_group
        : null
    );
  };
  // Resets all filters to default values
  const handleCancelFilter = () => {
    setDateCustSelOptionGEN('current_month');
    setCustomMonthGEN({
      startMonth: null,
      endMonth: null,
    });
    setSelectedBranches(isBM ? [authState?.branch?.id] : []);
    setSelectedMainCategories([]);
    setSelectedSubcategories([]);
    setSelectedOption('none');
    const filterData = {
      dateCustSelOptionGEN: 'current_month',
      customMonthGEN: '',
      selectedBranches: isBM ? [authState?.branch?.id] : [],
      selectedMainCategories: [],
      selectedSubcategories: [],
      selectedOption: '',
    };
    setAppliedFilterGEN(filterData);
    getGeneralReportFilter(filterData);
  };
  // Applies date filter with selected date options
  const handleDateApplyFilter = () => {
    const filterData = {
      dateCustSelOptionGEN,
      customMonthGEN: customMonthGEN,
      selectedBranches,
      selectedMainCategories,
      selectedSubcategories,
      selectedOption,
      isGroup: AppliedFilterGEN?.isGroup,
    };
    setAppliedFilterGEN(filterData);
    getGeneralReportFilter(
      filterData,
      sameArray(
        AppliedFilterGEN?.selectedSubcategories,
        selectedSubcategories
      ) &&
        GeneralData?.data?.length > 0 &&
        AppliedFilterGEN?.isGroup
        ? GeneralData?.columns_group
        : null
    );
  };
  // Resets the date filter to default values
  const handleDateCancelFilter = () => {
    // setDateCustSelOptionGEN('current_month');
    setCustomMonthGEN({
      startMonth: null,
      endMonth: null,
    });
  };

  const getDateByfilter = () => {
    if (dateCustSelOptionGEN === 'current_month') {
      return moment().startOf('month').format('MMM YYYY');
    } else if (dateCustSelOptionGEN === 'previous_month') {
      return moment().subtract(1, 'month').startOf('month').format('MMM YYYY');
    } else if (dateCustSelOptionGEN === '1st_quarter') {
      const start = moment().quarter(1).startOf('quarter').format('MMM YYYY');
      const end = moment().quarter(1).endOf('quarter').format('MMM YYYY');
      return `${start} - ${end}`;
    } else if (dateCustSelOptionGEN === '2nd_quarter') {
      const start = moment().quarter(2).startOf('quarter').format('MMM YYYY');
      const end = moment().quarter(2).endOf('quarter').format('MMM YYYY');
      return `${start} - ${end}`;
    } else if (dateCustSelOptionGEN === '3rd_quarter') {
      const start = moment().quarter(3).startOf('quarter').format('MMM YYYY');
      const end = moment().quarter(3).endOf('quarter').format('MMM YYYY');
      return `${start} - ${end}`;
    } else if (dateCustSelOptionGEN === '4th_quarter') {
      const start = moment().quarter(4).startOf('quarter').format('MMM YYYY');
      const end = moment().quarter(4).endOf('quarter').format('MMM YYYY');
      return `${start} - ${end}`;
    } else if (dateCustSelOptionGEN === 'last_4_months') {
      const start = moment()
        .subtract(4, 'months')
        .startOf('month')
        .format('MMM YYYY');
      const end = moment()
        .subtract(1, 'month')
        .endOf('month')
        .format('MMM YYYY');
      return `${start} - ${end}`;
    } else if (dateCustSelOptionGEN === 'current_year') {
      const start = moment().startOf('year').format('MMM YYYY');
      const end = moment().endOf('year').format('MMM YYYY');
      return `${start} - ${end}`;
    } else if (dateCustSelOptionGEN === 'previous_year') {
      const start = moment()
        .subtract(1, 'year')
        .startOf('year')
        .format('MMM YYYY');
      const end = moment().subtract(1, 'year').endOf('year').format('MMM YYYY');
      return `${start} - ${end}`;
    } else if (dateCustSelOptionGEN === 'last_2_years') {
      const start = moment()
        .subtract(2, 'years')
        .startOf('year')
        .format('MMM YYYY');
      const end = moment().subtract(1, 'year').endOf('year').format('MMM YYYY');
      return `${start} - ${end}`;
    } else {
      return '';
    }
  };
  return (
    <Box>
      <Box className="filter-container">
        <Box
          className="subcategories-wrap"
          width="75%"
          sx={{ paddingLeft: '20px' }}
        >
          {/* Custom filter */}

          <DateFilter
            dateSelectedOption={dateCustSelOptionGEN}
            setDateSelectedOption={setDateCustSelOptionGEN}
            dateFilterList={dateCustomFilterListGEN}
            customStartDate={customMonthGEN}
            setCustomStartDate={setCustomMonthGEN}
            type="customGen"
          />

          {/* Branch Filter */}
          <MultipleFilter
            selected={selectedBranches}
            setSelected={setSelectedBranches}
            List={branchList}
            placeholder="Branches"
            disabled={isBM}
          />

          <Box className="category-section">
            <CustomButton
              variant="outlined"
              isIconOnly
              startIcon={
                <Tooltip
                  title={
                    <Typography className="sub-title-text">
                      {ActiveCate ? 'All Categories' : 'Only Active Categories'}
                    </Typography>
                  }
                  classes={{
                    tooltip: 'info-tooltip-container',
                  }}
                  arrow
                >
                  {ActiveCate ? <ToggleOnIcon /> : <ToggleOffIcon />}
                </Tooltip>
              }
              onClick={() => {
                setActiveCate(!ActiveCate);
                getCategoriesPaymentList(
                  !ActiveCate,
                  dateCustSelOptionGEN,
                  '',
                  '',
                  isBM ? authState?.branch?.id : ''
                );
              }}
            />
            {/* Category Filter */}
            <CategoryFilter
              selectedMainCategories={selectedMainCategories}
              selectedSubcategories={selectedSubcategories}
              dsrCatData={dsrCatData}
              setSelectedMainCategories={setSelectedMainCategories}
              setSelectedSubcategories={setSelectedSubcategories}
            />
          </Box>

          {/* TimePeriod Filter */}
          <TimePeriodFilter
            selectedOption={selectedOption}
            setSelectedOption={setSelectedOption}
            TimePeriodList={TimePeriodList}
          />
          <Box className="d-flex gap-5">
            <CustomButton
              variant="contained"
              onClick={handleApplyFilter}
              title="Apply"
              disabled={
                dateCustSelOptionGEN === 'custom'
                  ? customMonthGEN?.startMonth && customMonthGEN?.endMonth
                    ? false
                    : true
                  : false
              }
            />
            <CustomButton
              variant="outlined"
              onClick={handleCancelFilter}
              title="Clear"
            />
          </Box>
        </Box>
      </Box>
      {getDateByfilter() ? (
        <span className="title-text">{getDateByfilter()}</span>
      ) : (
        <></>
      )}

      {/* {dateCustSelOptionGEN === 'custom' && (
        <>
          <Box className="dates-wrap">
            {customMonthGEN && (
              <>
                <Typography variant="h6" className="show-date-wrap">
                  {formatDateForComparison(customMonthGEN) ===
                  formatDateForComparison(customEndDate)
                    ? formatDate(customMonthGEN)
                    : `${formatDate(customMonthGEN)}${
                        customEndDate ? ` - ${formatDate(customEndDate)}` : ''
                      }`}
                </Typography>
              </>
            )}
          </Box>
        </>
      )} */}
      {dateCustSelOptionGEN === 'custom' && (
        <>
          <Box className="custom-date-fields d-flex gap-5">
            <Box>
              <CustomMonthRangePicker
                label={<span>Month Range</span>}
                name="date"
                value={customMonthGEN}
                // disabled={checkloader}
                disableFuture={true}
                onChange={(date) => {
                  setCustomMonthGEN({
                    startMonth: date?.startMonth,
                    endMonth: date?.endMonth,
                  });
                }}
                inputVariant="outlined"
                isClearable={false}
                showWeekNumbers={true}
                showWeekPicker={true}
                // maxDate={getEndOfCurrentWeek()}
              />
            </Box>

            <Box className="d-flex gap-5">
              <CustomButton
                variant="contained"
                isIconOnly
                startIcon={
                  <Tooltip
                    title={
                      <Typography className="sub-title-text">
                        Apply Filter
                      </Typography>
                    }
                    classes={{
                      tooltip: 'info-tooltip-container',
                    }}
                    arrow
                  >
                    <DoneOutlinedIcon sx={{ fontWeight: 700 }} />
                  </Tooltip>
                }
                onClick={() => {
                  customMonthGEN?.startMonth &&
                    customMonthGEN?.endMonth &&
                    handleDateApplyFilter();
                }}
              />
              <CustomButton
                variant="outlined"
                isIconOnly
                startIcon={
                  <Tooltip
                    title={
                      <Typography className="sub-title-text">Cancel</Typography>
                    }
                    classes={{
                      tooltip: 'info-tooltip-container',
                    }}
                    arrow
                  >
                    <CloseIcon sx={{ fontWeight: 700 }} />
                  </Tooltip>
                }
                onClick={handleDateCancelFilter}
              />
            </Box>
          </Box>
        </>
      )}
      <Box className="pt24">
        {loader || GeneralData?.data === undefined ? (
          <Box className="content-loader">
            <CircularProgress className="loader" color="inherit" />
          </Box>
        ) : GeneralData &&
          GeneralData?.data &&
          GeneralData?.data?.length > 0 &&
          GeneralData?.column &&
          Object.keys(GeneralData?.column).length > 1 ? (
          <DSRTable dsrWsrData={GeneralData} />
        ) : (
          <Box className="no-data d-flex align-center justify-center">
            <NoDataView
              title="No General Reports Found"
              description="There is no general reports available at the moment."
            />
          </Box>
        )}
      </Box>
    </Box>
  );
}
