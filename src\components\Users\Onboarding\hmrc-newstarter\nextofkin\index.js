import { Box, Divider } from '@mui/material';
import CustomTextField from '@/components/UI/CustomTextField';

export default function NextOfkin({
  touched,
  errors,
  values,
  handleChange,
  // setFieldValue,
  handleBlur,
  ViewAccessOnly,
}) {
  return (
    <Box>
      <Box className="display-grid pt16">
        <Box>
          <CustomTextField
            fullWidth
            id="nk_name1"
            name="nk_name1"
            value={values?.nk_name1}
            label="Name"
            placeholder="Enter name"
            error={Boolean(touched.nk_name1 && errors.nk_name1)}
            helperText={touched.nk_name1 && errors.nk_name1}
            onBlur={handleBlur}
            onChange={handleChange}
            disabled={ViewAccessOnly}
          />
        </Box>
        <Box>
          <CustomTextField
            fullWidth
            id="nk_relationship1"
            name="nk_relationship1"
            value={values?.nk_relationship1}
            label="Relationship"
            placeholder="Enter Relationship"
            error={<PERSON><PERSON>an(touched.nk_relationship1 && errors.nk_relationship1)}
            helperText={touched.nk_relationship1 && errors.nk_relationship1}
            onBlur={handleBlur}
            onChange={handleChange}
            disabled={ViewAccessOnly}
          />
        </Box>
        <Box className="phone-number">
          <CustomTextField
            fullWidth
            id="nk_phoneNo1"
            name="nk_phoneNo1"
            value={values?.nk_phoneNo1}
            label="Phone number"
            placeholder="Enter Phone number"
            error={Boolean(touched.nk_phoneNo1 && errors.nk_phoneNo1)}
            helperText={touched.nk_phoneNo1 && errors.nk_phoneNo1}
            onBlur={handleBlur}
            disabled={ViewAccessOnly}
            onChange={(e) => {
              if (e.target.value === '' || e.target.value?.length < 12) {
                handleChange(e);
              }
            }}
            onInput={(e) => {
              e.target.value = e.target.value.replace(/[^0-9]/g, ''); // Remove non-numeric characters
            }}
          />
        </Box>
      </Box>
      <Box className="display-grid-50 pt8">
        <CustomTextField
          fullWidth
          id="nk_address1"
          name="nk_address1"
          value={values?.nk_address1}
          label="Address"
          placeholder="Enter Address"
          error={Boolean(touched.nk_address1 && errors.nk_address1)}
          helperText={touched.nk_address1 && errors.nk_address1}
          onBlur={handleBlur}
          onChange={handleChange}
          disabled={ViewAccessOnly}
        />
      </Box>
      <Divider className="mb16 mt16" />
      <Box className="display-grid">
        <Box>
          <CustomTextField
            fullWidth
            id="nk_name2"
            name="nk_name2"
            value={values?.nk_name2}
            label="Name"
            placeholder="Enter name"
            error={Boolean(touched.nk_name2 && errors.nk_name2)}
            helperText={touched.nk_name2 && errors.nk_name2}
            onBlur={handleBlur}
            onChange={handleChange}
            disabled={ViewAccessOnly}
          />
        </Box>
        <Box>
          <CustomTextField
            fullWidth
            id="nk_relationship2"
            name="nk_relationship2"
            value={values?.nk_relationship2}
            label="Relationship"
            placeholder="Enter Relationship"
            error={Boolean(touched.nk_relationship2 && errors.nk_relationship2)}
            helperText={touched.nk_relationship2 && errors.nk_relationship2}
            onBlur={handleBlur}
            onChange={handleChange}
            disabled={ViewAccessOnly}
          />
        </Box>
        <Box className="phone-number">
          <CustomTextField
            fullWidth
            id="nk_phoneNo2"
            name="nk_phoneNo2"
            value={values?.nk_phoneNo2}
            label="Phone number"
            placeholder="Enter Phone number"
            error={Boolean(touched.nk_phoneNo2 && errors.nk_phoneNo2)}
            helperText={touched.nk_phoneNo2 && errors.nk_phoneNo2}
            onBlur={handleBlur}
            disabled={ViewAccessOnly}
            onChange={(e) => {
              if (e.target.value === '' || e.target.value?.length < 12) {
                handleChange(e);
              }
            }}
            onInput={(e) => {
              e.target.value = e.target.value.replace(/[^0-9]/g, ''); // Remove non-numeric characters
            }}
          />
        </Box>
      </Box>
      <Box className="display-grid-50 pt8">
        <CustomTextField
          fullWidth
          id="nk_address2"
          name="nk_address2"
          value={values?.nk_address2}
          label="Address"
          placeholder="Enter Address"
          error={Boolean(touched.nk_address2 && errors.nk_address2)}
          helperText={touched.nk_address2 && errors.nk_address2}
          onBlur={handleBlur}
          onChange={handleChange}
          disabled={ViewAccessOnly}
        />
      </Box>
    </Box>
  );
}
