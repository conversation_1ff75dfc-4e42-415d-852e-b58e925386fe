import React, { useState } from 'react';
import { Box, Typography } from '@mui/material';
import InsertLinkIcon from '@mui/icons-material/InsertLink';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import CustomButton from '@/components/UI/CustomButton';

function Container({
  block,
  blockIndex,
  setDSRReportsData,
  RemoveGroupBlocks,
  toggleSelectBlock,
  selectedIds,
}) {
  return (
    <>
      <Box>
        {block?.children &&
          block?.children.map((childBlock, index) => {
            return (
              <BlockWrapper
                key={childBlock.id}
                block={childBlock}
                blockIndex={[...blockIndex, index]}
                setDSRReportsData={setDSRReportsData}
                RemoveGroupBlocks={RemoveGroupBlocks}
                toggleSelectBlock={toggleSelectBlock}
                selectedIds={selectedIds}
              />
            );
          })}
      </Box>
    </>
  );
}
function BlockWrapper({
  block,
  blockIndex,
  setDSRReportsData,
  toggleSelectBlock,
  selectedIds,
  isMainItem,
  RemoveGroupBlocks,
}) {
  if (!block) return null;
  if (block?.type === 'Group') {
    return (
      <Box
        className={
          block.type === 'Group'
            ? 'container-block group-container'
            : 'text-block group-container'
        }
      >
        <Typography className="title-text fw600">{block?.content}</Typography>
        <Container
          block={block}
          setDSRReportsData={setDSRReportsData}
          blockIndex={blockIndex}
          RemoveGroupBlocks={RemoveGroupBlocks}
          isMainItem={isMainItem}
          toggleSelectBlock={toggleSelectBlock}
          selectedIds={selectedIds}
        />
      </Box>
    );
  } else {
    return (
      <Box
        className={
          selectedIds?.includes(block?.id)
            ? 'column-container selected-column'
            : 'column-container'
        }
        onClick={() => toggleSelectBlock(block?.id)}
      >
        {selectedIds?.includes(block?.id) && <CheckBoxIcon />}
        <Typography className="title-text">{block?.content}</Typography>
      </Box>
    );
  }
}
const TotalGroups = ({ onClose }) => {
  const [DSRReportsData, setDSRReportsData] = useState([]);
  const [selectedIds, setSelectedIds] = useState([]);

  const toggleSelectBlock = (id) => {
    setSelectedIds((prevSelected) =>
      prevSelected.includes(id)
        ? prevSelected.filter((selId) => selId !== id)
        : [...prevSelected, id]
    );
  };

  const RemoveGroupBlocks = (id, index) => {
    const removedData = DSRReportsData.map((group, groupIndex) =>
      groupIndex === index[0]
        ? {
            ...group,
            children: group?.children?.filter(
              (_, childIndex) => childIndex !== index[1]
            ),
          }
        : group
    );
    // Directly access the child item
    const newObject = DSRReportsData[index[0]]?.children?.[index[1]];
    const updatedArray = addObjectAtIndex(removedData, index[0] + 1, newObject);
    setDSRReportsData(updatedArray);
  };

  function findIndexInNestedArray(data, idToFind) {
    for (let parentIndex = 0; parentIndex < data.length; parentIndex++) {
      const block = data[parentIndex];
      if (block.id === idToFind) {
        return { parentIndex, childIndex: null }; // Found at the parent level
      }

      if (block.children) {
        const childIndex = block.children.findIndex(
          (child) => child.id === idToFind
        );
        if (childIndex !== -1) {
          return { parentIndex, childIndex }; // Found in the children array
        }
      }
    }

    return null; // Not found
  }

  function findParentIds(data, selectedIds) {
    const parentIdsSet = new Set();

    for (const idToFind of selectedIds) {
      for (const block of data) {
        if (block.id === idToFind && block.parent_id !== null) {
          parentIdsSet.add(block.parent_id);
          break; // Stop searching once the block is found
        }

        if (block.children?.some((child) => child.id === idToFind)) {
          const child = block.children.find((child) => child.id === idToFind);
          if (child?.parent_id !== null) {
            parentIdsSet.add(child.parent_id);
          }
          break; // Stop searching once the child is found
        }
      }
    }

    return Array.from(parentIdsSet); // Convert Set to Array
  }

  function addObjectAtIndex(array, index, newObject) {
    const newArray = [...array];
    newArray.splice(index, 0, newObject);
    return newArray;
  }

  const onChangeCreateTotal = () => {
    const dsrData = DSRReportsData;
    const selectedLastId = selectedIds[selectedIds.length - 1];

    const filterData = dsrData.filter((block) =>
      selectedIds.includes(block.id)
    );
    const uniqueParentIds = findParentIds(dsrData, selectedIds);
    if (filterData.length > 0 || uniqueParentIds.length > 1) {
      const findIndex = findIndexInNestedArray(dsrData, selectedLastId);

      const newObject = {
        Ids: selectedIds,
        content: `Total `,
        parent_id: null,
        type: 'text',
      };

      const updatedArray = addObjectAtIndex(
        dsrData,
        findIndex.parentIndex + 1,
        newObject
      );
      setDSRReportsData(updatedArray);
    } else {
      const findIndex = findIndexInNestedArray(dsrData, selectedLastId);

      const newObject = {
        Ids: selectedIds,
        content: `Total `,
        parent_id: dsrData[findIndex.parentIndex]?.id,
        width: 2,
        type: 'text',
      };

      const updatedChildren = addObjectAtIndex(
        dsrData[findIndex.parentIndex]?.children || [],
        findIndex.childIndex + 1,
        newObject
      );

      const updatedData = [...dsrData];
      updatedData[findIndex.parentIndex] = {
        ...updatedData[findIndex.parentIndex],
        children: updatedChildren,
      };

      setDSRReportsData(updatedData);
    }

    setSelectedIds([]);
  };
  // useEffect(() => {
  //   if (DSRReportsData && DSRReportsData?.length > 0) {
  //     const filterGrp = DSRReportsData?.filter((f) => f?.type === 'Group');
  //     setDSRReportsGroupData(filterGrp);
  //   }
  // }, [DSRReportsData]);
  return (
    <Box className="grouping-reorder-section">
      <Box className="group-options">
        <Box
          className={
            selectedIds && selectedIds?.length > 0
              ? 'group-selection-section'
              : 'group-selection-section disabled-selection'
          }
          onClick={() => {
            selectedIds && selectedIds?.length > 0 && onChangeCreateTotal();
          }}
        >
          <InsertLinkIcon />
          <Typography className="title-text fw600">Total Selection</Typography>
        </Box>
      </Box>

      <Box>
        {DSRReportsData?.map((block, blockIndex) => (
          <BlockWrapper
            key={block.id}
            block={block}
            blockIndex={[blockIndex]}
            setDSRReportsData={setDSRReportsData}
            toggleSelectBlock={toggleSelectBlock}
            selectedIds={selectedIds}
            isMainItem={true}
            RemoveGroupBlocks={RemoveGroupBlocks}
          />
        ))}
      </Box>

      <Box className="create-cancel-button pt32 pb16 justify-center">
        <CustomButton
          fullWidth
          variant="outlined"
          title="Cancel"
          onClick={() => {
            onClose();
          }}
        />
        <CustomButton
          fullWidth
          variant="contained"
          title={'Save'}
          onClick={() => {
            onClose();
          }}
        />
      </Box>
    </Box>
  );
};

export default TotalGroups;
