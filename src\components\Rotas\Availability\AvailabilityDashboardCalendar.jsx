'use client';

import React, { useRef, useState } from 'react';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import moment from 'moment';
import { AvailabilityShiftMenu } from '@/components/UI/CalendarViews/ShiftMenu';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import { AvailabilityComponent } from '@/components/UI/CalendarViews/ShiftCell';
import { ROTA_URLS } from '@/helper/constants/urls';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { setApiMessage } from '@/helper/common/commonFunctions';
import dayjs from 'dayjs';
import PreLoader from '@/components/UI/Loader';
import DialogBox from '@/components/UI/Modalbox';
import DeleteModal from '@/components/UI/DeleteModal';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import '@/components/UI/CalendarViews/calendarView.scss';

const AvailabilityDashboardCalendar = ({
  filterData,
  availabilityList,
  getDashboardAvailabilityList,
}) => {
  const filteredEvents = availabilityList?.length > 0 ? availabilityList : [];
  const [hoveredDate, setHoveredDate] = useState(null);
  const [isOpenMenu, setIsOpenMenu] = useState(false);
  const [addAvailabilityTooltip, setAddAvailabilityTooltip] = useState(false);
  const [addAvailabilityAnchorEl, setAddAvailabilityAnchorEl] = useState(null);
  const [singleAvailabilityData, setSingleAvailabilityData] = useState(null);
  const [currentCellDate, setCurrentCellDate] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [deleteAvailabilityModal, setDeleteAvailabilityModal] = useState(false);
  const calendarCellRefs = useRef({});

  const handleAddAvailabilityModalOpen = (
    event,
    availabilityData,
    date,
    cellId
  ) => {
    setAddAvailabilityTooltip(true);
    setAddAvailabilityAnchorEl(calendarCellRefs.current[cellId]);
    setIsOpenMenu(false);
    setSingleAvailabilityData(availabilityData);
    setCurrentCellDate(date);
  };

  const handleAddAvailabilityModalClose = () => {
    setAddAvailabilityTooltip(false);
    setAddAvailabilityAnchorEl(null);
    setIsOpenMenu(false);
  };

  const onOpenMenu = () => {
    setIsOpenMenu(true);
  };
  const onCloseMenu = () => {
    setIsOpenMenu(false);
  };

  const handleAvailabilityOnOff = async (
    availabilityType,
    isPresentEvent,
    eventDate
  ) => {
    const formatDate = eventDate ? dayjs(eventDate).format('YYYY-MM-DD') : '';

    const sendData = {
      userId: filterData?.selectedUser,
      date: formatDate,
      timeZone: null,
      available: availabilityType === 'available' ? true : false,
      type: 'full',
      status: 'active',
    };
    if (isPresentEvent) {
      try {
        const { status, data } = await axiosInstance.put(
          ROTA_URLS?.AVAILABILITY_URL + `/${isPresentEvent?.id}`,
          sendData
        );
        if (status === 200 || status === 201) {
          setApiMessage('success', data?.message);
          setIsLoading(false);
          handleAddAvailabilityModalClose();
          getDashboardAvailabilityList();
          setIsLoading(false);
        }
      } catch (error) {
        setApiMessage('error', error?.response?.data?.message);
        setIsLoading(false);
      }
    } else {
      try {
        const { status, data } = await axiosInstance.post(
          ROTA_URLS?.AVAILABILITY_URL,
          sendData
        );
        if (status === 200 || status === 201) {
          setApiMessage('success', data?.message);
          setIsLoading(false);
          handleAddAvailabilityModalClose();
          getDashboardAvailabilityList();
          setIsLoading(false);
        }
      } catch (error) {
        setApiMessage('error', error?.response?.data?.message);
        setIsLoading(false);
      }
    }
  };

  const handleDeleteAvailabilityModalOpen = (availabilityData) => {
    setSingleAvailabilityData(availabilityData);
    setIsOpenMenu(false);
    setAddAvailabilityTooltip(false);
    setAddAvailabilityAnchorEl(false);
    setDeleteAvailabilityModal(true);
  };

  const handleDeleteAvailabilityModalClose = () => {
    setDeleteAvailabilityModal(false);
  };

  // handle delete shift
  const handleDeleteAvailability = async () => {
    setIsLoading(true);
    try {
      const { status, data } = await axiosInstance.delete(
        ROTA_URLS?.AVAILABILITY_URL + `/${singleAvailabilityData?.id}`
      );
      if (status === 200 || status === 201) {
        setApiMessage('success', data?.message);
        setIsLoading(false);
        getDashboardAvailabilityList();
        handleDeleteAvailabilityModalClose();
        setIsLoading(false);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setIsLoading(false);
    }
  };

  // render day availability event
  const renderDashboardDayAvailabilityContent = (eventInfo) => {
    const dateKey = moment(eventInfo.date).format('YYYY-MM-DD');
    const cellId = `availability-cell-${dateKey}`;

    // Filter events for the given date
    const presentEvent =
      filteredEvents?.find((event) =>
        moment(event.start).isSame(dateKey, 'day')
      ) || null;

    return (
      <>
        <div
          ref={(el) => (calendarCellRefs.current[cellId] = el)}
          id={cellId}
          className="availability-calendar-shifts-day-wrap"
          onMouseEnter={() => setHoveredDate(eventInfo?.date)}
          onMouseLeave={() => setHoveredDate(null)}
        >
          <div className="availability-calendar-shifts-header">
            <div className="fc-daygrid-day-number-text">
              {eventInfo?.dayNumberText}
            </div>
            {hoveredDate &&
              hoveredDate?.getTime() === eventInfo?.date?.getTime() && (
                <div className="availability-calendar-shifts-menu">
                  <AvailabilityShiftMenu
                    isOpen={isOpenMenu}
                    onOpen={onOpenMenu}
                    onClose={onCloseMenu}
                    presentEvent={presentEvent}
                    handleAddAvailabilityModalOpen={(e) => {
                      handleAddAvailabilityModalOpen(
                        e,
                        presentEvent,
                        eventInfo?.date,
                        cellId
                      );
                    }}
                    handleDeleteAvailabilityModalOpen={
                      handleDeleteAvailabilityModalOpen
                    }
                    handleAvailabilityOnOff={handleAvailabilityOnOff}
                    eventInfo={eventInfo}
                  />
                </div>
              )}
          </div>
          <div
            className="availability-calendar-shifts-day-details"
            onClick={(e) =>
              presentEvent
                ? null
                : handleAddAvailabilityModalOpen(
                    e,
                    presentEvent,
                    eventInfo?.date,
                    cellId
                  )
            }
          >
            {presentEvent ? (
              <div className="availability-calendar-shifts-day-event-details">
                <div className="availability-event-details">
                  {presentEvent?.type === 'full' ? (
                    <div
                      className={`availability-event ${presentEvent?.available ? 'onDay' : 'offDay'}`}
                    >
                      {presentEvent?.available ? (
                        <CheckCircleOutlineIcon />
                      ) : (
                        <HighlightOffIcon />
                      )}
                      <span>All day</span>
                    </div>
                  ) : (
                    presentEvent?.timeZone?.map((item, i) => {
                      return (
                        <div
                          key={i}
                          className={`availability-event ${presentEvent?.available ? 'onDay' : 'offDay'}`}
                        >
                          <span>
                            {moment.utc(item?.startTime).format('HH:mm')} -{' '}
                            {moment.utc(item?.endTime).format('HH:mm')}
                          </span>
                        </div>
                      );
                    })
                  )}
                </div>
              </div>
            ) : (
              <div className="availability-calendar-shifts-day-no-event-details">
                <AddCircleOutlineIcon className="add-icon" />
              </div>
            )}
          </div>
        </div>
      </>
    );
  };

  return (
    <>
      {isLoading && <PreLoader />}
      <FullCalendar
        key={` ${filterData?.selectedUser} ${filterData?.currentDate}`}
        plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
        initialView="dayGridMonth"
        initialDate={filterData?.currentDate || moment().format('YYYY-MM-DD')}
        nowIndicator={true}
        headerToolbar={false}
        events={filteredEvents}
        height="auto"
        dayCellContent={(arg) => renderDashboardDayAvailabilityContent(arg)}
        firstDay={1} // 0 = Sunday, 1 = Monday, 2 = Tuesday, 3 = Wednesday, 4 = Thursday, 5 = Friday, 6 = Saturday
        fixedWeekCount={false} // default value is 6 if set false will show depending on the month
        showNonCurrentDates={false} //  previous or next month dates will be hidden
      />

      {addAvailabilityTooltip && (
        <AvailabilityComponent
          addAvailabilityTooltip={addAvailabilityTooltip}
          addAvailabilityAnchorEl={addAvailabilityAnchorEl}
          handleAddAvailabilityModalClose={handleAddAvailabilityModalClose}
          singleAvailabilityData={singleAvailabilityData}
          currentCellDate={currentCellDate}
          filterData={filterData}
          getDashboardAvailabilityList={getDashboardAvailabilityList}
        />
      )}

      {deleteAvailabilityModal && (
        <DialogBox
          open={deleteAvailabilityModal}
          handleClose={() => {
            handleDeleteAvailabilityModalClose();
          }}
          title="Confirmation"
          className="delete-modal"
          dividerClass="delete-modal-divider"
          content={
            <>
              <DeleteModal
                handleCancel={handleDeleteAvailabilityModalClose}
                handleConfirm={handleDeleteAvailability}
                text="Are you sure you want to clear availability for day?"
                confirmText="Yes, Clear it"
              />
            </>
          }
        />
      )}
    </>
  );
};

export default AvailabilityDashboardCalendar;
