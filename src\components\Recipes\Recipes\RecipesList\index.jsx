'use client';
import React, { useState, useEffect, useContext } from 'react';
import AuthContext from '@/helper/authcontext';
import Icon from '@/components/UI/AppIcon/AppIcon';
import CustomSearch from '@/components/UI/CustomSearch';
import RecipeGrid from './components/RecipeGrid';
import FilterPanel from './components/FilterPanel';
import { Divider } from '@mui/material';
import { useRouter, usePathname, useParams } from 'next/navigation';
import InfiniteScroll from 'react-infinite-scroll-component';
import {
  getRecipeCategoryList,
  getPublicRecipeCategoryList,
  updateBookmark,
  duplicateRecipe,
  deleteRecipe,
  getRecipeList,
  getPublicRecipeList,
  assignRecipeToUsers,
  exportRecipe,
  getIngredientItemsList,
  getPublicIngredientItemsList,
  getPublicAllergenList,
  getPublicDietaryList,
  trackPrivateRecipeView,
} from '@/services/recipeService';
import {
  setApiMessage,
  isPublicRoute,
  getRouteFeatures,
  getRouteBasedClassName,
  conditionalApiCall,
  getRecipeListByRouteType,
} from '@/helper/common/commonFunctions';
import CustomButton from '@/components/UI/CustomButton';
import MultipleFilter from '@/components/UI/MultipleFilter';
import RightDrawer from '@/components/UI/RightDrawer';
import AssignEmployee from '@/components/Leave/AssignEmployee';
import CategoryScrollChips from './CategoryScrollChips';
import PreLoader from '@/components/UI/Loader';
import { removeFromStorage, saveToStorage } from '@/helper/context';
import { identifiers } from '@/helper/constants/identifier';
import './recipeslisting.scss';

const RecipesList = ({ orgName }) => {
  const router = useRouter();
  const pathname = usePathname();
  const { org_name } = useParams();
  const { authState } = useContext(AuthContext);
  const [recipesData, setRecipesData] = useState([]);
  const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(['all']);
  const [totalCount, setTotalCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  // const [isInitialized, setIsInitialized] = useState(false);
  const [apiCallInProgress, setApiCallInProgress] = useState(false);
  const [categories, setCategories] = useState([]);
  const [categoriesLoading, setCategoriesLoading] = useState(true);
  const [publicAllergens, setPublicAllergens] = useState([]); // For public route allergens
  const [publicDietary, setPublicDietary] = useState([]); // For public route dietary info

  // Infinite scroll state variables
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [rowsPerPage] = useState(12); // Fixed page size for infinite scroll

  const [filters, setFilters] = useState({
    allergens: [],
    costRange: [],
    dietary: [],
    difficulty: '',
    recipe_cook_time: '',
    ownership: '',
  });

  const [ingExcluded, setIngExcluded] = useState([]);
  const [ingIncluded, setIngIncluded] = useState([]);

  const [assignEmployee, setAssignEmployee] = useState(false);
  const [selectedAssignment, setSelectedAssignment] = useState(null);

  const [ingIncludedOpt, setIngIncludedOpt] = useState([]);
  const [ingIncExcludedOpt, setIngExcludedOpt] = useState([]);
  const [ingIncludedLabelMap, setIngIncludedLabelMap] = useState({});
  const [ingExcludedLabelMap, setIngExcludedLabelMap] = useState({});
  const [ingredientSearchIncluded, setIngredientSearchIncluded] = useState('');
  const [ingredientSearchExcluded, setIngredientSearchExcluded] = useState('');

  const [exportLoading, setExportLoading] = useState(false);

  const getRecipesPermission = authState?.UserPermission?.['recipe'];

  const isAllPermission = getRecipesPermission === 2;

  // Use utility function to determine route type
  const isPublic = isPublicRoute(pathname);

  // Get route-based features and permissions
  const routeFeatures = getRouteFeatures(pathname);

  // Function to extract unique allergens from public API response (fallback)
  const extractAllergensFromRecipes = (recipesList) => {
    const uniqueAllergens = new Set();
    const allergenMap = new Map();

    recipesList?.forEach((recipe) => {
      recipe?.allergens?.forEach((allergen) => {
        if (allergen?.id && !uniqueAllergens.has(allergen.id)) {
          uniqueAllergens.add(allergen.id);
          allergenMap.set(allergen.id, {
            id: allergen.id,
            value: allergen.id,
            label: allergen.attribute_title || allergen.label,
          });
        }
      });
    });

    return Array.from(allergenMap.values());
  };

  // API function to get allergens data
  const getAllergensData = async () => {
    try {
      // Use conditional API call utility function
      const response = await conditionalApiCall(
        pathname,
        async () => {
          // Private API call - return empty as FilterPanel handles this
          return [];
        },
        async () => {
          // Public API call
          const apiResponse = await getPublicAllergenList(
            '',
            '',
            { status: 'active' },
            '',
            { key: '', value: 'ASC' }
          );

          return (
            apiResponse?.allergens?.map((allergen) => ({
              id: allergen.id,
              value: allergen.id,
              label: allergen.attribute_title || allergen.label,
            })) || []
          );
        },
        // Fallback value
        []
      );

      setPublicAllergens(response);
    } catch (error) {
      console.error('Error fetching allergens:', error);
      setPublicAllergens([]);
    }
  };

  // API function to get dietary data
  const getDietaryData = async () => {
    try {
      // Use conditional API call utility function
      const response = await conditionalApiCall(
        pathname,
        async () => {
          // Private API call - return empty as FilterPanel handles this
          return [];
        },
        async () => {
          // Public API call
          const apiResponse = await getPublicDietaryList(
            '',
            '',
            { status: 'active' },
            '',
            { key: '', value: 'ASC' }
          );

          return (
            apiResponse?.dietary?.map((dietary) => ({
              id: dietary.id,
              value: dietary.id,
              label: dietary.attribute_title || dietary.label,
            })) || []
          );
        },
        // Fallback value
        []
      );

      setPublicDietary(response);
    } catch (error) {
      console.error('Error fetching dietary data:', error);
      setPublicDietary([]);
    }
  };

  // API function to get recipe categories
  const getRecipeCategoriesData = async () => {
    try {
      setCategoriesLoading(true);

      // Direct API call based on route type instead of using conditionalApiCall
      let apiResponse;
      if (isPublic) {
        // For public routes, call public API directly
        apiResponse = await getPublicRecipeCategoryList(
          '',
          '',
          { status: 'active' },
          '',
          { key: '', value: 'ASC' }
        );
      } else {
        // For private routes, call private API directly
        apiResponse = await getRecipeCategoryList(
          '',
          '',
          { status: 'active' },
          '',
          { key: '', value: 'ASC' }
        );
      }

      // Transform categories to match expected format and add "All Recipes" option
      const transformedCategories = [
        { id: 'all', label: 'All Recipes', icon: 'ChefHat', icon_url: '' },
        ...(apiResponse?.categories?.map((category) => ({
          id: category?.id || category?.category_name,
          label: category?.category_name || category?.label,
          icon_url: category?.iconItem?.iconUrl,
        })) || []),
      ];

      setCategories(transformedCategories);
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      // Set default categories on error
      setCategories([
        { id: 'all', label: 'All Recipes', icon: 'ChefHat', icon_url: '' },
      ]);
    } finally {
      setCategoriesLoading(false);
    }
  };

  // API function to get recipes with filters
  const getRecipeListData = async (
    search,
    page,
    Rpp,
    apiFilters,
    showLoader = true,
    appendData = false
  ) => {
    // Prevent multiple simultaneous API calls
    if (apiCallInProgress) {
      return;
    }

    try {
      setApiCallInProgress(true);
      if (showLoader) {
        setIsLoading(true);
      }
      if (appendData) {
        setIsLoadingMore(true);
      }

      // Build filter object for API
      const filterParams = {
        // Categories filter
        categories: selectedCategory?.includes('all') ? [] : selectedCategory,

        // Advanced filters
        allergens: apiFilters?.allergens || [],
        dietary: apiFilters?.dietary || [],
        difficulty: apiFilters?.difficulty || '',
        status: apiFilters?.status || '',

        // Cost range filter
        cost_min: apiFilters?.costRange?.[0],
        cost_max: apiFilters?.costRange?.[1],

        // Time filters (convert minutes to seconds for API)
        cooking_time_min: (() => {
          const timeRange = getCookingTimeRange(apiFilters?.cookingTime);
          return timeRange?.min ? timeRange.min * 60 : undefined;
        })(),
        cooking_time_max: (() => {
          const timeRange = getCookingTimeRange(apiFilters?.cookingTime);
          return timeRange?.max ? timeRange.max * 60 : undefined;
        })(),
        ingredient: ingIncluded?.join(',') || '',
        exclude_ingredient: ingExcluded?.join(',') || '',
        // Ownership filter
        bookmarked: apiFilters?.ownership === 'bookmarked' ? 1 : undefined,
        organization_slug: org_name,
      };

      // Remove undefined values
      Object.keys(filterParams).forEach((key) => {
        if (
          filterParams[key] === undefined ||
          filterParams[key] === '' ||
          (Array.isArray(filterParams[key]) && filterParams[key].length === 0)
        ) {
          delete filterParams[key];
        }
      });

      // Use utility function to choose appropriate API based on route type
      const response = await getRecipeListByRouteType(
        pathname,
        search || '',
        page || 1,
        Rpp || rowsPerPage,
        filterParams,
        getPublicRecipeList,
        getRecipeList
      );

      if (appendData) {
        // Append new data to existing recipes
        setRecipesData((prev) => [...prev, ...response.recipesList]);
      } else {
        // Replace existing recipes
        setRecipesData(response.recipesList);
      }
      if (isPublic) {
        saveToStorage(
          identifiers.RECIPE_PUBLIC_ORG_DATA,
          response?.organization_details
        );
      }
      setTotalCount(response.totalCount);

      // Update hasMore based on whether there are more items to load
      const totalPages = Math.ceil(response.totalCount / rowsPerPage);
      const hasMorePages = page < totalPages;
      const hasMoreData =
        response.recipesList && response.recipesList.length > 0;
      setHasMore(hasMorePages && hasMoreData);

      // For public routes, extract allergens as fallback if not already loaded from dedicated APIs
      if (isPublic && response.recipesList && !appendData) {
        // Only extract allergens if they weren't loaded from dedicated API
        if (publicAllergens.length === 0) {
          const extractedAllergens = extractAllergensFromRecipes(
            response.recipesList
          );
          setPublicAllergens(extractedAllergens);
        }

        // Categories are now loaded from dedicated API, no need for fallback extraction
        // Removed the fallback logic that was overwriting properly loaded categories
      }
    } catch (error) {
      if (error?.response?.status === 404) {
        router.push(pathname + '/not-found');
        return;
      }
      setApiMessage('error', error?.response?.data?.message);
      // Set empty arrays on API error
      if (!appendData) {
        setRecipesData([]);
        setTotalCount(0);
      }
      setHasMore(false);
    } finally {
      setApiCallInProgress(false);
      if (showLoader) {
        setTimeout(() => {
          setIsLoading(false);
        }, 100);
      }
      if (appendData) {
        setIsLoadingMore(false);
      }
    }
  };

  // Function to load more data for infinite scroll
  const loadMoreData = () => {
    if (!isLoadingMore && hasMore) {
      const nextPage = currentPage + 1;
      setCurrentPage(nextPage);
      getRecipeListData(
        searchQuery,
        nextPage,
        rowsPerPage,
        filters,
        false,
        true
      );
    }
  };

  // Helper function to convert cooking time filter to min/max values
  const getCookingTimeRange = (timeFilter) => {
    switch (timeFilter) {
      case 'under_30_min':
        return { min: 0, max: 29 };
      case '30_60_min':
        return { min: 30, max: 60 };
      case '1_2_hours':
        return { min: 61, max: 120 };
      case '2+_hours':
        return { min: 121, max: undefined };
      default:
        return { min: undefined, max: undefined };
    }
  };

  // Fetch ingredient Included options for dropdowns
  const fetchIngredientIncludedOptions = async (search = '') => {
    try {
      // Use conditional API call utility function
      const response = await conditionalApiCall(
        pathname,
        async () => {
          return await getIngredientItemsList(
            search,
            '',
            { status: 'active' },
            '',
            ''
          );
        },
        // Fallback: use public API for public routes
        async () => {
          return await getPublicIngredientItemsList(
            search,
            '',
            { status: 'active' },
            '',
            ''
          );
        }
      );

      const options =
        response?.ingredients?.map((item) => ({
          value: item.id,
          label: item.ingredient_name,
        })) || [];
      setIngIncludedOpt(options);
      setIngIncludedLabelMap((prev) => {
        const newMap = { ...prev };
        options.forEach((opt) => {
          newMap[opt.value] = opt.label;
        });
        return newMap;
      });
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setIngIncludedOpt([]);
    }
  };

  // Fetch ingredient Excluded options for dropdowns
  const fetchIngredientExcludedOptions = async (search = '') => {
    try {
      // Use conditional API call utility function
      const response = await conditionalApiCall(
        pathname,
        async () => {
          return await getIngredientItemsList(
            search,
            '',
            { status: 'active' },
            '',
            ''
          );
        },
        // Fallback: use public API for public routes
        async () => {
          return await getPublicIngredientItemsList(
            search,
            '',
            { status: 'active' },
            '',
            ''
          );
        }
      );

      const options =
        response?.ingredients?.map((item) => ({
          value: item.id,
          label: item.ingredient_name,
        })) || [];
      setIngExcludedOpt(options);
      setIngExcludedLabelMap((prev) => {
        const newMap = { ...prev };
        options.forEach((opt) => {
          newMap[opt.value] = opt.label;
        });
        return newMap;
      });
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setIngExcludedOpt([]);
    }
  };

  // useEffect to fetch categories, allergens, and dietary data on component mount
  useEffect(() => {
    getRecipeCategoriesData();
    getAllergensData();
    getDietaryData();
    removeFromStorage(identifiers.RECIPE_CREATION_DATA);
  }, []);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (ingredientSearchIncluded.length >= 3) {
        fetchIngredientIncludedOptions(ingredientSearchIncluded);
      } else {
        setIngIncludedOpt([]);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [ingredientSearchIncluded]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (ingredientSearchExcluded.length >= 3) {
        fetchIngredientExcludedOptions(ingredientSearchExcluded);
      } else {
        setIngExcludedOpt([]);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [ingredientSearchExcluded]);

  // Single useEffect to handle all API calls and prevent duplicates
  useEffect(() => {
    // Reset pagination when filters change
    setCurrentPage(1);
    setHasMore(true);

    // Initial load
    // if (!isInitialized) {
    //   setIsInitialized(true);
    //   getRecipeListData('', 1, rowsPerPage, {}, true);
    //   return;
    // }

    // Debounce API calls for search and filter changes
    const timeoutId = setTimeout(() => {
      getRecipeListData(searchQuery, 1, rowsPerPage, filters, true);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [
    searchQuery,
    filters,
    selectedCategory,
    // isInitialized,
    ingExcluded,
    ingIncluded,
  ]);

  // Handle category selection with multiple selection support
  const handleCategorySelect = (categoryId) => {
    setSelectedCategory((prev) => {
      if (categoryId === 'all') {
        return ['all'];
      }

      // Remove 'all' if it exists when selecting specific categories
      const filteredPrev = prev?.filter((id) => id !== 'all') || [];

      if (filteredPrev?.includes(categoryId)) {
        // Remove category if already selected
        const newSelection = filteredPrev?.filter((id) => id !== categoryId);
        // If no categories selected, default to 'all'
        return newSelection?.length === 0 ? ['all'] : newSelection;
      } else {
        // Add category to selection
        return [...filteredPrev, categoryId];
      }
    });
  };

  // Handle filter changes
  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
    // API call will be triggered automatically by useEffect
  };

  // Handle bookmark toggle
  const handleBookmarkToggle = async (recipeId) => {
    // Check if bookmark functionality is available for this route
    if (!routeFeatures.showBookmarkFunctionality) {
      setApiMessage(
        'info',
        'Bookmark functionality is not available on public pages'
      );
      return;
    }

    try {
      const response = await updateBookmark(recipeId);

      if (response?.status) {
        setApiMessage('success', response?.message);
        setRecipesData(
          (prev) =>
            prev?.map((recipe) =>
              recipe?.id === recipeId
                ? {
                    ...recipe,
                    is_bookmarked: recipe?.is_bookmarked ? false : true,
                  }
                : recipe
            ) || []
        );
      } else {
        setApiMessage('error', response?.message);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // Handle recipe actions
  const handleDuplicate = async (recipe) => {
    // Check if duplicate functionality is available for this route
    if (!routeFeatures.allowDuplicate) {
      setApiMessage(
        'info',
        'Duplicate functionality is not available on public pages'
      );
      return;
    }

    try {
      const response = await duplicateRecipe(recipe?.id);

      if (response?.status) {
        setApiMessage('success', response?.message);
        // Add the new duplicate recipe at the beginning of the list
        setRecipesData((prev) => {
          const newRecipe = { ...response?.data };
          return [newRecipe, ...(prev || [])];
        });
      } else {
        setApiMessage('error', response?.message);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const handleDelete = async (recipe) => {
    // Check if delete functionality is available for this route
    if (!routeFeatures.allowDelete) {
      setApiMessage(
        'info',
        'Delete functionality is not available on public pages'
      );
      return;
    }

    try {
      const response = await deleteRecipe(recipe?.id);

      if (response?.status) {
        setApiMessage('success', response?.message);
        setRecipesData(
          (prev) => prev?.filter((r) => r?.id !== recipe?.id) || []
        );
      } else {
        setApiMessage('error', response?.message);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const handleShare = (recipe) => {
    // Handle share functionality for recipe
    if (recipe?.recipe_title) {
      // Implementation for sharing recipe
    }
  };

  const handleEdit = (recipe) => {
    // Check if edit functionality is available for this route
    if (!routeFeatures.allowEdit) {
      setApiMessage(
        'info',
        'Edit functionality is not available on public pages'
      );
      return;
    }

    router.push(`/recipes/recipe-update/${recipe?.recipe_slug || recipe?.id}`);
  };
  const handleViewDetails = async (recipe) => {
    // Call private recipe view tracking API when in private mode
    if (!isPublic && recipe?.id) {
      try {
        await trackPrivateRecipeView({
          recipe_id: recipe?.id,
          recipe_name: recipe?.recipe_title,
        });
      } catch (error) {
        // Don't block navigation if tracking fails
        console.error('Failed to track private recipe view:', error);
      }
    }

    // Navigate to different routes based on public/private context
    if (isPublic) {
      // For public routes, navigate to public recipe view page
      router.push(
        `/recipe/${orgName}/recipe-preview/${recipe?.recipe_slug || recipe?.id}`
      );
    } else {
      // For private routes, navigate to private recipe preview page
      router.push(
        `/recipes/recipe-preview/${recipe?.recipe_slug || recipe?.id}`
      );
    }
  };
  const handleCardClick = async (recipe) => {
    // Navigate to different routes based on public/private context
    if (isPublic) {
      // For public routes, navigate to public recipe view page
      router.push(
        `/recipe/${orgName}/recipe-preview/${recipe?.recipe_slug || recipe?.id}`
      );
    } else if (!isAllPermission) {
      // For private routes, navigate to private recipe preview page
      router.push(
        `/recipes/recipe-preview/${recipe?.recipe_slug || recipe?.id}`
      );
    } else {
      // For private routes, navigate to private recipe preview page
      router.push(
        `/recipes/recipe-update/${recipe?.recipe_slug || recipe?.id}`
      );
    }
  };
  const handleViewHistory = (recipe) => {
    router.push(`/recipes/recipe-history/${recipe?.id}`);
  };
  const handleViewActivity = (recipe) => {
    router.push(`/recipes/recipe-activity/${recipe?.id}`);
  };
  const handleExport = async (recipe) => {
    // Handle export functionality for recipe
    setExportLoading(true);
    try {
      const response = await exportRecipe(recipe?.id, 'pdf');

      // Get filename from response headers or fallback
      let filename = `${recipe?.recipe_title || 'recipe'}.pdf`;
      const disposition = response.headers['content-disposition'];
      if (disposition) {
        const match = disposition.match(/filename="?([^";]+)"?/);
        if (match) filename = match[1];
      }
      // Create blob and trigger download
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', filename);
      document.body.appendChild(link);
      link.click();
      link.parentNode.removeChild(link);
      window.URL.revokeObjectURL(url);
      setExportLoading(false);
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setExportLoading(false);
    }
  };

  // Function to handle recipe assignment
  const handleAssignRecipe = (recipe) => {
    setSelectedAssignment(recipe);
    setAssignEmployee(true);
  };

  // Function to handle assignment submission (for Leave AssignEmployee)
  const handleAssignEmployees = async (userIds) => {
    if (!selectedAssignment?.id) {
      setApiMessage('error', 'Recipe ID is required');
      return;
    }

    try {
      let response = await assignRecipeToUsers(selectedAssignment?.id, userIds);

      if (response?.status) {
        setApiMessage(
          'success',
          response?.message || 'Recipe assigned successfully'
        );
        // Update the recipe in the list to reflect the assignment
        setRecipesData(
          (prev) =>
            prev?.map((recipe) =>
              recipe?.id === selectedAssignment?.id
                ? {
                    ...recipe,
                    assigned_users: userIds,
                  }
                : recipe
            ) || []
        );
        setAssignEmployee(false);
        setSelectedAssignment(null);
      } else {
        setApiMessage('error', response?.message || 'Failed to assign recipe');
      }
    } catch (error) {
      setApiMessage(
        'error',
        error?.response?.data?.message || 'Failed to assign recipe'
      );
    }
  };

  return (
    <div className="recipes-listing" id="recipes-listing">
      <div
        className={`recipes-listing__header ${!isAllPermission && 'recipes-listing__header--public'}`}
      >
        <p className="sub-header-text">Recipe</p>

        {!isPublic && isAllPermission && (
          <div className="d-flex gap-10 align-center">
            <CustomButton
              variant="contained"
              startIcon={<Icon name="Plus" size={18} />}
              onClick={() => router.push('/recipes/recipe-create')}
              title="Add New"
              // className="recipe-grid__empty-button"
            />
          </div>
        )}
      </div>
      <Divider />
      {exportLoading && <PreLoader />}
      <main
        className={getRouteBasedClassName(pathname, 'recipes-listing__main')}
      >
        <div className="recipes-listing__container">
          {/* Category Filter Chips */}
          <CategoryScrollChips
            categories={categories}
            selectedCategory={selectedCategory}
            onCategorySelect={handleCategorySelect}
            loading={categoriesLoading}
          />

          {/* Controls Bar */}
          <div className="recipes-listing__controls">
            <div className="recipes-listing__controls-left">
              {/* Search Bar */}
              <div className="recipes-listing__search-section">
                <CustomSearch
                  placeholder="Search recipes by name, ingredients, or tags..."
                  setSearchValue={setSearchQuery}
                  searchValue={searchQuery}
                  searchclass=""
                />

                <MultipleFilter
                  selected={ingExcluded}
                  setSelected={(value) => {
                    setIngExcluded(value);
                  }}
                  List={ingIncExcludedOpt}
                  placeholder="Exclude ingredients"
                  isSearch
                  searchValue={ingredientSearchExcluded}
                  setSearchValue={setIngredientSearchExcluded}
                  valueLabelMap={ingExcludedLabelMap}
                />

                <MultipleFilter
                  selected={ingIncluded}
                  setSelected={(value) => {
                    setIngIncluded(value);
                  }}
                  List={ingIncludedOpt}
                  placeholder="Include ingredients"
                  isSearch
                  searchValue={ingredientSearchIncluded}
                  setSearchValue={setIngredientSearchIncluded}
                  valueLabelMap={ingIncludedLabelMap}
                />
              </div>
            </div>

            <div className="recipes-listing__controls-right">
              {/* Results Count */}
              <span className="recipes-listing__results-count">
                {recipesData?.length} of {totalCount} recipes
              </span>

              {/* Filter Toggle */}
              <button
                onClick={() => setIsFilterPanelOpen(!isFilterPanelOpen)}
                className={`recipes-listing__filter-btn ${
                  isFilterPanelOpen ? 'recipes-listing__filter-btn--active' : ''
                }`}
              >
                <Icon name="Filter" size={16} />
                <span>Filters</span>
              </button>
            </div>
          </div>

          {/* Main Content */}
          <div className="recipes-listing__content">
            {/* Filter Panel */}
            {isFilterPanelOpen && (
              <FilterPanel
                isOpen={isFilterPanelOpen}
                filters={filters}
                onFilterChange={handleFilterChange}
                onClose={() => setIsFilterPanelOpen(false)}
                publicAllergens={isPublic ? publicAllergens : undefined}
                publicDietary={isPublic ? publicDietary : undefined}
              />
            )}

            {/* Recipe Grid with Infinite Scroll */}
            <div className="recipes-listing__grid-container">
              <InfiniteScroll
                dataLength={recipesData?.length || 0}
                next={loadMoreData}
                hasMore={hasMore}
                loader={
                  <div className="recipes-listing__loading-more">
                    <div className="recipes-listing__loading-spinner"></div>
                    <span>Loading more recipes...</span>
                  </div>
                }
                endMessage={
                  recipesData?.length > 0 && (
                    <div className="recipes-listing__end-message">
                      <p>You've seen all recipes!</p>
                    </div>
                  )
                }
                scrollableTarget="recipes-listing"
              >
                <RecipeGrid
                  recipes={recipesData}
                  isLoading={isLoading && recipesData?.length === 0}
                  viewMode="grid"
                  isPublicPage={isPublic} // Pass the correct public route boolean
                  onBookmarkToggle={handleBookmarkToggle}
                  onDuplicate={handleDuplicate}
                  onDelete={handleDelete}
                  onShare={handleShare}
                  onExport={handleExport}
                  onEdit={handleEdit}
                  onView={handleViewDetails}
                  onCardClick={handleCardClick}
                  onViewHistory={handleViewHistory}
                  onViewActivity={handleViewActivity}
                  onAssign={handleAssignRecipe} // Pass the assign functionality
                  isAllPermission={isAllPermission}
                />
              </InfiniteScroll>
            </div>
          </div>
        </div>
        <RightDrawer
          anchor={'right'}
          open={assignEmployee}
          onClose={() => {
            setAssignEmployee(false);
            setSelectedAssignment(null);
          }}
          title="Assign Recipe to Employees"
          subTitle="Recipe Management"
          className="assign-employee-drawer recipe-assign-drawer"
          content={
            <>
              <AssignEmployee
                setClose={setAssignEmployee}
                AssignEmployees={handleAssignEmployees}
                assignId={[selectedAssignment?.id]}
                SelectedID={selectedAssignment?.assigned_users?.join(',') || ''}
              />
            </>
          }
        />
      </main>
    </div>
  );
};

export default RecipesList;
