'use client';

import React, { useEffect, useContext, useState } from 'react';
import {
  Box,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
} from '@mui/material';
import AuthContext from '@/helper/authcontext';
import { useRouter } from 'next/navigation';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import { fetchFromStorage, saveToStorage } from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import {
  DateFormat,
  getImageName,
  setApiMessage,
} from '@/helper/common/commonFunctions';
import ContentLoader from '../UI/ContentLoader';
import { getChangeRequestById } from '@/services/changeRequestService';
import NoDataView from '../UI/NoDataView';
import UserDetailsView from './UserDetailsVIew/UserDetailsView';
import CRHistoryDetails from './CRHistoryDetails/CRHistoryDetails';

const CRView = ({ crId }) => {
  const router = useRouter();

  const { authState } = useContext(AuthContext);
  const { userdata, setUserdata } = useContext(AuthContext);
  const [loader, setLoader] = useState(false);
  const [expanded, setExpanded] = useState(false);
  const [crData, setCrData] = useState();

  const handleChangeAccordion = (panel) => {
    expanded === panel ? setExpanded() : setExpanded(panel);
  };
  // Change request data by Id
  const getCRData = async () => {
    setLoader(true);
    try {
      const response = await getChangeRequestById(crId);
      if (response.success) {
        setLoader(false);
        setCrData({
          ...response?.data,
          old_data: response?.data?.old_data
            ? JSON.parse(response?.data?.old_data)
            : response?.data?.old_data,
          new_data: response?.data?.new_data
            ? JSON.parse(response?.data?.new_data)
            : response?.data?.new_data,
        });
      } else {
        setLoader(false);
        setCrData([]);
        setApiMessage(
          'error',
          response.error || 'Failed to fetch change request data'
        );
      }
    } catch (error) {
      setLoader(false);
      setCrData([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const handleRequestStatus = (status) => {
    const statusConfig = {
      reopened: {
        className: 'sub-title-text status-yellow fw600',
        label: 'Re-opened',
      },
      pending: {
        className: 'sub-title-text draft fw600 text-capital',
        label: status,
      },
      approved: {
        className: 'sub-title-text active-onboarding fw600 text-capital',
        label: status,
      },
      closed: {
        className: 'sub-title-text closed fw600 text-capital',
        label: status,
      },
      rejected: {
        className: 'sub-title-text failed fw600 text-capital',
        label: status,
      },
      deleted: {
        className: 'sub-title-text failed fw600 text-capital',
        label: status,
      },
      cancelled: {
        className: 'sub-title-text failed fw600 text-capital',
        label: status,
      },
    };

    const config = statusConfig[status] || {
      className: 'sub-title-text success fw600 text-capital',
      label: status,
    };

    return <span className={config?.className}>{config.label}</span>;
  };
  useEffect(() => {
    if (
      authState?.UserPermission?.dashboard === 1 ||
      authState?.UserPermission?.dashboard === 2
    ) {
      //
    }
  }, [authState?.UserPermission?.dashboard]);
  const oldCR = fetchFromStorage(identifiers?.RedirectData)
    ? fetchFromStorage(identifiers?.RedirectData)
    : userdata;

  useEffect(() => {
    if (oldCR?.IsFromUser === undefined && oldCR?.page !== undefined) {
      setUserdata({ ...userdata, IsFromUser: true });
      saveToStorage(identifiers?.RedirectData, {
        ...oldCR,
        IsFromUser: true,
      });
    }
  }, [oldCR]);
  useEffect(() => {
    if (crId) {
      getCRData();
    }
  }, [crId]);

  return (
    <>
      <Box className="change-request">
        <Box className="d-flex align-center mb8">
          <ArrowBackIosIcon
            className="cursor-pointer"
            onClick={() => {
              setTimeout(() => {
                router?.push('/change-request');
              }, 1000);
            }}
          />
          <Typography className="body-text fw600 pr8">
            View Change Request
          </Typography>
        </Box>
        <Divider className="mb16 mt16" />
        {loader ? (
          <ContentLoader />
        ) : !crData || crData.length === 0 ? (
          <NoDataView
            title="No Change Request Found"
            description="There is no Change Request available at the moment."
          />
        ) : (
          <>
            <Box>
              <UserDetailsView
                userData={crData?.change_request_user}
                handleRequestStatus={handleRequestStatus}
                showAvatar={true}
                showBranch={true}
                showDate={true}
                showStatus={true}
                createdAt={crData?.createdAt}
                status={crData?.change_request_status}
              />
            </Box>
            <Box>
              <Box>
                {crData &&
                  crData?.request_history &&
                  crData?.request_history?.length > 0 &&
                  crData?.request_history?.map((cdata, index) => {
                    return (
                      <Accordion
                        // key={1}
                        elevation={0}
                        className="cr-accordion"
                        expanded={expanded === index}
                        onChange={() => {
                          handleChangeAccordion(index);
                        }}
                      >
                        <AccordionSummary
                          expandIcon={<KeyboardArrowDownIcon />}
                          className="accordion-heading"
                        >
                          <Box className="subject-section gap-sm">
                            <Typography className="body-text fw600 subject-text text-ellipsis-line">
                              {cdata?.change_request_subject}
                            </Typography>
                            <Box className="d-flex align-center justify-center h100">
                              {handleRequestStatus(
                                cdata?.change_request_status
                              )}
                            </Box>
                            <Typography className="title-text date-text">
                              {DateFormat(cdata?.createdAt, 'datesWithhour')}
                            </Typography>
                          </Box>
                        </AccordionSummary>
                        <AccordionDetails>
                          <CRHistoryDetails
                            cdata={{
                              ...cdata,
                              old_data: cdata?.old_data
                                ? JSON.parse(cdata?.old_data)
                                : cdata?.old_data,
                              new_data: cdata?.new_data
                                ? JSON.parse(cdata?.new_data)
                                : cdata?.new_data,
                            }}
                            crData={crData}
                            getImageName={getImageName}
                          />
                        </AccordionDetails>
                      </Accordion>
                    );
                  })}
              </Box>
            </Box>
          </>
        )}
      </Box>
    </>
  );
};

export default CRView;
