'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Box, Typography, Tooltip } from '@mui/material';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import { useRouter } from 'next/navigation';
import PreLoader from '@/components/UI/Loader';
import InfoIcon from '@mui/icons-material/Info';
import dayjs from 'dayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import CustomButton from '@/components/UI/CustomButton';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomCheckbox from '@/components/UI/CustomCheckbox';
import CustomDatePicker from '@/components/UI/CustomDatePicker';
import CustomColorPicker from '@/components/UI/CustomColorPicker';
import './holiday.scss';

dayjs.extend(isSameOrAfter);

const AddEditHoliday = ({ isEdit, typeId, holidayType, getHolidayList }) => {
  const router = useRouter();
  const formikRef = useRef(null);
  const [loader, setLoader] = useState(false);
  const [holidayPolicy, setHolidayPolicy] = useState('');
  const [moreThan, setMorethan] = useState(false);

  // GET HOLIDAY POLICY DETAILS
  const getHolidayPolicyDetails = async () => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_hOLIDAY_POLICY_BY_ID + `${typeId}`
      );

      if (status === 200) {
        setLoader(false);
        setHolidayPolicy(data?.data);
        formikRef.current.setFieldValue(
          'has_leave_reprocess',
          data?.data?.has_leave_reprocess ? true : false
        );
        data?.data?.holiday_policy_end_date &&
        data?.data?.holiday_policy_end_date !==
          data?.data?.holiday_policy_start_date
          ? setMorethan(true)
          : setMorethan(false);
      }
    } catch (error) {
      setLoader(false);
      setHolidayPolicy('');
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  useEffect(() => {
    if (isEdit === 'false') {
      getHolidayPolicyDetails();
    }
  }, [isEdit]);

  return (
    <>
      {loader && <PreLoader />}
      <Formik
        innerRef={formikRef}
        initialValues={{
          policyName: holidayPolicy?.holiday_policy_name || '',
          remark: holidayPolicy?.holiday_policy_description || '',
          holidaySdate: holidayPolicy?.holiday_policy_start_date
            ? dayjs(holidayPolicy?.holiday_policy_start_date).format(
                'YYYY-MM-DD'
              )
            : null,
          holidayEdate: holidayPolicy?.holiday_policy_end_date
            ? dayjs(holidayPolicy?.holiday_policy_end_date).format('YYYY-MM-DD')
            : null,
          has_leave_reprocess: holidayPolicy?.has_leave_reprocess,
          leave_color: holidayPolicy?.holiday_policy_colour || '#39596e',
        }}
        enableReinitialize={true}
        validationSchema={Yup.object().shape({
          policyName: Yup.string().trim().required('This field is required'),
          holidaySdate: Yup.date()
            .typeError('Invalid date')
            .required('This field is required'),
          leave_color: Yup.string().trim().required('This field is required'),
          holidayEdate: Yup.string()
            .nullable()
            .when('holidaySdate', {
              is: (holidaySdate) => holidaySdate,
              then: (schema) =>
                schema.test(
                  'is-after-start',
                  'End date must be the same or after start date',
                  function (value) {
                    if (!value) return true; // Skip validation if no end date
                    return dayjs(value).isSameOrAfter(
                      dayjs(this.parent.holidaySdate)
                    );
                  }
                ),
            }),
        })}
        onSubmit={async (requestData) => {
          const sendData = {
            holiday_policy_name: requestData?.policyName,
            holiday_policy_description: requestData?.remark,
            holiday_policy_start_date: requestData?.holidaySdate
              ? dayjs(requestData?.holidaySdate)?.format('YYYY-MM-DD')
              : null,
            holiday_policy_end_date:
              requestData?.holidayEdate && moreThan
                ? dayjs(requestData?.holidayEdate)?.format('YYYY-MM-DD')
                : requestData?.holidaySdate
                  ? dayjs(requestData?.holidaySdate)?.format('YYYY-MM-DD')
                  : null,
            // requestData?.holidaySdate
            //   ? dayjs(requestData?.holidaySdate)?.format('YYYY-MM-DD')
            //   : null,
            holiday_policy_colour: requestData?.leave_color,
            has_leave_reprocess: requestData?.has_leave_reprocess,
            ...(isEdit === 'false' &&
              holidayType && { holiday_type_id: holidayType }),
          };
          let url = '';
          let method = '';
          if (isEdit === 'false') {
            url = URLS.UPDATE_HOLIDAY + `${typeId}`;
            method = 'put';
          } else {
            url = URLS.CREATE_HOLIDAY + `${typeId}`;
            method = 'post';
          }
          try {
            setLoader(true);
            const { status, data } = await axiosInstance[method](url, sendData);

            if (status === 200 || status === 201) {
              if (data?.status) {
                setApiMessage('success', data?.message);
                getHolidayList();
                setTimeout(() => {
                  router.back();
                  // setAddHoliday(false);
                }, 500);
              } else {
                setApiMessage('error', data?.message);
              }
              setLoader(false);
            }
          } catch (error) {
            setLoader(false);
            setApiMessage('error', error?.response?.data?.message);
          }
        }}
      >
        {({
          errors,
          touched,
          handleBlur,
          values,
          handleSubmit,
          handleChange,
          setFieldValue,
          dirty,
          isValid,
        }) => (
          <Form onSubmit={handleSubmit}>
            <Box className="pb24 add-leave-wrap">
              <Box className="display-grid">
                <Box>
                  <CustomTextField
                    fullWidth
                    id="policyName"
                    name="policyName"
                    label="Holiday Name"
                    placeholder="Holiday Name"
                    value={values?.policyName}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={Boolean(touched.policyName && errors.policyName)}
                    helperText={touched.policyName && errors.policyName}
                    required
                  />
                </Box>
                <Box>
                  <CustomColorPicker
                    id="leave_color"
                    name="leave_color"
                    label="Leave color"
                    required
                    value={values?.leave_color}
                    onChange={(val) => {
                      setFieldValue('leave_color', val);
                    }}
                  />
                </Box>
              </Box>
              <Box className="display-grid pt8">
                <Box className="date-picker-textfield year-start-leave-wrap pt16">
                  <CustomDatePicker
                    fullWidth
                    label="From"
                    name="holidaySdate"
                    value={dayjs(values?.holidaySdate) || null}
                    onChange={(date) => {
                      setFieldValue('holidaySdate', date);
                      setFieldValue('holidayEdate', null);
                    }}
                    onBlur={handleBlur}
                    error={Boolean(touched.holidaySdate && errors.holidaySdate)}
                    helperText={touched.holidaySdate && errors.holidaySdate}
                    required
                  />
                </Box>
                {!moreThan ? (
                  <Box className="pt16">
                    <Typography
                      className="title-text cursor-pointer primary-link-text text-uppercase"
                      onClick={() => {
                        setMorethan(true);
                      }}
                    >
                      More than One Day?
                    </Typography>
                  </Box>
                ) : (
                  <Box className="year-start-leave-wrap pt16">
                    <CustomDatePicker
                      fullWidth
                      label="To"
                      name="holidayEdate"
                      value={dayjs(values?.holidayEdate) || null}
                      onChange={(date) => setFieldValue('holidayEdate', date)}
                      onBlur={handleBlur}
                      minDate={dayjs(values?.holidaySdate)}
                      error={Boolean(
                        touched.holidayEdate && errors.holidayEdate
                      )}
                      helperText={touched.holidayEdate && errors.holidayEdate}
                    />
                  </Box>
                )}
                {moreThan ? (
                  <Box className="pt16">
                    <Typography
                      className="title-text primary-link-text text-uppercase"
                      onClick={() => {
                        setMorethan(false);
                        setFieldValue('holidayEdate', null);
                      }}
                    >
                      Add Only One Day?
                    </Typography>
                  </Box>
                ) : (
                  <></>
                )}
              </Box>

              <Box className="pt16">
                <CustomTextField
                  fullWidth
                  id="remark"
                  name="remark"
                  label="Remark"
                  placeholder="Remark"
                  value={values?.remark}
                  onBlur={handleBlur}
                  onChange={handleChange}
                  multiline
                  rows={4}
                />
              </Box>

              <Box className="d-flex align-center pt8">
                <CustomCheckbox
                  name="has_leave_reprocess"
                  checked={values?.has_leave_reprocess ? true : false}
                  onChange={(e) => {
                    setFieldValue('has_leave_reprocess', e.target.checked);
                  }}
                  disableRipple
                  label={
                    <Typography className="sub-title-text">
                      Reprocess leave applications based on this added holiday
                    </Typography>
                  }
                />

                <Tooltip
                  title={
                    <Typography>
                      Leaves that are already applied for this holiday will be
                      reprocessed and the balance will be adjusted accordingly.
                    </Typography>
                  }
                  placement="right"
                  arrow
                  classes={{ tooltip: 'info-tooltip-container' }}
                >
                  <InfoIcon
                    sx={{ marginLeft: '8px' }}
                    className="info-icon cursor-pointer"
                  />
                </Tooltip>
              </Box>
            </Box>

            <Box className="form-actions-btn">
              <CustomButton
                variant="contained"
                title={`${loader ? 'Saving...' : 'Save'}`}
                disabled={!(dirty && isValid) || loader}
                fullWidth={false}
                type="submit"
              />
            </Box>
          </Form>
        )}
      </Formik>
    </>
  );
};

export default AddEditHoliday;
