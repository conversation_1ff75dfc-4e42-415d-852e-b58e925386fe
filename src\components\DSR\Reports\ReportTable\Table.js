'use client';

import React from 'react';
import {
  Box,
  TableHead,
  TableCell,
  TableRow,
  TableBody,
  Table,
} from '@mui/material';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';

export default function DSRTable({ dsrData, setDsrData }) {
  const cellWidth = 150;
  const onDragEnd = (result) => {
    const { source, destination, type } = result;

    // If there's no destination (dropped outside), exit
    if (!destination) return;

    // For reordering groups
    if (type === 'group') {
      const reorderedGroups = Array.from(dsrData.columns);
      const [removedGroup] = reorderedGroups.splice(source.index, 1);
      reorderedGroups.splice(destination.index, 0, removedGroup);
      setDsrData((prevData) => ({
        ...prevData,
        columns: reorderedGroups,
      }));
    } else if (type === 'subItem') {
      const sourceGroupIndex = dsrData.columns.findIndex(
        (group) => group.groupId === source.droppableId
      );
      const destinationGroupIndex = dsrData.columns.findIndex(
        (group) => group.groupId === destination.droppableId
      );

      // If source and destination groups are the same
      if (sourceGroupIndex === destinationGroupIndex) {
        const group = dsrData.columns[sourceGroupIndex];
        const reorderedItems = Array.from(group.items);
        const [movedItem] = reorderedItems.splice(source.index, 1);
        reorderedItems.splice(destination.index, 0, movedItem);

        const updatedGroup = { ...group, items: reorderedItems };
        const updatedColumns = Array.from(dsrData.columns);
        updatedColumns[sourceGroupIndex] = updatedGroup;

        setDsrData((prevData) => ({
          ...prevData,
          columns: updatedColumns,
        }));
      }
    }
  };
  return (
    <Box className="DSR-table">
      <DragDropContext onDragEnd={onDragEnd}>
        <Table className="table-dsr-container">
          <TableHead>
            <Droppable droppableId="groups" type="group" direction="horizontal">
              {(provided) => (
                <TableRow ref={provided.innerRef} {...provided.droppableProps}>
                  {dsrData.columns.map((group, groupIndex) => (
                    <Draggable
                      key={group.groupId}
                      draggableId={group.groupId}
                      index={groupIndex}
                    >
                      {(provided) => (
                        <TableCell
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          {...provided.dragHandleProps}
                          colSpan={group.items.length}
                          align="center"
                          className="table-head"
                          style={{
                            fontWeight: 'bold',
                            border: '1px solid gray',
                            backgroundColor: '#f1f1f1',
                            // width: '100%',
                            // width: `${group?.items?.length * cellWidth}px`,
                            minWidth: `${group?.items?.length * cellWidth}px`,
                            // maxWidth: `${group?.items?.length * cellWidth}px`
                          }}
                        >
                          <Box>{group.groupLabel}</Box>

                          <Droppable
                            droppableId={group.groupId}
                            type="subItem"
                            direction="horizontal"
                          >
                            {(provided) => (
                              <TableRow
                                ref={provided.innerRef}
                                {...provided.droppableProps}
                                style={{
                                  display: 'flex',
                                  // gap: '8px',
                                  marginTop: '8px',
                                  // width: '100%',
                                  // width: `${
                                  //   group?.items?.length * cellWidth
                                  // }px`,
                                  minWidth: `${
                                    group?.items?.length * cellWidth
                                  }px`,
                                  // maxWidth: `${
                                  //   group?.items?.length * cellWidth
                                  // }px`
                                }}
                              >
                                {group.items.map((item, itemIndex) => (
                                  <Draggable
                                    key={item.key}
                                    draggableId={item.key}
                                    index={itemIndex}
                                  >
                                    {(provided) => (
                                      <TableCell
                                        ref={provided.innerRef}
                                        {...provided.draggableProps}
                                        {...provided.dragHandleProps}
                                        className="table-cell"
                                        style={{
                                          border: '1px solid gray',
                                          backgroundColor: '#fff',
                                          padding: '6px',
                                          cursor: 'grab',
                                          flex: 1,
                                          // width: '100%',
                                          // width: `${cellWidth}px`,
                                          minWidth: `${cellWidth}px`,
                                          // maxWidth: `${cellWidth}px`,
                                          overflow: 'hidden',
                                        }}
                                      >
                                        {item.label}
                                      </TableCell>
                                    )}
                                  </Draggable>
                                ))}
                                {provided.placeholder}
                              </TableRow>
                            )}
                          </Droppable>
                        </TableCell>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </TableRow>
              )}
            </Droppable>
          </TableHead>

          <TableBody>
            {dsrData.data.map((dataRow, rowIndex) => (
              <TableRow key={rowIndex}>
                {dsrData.columns.map((group) =>
                  group.items.map((item) => (
                    <TableCell
                      key={item.key}
                      style={{
                        border: '1px solid gray',
                        // width: '100%',
                        // width: `${cellWidth}px`,
                        minWidth: `${cellWidth}px`,
                        // maxWidth: `${cellWidth}px`,
                        overflow: 'hidden',
                      }}
                      className="table-cell table-value"
                    >
                      {dataRow[item?.key] ? dataRow[item?.key] : 'ddddddd'}
                    </TableCell>
                  ))
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </DragDropContext>
    </Box>
  );
}
