import { useState, useEffect } from 'react';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { ORG_URLS } from '@/helper/constants/urls';
import { setApiMessage } from '@/helper/common/commonFunctions';

export const useCurrency = () => {
  const [currencyData, setCurrencyData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchCurrency = async () => {
    try {
      const { status, data } = await axiosInstance.get(
        ORG_URLS?.GET_CURRENCIES
      );
      if (status === 200 || status === 201) {
        if (data?.status) {
          const currencyOptions = data?.data?.map((currency) => ({
            label: currency?.code,
            value: currency?.code,
            symbol: currency?.symbol,
          }));
          setCurrencyData(currencyOptions);
          return currencyOptions;
        } else {
          setApiMessage('error', data?.message);
          return [];
        }
      }
    } catch (error) {
      setError(error);
      setApiMessage('error', error?.response?.data?.message);
      return [];
    }
  };

  const getSymbol = (currencyCode) => {
    if (!currencyCode || !currencyData) return '';
    const currencySymbol = currencyData?.find((currency) => {
      return currency?.value === currencyCode?.toUpperCase();
    });
    return currencySymbol?.symbol || '';
  };

  useEffect(() => {
    const loadCurrency = async () => {
      setLoading(true);
      await fetchCurrency();
      setLoading(false);
    };
    loadCurrency();
  }, []);

  return {
    currencyData,
    loading,
    error,
    getSymbol,
  };
};
