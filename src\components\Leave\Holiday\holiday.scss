.holiday-accordion.MuiPaper-root {
  border-radius: var(--border-radius-md);
  margin-top: var(--spacing-md) !important;
  box-shadow: var(--box-shadow-xs);

  &:first-of-type {
    border-top-left-radius: var(--border-radius-md);
    border-top-right-radius: var(--border-radius-md);
  }
  &:last-of-type {
    border-bottom-left-radius: var(--border-radius-md);
    border-bottom-right-radius: var(--border-radius-md);
  }
  .accordion-summary {
    min-height: 40px;
    padding: 0 16px 0 0;
    .MuiAccordionSummary-content {
      padding: var(--spacing-none);
      margin: var(--spacing-none) !important;
      .holiday-name {
        padding: 10px 0;
        padding-left: 16px;
      }
    }
  }
  .MuiDataGrid-root {
    margin-top: var(--spacing-none) !important;
  }
  &::before {
    position: inherit !important;
  }
  .holiday-actions {
    margin-right: var(--spacing-xsm);
    .action-icon {
      border-radius: 50%;
    }
  }
  .toggle-icon {
    width: var(--action-icon-size);
    height: var(--action-icon-size);
    background: var(--action-icon-bg-color);
    padding: 0 2px;
    border-radius: 50%;
    path {
      height: 20px;
    }
  }
  .toggle-icon.active {
    fill: var(--icon-color-green);
  }
  .toggle-icon.inactive {
    fill: var(--icon-bold-red-color);
  }
  .assign-icons {
    path {
      stroke-width: 1.6px;
    }
  }
}
.holiday-table {
  padding: var(--spacing-none);
}
