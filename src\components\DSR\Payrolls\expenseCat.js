'use client';

import React from 'react';
import {
  Box,
  Typography,
  InputAdornment,
  Checkbox,
  FormControlLabel,
  FormGroup,
  Tooltip,
} from '@mui/material';
import { TotalOfExpenseObj } from '@/helper/common/commonFunctions';
import CustomTextField from '@/components/UI/CustomTextField';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import InfoIcon from '@mui/icons-material/Info';

export default function ExpenseCategory({
  setIsRemarkView,
  expenseData,
  IsRemarkView,
  setExpenseData,
  setRandom,
  currency,
  isEdit,
}) {
  return (
    <Box>
      {' '}
      {expenseData && expenseData?.length > 0 && (
        <>
          <FormGroup className="form-checkbox pt16">
            <FormControlLabel
              control={
                <Checkbox
                  className="check-box "
                  icon={<CheckBoxOutlineBlankIcon className="uncheck-icon" />}
                  checkedIcon={<CheckBoxIcon className="check-icon" />}
                  disableRipple
                />
              }
              name="weekly"
              className="check-box-form form-row max-content sub-title-text"
              checked={IsRemarkView}
              onChange={(e) => {
                setIsRemarkView(e.target.checked);
              }}
              label="View Remark"
            />
          </FormGroup>
          <Box className="dsr-add-view pt16">
            {expenseData &&
              expenseData?.length > 0 &&
              expenseData?.map((item, dindex) => {
                return (
                  <Box className="dsr-main-category income">
                    <Typography className="body-text fw600 text-capital main-category-text text-ellipsis-line expense-text ">
                      {item?.payment_type_title}
                    </Typography>
                    <span className="category-status income">
                      <span className="sub-title-text category-draft  fw600 text-capital">
                        {' '}
                        Expense
                      </span>
                    </span>
                    {item?.payment_type_category?.length > 0 &&
                      item?.payment_type_category?.map((citem, cindex) => {
                        return (
                          <Box className="">
                            {' '}
                            {citem?.payment_type_category_pattern ===
                              'multiple' && (
                              <Box className="d-flex align-center gap-5 pt16">
                                <Typography className="title-text fw600 text-capital text-ellipsis-line">
                                  {citem?.payment_type_category_title}
                                </Typography>
                                {citem?.payment_type_category_remarks &&
                                citem?.payment_type_category_remarks?.trim() !==
                                  '' ? (
                                  <Tooltip
                                    arrow
                                    title={citem?.payment_type_category_remarks}
                                    classes={{
                                      tooltip: 'info-tooltip-container ',
                                    }}
                                  >
                                    <InfoIcon className="info-icon-wrap cursor-pointer" />
                                  </Tooltip>
                                ) : (
                                  <></>
                                )}
                              </Box>
                            )}
                            {citem?.payment_type_category_pattern ===
                            'multiple' ? (
                              <>
                                {citem?.categoryBranchValue &&
                                  citem?.categoryBranchValue?.length > 0 &&
                                  citem?.categoryBranchValue?.map(
                                    (csitem, subIndex) => {
                                      return (
                                        <>
                                          <Box className="card-details-section pt16">
                                            <Box>
                                              <Typography className="title-text text-ellipsis-line2">
                                                {csitem?.first_field_value}
                                              </Typography>
                                            </Box>
                                            <Box className="amount-field">
                                              <CustomTextField
                                                value={csitem?.expense_amount}
                                                onChange={(e) => {
                                                  const income = expenseData;
                                                  if (
                                                    income[dindex]
                                                      .payment_type_category[
                                                      cindex
                                                    ].categoryBranchValue[
                                                      subIndex
                                                    ].old_expense_amount ===
                                                      undefined &&
                                                    isEdit
                                                  ) {
                                                    income[
                                                      dindex
                                                    ].payment_type_category[
                                                      cindex
                                                    ].categoryBranchValue[
                                                      subIndex
                                                    ].old_expense_amount =
                                                      csitem?.expense_amount;
                                                  }
                                                  income[
                                                    dindex
                                                  ].payment_type_category[
                                                    cindex
                                                  ].categoryBranchValue[
                                                    subIndex
                                                  ].expense_amount =
                                                    e.target.value;
                                                  setExpenseData(income);
                                                  setRandom(Math.random());
                                                }}
                                                name={`amount ${subIndex}`}
                                                placeholder={
                                                  item?.has_field_currency
                                                    ? 'Amount'
                                                    : 'Enter value'
                                                }
                                                className={'amount-textfield'}
                                                onInput={(e) => {
                                                  // Remove non-numeric characters
                                                  let value = e.target.value;
                                                  if (
                                                    value === '' ||
                                                    /^\d*\.?\d{0,2}$/.test(
                                                      value
                                                    )
                                                  ) {
                                                    e.target.value = value;
                                                  } else {
                                                    e.target.value =
                                                      value.slice(0, -1);
                                                  }
                                                }}
                                                InputProps={{
                                                  ...(item?.has_field_currency && {
                                                    startAdornment: (
                                                      <InputAdornment position="start">
                                                        <Typography className="title-text currency">
                                                          {currency?.symbol
                                                            ? currency?.symbol
                                                            : '£'}
                                                        </Typography>{' '}
                                                      </InputAdornment>
                                                    ),
                                                  }),
                                                }}
                                              />
                                            </Box>
                                          </Box>
                                        </>
                                      );
                                    }
                                  )}
                              </>
                            ) : (
                              <>
                                <Box className="card-details-section pt16">
                                  <Box className="d-flex align-center gap-5">
                                    <Typography className="title-text fw600 text-capital text-ellipsis-line2">
                                      {citem?.payment_type_category_title}
                                    </Typography>
                                    {citem?.payment_type_category_remarks &&
                                    citem?.payment_type_category_remarks?.trim() !==
                                      '' ? (
                                      <Tooltip
                                        arrow
                                        title={
                                          citem?.payment_type_category_remarks
                                        }
                                        classes={{
                                          tooltip: 'info-tooltip-container ',
                                        }}
                                      >
                                        <InfoIcon className="info-icon-wrap cursor-pointer" />
                                      </Tooltip>
                                    ) : (
                                      <></>
                                    )}
                                  </Box>
                                  <Box className="amount-field">
                                    <CustomTextField
                                      value={citem?.expense_amount}
                                      onChange={(e) => {
                                        const income = expenseData;
                                        if (
                                          income[dindex].payment_type_category[
                                            cindex
                                          ].old_expense_amount === undefined &&
                                          isEdit
                                        ) {
                                          income[dindex].payment_type_category[
                                            cindex
                                          ].old_expense_amount =
                                            citem?.expense_amount;
                                        }
                                        income[dindex].payment_type_category[
                                          cindex
                                        ].expense_amount = e.target.value;
                                        setExpenseData(income);
                                        setRandom(Math.random());
                                      }}
                                      name={`amount ${cindex}`}
                                      placeholder={
                                        item?.has_field_currency
                                          ? 'Amount'
                                          : 'Enter value'
                                      }
                                      className={'amount-textfield'}
                                      onInput={(e) => {
                                        let value = e.target.value;
                                        if (
                                          value === '' ||
                                          /^\d*\.?\d{0,2}$/.test(value)
                                        ) {
                                          e.target.value = value;
                                        } else {
                                          e.target.value = value.slice(0, -1);
                                        }
                                      }}
                                      InputProps={{
                                        ...(item?.has_field_currency && {
                                          startAdornment: (
                                            <InputAdornment position="start">
                                              <Typography className="title-text currency">
                                                {currency?.symbol
                                                  ? currency?.symbol
                                                  : '£'}
                                              </Typography>{' '}
                                            </InputAdornment>
                                          ),
                                        }),
                                      }}
                                    />
                                  </Box>
                                </Box>
                              </>
                            )}
                          </Box>
                        );
                      })}
                    <>
                      <Box className="card-details-section pt16 totol-dsr">
                        <Box>
                          <Typography className="title-text fw600">
                            Total
                          </Typography>
                        </Box>
                        <Box className="amount-field">
                          <CustomTextField
                            value={TotalOfExpenseObj(expenseData, dindex)}
                            name={`amount `}
                            disabled={true}
                            placeholder={
                              item?.has_field_currency
                                ? 'Amount'
                                : 'Enter value'
                            }
                            className={'amount-textfield'}
                            onInput={(e) => {
                              // Remove non-numeric characters
                              let value = e.target.value;
                              if (
                                value === '' ||
                                /^\d*\.?\d{0,2}$/.test(value)
                              ) {
                                e.target.value = value;
                              } else {
                                e.target.value = value.slice(0, -1);
                              }
                            }}
                            InputProps={{
                              ...(item?.has_field_currency && {
                                startAdornment: (
                                  <InputAdornment position="start">
                                    <Typography className="title-text currency">
                                      {currency?.symbol
                                        ? currency?.symbol
                                        : '£'}
                                    </Typography>{' '}
                                  </InputAdornment>
                                ),
                              }),
                            }}
                          />
                        </Box>
                      </Box>
                      {IsRemarkView && (
                        <Box className="card-details-section  card-details-remark-section pt16">
                          <Box>
                            <Typography className="title-text fw600">
                              Remark
                            </Typography>
                          </Box>
                          <Box className="amount-field">
                            <CustomTextField
                              value={item?.payment_type_remark}
                              onChange={(e) => {
                                if (
                                  e.target.value === '' ||
                                  e.target.value?.length < 161
                                ) {
                                  const income = expenseData;
                                  income[dindex].payment_type_remark =
                                    e.target.value;
                                  setExpenseData(income);
                                  setRandom(Math.random());
                                }
                              }}
                              onPaste={(e) => {
                                if (
                                  e.target.value === '' ||
                                  e.target.value?.length < 161
                                ) {
                                  const income = expenseData;
                                  income[dindex].payment_type_remark =
                                    e.target.value;
                                  setExpenseData(income);
                                  setRandom(Math.random());
                                }
                              }}
                              multiline
                              rows={2}
                              name={`amount ${dindex}`}
                              placeholder={'Enter value'}
                              className={
                                'amount-textfield additional-textfeild'
                              }
                            />
                          </Box>
                          <Typography className="sub-title-text text-align-end">
                            {(item?.payment_type_remark?.length
                              ? item?.payment_type_remark?.length
                              : 0) + ' / 160'}
                          </Typography>
                        </Box>
                      )}
                    </>
                  </Box>
                );
              })}
          </Box>
        </>
      )}{' '}
    </Box>
  );
}
