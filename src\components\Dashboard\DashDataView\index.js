import React, { useContext } from 'react';
import { Box, Typography } from '@mui/material';
import { useRouter } from 'next/navigation';
import EastIcon from '@mui/icons-material/East';
import AuthContext from '@/helper/authcontext';
import './dataview.scss';

const DashboardDataView = ({ item }) => {
  const { authState } = useContext(AuthContext);
  const router = useRouter();

  const dataview = {
    branch: { permission: 'branch', url: '/org/setup?is_setup=2&is_tab=1' },
    department: {
      permission: 'department',
      url: '/org/setup?is_setup=3&is_tab=1',
    },
    user: { permission: 'user', url: '/staff' },
    expense: { permission: 'dsr', url: '/payroll' },
    dsr: { permission: 'dsr', url: '/dsr' },
    wsr: { permission: 'dsr', url: '/wsr' },
    category: {
      permission: 'dsr_report',
      url: '/org/setup?is_setup=13&is_tab=1',
    },
  };

  return (
    <Box className="dashboard-numbers">
      <Typography className="title-sm fw600 header">
        {item?.name ? item?.name : item?.model_title}
      </Typography>
      <Box className="devider" />

      <Typography className="body-text fw600 value">
        {item?.value
          ? item?.value
          : item?.dashboard_data?.total
            ? item?.dashboard_data?.total
            : 0}
      </Typography>
      {item?.Numberkey ? (
        <Typography className="sub-title-text fw600 keyname">
          {'Total ' + item?.Numberkey}
        </Typography>
      ) : item?.xaxis_value ? (
        <Typography className="sub-title-text fw600 keyname">
          {'Total ' + item?.xaxis_value}
        </Typography>
      ) : (
        <></>
      )}
      {dataview &&
        dataview?.[item?.xaxis_value] &&
        dataview?.[item?.xaxis_value]?.permission &&
        (authState?.UserPermission?.[
          dataview?.[item?.xaxis_value]?.permission
        ] === 2 ||
          authState?.UserPermission?.[
            dataview?.[item?.xaxis_value]?.permission
          ] === 1) && (
          <Box className="view-all-sec">
            <Typography
              className="title-text cursor-pointer fw700 box-text"
              onClick={() => {
                router.push(`${dataview?.[item?.xaxis_value]?.url}`);
              }}
            >
              View All
            </Typography>
            <EastIcon />
          </Box>
        )}
    </Box>
  );
};

export default DashboardDataView;
