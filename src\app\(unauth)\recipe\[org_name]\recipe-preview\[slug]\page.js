import RecipePreviewView from '@/components/Recipes/Recipes/Preview';
import { generateMetadata } from '@/helper/common/commonFunctions';
import React from 'react';

export const metadata = generateMetadata({
  pageTitle: 'Recipe Preview',
});

export default function RecipePreviewViewPage({ params }) {
  // params.org_name and params.slug will be available here
  return (
    <div>
      <RecipePreviewView slug={params?.slug} />
    </div>
  );
}
