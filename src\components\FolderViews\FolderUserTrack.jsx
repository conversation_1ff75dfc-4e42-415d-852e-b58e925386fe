import React, { useContext, useState, useEffect } from 'react';
import {
  Box,
  Tooltip,
  Typography,
  Avatar,
  Checkbox,
  CircularProgress,
} from '@mui/material';
import AuthContext from '@/helper/authcontext';
import CancelIcon from '@mui/icons-material/Cancel';
import { DataGrid, gridClasses } from '@mui/x-data-grid';
import CustomPagination from '@/components/UI/customPagination';
import ResetIcon from '@/components/ActionIcons/ResetIcon';
import { setApiMessage, DateFormat } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
// import { LazyLoadImage } from 'react-lazy-load-image-component';
import { useRouter } from 'next/navigation';
import InfiniteScroll from 'react-infinite-scroll-component';
import { findDifferences, stringAvatar } from '@/helper/common/commonFunctions';
import { FolderDocIcon } from '../../helper/common/images';
import ViewIcon from '@/components/ActionIcons/ViewIcon';
import DialogBox from '@/components/UI/Modalbox';
import moment from 'moment';
import HeaderImage from '@/components/UI/ImageSecurity';
import NoDataView from '@/components/UI/NoDataView';
import CommonUserDetails from '@/components/UI/CommonUserDetails/index';
import BranchDepartmentDisplay from '@/components/BranchDepartmentDisplay';
import ConfirmationModal from '@/components/UI/ConfirmationModal';
import './folder.scss';

const FolderUserTrack = ({
  folderData,
  userList,
  selected,
  rowsPerPage,
  setPage,
  page,
  totalCount,
  setSelected,
  setRowsPerPage,
  getStatisticsUserList,
  CategoryDetails,
  Id,
}) => {
  const router = useRouter();
  const { setUserdata } = useContext(AuthContext);
  const [toggleModal, setToggleModal] = useState(false);
  const [UserLogs, setUserLogs] = useState([]);
  const [UserData, setUserData] = useState();
  const [loader, setLoader] = useState(false);
  const [selectedCat, setSelectedCat] = useState([]);

  const [pageLogs, setPageLogs] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [resetTrack, setResetTrack] = useState(false);
  const [resetTrackData, setResetTrackData] = useState();
  const handleClose = () => {
    setResetTrack(false);
    setResetTrackData();
  };
  const handleResetTrack = () => {
    ResetDoctMedia(resetTrackData);
  };
  const handleCheck = (e, value) => {
    const currentHSIndex = selectedCat?.indexOf(value);
    const newChecked = [...selectedCat];
    if (currentHSIndex === -1) {
      newChecked.push(value);
    } else {
      newChecked.splice(currentHSIndex, 1);
    }
    setSelectedCat(newChecked);
  };
  const handleSelectAllClick = (event) => {
    if (event.target.checked) {
      const newSelecteds = userList?.map((n) => n?.id);
      setSelectedCat(newSelecteds);
      return;
    }
    setSelectedCat([]);
  };
  // Reset Document media
  const ResetDoctMedia = async (id) => {
    const requestData = {
      user_id: id.toString(),
      ...(Id &&
        Id !== 'all' && { category_id: Id && Id !== 'all' ? Id : null }),
      search_category_id: selected ? selected.toString() : null,
    };
    try {
      const { status, data } = await axiosInstance.post(
        URLS?.USER_RESET_CATEGORY_TRACK,
        requestData
      );
      if (status === 200) {
        if (data.status) {
          setApiMessage('success', data?.message);
          getStatisticsUserList(page, selected);
          setSelectedCat([]);
        } else {
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  //List of Logs
  const getStaffLogs = async (user, page = 1) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.USER_TRACK_LOGS +
          `?page=${page}&size=20&category_id=${
            Id && Id !== 'all' ? Id : ''
          }&search_category_id=${
            selected ? selected.toString() : ''
          }&user_id=${user?.id.toString()}`
      );
      //page=1&size=""
      if (status === 200) {
        setLoader(false);

        const logsData =
          data?.data &&
          data?.data?.length > 0 &&
          data?.data?.map((a) => {
            let newdata = a?.new_data && JSON.parse(a?.new_data);
            let previousdata = a?.previous_data && JSON.parse(a?.previous_data);
            if (a?.activity_action !== 'created' && newdata && previousdata) {
              var diffs = findDifferences(previousdata, newdata);
            }
            return {
              ...a,
              differenceData:
                a?.activity_action !== 'created' ? diffs : newdata,
              new_data: newdata,
              previous_data: previousdata,
            };
          });
        //  const tempData = data?.data;
        //  setTemplateData((prevData) => [...prevData, ...logsData]);
        setHasMore(logsData?.length > 20);
        setLoader(false);
        logsData?.length > 0 &&
          setUserLogs((prevData) => [...prevData, ...logsData]);
      }
    } catch (error) {
      setLoader(false);
      console.error(error);
      setUserLogs([]);
    }
  };
  const fetchMoreData = () => {
    setPageLogs((prevPage) => prevPage + 1);
  };
  useEffect(() => {
    UserLogs && UserLogs?.length > 0 && getStaffLogs(UserData, pageLogs);
  }, [pageLogs]);
  const columns = [
    {
      field: 'id',
      // headerName: 'ID',
      headerName: (
        <Box className="d-flex align-center justify-start">
          <Box className="d-flex justify-start">
            <Checkbox
              className="table-checkbox pl0"
              indeterminate={
                selectedCat?.length > 0 &&
                selectedCat?.length < userList?.length
              }
              checked={
                folderData?.length > 0 &&
                selectedCat?.length === userList?.length
              }
              onClick={(event) => handleSelectAllClick(event)}
              // checked={selectedCat?.indexOf(params?.value) !== -1}
            />
          </Box>
          <Typography className="title-text cursor-pointer fw600">
            {'ID'}
          </Typography>
        </Box>
      ),
      width: 80,
      minWidth: 80,
      flex: 0,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-start h100">
            <Box className="d-flex align-center">
              <Checkbox
                className="table-checkbox pl0"
                onClick={(event) => handleCheck(event, params?.value)}
                checked={selectedCat?.indexOf(params?.value) !== -1}
              />
            </Box>
            <Typography className="title-text cursor-pointer">
              {params?.value}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'user_full_name',
      headerName: 'Staff Name',
      width: 260,
      minWidth: 260,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <CommonUserDetails
            userData={params?.row}
            page={page}
            rowsPerPage={rowsPerPage}
            setUserdata={setUserdata}
          />
        );
      },
    },
    {
      field: 'branch?.branch_name',
      headerName: 'Branch / Dep.',
      width: 200,
      minWidth: 200,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return <BranchDepartmentDisplay row={params?.row} />;
      },
    },
    {
      field: 'last_view',
      headerName: 'Last View',
      width: 200,
      minWidth: 200,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center  h100 gap-sm">
            <Typography className="title-text  text-ellipsis">
              {params?.value ? DateFormat(params?.value, 'dates') : '-'}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'total_items',
      headerName: 'Staff track',
      width: 200,
      minWidth: 200,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center  h100 gap-sm">
            <Typography className="title-text  text-ellipsis">
              {/* <span>{params?.value + '/100'}</span> */}

              <span>{params?.row?.total_track + '/' + params?.value}</span>
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 150,
      minWidth: 150,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex justify-center ">
            <Box className="d-flex actions-staff  ">
              <Tooltip
                title={<Typography>Reset track</Typography>}
                arrow
                classes={{
                  tooltip: 'info-tooltip-container ',
                }}
              >
                {/* <div className="action-icon"> */}
                <Box>
                  <ResetIcon
                    className="cursor-pointer"
                    onClick={() => {
                      ResetDoctMedia(params?.row?.id);
                    }}
                  />
                </Box>

                {/* </div> */}
              </Tooltip>
              <Tooltip
                title={<Typography>Track logs</Typography>}
                arrow
                classes={{
                  tooltip: 'info-tooltip-container ',
                }}
              >
                {/* <div className="action-icon"> */}
                <Box>
                  <ViewIcon
                    className="cursor-pointer"
                    onClick={() => {
                      getStaffLogs(params?.row, 1);
                      setUserData(params?.row);
                      setToggleModal(true);
                    }}
                  />
                </Box>

                {/* </div> */}
              </Tooltip>
            </Box>
          </Box>
        );
      },
    },
  ];

  const onPageChange = (newPage) => {
    setPage(newPage);
    getStatisticsUserList(newPage, selected);
  };
  const OnRowPerPage = (newPage) => {
    setRowsPerPage(newPage);
    setPage(1);
    getStatisticsUserList(1, selected, newPage);
  };
  const logBookValues = {
    activity_action: 'activity',
    updatedAt: 'at',
    document_category_item_track_status: 'track status',
  };

  const logbookKey = [
    'activity_action',
    'document_category_item_track_status',
    'updatedAt',
  ];
  const LogBook = (key, value) => {
    return (
      <>
        {key === 'updatedAt' ? (
          <> {/* <span className="fw600">Updated</span> //Updated */}</>
        ) : logBookValues[key] &&
          value?.newValue &&
          value?.newValue !== null ? (
          <>
            <span className="fw600 ">
              {key && logBookValues[key] ? logBookValues[key] : ''}
            </span>
            <span className="fw600 ">{' : '}</span>

            <span className=" fw400">{value?.newValue}</span>
          </>
        ) : (
          <></>
        )}
      </>
    );
  };
  return (
    <Box className="User-track-section">
      <Box className="User-track-folders">
        <Box
          className={
            CategoryDetails?.id === selected
              ? 'folder-content selected-folder-content cursor-pointer'
              : 'folder-content cursor-pointer'
          }
          onClick={() => {
            setSelected(CategoryDetails?.id);
            getStatisticsUserList(page, CategoryDetails?.id, null, true);
          }}
        >
          <Box className="list-view-folder">
            <FolderDocIcon className="sidebar-list-icon" />
          </Box>
          <Typography className="title-text fw400 link-text-name text-ellipsis-line">
            All
          </Typography>
          {/* {index === 2 && <CancelIcon className="cross-icon" />} */}
        </Box>
        {folderData &&
          folderData?.length > 0 &&
          folderData?.map((item, index) => {
            return (
              <Box
                key={index}
                className={
                  item?.id === selected
                    ? 'folder-content selected-folder-content cursor-pointer'
                    : 'folder-content cursor-pointer'
                }
                onClick={() => {
                  setSelected(item?.id);
                  getStatisticsUserList(page, item?.id);
                }}
              >
                {/* {item?.filetype === 'pdf' ? (
                  <PdfIcon className="list-view-folder" />
                ) : item?.filetype === 'doc' ? (
                  <DocIcon className="list-view-folder" />
                ) : item?.filetype === 'xls' ? (
                  <XlsIcon className="list-view-folder" />
                ) : (
                  <FolderIcon className="list-view-folder" />
                )} */}
                {item?.category_type === 'file' ? (
                  <Box className="list-view-file">
                    {/* <LazyLoadImage
                      src={item?.category_image_url}
                      alt="not found"
                      className="list-view-file-image"
                    /> */}
                    <HeaderImage
                      imageUrl={item?.category_image_url}
                      alt="not found"
                      className="list-view-file-image"
                      type="lazyload"
                    />
                  </Box>
                ) : (
                  <Box className="list-view-folder">
                    <FolderDocIcon className="sidebar-list-icon" />
                  </Box>
                )}
                <Typography className="title-text fw400 link-text-name text-ellipsis-line">
                  {item?.category_name}
                </Typography>
                {/* {index === 2 && <CancelIcon className="cross-icon" />} */}
              </Box>
            );
          })}
      </Box>
      {selectedCat && selectedCat?.length > 0 && (
        <Box className="document-all-action">
          <Box className="document-action-tooltip">
            <Box className="action-section selected-category">
              <CancelIcon
                onClick={() => {
                  setSelectedCat([]);
                }}
              />
              <Typography className="body-text">
                {selectedCat?.length + ' selected'}
              </Typography>
            </Box>
            <Box className="action-section action-reset">
              <Tooltip
                title={<Typography>Reset</Typography>}
                classes={{
                  tooltip: 'info-tooltip-container ',
                }}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="icon icon-tabler icon-tabler-rotate-clockwise"
                  width="22"
                  height="22"
                  viewBox="0 0 24 24"
                  stroke-width="2"
                  stroke="#2c3e50"
                  fill="none"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  onClick={() => {
                    setResetTrack(true);
                    setResetTrackData(selectedCat);
                  }}
                >
                  <path d="M4.05 11a8 8 0 1 1 .5 4m-.5 5v-5h5" />
                </svg>
              </Tooltip>
            </Box>
            {/*  <Box className="action-section">
              <Tooltip title="Copy">
                <ContentCopyIcon
                  className="svg-icon"
                  onClick={() => {
                    setCreateModal(true);
                    setCreateModalTitle({
                      title: 'Copy Items',
                      id: selectedCat
                    });
                  }}
                />
              </Tooltip>
            </Box> */}
          </Box>
        </Box>
      )}
      <Box>
        {userList && userList?.length === 0 ? (
          <Box className="no-data d-flex align-center justify-center">
            <NoDataView
              title="No User Data Found"
              description="There is no user data available at the moment."
            />
          </Box>
        ) : (
          <>
            {' '}
            <Box className="table-container table-layout">
              <DataGrid
                rows={userList}
                columns={columns}
                pageSize={rowsPerPage}
                checkboxSelection={false} // Disable default checkbox column
                disableSelectionOnClick // Disable row selection on click
                // selectedRows={selectedRows}
                hideMenuIcon
                getRowHeight={() => 'auto'}
                sx={{
                  [`& .${gridClasses.cell}`]: {
                    py: 1,
                  },
                }}
              />
              <CustomPagination
                currentPage={page}
                OnRowPerPage={OnRowPerPage}
                totalCount={totalCount}
                rowsPerPage={rowsPerPage}
                onPageChange={onPageChange}
              />
            </Box>
          </>
        )}
      </Box>
      <DialogBox
        open={toggleModal}
        handleClose={() => {
          setToggleModal(!toggleModal);
        }}
        title="User Logs"
        className="staff-dialogbox dialog-box-container"
        content={
          <>
            <Box
              id="scrollableSidebar"
              style={{
                height: 'calc(100vh - 150px - var(--banner-height))',
                overflow: 'auto',
              }}
            >
              <InfiniteScroll
                dataLength={UserLogs?.length}
                next={fetchMoreData}
                hasMore={hasMore}
                loader={<Typography>Loading more logs...</Typography>}
                scrollableTarget="scrollableSidebar"
              >
                {loader ? (
                  <Box className="content-loader">
                    <CircularProgress className="loader" color="inherit" />
                  </Box>
                ) : UserLogs && UserLogs?.length > 0 ? (
                  <Box className="User-track-logs">
                    {/* <Box className="folder-desc-divider" />
                <Typography className="title-sm fw600">Log Book</Typography> */}
                    <ul className="tree">
                      <ul>
                        {UserLogs &&
                          UserLogs?.map((list, i) => (
                            <li className="list-view">
                              {/* {list?.id}
                              {codnsolef.log(
                                list?.id,
                                list?.differenceData,
                                'fddddddd'
                              )} */}
                              <div key={i} className="list signature-list">
                                <Box className="Image-profile">
                                  {list && list?.user_avatar_link ? (
                                    <Box className="profile-image">
                                      {/* <LazyLoadImage
                                        src={list?.user_avatar_link}
                                        className="profile cursor-pointer"
                                        style={{ marginRight: '4px' }}
                                        alt="not found"
                                        onClick={() => {
                                          router.push(
                                            `/user/${list?.user_invite?.id}?IsInvite=true`
                                          );
                                        }}
                                      /> */}
                                      <HeaderImage
                                        imageUrl={list?.user_avatar_link}
                                        className="profile cursor-pointer"
                                        style={{ marginRight: '4px' }}
                                        alt="not found"
                                        onClick={() => {
                                          router.push(
                                            `/user/${list?.user_invite?.id}?IsInvite=true`
                                          );
                                        }}
                                        type="lazyload"
                                      />
                                    </Box>
                                  ) : (
                                    <Avatar
                                      className="profile-image profile-icon cursor-pointer"
                                      {...stringAvatar(list?.user_full_name)}
                                      onClick={() => {
                                        router.push(
                                          `/user/${list?.user_invite?.id}?IsInvite=true`
                                        );
                                      }}
                                    />
                                  )}
                                </Box>
                                <Typography className="title-text text-capitalize fw600">
                                  {list?.user_full_name}
                                  {list?.updatedAt || list?.createdAt
                                    ? ', '
                                    : ''}
                                  {list?.updatedAt ? (
                                    <span className="fw400 sub-title-text">
                                      {moment(list?.updatedAt).format(
                                        'DD/MM/YYYY hh:mm A'
                                      )}
                                    </span>
                                  ) : list?.createdAt ? (
                                    <span className="fw400 sub-title-text">
                                      {moment(list?.createdAt).format(
                                        'DD/MM/YYYY hh:mm A'
                                      )}
                                    </span>
                                  ) : (
                                    <></>
                                  )}
                                </Typography>

                                {list?.activity_action === 'created' ? (
                                  <Box className="sign-activity sub-title-text">
                                    {Object.entries(list?.new_data) &&
                                      Object.entries(list?.new_data)
                                        ?.filter(([key]) =>
                                          logbookKey?.includes(key)
                                        )
                                        ?.map(([key, value], index) => {
                                          const isLast =
                                            Object.entries(
                                              list?.new_data
                                            ).filter(([key]) =>
                                              logbookKey?.includes(key)
                                            ).length -
                                              1 >
                                            index;
                                          if (
                                            key &&
                                            logBookValues[key] &&
                                            value !== null
                                          ) {
                                            return (
                                              <>
                                                {index === 0 && (
                                                  <span className="fw600">
                                                    {'Created '}
                                                  </span>
                                                )}
                                                {key === 'updatedAt' ? (
                                                  <></>
                                                ) : (
                                                  <>
                                                    <span className="fw600 ">
                                                      {logBookValues[key]}
                                                    </span>
                                                    <span className="fw600">
                                                      {' : '}
                                                    </span>
                                                    <span className=" fw400">
                                                      {value}
                                                    </span>
                                                    {isLast &&
                                                      Object.entries(
                                                        list?.new_data
                                                      ).filter(([key]) =>
                                                        logbookKey?.includes(
                                                          key
                                                        )
                                                      ).length !== 2 &&
                                                      logbookKey?.includes(
                                                        'updatedAt'
                                                      ) && (
                                                        <span className="fw400">
                                                          {', '}
                                                        </span>
                                                      )}
                                                  </>
                                                )}
                                              </>
                                            );
                                          }
                                        })}
                                  </Box>
                                ) : list?.differenceData &&
                                  Object.keys(list?.differenceData).length >
                                    0 ? (
                                  <Box className="sign-activity sub-title-text">
                                    {Object.entries(list?.differenceData) &&
                                      Object.entries(list?.differenceData)
                                        .filter(([key]) =>
                                          logbookKey?.includes(key)
                                        )
                                        ?.map(([key, value], index) => {
                                          const isLast =
                                            Object.entries(
                                              list?.differenceData
                                            ).filter(([key]) =>
                                              logbookKey?.includes(key)
                                            ).length -
                                              1 >
                                            index;

                                          return (
                                            <>
                                              {index === 0 && (
                                                <span className="fw600">
                                                  {'Updated '}
                                                </span>
                                              )}
                                              {LogBook(key, value)}
                                              {isLast &&
                                                Object.entries(
                                                  list?.differenceData
                                                ).filter(([key]) =>
                                                  logbookKey?.includes(key)
                                                ).length !== 2 &&
                                                logbookKey?.includes(
                                                  'updatedAt'
                                                ) && (
                                                  <span className="fw400">
                                                    {', '}
                                                  </span>
                                                )}
                                            </>
                                          );
                                        })}
                                  </Box>
                                ) : list?.activity_action ? (
                                  <>
                                    <Box className="sign-activity sub-title-text">
                                      <span className="fw600">{'Reset'}</span>
                                    </Box>
                                  </>
                                ) : (
                                  <></>
                                )}
                              </div>
                            </li>
                          ))}
                      </ul>
                    </ul>
                  </Box>
                ) : (
                  <Box className="no-data d-flex align-center justify-center">
                    <NoDataView
                      title="No User Logs Found"
                      description="There is no user logs available at the moment."
                    />
                  </Box>
                )}
              </InfiniteScroll>
            </Box>
          </>
        }
      />
      <DialogBox
        open={resetTrack}
        handleClose={() => {
          handleClose();
        }}
        className="confirmation-modal"
        dividerClass="confirmation-modal-divider"
        title="Confirmation"
        content={
          <>
            <ConfirmationModal
              handleCancel={handleClose}
              handleConfirm={handleResetTrack}
              text={`Are you sure you want to reset the track?`}
            />
          </>
        }
      />
    </Box>
  );
};

export default FolderUserTrack;
