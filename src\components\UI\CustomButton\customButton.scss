@import '../../../styles/variable.scss';
.custom-button-wrapper {
  .custom-btn {
    text-transform: none;
    font-weight: var(--font-weight-medium);
    border-radius: var(--border-radius-md);
    padding: var(--btn-padding-sm);
    transition: all 0.3s ease-in-out;
    font-family: var(--font-family-primary);
    box-shadow: none;
    font-size: var(--font-size-sm);
    line-height: var(--line-height-xs);
    &.Mui<PERSON>utton-outlined {
      border-width: 1px;
      border-color: var(--btn-color-secondary);
      color: var(--btn-color-primary);
      box-shadow: 0px 1px 2px 0px #16171614;
      &:hover {
        border-width: 1px;
        background-color: var(--color-white);
      }
    }

    &.MuiButton-contained {
      background: var(--btn-color-primary);
      color: var(--btn-text-color-white);
      border: 1px solid var(--btn-color-primary);
      &:hover {
        box-shadow: none;
      }
    }
    &.Mui-disabled {
      opacity: 0.7;
      cursor: not-allowed;
    }

    // Size variants
    &.MuiButton-sizeSmall {
      padding: 4px 12px;
      font-size: 0.875rem;
    }

    &.MuiButton-sizeLarge {
      padding: 12px 24px;
      font-size: 1.125rem;
    }

    // Icon spacing
    .MuiButton-startIcon {
      margin-right: var(--spacing-xs);
      svg {
        font-size: var(--icon-size-xsm);
        width: var(--icon-size-xsm);
        height: var(--icon-size-xsm);
      }
    }

    .MuiButton-endIcon {
      margin-left: 8px;
    }
  }
  .icon-only {
    min-width: 50px;
    width: 100%;
    padding: var(--btn-icon-padding-sm);
    background: var(--color-white) !important;
    color: var(--btn-color-primary) !important;
    .MuiButton-startIcon {
      margin-right: var(--spacing-none);
      margin-left: var(--spacing-none);
    }
  }
}
.green-button {
  background: var(--color-green) !important;
  color: var(--btn-text-color-white) !important;
  border: 1px solid var(--color-green) !important;
  &:hover {
    box-shadow: none;
  }
}
.red-button {
  background: var(--color-danger) !important;
  color: var(--btn-text-color-white) !important;
  border: 1px solid var(--color-danger) !important;
  &:hover {
    box-shadow: none;
  }
}
