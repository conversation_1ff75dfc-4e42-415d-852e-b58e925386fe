@import '@/styles/variable.scss';

.verify-form-wrap {
  position: relative;
  padding: 0px 20px;

  .verify-container {
    width: 100%;
    max-width: 570px;
    margin: 0 auto;

    .verify-number-wrap {
      background-color: var(--color-white);
      min-height: 550px;
      border-top-left-radius: 22px;
      border-top-right-radius: 22px;
      padding: 52px;
      box-shadow: 0px 0px 110px 0px #0260e133;
      position: relative;
      z-index: 1;

      .heading-wrap {
        margin-bottom: 22px;
      }

      .input-wrap {
        gap: 25px;
        margin: 50px 0px;

        .opt-input {
          .MuiInputBase-root {
            height: 70px;
            width: 80px;
            border-radius: 12px;
            border: 1px solid #e2e2ea;
            background-color: var(--color-white);

            .MuiInputBase-input {
              padding: 0px 18px 9px 18px;
              font-size: 36px;
              line-height: 42px;
              font-weight: 600;
              text-align: center;
              color: #1d1e25;
              vertical-align: middle;
              font-family: var(--font-family-poly-median) !important;

              &::placeholder {
                font-family: var(--font-family-poly-median) !important;
                font-size: 36px;
                line-height: 42px;
                font-weight: 600;
                text-align: center;
                vertical-align: middle;
              }
            }

            &::before {
              border-bottom: none !important;
            }

            &::after {
              border-bottom: none !important;
            }

            @media (max-width: 474px) {
              height: 50px;
              width: 55px;
            }
          }
        }

        @media (max-width: 475px) {
          gap: 10px;
        }
      }

      .verify-btn-wrap {
        margin: 32px 0px 22px;

        .verify-btn {
          border-radius: 12px !important;
          background-color: var(--color-primary) !important;
          font-size: 16px !important;
          line-height: 20px !important;
          font-weight: 300 !important;
          padding: 16px !important;
          border: none !important;
          color: var(--text-color-white) !important;
          font-family: var(--font-family-poly-bulky) !important;

          &:hover {
            background-color: var(--color-primary) !important;
            color: var(--text-color-white) !important;
            box-shadow: none !important;
          }
        }
      }

      .code-text-wrap {
        font-size: 16px;
        line-height: 26px;
        font-weight: 300;
        color: var(--text-lavender-gray);
        text-align: center;
        font-family: var(--font-family-poly-slim) !important;

        .try-again-wrap {
          font-weight: 300;
          color: var(--text-color-primary) !important;
          margin-left: 6px;
          font-family: var(--font-family-poly-bulky) !important;
        }
        .timer-wrap {
          color: var(--text-color-primary);
        }
      }

      @media (max-width: 767px) {
        min-height: 100%;
      }

      @media (max-width: 575px) {
        padding: 35px 20px;
      }
    }
  }

  .left-vector {
    position: absolute;
    top: 140px;
    left: 110px;
    z-index: 0;

    @media (max-width: 1199px) {
      left: 45px;
    }

    @media (max-width: 767px) {
      display: none;
    }
  }

  .right-vector {
    position: absolute;
    top: 140px;
    right: 165px;
    z-index: 0;

    @media (max-width: 1199px) {
      right: 70px;
    }

    @media (max-width: 767px) {
      display: none;
    }
  }

  @media (max-width: 575px) {
    padding: 0px 14px;
  }
}
