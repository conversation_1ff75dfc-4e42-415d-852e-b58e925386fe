'use client';
import React, { useEffect, useState, useRef } from 'react';
import {
  Box,
  Typography,
  Radio,
  Tooltip,
  CircularProgress,
} from '@mui/material';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import CustomButton from '@/components/UI/CustomButton';
// import CustomSelect from '@/components/UI/selectbox';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { ORG_URLS } from '@/helper/constants/urls';
import { setApiMessage } from '@/helper/common/commonFunctions';
import DiscountCode from '@/components/PaymentMethod/DiscountCode';
import OfflinePinIcon from '@mui/icons-material/OfflinePin';
import AddNewCard from '@/components/PaymentMethod/AddCards/AddNewCard';
import EditOutlinedIcon from '@mui/icons-material/EditOutlined';
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined';
import DialogBox from '@/components/UI/Modalbox';
import DeleteModal from '@/components/UI/DeleteModal';
import './paynow.scss';

export default function PayNow({
  paymentProvider,
  selectedPlan,
  PurchasedPlan,
  plansList,
}) {
  const [cardData, setCardData] = useState([]);
  const [defaultCard, setDefaultCard] = useState(null);
  const [selectedCardToEdit, setSelectedCardToEdit] = useState(null);
  const [showAddCardModal, setShowAddCardModal] = useState(false); // State for showing AddNewCard
  const [deleteCardModal, setDeleteCardModal] = useState(false);
  const [deleteCardData, setDeleteCardData] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  const formikRef = useRef(null);
  const getAllCads = async () => {
    try {
      const { status, data } = await axiosInstance.get(ORG_URLS?.GET_ALL_CARDS);
      if (status === 200) {
        setCardData(data?.data || []);
        setIsLoading(false);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    } finally {
      setIsLoading(false); // done loading
    }
  };

  const validationSchema = Yup.object().shape({
    selectedCard: Yup.string().required('Please select a card'),
    plan: Yup.string().required('Please select a Plan'),
    userCard: Yup.string().required('Please select a card type'),
  });

  const handleSubmit = (values, { resetForm }) => {
    PurchasedPlan(values);
    resetForm();
  };

  const handleEditCard = (card) => {
    setSelectedCardToEdit(card);
    setShowAddCardModal(true);
  };

  const handleRemoveCard = async () => {
    try {
      const { data, status } = await axiosInstance.put(
        ORG_URLS?.UPDATE_CARD + `/${deleteCardData?.id}`,
        {
          status: 'deleted',
        }
      );
      if (status === 200) {
        if (data?.status) {
          setApiMessage('success', data?.message);
          getAllCads();
          setDeleteCardModal(false);
          setDeleteCardData(null);
        } else {
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const handleDefaultCard = async (card) => {
    try {
      const { data, status } = await axiosInstance.put(
        ORG_URLS?.UPDATE_CARD + `/${card?.id}`,
        {
          isDefault: true,
        }
      );
      if (status === 200) {
        if (data?.status) {
          setApiMessage('success', data?.message);
          getAllCads();
        } else {
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  useEffect(() => {
    getAllCads();
  }, []);
  useEffect(() => {
    formikRef.current.setFieldValue(
      'plan',
      selectedPlan?.id ? selectedPlan?.id : ''
    );
  }, [selectedPlan]);
  useEffect(() => {
    if (cardData?.length > 0 && !defaultCard) {
      const defaultCard = cardData?.find((f) => f?.card_is_default);
      defaultCard && setDefaultCard(defaultCard?.id);
      formikRef.current.setFieldValue(
        'selectedCard',
        defaultCard ? defaultCard?.id : ''
      );
    }
  }, [cardData]);
  useEffect(() => {
    if (paymentProvider && paymentProvider?.length > 0) {
      formikRef.current.setFieldValue(
        'userCard',
        paymentProvider?.[0] && paymentProvider?.[0]?.value
          ? paymentProvider?.[0]?.value
          : ''
      );
    }
  }, [paymentProvider?.length, formikRef]);
  return (
    <Box>
      {isLoading && (
        <Box className="content-loader mt16">
          <CircularProgress className="loader" color="inherit" />
        </Box>
      )}
      <Formik
        innerRef={formikRef}
        enableReinitialize
        initialValues={{
          selectedCard: defaultCard || '',
          userCard:
            paymentProvider?.[0] && paymentProvider?.[0]?.value
              ? paymentProvider?.[0]?.value
              : '',
        }}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ touched, errors, values, setFieldValue, handleSubmit }) => (
          <Form onSubmit={handleSubmit}>
            <Box className="add-card-container">
              {/* <Box className="add-card-header d-flex justify-space-between align-center">
                <Typography
                      variant="h6"
                      className="Payment Provider org-sub-header-text"
                    >
                      Payment Provider
                    </Typography>
                <CustomSelect
                name="userCard"
                placeholder="Payment provider"
                className="selected-wrap card-select"
                options={paymentProvider}
                value={values?.userCard}
                onChange={(e) => {
                  setFieldValue('userCard', e?.target?.value);
                }}
              />
              </Box> */}
              {cardData && cardData?.length > 0 ? (
                <Box className="add-cards d-flex flex-col">
                  {cardData?.map((card, index) => (
                    <Box
                      key={index}
                      className="add-card-wrap d-flex justify-space-between align-center"
                    >
                      <Box className="card-content-wrap d-flex gap-20 align-center">
                        <Radio
                          name="selectedCard"
                          checked={values?.selectedCard === card?.id}
                          onChange={() =>
                            setFieldValue('selectedCard', card?.id)
                          }
                          className="card-radio"
                        />
                        <Box className="card-content w100">
                          <Typography variant="body1" className="card-number">
                            **** **** **** {card.card_last4}
                          </Typography>
                          <Box className="d-flex align-center card-type-edit-wrap">
                            <Typography variant="body2" className="card-type">
                              {card.card_brand}
                            </Typography>
                            <Typography
                              className="edit-wrap edit"
                              component="p"
                              onClick={() => handleEditCard(card)}
                            >
                              <EditOutlinedIcon className="edit-wrap-icon" />
                              Edit
                            </Typography>
                            {card?.card_is_default !== 1 && (
                              <Typography
                                className="edit-wrap remove"
                                component="p"
                                onClick={() => {
                                  setDeleteCardData(card);
                                  setDeleteCardModal(true);
                                }}
                              >
                                <DeleteOutlineOutlinedIcon className="edit-wrap-icon" />
                                Remove
                              </Typography>
                            )}

                            {card?.card_is_default !== 1 && (
                              <Typography
                                className="edit-wrap default"
                                component="p"
                                onClick={() => handleDefaultCard(card)}
                              >
                                Set as default
                              </Typography>
                            )}
                          </Box>
                        </Box>
                      </Box>
                      <Box className="card-brand-wrap">
                        {card?.card_is_default === 1 && (
                          <Box className="d-flex justify-end">
                            <Tooltip
                              title={<Typography>Default Card</Typography>}
                              classes={{ tooltip: 'info-tooltip-container' }}
                            >
                              <OfflinePinIcon className="default-card" />
                            </Tooltip>
                          </Box>
                        )}
                        <Typography
                          component="p"
                          className="card-brand body-sm-regular"
                        >
                          {card?.cardType}
                        </Typography>
                      </Box>
                    </Box>
                  ))}
                </Box>
              ) : (
                <Box className="">
                  <AddNewCard
                    cardData={cardData}
                    cardToEdit={selectedCardToEdit}
                    setShowAddCardModal={setShowAddCardModal}
                    getAllCads={getAllCads}
                    setSelectedCardToEdit={setSelectedCardToEdit}
                    isClose={false}
                  />
                </Box>
              )}
              {cardData?.length > 0 && (
                <Box className="add-new-btn-wrap mt24 mb24 d-flex justify-end">
                  <CustomButton
                    variant="outlined"
                    className=""
                    title="Add new card"
                    // disabled={selectedCardToEdit}
                    onClick={() => {
                      setShowAddCardModal(true);
                      setSelectedCardToEdit(null);
                    }}
                  />
                </Box>
              )}

              {showAddCardModal && (
                <AddNewCard
                  cardData={cardData}
                  cardToEdit={selectedCardToEdit}
                  setShowAddCardModal={setShowAddCardModal}
                  getAllCads={getAllCads}
                  setSelectedCardToEdit={setSelectedCardToEdit}
                  isClose={true}
                />
              )}

              {touched.selectedCard && errors.selectedCard ? (
                <Typography
                  variant="body2"
                  color="error"
                  className="field-error pb16 ml0"
                >
                  {errors.selectedCard}
                </Typography>
              ) : touched.plan && errors.plan ? (
                <Typography
                  variant="body2"
                  color="error"
                  className="field-error pb16 ml0"
                >
                  {errors.plan}
                </Typography>
              ) : (
                touched.userCard &&
                errors.userCard && (
                  <Typography
                    variant="body2"
                    color="error"
                    className="field-error pb16 ml0"
                  >
                    {errors.userCard}
                  </Typography>
                )
              )}
            </Box>
            <DiscountCode
              planDiscount={selectedPlan}
              handleSubmitForm={handleSubmit}
              plansList={plansList}
            />
          </Form>
        )}
      </Formik>
      {deleteCardModal && (
        <DialogBox
          open={deleteCardModal}
          handleClose={() => {
            setDeleteCardModal(false);
            setDeleteCardData(null);
          }}
          className="delete-modal"
          title="Delete card"
          dividerClass="delete-modal-divider"
          content={
            <DeleteModal
              handleCancel={() => {
                setDeleteCardModal(false);
                setDeleteCardData(null);
              }}
              handleConfirm={() => handleRemoveCard()}
              text="Are you sure you wish to delete this card?"
            />
          }
        />
      )}
    </Box>
  );
}
