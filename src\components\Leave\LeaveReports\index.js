'use client';
import React, { useContext, useEffect, useState } from 'react';
import { Box, Popover, Tooltip, Typography } from '@mui/material';
import LeaveBalance from '@/components/Leave/LeaveReports/LeaveBalance';
import LeaveUsage from '@/components/Leave/LeaveReports/LeaveUsage';
import LeaveConsumption from '@/components/Leave/LeaveReports/LeaveConsumption';
import DownloadIcon from '@mui/icons-material/Download';
import AuthContext from '@/helper/authcontext';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import DatePicker from 'react-multi-date-picker';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import CustomTabs from '@/components/UI/CustomTabs';
import CustomButton from '@/components/UI/CustomButton';
import moment from 'moment';
import dayjs from 'dayjs';
import './leavereports.scss';

export default function LeaveReports() {
  const [tab, setTab] = useState(1);
  const [leaveConsuptionData, setLeaveConsuptionData] = useState([]);
  const [roleList, setRoleList] = useState([]);
  const [leaveBalance, setLeaveBalance] = useState([]);
  const [loaderBal, setLoaderBal] = useState(true);
  const [loaderCon, setLoaderCon] = useState(true);
  const [loaderUse, setLoaderUse] = useState(true);
  const [leaveBalanceRowsPerPage, setLeaveBalanceRowsPerPage] = useState(10);
  const [leavePage, setLeavePage] = useState(1);
  const [leaveCurrentPage, setLeaveCurrentPage] = useState(1);
  const [leaveTotalCount, setLeaveTotalCount] = useState(0);
  const [anchorEl, setAnchorEl] = useState(null);
  const [leaveCunsRowsPerPage, setLeaveCunsRowsPerPage] = useState(10);
  const [leaveCunsPage, setLeaveCunsPage] = useState(1);
  const [leaveCunsTotalCount, setLeaveCunsTotalCount] = useState();
  const [leaveUsageData, setLeaveUsageData] = useState([]);
  const [usageTotalCount, setUsageTotalCount] = useState();
  const [sortByDays, setSortByDays] = useState('');
  const { authState } = useContext(AuthContext);
  const { AllListsData } = useContext(AuthContext);
  const [leaveTypeList, setLeaveTypeList] = useState();
  const leave_type = authState?.generalSeetings?.leave_period_type
    ? authState?.generalSeetings?.leave_period_type
    : 'day';
  const reports_tabs = [
    { id: 1, name: 'Leave Balance' },
    // { id: 2, name: 'Leave Usage' },
    { id: 3, name: 'Leave Consumption' },
  ];
  const BM_Reports_tabs = [
    { id: 1, name: 'Leave Balance' },
    { id: 3, name: 'Leave Consumption' },
  ];
  const handleTabChange = (newValue) => {
    // const selectedTab = reports_tabs[newValue];
    setTab(newValue);
  };

  const reportsTab =
    authState?.web_user_active_role_id === 7 ||
    authState?.web_user_active_role_id === 14 ||
    authState?.web_user_active_role_id === 1
      ? BM_Reports_tabs
      : reports_tabs;
  // const currentYear = moment().year();
  const [leaveFilterData, setLeaveFilterData] = useState({
    searchValue: '',
    branch: '',
    department: '',
    role: '',
    year: '',
    leavetype: leave_type,
  });
  const [leavefilterDataApplied, setLeaveFilterDataApplied] = useState({
    searchValue: '',
    branch: '',
    department: '',
    role: '',
    year: '',
    leavetype: leave_type,
  });
  const [consuFilterData, setConsuFilterData] = useState({
    leaveType: '',
    branch: '',
    department: '',
    role: '',
    searchValue: '',
    year: '',
    leavetype: leave_type,
  });
  const [consuFilterDataApplied, setConsuFilterDataApplied] = useState({
    leaveType: '',
    branch: '',
    department: '',
    role: '',
    searchValue: '',
    year: '',
    leavetype: leave_type,
  });
  const [usageFilterData, setUsageFilterData] = useState({
    year: '',
  });
  const [usageFilterDataApplied, setUsageFilterDataApplied] = useState({
    year: '',
  });
  const [yearFilterdata, setYearFilterData] = useState('');

  let calenderStarDate = yearFilterdata[0]
    ? dayjs(yearFilterdata[0])?.format('YYYY-MM-DD')
    : '';
  let calenderEndDate = yearFilterdata[1]
    ? dayjs(yearFilterdata[1])?.format('YYYY-MM-DD')
    : '';
  const open = Boolean(anchorEl);

  const id = open ? 'simple-popper' : undefined;

  const handleClick = (event) => {
    setAnchorEl(anchorEl ? null : event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const getLeaveBalanceList = async (
    leavePage,
    searchValue,
    filter,
    Rpp,
    leavetype
  ) => {
    setLoaderBal(true);
    try {
      const { status, data } = await axiosInstance.get(
        `${URLS?.GET_LEAVE_BALANCE}?branch_id=${filter?.branch}&department_id=${
          filter?.department
        }&role_id=${filter?.role}&search=${filter?.searchValue}&page=${leavePage}&size=${
          Rpp ? Rpp : leaveBalanceRowsPerPage
        }&report_mode=${leavetype ? leavetype : filter?.leavetype}&start_date=${calenderStarDate}&end_date=${calenderEndDate}`
      );

      if (status === 200) {
        setLeaveBalance(data?.data);
        setLeaveTotalCount(data?.count);
        setLoaderBal(false);
      }
    } catch (error) {
      setLoaderBal(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const getLeaveBalanceListDownload = async (filter, format) => {
    try {
      const { status, data } = await axiosInstance.get(
        `${URLS?.GET_LEAVE_BALANCE}?branch_id=${filter?.branch}&department_id=${
          filter?.department
        }&role_id=${filter?.role}&report_mode=${filter?.leavetype}&search=${filter?.searchValue}&page=&size=&download=${format}&start_date=${calenderStarDate}&end_date=${calenderEndDate}`,
        {
          responseType: 'blob',
        }
      );

      if (status === 200) {
        const url = window.URL.createObjectURL(new Blob([data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute(
          'download',
          `leave_balance.${format === 'excel' ? 'xlsx' : format}`
        );
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        setLoaderBal(false);
      }
    } catch (error) {
      setLoaderBal(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const getLeaveUsageList = async () => {
    setLoaderUse(true);
    try {
      const { status, data } = await axiosInstance.get(
        `${URLS?.GET_LEAVE_TYPE_REPORTS}?start_date=${calenderStarDate}&end_date=${calenderEndDate}`
      );

      if (status === 200) {
        setLeaveUsageData(data?.data);
        setUsageTotalCount(data?.count);
        setLoaderUse(false);
      }
    } catch (error) {
      setLoaderUse(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // const getLeaveUsageListDownload = async (format) => {
  //   try {
  //     const { status, data } = await axiosInstance.get(
  //       `${URLS?.GET_LEAVE_TYPE_REPORTS}?download=${format}&start_date=${calenderStarDate}&end_date=${calenderEndDate}`,
  //       {
  //         responseType: 'blob',
  //       }
  //     );

  //     if (status === 200) {
  //       const url = window.URL.createObjectURL(new Blob([data]));
  //       const link = document.createElement('a');
  //       link.href = url;
  //       link.setAttribute(
  //         'download',
  //         `leave_Usage.${format === 'excel' ? 'xlsx' : format}`
  //       );
  //       document.body.appendChild(link);
  //       link.click();
  //       document.body.removeChild(link);
  //       setLoaderUse(false);
  //     }
  //   } catch (error) {
  //     setLoaderUse(false);
  //     setApiMessage('error', error?.response?.data?.message);
  //   }
  // };
  const getLeaveConsuptionList = async (
    leaveCunsPage,
    searchValue,
    filter,
    Rpp,
    leavetype
  ) => {
    setLoaderCon(true);
    try {
      const { status, data } = await axiosInstance.get(
        `${URLS?.GET_LEAVE_CONSUMPTION_REPORTS}?branch_id=${
          filter?.branch
        }&department_id=${filter?.department}&role_id=${
          filter?.role
        }&search=${filter?.searchValue}&leave_type_id=${
          filter?.leaveType
        }&sort=total_used_leave_days:${sortByDays}&page=${leaveCunsPage}&size=${
          Rpp ? Rpp : leaveCunsRowsPerPage
        }&report_mode=${leavetype ? leavetype : filter?.leavetype}&start_date=${calenderStarDate}&end_date=${calenderEndDate}`
      );
      if (status === 200) {
        setLoaderCon(false);
        setLeaveCunsTotalCount(data?.count);
        setLeaveConsuptionData(data?.data);
      }
    } catch (error) {
      setLoaderCon(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const getLeaveConsuptionListDownload = async (filter, format) => {
    try {
      const { status, data } = await axiosInstance.get(
        `${URLS?.GET_LEAVE_CONSUMPTION_REPORTS}?branch_id=${
          filter?.branch
        }&department_id=${filter?.department}&role_id=${
          filter?.role
        }&search=${filter?.searchValue}&report_mode=${filter?.leavetype}&leave_type_id=${
          filter?.leaveType
        }&sort=total_used_leave_days:${sortByDays}&page=&size=&download=${format}&start_date=${calenderStarDate}&end_date=${calenderEndDate}`,
        {
          responseType: 'blob',
        }
      );
      if (status === 200) {
        const url = window.URL.createObjectURL(new Blob([data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute(
          'download',
          `leave_cunsuption.${format === 'excel' ? 'xlsx' : format}`
        );
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        setLoaderCon(false);
      }
    } catch (error) {
      setLoaderCon(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const getRoleList = async () => {
    // setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(URLS?.GET_ROLE_LIST);

      if (status === 200) {
        // setLoader(false);
        let filterUserList = data?.data?.map((user) => ({
          label: user?.role_name,
          value: user?.id,
        }));
        setRoleList(filterUserList);
      }
    } catch (error) {
      setRoleList([]);
      // setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const getLeaveTypeList = async () => {
    // setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(URLS?.GET_LEAVE_TYPE);
      if (status === 200) {
        // setLoader(false);
        let filterUserList = data?.data?.map((user) => ({
          label: user?.name,
          value: user?.id,
        }));
        setLeaveTypeList(filterUserList);
      }
    } catch (error) {
      // setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const formatRange = (value) => {
    const [startDate, endDate] = value ? value.split(' to ') : [null, null];

    const formatDate = (dateStr) => {
      return dateStr ? moment(dateStr, 'YYYY/MM/DD').format('DD-MM-YYYY') : '';
    };

    if (!startDate || !endDate) {
      return formatDate(startDate); // Only show the start date if the end date is not available
    }

    return `${formatDate(startDate)} to ${formatDate(endDate)}`;
  };

  useEffect(() => {
    if (authState?.generalSeetings?.leave_period_type) {
      setLeaveFilterData({
        ...leaveFilterData,
        leavetype: authState?.generalSeetings?.leave_period_type,
      });
      setLeaveFilterDataApplied({
        ...leavefilterDataApplied,
        leavetype: authState?.generalSeetings?.leave_period_type,
      });
      setConsuFilterData({
        ...consuFilterData,
        leavetype: authState?.generalSeetings?.leave_period_type,
      });
      setConsuFilterDataApplied({
        ...consuFilterDataApplied,
        leavetype: authState?.generalSeetings?.leave_period_type,
      });
      setTimeout(() => {
        getLeaveBalanceList(
          leavePage,
          leavefilterDataApplied?.searchValue,
          leaveFilterData,
          '',
          authState?.generalSeetings?.leave_period_type
        );
        getLeaveConsuptionList(
          leaveCunsPage,
          consuFilterDataApplied?.searchValue,
          consuFilterData,
          '',
          authState?.generalSeetings?.leave_period_type
        );
        // getLeaveUsageList(usageFilterData);
      }, 400);
    } else {
      getLeaveBalanceList(
        leavePage,
        leavefilterDataApplied?.searchValue,
        leaveFilterData,
        '',
        'day'
      );
      getLeaveConsuptionList(
        leaveCunsPage,
        consuFilterDataApplied?.searchValue,
        consuFilterData,
        '',
        'day'
      );
      // getLeaveUsageList(usageFilterData);
    }
  }, [authState?.generalSeetings?.leave_period_type]);

  useEffect(() => {
    getRoleList();
    getLeaveTypeList();
  }, []);

  useEffect(() => {
    if (yearFilterdata[0] && yearFilterdata[1]) {
      getLeaveBalanceList(
        leavePage,
        leavefilterDataApplied?.searchValue,
        leaveFilterData
      );
      getLeaveConsuptionList(
        leaveCunsPage,
        consuFilterDataApplied?.searchValue,
        consuFilterData
      );
      // getLeaveUsageList(usageFilterData);
    }
  }, [yearFilterdata]);

  useEffect(() => {
    sortByDays &&
      getLeaveConsuptionList(
        leaveCunsPage,
        consuFilterDataApplied?.searchValue,
        consuFilterData
      );
  }, [sortByDays]);

  const handleDownload = (format) => {
    if (tab === 1) {
      getLeaveBalanceListDownload(leaveFilterData, format);
    } else if (tab === 2) {
      // getLeaveUsageListDownload(format);
    } else if (tab === 3) {
      getLeaveConsuptionListDownload(consuFilterData, format);
    }
    setAnchorEl(null);
  };
  const getCurrentContent = () => {
    switch (tab) {
      case 1:
        return (
          <LeaveBalance
            leaveFilterData={leaveFilterData}
            setLeaveFilterData={setLeaveFilterData}
            leavefilterDataApplied={leavefilterDataApplied}
            setLeaveFilterDataApplied={setLeaveFilterDataApplied}
            getLeaveBalanceList={getLeaveBalanceList}
            branchList={AllListsData?.ActiveBranchList}
            departmentList={AllListsData?.ActiveDepartmentList}
            roleList={roleList}
            loader={loaderBal}
            leavePage={leavePage}
            leaveBalanceRowsPerPage={leaveBalanceRowsPerPage}
            setLeaveBalanceRowsPerPage={setLeaveBalanceRowsPerPage}
            setLeavePage={setLeavePage}
            setLeaveCurrentPage={setLeaveCurrentPage}
            leaveBalance={leaveBalance}
            leaveCurrentPage={leaveCurrentPage}
            leaveTotalCount={leaveTotalCount}
            leave_type={leave_type}
          />
        );
      case 2:
        return (
          <LeaveUsage
            leaveUsageData={leaveUsageData}
            getLeaveUsageList={getLeaveUsageList}
            loader={loaderUse}
            usageFilterData={usageFilterData}
            setUsageFilterData={setUsageFilterData}
            setUsageFilterDataApplied={setUsageFilterDataApplied}
            usageFilterDataApplied={usageFilterDataApplied}
            usageTotalCount={usageTotalCount}
          />
        );
      case 3:
        return (
          <LeaveConsumption
            leaveConsuptionData={leaveConsuptionData}
            getLeaveConsuptionList={getLeaveConsuptionList}
            departmentList={AllListsData?.ActiveDepartmentList}
            branchList={AllListsData?.ActiveBranchList}
            roleList={roleList}
            leaveList={leaveTypeList}
            leaveCunsRowsPerPage={leaveCunsRowsPerPage}
            setLeaveCunsRowsPerPage={setLeaveCunsRowsPerPage}
            leaveCunsPage={leaveCunsPage}
            setLeaveCunsPage={setLeaveCunsPage}
            loader={loaderCon}
            leaveCunsTotalCount={leaveCunsTotalCount}
            consuFilterData={consuFilterData}
            setConsuFilterData={setConsuFilterData}
            consuFilterDataApplied={consuFilterDataApplied}
            setConsuFilterDataApplied={setConsuFilterDataApplied}
            setSortByDays={setSortByDays}
            sortByDays={sortByDays}
            leave_type={leave_type}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Box>
      <Box className="leave-report-section">
        <Box className="tabs-wrap">
          <Box className="report-tabs section-right-tab-header">
            <CustomTabs
              tabs={reportsTab?.map((tab) => ({
                id: tab?.id,
                label: tab?.name,
              }))}
              initialTab={tab}
              onTabChange={handleTabChange}
            />
            <Box className="d-flex align-center w100 gap-10 justify-end tab-header-wrap">
              <Box className="select-date-wrap">
                <DatePicker
                  className="year-date-picker-wrap"
                  range
                  dateSeparator=" to "
                  value={yearFilterdata}
                  placeholder="Select Date"
                  onChange={(value) => {
                    setYearFilterData(value);
                  }}
                  render={(value, openCalendar) => (
                    <div
                      className="select-date-range d-flex justify-space-between align-center"
                      onClick={openCalendar}
                    >
                      <span>
                        {value ? (
                          formatRange(value)
                        ) : (
                          <span className="placeholder-wrap">Select Date</span>
                        )}
                      </span>
                      <CalendarMonthIcon
                        sx={{ cursor: 'pointer' }}
                        className="calender-icon"
                      />
                    </div>
                  )}
                />
              </Box>

              <Box>
                <CustomButton
                  isIconOnly
                  startIcon={
                    <Tooltip
                      title={<Typography>Download </Typography>}
                      classes={{
                        tooltip: 'info-tooltip-container',
                      }}
                      arrow
                    >
                      <DownloadIcon />
                    </Tooltip>
                  }
                  onClick={handleClick}
                />
              </Box>
            </Box>
          </Box>

          <Box className="section-right-content pt16">
            {getCurrentContent()}
          </Box>
        </Box>
        <Popover
          className="download-popover"
          id={id}
          open={open}
          anchorEl={anchorEl}
          onClose={handleClose}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'left',
          }}
        >
          <Box className="export-option">
            <Typography
              className="p14 fw600 pb8 cursor-pointer"
              onClick={() => handleDownload('pdf')}
            >
              PDF
            </Typography>
            <Typography
              className="p14 fw600 pb8 cursor-pointer"
              onClick={() => handleDownload('excel')}
            >
              Excel
            </Typography>
            <Typography
              className="p14 fw600 cursor-pointer"
              onClick={() => handleDownload('csv')}
            >
              CSV
            </Typography>
          </Box>
        </Popover>
      </Box>
    </Box>
  );
}
