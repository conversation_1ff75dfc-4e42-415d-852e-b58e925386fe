import React, { useState } from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Typography,
  Drawer,
  <PERSON>lapse,
  Popover,
  LinearProgress,
} from '@mui/material';
import CustomButton from '@/components/UI/CustomButton';
import {
  MenuIcon,
  FolderDocIcon,
  FolderDocHomeIcon,
  FolderDocTrainingIcon,
} from '../../helper/common/images';
import AddIcon from '@mui/icons-material/Add';
import PropTypes from 'prop-types';
import { useRouter } from 'next/navigation';
import DialogBox from '@/components/UI/Modalbox';
import { CustomTextField } from '@/components/UI/CommonField/index';
import CreateNewFolderIcon from '@mui/icons-material/CreateNewFolder';
import NoteAddIcon from '@mui/icons-material/NoteAdd';
import ArrowRightIcon from '@mui/icons-material/ArrowRight';
import CloudIcon from '@mui/icons-material/Cloud';
import './folder.scss';

function LinearProgressWithLabel(props) {
  return (
    <Box display="flex" alignItems="center" className="linear-progress-bar">
      <Box width="100%">
        <LinearProgress
          variant="determinate"
          className="sync-blue"
          {...props}
        />
      </Box>
    </Box>
  );
}

LinearProgressWithLabel.propTypes = {
  value: PropTypes.number.isRequired,
};

const FolderSideBar = ({
  DocumentFolderList,
  setSelectedSideBarData,
  Id,
  openMenu,
  setOpenMenu,
  CategoryDetails,
  isOwn,
  documentMemory,
}) => {
  const router = useRouter();
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [selectedMenu, setSelectedMenu] = useState(1); // Default to first item
  const [anchorElReact, setAnchorElReact] = useState(null);
  const openReact = Boolean(anchorElReact);
  const idReact = openReact ? 'simple-popover' : undefined;
  const [createModal, setCreateModal] = useState(false);
  const [foldername, setFoldername] = useState('');
  const [isUpdate, setIsUpdate] = useState(false);
  const [setUpdateItem] = useState();
  const handleListItemClick = (event, item) => {
    setSelectedMenu(item?.id);
    setSelectedSideBarData(item);
    router.push(`${item?.id}`);
  };

  const toggleDrawer = () => {
    setIsDrawerOpen(!isDrawerOpen);
  };

  const handleClick = (item) => {
    setOpenMenu((prevState) => ({
      ...prevState,
      [item?.category_name]: !prevState[item?.category_name],
    }));
  };

  const handleNestedMenus = (children) => {
    return children?.map((subOption) => {
      const isSelected = subOption?.id === selectedMenu;
      const truncatedName =
        subOption?.category_name?.length > 30
          ? `${subOption?.category_name?.substring(0, 30)}...`
          : subOption?.category_name;
      if (!subOption?.children) {
        return (
          <div key={subOption?.id}>
            <ListItem
              className={`folder-list-item ${isSelected ? 'selected' : ''}`}
            >
              <Box style={{ marginLeft: '25px' }}></Box>
              <ListItemButton
                className="folder-list-btn document-folder-btn cursor-pointer"
                onClick={(event) => handleListItemClick(event, subOption)}
              >
                <FolderDocIcon className="sidebar-list-icon" />
              </ListItemButton>
              <ListItemText
                className="sidebar-list-menu-name sidebar-menu cursor-pointer"
                primary={truncatedName}
                onClick={(event) => handleListItemClick(event, subOption)}
              />
              {/* <LaunchIcon
                className="redirect-icon"
                onClick={(event) => handleListItemClick(event, subOption)}
              /> */}
            </ListItem>
          </div>
        );
      }
      return (
        <div key={subOption?.id}>
          <ListItem
            className="folder-list-item folder-menu"
            button
            onClick={() => handleClick(subOption)}
          >
            {subOption?.children?.length !== 0 ? (
              openMenu && openMenu[subOption?.name] ? (
                <ArrowRightIcon />
              ) : (
                <ArrowRightIcon />
              )
            ) : (
              <Box style={{ marginLeft: '25px' }}></Box>
            )}
            <ListItemButton
              className="folder-list-btn document-folder-btn cursor-pointer"
              onClick={(event) => handleListItemClick(event, subOption)}
            >
              {' '}
              {subOption?.category_use === 'training' ? (
                <FolderDocTrainingIcon
                  className={'sidebar-list-icon training-list-icon'}
                />
              ) : (
                <FolderDocIcon className={'sidebar-list-icon'} />
              )}
            </ListItemButton>
            <ListItemText
              className="sidebar-list-menu-name sidebar-menu cursor-pointer"
              primary={truncatedName}
              onClick={(event) => handleListItemClick(event, subOption)}
            />
            {/* <LaunchIcon className="redirect-icon" /> */}
          </ListItem>
          <Collapse
            in={openMenu?.[subOption?.category_name]}
            timeout="auto"
            unmountOnExit
            style={{ marginLeft: '21px' }}
          >
            <Box className="submenu-option">
              {handleNestedMenus(subOption?.children)}
            </Box>
          </Collapse>
        </div>
      );
    });
  };
  const AddReactions = (event) => {
    setAnchorElReact(event.currentTarget);
  };
  const handleCloseReact = () => {
    setAnchorElReact(null);
  };
  const drawerContent = (
    <Box className="folder-side-menu" role="presentation">
      {!isOwn && (
        <>
          {Id === 'all' ? (
            <Box className="folder-create-btn">
              <CustomButton
                fullWidth
                variant="contained"
                title={'Create category'}
                startIcon={<AddIcon />}
                onClick={() => router.push(`/create-category?id=${Id}`)}
              />
            </Box>
          ) : (
            <Box className="folder-create-btn">
              <CustomButton
                fullWidth
                variant="contained"
                title={'Create category'}
                startIcon={<AddIcon />}
                onClick={AddReactions}
              />
              <Popover
                className="folder-popover"
                open={openReact}
                id={idReact}
                anchorEl={anchorElReact}
                onClose={handleCloseReact}
                anchorOrigin={{
                  vertical: 'bottom',
                  horizontal: 'left',
                }}
              >
                {CategoryDetails?.category_use === 'training' ? (
                  CategoryDetails?.parent_id === null &&
                  CategoryDetails?.category_type === 'folder' ? (
                    <ListItem
                      className={`folder-list-item pt16 pb16`}
                      onClick={() => {
                        // if (
                        //   CategoryDetails &&
                        //   CategoryDetails?.parent_id &&
                        //   CategoryDetails?.category_type === 'folder'
                        // ) {
                        //   setApiMessage(
                        //     'error',
                        //     'Training allows you to add only one level folder.' //You cannot add more than one folder in training.
                        //   );
                        // } else {
                        router.push(
                          `/create-category?id=${Id}&type=${CategoryDetails?.category_use}`
                        );
                        // }
                      }}
                    >
                      <ListItemButton className="document-folder-btn">
                        <CreateNewFolderIcon className="sidebar-list-icon folder" />
                      </ListItemButton>
                      <ListItemText
                        className="menu-name"
                        primary={'Add Folder'}
                      />
                    </ListItem>
                  ) : (
                    <></>
                  )
                ) : (
                  <ListItem
                    className={`folder-list-item pt16`}
                    onClick={() => {
                      // if (
                      //   CategoryDetails &&
                      //   CategoryDetails?.parent_id &&
                      //   CategoryDetails?.category_type === 'folder'
                      // ) {
                      //   setApiMessage(
                      //     'error',
                      //     'Training allows you to add only one level folder.' //You cannot add more than one folder in training.
                      //   );
                      // } else {
                      router.push(
                        `/create-category?id=${Id}&type=${CategoryDetails?.category_use}`
                      );
                      // }
                    }}
                  >
                    <ListItemButton className="document-folder-btn">
                      <CreateNewFolderIcon className="sidebar-list-icon folder" />
                    </ListItemButton>
                    <ListItemText
                      className="menu-name"
                      primary={'Add Folder'}
                    />
                  </ListItem>
                )}
                {CategoryDetails &&
                CategoryDetails?.parent_id === null &&
                CategoryDetails?.category_type === 'folder' &&
                CategoryDetails?.category_use === 'training' ? (
                  <></>
                ) : (
                  <ListItem
                    className={
                      CategoryDetails?.parent_id !== null &&
                      CategoryDetails?.category_use === 'training'
                        ? `folder-list-item pb16 pt16`
                        : `folder-list-item pb16 pt4`
                    }
                    onClick={() =>
                      router.push(
                        `/create-content?id=${Id}&type=${CategoryDetails?.category_use}`
                      )
                    }
                  >
                    <ListItemButton className="document-folder-btn">
                      <NoteAddIcon className="sidebar-list-icon" />
                    </ListItemButton>
                    <ListItemText className="menu-name" primary={'Add File'} />
                  </ListItem>
                )}
              </Popover>
            </Box>
          )}
        </>
      )}

      <Box className="folder-name-list-container ">
        <Typography className="body-text fw600 folder-sidebar-header">
          Folders
        </Typography>
        <List className="folder-list-wrap">
          <div>
            <ListItem className={`folder-list-item selected`}>
              <Box style={{ marginLeft: '25px' }}></Box>
              <ListItemButton
                className="folder-list-btn document-folder-btn cursor-pointer"
                // onClick={(event) => handleListItemClick(event, subOption)}
                onClick={() => {
                  isOwn
                    ? router.push('/document-own/all')
                    : router.push('/document-staff/all');
                }}
              >
                {/* <HomeIcon className="sidebar-list-icon" /> */}
                <Box className="sidebar-list-icon">{FolderDocHomeIcon()}</Box>
              </ListItemButton>
              <ListItemText
                className="sidebar-list-menu-name sidebar-menu cursor-pointer"
                primary="Home"
                onClick={() => {
                  isOwn
                    ? router.push('/document-own/all')
                    : router.push('/document-staff/all');
                }}
              />
              {/* <LaunchIcon
                className="redirect-icon"
                onClick={(event) => handleListItemClick(event, subOption)}
              /> */}
            </ListItem>
          </div>
          {handleNestedMenus(DocumentFolderList)}
        </List>
      </Box>
    </Box>
  );

  return (
    <Box className="folder-section-left folder-sidebar-container document-sidebar-container">
      <Box className="mobile-view-menu" onClick={toggleDrawer}>
        <MenuIcon />
        <Drawer anchor="left" open={isDrawerOpen} onClose={toggleDrawer}>
          <Box className="folder-doc-side-menu folder-side-menu">
            {drawerContent}
            {documentMemory && (
              <Box className="memory-usage-for-sidemenu">
                <Box className="storage">
                  <CloudIcon />{' '}
                  <Typography className="title-text fw600">Storage</Typography>
                  {documentMemory?.percentage &&
                    documentMemory?.percentage.replace('%', '') &&
                    parseFloat(documentMemory?.percentage.replace('%', '')) >
                      1 && (
                      <Typography className="sub-title-text fw600">
                        &nbsp;&nbsp;
                        {'( ' + documentMemory?.percentage + ' ) '}
                      </Typography>
                    )}
                </Box>

                <LinearProgressWithLabel
                  className="progress-bar"
                  value={parseInt(
                    documentMemory?.percentage
                      ? documentMemory?.percentage.replace('%', '')
                      : 0
                  )}
                  // value="10"
                />
                <Typography className="sub-title-text">
                  {documentMemory?.total_size +
                    ' of ' +
                    documentMemory?.limit +
                    ' used'}
                </Typography>
              </Box>
            )}
          </Box>
        </Drawer>
      </Box>

      <Box className="folder-doc-side-menu folder-side-menu">
        {drawerContent}
        {documentMemory && (
          <Box className="memory-usage-for-sidemenu desktop-sidebar">
            <Box className="storage">
              <CloudIcon />{' '}
              <Typography className="title-text fw600">Storage</Typography>
              {documentMemory?.percentage &&
                documentMemory?.percentage.replace('%', '') &&
                parseFloat(documentMemory?.percentage.replace('%', '')) > 1 && (
                  <Typography className="sub-title-text fw600">
                    &nbsp;&nbsp;
                    {'( ' + documentMemory?.percentage + ' ) '}
                  </Typography>
                )}
            </Box>

            <LinearProgressWithLabel
              className="progress-bar"
              value={parseInt(
                documentMemory?.percentage
                  ? documentMemory?.percentage.replace('%', '')
                  : 0
              )}
              // value="10"
            />
            <Typography className="sub-title-text">
              {documentMemory?.total_size +
                ' of ' +
                documentMemory?.limit +
                ' used'}
            </Typography>
          </Box>
        )}
      </Box>

      <DialogBox
        open={createModal}
        handleClose={() => {
          setCreateModal(!createModal);
          setIsUpdate(false);
          setUpdateItem();
        }}
        title={isUpdate ? 'Update folder' : 'Add folder'}
        content={
          <>
            {' '}
            <Box className="pt24">
              <CustomTextField
                InputLabelProps={{
                  shrink: true,
                }}
                fullWidth
                id="categoryname"
                name="categoryname"
                value={foldername}
                label="Folder name *"
                variant="filled"
                placeholder="folder name"
                onChange={(e) => {
                  // handleChange(e);
                  setFoldername(e.target.value);
                }}
              />
              <Box className="create-cancel-button pt24">
                <CustomButton
                  fullWidth
                  className="title-text secondary-button"
                  fontWeight="600"
                  variant="contained"
                  background="#FFFFFF"
                  backgroundhover="#39596e"
                  colorhover="#FFFFFF"
                  title="Cancel"
                  onClick={() => {
                    setCreateModal(false);
                  }}
                />
                <CustomButton
                  fullWidth
                  className="title-text"
                  fontWeight="600"
                  variant="contained"
                  background="#39596e"
                  backgroundhover="#FFFFFF"
                  colorhover="#000000"
                  title={'Create'}
                  disabled={!foldername}
                />
              </Box>
            </Box>
          </>
        }
      />
    </Box>
  );
};

export default FolderSideBar;
