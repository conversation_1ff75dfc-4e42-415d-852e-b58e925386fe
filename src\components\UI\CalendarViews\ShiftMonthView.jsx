'use client';

import React, { useEffect, useState } from 'react';
import moment from 'moment';
import { StaffOpenShiftRow, StaffShiftRow } from './ShiftRow';
import { ShiftSummaryCell } from './ShiftCell';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { calculateTotalTime } from './ShiftUtility';
import './calendarView.scss';

// ========== Month Headers Component ==========
const MonthViewHeader = ({
  shiftData,
  monthDays,
  currentDate,
  currentMonth,
  isViewType,
}) => {
  return (
    <tr>
      <th className="shift-column">
        <ShiftSummaryCell
          shifts={shiftData?.filter((s) => s?.isOpen === 0)}
          currentMonth={currentMonth}
          isViewType={isViewType}
        />
      </th>
      {monthDays.map((day) => {
        const isCurrentDate = day?.format('YYYY-MM-DD') === currentDate;
        return (
          <th
            key={day.format('YYYY-MM-DD')}
            className={`date-column ${isCurrentDate ? 'isCurrentdate' : ''}`}
          >
            {day.format('D dd')}
            <div className={isCurrentDate ? 'current-date' : ''} />
          </th>
        );
      })}
    </tr>
  );
};

// ========== Group Component ==========
const GroupBaseUI = ({
  groupName,
  staffList,
  shiftData,
  monthDays,
  hoveredCell,
  setHoveredCell,
  menuOpen,
  setMenuOpen,
  shiftMenuOpen,
  setShiftMenuOpen,
  staffListData,
  rolesList,
  departmentList,
  locationList,
  filterData,
  getRotaShiftList,
  isViewType,
  dayOffList,
  setDayOffList,
  viewAccessOnly,
  availabilityList,
  setIsUpdatedShift,
  isSearchCollapse,
  userLeaveList,
}) => {
  const [isOpen, setIsOpen] = useState(true);

  // Calculate totals for this role group
  const groupShifts = shiftData?.filter((shift) =>
    staffList?.some((staff) => staff?.id === shift?.userId)
  );
  useEffect(() => {
    if (isSearchCollapse || filterData?.location) setIsOpen(true);
  }, [isSearchCollapse, filterData?.location]);

  const totalTime = calculateTotalTime(groupShifts);
  const totalShifts = groupShifts?.length || 0;

  return (
    <>
      <tr className="group-header-row">
        <td className="group-header-cell">
          <div className="group-header" onClick={() => setIsOpen(!isOpen)}>
            {isOpen ? (
              <ExpandMoreIcon style={{ transform: 'rotate(180deg)' }} />
            ) : (
              <ExpandMoreIcon />
            )}
            <p className="group-name">{groupName}</p>
          </div>
        </td>
        <td className="group-details-cell" colSpan={4}>
          <div className="group-header" onClick={() => setIsOpen(!isOpen)}>
            <div className="group-shift-details">
              <p className="group-shift-total-time-count">{totalTime}</p>
              {' • '}
              <p className="group-shift-total-count">
                {totalShifts} {totalShifts > 1 ? 'shifts' : 'shift'}
              </p>
            </div>
          </div>
        </td>
      </tr>
      {isOpen &&
        staffList?.map((staff) => {
          const userShifts = shiftData?.filter(
            (shift) => shift?.userId === staff?.id
          );

          return (
            <StaffShiftRow
              key={staff?.id}
              staff={staff}
              userShifts={userShifts}
              monthDays={monthDays}
              hoveredCell={hoveredCell}
              setHoveredCell={setHoveredCell}
              menuOpen={menuOpen}
              setMenuOpen={setMenuOpen}
              shiftMenuOpen={shiftMenuOpen}
              setShiftMenuOpen={setShiftMenuOpen}
              staffListData={staffListData}
              rolesList={rolesList}
              departmentList={departmentList}
              locationList={locationList}
              filterData={filterData}
              getRotaShiftList={getRotaShiftList}
              isViewType={isViewType}
              dayOffList={dayOffList}
              setDayOffList={setDayOffList}
              viewAccessOnly={viewAccessOnly}
              availabilityList={availabilityList}
              setIsUpdatedShift={setIsUpdatedShift}
              userLeaveList={userLeaveList}
            />
          );
        })}
    </>
  );
};

// ========== Shift Month Component ==========
const ShiftMonthView = ({
  staffListData,
  shiftData,
  filterData,
  rolesList,
  departmentList,
  locationList,
  getRotaShiftList,
  dayOffList,
  setDayOffList,
  viewAccessOnly,
  availabilityList,
  setIsUpdatedShift,
  isSearchCollapse,
  userLeaveList,
}) => {
  const [hoveredCell, setHoveredCell] = useState(null);
  const [menuOpen, setMenuOpen] = useState(false);
  const [shiftMenuOpen, setShiftMenuOpen] = useState(null);

  // Set currentMonth based on filterData or default to today
  const currentMonth = moment(filterData?.currentDate || moment());

  // Generate all days in the selected/current month
  const monthDays = Array.from({ length: currentMonth.daysInMonth() }, (_, i) =>
    currentMonth.clone().startOf('month').add(i, 'days')
  );

  // Open shift list
  const openShiftData = shiftData.filter((s) => s.isOpen === 1);

  const currentDate = moment().format('YYYY-MM-DD');

  // Group staff by role if isGroupBy is true
  const groupedStaff =
    filterData?.isGroupBy === 'role'
      ? staffListData?.reduce((acc, staff) => {
          const matchedRole = staff?.user_roles?.find((ur) =>
            rolesList?.some(
              (r) => r?.value === ur?.role_id || r?.value === ur?.id
            )
          );
          const roleGrp =
            rolesList?.find(
              (r) =>
                r?.value === matchedRole?.role_id ||
                r?.value === matchedRole?.id
            )?.label || 'No Role';
          if (!acc[roleGrp]) {
            acc[roleGrp] = [];
          }
          acc[roleGrp].push(staff);
          return acc;
        }, {})
      : filterData?.isGroupBy === 'department'
        ? staffListData?.reduce((acc, staff) => {
            const departmentGrp =
              departmentList?.find((r) => r.value === staff.department?.id)
                ?.label || 'No Department';
            if (!acc[departmentGrp]) {
              acc[departmentGrp] = [];
            }
            acc[departmentGrp].push(staff);
            return acc;
          }, {})
        : null;

  return (
    <div className="schedule-container">
      <table className="schedule-table">
        <thead>
          <MonthViewHeader
            shiftData={shiftData}
            monthDays={monthDays}
            currentDate={currentDate}
            currentMonth={currentMonth}
            isViewType="month"
          />
        </thead>

        <tbody>
          {/* Open Shift Row */}
          <StaffOpenShiftRow
            openShiftData={openShiftData}
            monthDays={monthDays}
            hoveredCell={hoveredCell}
            setHoveredCell={setHoveredCell}
            menuOpen={menuOpen}
            setMenuOpen={setMenuOpen}
            shiftMenuOpen={shiftMenuOpen}
            setShiftMenuOpen={setShiftMenuOpen}
            staffListData={staffListData}
            rolesList={rolesList}
            departmentList={departmentList}
            locationList={locationList}
            filterData={filterData}
            getRotaShiftList={getRotaShiftList}
            isViewType="month"
            viewAccessOnly={viewAccessOnly}
            setIsUpdatedShift={setIsUpdatedShift}
          />

          {/* User Shift Rows */}
          {filterData?.isGroupBy
            ? Object.entries(groupedStaff || {}).map(
                ([groupName, staffList]) => (
                  <GroupBaseUI
                    key={groupName}
                    groupName={groupName}
                    staffList={staffList}
                    shiftData={shiftData}
                    monthDays={monthDays}
                    hoveredCell={hoveredCell}
                    setHoveredCell={setHoveredCell}
                    menuOpen={menuOpen}
                    setMenuOpen={setMenuOpen}
                    shiftMenuOpen={shiftMenuOpen}
                    setShiftMenuOpen={setShiftMenuOpen}
                    staffListData={staffListData}
                    rolesList={rolesList}
                    departmentList={departmentList}
                    locationList={locationList}
                    filterData={filterData}
                    getRotaShiftList={getRotaShiftList}
                    isViewType="month"
                    dayOffList={dayOffList}
                    setDayOffList={setDayOffList}
                    viewAccessOnly={viewAccessOnly}
                    availabilityList={availabilityList}
                    setIsUpdatedShift={setIsUpdatedShift}
                    isSearchCollapse={isSearchCollapse}
                    userLeaveList={userLeaveList}
                  />
                )
              )
            : staffListData?.map((staff) => {
                const userShifts = shiftData?.filter(
                  (shift) => shift?.userId === staff?.id
                );

                return (
                  <StaffShiftRow
                    key={staff?.id}
                    staff={staff}
                    userShifts={userShifts}
                    monthDays={monthDays}
                    hoveredCell={hoveredCell}
                    setHoveredCell={setHoveredCell}
                    menuOpen={menuOpen}
                    setMenuOpen={setMenuOpen}
                    shiftMenuOpen={shiftMenuOpen}
                    setShiftMenuOpen={setShiftMenuOpen}
                    staffListData={staffListData}
                    rolesList={rolesList}
                    departmentList={departmentList}
                    locationList={locationList}
                    filterData={filterData}
                    getRotaShiftList={getRotaShiftList}
                    isViewType="month"
                    dayOffList={dayOffList}
                    setDayOffList={setDayOffList}
                    viewAccessOnly={viewAccessOnly}
                    availabilityList={availabilityList}
                    setIsUpdatedShift={setIsUpdatedShift}
                    userLeaveList={userLeaveList}
                  />
                );
              })}
        </tbody>
      </table>
    </div>
  );
};

export default ShiftMonthView;
