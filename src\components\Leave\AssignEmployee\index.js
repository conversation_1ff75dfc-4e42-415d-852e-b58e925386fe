'use client';

import React, { useState, useContext, useEffect } from 'react';
import { Box, Typography, Tooltip, Divider } from '@mui/material';
import CustomSelect from '@/components/UI/CustomSelect';
import CustomButton from '@/components/UI/CustomButton';
import _ from 'lodash';
import { identifiers } from '@/helper/constants/identifier';
import CustomSearch from '@/components/UI/CustomSearch';
import ClearOutlinedIcon from '@mui/icons-material/ClearOutlined';
import CheckIcon from '@mui/icons-material/Check';
import AuthContext from '@/helper/authcontext';
import UserDetails from '@/components/Leave/AssignEmployee/userdetails';
import AddIcon from '@mui/icons-material/Add';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
// import BranchFilter from '@/components/DSR/Reports/BranchFilter';
import MultipleFilter from '@/components/UI/MultipleFilter';
import useUserByFilter from '@/hooks/useUserByFilter';
import WarningIcon from '@mui/icons-material/Warning';
import useRoleList from '@/hooks/useRoleList';
import './assignemployee.scss';

const AssignEmployee = ({
  setClose,
  AssignEmployees,
  assignId,
  SelectedID,
  isJobRole,
  removeDelete,
  empcontract,
  isFromEmp,
}) => {
  const { AllListsData } = useContext(AuthContext);
  const { roleList, fetchRoleList } = useRoleList();
  const [selectedMenu, setSelectedMenu] = useState([]);
  const [oldselectedMenu, setOldSelectedMenu] = useState([]);
  const [selectedBranches, setSelectedBranches] = useState([]);
  const [selectedDepartment, setSelectedDepartment] = useState([]);
  const [selectedRole, setSelectedRole] = useState([]);
  const [assignFilter, setAssignFilter] = useState({
    searchValue: '',
    status: '',
  });
  const { userList, getUserList } = useUserByFilter();

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      // setSelectedMenu([]);
      getUserList(
        assignFilter?.searchValue,
        1,
        {
          selectedBranches: selectedBranches,
          selectedDepartment: selectedDepartment,
          selectedRole: selectedRole,
          status: assignFilter?.status,
        },
        '',
        empcontract
      );
    }
  };
  const handleItemClick = (id) => {
    const currentIndex = selectedMenu.indexOf(id);
    const newChecked = [...selectedMenu];
    if (currentIndex === -1) {
      newChecked.push(id);
    } else {
      newChecked.splice(currentIndex, 1);
    }
    setSelectedMenu(newChecked);
  };

  useEffect(() => {
    if (
      (!isJobRole?.emp_contract_category?.department_id ||
        isJobRole?.emp_contract_category?.type === 'general') &&
      empcontract
    ) {
      getUserList(
        '',
        1,
        {
          selectedBranches: '',
          selectedDepartment: '',
          selectedRole: '',
          status: '',
        },
        '',
        empcontract
      );
    } else if (!isFromEmp) {
      getUserList(
        '',
        1,
        {
          selectedBranches: '',
          selectedDepartment: '',
          selectedRole: '',
          status: '',
        },
        ''
      );
    }
    fetchRoleList();
  }, [empcontract]);
  useEffect(() => {
    if (SelectedID) {
      const numbersArray = SelectedID?.split(',').map(Number);
      if (numbersArray && numbersArray?.length > 0) {
        setSelectedMenu(numbersArray);
        setOldSelectedMenu(numbersArray);
      }
    }
  }, [SelectedID]);
  useEffect(() => {
    if (
      isJobRole?.emp_contract_category?.department_id &&
      isJobRole?.emp_contract_category?.type !== 'general' &&
      empcontract
    ) {
      setSelectedDepartment([isJobRole?.emp_contract_category?.department_id]);
      getUserList(
        '',
        1,
        {
          selectedBranches: '',
          selectedDepartment: [isJobRole?.emp_contract_category?.department_id],
          selectedRole: '',
          status: '',
        },
        '',
        empcontract
      );
    }
  }, [isJobRole?.emp_contract_category?.department_id, empcontract]);

  return (
    <Box className="assign-employee-section">
      <Divider className="mb16" />
      <Box className="filters-wrap d-flex justify-end align-center">
        <CustomSearch
          setSearchValue={(e) => {
            setAssignFilter({
              ...assignFilter,
              searchValue: e,
            });
          }}
          onKeyPress={handleKeyPress}
          searchValue={assignFilter?.searchValue}
        />

        {/* Select for Branch */}
        <Box>
          <CustomSelect
            placeholder="Employees"
            dropDownClass="select-dropdown-small"
            options={identifiers?.ASSIGN_EMP_STATUS}
            value={
              identifiers?.ASSIGN_EMP_STATUS?.find((opt) => {
                return opt?.value === assignFilter?.status;
              }) || ''
            }
            onChange={(e) => {
              setAssignFilter({
                ...assignFilter,
                status: e?.value,
              });
            }}
          />
        </Box>

        <MultipleFilter
          selected={selectedBranches}
          setSelected={setSelectedBranches}
          List={AllListsData?.ActiveBranchList}
          placeholder="Branch"
        />

        <MultipleFilter
          selected={selectedDepartment}
          setSelected={setSelectedDepartment}
          List={AllListsData?.ActiveDepartmentList}
          placeholder="Department"
          disabled={isJobRole?.emp_contract_category?.department_id}
        />
        <MultipleFilter
          selected={selectedRole}
          setSelected={setSelectedRole}
          List={roleList}
          placeholder="System Access"
        />

        <Box>
          <CustomButton
            isIconOnly
            startIcon={
              <Tooltip
                title={
                  <Typography className="sub-title-text">
                    Apply Filter
                  </Typography>
                }
                classes={{
                  tooltip: 'info-tooltip-container',
                }}
                arrow
              >
                <CheckIcon />
              </Tooltip>
            }
            onClick={() => {
              // setLeavePage(1);
              // setSelectedMenu([]);
              getUserList(
                assignFilter?.searchValue,
                1,
                {
                  selectedBranches: selectedBranches,
                  selectedDepartment: selectedDepartment,
                  selectedRole: selectedRole,
                  status: assignFilter?.status,
                },
                '',
                empcontract
              );
            }}
          />
        </Box>
        <Box>
          <CustomButton
            variant="outlined"
            isIconOnly
            startIcon={
              <Tooltip
                title={
                  <Typography className="sub-title-text">
                    Clear Filter
                  </Typography>
                }
                classes={{
                  tooltip: 'info-tooltip-container',
                }}
                arrow
              >
                <ClearOutlinedIcon />
              </Tooltip>
            }
            onClick={() => {
              setSelectedDepartment([]);
              setSelectedBranches([]);
              setSelectedRole([]);
              setAssignFilter({
                searchValue: '',
                status: '',
              });

              getUserList(
                '',
                1,
                {
                  selectedBranches: [],
                  selectedDepartment: [],
                  selectedRole: [],
                  status: '',
                },
                '',
                empcontract
              );
            }}
          />
        </Box>
      </Box>
      {empcontract ? (
        <Box className="pb16">
          <Box className={`less-zero warning-details`}>
            <WarningIcon className="warning-icon" />
            <Box className="rejected">
              <Typography className="caption-text fw500">
                Employment contract details are incomplete. Please update the
                user profile to enable contract generation.
              </Typography>
            </Box>
          </Box>
        </Box>
      ) : (
        <></>
      )}
      <Box className="assign-emp">
        <Box className="Assign-emp-grid">
          <Box className="left-grid">
            <Box className="assign-role-title d-flex justify-space-between pr4">
              <Typography className="body-sm fw600">
                Un-Assign Employee
              </Typography>
              <Typography
                className={`caption-text  fw600 color-green ${selectedMenu && userList && userList?.length > 0 && selectedMenu?.length === userList?.map((u) => u?.id)?.length ? 'cursor-default disabled-field' : 'cursor-pointer'}`}
                onClick={() => {
                  if (empcontract) {
                    userList &&
                      userList?.length > 0 &&
                      setSelectedMenu(
                        userList
                          ?.filter(
                            (f) =>
                              f?.has_user_meta || selectedMenu?.includes(f?.id)
                          )
                          ?.map((u) => u?.id)
                      );
                  } else {
                    userList &&
                      userList?.length > 0 &&
                      setSelectedMenu(userList?.map((u) => u?.id));
                  }
                }}
              >
                Add All
              </Typography>
            </Box>

            {userList && userList?.length > 0 ? (
              <>
                {userList?.map((user) => {
                  if (!selectedMenu?.includes(user?.id)) {
                    return (
                      <Box
                        className={`d-flex justify-space-between align-center ${!user?.has_user_meta && empcontract ? 'disabled-user' : ''}`}
                      >
                        <UserDetails user={user} empcontract={empcontract} />

                        <Tooltip
                          arrow
                          title={
                            <Typography className="sub-title-text">
                              Add
                            </Typography>
                          }
                          classes={{
                            tooltip: 'info-tooltip-container',
                          }}
                        >
                          <Box className="primary-small-icon d-flex align-center justify-center primary-assign-emp-icon mr20">
                            <AddIcon
                              className={`download-icon ${!user?.has_user_meta && empcontract ? 'disabled-user' : 'cursor-pointer'}`}
                              onClick={() => {
                                if (user?.has_user_meta && empcontract) {
                                  handleItemClick(user?.id);
                                } else if (!empcontract) {
                                  handleItemClick(user?.id);
                                }
                              }}
                            />
                          </Box>
                        </Tooltip>
                      </Box>
                    );
                  } else {
                    return <></>;
                  }
                })}
              </>
            ) : (
              <>
                <Box className="mt32">
                  <Typography className="text-align h6 color-gray">
                    No data found
                  </Typography>
                </Box>
              </>
            )}
            {/*   </>
           ) : (
              <></>
            )} */}
          </Box>
          <Box className="right-grid ">
            <Box className="assign-role-title pl4 d-flex align-center justify-space-between">
              <Typography className="body-sm fw600 ">{`Assign Employee (${selectedMenu?.length})`}</Typography>
              <Typography
                className={`caption-text fw600 color-red ${selectedMenu && selectedMenu?.length > 0 && (!removeDelete || selectedMenu?.length !== oldselectedMenu?.length) ? 'cursor-pointer' : 'cursor-default disabled-field'}`}
                onClick={() => {
                  removeDelete
                    ? setSelectedMenu(oldselectedMenu)
                    : setSelectedMenu([]);
                }}
              >
                Remove All
              </Typography>
            </Box>

            {userList && userList?.length > 0 ? (
              <>
                {_.sortBy(userList, (user) =>
                  _.indexOf(selectedMenu, user?.id)
                )?.map((user) => {
                  if (selectedMenu?.includes(user?.id)) {
                    return (
                      <Box className="d-flex justify-space-between align-center pl4">
                        <UserDetails user={user} />
                        <Tooltip
                          arrow
                          title={
                            <Typography className="sub-title-text">
                              Remove
                            </Typography>
                          }
                          classes={{
                            tooltip: 'info-tooltip-container',
                          }}
                        >
                          {(!removeDelete ||
                            !oldselectedMenu?.includes(user?.id)) && (
                            <Box className="secondary-small-icon d-flex align-center justify-center secondary-assign-emp-icon">
                              <Tooltip
                                arrow
                                title={
                                  <Typography className="sub-title-text">
                                    Remove
                                  </Typography>
                                }
                                classes={{
                                  tooltip: 'info-tooltip-container',
                                }}
                              >
                                <DeleteOutlineIcon
                                  className="svg-icon cursor-pointer"
                                  onClick={() => {
                                    handleItemClick(user?.id);
                                  }}
                                />
                              </Tooltip>
                            </Box>
                          )}
                        </Tooltip>
                      </Box>
                    );
                  } else {
                    return <></>;
                  }
                })}
              </>
            ) : (
              <>
                <Box className="mt32">
                  <Typography className="text-align h6 color-gray">
                    No data found
                  </Typography>
                </Box>
              </>
            )}
          </Box>
        </Box>
      </Box>

      <Box className="d-flex justify-end pt16 create-cancel-button">
        <CustomButton
          variant="outlined"
          title="Cancel"
          onClick={() => {
            setClose(false);
          }}
        />
        <CustomButton
          variant="contained"
          title={'Save'}
          fullWidth={false}
          onClick={() =>
            AssignEmployees(selectedMenu, assignId, oldselectedMenu)
          }
        />
      </Box>
    </Box>
  );
};

export default AssignEmployee;
