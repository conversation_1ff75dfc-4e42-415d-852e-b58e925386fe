@import '../../../styles/variable.scss';

.custom-textfield-wrapper {
  .field-label {
    font-family: var(--font-family-primary);
    margin-bottom: var(--spacing-xxs);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-bright-blue);
    display: flex;
    align-items: center;
    justify-content: space-between;
    &.error-label {
      color: var(--text-error);
    }
    .label-icon {
      width: var(--font-size-sm);
      height: var(--font-size-sm);
      cursor: pointer;
    }
    .required {
      color: var(--text-error);
      margin-left: var(--spacing-xxs);
    }
  }
  &.Mui-error {
    & .MuiOutlinedInput-notchedOutline {
      border-color: var(--border-color-error) !important;
      &:hover {
        border-color: var(--border-color-error) !important;
      }
    }
  }
  .field-error-text {
    font-family: var(--font-family-primary);
    color: var(--text-error);
    margin-top: var(--spacing-xxs);
    font-size: var(--font-size-xs);
    line-height: var(--line-height-xs);
  }
}
