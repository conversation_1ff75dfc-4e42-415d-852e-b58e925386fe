.recipe-grid {
  &--grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: var(--spacing-md);
  }

  &--list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
  }

  // Loading Skeleton
  &__skeleton {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  &__skeleton-card {
    background-color: var(--color-white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-card);
    overflow: hidden;
  }

  &__skeleton-image {
    width: 100%;
    height: 192px;
    background-color: var(--color-light-gray);
  }

  &__skeleton-content {
    padding: var(--spacing-md);
  }

  &__skeleton-title {
    height: 16px;
    background-color: var(--color-light-gray);
    border-radius: var(--border-radius-sm);
    margin-bottom: var(--spacing-xs);
  }

  &__skeleton-description {
    height: 12px;
    background-color: var(--color-light-gray);
    border-radius: var(--border-radius-sm);
    width: 75%;
    margin-bottom: var(--spacing-sm);
  }

  &__skeleton-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__skeleton-meta-item {
    height: 12px;
    background-color: var(--color-light-gray);
    border-radius: var(--border-radius-sm);
    width: 25%;
  }

  &__skeleton-list {
    background-color: var(--color-white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-card);
    padding: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
  }

  &__skeleton-list-image {
    width: 64px;
    height: 64px;
    background-color: var(--color-light-gray);
    border-radius: var(--border-radius-md);
    flex-shrink: 0;
  }

  &__skeleton-list-content {
    flex: 1;
  }

  &__skeleton-actions {
    width: 80px;
    height: 32px;
    background-color: var(--color-light-gray);
    border-radius: var(--border-radius-sm);
  }

  // Empty State
  &__empty {
    text-align: center;
    padding: var(--spacing-3xl) var(--spacing-md);
  }

  &__empty-icon {
    width: 96px;
    height: 96px;
    margin: 0 auto var(--spacing-md);
    background-color: var(--color-primary-opacity);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-primary);
  }

  &__empty-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-xs);
    font-family: var(--font-family-primary);
  }

  &__empty-description {
    color: var(--text-color-slate-gray);
    margin-bottom: var(--spacing-md);
    font-family: var(--font-family-primary);
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
  }

  &__empty-button {
    margin-top: var(--spacing-md);
  }
}

// Animations
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
