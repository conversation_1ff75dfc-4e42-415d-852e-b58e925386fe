@import '../../../styles/variable.scss';

.date-wrap {
  margin-top: -14px;
  height: 65px;
  .textfeild-error {
    .MuiOutlinedInput-root:hover {
      fieldset {
        border: 1px solid $error !important;
      }
    }
    fieldset {
      border: 1px solid $error !important;
      border-color: $error !important;
    }
    label {
      color: $error !important;
    }
    svg {
      fill: $error !important;
    }
  }
  .MuiInputLabel-root {
    top: -5px;
    left: 1px;
    transform: none;
    color: $color-Dark-80 !important;
    font-family: $PrimaryFont;
    line-height: 18px;
    margin-bottom: 2px;
    font-weight: 600;
    background-color: transparent !important;
    font-size: 12px;
    letter-spacing: -0.5px;
    text-transform: capitalize;
    span {
      text-transform: capitalize;
    }
  }
  input::placeholder {
    text-transform: capitalize;
  }
  // .MuiInputBase-root {
  //   &:hover {
  //     border: none;
  //   }
  // }
  .MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline {
    border: 1px solid $color-Dark-10;
    border-color: $color-Dark-10;
  }

  .MuiInputBase-input {
    position: relative;
    padding: 6px 14px;
    z-index: 998;
    left: 1px;
  }
  .MuiInputAdornment-root {
    z-index: 99;
  }
  legend {
    display: none;
  }
  .MuiOutlinedInput-notchedOutline {
    background-color: $color-White;
    border-radius: 8px !important;
    height: 45px;
    // left: 0px;
    // border: 0px solid $color-Dark-10;
    border: 1px solid $color-Dark-10;
    min-height: 45px;
    margin-top: 1px;
  }
  .MuiInputBase-root .MuiOutlinedInput-notchedOutline {
    border: 1px solid $color-Dark-10;
    border-color: $color-Dark-10 !important;
  }
  .Mui-disabled {
    // .MuiInputBase-input {
    //   background-color: $color-White !important;
    //   // border: none;
    // }
    cursor: not-allowed;
    opacity: 0.9;
    -webkit-text-fill-color: $color-Black !important;
    .MuiOutlinedInput-notchedOutline {
      background-color: $color-White !important;
      border-color: $color-Dark-10 !important;
      z-index: 9;
      border-radius: 8px;
      border-color: transparent;
    }
    .MuiInputAdornment-root {
      .Mui-disabled {
        cursor: not-allowed;
        pointer-events: auto;
      }
    }
  }
  .MuiFormControl-root {
    width: 100%;
  }
}
.MuiPickersLayout-root {
  .Mui-selected {
    background-color: $color-primary !important;
  }
}
.input-label-date span {
  text-transform: none !important;
}
.MuiPickersPopper-root {
  z-index: 99999 !important;
}
