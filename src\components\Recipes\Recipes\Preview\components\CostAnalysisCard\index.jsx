import React, { useContext } from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import { getCurrencySymbol } from '@/helper/common/commonFunctions';
import AuthContext from '@/helper/authcontext';
import './CostAnalysisCard.scss';

const CostAnalysisCard = ({ recipeData, scaling }) => {
  const { authState } = useContext(AuthContext);
  const currency = getCurrencySymbol(authState?.currency_details);

  // Calculate total ingredient cost from all ingredients
  const totalIngredientCost =
    recipeData?.ingredients?.reduce((total, ingredient) => {
      const baseCost = ingredient?.ingredient_cost || 0;
      const wastageMultiplier = 1 + (ingredient?.ingredient_wastage || 0) / 100;
      const quantity = (ingredient?.ingredient_quantity || 0) * (scaling || 1);
      const finalCost = quantity * baseCost * wastageMultiplier;
      return total + finalCost;
    }, 0) || 0;

  const laborCost = 0.0;
  const totalCost = totalIngredientCost + laborCost;
  const costPerPortion = totalCost / (recipeData?.recipe_total_portions || 1);

  return (
    <div className="cost-analysis-card">
      <div className="cost-analysis-card__header">
        <p className="cost-analysis-card__title">
          <Icon name="Calculator" size={20} color="currentColor" />
          <span>Cost Analysis</span>
        </p>
      </div>

      <div className="cost-analysis-card__content">
        <div className="cost-analysis-card__breakdown">
          <div className="cost-analysis-card__item">
            <p className="cost-analysis-card__label">Ingredient Cost:</p>
            <p className="cost-analysis-card__value">
              {currency}
              {totalIngredientCost?.toFixed(2)}
            </p>
          </div>

          {/* <div className="cost-analysis-card__item">
            <p className="cost-analysis-card__label">Labor Estimate:</p>
            <p className="cost-analysis-card__value">
              {currency}
              {laborCost?.toFixed(2)}
            </p>
          </div> */}

          <div className="cost-analysis-card__item cost-analysis-card__item--total">
            <p className="cost-analysis-card__label cost-analysis-card__label--total">
              Total Recipe Cost:
            </p>
            <p className="cost-analysis-card__value">
              {currency}
              {totalCost?.toFixed(2)}
            </p>
          </div>

          <div className="cost-analysis-card__item">
            <p className="cost-analysis-card__label">Cost Per Portion:</p>
            <p className="cost-analysis-card__value cost-analysis-card__value--accent">
              {currency}
              {costPerPortion?.toFixed(2)}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CostAnalysisCard;
