'use client';

import React, { useContext, useEffect, useState } from 'react';
import { Box, Typography } from '@mui/material';
import AuthContext from '@/helper/authcontext';
import ReactYearRangePicker from '@/components/UI/ReactYearRangePicker';
import CustomButton from '@/components/UI/CustomButton';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import { setApiMessage } from '@/helper/common/commonFunctions';
import CustomDatePicker from '@/components/UI/CustomDatePicker';
import CustomSelect from '@/components/UI/CustomSelect';
import dayjs from 'dayjs';
import moment from 'moment';

export default function AssignBudget({
  handleCloseDrawer,
  editBudget,
  page,
  getBudgetList,
  getOneBudgetList,
  FilterBudget,
  getStaffList,
  staffList,
  setStaffList,
}) {
  const { authState, AllListsData } = useContext(AuthContext);
  const [isSubmit, setIsSubmit] = useState(false);
  const IsForecastYearJanuary =
    authState?.generalSeetings?.financial_month === 'january - december';
  const [createBudget, setCreateBudget] = useState({
    Year: '',
    Branches: '',
    validDate: '',
    user: '',
  });

  const ConvertToBackendFormat = (data) => {
    const transformedData = {
      ...(!editBudget && { forecast_year: data?.ForecastYear || '' }),
      ...(!editBudget && { branch_id: data?.Branches || '' }),
      user_id: data?.user,
      ...(editBudget && { forecast_id: editBudget?.id }),
      ...(!editBudget && {
        forecast_due_date: dayjs(data?.validDate).format('YYYY-MM-DD') || null,
      }),
      is_create_budget: editBudget ? false : true,
    };

    return transformedData;
  };
  const AssignBudgetFun = async () => {
    const requestData = ConvertToBackendFormat(createBudget);
    const ApiUrl = editBudget ? URLS.ASSIGN_FORCAST : URLS.ASSIGN_FORCAST;
    const method = editBudget ? 'post' : 'post';
    try {
      // setLoader(true);
      const { status, data } = await axiosInstance[method](ApiUrl, requestData);
      if (status === 200) {
        if (data.status) {
          setApiMessage('success', data?.message);
          handleCloseDrawer();
          if (editBudget) {
            getOneBudgetList(
              editBudget?.branch?.id,
              editBudget?.forecast_year,
              FilterBudget,
              editBudget?.index
            );
          } else {
            getBudgetList(authState?.assign_branch_ids, FilterBudget, '', page);
          }
        } else {
          setApiMessage('error', data?.message);
        }
      } else {
        setApiMessage('error', data?.message);
      }
      // setLoader(false);
    } catch (error) {
      // setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const checkValidation = () => {
    if (
      !editBudget &&
      (!createBudget?.ForecastYear ||
        !createBudget?.Branches ||
        !createBudget?.validDate ||
        dayjs(createBudget?.validDate).format('YYYY-MM-DD') === 'Invalid Date')
    ) {
      return true; // Invalid if ForecastYear or Branches are missing/empty
    }
    if (!createBudget?.user) {
      return true; // Invalid if BM/Date are missing/empty
    }
  };

  const checkValidationMessage = () => {
    if (!editBudget && !createBudget?.ForecastYear) {
      return 'Budget year must be require.';
    } else if (!editBudget && !createBudget?.Branches) {
      return 'Branch must be require.';
    } else if (!createBudget?.user) {
      return 'Assignee must be require.';
    } else if (
      !editBudget &&
      (!createBudget?.validDate ||
        dayjs(createBudget?.validDate).format('YYYY-MM-DD') === 'Invalid Date')
    ) {
      return 'Budget valid date must be require.';
    }
  };
  // const getMainIncomeId = (selectedCat) => {
  //   return (
  //     dsrCatData?.income &&
  //     dsrCatData?.income?.length > 0 &&
  //     dsrCatData?.income
  //       .filter((item) =>
  //         item?.catList?.some((cat) =>
  //           selectedCat.includes(cat.payment_type_category_id)
  //         )
  //       )
  //       .map((item) => item.id)
  //   );
  // };
  const FinancialYear = (monthRange, year) => {
    if (!monthRange || !year) return null; // Handle undefined cases

    const months = monthRange
      .split(' - ')
      .map(
        (month) => month.charAt(0).toUpperCase() + month.slice(1).toLowerCase()
      );

    const [startMonth, endMonth] = months;
    const nextYear = Number(year) + 1;

    return (
      <>
        {startMonth} {year} - {endMonth} {nextYear}
      </>
    );
  };
  const FinancialYearJan = (monthRange, year) => {
    if (!monthRange || !year) return null; // Handle undefined cases

    const months = monthRange
      .split(' - ')
      .map(
        (month) => month.charAt(0).toUpperCase() + month.slice(1).toLowerCase()
      );

    const [startMonth, endMonth] = months;
    return (
      <>
        {startMonth} {year} - {endMonth} {year}
      </>
    );
  };
  function getStartMonth(financialMonth) {
    // Mapping month names to numbers
    const monthMap = {
      january: 1,
      february: 2,
      march: 3,
      april: 4,
      may: 5,
      june: 6,
      july: 7,
      august: 8,
      september: 9,
      october: 10,
      november: 11,
      december: 12,
    };

    // Extract start month name (before ' - ')
    const startMonthName = financialMonth?.toLowerCase().split(' - ')[0];

    // Get corresponding month number
    return monthMap[startMonthName] || null;
  }
  function getAcademicYear() {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1; // getMonth() is zero-based
    const [startYear] = authState?.generalSeetings?.financial_month
      ? authState?.generalSeetings?.financial_month.split(' - ')
      : 'april';
    const monthCount = getStartMonth(startYear);
    // If financial year starts in January, it follows the calendar year
    if (monthCount === 1) {
      return `${currentYear}-${currentYear + 1}`;
    }
    // If the current month is before the financial start month, previous year is the start
    if (currentMonth < monthCount) {
      return `${currentYear - 1}-${currentYear}`;
    } else {
      return `${currentYear}-${currentYear + 1}`;
    }
  }
  const CheckIsPast = (forecastYear) => {
    const [firstYear] = forecastYear.split('-').map(Number);
    const [firstYearCurrent] = getAcademicYear().split('-').map(Number);
    if (firstYearCurrent <= firstYear) {
      return true;
    } else {
      return false;
    }
  };
  useEffect(() => {
    if (editBudget) {
      getStaffList(editBudget?.branch?.id);
      setCreateBudget({ validDate: editBudget?.forecast_due_date });
    }
  }, [editBudget]);

  return (
    <Box className="Create-budget">
      <Box className="budget-filters">
        <Typography className="sub-title-text pb16 fw500 d-flex  align-center">
          Assign budgets to Branch Managers/Hotel Managers for forecasting.
        </Typography>

        {editBudget ? (
          <>
            {editBudget?.branch?.branch_name && (
              <Typography className="title-text fw600">
                Branch :{' '}
                <span className="fw400">
                  {' '}
                  {editBudget?.branch?.branch_name}
                </span>
              </Typography>
            )}
            {editBudget?.forecast_year && (
              <Typography className="title-text fw600">
                Year :{' '}
                <span className="fw400">{editBudget?.forecast_year}</span>
              </Typography>
            )}
          </>
        ) : (
          <Box className="">
            <Box className="budget-custom-date-fields ">
              {IsForecastYearJanuary ? (
                <ReactYearRangePicker
                  value={createBudget}
                  disableFuture={true}
                  onChange={(date) => {
                    setCreateBudget({
                      ...createBudget,
                      Year: date?.Year,
                      ForecastYear: `${date?.Year.getFullYear()}-${
                        date?.Year.getFullYear() + 1
                      }`,
                    });
                    setIsSubmit(false);
                  }}
                  inputVariant="outlined"
                  isClearable={false}
                  placeholder="Select Year"
                  label={
                    <span className="field-label">
                      Choose Forecasting Year
                      <span className="required"> *</span>
                    </span>
                  }
                  isSingleMonthpicker={true}
                  // maxDate={getEndOfCurrentWeek()}
                />
              ) : (
                <ReactYearRangePicker
                  value={createBudget}
                  disableFuture={true}
                  onChange={(date) => {
                    setCreateBudget({
                      ...createBudget,
                      Year: date?.Year,
                      ForecastYear: `${date?.Year.getFullYear()}-${
                        date?.Year.getFullYear() + 1
                      }`,
                    });
                    setIsSubmit(false);
                  }}
                  inputVariant="outlined"
                  isClearable={false}
                  placeholder="Select Year"
                  label={
                    <span className="field-label">
                      Choose Forecasting Year
                      <span className="required"> *</span>
                    </span>
                  }
                  // maxDate={getEndOfCurrentWeek()}
                />
              )}
              {authState?.generalSeetings?.financial_month &&
              createBudget?.Year ? (
                <Typography className="year-range pt4">
                  {' '}
                  {IsForecastYearJanuary
                    ? FinancialYearJan(
                        authState?.generalSeetings?.financial_month,
                        moment(createBudget?.Year).format('yyyy')
                      )
                    : FinancialYear(
                        authState?.generalSeetings?.financial_month,
                        moment(createBudget?.Year).format('yyyy')
                      )}
                </Typography>
              ) : (
                <></>
              )}
            </Box>
            {/* Branch Filter */}
            <Box className="pt8">
              <CustomSelect
                label={<span>Branch</span>}
                required
                placeholder="Branch"
                showDot
                options={AllListsData?.ActiveBranchList}
                value={
                  AllListsData?.ActiveBranchList?.find((opt) => {
                    return opt?.value === createBudget?.Branches;
                  }) || ''
                }
                dropDownClass="drawer-select-dropdwon"
                onChange={(e) => {
                  setCreateBudget({
                    ...createBudget,
                    Branches: e?.value,
                  });
                  setIsSubmit(false);
                  setStaffList([]);
                  getStaffList(e?.value);
                  // value && value?.length > 0 && getCategorieList(value);
                }}
              />
            </Box>
          </Box>
        )}
        <Box>
          {/* Staff Filter */}
          {editBudget || createBudget?.Branches ? (
            staffList && staffList?.length > 0 ? (
              <Box className="pt8">
                <CustomSelect
                  placeholder="Select assignee"
                  options={staffList}
                  value={
                    staffList?.find((opt) => {
                      return opt?.value === createBudget?.user;
                    }) || ''
                  }
                  name="bm"
                  onChange={(e) => {
                    setCreateBudget({ ...createBudget, user: e?.value });
                    setIsSubmit(false);
                  }}
                  label={<span>Select assignee</span>}
                  required
                />
              </Box>
            ) : (
              <Typography className="sub-title-text pt16 fw500 d-flex  align-center color-gray">
                There are no Branch Managers or Hotel Managers assigned to this
                branch.{' '}
              </Typography>
            )
          ) : (
            <></>
          )}
        </Box>
        {editBudget ? (
          <></>
        ) : (
          <Box className="pt8">
            <CustomDatePicker
              label={<span>Budget Submission Deadline</span>}
              required
              name="birthdate"
              value={
                createBudget?.validDate ? dayjs(createBudget?.validDate) : null
              }
              onChange={(date) => {
                setCreateBudget({ ...createBudget, validDate: date });
              }}
              // disableFuture={true}
              disablePast={true}
              inputVariant="outlined"
            />
          </Box>
        )}
      </Box>
      <Box className="pt-56 pb16">
        <Box>
          {/* <Typography className="sub-title-text mb8 fw500">
            The budget for the past year will not be editable.
          </Typography> */}
          {isSubmit && (
            <Typography className="error-text mb8">
              <span className="fw600"> {checkValidationMessage()}</span>
            </Typography>
          )}
          {createBudget?.ForecastYear &&
            !CheckIsPast(createBudget?.ForecastYear) && (
              <Typography className="sub-title-text mb16 d-flex align-center noted-text fw600">
                The budget for the past year will not be editable.
              </Typography>
            )}
        </Box>

        <Box className="form-actions-btn">
          <CustomButton
            fullWidth
            variant="outlined"
            title="Cancel"
            onClick={() => {
              setIsSubmit(false);
              handleCloseDrawer();
            }}
          />
          <CustomButton
            fullWidth
            variant="contained"
            title={editBudget ? 'Assign' : 'Assign'}
            disabled={
              staffList?.length === 0 && (editBudget || createBudget?.Branches)
            }
            onClick={() => {
              setIsSubmit(true);
              if (!checkValidation()) {
                AssignBudgetFun();
              }
            }}
          />
        </Box>
      </Box>
    </Box>
  );
}
