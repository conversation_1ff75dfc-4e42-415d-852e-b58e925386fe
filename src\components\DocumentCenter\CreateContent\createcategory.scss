.create-document-page {
  background-color: var(--color-white);
  padding: var(--spacing-xxl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-xs);

  .external-link-section {
    .external-link-sec {
      display: grid;
      justify-content: space-between;
      align-items: center;
      column-gap: 12px;
      grid-template-columns: calc(100% - 24px - 200px - 24px) 200px 24px;
      .cancel-icon {
        fill: var(--color-primary);
        margin-top: 16px;
      }
    }
  }
  .external-link-section.error {
    .external-link-sec:last-child {
      padding-bottom: 0 !important;
    }
  }
  .upload-category-sec {
    width: 256px;
  }
  .uploaded-media-sec {
    width: 256px;
    max-width: 100%;
    svg {
      width: var(--icon-size-xs);
      height: var(--icon-size-xs);
    }
    .image-sec {
      max-width: calc(100% - 50px);
      .file-name {
        width: 90%;
      }
    }
    @media (max-width: 799px) {
      max-width: 100%;
    }
  }
  .display-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(0, 250px));
    gap: var(--spacing-sm);
  }
  .create-content-grid {
    display: grid;
    grid-template-columns: 40% calc(60% - var(--spacing-lg) - var(--spacing-lg));
    gap: var(--spacing-lg);
    .content-left {
      padding-right: var(--spacing-lg);
      border-right: var(--normal-sec-border);
    }
  }
  .logo-section {
    max-width: max-content;
    max-height: max-content;
    position: relative;
    img {
      max-height: 100px;
      object-fit: cover;
      border-radius: var(--border-radius-lg);
    }
    .cancel-icon {
      position: absolute;
      top: -11px;
      right: -11px;
      width: var(--icon-size-sm);
      height: var(--icon-size-sm);
    }
  }
}
.form-checkbox {
  display: flex;

  .check-box-form {
    flex-direction: row-reverse;
    margin-left: 0;

    .MuiFormControlLabel-label {
      font-family: var(--font-family-primary);
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-regular);
      color: var(--text-color-black);
      line-height: var(--line-height-base);
    }
    .MuiCheckbox-root.Mui-checked {
      color: var(--color-primary);
    }
  }
  .form-row {
    flex-direction: row !important;
    .MuiButtonBase-root {
      padding-left: 0 !important;
    }
  }
  .approved-checkbox {
    color: var(--text-green);

    .MuiFormControlLabel-label {
      font-weight: 600;
    }

    .Mui-disabled.MuiTypography-root {
      color: var(--text-green) !important;
    }

    .MuiTypography-root {
      color: var(--text-green) !important;
    }

    svg {
      fill: var(--text-green) !important;
    }
  }

  .reject-checkbox {
    color: var(--color-danger);

    .MuiFormControlLabel-label {
      font-weight: 600;
    }

    .Mui-disabled.MuiTypography-root {
      color: var(--color-danger) !important;
    }

    .MuiTypography-root {
      color: var(--color-danger) !important;
    }

    svg {
      fill: var(--color-danger) !important;
    }
  }
  .Mui-disabled {
    opacity: 1;
    cursor: not-allowed !important;
    .MuiCheckbox-root.Mui-checked {
      color: var(--color-primary);
    }
    .MuiTypography-root {
      color: var(--color-black) !important;
    }
  }
}
