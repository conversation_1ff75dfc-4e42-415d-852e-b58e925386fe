'use client';

import React, { useEffect, useContext, useState } from 'react';
import { Box, Typography, Tooltip } from '@mui/material';
import AuthContext from '@/helper/authcontext';
import { useRouter } from 'next/navigation';
import CustomDatePicker from '@/components/UI/CustomDatePicker';
import CustomButton from '@/components/UI/CustomButton';
import CustomSelect from '@/components/UI/CustomSelect';
import { DateFormat, setApiMessage } from '@/helper/common/commonFunctions';
import CustomSearch from '@/components/UI/CustomSearch';
import { identifiers } from '@/helper/constants/identifier';
import DialogBox from '@/components/UI/Modalbox';
import {
  removeFromStorage,
  saveToStorage,
  fetchFromStorage,
} from '@/helper/context/storage';
import { DataGrid } from '@mui/x-data-grid';
import CustomOrgPagination from '@/components/UI/customPagination';
// import AddIcon from '@mui/icons-material/Add';
import dayjs from 'dayjs';
// import EditIcon from '../ActionIcons/EditIcon';
import ViewIcon from '../ActionIcons/ViewIcon';
import DeleteIcon from '../ActionIcons/DeleteIcon';
import RemarkIcon from '../ActionIcons/RemarkIcon';
import NoDataView from '../UI/NoDataView';
import CommonUserDetails from '../UI/CommonUserDetails';
import DeleteModal from '../UI/DeleteModal';
import { changeRequestService } from '@/services/changeRequestService';
import ContentLoader from '../UI/ContentLoader';
import ClearOutlinedIcon from '@mui/icons-material/ClearOutlined';
import CheckIcon from '@mui/icons-material/Check';
import { staticOptions } from '@/helper/common/staticOptions';

const ChangeRequestStaffPage = ({
  tab,
  isOwn,
  isSuperAdmin,
  tabValue,
  rowsPerPage,
  setRowsPerPage,
  setCrList,
  crList,
  searchValue,
  setSearchValue,
  page,
  setPage,
  totalCount,
  setTotalCount,
  filterData,
  setFilterData,
  filterDataApplied,
  setFilterDataApplied,
  activeTab,
}) => {
  const router = useRouter();

  const { authState } = useContext(AuthContext);
  const { userdata, setUserdata } = useContext(AuthContext);
  const [loader, setLoader] = useState(true);

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteId, setDeleteId] = useState(null);
  // Delete change request
  const deleteChangeRequestHandler = async (id) => {
    try {
      const data = await changeRequestService.deleteChangeRequest(id);
      if (data?.status) {
        handleCloseDeleteDialog();
        if (crList?.length === 1 && page !== 1) {
          getCRList(
            searchValue,
            Number(page) - 1,
            filterDataApplied?.status,
            filterDataApplied?.date,
            filterDataApplied?.search
          );
          setPage(Number(page) - 1);
        } else {
          getCRList(
            searchValue,
            page,
            filterDataApplied?.status,
            filterDataApplied?.date,
            filterDataApplied?.search
          );
        }
        setApiMessage('success', data?.message);
      } else {
        setApiMessage('error', data?.message);
      }
      setLoader(false);
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const handleOpenDeleteDialog = (id) => {
    setDeleteId(id);
    setDeleteDialogOpen(true);
  };
  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setDeleteId(null);
  };
  const columns = [
    {
      field: 'id',
      headerName: 'ID',
      width: 48,
      minWidth: 48,
      flex: 0,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      // renderCell: (params) => {
      //   return (
      //     <Box className="d-flex align-center h100">
      //       {params?.row?.change_request_user?.employment_number}
      //     </Box>
      //   );
      // }
    },
    {
      field: 'name',
      headerName: 'Name',
      width: 280,
      minWidth: 280,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        let userData = params?.row?.change_request_user;
        return (
          <CommonUserDetails
            userData={userData}
            filterDataApplied={filterDataApplied}
            searchValue={searchValue}
            page={page}
            rowsPerPage={rowsPerPage}
            setUserdata={setUserdata}
            authState={authState}
            navigationProps={{ changeReq: true }}
          />
        );
      },
    },
    {
      field: 'change_request_subject',
      headerName: 'Subject',
      width: 250,
      minWidth: 250,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
    },
    {
      field: 'createdAt',
      headerName: 'Date',
      width: 150,
      minWidth: 150,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return <Box className="">{DateFormat(params?.value, 'dates')}</Box>;
      },
    },
    {
      field: 'change_request_status',
      headerName: 'Status',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100 text-capital">
            {params?.value === 'reopened' ? (
              <Typography className="sub-title-text  status-yellow fw600">
                Re-opened
              </Typography>
            ) : params?.value === 'pending' ? (
              <Typography className="sub-title-text draft fw600">
                {params?.value}
              </Typography>
            ) : params?.value === 'approved' ? (
              <Typography className="sub-title-text active-onboarding fw600">
                {params?.value}
              </Typography>
            ) : params?.value === 'closed' ? (
              <Typography className="sub-title-text closed fw600">
                {params?.value}
              </Typography>
            ) : params?.value === 'rejected' ||
              params?.value === 'deleted' ||
              params?.value === 'cancelled' ? (
              <Typography className="sub-title-text failed fw600">
                {params?.value}
              </Typography>
            ) : (
              <Typography className="sub-title-text success fw600">
                approved
              </Typography>
            )}
          </Box>
        );
      },
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 120,
      minWidth: 120,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex justify-center actions">
            <Box className="d-flex align-center actions-cr gap-sm">
              {/* {authState?.id === params?.row?.change_request_user?.id &&
                params?.row?.change_request_status === 'rejected' &&
                params?.row?.change_request_status !== 'deleted' && (
                  <Tooltip
                    title={<Typography>Edit</Typography>}
                    arrow
                    classes={{
                      tooltip: 'info-tooltip-container',
                    }}
                  >
                    <Box>
                      <EditIcon
                        style={{ width: '16px', height: '16px' }}
                        onClick={() => {
                          setUserdata({
                            id: params?.row?.id,
                            filterData: filterDataApplied,
                            searchValue: searchValue,
                            page: page,
                            rowsPerPage: rowsPerPage,
                            tabValue: tabValue,
                            changeReq: true,
                          });
                          saveToStorage(identifiers?.RedirectData, {
                            id: params?.row?.id,
                            filterData: filterDataApplied,
                            searchValue: searchValue,
                            tabValue: tabValue,
                            rowsPerPage: rowsPerPage,
                            page: page,
                            changeReq: true,
                          });
                          router.push(
                            `/change-req?isEdit=true&id=${params?.row?.id}`
                          );
                        }}
                      />
                    </Box>
                  </Tooltip>
                )} */}
              <Tooltip
                title={<Typography>View</Typography>}
                arrow
                classes={{
                  tooltip: 'info-tooltip-container',
                }}
              >
                <Box>
                  <ViewIcon
                    onClick={() => {
                      setUserdata({
                        id: params?.row?.id,
                        filterData: filterDataApplied,
                        searchValue: searchValue,
                        tabValue: tabValue,
                        rowsPerPage: rowsPerPage,
                        page: page,
                        changeReq: true,
                      });
                      saveToStorage(identifiers?.RedirectData, {
                        id: params?.row?.id,
                        filterData: filterDataApplied,
                        searchValue: searchValue,
                        tabValue: tabValue,
                        rowsPerPage: rowsPerPage,
                        page: page,
                        changeReq: true,
                      });
                      router.push(
                        `/change-request/${params?.row?.id}?isView=true`
                      );
                    }}
                  />
                </Box>
              </Tooltip>

              {authState?.UserPermission?.change_request === 2 &&
                params?.row?.change_request_status !== 'rejected' &&
                params?.row?.change_request_status !== 'closed' &&
                params?.row?.change_request_status !== 'cancelled' &&
                params?.row?.change_request_status !== 'deleted' &&
                params?.row?.change_request_status !== 'approved' &&
                authState?.id !== params?.row?.change_request_user?.id && (
                  <Tooltip
                    title={<Typography>Remark</Typography>}
                    arrow
                    classes={{
                      tooltip: 'info-tooltip-container',
                    }}
                  >
                    <Box>
                      <RemarkIcon
                        onClick={() => {
                          setUserdata({
                            id: params?.row?.id,
                            filterData: filterDataApplied,
                            searchValue: searchValue,
                            tabValue: tabValue,
                            rowsPerPage: rowsPerPage,
                            page: page,
                            changeReq: true,
                          });
                          saveToStorage(identifiers?.RedirectData, {
                            id: params?.row?.id,
                            filterData: filterDataApplied,
                            searchValue: searchValue,
                            tabValue: tabValue,
                            rowsPerPage: rowsPerPage,
                            page: page,
                            changeReq: true,
                          });
                          router.push(
                            `/change-request/${params?.row?.id}?isRemark=true`
                          );
                        }}
                      />
                    </Box>
                  </Tooltip>
                )}

              {authState?.UserPermission?.change_request === 2 &&
                authState?.id !== params?.row?.change_request_user?.id &&
                params?.row?.change_request_status !== 'deleted' &&
                tab === 'staff' && (
                  <Tooltip
                    title={<Typography>Delete</Typography>}
                    arrow
                    classes={{
                      tooltip: 'info-tooltip-container',
                    }}
                  >
                    <Box>
                      <DeleteIcon
                        onClick={() => handleOpenDeleteDialog(params?.row?.id)} //deleteChangeRequest()
                      />
                    </Box>
                  </Tooltip>
                )}
            </Box>
          </Box>
        );
      },
    },
  ];
  // List of change request
  const getCRList = async (search, pageNo, statusValue, date, Rpp) => {
    setLoader(true);
    if (authState?.UserPermission?.change_request === 2 && tab === 'staff') {
      try {
        const data = await changeRequestService.getChangeRequestList(
          search,
          pageNo,
          statusValue,
          date,
          Rpp ? Rpp : rowsPerPage
        );
        if (data) {
          setCrList(data?.data);
          removeFromStorage(identifiers?.RedirectData);
          setUserdata();
          setTotalCount(data?.count);
          setTimeout(() => {
            setLoader(false);
          }, 500);
        }
      } catch (error) {
        setTimeout(() => {
          setLoader(false);
        }, 500);
        setCrList([]);
        setTotalCount(0);
        setApiMessage('error', error?.response?.data?.message);
      }
    } else {
      setLoader(true);
      try {
        const data = await changeRequestService.getOwnChangeRequestList(
          search,
          pageNo,
          statusValue,
          date,
          Rpp ? Rpp : rowsPerPage
        );
        if (data) {
          setCrList(data?.data);
          removeFromStorage(identifiers?.RedirectData);
          setUserdata();
          setTotalCount(data?.count);
          setTimeout(() => {
            setLoader(false);
          }, 500);
        }
      } catch (error) {
        setCrList([]);
        setTotalCount(0);
        setTimeout(() => {
          setLoader(false);
        }, 500);
        setApiMessage('error', error?.response?.data?.message);
      }
    }
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      setPage(1);
      getCRList(
        searchValue,
        1,
        filterDataApplied?.status,
        filterDataApplied?.date,
        filterDataApplied?.search
      );
    }
  };
  const onPageChange = (newPage) => {
    setPage(newPage);
    getCRList(
      searchValue,
      newPage,
      filterDataApplied?.status,
      filterDataApplied?.date,
      filterDataApplied?.search
    );
  };
  const OnRowPerPage = (newPage) => {
    setRowsPerPage(newPage);
    setPage(1);
    getCRList(
      searchValue,
      1,
      filterDataApplied?.status,
      filterDataApplied?.date,
      newPage,
      filterDataApplied?.search
    );
  };

  useEffect(() => {
    if (
      (!fetchFromStorage(identifiers?.RedirectData) &&
        userdata?.page === undefined &&
        fetchFromStorage(identifiers?.RedirectData)?.IsFromUser === undefined &&
        userdata?.IsFromUser === undefined) ||
      !fetchFromStorage(identifiers?.RedirectData)?.changeReq
    ) {
      getCRList(
        searchValue,
        page,
        filterDataApplied?.status,
        filterDataApplied?.date,
        rowsPerPage,
        filterDataApplied?.search
      );
    }
  }, [authState?.UserPermission?.change_request, activeTab]);

  useEffect(() => {
    if (
      fetchFromStorage(identifiers?.RedirectData) &&
      fetchFromStorage(identifiers?.RedirectData)?.IsFromUser &&
      fetchFromStorage(identifiers?.RedirectData)?.changeReq
    ) {
      const fdata = fetchFromStorage(identifiers?.RedirectData);
      setPage(fdata?.page);
      setFilterData(fdata?.filterData);
      setFilterDataApplied(fdata?.filterData);
      setSearchValue(fdata?.searchValue);
      setRowsPerPage(fdata?.rowsPerPage);
      getCRList(
        fdata?.searchValue,
        fdata?.page,
        fdata?.filterData?.status,
        fdata?.filterData?.date,
        fdata?.rowsPerPage,
        fdata?.filterData?.search
      );
    } else if (userdata && userdata?.IsFromUser && userdata?.changeReq) {
      const fdata = userdata;
      setPage(fdata?.page);
      setFilterData(fdata?.filterData);
      setFilterDataApplied(fdata?.filterData);
      setSearchValue(fdata?.searchValue);
      setRowsPerPage(fdata?.rowsPerPage);
      getCRList(
        fdata?.searchValue,
        fdata?.page,
        fdata?.filterData?.status,
        fdata?.filterData?.date,
        fdata?.rowsPerPage,
        fdata?.filterData?.search
      );
    } else {
      removeFromStorage(identifiers?.RedirectData);
      setUserdata();
    }
  }, [
    fetchFromStorage(identifiers?.RedirectData)?.IsFromUser,
    userdata?.IsFromUser,
  ]);
  return (
    <>
      <Box className="request-change-container">
        <Box
          className={
            isOwn || isSuperAdmin
              ? 'change-request change-request-tab-container'
              : 'change-request pt24'
          }
        >
          <Box>
            <Box className="search-section-wrap">
              <Box className="search-section-fields">
                <CustomSearch
                  setSearchValue={(e) => {
                    setFilterData({
                      ...filterData,
                      search: e,
                    });
                    setSearchValue(e);
                  }}
                  searchValue={filterData?.search}
                  onKeyPress={handleKeyPress}
                />
              </Box>
              <Box className="search-section-fields">
                <CustomSelect
                  placeholder="Status"
                  options={staticOptions?.CHANGE_REQUEST_OPTION}
                  value={
                    staticOptions?.CHANGE_REQUEST_OPTION?.find(
                      (opt) => opt?.value === filterData?.status
                    ) || ''
                  }
                  onChange={(e) => {
                    setFilterData({
                      ...filterData,
                      status: e?.value,
                    });
                  }}
                  menuPortalTarget={document.body}
                  styles={{
                    menuPortal: (base) => ({
                      ...base,
                      zIndex: 9999,
                    }),
                  }}
                />
              </Box>
              <Box className="search-section-fields">
                <CustomDatePicker
                  error={false}
                  name="date"
                  format="DD/MM/YYYY"
                  value={dayjs(filterData?.date)}
                  onChange={(date) => {
                    setFilterData({
                      ...filterData,
                      date: date,
                    });
                  }}
                  inputVariant="outlined"
                />
              </Box>
              <Box>
                <CustomButton
                  isIconOnly
                  startIcon={
                    <Tooltip
                      title={
                        <Typography className="sub-title-text">
                          Apply Filter
                        </Typography>
                      }
                      arrow
                      classes={{
                        tooltip: 'info-tooltip-container',
                      }}
                    >
                      <CheckIcon />
                    </Tooltip>
                  }
                  onClick={() => {
                    setPage(1);
                    setFilterDataApplied({
                      status: filterData?.status,
                      date: filterData?.date,
                      search: filterData?.search,
                    });
                    getCRList(
                      filterData?.search,
                      1,
                      filterData?.status,
                      filterData?.date,
                      rowsPerPage
                    );
                  }}
                />
              </Box>
              <Box>
                <CustomButton
                  variant="outlined"
                  isIconOnly
                  startIcon={
                    <Tooltip
                      title={
                        <Typography className="sub-title-text">
                          Clear Filter
                        </Typography>
                      }
                      arrow
                      classes={{
                        tooltip: 'info-tooltip-container',
                      }}
                    >
                      <ClearOutlinedIcon />
                    </Tooltip>
                  }
                  onClick={() => {
                    setPage(1);
                    setFilterData({
                      date: '',
                      status: '',
                      search: '',
                    });
                    setFilterDataApplied({
                      date: '',
                      status: '',
                      search: '',
                    });
                    setSearchValue('');
                    getCRList('', 1, '', '', '', '');
                  }}
                />
              </Box>

              {/* <CustomButton
                variant="outlined"
                title="Search"
                fullWidth={false}
                onClick={() => {
                  setPage(1);
                  getCRList(
                    searchValue,
                    1,
                    filterDataApplied?.status,
                    filterDataApplied?.date
                  );
                }}
              /> */}

              {/* {authState?.web_user_active_role_id !== 1 && tab !== 'staff' && (
                <CustomButton
                  variant="contained"
                  title="Add Change request"
                  leftIcon={<AddIcon />}
                  fullWidth={false}
                  onClick={() => {
                    setUserdata({
                      filterData: filterDataApplied,
                      searchValue: searchValue,
                      page: page,
                      tabValue: tabValue,
                      rowsPerPage: rowsPerPage,
                      changeReq: true,
                    });
                    saveToStorage(identifiers?.RedirectData, {
                      filterData: filterDataApplied,
                      searchValue: searchValue,
                      tabValue: tabValue,
                      page: page,
                      rowsPerPage: rowsPerPage,
                      changeReq: true,
                    });
                    router.push('/change-req');
                  }}
                />
              )} */}
            </Box>
          </Box>
          <Box className="table-container table-layout">
            {loader ? (
              <ContentLoader />
            ) : (
              <>
                {crList && crList?.length === 0 ? (
                  <NoDataView
                    // image
                    title="No Change Request Found"
                    description="There is no Change Request available at the moment."
                  />
                ) : (
                  <>
                    <DataGrid
                      rows={crList}
                      columns={columns}
                      pageSize={rowsPerPage}
                      checkboxSelection={false} // Disable default checkbox column
                      disableSelectionOnClick // Disable row selection on click
                      // columnVisibilityModel={{
                      //   actions:
                      //     authState?.UserPermission?.branch === 2 ? true : false
                      // }}
                      hideMenuIcon
                    />
                    <CustomOrgPagination
                      currentPage={page}
                      // totalPages={totalPages}
                      totalCount={totalCount}
                      rowsPerPage={rowsPerPage}
                      onPageChange={onPageChange}
                      OnRowPerPage={OnRowPerPage}
                    />
                  </>
                )}
              </>
            )}
          </Box>
        </Box>
      </Box>

      <DialogBox
        open={deleteDialogOpen}
        handleClose={handleCloseDeleteDialog}
        title="Confirmation"
        className="delete-modal"
        dividerClass="delete-modal-divider"
        content={
          <DeleteModal
            handleCancel={handleCloseDeleteDialog}
            handleConfirm={() => deleteChangeRequestHandler(deleteId)}
            text="Are you sure you want to delete this Change Request?"
          />
        }
      />
    </>
  );
};

export default ChangeRequestStaffPage;
