.side-letter-page {
  .employee-contract-checklist .rtwc-upload {
    max-width: 400px;
  }
  .delete-wrap {
    .delete-icon {
      background-color: var(--text-color-danger);
      border-color: var(--text-color-danger);
      color: var(--text-color-white);
    }
    .select-delete-wrap {
      display: flex;
      gap: var(--spacing-sm);

      @media (max-width: 420px) {
        width: 100%;
      }
    }
    .title-text {
      font-size: 16px;
      font-weight: 600;
    }
  }
  .contract-history-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 22px;
    @media (max-width: 440px) {
      grid-template-columns: repeat(auto-fill, minmax(100%, 1fr));
    }
    .contract-history-wrap {
      display: flex !important;
      flex-direction: column !important;
      box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
      padding: 12px 12px;
      border-radius: 8px;

      .custom-button {
        padding: 6px 26px !important;
        width: 100px !important;
      }

      .custom-button :hover {
        svg {
          fill: var(--icon-color-white) !important;
        }
      }

      .title-section {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        column-gap: 10px;

        .title {
          text-align: start;
          width: calc(100% - 100px);
        }
      }

      .action-sec {
        display: flex;
        margin-top: 6px;
        align-items: center;

        .delete-icon,
        .view-icon {
          margin-left: 10px;
          width: 18px;
          height: 18px;
          cursor: pointer;
        }

        .view-icon {
          margin-right: 5px;
        }
      }

      .upload-sec,
      .download-sec {
        width: 60px;
        height: 60px;
        margin: 0 !important;

        svg {
          width: 24px;
          height: 24px;
        }

        .upload-text,
        .download-text {
          display: none;
        }
      }

      .upload-sec {
        border: 3px dashed var(--color-primary);
      }

      .download-sec {
        border: 3px solid var(--color-danger);
      }
    }

    .contract-history-wrap {
      .contract-name {
        line-height: 0px;
        color: var(--text-green) !important;
        align-items: center;
      }
      .action-sec {
        color: var(--text-green);
        align-items: center;

        .inactive-check-box {
          padding: 0px;
        }

        .custom-button {
          width: 80px !important;
        }

        svg {
          fill: var(--text-color-danger) !important;
        }

        .delete-icon {
          fill: var(--text-color-danger) !important;
          width: 17px;
          height: 17px;
        }
      }
    }
  }

  @media (max-width: 540px) {
    .search-section {
      flex-wrap: wrap;
    }
  }

  .check-box-form {
    margin-left: 0;
    .MuiFormControlLabel-label {
      font-family: var(--font-family-primary);
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-regular);
      color: var(--text-color-black);
      line-height: var(--line-height-base);
    }
  }

  .info-icon {
    height: 18px;
    width: 18px;
    margin-left: 8px;
    fill: var(--color-black);
    &:hover {
      fill: var(--color-primary);
    }
  }

  .download-icon {
    fill: var(--color-primary);
  }
  .view-icon-sec {
    display: flex;
    align-items: flex-end;
    .view-icon {
      fill: var(--color-green);
      width: 22px;
      height: 22px;
    }
  }

  .created-date {
    width: 20px;
    height: 20px;
    margin-right: 6px;
    margin-left: -2px;
  }
  .right-side-letter {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .donload-checked {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: flex-end;
    }
  }
  .active-onboarding {
    background-color: var(--color-white);
    color: var(--text-green) !important;
  }
  .confimation-dates {
    svg {
      fill: var(--color-green);
    }
    p {
      color: var(--color-green);
    }
  }
  .check-read-confirmation {
    margin-top: 12px;
    .check-box-form {
      align-items: center;
    }
    .MuiButtonBase-root {
      padding: 0;
      padding-right: 8px;
      margin-left: -2px;
    }
  }
  .pagination-wrap {
    border-top: 0;
  }

  .delete-button {
    background-color: var(--text-color-danger);
    border-color: var(--text-color-danger);
    color: var(--text-color-white);
  }

  .search-section {
    .select-delete-wrap {
      display: flex;
      gap: 10px;
      align-items: center;
    }
  }
}
.read-letter {
  .contract-history-wrap {
    box-shadow: none !important;
    padding: 0px;
  }
}
