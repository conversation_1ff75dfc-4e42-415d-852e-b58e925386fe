'use client';

import React from 'react';
import { styled } from '@mui/material/styles';
import Switch from '@mui/material/Switch';
import FormControlLabel from '@mui/material/FormControlLabel';

// Styled component for the FormControlLabel with IOSSwitch
const StyledFormControlLabel = styled(FormControlLabel)(({ theme }) => ({
  margin: 0,
  gap: 'var(--spacing-sm)',
  '& .MuiFormControlLabel-label': {
    fontFamily: 'var(--font-family-primary)',
    fontSize: 'var(--font-size-sm)',
    fontWeight: 'var(--font-weight-regular)',
    color: 'var(--text-color-black)',
    lineHeight: 'var(--line-height-base)',
  },
  '& .MuiSwitch-root': {
    width: 33,
    height: 18,
    padding: 0,
    '& .MuiSwitch-switchBase': {
      padding: 'var(--spacing-none)',
      margin: 'var(--spacing-xxs)',
      transitionDuration: '300ms',
      '&.Mui-checked': {
        transform: 'translateX(16px)',
        color: 'var(--text-color-white)',
        '& + .MuiSwitch-track': {
          backgroundColor: 'var(--color-primary)',
          opacity: 'var(--opacity-100)',
          border: 0,
        },
        '&.Mui-disabled + .MuiSwitch-track': {
          opacity: 'var(--opacity-5)',
        },
      },
      '&.Mui-focusVisible .MuiSwitch-thumb': {
        color: 'var( --color-green)',
        border: '6px solid var(--border-color-white)',
      },
      '&.Mui-disabled .MuiSwitch-thumb': {
        color: 'var(--text-color-white)',
      },
      '&.Mui-disabled + .MuiSwitch-track': {
        opacity: theme.palette.mode === 'light' ? 0.7 : 0.3,
      },
    },
    '& .MuiSwitch-thumb': {
      boxSizing: 'border-box',
      width: 14,
      height: 14,
      boxShadow: '0 2px 4px 0 rgba(0,0,0,0.2)',
    },
    '& .MuiSwitch-track': {
      borderRadius: 34 / 2,
      backgroundColor: 'var(--color-light-gray)',
      opacity: 'var(--opacity-100)',
      transition: theme.transitions.create(['background-color'], {
        duration: 500,
      }),
    },
  },
}));

function CustomSwitch({ label, checked, onChange, disabled }) {
  return (
    <StyledFormControlLabel
      control={
        <Switch
          focusVisibleClassName=".Mui-focusVisible"
          disableRipple
          checked={checked}
          onChange={onChange}
          inputProps={{ 'aria-label': label }}
          disabled={disabled}
        />
      }
      label={label}
    />
  );
}

export default CustomSwitch;
