import React from 'react';
import { Box } from '@mui/material';
import { generateMetadata } from '@/helper/common/commonFunctions';
import DashboardMain from '@/components/Dashboard/DashboardMain/index';

export const metadata = generateMetadata({
  pageTitle: 'Dashboard',
});

const DashboardMainPage = ({ params }) => {
  return (
    <Box className="main-page-container">
      <DashboardMain params={params} />
    </Box>
  );
};

export default DashboardMainPage;
