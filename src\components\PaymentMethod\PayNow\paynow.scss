@import '@/styles/variable.scss';

.add-card-container {
  .add-card-header {
    font-size: 20px;

    .add-card-text-wrap {
      @media (max-width: 575px) {
        width: 100%;
      }
    }

    .card-select {
      .MuiSelect-select {
        padding: 3px 7px 0px 15px;
        min-width: 150px;
        margin-top: 0px;
      }

      fieldset {
        height: 31px;
        margin-top: 4px;
      }

      .placeholder {
        font-weight: 300 !important;
        padding-top: 2px;
      }

      .MuiSvgIcon-root {
        margin-top: 1px;
      }

      @media (max-width: 575px) {
        width: 100%;
      }
    }

    @media (max-width: 575px) {
      flex-direction: column;
      row-gap: 10px;
    }
  }

  .add-cards {
    margin-top: 10px;
    gap: 10px;

    .add-card-wrap {
      background-color: var(--color-white);
      padding: var(--spacing-lg) var(--spacing-xl);
      // border: 1px solid #e0e0e0;
      border: var(--normal-sec-border);
      border-radius: var(--border-radius-lg);
      box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.05);

      .card-content-wrap {
        padding: 0px;

        .card-content {
          .card-number {
            font-family: var(--font-family-primary);
            font-size: 16px;
            font-weight: 500;

            @media (max-width: 575px) {
              font-size: 14px;
            }
          }

          .card-type-edit-wrap {
            font-size: 14px;
            color: $color-Dark-20;
            gap: 10px;
            margin-top: var(--spacing-xs);
            .card-type,
            .edit-wrap {
              font-family: var(--font-family-primary);
              font-size: 14px;
            }

            .edit-wrap {
              display: flex;
              align-items: center;
              gap: var(--spacing-xxs);
              cursor: pointer;
              text-decoration: underline;
              .edit-wrap-icon {
                width: var(--icon-size-xsm);
                height: var(--icon-size-xsm);
              }
              &.edit {
                color: var(--text-color-black);
              }
              &.remove {
                color: var(--text-color-danger);
              }
              &.default {
                color: var(--text-color-primary);
              }
            }
          }
        }

        .card-radio {
          padding: 0px;
          color: var(--icon-color-primary);
        }

        @media (max-width: 575px) {
          gap: 10px;
        }
      }

      .card-brand-wrap {
        text-align: right;

        .set-default-text {
          font-size: 14px;
          cursor: pointer;
        }

        .default-card-text {
          background-color: var(--color-primary);
          color: var(--color-white);
          padding: var(--spacing-none) var(--spacing-xs);
          border-radius: var(--border-radius-xs);
          cursor: pointer;
        }
        .default-card {
          fill: var(--icon-color-primary);
          width: 24px;
          height: 24px;
        }
        .card-brand {
          color: var(--text-color-slate-gray);
        }
      }

      @media (max-width: 575px) {
        padding: 24px 14px;
      }
    }
  }

  .add-new-btn-wrap {
    .add-new-btn {
      padding: 4px 10px !important;
      font-size: 14px !important;
      background-color: #ffffff;
      color: $color-primary !important;

      &:hover {
        box-shadow: none !important;
      }
    }
  }
}
