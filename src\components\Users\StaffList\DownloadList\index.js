'use client';

import React, { useEffect, useState } from 'react';
import { Box, Tooltip, Typography } from '@mui/material';
import CustomButton from '@/components/UI/CustomButton';
import CustomCheckbox from '@/components/UI/CustomCheckbox';
import { setApiMessage, getFields } from '@/helper/common/commonFunctions';
import PreLoader from '@/components/UI/Loader';
import NoDataView from '@/components/UI/NoDataView';
import {
  getLeavingChecklist,
  verifyLeavingChecklist,
} from '@/services/resignationService';
import { ListManager } from 'react-beautiful-dnd-grid';
import { MenuIcon } from '@/helper/common/images';

export default function DownloadList({ searchValue, filterData }) {
  const [loader, setLoader] = useState(false);
  const [filedName, setFiledName] = useState();
  const [checkId, setCheckId] = useState([]);
  const [isLoader, setIsLoader] = useState(false);

  const handleSelectAllClick = (event) => {
    if (event.target.checked) {
      const newSelecteds = filedName?.map((n) => n?.id);
      setCheckId(newSelecteds);
      return;
    }
    setCheckId([]);
  };
  const handleCheck = (value) => {
    const currentIndex = checkId.indexOf(value);
    const newChecked = [...checkId];
    if (currentIndex === -1) {
      newChecked.push(value);
    } else {
      newChecked.splice(currentIndex, 1);
    }
    setCheckId(newChecked);
  };

  const getJoiningRemovingChecklist = async (id) => {
    setLoader(true);
    try {
      const response = await getLeavingChecklist(id);
      if (response.success) {
        setFiledName(response.data);
        const filterdata = response.data.filter(
          (f) => f?.leaving_checklist_status === 1
        );
        const selectedID =
          filterdata && filterdata.length > 0 && getFields(filterdata, 'id');
        selectedID && setCheckId(selectedID);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    } finally {
      setLoader(false);
    }
  };

  const checklistverification = async (id) => {
    setIsLoader(true);
    try {
      const response = await verifyLeavingChecklist(id, {
        verified_checklist: checkId,
      });
      if (response.success) {
        setJoiningChecklist(false);
        getResignationList(currentPage, searchValue, filterData);
      } else {
        setApiMessage('error', response.message);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    } finally {
      setLoader(false);
      setIsLoader(false);
    }
  };
  const onDragEnd = (result) => {
    const { source, destination } = result;
    const newData = filedName;
    const [movedRow] = newData.splice(source?.index, 1);
    newData.splice(destination?.index, 0, movedRow);
    setFiledName(newData);
  };

  //   useEffect(() => {
  //     if (resignId !== undefined) {
  //       getJoiningRemovingChecklist(resignId);
  //     }
  //   }, [resignId]);
  useEffect(() => {
    getJoiningRemovingChecklist(12);
  }, []);

  return (
    <Box className="download-staff-list">
      {loader ? (
        <PreLoader />
      ) : filedName && filedName.length > 0 ? (
        <>
          <Box className="download-staff-section">
            <Box className="checklist-item">
              <CustomCheckbox
                checked={
                  filedName?.length > 0 && checkId?.length === filedName?.length
                }
                onChange={(e) => handleSelectAllClick(e)}
                // label={item?.checkList_name}
                label="Select All"
              />
            </Box>
            <Box />
            <ListManager
              items={filedName || []}
              direction="horizontal"
              maxItems={1}
              render={(item) => (
                <Box key={item.id} className="checklist-item">
                  <CustomCheckbox
                    checked={checkId.indexOf(item.id) !== -1}
                    onChange={() => handleCheck(item.id)}
                    label={item?.checkList_name}
                    // label="First Name"
                  />
                  <CustomButton
                    variant="outlined"
                    isIconOnly
                    startIcon={
                      <Tooltip
                        title={
                          <Typography className="sub-title-text">
                            Drag and drop to reorder
                          </Typography>
                        }
                        classes={{
                          tooltip: 'info-tooltip-container',
                        }}
                        arrow
                      >
                        <MenuIcon />
                      </Tooltip>
                    }
                  />
                </Box>
              )}
              onDragEnd={onDragEnd}
            />
          </Box>
        </>
      ) : (
        <NoDataView
          title="No checklist found"
          description="There is no checklist available at the moment."
        />
      )}

      <Box className="form-actions-btn">
        <CustomButton
          type="submit"
          variant="outlined"
          title="Cancel"
          onClick={() => setJoiningChecklist(false)}
        />
        <CustomButton
          type="submit"
          variant="contained"
          disabled={checkId?.length === 0 || isLoader}
          title={`${isLoader ? 'Downloading...' : 'Download'}`}
          onClick={() => checklistverification(resignId)}
        />
      </Box>
    </Box>
  );
}
