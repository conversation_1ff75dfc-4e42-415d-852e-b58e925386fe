import React from 'react';
import Image from 'next/image';
import AppLogo from '../../../../public/images/app-logo.svg';
import { Box } from '@mui/material';
import './applogo.scss';

const AppBrandLogo = ({ onClick = () => {}, className, logoClassName }) => {
  return (
    <Box className={`app-logo-wrap ${className}`}>
      <Image
        width={100}
        height={62}
        src={AppLogo}
        alt="Microffice"
        className={`app-logo cursor-pointer ${logoClassName}`}
        onClick={onClick}
      />
    </Box>
  );
};

export default AppBrandLogo;
