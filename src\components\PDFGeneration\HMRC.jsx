import { Box, Button, Typography } from '@mui/material';
import React, { useRef } from 'react';
import { useReactToPrint } from 'react-to-print';
import HMRCLogo from '../../../public/images/hmrcPDFLogo.svg';
import './pdfGenerate.scss';
import Image from 'next/image';
import moment from 'moment';
import InsertDriveFileOutlinedIcon from '@mui/icons-material/InsertDriveFileOutlined';

const HMRCFormPDF = ({ userDetails, formDetails }) => {
  const componentRef = useRef();

  const handleDownlodPDF = useReactToPrint({
    content: () => componentRef.current,
    documentTitle: 'HMRC',
  });

  const dateFormat = (date) => {
    return date ? moment(date).format('DDMMYYYY')?.split('') : [];
  };

  const insuranceFormat = (data) => {
    return data ? data?.split('') : [];
  };

  const hmrcPDFForm = () => {
    return (
      <Box ref={componentRef} className="pdf-generate-main-container">
        <Box className="pb32 hmrc-header-container">
          <Box className="hmrc-header-logo">
            <Image src={HMRCLogo} alt="" />
            {/* <HMRCLogo /> */}
          </Box>
          <Box className="text-align-end">
            <Typography variant="h5" className="header-title title-sm fw600">
              Starter checklist
            </Typography>
            <Typography variant="h6" className="p18" style={{ width: '385px' }}>
              Tell your employer of your circumstances so that you do not pay
              too much or too little tax
            </Typography>
          </Box>
        </Box>
        <Box className="hmrc-information-wrapper">
          {/* PAGE 1 */}
          <Box>
            <Typography variant="h6" className="sub-title-text fw600">
              Do not send this form to HM Revenue and Customs (HMRC)
            </Typography>
            <Typography variant="h6" className="p18 pt16 fw600">
              Instructions for employer
            </Typography>
            <Typography variant="h6" className="sub-title-text pt4">
              Use this starter checklist to gather information about your new
              employee if they do not have a P45. You can also use this form if
              they have a student loan (whether or not they have a P45). Use the
              information to help fill in your first Full Payment Submission
              (FPS) for this employee. If you have already submitted your first
              FPS, keep using the tax code in that FPS until HMRC sends you a
              new tax code. If the employee gives you their P45 after the first
              FPS submission, use the tax code shown in parts 2 and 3 of the
              P45. You must keep the information recorded on the starter
              checklist for the current and next 3 tax years. This form is for
              your use only.
            </Typography>
            <Typography variant="h6" className="p18 pt8 fw600">
              Instructions for employee
            </Typography>
            <Typography variant="h6" className="sub-title-text pt4">
              Fill in this form if you do not have a P45 (a document you get
              from your employer when you stop working for them). You should
              also fill in this form if you have a student loan (whether or not
              you’ve a P45). Give the completed form to your employer as soon as
              possible. They need this information to tell HMRC about you and
              help them to use the right tax code. Make sure you answer the
              questions correctly. If you do not, you may pay the wrong amount
              of tax or student loan deductions.
            </Typography>
            <Box className="bold-divider mt8 mb8"></Box>
            {/* Employee personal details */}
            <Typography variant="h6" className="p18 fw600">
              Employee’s personal details
            </Typography>
            <Box className="grid-2container">
              <Box className="pt8 w90">
                <Box className="pb32">
                  <Typography variant="h6" className="sub-title-text fw600 pb8">
                    <span className="square-number">1</span> Last name
                  </Typography>
                  <Box className="custom-input-field border-b ml40">
                    <Typography
                      variant="h6"
                      className="sub-title-text custom-field-value"
                    >
                      {userDetails?.user_last_name}
                    </Typography>
                  </Box>
                </Box>
                <Box className="pb32">
                  <Typography variant="h6" className="sub-title-text fw600 pb4">
                    <span className="square-number">2</span> First names
                  </Typography>
                  <Typography variant="h6" className="sub-title-text pb4 ml40">
                    Do not enter initials or shortened names for example, Jim
                    for James or Liz for Elizabeth
                  </Typography>
                  <Box className="custom-input-field ml40">
                    <Typography
                      variant="h6"
                      className="sub-title-text custom-field-value two-line-ellipsis"
                    >
                      {userDetails?.user_first_name}
                    </Typography>
                  </Box>
                  <Box className="custom-input-field border-b ml40"></Box>
                </Box>
                <Box className="pb32">
                  <Typography variant="h6" className="sub-title-text fw600 pb4">
                    <span className="square-number">3</span> What is your sex?
                  </Typography>
                  <Typography variant="h6" className="sub-title-text pb4 ml40">
                    As shown on your birth certificate or gender recognition
                    certificate
                  </Typography>
                  <Box className="d-flex pt8 ml40">
                    <Box className="d-flex align-items-baseline">
                      <Typography variant="h6" className="sub-title-text">
                        Male
                      </Typography>
                      <Box className="custom-input-field border-b checkbox">
                        <Typography
                          variant="h6"
                          className="sub-title-text custom-field-value"
                        >
                          {userDetails?.user_gender === 'male' ? '✔' : ''}
                        </Typography>
                      </Box>
                    </Box>
                    <Box className="d-flex align-items-baseline">
                      <Typography variant="h6" className="sub-title-text">
                        Female
                      </Typography>
                      <Box className="custom-input-field border-b checkbox">
                        <Typography
                          variant="h6"
                          className="sub-title-text custom-field-value"
                        >
                          {userDetails?.user_gender === 'female' ? '✔' : ''}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                </Box>
                <Box className="pb32">
                  <Typography variant="h6" className="sub-title-text fw600 pb8">
                    <span className="square-number">4</span> Date of birth{' '}
                    <span className="sub-title-text fw400">DD MM YYYY</span>
                  </Typography>
                  <Box className="d-flex number-field-container pl4">
                    <Box className="custom-input-field border-b checkbox ml40">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {dateFormat(userDetails?.date_of_birth)?.[0]}
                      </Typography>
                    </Box>
                    <Box className="custom-input-field border-b checkbox">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {dateFormat(userDetails?.date_of_birth)?.[1]}
                      </Typography>
                    </Box>
                    <Box className="custom-input-field border-b checkbox ml20">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {dateFormat(userDetails?.date_of_birth)?.[2]}
                      </Typography>
                    </Box>
                    <Box className="custom-input-field border-b checkbox">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {dateFormat(userDetails?.date_of_birth)?.[3]}
                      </Typography>
                    </Box>
                    <Box className="custom-input-field border-b checkbox ml20">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {dateFormat(userDetails?.date_of_birth)?.[4]}
                      </Typography>
                    </Box>
                    <Box className="custom-input-field border-b checkbox">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {dateFormat(userDetails?.date_of_birth)?.[5]}
                      </Typography>
                    </Box>
                    <Box className="custom-input-field border-b checkbox">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {dateFormat(userDetails?.date_of_birth)?.[6]}
                      </Typography>
                    </Box>
                    <Box className="custom-input-field border-b checkbox">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {dateFormat(userDetails?.date_of_birth)?.[7]}
                      </Typography>
                    </Box>
                  </Box>
                </Box>
              </Box>
              <Box className="pt8 w90">
                <Box className="pb32">
                  <Typography variant="h6" className="sub-title-text fw600 pb8">
                    <span className="square-number">5</span> Home address
                  </Typography>
                  <Box className="custom-input-field ml40">
                    <Typography
                      variant="h6"
                      className="sub-title-text custom-field-value one-line-ellipsis"
                    >
                      {userDetails?.address_line1}
                    </Typography>
                  </Box>
                  <Box className="custom-input-field ml40">
                    <Typography
                      variant="h6"
                      className="sub-title-text custom-field-value two-line-ellipsis"
                    >
                      {userDetails?.address_line2}
                    </Typography>
                  </Box>
                  <Box className="custom-input-field ml40"></Box>
                  <Box className="custom-input-field ml40">
                    <span className="sub-title-text field-text-placeholder">
                      Postcode
                    </span>
                    <Typography
                      variant="h6"
                      className="sub-title-text custom-field-value two-line-ellipsis"
                      style={{ left: '65px' }}
                    >
                      {userDetails?.pin_code}
                    </Typography>
                  </Box>
                  <Box className="custom-input-field border-b ml40">
                    <span className="sub-title-text field-text-placeholder">
                      Country
                    </span>
                    <Typography
                      variant="h6"
                      className="sub-title-text custom-field-value one-line-ellipsis"
                      style={{ left: '65px' }}
                    >
                      {userDetails?.geo_country?.place_name}
                      {userDetails?.geo_country?.place_name}
                    </Typography>
                  </Box>
                </Box>
                <Box className="pb32">
                  <Typography variant="h6" className="sub-title-text fw600 pb8">
                    <span className="square-number">6</span> National Insurance
                    number{' '}
                    <span className="sub-title-text fw400">(if known)</span>
                  </Typography>
                  <Box className="d-flex number-field-container pl4">
                    <Box className="custom-input-field border-b checkbox ml40">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {insuranceFormat(formDetails?.insurance_number)?.[0]}
                      </Typography>
                    </Box>
                    <Box className="custom-input-field border-b checkbox">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {insuranceFormat(formDetails?.insurance_number)?.[1]}
                      </Typography>
                    </Box>
                    <Box className="custom-input-field border-b checkbox ml20">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {insuranceFormat(formDetails?.insurance_number)?.[2]}
                      </Typography>
                    </Box>
                    <Box className="custom-input-field border-b checkbox">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {insuranceFormat(formDetails?.insurance_number)?.[3]}
                      </Typography>
                    </Box>
                    <Box className="custom-input-field border-b checkbox ml20">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {insuranceFormat(formDetails?.insurance_number)?.[4]}
                      </Typography>
                    </Box>
                    <Box className="custom-input-field border-b checkbox">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {insuranceFormat(formDetails?.insurance_number)?.[5]}
                      </Typography>
                    </Box>
                    <Box className="custom-input-field border-b checkbox ml20">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {insuranceFormat(formDetails?.insurance_number)?.[6]}
                      </Typography>
                    </Box>
                    <Box className="custom-input-field border-b checkbox">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {insuranceFormat(formDetails?.insurance_number)?.[7]}
                      </Typography>
                    </Box>
                    <Box className="custom-input-field border-b checkbox ml20">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {insuranceFormat(formDetails?.insurance_number)?.[8]}
                      </Typography>
                    </Box>
                  </Box>
                </Box>
                <Box className="pb32">
                  <Typography variant="h6" className="sub-title-text fw600 pb8">
                    <span className="square-number">7</span> Employment start
                    date{' '}
                    <span className="sub-title-text fw400">DD MM YYYY</span>
                  </Typography>
                  <Box className="d-flex number-field-container pl4">
                    <Box className="custom-input-field border-b checkbox ml40">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {dateFormat(userDetails?.user_joining_date)?.[0]}
                      </Typography>
                    </Box>
                    <Box className="custom-input-field border-b checkbox">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {dateFormat(userDetails?.user_joining_date)?.[1]}
                      </Typography>
                    </Box>
                    <Box className="custom-input-field border-b checkbox ml20">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {dateFormat(userDetails?.user_joining_date)?.[2]}
                      </Typography>
                    </Box>
                    <Box className="custom-input-field border-b checkbox">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {dateFormat(userDetails?.user_joining_date)?.[3]}
                      </Typography>
                    </Box>
                    <Box className="custom-input-field border-b checkbox ml20">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {dateFormat(userDetails?.user_joining_date)?.[4]}
                      </Typography>
                    </Box>
                    <Box className="custom-input-field border-b checkbox">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {dateFormat(userDetails?.user_joining_date)?.[5]}
                      </Typography>
                    </Box>
                    <Box className="custom-input-field border-b checkbox">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {dateFormat(userDetails?.user_joining_date)?.[6]}
                      </Typography>
                    </Box>
                    <Box className="custom-input-field border-b checkbox">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {dateFormat(userDetails?.user_joining_date)?.[7]}
                      </Typography>
                    </Box>
                  </Box>
                </Box>
              </Box>
            </Box>
            <Box className="footer-wapper" style={{ marginTop: '100px' }}>
              <Typography
                variant="h6"
                className="sub-title-text pt4 text-start"
              >
                Starter checklist
              </Typography>
              <Typography variant="h6" className="sub-title-text pt4">
                Page 1
              </Typography>
              <Box className="text-align-end">
                <Typography variant="h6" className="sub-title-text fw600 pb16">
                  Continue on the next page
                </Typography>
                <Typography variant="h6" className="sub-title-text">
                  HMRC 09/22
                </Typography>
              </Box>
            </Box>
          </Box>
          {/* PAGE 2 */}
          <Box style={{ pageBreakBefore: 'always' }}>
            <Box className="bold-divider mt40 mb8"></Box>
            {/* Employee statement */}
            <Typography variant="h6" className="p18 fw600">
              Employee statement
            </Typography>
            <Typography variant="h6" className="sub-title-text pt4">
              These questions will help you to choose the statement that matches
              your circumstances. The statement you choose helps your employer
              to apply the correct tax code.
            </Typography>
            <Box className="grid-2container">
              <Box className="pt8 w90">
                <Box className="pb32">
                  <Typography variant="h6" className="sub-title-text fw600 pb8">
                    <span className="square-number">8</span> Do you have another
                    job?
                  </Typography>
                  <Box className="d-flex align-items-baseline ml40 pl4">
                    <Typography variant="h6" className="sub-title-text">
                      Yes
                    </Typography>
                    <Box className="custom-input-field border-b checkbox">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {formDetails?.another_job ? '✔' : ''}
                      </Typography>
                    </Box>
                    <Typography variant="h6" className="sub-title-text">
                      Put an ‘X’ in the statement C box below
                    </Typography>
                  </Box>
                  <Box className="d-flex align-items-baseline ml40 pl4">
                    <Typography variant="h6" className="sub-title-text">
                      No
                    </Typography>
                    <Box
                      className="custom-input-field border-b checkbox"
                      style={{ marginLeft: '9px' }}
                    >
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {!formDetails?.another_job ? '✔' : ''}
                      </Typography>
                    </Box>
                    <Typography variant="h6" className="sub-title-text">
                      Go to question 9
                    </Typography>
                  </Box>
                </Box>
                <Box className="">
                  <Box className="d-flex">
                    <Typography
                      variant="h6"
                      className="sub-title-text fw600 pb8"
                    >
                      <span className="square-number">9</span>
                    </Typography>
                    <Typography
                      variant="h6"
                      className="sub-title-text fw600 pb8 pl4"
                    >
                      Do you receive payments from a State, workplace or private
                      pension?
                    </Typography>
                  </Box>
                  <Box className="d-flex align-items-baseline ml40 pl4">
                    <Typography variant="h6" className="sub-title-text">
                      Yes
                    </Typography>
                    <Box className="custom-input-field border-b checkbox">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {formDetails?.private_pension ? '✔' : ''}
                      </Typography>
                    </Box>
                    <Typography variant="h6" className="sub-title-text">
                      Put an ‘X’ in the statement C box below
                    </Typography>
                  </Box>
                  <Box className="d-flex align-items-baseline ml40 pl4">
                    <Typography variant="h6" className="sub-title-text">
                      No
                    </Typography>
                    <Box
                      className="custom-input-field border-b checkbox"
                      style={{ marginLeft: '9px' }}
                    >
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {!formDetails?.private_pension ? '✔' : ''}
                      </Typography>
                    </Box>
                    <Typography variant="h6" className="sub-title-text">
                      Go to question 10
                    </Typography>
                  </Box>
                </Box>
              </Box>
              <Box className="pt8 w90">
                <Box className="">
                  <Typography variant="h6" className="sub-title-text fw600 pb8">
                    <span className="square-number">10</span> Since 6 April have
                    you received payments from:
                  </Typography>
                  <Box className="ml40 pl8 pb8">
                    <Typography variant="h6" className="sub-title-text">
                      • another job which has ended or any of the following
                      taxable benefits <br />
                      • Jobseeker’s Allowance (JSA)
                      <br />
                      • Employment and Support Allowance (ESA)
                      <br />• Incapacity Benefit
                    </Typography>
                  </Box>
                  <Box className="d-flex align-items-baseline ml40 pl8">
                    <Typography variant="h6" className="sub-title-text">
                      Yes
                    </Typography>
                    <Box className="custom-input-field border-b checkbox">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {formDetails?.payment_from ? '✔' : ''}
                      </Typography>
                    </Box>
                    <Typography variant="h6" className="sub-title-text">
                      Put an ‘X’ in the statement B box below
                    </Typography>
                  </Box>
                  <Box className="d-flex align-items-baseline ml40 pl8">
                    <Typography variant="h6" className="sub-title-text">
                      No
                    </Typography>
                    <Box
                      className="custom-input-field border-b checkbox"
                      style={{ marginLeft: '9px' }}
                    >
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {!formDetails?.payment_from ? '✔' : ''}
                      </Typography>
                    </Box>
                    <Typography variant="h6" className="sub-title-text">
                      Put an ‘X’ in the statement A box below
                    </Typography>
                  </Box>
                  <Typography variant="h6" className="sub-title-text pt16">
                    For more information about tax codes, go to
                    www.gov.uk/tax-codes
                  </Typography>
                </Box>
              </Box>
            </Box>
            {/* Statement Details */}
            <Box className="statement-information">
              <Box className="statement-details-table pt8">
                <table>
                  <tbody>
                    <tr>
                      <td>
                        <Box className="d-flex align-center flex-column">
                          <Typography
                            variant="h6"
                            className="sub-title-text fw600 pb4"
                          >
                            Statement A
                          </Typography>
                          <Box className="custom-input-field border-b checkbox">
                            <Typography
                              variant="h6"
                              className="sub-title-text custom-field-value"
                            >
                              {formDetails?.statementA ? '✔' : ''}
                            </Typography>
                          </Box>
                          <Typography
                            variant="h6"
                            className="sub-title-text pt4"
                          >
                            Current personal allowance
                          </Typography>
                        </Box>
                      </td>
                      <td>
                        <Box className="d-flex align-center flex-column">
                          <Typography
                            variant="h6"
                            className="sub-title-text fw600 pb4"
                          >
                            Statement B
                          </Typography>
                          <Box className="custom-input-field border-b checkbox">
                            <Typography
                              variant="h6"
                              className="sub-title-text custom-field-value"
                            >
                              {formDetails?.statementB ? '✔' : ''}
                            </Typography>
                          </Box>
                          <Typography
                            variant="h6"
                            className="sub-title-text pt4"
                            style={{ width: '200px' }}
                          >
                            Current personal allowance on a Week 1/Month 1 basis
                          </Typography>
                        </Box>
                      </td>
                      <td>
                        <Box className="d-flex align-center flex-column">
                          <Typography
                            variant="h6"
                            className="sub-title-text fw600 pb4"
                          >
                            Statement C
                          </Typography>
                          <Box className="custom-input-field border-b checkbox">
                            <Typography
                              variant="h6"
                              className="sub-title-text custom-field-value"
                            >
                              {formDetails?.statementC ? '✔' : ''}
                            </Typography>
                          </Box>
                          <Typography
                            variant="h6"
                            className="sub-title-text pt4"
                          >
                            Tax Code BR
                          </Typography>
                        </Box>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <Box className="">
                          <Typography
                            variant="h6"
                            className="sub-title-text fw600 pb4"
                          >
                            Key
                          </Typography>
                          <Typography variant="h6" className="sub-title-text">
                            This is my first job since 6 April and since the 6
                            April I have not received payments from any of the
                            following:
                          </Typography>
                          <Typography variant="h6" className="sub-title-text">
                            • Jobseeker’s Allowance
                            <br /> • Employment and Support Allowance
                            <br /> • Incapacity Benefit
                          </Typography>
                        </Box>
                      </td>
                      <td>
                        <Box className="">
                          <Typography
                            variant="h6"
                            className="sub-title-text fw600 pb4"
                          >
                            Key
                          </Typography>
                          <Typography variant="h6" className="sub-title-text">
                            Since 6 April I have had another job but I do not
                            have a P45. And/or since the 6 April I have received
                            payments from any of the following:
                          </Typography>
                          <Typography variant="h6" className="sub-title-text">
                            • Jobseeker’s Allowance <br />
                            • Employment and Support Allowance
                            <br />• Incapacity Benefit
                          </Typography>
                        </Box>
                      </td>
                      <td>
                        <Box className="">
                          <Typography
                            variant="h6"
                            className="sub-title-text fw600 pb4"
                          >
                            Key
                          </Typography>
                          <Typography variant="h6" className="sub-title-text">
                            I have another job and/or I am in receipt of a
                            State, workplace or private pension.
                          </Typography>
                        </Box>
                      </td>
                    </tr>
                    <tr>
                      <td colspan="3" className="statement-address">
                        <Box className="">
                          <Typography
                            variant="h6"
                            className="sub-title-text fw600 pb4"
                          >
                            Key
                          </Typography>
                          <Typography variant="h6" className="sub-title-text">
                            Jobseeker’s Allowance (JSA) is an unemployment
                            benefit which can be claimed while looking for work.
                            <br />
                            Employment and Support Allowance (ESA) is a benefit
                            which can be claimed if you have a disability
                            <br />
                            or health condition that affects how much you can
                            work.
                            <br />
                            Incapacity Benefit is help if you could not work
                            because of an illness or disability before 31
                            January 2011.
                            <br />
                            State Pension is a pension paid when you reach State
                            Pension age.
                            <br />
                            Workplace pension is a pension which was arranged by
                            your employer and is being paid to you.
                            <br />
                            Private pension is a pension arranged by you and is
                            being paid to you.
                            <br />
                            Please note that no other Government or HMRC paid
                            benefits need to be considered when completing this
                            form
                          </Typography>
                        </Box>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </Box>
            </Box>
            <Box className="footer-wapper pt32" style={{ marginTop: '180px' }}>
              <Typography variant="h6" className="sub-title-text pt4">
                &nbsp;
              </Typography>
              <Typography variant="h6" className="sub-title-text pt4">
                Page 2
              </Typography>
              <Box className="text-align-end">
                <Typography variant="h6" className="sub-title-text fw600 pb16">
                  Continue on the next page
                </Typography>
                <Typography
                  variant="h6"
                  className="sub-title-text"
                ></Typography>
              </Box>
            </Box>
          </Box>
          {/* PAGE 3 */}
          <Box style={{ pageBreakBefore: 'always' }} className="pt16">
            <Box
              className="bold-divider mb8"
              style={{ marginTop: '15px' }}
            ></Box>
            {/* Staudent loans */}
            <Typography variant="h6" className="p18 fw600">
              Student loans
            </Typography>
            <Box className="grid-2container">
              <Box className="pt8 w90">
                <Box className="pb8">
                  <Box className="d-flex">
                    <Typography
                      variant="h6"
                      className="sub-title-text fw600 pb8"
                    >
                      <span className="square-number">11</span>
                    </Typography>
                    <Typography
                      variant="h6"
                      className="sub-title-text fw600 pl4"
                    >
                      Do you have a student or postgraduate loan?
                    </Typography>
                  </Box>
                  <Box className="d-flex align-items-baseline ml40 pl8">
                    <Typography variant="h6" className="sub-title-text">
                      Yes
                    </Typography>
                    <Box className="custom-input-field border-b checkbox">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {formDetails?.postgraduate_loan ? '✔' : ''}
                      </Typography>
                    </Box>
                    <Typography variant="h6" className="sub-title-text">
                      Go to question 12
                    </Typography>
                  </Box>
                  <Box className="d-flex align-items-baseline ml40 pl8">
                    <Typography variant="h6" className="sub-title-text">
                      No
                    </Typography>
                    <Box
                      className="custom-input-field border-b checkbox"
                      style={{ marginLeft: '9px' }}
                    >
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {!formDetails?.postgraduate_loan ? '✔' : ''}
                      </Typography>
                    </Box>
                    <Typography variant="h6" className="sub-title-text">
                      Go straight to the Declaration
                    </Typography>
                  </Box>
                </Box>
                <Box className="pb8">
                  <Typography variant="h6" className="sub-title-text fw600">
                    <span className="square-number">12</span> Do any of the
                    following statements apply:
                  </Typography>
                  <Typography
                    variant="h6"
                    className="sub-title-text ml40 pl8 pb4"
                  >
                    • you’re still studying on a course that your student loan
                    relates to <br />
                    • you completed or left your course after the start of the
                    current tax year, which started on 6 April <br />
                    • you’ve already repaid your loan in full <br />• you’re
                    paying the Student Loans Company by Direct Debit from your
                    bank to manage your end of loan repayments
                  </Typography>
                  <Box className="d-flex align-items-baseline ml40 pl8">
                    <Typography variant="h6" className="sub-title-text">
                      Yes
                    </Typography>
                    <Box className="custom-input-field border-b checkbox">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {formDetails?.statement_apply ? '✔' : ''}
                      </Typography>
                    </Box>
                    <Typography variant="h6" className="sub-title-text">
                      Go straight to the Declaration
                    </Typography>
                  </Box>
                  <Box className="d-flex align-items-baseline ml40 pl8">
                    <Typography variant="h6" className="sub-title-text">
                      No
                    </Typography>
                    <Box
                      className="custom-input-field border-b checkbox"
                      style={{ marginLeft: '9px' }}
                    >
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {!formDetails?.statement_apply ? '✔' : ''}
                      </Typography>
                    </Box>
                    <Typography variant="h6" className="sub-title-text">
                      Go to question 13
                    </Typography>
                  </Box>
                </Box>
                <Box className="">
                  <Box className="d-flex">
                    <Typography variant="h6" className="sub-title-text fw600">
                      <span className="square-number">13</span>
                    </Typography>
                    <Typography
                      variant="h6"
                      className="sub-title-text fw600 pl4"
                    >
                      To avoid repaying more than you need to, tick the correct
                      student loan or loans that you have – use the guidance on
                      the right to help you.
                    </Typography>
                  </Box>
                  <Typography
                    variant="h6"
                    className="sub-title-text ml40 pl8 pb4"
                  >
                    Please tick all that apply
                  </Typography>
                  <Box className="ml40 pl8">
                    <Box className="d-flex align-items-baseline justify-space-between ">
                      <Typography variant="h6" className="sub-title-text">
                        Plan 1
                      </Typography>
                      <Box className="custom-input-field border-b checkbox m0">
                        <Typography
                          variant="h6"
                          className="sub-title-text custom-field-value"
                        >
                          {formDetails?.load_guidance?.includes('1')
                            ? '✔'
                            : ''}
                        </Typography>
                      </Box>
                    </Box>
                    <Box className="d-flex align-items-baseline justify-space-between">
                      <Typography variant="h6" className="sub-title-text">
                        Plan 2
                      </Typography>
                      <Box className="custom-input-field border-b checkbox m0">
                        <Typography
                          variant="h6"
                          className="sub-title-text custom-field-value"
                        >
                          {formDetails?.load_guidance?.includes('2')
                            ? '✔'
                            : ''}
                        </Typography>
                      </Box>
                    </Box>
                    <Box className="d-flex align-items-baseline justify-space-between">
                      <Typography variant="h6" className="sub-title-text">
                        Plan 4
                      </Typography>
                      <Box className="custom-input-field border-b checkbox m0">
                        <Typography
                          variant="h6"
                          className="sub-title-text custom-field-value"
                        >
                          {formDetails?.load_guidance?.includes('3')
                            ? '✔'
                            : ''}
                        </Typography>
                      </Box>
                    </Box>
                    <Box className="d-flex align-items-baseline justify-space-between">
                      <Typography variant="h6" className="sub-title-text">
                        Postgraduate loan (England and Wales only)
                      </Typography>
                      <Box className="custom-input-field border-b checkbox m0">
                        <Typography
                          variant="h6"
                          className="sub-title-text custom-field-value"
                        >
                          {formDetails?.load_guidance?.includes('4')
                            ? '✔'
                            : ''}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                </Box>
              </Box>
              <Box className="pt8 w90">
                <Box className="">
                  <Box className="pl8">
                    <Typography variant="h6" className="sub-title-text pb8">
                      Employees, for more information about the type of loan you
                      have or to check your balance, go to
                      www.gov.uk/sign-in-to-manage-your-student-loan-balance
                    </Typography>
                    <Typography variant="h6" className="sub-title-text pb8">
                      Employers, for guidance on student loans and which plan or
                      loan type to use if your employee has selected more than
                      one, go to
                      www.gov.uk/guidance/special-rules-for-student-loans
                    </Typography>
                  </Box>
                  <Box className="pl8">
                    <table>
                      <tbody>
                        <tr>
                          <td>
                            <Box className="pl4">
                              <Typography
                                variant="h6"
                                className="sub-title-text fw600 pb4"
                              >
                                You have Plan 1 if any of the following apply:
                              </Typography>
                              <Typography
                                variant="h6"
                                className="sub-title-text"
                              >
                                • you lived in Northern Ireland when you started
                                your course
                                <br />• you lived in England or Wales and
                                started your course before 1 September 2012
                              </Typography>
                            </Box>
                          </td>
                        </tr>
                        <tr>
                          <td>
                            <Box className="pl4">
                              <Typography
                                variant="h6"
                                className="sub-title-text fw600 pb4"
                              >
                                You have Plan 2 if:
                              </Typography>
                              <Typography
                                variant="h6"
                                className="sub-title-text"
                              >
                                You lived in England or Wales and started your
                                course on or after 1 September 2012.
                              </Typography>
                            </Box>
                          </td>
                        </tr>
                        <tr>
                          <td>
                            <Box className="pl4">
                              <Typography
                                variant="h6"
                                className="sub-title-text fw600 pb4"
                              >
                                You have Plan 4 if:
                              </Typography>
                              <Typography
                                variant="h6"
                                className="sub-title-text"
                              >
                                You lived in Scotland and applied through the
                                Students Award Agency Scotland (SAAS) when you
                                started your course.
                              </Typography>
                            </Box>
                          </td>
                        </tr>
                        <tr>
                          <td>
                            <Box className="pl4">
                              <Typography
                                variant="h6"
                                className="sub-title-text fw600 pb4"
                              >
                                You have a postgraduate loan if any of the
                                following apply:
                              </Typography>
                              <Typography
                                variant="h6"
                                className="sub-title-text"
                              >
                                • you lived in England and started your
                                postgraduate master’s course on or after 1
                                August 2016
                                <br />
                                • you lived in Wales and started your
                                postgraduate master’s course on or after 1
                                August 2017
                                <br />• you lived in England or Wales and
                                started your postgraduate doctoral course on or
                                after 1 August 2018
                              </Typography>
                            </Box>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </Box>
                </Box>
              </Box>
            </Box>
            <Box className="bold-divider mt8 mb8"></Box>
            {/* Declaration */}
            <Typography variant="h6" className="p18 fw600">
              Declaration
            </Typography>
            <Typography variant="h6" className="sub-title-text pt4">
              I confirm that the information I’ve given on this form is correct.
            </Typography>
            <Box className="grid-2container">
              <Box className="pt8 w90">
                <Box className="pb8">
                  <Typography variant="h6" className="sub-title-text fw600 pb8">
                    Full name{' '}
                    <span className="sub-title-text fw400">
                      Use capital letters
                    </span>
                  </Typography>
                  <Box className="custom-input-field border-b">
                    <Typography
                      variant="h6"
                      className="sub-title-text custom-field-value"
                    >
                      {userDetails?.user_first_name +
                        ' ' +
                        userDetails?.user_middle_name +
                        ' ' +
                        userDetails?.user_last_name}
                    </Typography>
                  </Box>
                </Box>
                <Box className="pb8">
                  <Typography variant="h6" className="sub-title-text fw600 pb8">
                    Date{' '}
                    <span className="sub-title-text fw400">DD MM YYYY</span>
                  </Typography>
                  <Box className="d-flex number-field-container">
                    <Box className="custom-input-field border-b checkbox">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {
                          dateFormat(
                            userDetails?.updatedAt
                              ? userDetails?.updatedAt
                              : userDetails?.createdAt
                          )?.[0]
                        }
                      </Typography>
                    </Box>
                    <Box className="custom-input-field border-b checkbox">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {
                          dateFormat(
                            userDetails?.updatedAt
                              ? userDetails?.updatedAt
                              : userDetails?.createdAt
                          )?.[1]
                        }
                      </Typography>
                    </Box>
                    <Box className="custom-input-field border-b checkbox ml20">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {
                          dateFormat(
                            userDetails?.updatedAt
                              ? userDetails?.updatedAt
                              : userDetails?.createdAt
                          )?.[2]
                        }
                      </Typography>
                    </Box>
                    <Box className="custom-input-field border-b checkbox">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {
                          dateFormat(
                            userDetails?.updatedAt
                              ? userDetails?.updatedAt
                              : userDetails?.createdAt
                          )?.[3]
                        }
                      </Typography>
                    </Box>
                    <Box className="custom-input-field border-b checkbox ml20">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {
                          dateFormat(
                            userDetails?.updatedAt
                              ? userDetails?.updatedAt
                              : userDetails?.createdAt
                          )?.[4]
                        }
                      </Typography>
                    </Box>
                    <Box className="custom-input-field border-b checkbox">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {
                          dateFormat(
                            userDetails?.updatedAt
                              ? userDetails?.updatedAt
                              : userDetails?.createdAt
                          )?.[5]
                        }
                      </Typography>
                    </Box>
                    <Box className="custom-input-field border-b checkbox">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {
                          dateFormat(
                            userDetails?.updatedAt
                              ? userDetails?.updatedAt
                              : userDetails?.createdAt
                          )?.[6]
                        }
                      </Typography>
                    </Box>
                    <Box className="custom-input-field border-b checkbox">
                      <Typography
                        variant="h6"
                        className="sub-title-text custom-field-value"
                      >
                        {
                          dateFormat(
                            userDetails?.updatedAt
                              ? userDetails?.updatedAt
                              : userDetails?.createdAt
                          )?.[7]
                        }
                      </Typography>
                    </Box>
                  </Box>
                </Box>
              </Box>
              <Box className="pt8 w90">
                <Box className="pb8">
                  <Typography variant="h6" className="sub-title-text fw600 pb8">
                    Signature
                  </Typography>
                  <Box
                    className="custom-input-field border-b signature-field"
                    style={{ height: '60px' }}
                  >
                    {' '}
                    {userDetails?.user_signature && (
                      <img
                        src={userDetails?.user_signature}
                        alt="not found"
                        className="signature"
                      />
                    )}
                  </Box>
                </Box>
              </Box>
            </Box>
            {/* Give this form to your employer */}
            <Box className="bold-divider mb8"></Box>
            <Typography variant="h6" className="p18 fw600">
              Give this form to your employer
            </Typography>
            <Typography variant="h6" className="sub-title-text pt4">
              Your employer will use the information to make sure you pay the
              right amount of tax.
              <br />
              Do not send this form to HMRC.
            </Typography>

            {/* Employer guidance */}
            <Box className="bold-divider mt8 mb8"></Box>
            <Typography variant="h6" className="p18 fw600">
              Employer guidance
            </Typography>
            <Typography variant="h6" className="sub-title-text pt4">
              For information on how to work out your new employee’s tax code,
              go to www.gov.uk/new-employee-tax-code
              <br />
              Use Statement A, B or C that your employee has chosen in the
              employee statement section and apply the tax code below:
              <br />
              • Statement A – use the current personal allowance
              <br />
              • Statement B – use the current personal allowance on a ‘week
              1/month 1’ basis
              <br />• Statement C – use tax code BR
            </Typography>

            <Box className="hmrc-contract-footer">
              <Typography
                variant="h6"
                className="sub-title-text pt4 arial-font"
              >
                Page 3
              </Typography>
            </Box>
          </Box>
        </Box>
      </Box>
    );
  };

  return (
    <div>
      <div style={{ display: 'none' }}>{hmrcPDFForm()}</div>
      <Button
        onClick={() => handleDownlodPDF()}
        className="pdf-download-btn"
        disableRipple
      >
        <span className="title-text onboarding-form-name">HMRC FORM : </span>
        <InsertDriveFileOutlinedIcon />
        <span className="title-text pl4 text-underline">View PDF</span>
      </Button>
    </div>
  );
};

export default HMRCFormPDF;
