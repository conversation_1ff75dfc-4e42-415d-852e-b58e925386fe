'use client';
import React, { useState } from 'react';
import {
  Box,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import { getFields } from '@/helper/common/commonFunctions';
import moment from 'moment';
import UserAvatar from '@/components/UI/Avatar/UserAvatar';

export default function Notification({ notificationList }) {
  const [expanded, setExpanded] = useState(false);

  const handleChange = (panel) => {
    expanded === panel ? setExpanded() : setExpanded(panel);
  };
  return (
    <Box className="staff-notification-sec">
      {notificationList &&
        notificationList?.length > 0 &&
        notificationList?.map((item, index) => {
          const users = getFields(item?.to_user_name, 'user_full_name');
          const branches = getFields(item?.to_branch_name, 'branch_name');
          const department = getFields(
            item?.to_department_name,
            'department_name'
          );
          const roles = getFields(item?.to_role_name, 'role_name');
          return (
            <Accordion
              key={index}
              elevation={0}
              className={`staff-notification-details ${
                item?.notification_status === 'read'
                  ? ''
                  : 'cursor-pointer unread-notification'
              }`}
              expanded={expanded === index}
              onChange={() => handleChange(index)}
            >
              <AccordionSummary
                // expandIcon={<KeyboardArrowDownIcon />}
                className="notification-accordion"
              >
                <Box className="notification-card">
                  <Box className="notification-card-content gap-sm">
                    <Box className="d-flex">
                      <UserAvatar
                        name={item?.notification_subject}
                        src={item?.notification_image_url}
                        classname="notification-list-user-icon"
                      />
                      <Box className="pl16">
                        <Typography className="body-text fw600 pb4">
                          {item?.notification_subject}
                        </Typography>
                        <Typography className="title-text">
                          {item?.notification_content}
                        </Typography>
                      </Box>
                    </Box>
                    <Box>
                      <Typography className="sub-title-text notification-date bg-date-transparent">
                        <span>
                          {item?.createdAt
                            ? moment(item?.createdAt)?.format(
                                'DD, MMM YYYY hh:mm A'
                              )
                            : ''}
                        </span>
                      </Typography>
                    </Box>
                  </Box>
                </Box>
              </AccordionSummary>
              <AccordionDetails className="">
                <Box className="accordion-details">
                  {item?.from_user?.user_full_name && (
                    <>
                      <Typography className="title-text pb4">
                        <span className="fw600 text-underline">Sent By</span>
                      </Typography>
                      <Typography className="title-text pb4">
                        <span>{item?.from_user?.user_full_name}</span>
                      </Typography>
                    </>
                  )}
                  <Typography className="title-text fw600 pb4 pt4 text-underline">
                    Sent To
                  </Typography>
                  {((users && users?.length > 0) ||
                    item?.notification_type === 'all') && (
                    <Box className="d-flex align-center">
                      <Typography className="title-text fw400 user-date">
                        <span className="fw600">User : </span>
                        <span>
                          {item?.notification_type === 'all'
                            ? 'All'
                            : users.join(', ')}
                        </span>
                      </Typography>
                    </Box>
                  )}
                  {branches && branches?.length > 0 && (
                    <Box className="d-flex align-center">
                      <Typography className="title-text fw400 user-date">
                        <span className="fw600">Branch : </span>
                        <span>{branches.join(', ')}</span>
                      </Typography>
                    </Box>
                  )}
                  {department && department?.length > 0 && (
                    <Box className="d-flex align-center">
                      <Typography className="title-text fw400 user-date">
                        <span className="fw600">Department : </span>
                        <span>
                          <span>{department.join(', ')}</span>
                        </span>
                      </Typography>
                    </Box>
                  )}
                  {roles && roles?.length > 0 && (
                    <Box className="d-flex align-center">
                      <Typography className="title-text fw400 user-date">
                        <span className="fw600">System Access : </span>
                        <span>
                          <span>{roles.join(', ')}</span>
                        </span>
                      </Typography>
                    </Box>
                  )}
                </Box>
              </AccordionDetails>
            </Accordion>
          );
        })}
    </Box>
  );
}
