import React from 'react';
import { Box, Typography } from '@mui/material';
import CustomSelect from '@/components/UI/CustomSelect';
import CustomTextField from '@/components/UI/CustomTextField';

const CustomDropdown = ({
  type,
  dayOptions,
  monthOptions,
  accrualTime,
  setAccrualTime,
  resetTime,
  setResetTime,
  months,
  index,
  onChange,
  quarter,
  leaveType,
}) => {
  return (
    <Box className=" d-flex align-center gap-sm flex-wrap">
      {/* Accrual Section */}
      {type === 'accrual' && (
        <Box className="d-flex align-center gap-10 flex-wrap">
          <Typography
            className={`title-text ${index === 0 ? 'on-the-text' : 'and-the-text'}`}
          >
            {index === 0 ? 'On the' : 'And the'}
          </Typography>
          <Box>
            <CustomSelect
              className="leave-select-wrap"
              placeholder="Days"
              name={`accrualDays_${index}`}
              options={dayOptions}
              value={
                dayOptions?.find((opt) => {
                  return opt?.value === months?.on_date;
                }) || ''
              }
              onChange={(e) => {
                const resetTimes = accrualTime;
                resetTimes.reset_value[index].on_date = e?.value;
                setAccrualTime(resetTimes);
                onChange();
              }}
              isClearable={false}
            />
          </Box>
          <Box>
            <CustomSelect
              className="leave-select-wrap"
              placeholder="Month"
              name={`accrualMonth_${index}`}
              value={
                monthOptions?.find((opt) => {
                  return opt?.value === months?.month;
                }) || ''
              }
              onChange={(e) => {
                const resetTimes = accrualTime;
                resetTimes.reset_value[index].month = e?.value;
                setAccrualTime(resetTimes);
                onChange();
              }}
              isDisabled={!quarter}
              options={monthOptions}
              isClearable={false}
            />
          </Box>
        </Box>
      )}

      {type === 'accrual' && (
        <Box className="d-flex align-center gap-sm">
          <Typography className="title-text">
            {' '}
            {leaveType?.leave_period_type === 'hour'
              ? 'No. of Hours'
              : 'No. of Days'}
          </Typography>
          <Box>
            <CustomTextField
              fullWidth
              id={`accrualNoOfDays_${index}`}
              name={`accrualNoOfDays_${index}`}
              variant="outlined"
              placeholder="Days"
              value={months?.days}
              onChange={(e) => {
                const resetTimes = accrualTime;
                resetTimes.reset_value[index].days = e?.target?.value;
                setAccrualTime(resetTimes);
                onChange();
              }}
            />
          </Box>
        </Box>
      )}

      {/* Reset Section */}
      {type === 'reset' && (
        <Box className="d-flex align-center gap-10 flex-wrap">
          <Typography className="title-text">On the</Typography>
          <Box>
            <CustomSelect
              className="leave-select-wrap"
              placeholder="Last Day"
              name={`resetDays ${index}`}
              value={
                dayOptions?.find((opt) => {
                  return opt?.value === months?.on_date;
                }) || ''
              }
              onChange={(e) => {
                const resetTimes = resetTime;
                resetTimes.reset_value[index].on_date = e?.value;
                setResetTime(resetTimes);
                onChange();
              }}
              options={dayOptions}
              isClearable={false}
            />
          </Box>
          <Box>
            <CustomSelect
              className="leave-select-wrap"
              placeholder="Month"
              name={`resetMonths ${index}`}
              value={
                monthOptions?.find((opt) => {
                  return opt?.value === months?.month;
                }) || ''
              }
              onChange={(e) => {
                const resetTimes = resetTime;
                resetTimes.reset_value[index].month = e?.value;
                setResetTime(resetTimes);
                onChange();
              }}
              isDisabled={!quarter}
              options={monthOptions}
              isClearable={false}
            />
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default CustomDropdown;
