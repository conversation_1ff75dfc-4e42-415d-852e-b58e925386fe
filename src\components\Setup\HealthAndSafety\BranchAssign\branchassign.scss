.assign-branch-section {
  // height: calc(100% - var(--spacing-lg));
  height: 100%;
  overflow: auto;
  .divider {
    border-top: var(--normal-sec-border);
    margin: 0 0 var(--spacing-lg) 0;
  }

  .filters-wrap {
    padding-bottom: var(--spacing-lg);
    gap: var(--spacing-md);
    flex-wrap: wrap;
    .search-wrap {
      .MuiInputBase-root {
        min-height: 30px;
        border-radius: var(--border-radius-xs);
        .MuiInputBase-input {
          padding: var(--spacing-xxs) var(--spacing-lg) !important;
          &::placeholder {
            font-size: var(--font-size-sm);
          }
        }
      }
    }

    .staff-leave-search {
      width: 100% !important;
      max-width: 230px !important;
      .MuiInputBase-root {
        min-height: 30px;
        border-radius: var(--border-radius-xs) !important;
        padding-left: 0px !important;
        .MuiSvgIcon-root {
          height: var(--icon-size-sm);
          width: var(--icon-size-sm);
        }
        .MuiInputBase-input {
          padding: var(--spacing-xxs) var(--spacing-md) !important;
          &::placeholder {
            font-size: var(--font-size-sm);
          }
        }
        .MuiOutlinedInput-notchedOutline {
          border-color: none !important;
          border-color: var(--border-color-light-gray) !important;
        }
      }
      @media (max-width: 575px) {
        max-width: 100% !important;
      }
    }
    .apply-clear-btns-wrap {
      gap: var(--spacing-md);
      .filter-apply-btn {
        min-width: 40px !important;
        padding: 3.5px 0px !important;
        &:hover {
          .MuiSvgIcon-root {
            fill: var(--color-primary) !important;
          }
        }
      }
      .filter-clear-btn {
        padding: 3.5px 0px !important;
        border: var(--border-width-xs) var(--border-style-solid)
          var(--color-primary);
        min-width: 40px !important;
        .MuiSvgIcon-root {
          fill: var(--color-primary) !important;
        }
        &:hover {
          .MuiSvgIcon-root {
            fill: var(--color-white) !important;
          }
        }
      }
      @media (max-width: 575px) {
        max-width: 100% !important;
      }
    }
    .select-small-box {
      .MuiSelect-select {
        padding: var(--spacing-xxs) 30px var(--spacing-xs) var(--spacing-sm) !important;
      }
      fieldset {
        border-radius: var(--border-radius-xs) !important;
        margin-top: var(--spacing-xs) !important;
      }
    }
    .___SBoxInline_ves2u_gg_ {
      .___SFilterTrigger_1bhpg_gg_ {
        height: 30px;
        border-radius: var(--border-radius-xs);
      }
    }
  }
  .assign-emp {
    height: calc(100% - 138px);
    overflow: auto;
    // height: calc(
    //   100% - 46px - var(--spacing-lg) - var(--spacing-lg) - var(--spacing-lg) -
    //     50px - 40px
    // );
    // overflow: scroll;
    // max-height: 60vh;

    @media (max-width: 575px) {
      height: calc(100% - 260px) !important;
    }
    .Assign-emp-grid {
      display: grid;
      grid-template-columns: calc(50% - 4px) calc(50% - 4px);
      gap: 0px;
      height: 100%;
      .left-grid {
        border-right: var(--normal-sec-border);
      }
      .branch-name-sec {
        padding-top: var(--spacing-xsm);
        .branch-name-color {
          width: 5px;
          height: 18px;
          border-top-right-radius: var(--border-radius-xxl);
          border-bottom-right-radius: var(--border-radius-xxl);
        }
      }

      @media (max-width: 768px) {
        grid-template-columns: calc(100%);
        gap: var(--spacing-lg);
        .left-grid {
          border-right: 0;
        }
      }
    }
  }

  .assign-role-title {
    border-bottom: var(--normal-sec-border);
    border-top: var(--normal-sec-border);
    padding: var(--spacing-sm) 0;
  }
  .assign-user-details {
    margin-top: var(--spacing-sm);
  }
  .small-buttons {
    padding-top: var(--spacing-sm);
    border-top: var(--normal-sec-border);
  }
  .intergalactic-multiselect {
    [class*='neighborLocation_right'],
    [class*='SBaseTrigger']:not([class*='neighborLocation_left']) {
      min-width: 235px;
      max-width: 235px;
      @media (max-width: 575px) {
        max-width: 100% !important;
        min-width: 100% !important;
      }
    }
    @media (max-width: 575px) {
      max-width: 100% !important;
      min-width: 100% !important;
      .multiple-branch-wrap {
        display: flex;
        justify-content: flex-start;
        gap: var(--spacing-sm);
      }
    }
  }
  .disabled-user {
    opacity: 0.6;
    cursor: default !important;
    pointer-events: none !important;
  }
  .warning-icon {
    fill: var(--color-warning) !important;
    margin-right: var(--spacing-sm);
    align-self: flex-start;
    width: var(--icon-size-sm);
    height: var(--icon-size-sm);
  }
  .user-warn {
    width: var(--icon-size-xs);
    height: var(--icon-size-xs);
    align-self: center;
  }
  .warning-details {
    padding: var(--spacing-xs) var(--spacing-base);
    display: flex;
    align-items: center;
    background-color: var(--color-warning-background);
  }
}
