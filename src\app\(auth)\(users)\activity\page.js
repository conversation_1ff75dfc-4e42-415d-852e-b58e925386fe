import React from 'react';
import { Box } from '@mui/material';
import { generateMetadata } from '@/helper/common/commonFunctions';
import ActivityLogs from '@/components/ActivityLogs';

export const metadata = generateMetadata({
  pageTitle: 'Activity Logs',
});

const ActivityLogsPage = ({ params }) => {
  return (
    <Box className="main-page-container">
      <ActivityLogs params={params} />
    </Box>
  );
};

export default ActivityLogsPage;
