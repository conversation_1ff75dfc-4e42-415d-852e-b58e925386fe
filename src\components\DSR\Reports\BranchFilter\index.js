'use client';
import React, { useState, useMemo } from 'react';
import { Box } from 'intergalactic/flex-box';
import Select from 'intergalactic/select';
import { FilterTrigger } from 'intergalactic/base-trigger';
import Ellipsis from 'intergalactic/ellipsis';
// import ExpandMoreOutlinedIcon from '@mui/icons-material/ExpandMoreOutlined';
// import ClearIcon from '@mui/icons-material/Clear';
import { Tooltip, Typography } from '@mui/material';
import { DropdownCloseIcon, DropdownIcon } from '@/helper/common/images';
import '../Generals/generalpage.scss';

export default function BranchFilter({
  selectedBranches,
  setSelectedBranches,
  branchList,
  disabled,
}) {
  const [visible, setVisible] = useState(false);

  // Create branch data once instead of inside useMemo for performance
  const branchData = branchList.map((feature) => ({
    label: feature.label,
    value: feature.value,
    color: feature.color,
  }));

  // Filtered options memoized to avoid unnecessary recalculations
  const filteredOptions = useMemo(() => {
    return branchData;
  }, [branchData]);

  // Handle visibility and loading
  const handleChangeVisible = (visible) => {
    setVisible(visible);
  };

  // Select/Deselect individual branch
  const handleSelectBranch = (branchId) => {
    setSelectedBranches((prev) =>
      prev.includes(branchId)
        ? prev.filter((id) => id !== branchId)
        : [...prev, branchId]
    );
  };

  // Select/Deselect all branches
  const handleSelectAll = () => {
    setSelectedBranches(filteredOptions?.map((branch) => branch?.value));
  };

  const handleDeselectAll = () => {
    setSelectedBranches([]);
  };

  const isAllSelected = selectedBranches?.length === filteredOptions?.length;

  const handleClearSelection = () => {
    setSelectedBranches([]);
  };
  const truncateText = (text, maxLength) =>
    text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;
  return (
    <>
      <Select
        placeholder="Branches"
        multiselect
        value={selectedBranches}
        onVisibleChange={disabled ? '' : handleChangeVisible}
        visible={visible}
        onChange={setSelectedBranches}
        disabled={disabled}
      >
        <Select.Trigger
          className="intergalactic-multiselect"
          tag={FilterTrigger}
        >
          {selectedBranches?.length === 1 ? (
            <Box className="d-flex justify-space-between gap-sm">
              <Box className="d-flex align-center gap-5">
                <span className="branches-wrap">Branch :</span>
                <Tooltip
                  title={
                    branchData.find(
                      (branch) => branch?.value === selectedBranches[0]
                    )?.label || ''
                  }
                  arrow
                >
                  <span className="label-wrap">
                    {truncateText(
                      branchData.find(
                        (branch) => branch?.value === selectedBranches[0]
                      )?.label || '',
                      14
                    )}
                  </span>
                </Tooltip>
              </Box>
              <Box className="d-flex align-center gap-5">
                {!disabled && (
                  <DropdownCloseIcon
                    onClick={handleClearSelection}
                    className="clear-icon-wrap"
                  />
                )}
                <DropdownIcon className="down-arrow-wrap" />
              </Box>
            </Box>
          ) : selectedBranches?.length > 1 ? (
            <Box className="multiple-branch-wrap">
              <span className="branches-wrap">Branches</span>
              <span className="selected-count">
                {selectedBranches?.length}
              </span>{' '}
              <span className="selected-wrap">selected</span>
              {!disabled && (
                <DropdownCloseIcon
                  onClick={handleClearSelection}
                  className="clear-icon-wrap"
                />
              )}
              <DropdownIcon className="down-arrow-wrap" />
            </Box>
          ) : (
            'Select Branches'
          )}
        </Select.Trigger>
        <Select.Popper
          className="select-input-wrap"
          aria-label="Options with search"
        >
          <>
            <Box>
              <Typography
                className="select-all-wrap"
                onClick={
                  !disabled
                    ? isAllSelected
                      ? handleDeselectAll
                      : handleSelectAll
                    : undefined
                }
              >
                {isAllSelected ? 'Deselect All' : 'Select All'}
              </Typography>
            </Box>
            <Select.List id="search-list">
              {filteredOptions?.map((option, index) => (
                <Select.Option
                  className="option-wrap"
                  key={option?.value}
                  value={option?.value}
                  id={`option-${index}`}
                  onClick={
                    !disabled
                      ? () => handleSelectBranch(option?.value)
                      : undefined
                  }
                  aria-selected={selectedBranches?.includes(option?.value)}
                >
                  <Select.Option.Checkbox
                    className="checkbox-wrap"
                    checked={selectedBranches?.includes(option?.value)}
                  />
                  <Ellipsis placement="right">
                    <Ellipsis.Content>
                      <span style={{ display: 'flex', alignItems: 'center' }}>
                        <span
                          style={{
                            width: '10px',
                            height: '10px',
                            borderRadius: '50%',
                            backgroundColor: option?.color,
                            marginRight: '8px',
                          }}
                        />
                        {option.label}
                      </span>
                    </Ellipsis.Content>
                  </Ellipsis>
                </Select.Option>
              ))}
              {!filteredOptions?.length && (
                <Select.OptionHint>No branches found</Select.OptionHint>
              )}
            </Select.List>
            {/* <Box m="8px">
                <Button
                  className="apply-btn"
                  use="primary"
                  w="100%"
                  onClick={() => setVisible(false)}>
                  Apply
                </Button>
              </Box> */}
          </>
        </Select.Popper>
      </Select>
    </>
  );
}
