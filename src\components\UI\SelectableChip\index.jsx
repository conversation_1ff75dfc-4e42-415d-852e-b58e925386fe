import React from 'react';
import { Chip } from '@mui/material';

const SelectableChip = ({
  label,
  selected,
  onClick,
  className = '',
  selectedClassName = 'chip-selected',
}) => {
  return (
    <Chip
      label={label}
      clickable
      variant="outlined"
      className={`custom-chip ${className} ${selected ? selectedClassName : ''}`}
      onClick={onClick}
    />
  );
};

export default SelectableChip;
