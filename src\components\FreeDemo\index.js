import React, { useState } from 'react';
import * as Yup from 'yup';
import Dialog from '@mui/material/Dialog';
import Image from 'next/image';
import RightIcon from '../../../public/images/right_icon.svg';
import BackgroundImage from '../../../public/images/free_demo_background.png';
import {
  Box,
  IconButton,
  InputAdornment,
  List,
  ListItem,
  TextField,
  Typography,
  DialogContent,
} from '@mui/material';
import {
  Buildingicon,
  EmailIcon,
  FreeDemoCloseIcon,
  // FreeDemoLeftBackSvg,
  Industry,
  MessageIcon,
  PeopleIcon,
  PhoneIcon,
  ProfileRoundIcon,
} from '../../helper/common/images';
import { Form, Formik } from 'formik';
import CustomButton from '../UI/button';
import PhoneNumberWithCountryCode from '../UI/CustomCountryCodeField';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { ORG_URLS } from '@/helper/constants/urls';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import './freedemo.scss';

let ListData = [
  {
    id: 1,
    title: 'Centralized Employee Records',
  },
  {
    id: 2,
    title: 'Seamless Onboarding & Document Management',
  },
  {
    id: 3,
    title: 'Leave, Attendance & Shift Management',
  },
  {
    id: 4,
    title: 'Role-Based Access Controls',
  },
  {
    id: 5,
    title: 'Powerful Reports',
  },
];

export default function FreeDemo({ open, handleClose }) {
  const [selectedCountryId, setSelectedCountryId] = useState(null);
  // const [loader, setLoader] = useState(false);
  return (
    <>
      <Dialog open={open} onClose={handleClose} className="free-demo-dialog">
        <Box className="free-demo-dialog-inner-wrap">
          <Box className="free-demo-close" onClick={handleClose}>
            <FreeDemoCloseIcon className="cursor-pointer" />
          </Box>
          <Box className="free-demo-dialog-inner">
            <DialogContent className={`dialog-content`}>
              <Box className="free-demo-wrap d-flex">
                <Box
                  className="free-demo-left-content-wrap"
                  style={{
                    backgroundImage: `url(${BackgroundImage.src})`,
                  }}
                >
                  <Box className="free-demo-left-content">
                    <Typography
                      variant="h5"
                      className="free-demo-left-title heading-text"
                    >
                      Start a <br /> free demo with us!
                    </Typography>
                    <Typography component="p" className="free-demo-left-desc">
                      Discover how our powerful TeamTrain Hub can simplify your
                      HR operations, boost productivity, and improve employee
                      satisfaction. Get a personalized walkthrough from our
                      experts. Whether you're a small business or a growing
                      enterprise, our system adapts to your team's unique needs
                    </Typography>
                    <Box className="important-points-wrap">
                      <Typography
                        variant="h6"
                        className="important-points-title"
                      >
                        Key Features You'll Explore
                      </Typography>
                      <List className="important-points-list">
                        {ListData?.map((item, index) => {
                          return (
                            <ListItem key={index} className="list-item d-flex">
                              <Image
                                src={RightIcon}
                                alt="icon"
                                height={20}
                                width={20}
                              />
                              {item.title}
                            </ListItem>
                          );
                        })}
                        {/* <ListItem className="list-item d-flex">
                    <Image src={RightIcon} alt="icon" height={20} width={20} />
                    Lorem Ipsum has been the industry.
                  </ListItem>
                  <ListItem className="list-item d-flex">
                    <Image src={RightIcon} alt="icon" height={20} width={20} />
                    Contrary to popular belief
                  </ListItem>
                  <ListItem className="list-item d-flex">
                    <Image src={RightIcon} alt="icon" height={20} width={20} />
                    There are many variations of passages
                  </ListItem>
                  <ListItem className="list-item d-flex">
                    <Image src={RightIcon} alt="icon" height={20} width={20} />
                    Lorem Ipsum is not simply random text.
                  </ListItem>
                  <ListItem className="list-item d-flex">
                    <Image src={RightIcon} alt="icon" height={20} width={20} />
                    All the Lorem Ipsum generators on the.
                  </ListItem> */}
                      </List>
                    </Box>
                  </Box>
                  {/* <Box
                  className="free-demo-left-back-svg"
                  style={{
                    backgroundImage: `url(${BackgroundImage.src})`,
                    width: '100%',
                    height: '100%',
                  }}
                > */}
                  {/* <FreeDemoLeftBackSvg className="left-back-svg" /> */}
                  {/* <Image
                    src={BackgroundImage}
                    alt="icon"
                    height={100}
                    width={20}
                  /> */}
                  {/* </Box> */}
                </Box>
                <Box className="free-demo-right-content-wrap">
                  <Typography
                    variant="h5"
                    className="free-demo-right-title heading-text"
                  >
                    Start a <br /> free demo with us!
                  </Typography>
                  <Box className="free-demo-right-form-wrap">
                    <Formik
                      initialValues={{
                        first_name: '',
                        last_name: '',
                        country_code: '',
                        phone_number: '',
                        email: '',
                        org_name: '',
                        number_of_employees: '',
                        industry: '',
                        how_can_help: '',
                      }}
                      validationSchema={Yup.object().shape({
                        first_name: Yup.string().required(
                          'First name is required'
                        ),
                        last_name: Yup.string().required(
                          'Last name is required'
                        ),
                        country_code: Yup.string().required(
                          'Country code is required'
                        ),
                        phone_number: Yup.string()
                          .required('Phone number is required')
                          .matches(
                            /^[0-9]{10,11}$/,
                            'Please enter valid phone number'
                          ),
                        email: Yup.string()
                          .required('Email is required')
                          .email('Invalid email address'),
                        org_name: Yup.string().required(
                          'Organization name is required'
                        ),
                        number_of_employees: Yup.string()
                          .required('Number of employees is required')
                          .matches(
                            /^\d+$/,
                            'Number of employees must be a numeric value'
                          ),
                        industry: Yup.string().required('Industry is required'),
                      })}
                      onSubmit={async (requestData) => {
                        let sendData = {
                          first_name: requestData?.first_name,
                          last_name: requestData?.last_name,
                          email: requestData?.email,
                          mobile_number: requestData?.phone_number,
                          organization_name: requestData?.org_name,
                          number_of_employee: requestData?.number_of_employees,
                          industry: requestData?.industry,
                          country_code: selectedCountryId,
                          description: requestData?.how_can_help,
                        };

                        try {
                          // setLoader(true);
                          const { status, data } = await axiosInstance.post(
                            ORG_URLS?.FREE_DEMO,
                            sendData
                          );
                          if (status === 200 || status === 201) {
                            setApiMessage('success', data?.message);
                            handleClose();
                          }
                          // setLoader(false);
                        } catch (error) {
                          setApiMessage(
                            'error',
                            error?.response?.data?.message
                          );
                        }
                      }}
                    >
                      {({
                        errors,
                        touched,
                        handleBlur,
                        values,
                        handleSubmit,
                        handleChange,
                        setFieldValue,
                      }) => (
                        <Form onSubmit={handleSubmit} className="form-wrap">
                          <Box className="input-field-wrap">
                            <Box className="input-wrap">
                              <Typography
                                variant="h6"
                                className="un-auth-label-wrap"
                              >
                                First Name*
                              </Typography>
                              <TextField
                                InputLabelProps={{ shrink: true }}
                                id="first_name"
                                name="first_name"
                                className="w100"
                                variant="standard"
                                placeholder="First name"
                                value={values.first_name}
                                error={Boolean(
                                  touched.first_name && errors.first_name
                                )}
                                helperText={
                                  touched.first_name && errors.first_name
                                }
                                onBlur={handleBlur}
                                onChange={handleChange}
                                InputProps={{
                                  endAdornment: (
                                    <InputAdornment position="end">
                                      <IconButton>
                                        <Box className="eye-wrap">
                                          <ProfileRoundIcon />
                                        </Box>
                                      </IconButton>
                                    </InputAdornment>
                                  ),
                                }}
                              />
                            </Box>

                            <Box className=" input-wrap">
                              <Typography
                                variant="h6"
                                className="un-auth-label-wrap"
                              >
                                Last Name*
                              </Typography>
                              <TextField
                                InputLabelProps={{ shrink: true }}
                                id="last_name"
                                name="last_name"
                                placeholder="Last_name"
                                value={values.last_name}
                                error={Boolean(
                                  touched.last_name && errors.last_name
                                )}
                                helperText={
                                  touched.last_name && errors.last_name
                                }
                                className="w100"
                                variant="standard"
                                onBlur={handleBlur}
                                onChange={handleChange}
                                InputProps={{
                                  endAdornment: (
                                    <InputAdornment position="end">
                                      <IconButton>
                                        <Box className="eye-wrap">
                                          <ProfileRoundIcon />
                                        </Box>
                                      </IconButton>
                                    </InputAdornment>
                                  ),
                                }}
                              />
                            </Box>

                            <Box className=" input-wrap">
                              <Typography
                                className="un-auth-label-wrap"
                                variant="h6"
                              >
                                Email Address*
                              </Typography>
                              <TextField
                                InputLabelProps={{ shrink: true }}
                                id="email"
                                name="email"
                                placeholder="Email address"
                                value={values?.email}
                                error={Boolean(touched?.email && errors?.email)}
                                helperText={touched?.email && errors?.email}
                                className="w100"
                                variant="standard"
                                onBlur={handleBlur}
                                onChange={handleChange}
                                InputProps={{
                                  endAdornment: (
                                    <InputAdornment position="end">
                                      <IconButton>
                                        <Box className="eye-wrap">
                                          <EmailIcon />
                                        </Box>
                                      </IconButton>
                                    </InputAdornment>
                                  ),
                                }}
                              />
                            </Box>

                            <Box className=" input-wrap">
                              <Typography
                                variant="h6"
                                className="un-auth-label-wrap"
                              >
                                Organization Name*
                              </Typography>
                              <TextField
                                fullWidth
                                InputLabelProps={{ shrink: true }}
                                id="org_name"
                                name="org_name"
                                placeholder="Organization name"
                                value={values?.org_name}
                                error={Boolean(
                                  touched?.org_name && errors?.org_name
                                )}
                                helperText={
                                  touched?.org_name && errors?.org_name
                                }
                                className="w100"
                                variant="standard"
                                onBlur={handleBlur}
                                onChange={handleChange}
                                InputProps={{
                                  endAdornment: (
                                    <InputAdornment position="end">
                                      <IconButton>
                                        <Box className="eye-wrap">
                                          <Buildingicon />
                                        </Box>
                                      </IconButton>
                                    </InputAdornment>
                                  ),
                                }}
                              />
                            </Box>

                            <Box className="input-wrap">
                              <Typography
                                className="un-auth-label-wrap"
                                variant="h6"
                              >
                                Mobile Number*
                              </Typography>
                              <Box
                                className={`d-flex align-center phone-input-wrap ${
                                  touched.phone_number && errors.phone_number
                                    ? 'error'
                                    : ''
                                }`}
                              >
                                <PhoneNumberWithCountryCode
                                  label=""
                                  onBlur={handleBlur}
                                  onChange={handleChange}
                                  fullWidth
                                  values={values}
                                  setFieldValue={setFieldValue}
                                  handleBlur={handleBlur}
                                  handleChange={handleChange}
                                  selectedCountryId={selectedCountryId}
                                  setSelectedCountryId={setSelectedCountryId}
                                />
                                <Box
                                  className={`${
                                    touched?.phone_number &&
                                    errors?.phone_number
                                      ? 'phone-icon-wrap'
                                      : 'phone-icon'
                                  }`}
                                >
                                  <PhoneIcon />
                                </Box>
                              </Box>
                              {touched?.phone_number &&
                                errors?.phone_number && (
                                  <Typography
                                    className="error-message"
                                    variant="body2"
                                    color="error"
                                  >
                                    {errors?.phone_number}
                                  </Typography>
                                )}
                            </Box>

                            <Box className=" input-wrap">
                              <Typography
                                variant="h6"
                                className="un-auth-label-wrap"
                              >
                                Number of employees*
                              </Typography>
                              <TextField
                                fullWidth
                                InputLabelProps={{ shrink: true }}
                                id="number_of_employees"
                                name="number_of_employees"
                                placeholder="Total number of employees"
                                value={values?.number_of_employees}
                                error={Boolean(
                                  touched?.number_of_employees &&
                                    errors?.number_of_employees
                                )}
                                helperText={
                                  touched?.number_of_employees &&
                                  errors?.number_of_employees
                                }
                                className="w100"
                                variant="standard"
                                onBlur={handleBlur}
                                onChange={handleChange}
                                onInput={(e) => {
                                  e.target.value = e.target.value.replace(
                                    /[^0-9]/g,
                                    ''
                                  ); // Remove non-numeric characters
                                }}
                                InputProps={{
                                  endAdornment: (
                                    <InputAdornment position="end">
                                      <IconButton>
                                        <Box className="eye-wrap">
                                          <PeopleIcon />
                                        </Box>
                                      </IconButton>
                                    </InputAdornment>
                                  ),
                                }}
                              />
                            </Box>

                            <Box className=" input-wrap">
                              <Typography
                                variant="h6"
                                className="un-auth-label-wrap"
                              >
                                Industry*
                              </Typography>
                              <TextField
                                fullWidth
                                InputLabelProps={{ shrink: true }}
                                id="industry"
                                name="industry"
                                placeholder="Your industry"
                                value={values?.industry}
                                error={Boolean(
                                  touched?.industry && errors?.industry
                                )}
                                helperText={
                                  touched?.industry && errors?.industry
                                }
                                className="w100"
                                variant="standard"
                                onBlur={handleBlur}
                                onChange={handleChange}
                                InputProps={{
                                  endAdornment: (
                                    <InputAdornment position="end">
                                      <IconButton>
                                        <Box className="eye-wrap">
                                          <Industry />
                                        </Box>
                                      </IconButton>
                                    </InputAdornment>
                                  ),
                                }}
                              />
                            </Box>
                            <Box className=" input-wrap">
                              <Typography
                                variant="h6"
                                className="un-auth-label-wrap"
                              >
                                How can we help ?
                              </Typography>
                              <TextField
                                fullWidth
                                InputLabelProps={{ shrink: true }}
                                id="how_can_help"
                                name="how_can_help"
                                placeholder="Describe your needs"
                                value={values?.how_can_help}
                                error={Boolean(
                                  touched?.how_can_help && errors?.how_can_help
                                )}
                                helperText={
                                  touched?.how_can_help && errors?.how_can_help
                                }
                                className="w100"
                                variant="standard"
                                onBlur={handleBlur}
                                onChange={handleChange}
                                InputProps={{
                                  endAdornment: (
                                    <InputAdornment position="end">
                                      <IconButton>
                                        <Box className="eye-wrap">
                                          <MessageIcon />
                                        </Box>
                                      </IconButton>
                                    </InputAdornment>
                                  ),
                                }}
                              />
                            </Box>
                          </Box>
                          <Box className="submit-btn-wrap" textAlign="center">
                            <CustomButton
                              fullWidth
                              className="submit-btn"
                              type="submit"
                              variant="contained"
                              title="Submit"
                            />
                          </Box>
                        </Form>
                      )}
                    </Formik>
                  </Box>
                </Box>
              </Box>
            </DialogContent>
          </Box>
        </Box>
      </Dialog>
    </>
  );
}
