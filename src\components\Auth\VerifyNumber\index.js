'use client';
import { Box, Typography } from '@mui/material';
import React from 'react';
import CustomButton from '@/components/UI/button';
import './verifynumber.scss';

export default function VerifyNumber() {
  return (
    <Box className="verify-number-wrap">
      <Typography variant="h5" className="heading-wrap">
        Phone Number verification
      </Typography>
      <Typography component="p" className="text-wrap">
        You will be recieved an message with a one time password on your
        registerd mobile number
      </Typography>
      <Box className="pt24 pb24 proceed-btn-wrap" textAlign="center">
        <CustomButton
          className="p16 proceed-btn"
          type="submit"
          fontWeight="600"
          variant="contained"
          background="#39596e"
          backgroundhover="#39596e"
          colorhover="#FFFFFF"
          title="Next"
          onClick={() => console.log('Button clicked')}
        />
      </Box>
    </Box>
  );
}
