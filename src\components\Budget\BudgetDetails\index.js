'use client';

import React, { useContext, useEffect, useState, useRef } from 'react';
import { Box, Typography, Popover, Tooltip, MenuItem } from '@mui/material';
import { useRouter } from 'next/navigation';
import AuthContext from '@/helper/authcontext';
import BudgetTable from '../BudgetTable';
import BudgetTableBM from '../BudgetTableBM';
import EditIcon from '@/components/ActionIcons/EditIcon';
import DeleteIcon from '@/components/ActionIcons/DeleteIcon';
import DownloadIcon from '@/components/ActionIcons/Download';
import HistoryIcon from '@mui/icons-material/History';
import InfoIcon from '@mui/icons-material/Info';
import CustomButton from '@/components/UI/CustomButton';
import SaveIcon from '@mui/icons-material/Save';
import SaveAsIcon from '@mui/icons-material/SaveAs';
import InfiniteScroll from 'react-infinite-scroll-component';
import DialogBox from '@/components/UI/Modalbox';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import PersonRemoveIcon from '@mui/icons-material/PersonRemove';
import ConfirmationModal from '@/components/UI/ConfirmationModal';
import NoDataView from '@/components/UI/NoDataView';
import './budgetdetails.scss';

const FilterInfo = () => {
  return (
    <Box>
      <Typography className="title-text fw600 filter-name d-flex">
        Define the Filter
        <span>
          <Tooltip
            title={
              <Box className="tooltip-seasonality-menu">
                <MenuItem className="seasonality-Profile-title mb-25 body-text fw600">
                  Filter Info
                </MenuItem>
                <MenuItem>
                  <Box className="tri-state-toggle-wrap">
                    <Box className="tri-state-toggle green">
                      <input
                        className="active togglbuton"
                        type="radio"
                        name="toggleprior"
                        value={0}
                        checked={true}
                      />
                      <input
                        className="togglbuton"
                        type="radio"
                        name="toggleprior"
                        value={1}
                        checked={false}
                      />
                    </Box>
                  </Box>
                  <Typography className="title-text">
                    Based of prior year data
                  </Typography>
                </MenuItem>
                <MenuItem className="mb16">
                  <Box className="tri-state-toggle-wrap">
                    <Box className="tri-state-toggle blue">
                      <input
                        className="togglbuton"
                        type="radio"
                        name="toggleevenly"
                        value={0}
                        checked={false}
                      />
                      <input
                        className="active togglbuton"
                        type="radio"
                        name="toggleevenly"
                        value={1}
                        checked={true}
                      />
                    </Box>
                  </Box>
                  <Typography className="title-text">
                    Distribute evenly across the year
                  </Typography>
                </MenuItem>
              </Box>
            }
            arrow
            placement="bottom"
            classes={{
              tooltip: 'seasonality-tooltip-container',
            }}
          >
            {' '}
            <InfoIcon />
          </Tooltip>
        </span>
      </Typography>
    </Box>
  );
};

export default function BudgetDetails({
  BudgetData,
  setBudgetData,
  setRandom,
  // LockForecast,
  getBudgetByID,
  // getPreviousBudget,
  // previousBudget,
  fetchMoreData,
  hasMore,
  SaveBudgetDetails,
  getExportBudget,
  seprateBranch,
  DeleteForecast,
}) {
  const { authState } = useContext(AuthContext);
  const [anchorEl, setAnchorEl] = useState(null);
  const [Download, setDownload] = useState();
  const [anchorElInfo, setAnchorElInfo] = useState(null);
  const [createModalDep2, setCreateModalDep2] = useState(false);
  const router = useRouter();
  const open = Boolean(anchorEl);
  const id = open ? 'simple-popover' : undefined;
  const openProfile = anchorElInfo;
  const handleClick = (event, item) => {
    setAnchorEl(event.currentTarget);
    setDownload(item);
  };
  const handleClose = () => {
    setAnchorEl(null);
    setDownload();
  };
  function getStartMonth(financialMonth) {
    // Mapping month names to numbers
    const monthMap = {
      january: 1,
      february: 2,
      march: 3,
      april: 4,
      may: 5,
      june: 6,
      july: 7,
      august: 8,
      september: 9,
      october: 10,
      november: 11,
      december: 12,
    };

    // Extract start month name (before ' - ')
    const startMonthName = financialMonth?.toLowerCase().split(' - ')[0];

    // Get corresponding month number
    return monthMap[startMonthName] || null;
  }
  function getAcademicYear() {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1; // getMonth() is zero-based
    const [startYear] = authState?.generalSeetings?.financial_month
      ? authState?.generalSeetings?.financial_month.split(' - ')
      : 'april';
    const monthCount = getStartMonth(startYear);
    // If financial year starts in January, it follows the calendar year
    if (monthCount === 1) {
      return `${currentYear}-${currentYear + 1}`;
    }
    // If the current month is before the financial start month, previous year is the start
    if (currentMonth < monthCount) {
      return `${currentYear - 1}-${currentYear}`;
    } else {
      return `${currentYear}-${currentYear + 1}`;
    }
  }

  const handleCloseProfile = () => {
    setTimeout(() => {
      setAnchorElInfo(false);
    }, 10);
  };
  const IsAccountant = authState?.web_user_active_role_id === 6;

  // const calculateTotalForPast = (data, key) => {
  //   const total = data?.data
  //     .filter((item) => item[key] && item[key].isPast) // Filter isPast === false
  //     .reduce((sum, item) => {
  //       const value = parseFloat(item[key].Value) || 0; // Parse Value to number
  //       return sum + value;
  //     }, 0);

  //   return total.toFixed(2); // Round to 2 decimal places
  // };
  // const TotalValue = (data, key) => {
  //   const total = data?.data?.reduce((sum, item) => {
  //     const value = parseFloat(item[key]?.Value) || 0;
  //     return sum + value;
  //   }, 0);

  //   // Return total rounded to 2 decimal places
  //   return total.toFixed(2);
  // };
  // const getPastBudgetTotal = (data, index, columns_group) => {
  //   const firstChildKeys = columns_group
  //     .map(
  //       (col) =>
  //         col?.group_type !== 'Total' &&
  //         col.children?.[index]?.key &&
  //         col?.isPast === true
  //     )
  //     .filter(Boolean);
  //   const total = firstChildKeys.reduce(
  //     (sum, key) => sum + (parseFloat(data[key]) || 0),
  //     0
  //   );
  //   return total.toFixed(2);
  // };
  const getAllTotals = (data, index, columns_group) => {
    const firstChildKeys = columns_group
      .map((col) => col?.group_type !== 'Total' && col.children?.[index]?.key)
      .filter(Boolean);
    const total = firstChildKeys.reduce(
      (sum, key) => sum + (parseFloat(data[key]) || 0),
      0
    );
    return total.toFixed(2);
  };
  const OnChangeFilter = (index, toggle, BudgetDataTable) => {
    if (toggle === 'prior') {
      const BudgetTableValue = BudgetDataTable?.data?.map((row) => {
        let rowupdated = row;
        BudgetDataTable?.columns_group?.map((group) => {
          group?.children?.map((item, findex) => {
            if (findex === 1) {
              if (group?.isPast === false) {
                // const remainingbudget =
                //   row['col41'] -
                //   getPastBudgetTotal(row, 1, BudgetDataTable?.columns_group);
                const remainingbudget = row['col41'];
                const priousKey = `col${parseFloat(item?.key?.replace('col', '')) - 1}`;
                const value = /^0+(\.0+)?$/.test(
                  parseFloat(
                    getAllTotals(row, 0, BudgetDataTable?.columns_group)
                  )
                )
                  ? (0).toFixed(2)
                  : /^0+(\.0+)?$/.test(parseFloat(row[priousKey]))
                    ? (0).toFixed(2)
                    : (
                        (remainingbudget * row[priousKey]) /
                        getAllTotals(row, 0, BudgetDataTable?.columns_group)
                      ).toFixed(2);
                const perKey = `col${parseFloat(item?.key?.replace('col', '')) + 1}`;
                const actualKey = `col${parseFloat(item?.key?.replace('col', '')) - 1}`;
                let per = /^0+(\.0+)?$/.test(value)
                  ? 0
                  : ((parseFloat(value) - parseFloat(row[actualKey])) /
                      parseFloat(value)) *
                    100;
                rowupdated = {
                  ...rowupdated,
                  [item?.key]: value,
                  [perKey]: per.toFixed(2),
                };
              }
            }
          });
        });

        return rowupdated;
      });
      let budget = BudgetData;
      budget[index].forecast_bugdet_data = {
        ...budget[index].forecast_bugdet_data,
        data: BudgetTableValue,
      };
      setBudgetData(budget);
      setRandom(Math.random());
    } else if (toggle === 'distribute') {
      const RemaingMonths =
        BudgetDataTable?.columns_group &&
        BudgetDataTable?.columns_group?.length > 0 &&
        BudgetDataTable?.columns_group?.filter((entry) => {
          return entry?.isPast === false;
        }).length - 3;
      // const CommpletedMonths =
      //   BudgetDataTable?.columns_group &&
      //   BudgetDataTable?.columns_group?.length > 0 &&
      //   BudgetDataTable?.columns_group?.filter((entry) => {
      //     return entry?.isPast === true;
      //   });
      const BudgetTableValue = BudgetDataTable?.data?.map((row) => {
        let rowupdated = row;
        BudgetDataTable?.columns_group?.map((group) => {
          group?.children?.map((item, findex) => {
            if (findex === 1) {
              if (
                group?.isPast === false &&
                group?.group_type !== 'Total' &&
                group?.key !== 'col41'
              ) {
                // const remainingbudget =
                //   row['col41'] -
                //   getPastBudgetTotal(row, 1, BudgetDataTable?.columns_group);
                const remainingbudget = row['col41'];
                const value = (remainingbudget / RemaingMonths).toFixed(2);
                const perKey = `col${parseFloat(item?.key?.replace('col', '')) + 1}`;
                const actualKey = `col${parseFloat(item?.key?.replace('col', '')) - 1}`;
                let per = /^0+(\.0+)?$/.test(value)
                  ? 0
                  : ((parseFloat(value) - parseFloat(row[actualKey])) /
                      parseFloat(value)) *
                    100;
                rowupdated = {
                  ...rowupdated,
                  [item?.key]: value,
                  [perKey]: per.toFixed(2),
                };
              }
            }
          });
        });

        return rowupdated;
      });
      let budget = BudgetData;
      budget[index].forecast_bugdet_data = {
        ...budget[index].forecast_bugdet_data,
        data: BudgetTableValue,
      };
      setBudgetData(budget);
      setRandom(Math.random());
    }
  };
  const CheckIsPast = (forecastYear) => {
    const [firstYear] = forecastYear.split('-').map(Number);
    const [firstYearCurrent] = getAcademicYear().split('-').map(Number);
    if (firstYearCurrent <= firstYear) {
      return true;
    } else {
      return false;
    }
  };
  const scrollContainerRef = useRef(null);
  useEffect(() => {
    const checkAndLoadMore = () => {
      const container = scrollContainerRef.current;
      if (
        container &&
        container.scrollHeight <= container.clientHeight &&
        hasMore
      ) {
        fetchMoreData();
      }
    };

    checkAndLoadMore(); // Check on mount and updates
  }, [BudgetData, hasMore]);
  return (
    <Box className="Budget-details-section">
      {authState?.web_user_active_role_id === 7 ||
      authState?.web_user_active_role_id === 14 ? (
        <>
          {BudgetData?.map((item, index) => {
            return (
              <Box className="Budget-details mt32">
                <Box className="Budget-details-action">
                  <Box className={`budget-branch-filter `}>
                    <Box
                      className={`${CheckIsPast(item?.forecast_year) && item?.forecast_status === 'assigned' ? '' : 'border-right-0'} branch-name-year `}
                    >
                      {item?.forecast_branch?.branch_name && (
                        <Typography className="title-text fw600 text-ellipsis-line">
                          Branch :{' '}
                          <span className="fw400">
                            {' '}
                            {item?.forecast_branch?.branch_name}
                          </span>
                        </Typography>
                      )}
                      {item?.forecast_year && (
                        <Typography className="title-text fw600 d-flex align-center">
                          Year :{' '}
                          <span className="fw400">{item?.forecast_year}</span>
                          <span className="budget-status">
                            {item?.forecast_status === 'approved' ? (
                              <span className="sub-title-text invited-accepted fw600 text-capital">
                                Approved
                              </span>
                            ) : (
                              <span className="sub-title-text invited-ongoing fw600 text-capital">
                                Pending
                              </span>
                            )}
                          </span>
                        </Typography>
                      )}
                    </Box>
                    {CheckIsPast(item?.forecast_year) &&
                    item?.forecast_status === 'assigned' ? (
                      <>
                        <Box className="branch-filter">
                          {' '}
                          {FilterInfo(openProfile, handleCloseProfile)}
                          <Box
                            className={
                              item?.forecast_locked
                                ? 'prior-filter prior-filter-default'
                                : 'prior-filter'
                            }
                          >
                            <Box className="d-flex align-center budget-prior-filter">
                              <Box className="tri-state-toggle-wrap">
                                <Box
                                  className={`tri-state-toggle ${
                                    item?.toggleout === 'prior'
                                      ? 'green'
                                      : item?.toggleout === 'distribute'
                                        ? 'blue'
                                        : item?.toggleout === 'industry'
                                          ? 'orange'
                                          : ''
                                  }`}
                                  onChange={(e) => {
                                    if (
                                      item?.forecast_bugdet_data &&
                                      item?.forecast_bugdet_data?.columns_group
                                        ?.length > 1 &&
                                      !item?.forecast_locked &&
                                      CheckIsPast(item?.forecast_year)
                                    ) {
                                      let budget = BudgetData;
                                      budget[index].toggleout = e.target.value;
                                      setBudgetData(budget);
                                      setRandom(Math.random());
                                    }
                                  }}
                                >
                                  <input
                                    className={
                                      item?.toggleout === 'prior'
                                        ? 'active togglbuton'
                                        : 'togglbuton'
                                    }
                                    type="radio"
                                    name="toggleout"
                                    value={'prior'}
                                    checked={item?.toggleout === 'prior'}
                                  />
                                  <input
                                    className={
                                      item?.toggleout === 'distribute'
                                        ? 'active togglbuton'
                                        : 'togglbuton'
                                    }
                                    type="radio"
                                    name="toggleout"
                                    value={'distribute'}
                                    checked={item?.toggleout === 'distribute'}
                                  />
                                  {/* <input
                                className={
                                  item?.toggleout === 'industry'
                                    ? 'active togglbuton'
                                    : 'togglbuton'
                                }
                                type="radio"
                                name="toggleout"
                                value={'industry'}
                                checked={item?.toggleout === 'industry'}
                              /> */}
                                </Box>
                              </Box>
                              <Typography className="sub-title-text fw600">
                                {item?.toggleout === 'prior'
                                  ? 'Based of prior year data'
                                  : item?.toggleout === 'distribute'
                                    ? 'Distribute evenly across the year'
                                    : 'Pre-defined based on sector'}
                              </Typography>
                            </Box>
                            <CustomButton
                              variant="contained"
                              title="Apply"
                              className="apply-prior"
                              disabled={
                                !(
                                  item?.forecast_bugdet_data &&
                                  item?.forecast_bugdet_data?.columns_group
                                    ?.length > 1
                                ) ||
                                item?.forecast_locked ||
                                !CheckIsPast(item?.forecast_year)
                              }
                              fullWidth={false}
                              onClick={() => {
                                OnChangeFilter(
                                  index,
                                  item?.toggleout,
                                  item?.forecast_bugdet_data
                                );
                              }}
                            />
                          </Box>
                        </Box>
                      </>
                    ) : (
                      <></>
                    )}
                  </Box>
                  <Box className="budget-save-actions">
                    <Box className="d-flex budget-icons pb16">
                      {item?.forecast_status === 'assigned' &&
                        item?.ForecastAssignBudgets?.is_create_budget &&
                        CheckIsPast(item?.forecast_year) &&
                        authState?.UserPermission?.forecast === 2 && ( // &&
                          <Tooltip
                            title={<Typography>Edit</Typography>}
                            placement="bottom"
                            arrow
                            classes={{
                              tooltip: 'info-tooltip-container ',
                            }}
                          >
                            {' '}
                            <EditIcon
                              onClick={() =>
                                setCreateModalDep2({
                                  item: item,
                                  type: 'edit',
                                  index: index,
                                })
                              }
                            />
                          </Tooltip>
                        )}
                      {/* {authState?.UserPermission?.forecast === 2 && (
                        <Tooltip title={'History'} placement="bottom" arrow>
                          {' '}
                          <Box
                            className="action-icon d-flex history-icon"
                            onClick={() =>
                              router.push(`/budget-forecast/${item?.id}`)
                            }
                          >
                            {' '}
                            <HistoryIcon />
                          </Box>
                        </Tooltip>
                      )} */}
                      {(item?.forecast_status === 'approved' ||
                        item?.forecast_status === 'submitted') && (
                        <>
                          <Tooltip
                            title={<Typography>Download</Typography>}
                            placement="bottom"
                            arrow
                            classes={{
                              tooltip: 'info-tooltip-container ',
                            }}
                          >
                            <Box>
                              <DownloadIcon
                                onClick={(e) => handleClick(e, item)}
                              />
                            </Box>
                          </Tooltip>
                          <Popover
                            className="export-popover"
                            id={id}
                            open={open}
                            anchorEl={anchorEl}
                            onClose={handleClose}
                            anchorOrigin={{
                              vertical: 'bottom',
                              horizontal: 'left',
                            }}
                          >
                            <Box className="export-option">
                              <Typography
                                className={
                                  Download?.forecast_bugdet_data &&
                                  Download?.forecast_bugdet_data?.columns_group
                                    ?.length > 1
                                    ? 'title-text fw600 pb8 cursor-pointer'
                                    : 'title-text fw600 pb8'
                                }
                                onClick={() =>
                                  Download?.forecast_bugdet_data &&
                                  Download?.forecast_bugdet_data?.columns_group
                                    ?.length > 1 &&
                                  getExportBudget(Download?.id, 'csv', Download)
                                }
                              >
                                {' '}
                                CSV{' '}
                              </Typography>
                              <Typography
                                className={
                                  Download?.forecast_bugdet_data &&
                                  Download?.forecast_bugdet_data?.columns_group
                                    ?.length > 1
                                    ? 'title-text fw600 pb8 cursor-pointer'
                                    : 'title-text fw600 pb8'
                                }
                                onClick={() =>
                                  Download?.forecast_bugdet_data &&
                                  Download?.forecast_bugdet_data?.columns_group
                                    ?.length > 1 &&
                                  getExportBudget(
                                    Download?.id,
                                    'excel',
                                    Download
                                  )
                                }
                              >
                                {' '}
                                Excel{' '}
                              </Typography>
                            </Box>
                          </Popover>
                        </>
                      )}
                    </Box>
                    {CheckIsPast(item?.forecast_year) ? (
                      <Box className="save-buttons">
                        {!item?.forecast_locked &&
                          item?.forecast_status === 'assigned' && (
                            <Box className="d-flex gap-sm align-center save-btns-icon">
                              <CustomButton
                                variant="outlined"
                                className="save-btn"
                                onClick={() =>
                                  SaveBudgetDetails(item, 'draft', index)
                                }
                                startIcon={<SaveAsIcon />}
                                disabled={
                                  !item?.forecast_bugdet_data ||
                                  item?.forecast_bugdet_data?.columns_group
                                    ?.length === 0
                                }
                                title={'Save as Draft'}
                              />
                              <CustomButton
                                variant="contained"
                                className="save-btn"
                                onClick={() =>
                                  SaveBudgetDetails(item, 'submitted', index)
                                }
                                startIcon={<SaveIcon />}
                                disabled={
                                  !item?.forecast_bugdet_data ||
                                  item?.forecast_bugdet_data?.columns_group
                                    ?.length === 0
                                }
                                title={'Submit for Approval'}
                              />
                            </Box>
                          )}
                      </Box>
                    ) : (
                      <></>
                    )}
                  </Box>
                </Box>
                {item?.forecast_bugdet_data &&
                item?.forecast_bugdet_data?.columns_group?.length > 1 ? (
                  <BudgetTableBM
                    BudgetData={item?.forecast_bugdet_data} //item?.BudgetTable}
                    Targetdata={item?.forecast_bugdet_data?.targetarray}
                    setBudgetData={setBudgetData}
                    BudgetDataReal={BudgetData}
                    parentIndex={index}
                    budgetDetails={item}
                    setRandom={setRandom}
                    isFromHistory={
                      item?.forecast_status === 'active' ||
                      item?.forecast_status === 'approved' ||
                      item?.forecast_status === 'submitted'
                    }
                  />
                ) : (
                  <>
                    <Box
                      className="mt32"
                      // style={{
                      //   height: 'calc(100vh - 300px)',
                      //   overflow: 'auto',
                      // }}
                    >
                      <Box className="no-data d-flex align-center justify-center">
                        <NoDataView
                          title="No Budget Data Found"
                          description="There is no budget data available at the moment."
                        />
                      </Box>
                    </Box>
                  </>
                )}
              </Box>
            );
          })}
          {/* <Box className="mt16">
            {' '}
            <Box className="Budget-details previous-budget-details">
              <Box className="previous-year mb16">
                <Box className="budget-custom-date-fields ">
                  <ReactYearRangePicker
                    value={PreviousYear}
                    disableFuture={true}
                    onChange={(date) => {
                      setPreviousYear({
                        Year: date?.Year,
                        ForecastYear: `${date?.Year.getFullYear()}-${
                          date?.Year.getFullYear() + 1
                        }`,
                      });
                    }}
                    inputVariant="outlined"
                    isClearable={false}
                    placeholder="Select Year"
                    label="Choose Forecasting Year"
                    // maxDate={getEndOfCurrentWeek()}
                  />
                </Box>
                <CustomButton
                  variant="contained"
                  background="#39596e"
                  backgroundhover="#FFFFFF"
                  colorhover="#000000"
                  className="sub-title-text"
                  fontWeight="600"
                  title="Apply"
                  fullWidth={false}
                  onClick={() => {
                    getPreviousBudget(
                      authState?.branch?.id,
                      PreviousYear?.ForecastYear
                    );
                  }}
                />
              </Box>
              {previousBudget?.forecast_bugdet_data &&
              previousBudget?.forecast_bugdet_data?.columns_group?.length >
                1 ? (
                <BudgetTableBM
                  BudgetData={previousBudget?.forecast_bugdet_data}
                  BudgetDataReal={previousBudget}
                  parentIndex={0}
                  budgetDetails={previousBudget}
                  isFromHistory={true}
                />
              ) : (
                <>
                  <Box className="mt32">
                    <Typography className="text-align h6 color-gray">
                      {previousBudget
                        ? 'No data found'
                        : 'Please select year for view previous year'}
                    </Typography>
                  </Box>
                </>
              )}
            </Box>
          </Box> */}
        </>
      ) : (
        <>
          <Box
            id="scrollableSidebar"
            ref={scrollContainerRef}
            style={{
              height: 'calc(100vh - 300px - var(--banner-height))',
              overflow: 'auto',
            }}
          >
            <InfiniteScroll
              dataLength={BudgetData?.length}
              next={fetchMoreData}
              hasMore={hasMore}
              loader={<Typography>Loading more budget data...</Typography>}
              scrollableTarget="scrollableSidebar"
            >
              {BudgetData?.map((item, index) => {
                return (
                  <Box className="Budget-details">
                    <Box className="Budget-details-action">
                      <Box
                        className={`budget-branch-filter ${
                          (!seprateBranch ||
                            item?.forecast_status === 'approved' ||
                            IsAccountant) &&
                          'all-branch-filter'
                        }`}
                      >
                        <Box
                          className={`branch-name-year ${
                            (!seprateBranch ||
                              item?.forecast_status === 'approved' ||
                              IsAccountant) &&
                            'all-branch'
                          }`}
                        >
                          {item?.forecast_branch?.branch_name && (
                            <Typography className="title-text fw600 text-ellipsis-line">
                              Branch :{' '}
                              <span className="fw400">
                                {' '}
                                {item?.forecast_branch?.branch_name}
                              </span>
                            </Typography>
                          )}
                          {item?.forecast_year && (
                            <Typography className="title-text fw600 d-flex align-center">
                              Year :{' '}
                              <span className="fw400">
                                {item?.forecast_year}
                              </span>
                              <span className="budget-status">
                                {item?.forecast_status === 'approved' ? (
                                  <span className="sub-title-text invited-accepted fw600 text-capital">
                                    Approved
                                  </span>
                                ) : (
                                  <span className="sub-title-text invited-ongoing fw600 text-capital">
                                    Pending
                                  </span>
                                )}
                              </span>
                            </Typography>
                          )}
                        </Box>
                        {seprateBranch &&
                          item?.forecast_status !== 'approved' &&
                          !IsAccountant && (
                            <Box className="branch-filter">
                              {FilterInfo(openProfile, handleCloseProfile)}
                              <Box
                                className={
                                  item?.forecast_locked
                                    ? 'prior-filter prior-filter-default'
                                    : 'prior-filter'
                                }
                              >
                                <Box className="d-flex align-center budget-prior-filter">
                                  <Box className="tri-state-toggle-wrap">
                                    <Box
                                      className={`tri-state-toggle ${
                                        item?.toggleout === 'prior'
                                          ? 'green'
                                          : item?.toggleout === 'distribute'
                                            ? 'blue'
                                            : item?.toggleout === 'industry'
                                              ? 'orange'
                                              : ''
                                      }`}
                                      onChange={(e) => {
                                        if (
                                          item?.forecast_bugdet_data &&
                                          item?.forecast_bugdet_data
                                            ?.columns_group?.length > 1 && //!item?.forecast_locked &&
                                          CheckIsPast(item?.forecast_year) &&
                                          authState?.UserPermission
                                            ?.forecast === 2 &&
                                          item?.forecast_status !== 'assigned'
                                        ) {
                                          let budget = BudgetData;
                                          budget[index].toggleout =
                                            e.target.value;
                                          setBudgetData(budget);
                                          setRandom(Math.random());
                                        }
                                      }}
                                    >
                                      <input
                                        className={
                                          item?.toggleout === 'prior'
                                            ? 'active togglbuton'
                                            : 'togglbuton'
                                        }
                                        type="radio"
                                        name="toggleout"
                                        value={'prior'}
                                        checked={item?.toggleout === 'prior'}
                                      />
                                      <input
                                        className={
                                          item?.toggleout === 'distribute'
                                            ? 'active togglbuton'
                                            : 'togglbuton'
                                        }
                                        type="radio"
                                        name="toggleout"
                                        value={'distribute'}
                                        checked={
                                          item?.toggleout === 'distribute'
                                        }
                                      />
                                      {/* <input
                                className={
                                  item?.toggleout === 'industry'
                                    ? 'active togglbuton'
                                    : 'togglbuton'
                                }
                                type="radio"
                                name="toggleout"
                                value={'industry'}
                                checked={item?.toggleout === 'industry'}
                              /> */}
                                    </Box>
                                  </Box>
                                  <Typography className="sub-title-text fw600">
                                    {item?.toggleout === 'prior'
                                      ? 'Based of prior year data'
                                      : item?.toggleout === 'distribute'
                                        ? 'Distribute evenly across the year'
                                        : 'Pre-defined based on sector'}
                                  </Typography>
                                </Box>
                                <CustomButton
                                  variant="contained"
                                  title="Apply"
                                  className="apply-prior"
                                  disabled={
                                    !(
                                      item?.forecast_bugdet_data &&
                                      item?.forecast_bugdet_data?.columns_group
                                        ?.length > 1
                                    ) || // item?.forecast_locked ||
                                    !CheckIsPast(item?.forecast_year) ||
                                    (authState?.UserPermission?.forecast ===
                                      2 &&
                                      item?.forecast_status === 'assigned')
                                  }
                                  fullWidth={false}
                                  onClick={() => {
                                    OnChangeFilter(
                                      index,
                                      item?.toggleout,
                                      item?.forecast_bugdet_data
                                    );
                                  }}
                                />
                              </Box>
                            </Box>
                          )}
                      </Box>
                      {seprateBranch ? (
                        <Box className="budget-save-actions">
                          <Box className="d-flex budget-icons pb16">
                            {CheckIsPast(item?.forecast_year) &&
                            authState?.UserPermission?.forecast === 2 &&
                            item?.forecast_status === 'assigned' &&
                            !IsAccountant ? (
                              <>
                                <Tooltip
                                  arrow
                                  title={<Typography>Revoke Budget</Typography>}
                                  classes={{
                                    tooltip: 'info-tooltip-container ',
                                  }}
                                >
                                  <div className="action-icon assign-icons d-flex">
                                    <PersonRemoveIcon
                                      className="cursor-pointer"
                                      onClick={() => {
                                        setCreateModalDep2({
                                          item: item,
                                          type: 'revoke',
                                          index: index,
                                        });
                                      }}
                                    />
                                  </div>
                                </Tooltip>
                              </>
                            ) : (
                              CheckIsPast(item?.forecast_year) &&
                              authState?.UserPermission?.forecast === 2 &&
                              item?.forecast_status !== 'approved' &&
                              item?.forecast_status !== 'submitted' &&
                              !IsAccountant && (
                                <Tooltip
                                  arrow
                                  title={<Typography>Assign Budget</Typography>}
                                  classes={{
                                    tooltip: 'info-tooltip-container ',
                                  }}
                                >
                                  <div className="action-icon assign-icons d-flex">
                                    <PersonAddIcon
                                      className="cursor-pointer"
                                      onClick={() => {
                                        setCreateModalDep2({
                                          item: item,
                                          type: 'assign',
                                          index: index,
                                        });
                                      }}
                                    />
                                  </div>
                                </Tooltip>
                              )
                            )}
                            {authState?.UserPermission?.forecast === 2 &&
                              item?.forecast_status !== 'approved' &&
                              item?.forecast_status !== 'assigned' &&
                              item?.forecast_status !== 'submitted' &&
                              !IsAccountant && ( // &&
                                <Tooltip
                                  title={<Typography>Edit</Typography>}
                                  placement="bottom"
                                  arrow
                                  classes={{
                                    tooltip: 'info-tooltip-container ',
                                  }}
                                >
                                  {' '}
                                  <EditIcon
                                    onClick={() => {
                                      setCreateModalDep2({
                                        item: item,
                                        type: 'edit',
                                        index: index,
                                      });
                                    }}
                                  />
                                </Tooltip>
                              )}
                            {authState?.UserPermission?.forecast === 2 &&
                              !IsAccountant && (
                                <Tooltip
                                  title={<Typography>History</Typography>}
                                  placement="bottom"
                                  arrow
                                  classes={{
                                    tooltip: 'info-tooltip-container ',
                                  }}
                                >
                                  {' '}
                                  <Box
                                    className="action-icon d-flex history-icon"
                                    onClick={() =>
                                      router.push(
                                        `/budget-forecast/${item?.id}`
                                      )
                                    }
                                  >
                                    {' '}
                                    <HistoryIcon />
                                  </Box>
                                </Tooltip>
                              )}

                            {/* {!item?.forecast_locked ? (
                              <Tooltip
                                title={'UnLock'}
                                placement="bottom"
                                arrow
                              >
                                <Box>
                                  <UnLockIcon
                                    onClick={() =>
                                      !item?.has_active_status
                                        ? setCreateModalDep2({
                                            item: item,
                                            index: index,
                                            modal: 'Incomplete',
                                          })
                                        : LockForecast(item?.id, index, true)
                                    }
                                  />
                                </Box>
                              </Tooltip>
                            ) : (
                              <Tooltip
                                title={'Locked'}
                                placement="bottom"
                                arrow
                              >
                                <Box>
                                  <LockIcon
                                    onClick={() =>
                                      LockForecast(item?.id, index, false)
                                    }
                                  />
                                </Box>
                              </Tooltip>
                            )} */}
                            {authState?.UserPermission?.forecast === 2 &&
                              item?.forecast_status !== 'submitted' &&
                              item?.forecast_status !== 'approved' &&
                              !IsAccountant && (
                                <Tooltip
                                  title={<Typography>Delete</Typography>}
                                  placement="bottom"
                                  arrow
                                  classes={{
                                    tooltip: 'info-tooltip-container ',
                                  }}
                                >
                                  <Box>
                                    <DeleteIcon
                                      onClick={() => {
                                        setCreateModalDep2({
                                          item: item,
                                          type: 'delete',
                                          index: index,
                                        });
                                      }}
                                    />
                                  </Box>
                                </Tooltip>
                              )}
                            {(item?.forecast_status === 'approved' ||
                              item?.forecast_status === 'submitted') && (
                              <>
                                <Tooltip
                                  title={<Typography>Download</Typography>}
                                  placement="bottom"
                                  arrow
                                  classes={{
                                    tooltip: 'info-tooltip-container ',
                                  }}
                                >
                                  <Box>
                                    <DownloadIcon
                                      onClick={(e) => handleClick(e, item)}
                                    />
                                  </Box>
                                </Tooltip>
                                <Popover
                                  className="export-popover"
                                  id={id}
                                  open={open}
                                  anchorEl={anchorEl}
                                  onClose={handleClose}
                                  anchorOrigin={{
                                    vertical: 'bottom',
                                    horizontal: 'left',
                                  }}
                                >
                                  <Box className="export-option">
                                    <Typography
                                      className={
                                        Download?.forecast_bugdet_data &&
                                        Download?.forecast_bugdet_data
                                          ?.columns_group?.length > 1
                                          ? 'title-text fw600 pb8 cursor-pointer'
                                          : 'title-text fw600 pb8'
                                      }
                                      onClick={() =>
                                        Download?.forecast_bugdet_data &&
                                        Download?.forecast_bugdet_data
                                          ?.columns_group?.length > 1 &&
                                        getExportBudget(
                                          Download?.id,
                                          'csv',
                                          Download
                                        )
                                      }
                                    >
                                      {' '}
                                      CSV{' '}
                                    </Typography>
                                    <Typography
                                      className={
                                        Download?.forecast_bugdet_data &&
                                        Download?.forecast_bugdet_data
                                          ?.columns_group?.length > 1
                                          ? 'title-text fw600 pb8 cursor-pointer'
                                          : 'title-text fw600 pb8'
                                      }
                                      onClick={() =>
                                        Download?.forecast_bugdet_data &&
                                        Download?.forecast_bugdet_data
                                          ?.columns_group?.length > 1 &&
                                        getExportBudget(
                                          Download?.id,
                                          'excel',
                                          Download
                                        )
                                      }
                                    >
                                      {' '}
                                      Excel{' '}
                                    </Typography>
                                  </Box>
                                </Popover>
                              </>
                            )}
                          </Box>
                          <Box>
                            {CheckIsPast(item?.forecast_year) &&
                              !item?.forecast_locked &&
                              item?.forecast_status !== 'approved' &&
                              item?.forecast_status !== 'assigned' &&
                              !IsAccountant && (
                                <Box className="d-flex gap-sm align-center save-btns-icon">
                                  <CustomButton
                                    variant="outlined"
                                    className="save-btn"
                                    onClick={() =>
                                      SaveBudgetDetails(item, 'draft', index)
                                    }
                                    startIcon={<SaveAsIcon />}
                                    // disabled={item?.forecast_locked}
                                    disabled={
                                      !item?.forecast_bugdet_data ||
                                      item?.forecast_bugdet_data?.columns_group
                                        ?.length === 0 ||
                                      item?.forecast_status === 'assigned'
                                    }
                                    title={'Save as Draft'}
                                  />
                                  <CustomButton
                                    variant="contained"
                                    className="save-btn"
                                    onClick={() =>
                                      SaveBudgetDetails(item, 'approved', index)
                                    }
                                    startIcon={<SaveIcon />}
                                    disabled={
                                      !item?.forecast_bugdet_data ||
                                      item?.forecast_bugdet_data?.columns_group
                                        ?.length === 0 ||
                                      item?.forecast_status === 'assigned'
                                    }
                                    title={
                                      item?.forecast_status === 'submitted'
                                        ? 'Approve'
                                        : 'Save & Approve'
                                    }
                                  />
                                </Box>
                              )}
                          </Box>
                        </Box>
                      ) : (
                        <>
                          <Box className="budget-save-actions all-branch-actions">
                            {(item?.forecast_status === 'approved' ||
                              item?.forecast_status === 'submitted') &&
                            item?.forecast_bugdet_data &&
                            item?.forecast_bugdet_data?.columns_group?.length >
                              1 ? (
                              <>
                                <Box className="d-flex budget-icons pb16">
                                  <Tooltip
                                    title={<Typography>Download</Typography>}
                                    placement="bottom"
                                    arrow
                                    classes={{
                                      tooltip: 'info-tooltip-container ',
                                    }}
                                  >
                                    <Box>
                                      <DownloadIcon
                                        onClick={(e) => handleClick(e, item)}
                                      />
                                    </Box>
                                  </Tooltip>
                                  <Popover
                                    className="export-popover"
                                    id={id}
                                    open={open}
                                    anchorEl={anchorEl}
                                    onClose={handleClose}
                                    anchorOrigin={{
                                      vertical: 'bottom',
                                      horizontal: 'left',
                                    }}
                                  >
                                    <Box className="export-option">
                                      <Typography
                                        className={
                                          Download?.forecast_bugdet_data &&
                                          Download?.forecast_bugdet_data
                                            ?.columns_group?.length > 1
                                            ? 'title-text fw600 pb8 cursor-pointer'
                                            : 'title-text fw600 pb8'
                                        }
                                        onClick={() =>
                                          Download?.forecast_bugdet_data &&
                                          Download?.forecast_bugdet_data
                                            ?.columns_group?.length > 1 &&
                                          getExportBudget(
                                            Download?.id,
                                            'csv',
                                            Download
                                          )
                                        }
                                      >
                                        {' '}
                                        CSV{' '}
                                      </Typography>
                                      <Typography
                                        className={
                                          Download?.forecast_bugdet_data &&
                                          Download?.forecast_bugdet_data
                                            ?.columns_group?.length > 1
                                            ? 'title-text fw600 pb8 cursor-pointer'
                                            : 'title-text fw600 pb8'
                                        }
                                        onClick={() =>
                                          Download?.forecast_bugdet_data &&
                                          Download?.forecast_bugdet_data
                                            ?.columns_group?.length > 1 &&
                                          getExportBudget(
                                            Download?.id,
                                            'excel',
                                            Download
                                          )
                                        }
                                      >
                                        {' '}
                                        Excel{' '}
                                      </Typography>
                                    </Box>
                                  </Popover>
                                </Box>
                              </>
                            ) : (
                              <></>
                            )}
                          </Box>
                        </>
                      )}
                    </Box>

                    {seprateBranch && item?.forecast_status === 'submitted' && (
                      <Box className="pt8">
                        <Typography className="sub-title-text fw600 color-green">{`This year's forecast has been submitted by the Branch Manager/Hotel Manager.`}</Typography>
                      </Box>
                    )}
                    {item?.forecast_bugdet_data &&
                    item?.forecast_bugdet_data?.columns_group?.length === 0 &&
                    item?.forecast_status === 'assigned' &&
                    item?.ForecastAssignBudgets?.is_create_budget ? (
                      <>
                        <Typography className="title-text text-align color-gray pt32">
                          This year's forecast has been assigned to the Branch
                          Manager for creation.
                        </Typography>
                      </>
                    ) : item?.forecast_bugdet_data &&
                      item?.forecast_bugdet_data?.columns_group?.length > 1 ? (
                      <BudgetTable
                        BudgetData={item?.forecast_bugdet_data} //item?.BudgetTable}
                        setBudgetData={setBudgetData}
                        BudgetDataReal={BudgetData}
                        parentIndex={index}
                        budgetDetails={item}
                        setRandom={setRandom}
                        isFromHistory={
                          item?.forecast_status === 'assigned' ||
                          !seprateBranch ||
                          item?.forecast_status === 'approved' ||
                          IsAccountant
                        }
                      />
                    ) : (
                      <>
                        <Box
                          className="mt32"
                          // style={{
                          //   height: 'calc(100vh - 300px)',
                          //   overflow: 'auto',
                          // }}
                        >
                          <Box className="no-data d-flex align-center justify-center">
                            <NoDataView
                              title="No Budget Data Found"
                              description="There is no budget data available at the moment."
                            />
                          </Box>
                        </Box>
                      </>
                    )}
                  </Box>
                );
              })}
            </InfiniteScroll>
          </Box>
        </>
      )}
      <DialogBox
        open={createModalDep2}
        handleClose={() => {
          setCreateModalDep2(false);
        }}
        title={'Confirmation'}
        className="confirmation-modal"
        dividerClass="confirmation-modal-divider"
        content={
          <>
            <ConfirmationModal
              handleCancel={() => {
                setCreateModalDep2(false);
              }}
              handleConfirm={() => {
                if (createModalDep2?.type === 'edit') {
                  getBudgetByID(
                    createModalDep2?.item?.id,
                    createModalDep2?.type,
                    createModalDep2?.index
                  );
                } else if (createModalDep2?.type === 'assign') {
                  getBudgetByID(
                    createModalDep2?.item?.id,
                    createModalDep2?.type,
                    createModalDep2?.index
                  );
                } else if (createModalDep2?.type === 'delete') {
                  DeleteForecast(
                    createModalDep2?.item?.id,
                    createModalDep2?.index,
                    'deleted'
                  );
                } else if (createModalDep2?.type === 'revoke') {
                  DeleteForecast(
                    createModalDep2?.item?.id,
                    createModalDep2?.index,
                    'revoked',
                    createModalDep2?.item
                  );
                } else {
                  SaveBudgetDetails(
                    createModalDep2?.item,
                    createModalDep2?.type
                  );
                }
                setCreateModalDep2(false);
              }}
              text={
                createModalDep2?.type === 'revoke'
                  ? 'Would you like to revoke your budget to Branch Manager/Hotel Manager?'
                  : createModalDep2?.type === 'assign'
                    ? 'Would you like to assign your budget to Branch Manager/Hotel Manager?'
                    : createModalDep2?.type === 'edit'
                      ? 'Would you like to change your budget?'
                      : createModalDep2?.type === 'delete'
                        ? 'Are you sure you want to delete the budget?'
                        : createModalDep2?.modal === 'Incomplete'
                          ? 'The budget forecast details are incomplete. Are you sure you want to lock? If you proceed, the lock will still be applied.'
                          : ''
              }
            />
          </>
        }
      />
    </Box>
  );
}
