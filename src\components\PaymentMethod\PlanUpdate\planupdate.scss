@import '@/styles/variable.scss';

.create-plan-wrap {
  .create-sub-plan-form {
    .error-label {
      color: $error;
    }

    .address-wrap.input-error {
      color: $error !important;
    }

    .create-plan-btn-wrap {
      .create-plan-btn {
        padding: 7px 24px !important;

        &:hover {
          color: $color-secondary !important;
          box-shadow: none !important;
        }
      }
    }

    .employee-input-wrap {
      gap: 10px;

      .employee-fields-row {
        gap: 10px;
      }

      .employee-text {
        padding-bottom: 20px;
      }

      .employee-lable-wrap {
        font-size: 14px !important;
      }

      @media (max-width: 768px) {
        .employee-fields-row {
          column-gap: 8px;
        }
      }
    }

    .display-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      column-gap: 20px;
      row-gap: 20px;

      @media (max-width: 767px) {
        grid-template-columns: repeat(1, 1fr);
      }

      // @media(max-width:575px) {
      //     grid-template-columns: repeat(1, 1fr);
      // }
    }

    .sub-plan-input-wrap {
      .MuiInputBase-root {
        min-height: 38px;

        .MuiInputBase-input {
          padding: 7.5px 16px;
        }
      }

      .phone-county-wrap {
        .country-select-adornment {
          margin-right: 0px;
        }
      }
    }

    .slected-wrap {
      .MuiInputBase-root {
        min-height: 39px !important;

        .placeholder {
          margin-top: 3px;
          font-family: Inter, sans-serif;
          font-size: 16px;
          font-weight: 400 !important;
        }

        .MuiInputBase-input {
          padding: 7px 14px !important;
          margin-top: 0 !important;
        }

        fieldset {
          height: 40px !important;
          margin-top: 5px !important;
        }

        .MuiSelect-select {
          font-size: 16px;
        }
      }
    }
    .slected-wrap.error-border {
      border: 1px solid $error !important;
      border-radius: 8px !important;
    }
    .date-picker-textfield {
      .MuiInputBase-input {
        padding: 4px 14px;
      }
      fieldset {
        height: 39px;
      }
    }
  }

  @media (max-width: 1200px) {
    width: 80%;
  }

  @media (max-width: 992px) {
    width: 90%;
  }

  @media (max-width: 899px) {
    width: 100%;
  }

  @media (max-width: 575px) {
    padding: 20px 12px 56px;
  }
}
