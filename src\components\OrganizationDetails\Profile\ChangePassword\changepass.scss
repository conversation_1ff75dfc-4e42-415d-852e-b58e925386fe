@import '@/styles/variable.scss';

.right-password-sec {
  width: 30%;
  border-left: var(--normal-sec-border);
  margin: var(--spacing-base) var(--spacing-none) var(--spacing-base)
    var(--spacing-none);
  padding: var(--spacing-none) var(--spacing-base) var(--spacing-none)
    var(--spacing-base);
  border: var(--normal-dashed-sec-border);
  border-radius: var(--border-radius-lg);
  .change-pass-wrap,
  .close-acc-wrap {
    .text-wrap {
      width: 100% !important;
      color: var(--text-color-slate-gray);
      font-size: var(--font-size-sm);
    }
    .change-pass-title {
      font-family: var(--font-family-primary);
      color: var(--text-color-black);
      font-size: var(--font-size-base);
      font-weight: var(--font-weight-semibold);
      margin-top: var(--spacing-lg);
    }
    .change-pass {
      .MuiInputBase-root {
        padding-right: var(--spacing-none);
      }
      .validation-text {
        font-family: var(--font-family-primary);
        font-size: var(--font-size-xs);
        font-weight: var(--font-weight-regula);
        line-height: var(--line-height-xs);
      }
    }
    .change-btn-wrap,
    .close-btn-wrap {
      padding-top: 15px;

      .change-btn,
      .close-btn {
        padding: 7px 24px !important;
        font-size: 15px !important;

        &:hover {
          box-shadow: none !important;
        }
      }

      .change-btn {
        &:hover {
          color: $color-secondary !important;
        }
      }

      .close-btn {
        background: $color-secondary !important;
        color: $color-primary !important;
      }
    }
  }
  .change-pass-wrap {
    flex: 1;
    padding-bottom: var(--spacing-lg);
    // border-bottom: var(--field-border-primary);
  }
  .close-acc-wrap {
    flex: 1;
    @media (max-width: 1200px) {
      max-height: 311px;
    }
    @media (max-width: 767px) {
      min-height: 200px;
    }
    @media (max-width: 575px) {
      .change-pass {
        font-size: 18px;
      }
    }
  }
  @media (max-width: 1200px) {
    width: 100%;
    flex-direction: row;
    gap: 20px;
    border: none;
    border-top: var(--normal-sec-border);
    border-radius: 0px;
    margin-top: 0px;
    padding: var(--spacing-none) var(--spacing-lg) var(--spacing-none)
      var(--spacing-lg);
  }
  @media (max-width: 575px) {
    flex-direction: column;
    padding: var(--spacing-none) var(--spacing-none) var(--spacing-none)
      var(--spacing-none);
  }
}
.close-aacount-dialog {
  .input-field-wrap {
    .MuiInputBase-root {
      min-height: 38px;
      .MuiInputBase-input {
        padding: 7.5px 16px;
      }
    }
    .phone-county-wrap {
      .country-select-adornment {
        margin-right: 0px;
      }
    }
  }
}
