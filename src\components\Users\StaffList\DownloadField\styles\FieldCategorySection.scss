@import '@/app/_globals.scss';

// Field Category Section Component Styles
.field-category-section {
  background: var(--color-off-white);
  border-radius: var(--border-radius-md);
  border: var(--normal-sec-border);
  overflow: hidden;

  // Category Header
  .category-header {
    padding: var(--spacing-lg);
    border-bottom: var(--normal-sec-border);

    .header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .category-toggle {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        flex: 1;
        text-align: left;
        background: transparent;
        border: none;
        cursor: pointer;
        transition: color 0.15s ease;

        &:hover {
          color: var(--color-primary);
        }

        &:focus {
          outline: none;
          border: 0;
        }

        .category-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: var(--spacing-2xl);
          height: var(--spacing-2xl);
          background: var(--color-secondary);
          border-radius: var(--border-radius-md);

          .icon {
            width: var(--icon-size-xs);
            height: var(--icon-size-xs);
            color: var(--color-primary);
            stroke-width: 2;
          }
        }

        .category-info {
          flex: 1;

          .category-title {
            margin: 0;
          }

          .category-description {
            color: var(--text-light-dark);
            margin: 0;
          }
        }

        .expand-icon {
          width: var(--icon-size-sm);
          height: var(--icon-size-sm);
          color: var(--text-slate-gray);
          stroke-width: 2;
          transition: transform 0.2s ease;

          &.expanded {
            transform: rotate(180deg);
          }
        }
      }

      .category-actions {
        margin-left: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .select-all-button {
          display: flex;
          align-items: center;
          border: none;
          cursor: pointer;
          transition: all 0.15s ease;

          // All selected state
          &.all-selected {
            background: var(--color-primary);
            color: var(--color-white);

            &:hover {
              background: var(--color-green);
            }
          }

          // Some selected state
          &.some-selected {
            background: var(--color-secondary);
            color: var(--color-primary);

            &:hover {
              background: var(--color-primary);
              color: var(--color-white);
            }
          }

          // None selected state
          &.none-selected {
            background: var(--color-secondary);
            color: var(--text-light-dark);

            &:hover {
              background: var(--color-light-gray);
            }
          }
        }

        .selection-count {
          color: var(--text-light-dark);
        }
      }
    }
  }

  // Category Fields
  .category-fields {
    padding: var(--spacing-lg);

    .fields-container {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-md);

      .field-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: var(--spacing-md);
        border-radius: var(--border-radius-md);
        border: var(--normal-sec-border);
        transition: all 0.15s ease;

        &.selected {
          border-color: var(--color-primary);
          background: rgba(19, 94, 150, 0.05);
        }

        &:not(.selected) {
          &:hover {
            border-color: var(--border-color-light-gray);
            background: rgba(19, 94, 150, 0.02);
          }
        }

        .field-content {
          display: flex;
          align-items: center;
          gap: 0.75rem;

          .field-checkbox {
            width: var(--icon-size-sm);
            height: var(--icon-size-sm);
            border-radius: var(--border-radius-xs);
            border: 2px solid var(--border-color-light-gray);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.15s ease;

            &:focus {
              outline: none;
              box-shadow:
                0 0 0 2px var(--color-primary),
                0 0 0 4px rgba(19, 94, 150, 0.1);
            }

            &.checked {
              border-color: var(--color-primary);
              background: var(--color-primary);

              .check-icon {
                width: var(--spacing-md);
                height: var(--spacing-md);
                color: var(--color-white);
                stroke-width: 3;
              }
            }

            &:not(.checked) {
              &:hover {
                border-color: var(--color-primary);
              }
            }

            &.disabled {
              cursor: not-allowed;
              opacity: 0.75;
            }
          }

          .field-info {
            flex: 1;

            .field-header {
              display: flex;
              align-items: center;
              gap: 0.5rem;

              .field-name {
                font-weight: var(--font-weight-medium);
                margin: 0;

                &.selected {
                  color: var(--color-primary);
                }

                &:not(.selected) {
                  color: var(--text-color-black);
                }
              }

              .required-badge {
                font-size: var(--font-size-xs);
                background: var(--color-danger-background);
                color: var(--color-danger);
                padding: var(--spacing-xxs) var(--spacing-xsm);
                border-radius: var(--border-radius-xs);
              }
            }

            .field-type {
              font-size: var(--font-size-sm);
              color: var(--text-light-dark);
              margin: 0;
              text-transform: capitalize;
            }
          }
        }

        .field-status {
          display: flex;
          align-items: center;
          gap: 0.5rem;

          .order-badge {
            color: var(--color-primary);
            background: var(--color-secondary);
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--border-radius-xs);
            min-width: var(--spacing-2xl);
            text-align: center;
          }

          .check-icon {
            width: var(--icon-size-xs);
            height: var(--icon-size-xs);
            color: var(--color-primary);
            stroke-width: 2;
          }
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .field-category-section {
    .category-header {
      .header-content {
        .category-actions {
          flex-direction: column;
          align-items: flex-end;
          gap: 0.25rem;
        }
      }
    }

    .category-fields {
      .fields-container {
        .field-item {
          .field-content {
            .field-info {
              .field-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.25rem;
              }
            }
          }
        }
      }
    }
  }
}
