import React, { useState, useEffect } from 'react';
import { Box, Typography } from '@mui/material';
import Gauge<PERSON><PERSON> from 'react-gauge-chart';
import ReactSpeedometer from 'react-d3-speedometer';
import './radialgraph.scss';

const RadialChart = ({ item }) => {
  const [options, setOptions] = useState();
  const [screenWidth, setScreenWidth] = useState(null);
  useEffect(() => {
    if (item) {
      setOptions(item);
    }
  }, [item]);
  const maxValue =
    parseFloat(options?.value) > parseFloat(options?.scale?.max)
      ? Math.ceil(parseFloat(options?.value))
      : parseFloat(options?.scale?.max)
        ? Math.ceil(parseFloat(options?.scale?.max))
        : 100;

  const minValue = parseFloat(options?.scale?.min);
  const value = parseInt(options?.value);
  const segmentStep = Math.round((maxValue - minValue) / 3);
  const segmentStops = [
    minValue,
    minValue + segmentStep,
    minValue + segmentStep * 2,
    maxValue,
  ];
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const handleResize = () => {
        setScreenWidth(window.innerWidth);
      };

      window.addEventListener('resize', handleResize);
      handleResize();
      return () => {
        window.removeEventListener('resize', handleResize);
      };
    }
  }, [screenWidth]);
  const MeterGraph = (width) => {
    return (
      <ReactSpeedometer
        maxValue={maxValue}
        minValue={minValue}
        value={value} // Ensure value is an integer
        height={250}
        width={width > 500 ? 330 : width > 450 ? 300 : width > 400 ? 270 : 200}
        needleTransition="easeQuadIn"
        needleTransitionDuration={1000}
        needleColor="#555555"
        customSegmentStops={segmentStops}
        segmentColors={['#F27476', '#F9BF61', '#64D273']} // Define segment colors
        ringWidth={25} // Control the thickness of the gauge arc
      />
    );
  };
  useEffect(() => {
    MeterGraph(screenWidth);
  }, [screenWidth, options?.scale?.max, options?.scale?.min]);
  return (
    <>
      <Box className="radial-graphs-section text-align">
        {/* <GaugeChart
          id={'dial1'}
          style={{
            width: 390,
            height: 300
          }}
          nrOfLevels={30}
          colors={['red', 'yellow', 'green']}
          arcWidth={0.5}
          percent={10}
          textColor={'#FFFFFF'}
          formatTextValue={(value) => value}
        /> */}

        {options?.title?.text && (
          <Typography className="p16 mb16 text-align mt8 graphs-title">
            {options?.title?.text}
          </Typography>
        )}
        {options ? <>{MeterGraph(screenWidth)}</> : <></>}
        {parseFloat(options?.value) > parseFloat(options?.scale?.max) && (
          <Typography className="p14 mb16 fw600 text-align color-green">
            Your Budget total exceeds your target.
          </Typography>
        )}
      </Box>
    </>
  );
};

export default RadialChart;
