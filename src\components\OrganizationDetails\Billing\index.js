import React, { useEffect, useState } from 'react';
import { Box, CircularProgress } from '@mui/material';
import PricingCards from '@/components/PaymentMethod/PlanCards';
import AddCard from '@/components/PaymentMethod/AddCards';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { ORG_URLS } from '@/helper/constants/urls';
import { identifiers } from '@/helper/constants/identifier';
import './billing.scss';

export default function Billing({ setTabVal }) {
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [planData, setPlanData] = useState([]);
  const [paymentProviderOptions, setPaymentProviderOptions] = useState([]);
  const [providerID, setProviderID] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  const getSubPlanDetails = async () => {
    try {
      const { status, data } = await axiosInstance.get(
        ORG_URLS.GET_ALL_SUBSCRIPTION_PLAN
      );
      if (status === 200) {
        setPlanData(data?.data || []);
        setIsLoading(false);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setIsLoading(false);
    }
  };

  const getPaymetProviderDetails = async () => {
    try {
      const { status, data } = await axiosInstance.get(
        ORG_URLS?.GET_ALL_PAYMENT_PROVIDER
      );
      if (status === 200) {
        const options = data?.data?.map((provider) => ({
          ...provider,
          label: provider.provider_name,
          value: provider.id,
        }));
        setPaymentProviderOptions(options);
        setProviderID(
          options?.find(
            (item) => item?.provider_identifier === identifiers?.PYMENT_PROVIDER
          )?.id
        );
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const PurchasedPlan = async (Plan) => {
    const sendData = {
      card_id: Plan?.selectedCard,
      plan_id: Plan?.plan,
      provider_id: providerID,
    };

    const isUpdatePlan =
      !!planData?.find(
        (item) =>
          item?.purchased_plan &&
          item?.subs_is_free_trial === 0 &&
          selectedPlan?.subs_plan_type !== 'one_time' &&
          item?.subs_plan_category === selectedPlan?.subs_plan_category
      ) || false;

    // const isRepeated = selectedPlan?.subs_plan_type !== 'one_time';

    if (isUpdatePlan) {
      try {
        const { status, data } = await axiosInstance.put(
          ORG_URLS?.UPDATE_PLAN,
          sendData
        );

        if (status === 200) {
          if (data?.status) {
            setApiMessage('success', data?.message);
            getSubPlanDetails();
            setTabVal(1);
          } else {
            setApiMessage('error', data?.message);
          }
        }
      } catch (error) {
        setApiMessage('error', error?.response?.data?.message);
      }
    } else {
      try {
        const { status, data } = await axiosInstance.post(
          ORG_URLS?.PURCHASE_PLAN,
          sendData
        );

        if (status === 200) {
          if (data?.status) {
            setApiMessage('success', data?.message);
            getSubPlanDetails();
            setTabVal(1);
          } else {
            setApiMessage('error', data?.message);
          }
        }
      } catch (error) {
        setApiMessage('error', error?.response?.data?.message);
      }
    }
  };
  // Fetch plan details when the page or rows per page changes.
  useEffect(() => {
    getSubPlanDetails();
    getPaymetProviderDetails();
  }, []);
  return (
    <Box className="plans-wrap d-flex gap-20">
      <Box className="choose-plan-wrap">
        {isLoading ? (
          <Box className="content-loader">
            <CircularProgress className="loader" color="inherit" />
          </Box>
        ) : (
          <PricingCards
            subscriptions={planData}
            selectedPlan={selectedPlan}
            setSelectedPlan={setSelectedPlan}
          />
        )}
      </Box>
      <Box className="plan-payment-wrap">
        <AddCard
          paymentProvider={paymentProviderOptions}
          selectedPlan={selectedPlan}
          PurchasedPlan={PurchasedPlan}
          plansList={planData}
        />
      </Box>
    </Box>
  );
}
