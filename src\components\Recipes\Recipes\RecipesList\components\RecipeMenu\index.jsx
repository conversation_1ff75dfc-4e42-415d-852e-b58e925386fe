import React, { useState, useRef, useEffect } from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import './recipemenu.scss';

const RecipeMenu = ({
  recipe,
  onDuplicate,
  onDelete,
  onBookmark,
  onExport,
  // position = 'right',
  onEdit,
  onView,
  onViewHistory,
  onViewActivity,
  onAssign,
  isAllPermission,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  // const [calculatedPosition, setCalculatedPosition] = useState(position);
  const menuRef = useRef(null);
  const triggerRef = useRef(null);

  const menuItems = [
    {
      id: 1,
      label: 'Edit Recipe',
      icon: 'Edit3',
      action: () => onEdit?.(recipe),
    },
    {
      id: 2,
      label: 'View Details',
      icon: 'Eye',
      action: () => onView?.(recipe),
    },
    {
      id: 3,
      label: recipe?.is_bookmarked ? 'Remove Bookmark' : 'Add Bookmark',
      icon: recipe?.is_bookmarked ? 'BookmarkMinus' : 'BookmarkPlus',
      action: () => onBookmark?.(recipe),
    },
    {
      id: 4,
      label: 'Duplicate Recipe',
      icon: 'Copy',
      action: () => onDuplicate?.(recipe),
    },
    {
      id: 5,
      label: 'View History',
      icon: 'History',
      action: () => onViewHistory?.(recipe),
    },
    {
      id: 9,
      label: 'View Activity',
      icon: 'Activity',
      action: () => onViewActivity?.(recipe),
    },
    {
      id: 6,
      label: 'Assign Recipe',
      icon: 'UserPlus',
      action: () => onAssign?.(recipe),
    },
    {
      id: 7,
      label: 'Export Recipe',
      icon: 'Download',
      action: () => onExport?.(recipe),
    },
    {
      id: 8,
      label: 'Delete Recipe',
      icon: 'Trash2',
      action: () => onDelete?.(recipe),
      variant: 'danger',
    },
  ]?.filter((item) =>
    isAllPermission
      ? recipe?.recipe_status !== 'publish'
        ? item.id !== 2 && item.id !== 6
        : true
      : item.id !== 1 &&
        item.id !== 4 &&
        item.id !== 5 &&
        item.id !== 9 &&
        item.id !== 6 &&
        item.id !== 8
  );

  // const calculatePosition = () => {
  //   if (!triggerRef.current) return position;

  //   const triggerRect = triggerRef.current.getBoundingClientRect();
  //   const viewportWidth = window.innerWidth;
  //   const viewportHeight = window.innerHeight;
  //   const menuWidth = 180; // Width from SCSS
  //   const menuHeight = menuItems.length * 40; // Approximate height per item

  //   let newPosition = position;

  //   // Check horizontal positioning
  //   if (position === 'right') {
  //     if (triggerRect.right + menuWidth > viewportWidth) {
  //       newPosition = 'left';
  //     }
  //   } else if (position === 'left') {
  //     if (triggerRect.left - menuWidth < 0) {
  //       newPosition = 'right';
  //     }
  //   }

  //   // Check vertical positioning
  //   if (triggerRect.bottom + menuHeight > viewportHeight) {
  //     // If menu would go below viewport, position it above the trigger
  //     newPosition = newPosition === 'right' ? 'right-top' : 'left-top';
  //   }

  //   return newPosition;
  // };

  const handleMenuToggle = (e) => {
    e.stopPropagation();
    // if (!isOpen) {
    //   const newPosition = calculatePosition();
    //   setCalculatedPosition(newPosition);
    // }
    setIsOpen(!isOpen);
  };

  const handleMenuItemClick = (action) => {
    action();
    setIsOpen(false);
  };

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef?.current && !menuRef.current.contains(event?.target)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  return (
    <div className="recipe-menu" ref={menuRef}>
      <button
        ref={triggerRef}
        onClick={handleMenuToggle}
        className="recipe-menu__trigger"
        aria-label="Recipe options"
      >
        <Icon name="MoreVertical" size={16} />
      </button>

      {isOpen && (
        <div
          className={`recipe-menu__dropdown recipe-menu__dropdown--right-top`}
        >
          {/* ${calculatedPosition} */}
          {menuItems.map((item, index) => (
            <button
              key={index}
              onClick={(e) => {
                e.stopPropagation();
                handleMenuItemClick(item.action);
              }}
              className={`recipe-menu__item ${
                item.variant === 'danger' ? 'recipe-menu__item--danger' : ''
              }`}
            >
              <Icon name={item.icon} size={16} />
              <span>{item.label}</span>
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default RecipeMenu;
