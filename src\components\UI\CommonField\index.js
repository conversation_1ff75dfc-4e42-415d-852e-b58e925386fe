import { TextField, styled } from '@mui/material';

const CustomTextField = styled(TextField)(({ theme }) => ({
  '& .MuiInputBase-root': {
    border: '1px solid #d9dae2',
    minHeight: '45px ',
    borderRadius: '8px',
    background: '#fff',
    '&:before': {
      border: '0',
      display: 'none',
    },
    '&:after': {
      border: '0',
    },
    '&:hover': {
      background: '#fff',
    },
  },
  '& .Mui-error': {
    color: '#d32f2f !important',
    fontFamily: 'Inter, sans-serif !important',
    fontSize: '12px !important',
    lineHeight: '18px !important',
    letterSpacing: '-0.5px !important',
    fontWeight: '600 !important',
    '& .MuiFilledInput-input': {
      border: '1px solid #d32f2f !important',
    },
  },
  '& .MuiOutlinedInput-input': {
    padding: '0',
  },
  '& .MuiOutlinedInput-notchedOutlin': {
    display: 'none !important',
  },
  '& legend': {
    display: 'none !important',
  },

  '& .MuiInputBase-input': {
    fontSize: '16px',
    fontWeight: '400',
    lineHeight: '24px',
    color: '#33343a !important',
    padding: '10px 16px;',
    borderRadius: '8px',
    fontFamily: 'Inter, sans-serif',
  },
  '& ::placeholder': {
    color: '#A6A8B1',
    opacity: 1,
    fontWeight: 400,
    textTransform: 'capitalize',
    fontSize: '15px',
    fontFamily: 'Inter, sans-serif',
    lineHeight: '18px',
  },
  '& .MuiInputBase-root.Mui-focused': {
    border: '1px solid #39596e',
  },
  '& .MuiInputLabel-root': {
    fontSize: '12px',
    color: '#33343a',
    fontFamily: 'Inter, sans-serif',
    lineHeight: '18px',
    marginBottom: '2px',
    fontWeight: '600',
    top: '-20px',
    left: '2px  ',
    transform: 'inherit',
    letterSpacing: '-0.5px',
    textTransform: 'capitalize',
  },
  '& .MuiInputLabel-root.Mui-focused': {
    color: '#39596e',
  },
  '& .MuiFormLabel-root.Mui-disabled': {
    background: 'transparent !important',
    color: '#000000 !important',
  },
  '& .Mui-disabled': {
    cursor: 'not-allowed',
    background: '#FFFFFF !important',
    color: '#000000 !important',
    opacity: 0.9,
    WebkitTextFillColor: '#000000 !important',
  },
}));

export { CustomTextField };
