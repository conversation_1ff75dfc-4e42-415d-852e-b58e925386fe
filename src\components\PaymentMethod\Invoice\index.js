import React from 'react';
import {
  <PERSON>,
  But<PERSON>,
  Chip,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material';
import { Download, Close } from '@mui/icons-material';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import './invoice.scss';
import CustomButton from '@/components/UI/button';
import TextSnippetOutlinedIcon from '@mui/icons-material/TextSnippetOutlined';

export default function Invoice() {
  const invoiceData = {
    invoiceNumber: '1019',
    paidDate: 'August 15, 2023',
    issueDate: 'July 31, 2023',
    dueDate: 'August 20, 2023',
    fromName: '<PERSON><PERSON>',
    fromAddress: '2973 Westheimer Rd. Santa Ana, Illinois 85486',
    toName: '<PERSON>',
    toAddress: '2715 Thornridge Cir. Syracuse, Connecticut 35624',
    planName: 'Basic Plan',
    amount: 9.99,
    label: 'paid',
  };

  return (
    <Box className="invoice-container">
      <Box className="invoice-wrap">
        {/* Invoice Header */}
        <Box className="invoice-header-wrap">
          <Box className="d-flex align-center justify-space-between invoice-header">
            <Typography variant="h6">
              Invoice #{invoiceData.invoiceNumber}
            </Typography>
            <Chip
              label={invoiceData.label}
              color="success"
              size="small"
              icon={<CheckCircleOutlineIcon />}
            />
          </Box>
          <Box className="paid-on-wrap d-flex align-center">
            <CheckCircleOutlineIcon fontSize="small" className="check-icon" />
            <Typography component="p" className="paid-on">
              Invoice Paid on {invoiceData.paidDate}
            </Typography>
          </Box>
        </Box>

        <Box className="invoice-middle-sec-wrap">
          <Box className="date-wrap d-flex align-center">
            <Box className="date">
              <Typography component="p" className="issued-on text-wrap ">
                Issued On
              </Typography>
              <Typography component="p" className="issue-date">
                {invoiceData.issueDate}
              </Typography>
            </Box>
            <Box className="date">
              <Typography component="p" className="due-on text-wrap">
                Due On
              </Typography>
              <Typography component="p" className="due-date">
                {invoiceData.dueDate}
              </Typography>
            </Box>
          </Box>

          <Box className="from-to-wrap d-flex align-center">
            <Box className="from-wrap">
              <Typography component="p" className="from-text text-wrap">
                From
              </Typography>
              <Typography component="p" className="from-name">
                {invoiceData.fromName}
              </Typography>
              <Typography component="p" className="from-adress">
                {invoiceData.fromAddress}
              </Typography>
            </Box>
            <Box className="from-wrap">
              <Typography component="p" className="from-to-text text-wrap">
                To
              </Typography>
              <Typography component="p" className="from-two-name">
                {invoiceData.toName}
              </Typography>
              <Typography component="p" className="from-two-adress">
                {invoiceData.toAddress}
              </Typography>
            </Box>
          </Box>

          <Box className="plan-wrap">
            <Typography component="p" className="plan-text text-wrap">
              Plan
            </Typography>
            <Typography className="plan-name-wrap">
              {invoiceData.planName} - ${invoiceData.amount}
            </Typography>
          </Box>
          <Divider />

          <Box className="table-wrap">
            <Typography component="p" className="item-text-wrap">
              Items
            </Typography>
            <Box className="invoice-table-wrap">
              <Table className="invoice-table">
                <TableHead>
                  <TableRow>
                    <TableCell>Description</TableCell>
                    <TableCell>Amount</TableCell>
                    <TableCell>Qty</TableCell>
                    <TableCell className="total-amount" align="right">
                      Total Amount
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody className="table-body">
                  <TableRow>
                    <TableCell>{invoiceData.planName}</TableCell>
                    <TableCell>${invoiceData.amount}</TableCell>
                    <TableCell>1</TableCell>
                    <TableCell align="right">${invoiceData.amount}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell colSpan={2}>Total</TableCell>
                    <TableCell colSpan={2} align="right">
                      ${invoiceData.amount}
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </Box>
          </Box>
        </Box>
      </Box>

      <Divider />
      <Box className="d-flex align-center invoice-btn-wrap">
        <CustomButton
          fullWidth
          className="p16 download-rec-btn"
          fontWeight="600"
          variant="contained"
          background="#39596e"
          backgroundhover="#39596e"
          colorhover="#FFFFFF"
          title="Download Reciept"
          startIcon={<TextSnippetOutlinedIcon />}
          // onClick={() => console.log('Button clicked')}
        />{' '}
        <CustomButton
          fullWidth
          className="p16 mark-btn"
          fontWeight="600"
          variant="contained"
          background="#39596e"
          backgroundhover="#39596e"
          colorhover="#FFFFFF"
          startIcon={<Close />}
          title="Mark as Unpaid"
          // onClick={() => console.log('Button clicked')}
        />
      </Box>
    </Box>
  );
}
