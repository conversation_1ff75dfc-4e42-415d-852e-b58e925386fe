import React from 'react';
import { Box, Typography } from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { styled } from '@mui/material/styles';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import './datepicker.scss';

const StyledDatePicker = styled(DatePicker)(() => ({
  width: '100%',
  '& .MuiOutlinedInput-root': {
    backgroundColor: 'var(--color-secondary)',
    color: 'var(--text-color-black)',
    borderRadius: 'var(--field-radius)',
    fontFamily: 'var(--font-family-primary)',
    '& .MuiOutlinedInput-input': {
      padding: 'var(--field-padding)',
      fontSize: 'var(--font-size-sm)',
      lineHeight: 'var(--line-height-xs)',
      height: 'auto',
    },
    '&.Mui-focused fieldset': {
      border: 'var(--field-border-primary)',
    },
    '&:hover fieldset': {
      border: 'var(--field-border-primary)',
    },
    '& .MuiInputAdornment-root': {
      color: 'var(--color-primary)',
      marginRight: '0px',

      '& .MuiSelect-select': {
        border: 'none',
      },
      '& fieldset': {
        border: 'none',
      },
      '&:hover fieldset': {
        border: 'none',
      },
      '& .MuiSvgIcon-root': {
        fontSize: 'var(--field-icon-size)',
        fill: 'var(--icon-color-primary)',
      },
    },
  },
  '& ::placeholder': {
    fontFamily: 'var(--font-family-primary)',
    color: 'var(--field-placeholder)',
  },
  '& .MuiOutlinedInput-notchedOutline': {
    border: 'var(--field-border)',
  },
  '& .Mui-disabled': {
    cursor: 'not-allowed',
    color: 'var(--text-color-muted)',
  },
}));

const CustomDatePicker = ({
  name,
  label,
  required,
  helperText,
  error,
  value,
  onChange,
  placeholder,
  fullWidth,
  format = 'DD/MM/YYYY',
  ...props
}) => {
  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box className={`custom-datepicker-wrapper ${error ? 'Mui-error' : ''}`}>
        {label && (
          <Typography
            className={`field-label ${error ? 'error-label' : ''}`}
            variant="body2"
            id={name}
          >
            {label}
            {required && <span className="required">*</span>}
          </Typography>
        )}
        <StyledDatePicker
          value={value}
          onChange={onChange}
          slotProps={{
            textField: {
              name: name,
              error: error,
              placeholder: placeholder,
              onBlur: props.onBlur,
              fullWidth: fullWidth,
            },
          }}
          format={format}
          {...props}
        />
        {error && helperText && (
          <Typography className="date-field-error-text">
            {helperText}
          </Typography>
        )}
      </Box>
    </LocalizationProvider>
  );
};

export default CustomDatePicker;
