import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Typography,
  FormGroup,
  FormControlLabel,
  Checkbox,
} from '@mui/material';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import LibraryMusicIcon from '@mui/icons-material/LibraryMusic';
import CustomButton from '@/components/UI/CustomButton';
import PlayCircleIcon from '@mui/icons-material/PlayCircle';
import { YoutubeIcon } from '@/helper/common/images';
import Slider from 'react-slick';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import HeaderImage from '@/components/UI/ImageSecurity';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import './folder.scss';

const FileDetails = ({
  CategoryDetails,
  DocumentList,
  getDocumentCategory,
  // CatId,
  currentHSIndex,
  setCurrentHSIndex,
  userMediaTrack,
  filesId,
}) => {
  const sliderRef = useRef(null);
  const sliderRef2 = useRef(null);
  const [checked, setChecked] = useState(false);

  const [MediaDoc, setMediaDoc] = useState();
  // const [setSelectedCat] = useState();

  const sliderWidth =
    MediaDoc &&
    MediaDoc?.categoryItemDetails &&
    MediaDoc?.categoryItemDetails?.length
      ? MediaDoc &&
        MediaDoc?.categoryItemDetails &&
        MediaDoc?.categoryItemDetails?.length * 2 * 64
      : 64;
  const settings = {
    dots: false,
    infinite: true,
    speed: 1000,
    autoplay: false,
    slidesToShow: 1,
  };
  const settingsView = {
    dots: false,
    infinite: true,
    speed: 1000,
    autoplay: false,
    slidesToShow:
      MediaDoc &&
      MediaDoc?.categoryItemDetails &&
      MediaDoc?.categoryItemDetails?.length
        ? MediaDoc?.categoryItemDetails?.length
        : 1,
  };
  const resetSlider = () => {
    if (sliderRef.current) {
      sliderRef.current.slickGoTo(0); // Go to the first slide
    }
    if (sliderRef2.current) {
      sliderRef2.current.slickGoTo(0); // Go to the first slide
    }
  };
  const ChangeSlider = (index) => {
    if (sliderRef.current) {
      sliderRef.current.slickGoTo(index); // Go to the first slide
    }
  };
  const getYouTubeVideoId = (url) => {
    const urlObj = new URL(url);
    if (urlObj.hostname === 'youtu.be') {
      return urlObj.pathname.substring(1);
    }
    return urlObj.searchParams.get('v');
  };

  const handlePrevClick = () => {
    if (currentHSIndex > 0) {
      resetSlider();
      setCurrentHSIndex(currentHSIndex - 1);
      getDocumentCategory(filesId[currentHSIndex - 1]);
    }
  };
  const handleNextClick = () => {
    if (currentHSIndex === DocumentList?.length - 1) {
      resetSlider();
      userMediaTrack(filesId[currentHSIndex], true);
    } else if (currentHSIndex < DocumentList?.length - 1) {
      resetSlider();
      userMediaTrack(filesId[currentHSIndex]);
      setCurrentHSIndex(currentHSIndex + 1);
      getDocumentCategory(filesId[currentHSIndex + 1]);
    }
  };

  const GetItemsDetails = (type, url, videoControl, IsExternal) => {
    return (
      <>
        {type === 'pdf' ? (
          <>
            {/*  <PictureAsPdfIcon
            className="pdf-svg"
            onClick={() => window.open(url, '_blank')}
          /> */}
            <HeaderImage
              type="url"
              imageUrl={url}
              Content={<PictureAsPdfIcon className="pdf-svg" />}
              className="d-flex align-center"
            />
          </>
        ) : (
          <Box
            className={
              type === 'youtube' ? 'preview-image w100' : 'preview-image'
            }
          >
            {type === 'image' ? (
              <>
                {/* <LazyLoadImage src={url} alt="not found" className="media" /> */}
                <HeaderImage
                  imageUrl={url}
                  alt="not found"
                  className="media"
                  type="lazyload"
                  IsExternal={IsExternal}
                />
              </>
            ) : type === 'youtube' ? (
              <>
                <iframe
                  // width="420"
                  height="315"
                  width="100%"
                  // height="100%"
                  frameborder="0"
                  src={`https://www.youtube.com/embed/${getYouTubeVideoId(
                    url
                  )}`}
                  allowFullScreen
                ></iframe>
              </>
            ) : type === 'video' ? (
              <>
                {/* <video
                  src={url}
                  //   src={imgVideo?.url}
                  controls={videoControl}
                  className="media ration-16-9"
                /> */}
                <HeaderImage
                  imageUrl={url}
                  controls={videoControl}
                  className="media ration-16-9"
                  type="video"
                  IsExternal={IsExternal}
                />
              </>
            ) : type === 'audio' ? (
              <>
                {' '}
                {/* <ReactPlayer
                className="react-player-audio"
                url={url}
                playing={false}
                // width='100%'
                // height='100%'
                controls={true}
                config={{
                  file: {
                    attributes: { controlsList: 'nodownload' }
                  }
                }}
              />*/}
                <HeaderImage
                  imageUrl={url}
                  type="audio"
                  IsExternal={IsExternal}
                />
              </>
            ) : type === 'pdf2' ? (
              <Box style={{ width: '100%', height: '500px' }}>
                <iframe
                  title="PDF Viewer"
                  src={url}
                  width="100%"
                  height="100%"
                  frameborder="0"
                >
                  This browser does not support PDFs. Please download the PDF to
                  view it.
                </iframe>
              </Box>
            ) : (
              ''
            )}
          </Box>
        )}
      </>
    );
  };
  const GetItemsDetailsSlider = (type, url, index, IsExternal) => {
    return (
      <>
        {type === 'pdf' || type === 'doc' ? (
          <PictureAsPdfIcon
            className="pdf-svg"
            onClick={() => ChangeSlider(index)}
          />
        ) : (
          <Box
            className={
              type === 'youtube' ? 'preview-image w100' : 'preview-image'
            }
          >
            {type === 'image' ? (
              <>
                {/* <LazyLoadImage
                src={url}
                alt="not found"
                className="media"
                onClick={() => ChangeSlider(index)}
              /> */}
                <HeaderImage
                  imageUrl={url}
                  alt="not found"
                  className="media"
                  onClick={() => ChangeSlider(index)}
                  type="lazyload"
                  IsExternal={IsExternal}
                />
              </>
            ) : type === 'youtube' ? (
              <>
                <Box
                  className="video-section youtube-slide"
                  onClick={() => ChangeSlider(index)}
                >
                  {' '}
                  <Box className="play-icon youtube-icon">{YoutubeIcon()}</Box>
                  <Box className="youtube-icon-back">
                    <Box className="back" />
                  </Box>
                </Box>
              </>
            ) : type === 'video' ? (
              <Box
                className="video-section"
                onClick={() => ChangeSlider(index)}
              >
                {' '}
                <>
                  {/* <video
                    src={url}
                    //   src={imgVideo?.url}
                    controls={false}
                    className="media ration-16-9"
                  /> */}
                  <HeaderImage
                    imageUrl={url}
                    controls={false}
                    className="media ration-16-9"
                    type="video"
                    IsExternal={IsExternal}
                  />
                </>
                <Box className="play-icon">
                  <PlayCircleIcon />
                </Box>
              </Box>
            ) : type === 'audio' ? (
              <LibraryMusicIcon
                className="pdf-svg"
                onClick={() => ChangeSlider(index)}
                IsExternal={IsExternal}
              />
            ) : (
              ''
            )}
          </Box>
        )}
      </>
    );
  };
  useEffect(() => {
    if (CategoryDetails) {
      setMediaDoc(CategoryDetails);
    }
  }, [CategoryDetails]);
  useEffect(() => {
    if (filesId) {
      let selecteda = {};
      filesId.map((f) => {
        selecteda = { ...selecteda, [f]: false };
      });
      setChecked(selecteda);
    }
  }, [filesId]);
  const cid = checked?.[MediaDoc?.id];

  return (
    <Box className="document-center-media-section Onboarding-media-view">
      {' '}
      <Box className="document-center-media-view">
        <Box className="mb26 onboarding-media">
          <Typography className="title-sm fw600 mb8">
            {MediaDoc?.category_name}
          </Typography>
          <Box className="mb8">
            {MediaDoc &&
            MediaDoc?.categoryItemDetails &&
            MediaDoc?.categoryItemDetails?.length > 1 ? (
              <>
                <Slider
                  {...settings}
                  className="document-media-slider"
                  ref={sliderRef}
                  // afterChange={handleAfterChange}
                >
                  {MediaDoc &&
                    MediaDoc?.categoryItemDetails?.map((item) => (
                      <Box className="image-container ">
                        {item?.item_id
                          ? GetItemsDetails(
                              item?.document_category_item_type,
                              item?.item_image_url,
                              MediaDoc?.has_video_control
                            )
                          : GetItemsDetails(
                              item?.document_category_item_type,
                              item?.document_category_item_link,
                              MediaDoc?.has_video_control,
                              true
                            )}
                      </Box>
                    ))}
                </Slider>
              </>
            ) : MediaDoc &&
              MediaDoc?.categoryItemDetails &&
              MediaDoc?.categoryItemDetails?.length === 1 ? (
              <>
                <Box className="image-container ">
                  {MediaDoc?.categoryItemDetails?.[0]?.item_id
                    ? GetItemsDetails(
                        MediaDoc?.categoryItemDetails?.[0]
                          ?.document_category_item_type,
                        MediaDoc?.categoryItemDetails?.[0]?.item_image_url,
                        MediaDoc?.has_video_control
                      )
                    : GetItemsDetails(
                        MediaDoc?.categoryItemDetails?.[0]
                          ?.document_category_item_type,
                        MediaDoc?.categoryItemDetails?.[0]
                          ?.document_category_item_link,
                        MediaDoc?.has_video_control,
                        true
                      )}
                </Box>
              </>
            ) : (
              <></>
            )}
          </Box>
          <Box className="w100 d-flex justify-center mb24">
            {MediaDoc &&
              MediaDoc?.categoryItemDetails &&
              MediaDoc?.categoryItemDetails?.length > 1 && (
                <>
                  <Slider
                    {...settingsView}
                    className="document-media-view"
                    ref={sliderRef2}
                    style={{
                      width: `${sliderWidth}px`,
                      maxWidth: '100%',
                    }}
                    // afterChange={handleAfterChange}
                  >
                    {MediaDoc?.categoryItemDetails?.map((item, index) => (
                      <Box className="image-container ">
                        {item?.item_id
                          ? GetItemsDetailsSlider(
                              item?.document_category_item_type,
                              item?.item_image_url,
                              index
                            )
                          : GetItemsDetailsSlider(
                              item?.document_category_item_type,
                              item?.document_category_item_link,
                              index,
                              true
                            )}
                      </Box>
                    ))}
                  </Slider>
                </>
              )}
          </Box>

          {/* <Typography className="title-text">
            {MediaDoc && MediaDoc?.category_description
              ? MediaDoc?.category_description
              : ''}
          </Typography> */}
          {MediaDoc &&
            MediaDoc?.category_description &&
            MediaDoc?.category_description !== 'null' && (
              <>
                <Box
                  className=""
                  dangerouslySetInnerHTML={{
                    __html: MediaDoc?.category_description,
                  }}
                />
              </>
            )}
        </Box>
        {cid}
        {MediaDoc?.has_agreement_required ? (
          <FormGroup className={'form-checkbox pb16'}>
            <FormControlLabel
              control={
                <Checkbox
                  className="check-box "
                  icon={<CheckBoxOutlineBlankIcon className="uncheck-icon" />}
                  checkedIcon={<CheckBoxIcon className="check-icon" />}
                />
              }
              className="check-box-form form-row max-content title-text"
              name="confirmation"
              checked={cid ? true : false}
              onChange={(e) => {
                let sChecked = checked;
                sChecked = { ...sChecked, [MediaDoc?.id]: e.target.checked };
                setChecked(sChecked);
              }}
              // disabled={}
              label="I confirm that I have read documents."
            />
          </FormGroup>
        ) : (
          <></>
        )}

        <Box className="documents-buttons">
          <CustomButton
            variant="outlined"
            className={
              currentHSIndex === 0 ? 'visiblity-hidden body-text' : 'body-text'
            }
            title="Previous"
            onClick={handlePrevClick}
            disabled={
              currentHSIndex === 0 ||
              (!checked?.[MediaDoc?.id] && MediaDoc?.has_agreement_required)
            }
          />
          <CustomButton
            variant="contained"
            title={
              MediaDoc && currentHSIndex === DocumentList?.length - 1
                ? 'Complete'
                : 'Next'
            }
            disabled={
              !checked?.[MediaDoc?.id] && MediaDoc?.has_agreement_required
            }
            onClick={() => {
              handleNextClick();
              //  MediaDoc?.categoryItemDetails[currentHSIndex]?.id,
              // MediaDoc?.categoryItemDetails[currentHSIndex];
            }}
            // disabled={currentHSIndex === MediaDoc.length - 1}
          />
        </Box>
      </Box>
    </Box>
  );
};

export default FileDetails;
