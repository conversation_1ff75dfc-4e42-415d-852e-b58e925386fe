    

    .time-entry-wrap {
        padding: var(--spacing-lg) var(--spacing-none);
        font-family: var(--font-family-primary);

        .no-time-entry {
            font-family: var(--font-family-primary);
        }

        .add-time-wrap {
            font-family: var(--font-family-primary);
        }
    }

    .show-time-wrap {
        padding: var(--spacing-md) var(--spacing-lg) var(--spacing-none) var(--spacing-lg);

        .time-show-wrap {
            padding-bottom: var(--spacing-md);
            justify-content: space-between;

            .info-wrap {
                line-height: 0px;

                .preview-img {
                    border-radius: var(--border-radius-full);

                    @media(max-width:767px) {
                        height: 35px;
                        width: 35px;
                    }
                }

                .profile-name {
                    font-family: var(--font-family-primary);

                    @media(max-width:767px) {
                        font-size: var(--font-size-base);
                    }
                }

                @media(max-width:767px) {
                    gap: var(--spacing-md);
                }

            }

            .time-wrap {
                font-family: var(--font-family-primary);
                display: flex;
                align-items: center;
                gap: var(--spacing-5xl);

                .edit-time-wrap {
                    .MuiInputBase-input {
                        padding: var(--spacing-xs) var(--spacing-md) !important;
                    }

                    .MuiInputBase-root {
                        min-height: 30px;
                        min-width: 94px;
                    }
                }

                .icons-wrap {

                    .save-icon,
                    .edit-icon,
                    .delete-icon {
                        fill: var(--color-primary);
                        height: 21px;
                        width: 21px;
                        cursor: pointer;
                    }
                }

                @media(max-width:767px) {
                    font-size: var(--font-size-base);
                    padding-left: var(--spacing-xxs);
                }

                @media(max-width:424px) {
                    justify-content: space-between;
                    width: 100%;
                }
            }

            @media(max-width: 575px) {
                flex-direction: column;
                row-gap: var(--spacing-xsm);
                justify-content: center;
                align-items: flex-start !important;
                padding-left: var(--spacing-lg);
            }

            @media(max-width: 374px) {
                padding: var(--spacing-md) var(--spacing-none);

            }
        }

        @media(max-width:767px) {
            padding: var(--spacing-xsm) var(--spacing-none);
        }
    }