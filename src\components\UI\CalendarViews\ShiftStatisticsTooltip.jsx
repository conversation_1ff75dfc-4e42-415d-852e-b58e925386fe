import { Tooltip } from '@mui/material';
import {
  calculateTotalTime,
  formatBreakTime,
  getRoleUserCount,
  getTotalBreakMinutes,
} from './ShiftUtility';
import moment from 'moment';

// ========== Shift Statistics Tooltip ==========
export const ShiftStatistics = ({ shifts, isViewType }) => (
  <Tooltip
    placement="right"
    title={
      <div>
        <div className="tooltip-shift-details">
          <p className="tooltip-shift-title">Hours</p>
          <p className="tooltip-shift-value">
            {calculateTotalTime(shifts)}{' '}
            <span className="tooltip-shift-title">
              /{isViewType === 'week' ? 'week' : 'month'}
            </span>
          </p>
        </div>
        <div className="tooltip-shift-details">
          <p className="tooltip-shift-title">Extra Info</p>
          <p className="tooltip-shift-value">
            {formatBreakTime(getTotalBreakMinutes(shifts))}{' '}
            <span className="tooltip-shift-title">of unpaid breaks</span>
          </p>
        </div>
        <div className="tooltip-shift-details">
          <p className="tooltip-shift-title">
            {shifts?.length} {shifts?.length > 1 ? 'shifts' : 'shift'}
          </p>
          <div>
            {getRoleUserCount(shifts)?.map((item, i) => (
              <p className="tooltip-shift-value" key={i}>
                {item?.shiftCount} {item?.roleName}
              </p>
            ))}
          </div>
        </div>
      </div>
    }
    arrow
    classes={{
      tooltip:
        'calendar-event-list-tooltip-custom calendar-event-left-shift-tooltip',
    }}
  >
    <div>
      <p className="shift-total-time-count">{calculateTotalTime(shifts)}</p>
      <p className="shift-total-count">
        {shifts?.length} {shifts?.length > 1 ? 'shifts' : 'shift'}
      </p>
    </div>
  </Tooltip>
);

// ========== Shift Summary Statistics Tooltip ==========
export const ShiftSummaryStatistics = ({
  shifts,
  currentMonth,
  isViewType,
}) => {
  var weekStart = moment(currentMonth).clone().startOf('isoWeek');
  var weekEnd = moment(currentMonth).clone().endOf('isoWeek');
  var weeknumber = moment(currentMonth, 'MMDDYYYY').isoWeek();
  return (
    <Tooltip
      placement="right"
      title={
        <div>
          {isViewType === 'month' && (
            <div className="tooltip-shift-details">
              <p className="tooltip-shift-value">
                {currentMonth.format('MMMM YYYY')}
              </p>
            </div>
          )}
          {isViewType === 'week' && (
            <div className="tooltip-shift-details">
              <p className="tooltip-shift-value">Week {weeknumber}</p>
              <p className="tooltip-shift-value">
                {weekStart.format('MMM') === moment().format('MMM')
                  ? weekStart.format('D') +
                    ' - ' +
                    weekEnd.format('D MMMM YYYY')
                  : weekStart.format('D MMM') +
                    ' - ' +
                    weekEnd.format('D MMMM YYYY')}
              </p>
            </div>
          )}
          <div className="tooltip-shift-details">
            <p className="tooltip-shift-title">Hours</p>
            <p className="tooltip-shift-value">{calculateTotalTime(shifts)}</p>
          </div>
          <div className="tooltip-shift-details">
            <p className="tooltip-shift-title">Salaried Staff</p>
            <p className="tooltip-shift-value">{calculateTotalTime(shifts)}</p>
          </div>
          <div className="tooltip-shift-details">
            <p className="tooltip-shift-title">Ad. Info</p>
            <p className="tooltip-shift-value">
              {formatBreakTime(getTotalBreakMinutes(shifts))}{' '}
              <span>of unpaid breaks</span>
            </p>
          </div>
        </div>
      }
      arrow
      classes={{
        tooltip:
          'calendar-event-list-tooltip-custom calendar-event-left-shift-tooltip',
      }}
    >
      <div>
        {currentMonth.format('MMMM')} {isViewType === 'week' && weeknumber}{' '}
        Summary
      </div>
    </Tooltip>
  );
};
