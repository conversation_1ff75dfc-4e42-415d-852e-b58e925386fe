'use client';
import React, { useEffect, useState } from 'react';
import {
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  Box,
  Divider,
} from '@mui/material';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import { useRouter, useSearchParams } from 'next/navigation';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import AddIcon from '@mui/icons-material/Add';
import RightDrawer from '@/components/UI/RightDrawer';
import AssignEmployee from '@/components/Leave/AssignEmployee';
import EditIcon from '@/components/ActionIcons/EditIcon';
import DialogBox from '@/components/UI/Modalbox';
import DeleteIcon from '@/components/ActionIcons/DeleteIcon';
import NoDataView from '@/components/UI/NoDataView';
import CustomSearch from '@/components/UI/CustomSearch';
import CustomButton from '@/components/UI/CustomButton';
import ContentLoader from '@/components/UI/ContentLoader';
import PreLoader from '@/components/UI/Loader';
import DeleteModal from '@/components/UI/DeleteModal';
import AddEditPolicyType from '../LeavePolicy/AddEditPolicyType';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import AddLeavePolicyComp from '../AddLeavePolicy';
import './leavepolicytype.scss';

export default function LeavePolicyType() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const isEdit = searchParams.get('is_edit');
  const isPolicy = searchParams.get('is_policy');
  // const policyid = searchParams.get('policy_id');
  const policyTypeId = searchParams.get('policy_type_id');
  const queryParams = new URLSearchParams(searchParams);
  const [loader, setLoader] = useState(true);
  const [actionLoader, setActionLoader] = useState(false);
  const [leaveTypeList, setLeaveTypeList] = useState([]);
  const [searchValue, setSearchValue] = useState('');
  const [anuualExist, setanuualExist] = useState(false);
  // const [filterData, setFilterData] = useState({ status: '' });
  // const [filterDataApplied, setFilterDataApplied] = useState({ status: '' });
  const [assignEmployee, setAssignEmployee] = useState();
  const [holidayExpand, setHolidayExpand] = useState();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deletePolicyToggle, setDeletePolicyToggle] = useState(false);
  const [deleteId, setDeleteId] = useState(null);

  const getLeaveTypeList = async (searchValue, isUpdated = false) => {
    !isUpdated && setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_LEAVE_TYPE + `?search=${searchValue}`
      );
      if (status === 200) {
        setLoader(false);
        setanuualExist(data?.anuualExist);
        // const leaveList = data?.data;
        // setTotalCount(data?.total);
        data?.data && setLeaveTypeList(data?.data);
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const handleFilterData = (type) => {
    // setFilter(false);
    if (type === 'apply') {
      getLeaveTypeList(searchValue, true);
      // setFilterDataApplied(filterData);
    } else {
      // const clearFilter = {
      //   status: '',
      // };
      // setFilterData(clearFilter);
      // setFilterDataApplied(clearFilter);
      getLeaveTypeList(searchValue, true);
    }
  };
  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      handleFilterData('apply');
    }
  };
  const handleDeletePolicy = async () => {
    if (!deleteId) return;
    try {
      setActionLoader(true);
      const { status, data } = await axiosInstance.delete(
        URLS?.REMOVE_POLICY + `${deleteId}`
      );

      if (status === 200) {
        if (data.status) {
          setApiMessage('success', data?.message);
          getLeaveTypeList(searchValue, true);
        } else {
          setApiMessage('error', data?.message);
          getLeaveTypeList(searchValue, true);
        }
      } else {
        setApiMessage('error', data?.message);
      }
      setActionLoader(false);
      setDeletePolicyToggle(false);
      setDeleteId(null);
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setActionLoader(false);
      setDeletePolicyToggle(false);
      setDeleteId(null);
    }
  };
  const handleAssignEmployees = async (user_ids, assignId) => {
    const requestData = {
      user_ids: user_ids,
      policy_ids: assignId,
      policy_type: 'leave',
    };
    try {
      const { status, data } = await axiosInstance.post(
        URLS?.ASSIGN_EMPLOYEE,
        requestData
      );
      if (status === 200) {
        if (data?.status) {
          setApiMessage('success', data?.message);
          setAssignEmployee(false);
          getLeaveTypeList(searchValue, true);
        } else {
          setApiMessage('error', data?.message);
          getLeaveTypeList(searchValue, true);
        }
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const handleOpenDeleteDialog = (id) => {
    setDeleteId(id);
    setDeleteDialogOpen(true);
  };
  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setDeleteId(null);
  };
  const handleConfirmDelete = async () => {
    if (!deleteId) return;
    try {
      setActionLoader(true);
      const { status, data } = await axiosInstance.delete(
        URLS?.LEAVE_TYPE + `/${deleteId}`
      );

      if (status === 200) {
        if (data.status) {
          setApiMessage('success', data?.message);
          setHolidayExpand();
          getLeaveTypeList(searchValue, true);
        } else {
          setApiMessage('error', data?.message);
          getLeaveTypeList(searchValue, true);
        }
      } else {
        setApiMessage('error', data?.message);
      }
      setActionLoader(false);
      handleCloseDeleteDialog();
    } catch (error) {
      setActionLoader(false);
      setApiMessage('error', error?.response?.data?.message);
      handleCloseDeleteDialog();
    }
  };

  useEffect(() => {
    getLeaveTypeList(searchValue, false);
  }, []);

  useEffect(() => {
    if (leaveTypeList?.length > 0) setHolidayExpand(leaveTypeList[0]?.id);
  }, [leaveTypeList]);

  const handleClearSearch = () => {
    getLeaveTypeList('', true);
  };

  return (
    <>
      {isEdit === 'true' || isEdit === 'false' ? (
        <>
          {isPolicy === 'true' || isPolicy === 'false' ? (
            <AddLeavePolicyComp
              isEdit={isEdit === 'true' ? true : false}
              policyid={policyTypeId}
              getLeaveList={getLeaveTypeList}
            />
          ) : (
            <>
              <Box className="d-flex align-center pb8">
                <ArrowBackIosIcon
                  className="cursor-pointer"
                  onClick={() => {
                    router.back();
                  }}
                />
                <Typography className="body-text fw600 pr8">
                  {isEdit === 'true' ? 'Edit Leave Type' : 'Add Leave Type'}
                </Typography>
              </Box>
              <Divider className="mb8" />
              <AddEditPolicyType getLeaveTypeList={getLeaveTypeList} />
            </>
          )}
        </>
      ) : (
        <>
          {actionLoader && <PreLoader />}
          <Box className="leave-policy-type-wrap">
            <Box className="d-flex justify-space-between align-center flex-wrap gap-10 mb16">
              <Box>
                <Typography className="title-sm">
                  Leave Type & Policy
                </Typography>
                <Typography className="title-text">
                  Create varios access levels for employees to determine the
                  access to organizational data
                </Typography>
              </Box>
              <Box className="search-section-wrap">
                <Box className="search-section-fields">
                  <CustomSearch
                    setSearchValue={setSearchValue}
                    onKeyPress={handleKeyPress}
                    searchValue={searchValue}
                    isClearSearch
                    handleClearSearch={() => handleClearSearch()}
                  />
                </Box>
                <Box>
                  <CustomButton
                    variant="outlined"
                    title="Search"
                    fullWidth={false}
                    onClick={() => handleFilterData('apply')}
                  />
                </Box>
                <Box>
                  <CustomButton
                    title="Add Leave Type"
                    startIcon={<AddIcon />}
                    onClick={() => {
                      queryParams.set('anuualExist', anuualExist);
                      queryParams.set('is_edit', false);
                      router.push(`?${queryParams.toString()}`);
                      // router.push(`/create-policy-type?anuualExist=${anuualExist}`);
                    }}
                  />
                </Box>
              </Box>
            </Box>
            <Divider />

            {loader ? (
              <ContentLoader />
            ) : leaveTypeList && leaveTypeList?.length > 0 ? (
              leaveTypeList?.map((policyGroup, index) => {
                return (
                  <Accordion
                    className="leave-accordion cursor-default"
                    key={index}
                    expanded={holidayExpand === policyGroup?.id}
                  >
                    <AccordionSummary
                      className="accordion-summary cursor-default"
                      expandIcon={
                        <ExpandMoreIcon
                          className="cursor-pointer"
                          onClick={() => {
                            setHolidayExpand(
                              holidayExpand === policyGroup?.id
                                ? null
                                : policyGroup?.id
                            );
                          }}
                        />
                      }
                    >
                      <Box className="d-flex align-center justify-space-between  w100">
                        <Box
                          className="d-flex align-center flex-wrap w100 cursor-pointer policy-name"
                          onClick={() => {
                            setHolidayExpand(
                              holidayExpand === policyGroup?.id
                                ? null
                                : policyGroup?.id
                            );
                          }}
                        >
                          <Box className="d-flex align-center gap-5">
                            <Typography
                              className="policy-color-wrap"
                              sx={{
                                backgroundColor: policyGroup?.leave_type_color,
                              }}
                            />
                            {policyGroup?.name}
                          </Box>
                          {policyGroup?.has_annual_leave && (
                            <Typography
                              component="span"
                              className="sub-title-text annual-text ml16"
                            >
                              Annual
                            </Typography>
                          )}
                          {/* ({policyGroup?.count}) */}
                        </Box>
                        <Box className="d-flex align-center justify-center actions holiday-actions p0 h100 ">
                          <Tooltip
                            title={<Typography>Edit</Typography>}
                            arrow
                            classes={{
                              tooltip: 'info-tooltip-container',
                            }}
                          >
                            <Box>
                              <EditIcon
                                onClick={() => {
                                  queryParams.set('policy_id', policyGroup?.id);
                                  queryParams.set('anuualExist', anuualExist);
                                  queryParams.set('is_edit', true);
                                  router.push(`?${queryParams.toString()}`);

                                  // router.push(
                                  //   `/edit-policy-type/${policyGroup?.id}?anuualExist=${anuualExist}` //?anuualExist=${anuualExist}
                                  // );
                                }}
                              />
                            </Box>
                          </Tooltip>
                          <Tooltip
                            title={<Typography>Delete</Typography>}
                            arrow
                            classes={{
                              tooltip: 'info-tooltip-container',
                            }}
                          >
                            <Box>
                              <DeleteIcon
                                onClick={() =>
                                  handleOpenDeleteDialog(policyGroup?.id)
                                }
                              />
                            </Box>
                          </Tooltip>{' '}
                        </Box>
                      </Box>
                    </AccordionSummary>
                    <AccordionDetails>
                      {policyGroup?.leave_accural_policy &&
                        policyGroup?.leave_accural_policy?.length > 0 &&
                        policyGroup?.leave_accural_policy?.map(
                          (policy, idx) => (
                            <Box key={idx}>
                              <Box
                                // className='d-flex align-center justify-space-between mt8 mb8 ml16 mr16'
                                className="leave-accordian-tab mt8 mb8 ml16 mr16"
                              >
                                <Typography className="title-text">
                                  {policy?.leave_policy_name}
                                  {policy?.has_leave_policy_default && (
                                    <Typography
                                      component="span"
                                      className="sub-title-text default-text ml16"
                                    >
                                      Default
                                    </Typography>
                                  )}
                                </Typography>
                                <Typography
                                  className="title-text text-align employyes-text-wrap cursor-pointer"
                                  onClick={() => {
                                    setAssignEmployee(policy);
                                  }}
                                >
                                  {policy?.leave_policy_user_count
                                    ? policy?.leave_policy_user_count
                                    : 0}{' '}
                                  Employees
                                </Typography>
                                <Box className="d-flex align-center justify-end actions p0 h100">
                                  <Tooltip
                                    arrow
                                    title={
                                      <Typography>Assign Employees</Typography>
                                    }
                                    classes={{
                                      tooltip: 'info-tooltip-container',
                                    }}
                                  >
                                    <div className="action-icon assign-icons d-flex">
                                      <PersonAddIcon
                                        className="cursor-pointer"
                                        onClick={() => {
                                          setAssignEmployee(policy);
                                        }}
                                      />
                                    </div>
                                  </Tooltip>
                                  <Tooltip
                                    title={<Typography>Edit</Typography>}
                                    arrow
                                    classes={{
                                      tooltip: 'info-tooltip-container',
                                    }}
                                  >
                                    <Box>
                                      <EditIcon
                                        onClick={() => {
                                          queryParams.set(
                                            'policy_id',
                                            policyGroup?.id
                                          );
                                          queryParams.set(
                                            'policy_type_id',
                                            policy?.id
                                          );
                                          queryParams.set(
                                            'is_anuual',
                                            policyGroup?.has_annual_leave
                                          );
                                          queryParams.set('is_policy', true);
                                          queryParams.set('is_edit', true);
                                          router.push(
                                            `?${queryParams.toString()}`
                                          );
                                          // router.push(
                                          //   `/edit-leave-policy/${policy?.id}?id=${policyGroup?.id}`
                                          // );
                                        }}
                                      />
                                    </Box>
                                  </Tooltip>

                                  <Tooltip
                                    title={<Typography>Delete</Typography>}
                                    arrow
                                    classes={{
                                      tooltip: 'info-tooltip-container',
                                    }}
                                  >
                                    <Box>
                                      <DeleteIcon
                                        onClick={() => {
                                          setDeletePolicyToggle(true);
                                          setDeleteId(policy?.id);
                                        }}
                                      />
                                    </Box>
                                  </Tooltip>
                                </Box>
                              </Box>
                              <Divider />
                            </Box>
                          )
                        )}
                      <Box className="create-new-policy-btn-wrap d-flex justify-start mt8 mb8">
                        <CustomButton
                          className="create-new-policy-btn"
                          startIcon={<AddIcon />}
                          title="Create New Policy"
                          onClick={() => {
                            queryParams.set('policy_id', policyGroup?.id);
                            queryParams.set('is_policy', true);
                            queryParams.set(
                              'is_anuual',
                              policyGroup?.has_annual_leave
                            );
                            queryParams.set('is_edit', false);
                            router.push(`?${queryParams.toString()}`);
                            // router.push(
                            //   `/add-leave-policy?id=${policyGroup?.id}`
                            // );
                          }}
                        />
                      </Box>
                    </AccordionDetails>
                  </Accordion>
                );
              })
            ) : (
              <Box className="no-data d-flex align-center justify-center">
                <NoDataView
                  title="No Leave Policy Found"
                  description="There is no Leave Policy available at the moment."
                />
              </Box>
            )}
          </Box>
          <RightDrawer
            anchor={'right'}
            open={assignEmployee}
            onClose={() => {
              setAssignEmployee(false);
            }}
            title="Assign Employee"
            subTitle="Leave Configuration"
            className="assign-employee-drawer leave-assign-drawer"
            content={
              <>
                <AssignEmployee
                  setClose={setAssignEmployee}
                  AssignEmployees={handleAssignEmployees}
                  assignId={[assignEmployee?.id]}
                  SelectedID={assignEmployee?.leave_policy_user_ids}
                />
              </>
            }
          />
          <DialogBox
            open={deleteDialogOpen}
            handleClose={handleCloseDeleteDialog}
            className="delete-modal"
            dividerClass="delete-modal-divider"
            title="Confirmation"
            content={
              <DeleteModal
                handleCancel={handleCloseDeleteDialog}
                handleConfirm={handleConfirmDelete}
                text="Are you sure you want to delete this leave type?"
              />
            }
          />
          <DialogBox
            open={deletePolicyToggle}
            handleClose={() => setDeletePolicyToggle(false)}
            className="delete-modal"
            dividerClass="delete-modal-divider"
            title="Confirmation"
            content={
              <DeleteModal
                handleCancel={() => setDeletePolicyToggle(false)}
                handleConfirm={handleDeletePolicy}
                text="Are you sure you want to delete this leave policy?"
              />
            }
          />
        </>
      )}
    </>
  );
}
