'use client';

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Tooltip,
  InputAdornment,
  IconButton,
} from '@mui/material';
import CustomTextField from '@/components/UI/CustomTextField';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import CachedIcon from '@mui/icons-material/Cached';
import CustomButton from '@/components/UI/CustomButton';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import { setApiMessage, GeneratePin } from '@/helper/common/commonFunctions';

export default function UserResetPin({
  RandomPIN,
  setRandomPIN,
  isReset,
  setIsReset,
  ViewAccessOnly,
  UserId,
  setLoader,
  handleClosePin,
}) {
  const [showEnterPassword, setShowCurrentPassword] = useState(false);
  const [genPassword, setGenPassword] = useState(false);
  const copyToClipboard = () => {
    navigator.clipboard.writeText(RandomPIN).then(
      () => {
        setApiMessage('success', 'PIN copied');
      },
      () => {
        setApiMessage('error', 'Failed to copy PIN');
      }
    );
  };
  const ResetPIN = async (Password, isNotify) => {
    let sendData = {
      user_login_pin: Password,
      user_id: UserId,
      reset_type: 'pin',
      ...(isNotify && { isNotify: true }),
    };
    try {
      setLoader(true);
      handleClosePin();
      const { status } = await axiosInstance.post(
        URLS.RESET_USER_PASSWORD,
        sendData
      );

      if (status === 200) {
        setLoader(false);
        setIsReset(false);
        setRandomPIN();
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setLoader(false);
    }
  };

  return (
    <>
      <Box className="generate-password-section">
        <Box className="reset-password-display-grid">
          <Box className="reset-password-textfield-sec">
            <CustomTextField
              id="new_password"
              name="new_password"
              value={RandomPIN}
              label="PIN"
              type={showEnterPassword ? 'text' : 'password'}
              className={
                (!RandomPIN || RandomPIN?.length < 4) && isReset
                  ? 'w100 password-textfield password-error reset-password-textfield'
                  : 'w100 password-textfield reset-password-textfield'
              }
              error={Boolean((!RandomPIN || RandomPIN?.length < 4) && isReset)}
              helperText={
                !RandomPIN && isReset
                  ? 'This field is required'
                  : RandomPIN?.length < 4 && isReset
                    ? 'PIN length must be minimum 4 character'
                    : null
              }
              placeholder="Enter PIN"
              onChange={(e) => {
                if (e.target.value === '' || e.target.value?.length < 5) {
                  setIsReset(false);
                  setRandomPIN(e.target.value);
                  setGenPassword(false);
                }
              }}
              disabled={ViewAccessOnly}
              onInput={(e) => {
                let value = e.target.value;
                if (value === '' || /^\d*\.?\d{0,2}$/.test(value)) {
                  e.target.value = value;
                } else {
                  e.target.value = value.slice(0, -1);
                }
              }}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end" className="eye-icon">
                    <IconButton
                      disableRipple
                      onClick={() => setShowCurrentPassword(!showEnterPassword)}
                    >
                      <Box className="eye-wrap">
                        {showEnterPassword ? (
                          <VisibilityIcon />
                        ) : (
                          <VisibilityOffIcon />
                        )}
                      </Box>
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
          </Box>

          <Box className="generate-password-btn">
            {RandomPIN ? (
              <Tooltip title="Copy PIN" arrow>
                <ContentCopyIcon
                  className={'cursor-pointer'}
                  onClick={() => {
                    !ViewAccessOnly && copyToClipboard();
                  }}
                />
              </Tooltip>
            ) : (
              <ContentCopyIcon className={'disabled-copy'} />
            )}
            {ViewAccessOnly ? (
              <CachedIcon />
            ) : (
              <Tooltip title="Generate PIN" arrow>
                <CachedIcon
                  className={'cursor-pointer '}
                  onClick={() => {
                    setRandomPIN(GeneratePin(4));
                    setGenPassword(true);
                  }}
                />
              </Tooltip>
            )}
          </Box>
        </Box>
        {RandomPIN && !genPassword && (
          <Box className="pt8">
            <Typography
              className="p12"
              style={{
                color: RandomPIN.length === 4 ? 'green' : 'red',
              }}
            >
              {RandomPIN.length === 4 ? '✔' : '✖'} At least 4 characters long
            </Typography>
          </Box>
        )}

        <Box className="create-cancel-button  mt32 justify-center">
          <CustomButton
            variant="outlined"
            title="Save"
            fullWidth
            disabled={ViewAccessOnly || !RandomPIN || RandomPIN?.length < 4}
            onClick={() => {
              ResetPIN(RandomPIN);
            }}
          />
          <CustomButton
            variant="contained"
            onClick={() => {
              ResetPIN(RandomPIN, true);
            }}
            title="Save & Send Email"
            fullWidth
            disabled={ViewAccessOnly || !RandomPIN || RandomPIN?.length < 4}
          />
        </Box>
      </Box>
    </>
  );
}
