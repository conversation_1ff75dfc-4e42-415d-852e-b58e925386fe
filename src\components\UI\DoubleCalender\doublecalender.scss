.double-calender {
  .double-calender-wrap {
    .fc-header-toolbar {
      margin-bottom: 0px !important;
    }

    .fc-daygrid-day-top {
      height: 24px;
    }
    .fc-button-group {
      .fc-resourceTimelineWeek-button,
      .fc-dayGridMonth-button,
      .fc-timeGridDay-button {
        padding: 4px 8px !important;
        background-color: var(--color-secondary) !important;
        color: var(--text-color-primary) !important;
      }
      .fc-button-active {
        background-color: var(--text-color-primary) !important;
        color: var(--color-secondary) !important;
      }
      .fc-prev-button,
      .fc-next-button {
        padding: 4px 5px !important;
        background-color: var(--color-secondary) !important;
        border-radius: 10px !important;
        margin-right: 10px !important;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 30px;
        width: 30px;

        .fc-icon {
          font-size: 21px;
          color: var(--text-color-primary) !important;
        }
      }

      .fc-button {
        background-color: var(--color-white) !important;
        color: var(--text-color-primary) !important;
        border-color: var(--color-primary);
        line-height: 12px;
        font-size: 14px;
        text-transform: capitalize;
        &:focus {
          box-shadow: none !important;
        }
      }
    }
    .fc-today-button {
      line-height: 16px;
      font-size: 14px;
      text-transform: capitalize !important;
      background-color: var(--color-primary);
      color: var(--text-color-white);
      border-color: var(--color-primary);
    }
    .fc-toolbar-title {
      font-size: var(--font-size-base);
      font-family: var(--font-family-primary);
      font-weight: var(--font-weight-medium);
      line-height: var(--line-height-base);
      color: var(--text-color-black);
    }
    .fc-view-harness {
      height: 100% !important;
      min-height: 400px;
      .fc-multimonth-title {
        padding: 10px 0px 15px 0px !important;
        font-weight: 500;
      }
      .fc-license-message {
        display: none;
      }
      .fc-multimonth {
        gap: 20px;
        display: flex;
        flex-wrap: nowrap !important;
        width: auto !important;
        border: none;
        height: 100%;
        max-height: fit-content;

        .fc-multimonth-month {
          width: 50% !important;
          padding: 0px !important;
          height: 100% !important;
          max-height: fit-content !important;
          .fc-multimonth-header-table {
            border-left-style: none;
            border-right-style: none;
            .fc-col-header-cell {
              font-weight: 400;
              padding-bottom: 20px;
            }
          }
          @media (max-width: 767px) {
            width: 100% !important;
          }
        }
        .fc-multimonth-daygrid-table {
          height: 100% !important;
          min-height: 300px !important;
          border-left-style: none;
          border-right-style: none;
          &:last-child {
            border-bottom-style: none !important;
          }
          .fc-daygrid-day-frame {
            display: flex;
            justify-content: center;
          }
        }
        .fc-daygrid-body-balanced {
          .fc-daygrid-day-frame {
            .fc-daygrid-day-events {
              height: 100%;
              display: flex;
              flex-direction: column;
              align-items: flex-start;
              justify-content: flex-end;
              .fc-event {
                margin-left: 0px;
                margin-bottom: 2px;
              }
              .fc-daygrid-event-harness {
                margin-top: 0px !important;
              }
              .fc-event-main {
                margin-top: 2px;
              }
              .fc-daygrid-event-harness,
              .fc-daygrid-event-harness-abs {
                top: 14px !important;
                display: flex;
                flex-direction: column;
                gap: 10px;
                max-width: 60px;
                // visibility: visible !important;
              }
            }
            .fc-daygrid-day-bottom {
              font-size: 12px;
              color: var(--text-color-primary);
              max-width: 100%;
              min-height: 16px;
              .fc-daygrid-more-link {
                border: none;
                text-overflow: ellipsis;
                overflow: hidden;
                word-break: break-all;
                white-space: nowrap;
                width: 100%;
              }
            }
          }
        }
        &::-webkit-scrollbar {
          display: none !important;
        }
        @media (max-width: 767px) {
          flex-wrap: wrap !important;
        }
      }
      // .fc-popover {
      //   display: none;
      // }
    }
    .fc-theme-standard th,
    td {
      border: none !important;
    }
    .fc-day-disabled {
      background-color: transparent !important;
    }
    .fc-daygrid-body {
      .fc-daygrid-event-harness {
        z-index: 1;
        .fc-event {
          border: none !important;
          font-size: 10px;
          z-index: 10;
          background: transparent;
          .event-title {
            font-size: 12px;
            color: var(--text-color-primary);
            max-width: 100%;
          }
        }
      }

      .fc-daygrid-day-bottom {
        z-index: 9999;
      }
      .events-wrap {
        width: 100%;
        background-color: transparent !important;
        .event-container-wrap {
          padding-top: 16px;
          cursor: pointer;
          text-align: center;
          .event-title {
            border-radius: 3px;
            width: 100%;
            background: transparent;
            cursor: pointer;
          }
        }
      }
      .more-btn-wrap {
        margin-bottom: 2px;
        margin-top: 2px;
      }
      .holiday-event-wrap {
        background-color: var(--color-bg-holiday);
        border: 1px solid var(--color-holiday);
        color: var(--color-holiday);
        border-radius: 4px;
        padding: 0px 5px;
        // width: 50px;
        white-space: normal;
      }
      .fc-daygrid-more-link {
        display: block;
      }
    }
    .fc-day-today {
      background-color: var(--color-chip);
      .fc-daygrid-day-number {
        color: var(--text-color-white);
        background-color: var(--color-primary);
        border-radius: 50%;
        padding: 0px 7px;
        margin-top: 1px;
        margin-right: 2px;
        max-width: max-content;
        max-height: max-content;
      }
    }
    .highlight-date {
      background-color: var(
        --color-ice-blue
      ) !important; // Highlight with yellow
    }
    .fc-multiMonthYear-view {
      .event-container-wrap {
        // display: none;
        .year-title {
          // max-height: 0 !important;
          // padding: 0 !important;
          // border: 0 !important;
          // display: none !important;
        }
      }
      // .fc-daygrid-day-events {
      //   .fc-daygrid-event-harness {
      //     display: none;
      //   }
      //   .fc-daygrid-event-harness:first-child {
      //     display: block;
      //   }
      // }
      .fc-daygrid-day {
        .fc-daygrid-day-frame {
          .fc-daygrid-day-events {
            .year-title {
              visibility: visible !important;
              min-height: auto !important;
              max-height: none !important;
              font-size: 10px;
            }
            .event-title {
              // visibility: hidden !important;
              // min-height: auto !important;
              // max-height: none !important;
            }
            .fc-daygrid-more-link {
              z-index: 1000 !important;
            }
            .fc-daygrid-event-harness {
              // height: 9px;
            }
            // .fc-daygrid-event-harness {
            //   // display: none;
            //   position: absolute;
            //   top: -5px !important;
            //   left: 2px !important;
            //   margin-top: 0 !important;
            //   right: auto !important;
            //   max-width: 64px;
            //   visibility: visible !important;
            //   width: 64px;
            //   .fc-event-start {
            //     margin-left: 0 !important;
            //   }
            // }
            // .fc-daygrid-event-harness:first-child {
            //   display: block;
            //   max-width: 64px;
            //   margin-top: 0 !important;
            // }
          }
          .fc-daygrid-day-bottom {
            margin-top: 8px !important;
          }
        }
      }
      .fc-event-main {
        max-width: 64px;
        .year-holiday {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          max-width: 9px;
          //   flex-direction: column;
          .holiday-dots {
            width: 9px;
            height: 9px;
            min-width: 9px;
            background-color: var(--color-holiday);
            border-radius: 50%;
            // margin-right: 5px;
          }
        }
      }
    }
  }

  .selected-date-details-wrap {
    padding: 20px 0px 15px 0px;
    .selected-date-details {
      .show-date-wrap {
        padding-bottom: 20px;
        gap: 10px;
        .date-show-wrap {
          gap: 10px;
          @media (max-width: 1199px) {
            flex-wrap: wrap;
            row-gap: 10px;
            justify-content: flex-start !important;
          }
        }
        .show-date-text-wrap {
          font-size: 16px;
          font-weight: 500;
        }
        .person-count-wrap {
          border: var(--normal-sec-border);
          border-radius: 30px;
          padding: 2px 10px;
          font-size: 12px;
          font-weight: 500;
        }
        .search-wrap {
          .search-bar {
            width: 100% !important;
            max-width: 230px !important;
            @media (max-width: 767px) {
              max-width: 100% !important;
            }
          }
          .MuiInputBase-root {
            min-height: 30px;
            border-radius: 4px;
            .MuiInputBase-input {
              padding: 2px 10px !important;
              &::placeholder {
                font-size: 14px;
              }
            }
            .MuiOutlinedInput-notchedOutline {
              border-color: none !important;
              border-color: var(--color-light-grayish-blue) !important;
            }
          }
          @media (max-width: 767px) {
            width: 100% !important;
          }
        }

        @media (max-width: 1199px) {
          flex-wrap: wrap;
          row-gap: 15px;
          justify-content: end !important;
        }
      }
      .event-list-wrap {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 10px;
        .event-card {
          background-color: var(--color-secondary);
          border-radius: 8px;
          padding: 10px;
          width: 100%;
          max-width: 300px;

          .event-title-wrap {
            font-size: 16px;
            font-weight: 400;
          }
          .event-date-wrap {
            gap: 10px;
            .east-icon {
              fill: var(--color-muted);
              height: 20px;
              width: 20px;
            }
          }
          .right-arrow-icon {
            height: 21px;
            width: 21px;
          }
        }

        .holiday-text {
          background-color: var(--color-bg-holiday);
          border: 1px solid var(--color-holiday);
          color: var(--color-holiday);
          border-radius: 4px;
          padding: 0 5px;
        }
      }
    }
  }
  .leave-type-wrap {
    gap: 10px;
    .dot-wrap {
      height: 12px;
      width: 12px;
      border-radius: 50%;
    }
    .pending-leave {
      color: var(--color-warning);
    }
    .approved-leave {
      color: var(--color-green);
    }
    .rejected-leave {
      color: var(--color-danger);
    }
    .cancelled-leave {
      color: var(--color-black);
    }
    .calendar-holiday {
      color: var(--color-black);
    }
    .yellow-dot-wrap {
      background-color: var(--color-warning);
      display: inline-block;
    }
    .green-dot-wrap {
      background-color: var(--color-green);
      display: inline-block;
    }
    .red-dot-wrap {
      background-color: var(--color-danger);
      display: inline-block;
    }
    .black-dot-wrap {
      background-color: var(--color-black);
      display: inline-block;
    }
    .blue-dot-wrap {
      background-color: var(--color-light-holiday);
      display: inline-block;
    }
    @media (max-width: 1199px) {
      flex-wrap: wrap;
      margin-top: 10px;
    }
  }
}
