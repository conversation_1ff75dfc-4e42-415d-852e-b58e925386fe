@import '@/styles/variable.scss';

.profile-sec-wrap {
  .registration-page {
    height: 100%;
    width: 70%;

    .form-wrap {
      .left-sec-wrap {
        padding: var(--spacing-md) var(--spacing-lg) var(--spacing-none)
          var(--spacing-lg);
        margin-bottom: var(--spacing-xxl);
        .update-btn {
          width: 120px;
        }
        .upload-image-sec {
          .logo-section-wrap {
            padding: 0px;
          }
        }
        .org-profile {
          border-bottom: var(--normal-sec-border);
          padding-bottom: var(--spacing-lg);
        }
        .admin-form-title {
          font-family: var(--font-family-primary);
          color: var(--text-color-black);
          font-size: var(--font-size-base);
          font-weight: var(--font-weight-semibold);
          margin-top: var(--spacing-lg);
        }
        .form-grid {
          margin-top: var(--spacing-md);
          display: grid;
          grid-template-columns: calc(50% - 9px) calc(50% - 9px);
          grid-gap: 18px;
          gap: 18px;
          @media (max-width: 575px) {
            grid-template-columns: 100%;
          }
        }
        .input-field-wrap {
          .MuiInputBase-root {
            min-height: 38px;

            .MuiInputBase-input {
              padding: 7.5px 16px;
            }
          }

          .phone-county-wrap {
            .country-select-adornment {
              margin-right: 0px;
            }
          }
        }

        .upload-image-sec {
          gap: 30px;

          .image-wrap {
            border-radius: 50px;
          }

          .btns-wrap {
            gap: 15px;

            .upload-photo-btn,
            .delete-btn {
              font-weight: 500;
              padding: 7px 12px !important;
              font-size: 15px !important;

              &:hover {
                box-shadow: none !important;
              }
            }

            .upload-photo-btn {
              &:hover {
                color: $color-secondary !important;
              }
            }

            .delete-btn {
              background: $color-secondary !important;
              color: $color-primary !important;
            }
          }
        }

        .registration-wrap {
          text-align: center;
          color: $color-primary;
        }

        .register-btn {
          width: 100%;
          max-width: 340px;
          padding: 7px 24px !important;

          &:hover {
            color: white !important;
          }

          @media (max-width: 767px) {
            width: 100%;
            max-width: 100%;
          }
        }

        @media (max-width: 1200px) {
          width: 100%;
        }
        @media (max-width: 575px) {
          padding: var(--spacing-md) var(--spacing-none) var(--spacing-none)
            var(--spacing-none);
        }
      }
    }
    @media (max-width: 1200px) {
      width: 100%;
    }
  }

  .eye-wrap {
    padding: 10px;
    line-height: 0px;
  }

  @media (max-width: 1200px) {
    flex-direction: column;
  }
}

.country-select-adornment {
  display: flex;
  align-items: center;

  .country-select {
    min-width: 80px;
    margin-right: 8px;
  }
}

.display-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  column-gap: 15px;
  row-gap: 15px;

  @media (max-width: 767px) {
    grid-template-columns: repeat(1, 1fr);
  }
}
