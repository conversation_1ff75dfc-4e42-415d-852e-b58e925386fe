import { useState, useEffect, useRef } from 'react';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin from '@fullcalendar/interaction';
import resourceTimelinePlugin from '@fullcalendar/resource-timeline';
import timeGridPlugin from '@fullcalendar/timegrid';
import { Box, Typography, Divider } from '@mui/material';
import EastIcon from '@mui/icons-material/East';
import moment from 'moment';
import multiMonthPlugin from '@fullcalendar/multimonth';
import UserAvatar from '@/components/UI/Avatar/UserAvatar';
import { CalenderIcon } from '@/helper/common/images';
import { identifiers } from '@/helper/constants/identifier';
import dayjs from 'dayjs';
import CustomSearch from '@/components/UI/CustomSearch';
import './doublecalender.scss';
const CustomDoubleCalender = ({
  setSelectedDate,
  selectedDate,
  getLeaveDetails,
  calenderData,
  userCanView,
  selectedLeave,
  userDetails,
  type,
}) => {
  const [extraEventsData, setExtraEventsData] = useState([]);
  const [filteredEvents, setFilteredEvents] = useState(extraEventsData || []);
  const [calenderSearch, setCalenderSearch] = useState('');
  // const [calendarDate, setCalendarDate] = useState(null);
  const calendarRef = useRef(null);
  const holidayCount = filteredEvents.filter(
    (item) => item.extendedProps.eventType === 'holiday'
  ).length;
  const leaveCount = filteredEvents.filter(
    (item) => item.extendedProps.eventType === 'leave'
  ).length;

  // const handleDateSelect = (selectInfo) => {
  //   const selectedStart = selectInfo.start;
  //   setSelectedDate(selectedStart);

  //   const eventsForDate = events.filter(
  //     (event) =>
  //       moment(event.start).format('YYYY-MM-DD') ===
  //       moment(selectedStart).format('YYYY-MM-DD')
  //   );
  //   setFilteredEvents(eventsForDate);

  //   if (onDateSelect) onDateSelect(selectInfo);
  // };

  // useEffect(() => {
  //   if (userDetails) {
  //     setCalendarDate(moment(userDetails).format('YYYY-MM-DD'));
  //   }
  // }, [userDetails]);

  useEffect(() => {
    if (calendarRef?.current && userDetails && type === 'apply-leave') {
      const calendarApi = calendarRef?.current?.getApi(); // Access the calendar API
      if (dayjs(userDetails).format('YYYY-MM-DD') !== 'Invalid Date') {
        calendarApi?.gotoDate(dayjs(userDetails).format('YYYY-MM-DD'));
      }
    }
  }, [userDetails, selectedLeave]);

  const handleDateRangeChange = (info) => {
    const { view, start } = info;

    const dateData = {
      Start: '',
      End: '',
      type: view?.type,
    };
    switch (view?.type) {
      case 'multiMonthYear':
        // Set year data
        dateData.Start = moment(start).format('YYYY-MM-DD');
        dateData.End = moment(start)
          .add(1, 'month')
          .endOf('month')
          .format('YYYY-MM-DD');
        if (userCanView && selectedLeave) {
          getLeaveDetails(dateData);
        }
        // setViewData(dateData);
        break;
      default:
        // Reset data if no match
        dateData.Start = '';
        dateData.End = '';
    }
  };
  // useEffect(() => {
  //   if (viewData) {
  //     // getLeaveDetails(viewData);
  //   }
  // }, [viewData]);

  // const handleMoreClick = (arg) => {
  //   arg.jsEvent.preventDefault();
  //   arg.jsEvent.stopPropagation();

  //   // MutationObserver to remove .fc-popover dynamically when it appears
  //   const observer = new MutationObserver((mutationsList, observer) => {
  //     document.querySelectorAll('.fc-popover').forEach((el) => el.remove());
  //   });

  //   // Start observing changes in the body
  //   observer.observe(document.body, { childList: true, subtree: true });

  //   let eventsForDate = arg?.allSegs?.map((seg) => seg?.event?._def);

  //   setSelectedDate(arg?.date);
  //   setExtraEventsData(eventsForDate);
  //   setFilteredEvents(eventsForDate);

  //   return false;
  // };

  const handleMoreClick = (arg) => {
    arg.jsEvent.preventDefault();
    arg.jsEvent.stopPropagation();

    // MutationObserver to remove .fc-popover dynamically when it appears
    const observer = new MutationObserver(() => {
      document.querySelectorAll('.fc-popover').forEach((el) => el.remove());
    });

    // Start observing changes in the body
    observer.observe(document.body, { childList: true, subtree: true });

    let eventsForDate = arg?.allSegs?.map((seg) => seg?.event?._def);
    setTimeout(() => {
      setSelectedDate(arg?.date);
      setExtraEventsData(eventsForDate);
      setFilteredEvents(eventsForDate);
    }, 0); // You can adjust the timeout duration if needed

    return false;
  };

  // const nonHolidayCount = filteredEvents?.filter(
  //   (event) => event?.extendedProps?.eventType !== 'holiday'
  // )?.length;

  const renderEventContentDetails = (eventInfo) => {
    const { event } = eventInfo;
    const isHoliday = event.extendedProps.eventType === 'holiday';
    const userEvents = event?.extendedProps;
    if (isHoliday) {
      return (
        <>
          <Typography className="year-holiday">
            <span className="holiday-dots"></span>
          </Typography>
          {userEvents ? (
            <Box className="events-wrap">
              <Box className="event-container-wrap" key={userEvents?.id}></Box>
            </Box>
          ) : (
            <></>
          )}
        </>
      );
    }
    return (
      <Box className="events-wrap">
        <Box className="event-container-wrap" key={userEvents?.id}>
          <Typography
            className="event-title"
            onClick={() => {
              setSelectedDate(userEvents?.start_date);
              setExtraEventsData([event]);
              setFilteredEvents([event]);
            }}
          >
            +1 more
          </Typography>
        </Box>
      </Box>
    );
  };

  const highlightInitialDate = (date) => {
    return moment(date).format('YYYY-MM-DD') ===
      moment(userDetails).format('YYYY-MM-DD')
      ? 'highlight-date'
      : '';
  };

  const headerToolbarConfig = {
    left: 'prev,next',
    center: 'title',
    right: 'today',
  };

  useEffect(() => {
    if (!calenderSearch) {
      setFilteredEvents(extraEventsData);
    } else {
      const lowerSearch = calenderSearch ? calenderSearch?.toLowerCase() : '';
      const filtered = extraEventsData?.filter((eventItem) => {
        const item = eventItem?.extendedProps;

        return (
          item.request_from_users?.user_full_name
            ?.toLowerCase()
            .includes(lowerSearch) ||
          item.role?.role_name?.toLowerCase().includes(lowerSearch) ||
          item.request_type?.toLowerCase().includes(lowerSearch) ||
          item.request_status?.toLowerCase().includes(lowerSearch) ||
          item?.holiday_policy_name?.toLowerCase().includes(lowerSearch)
        );
      });
      setFilteredEvents(filtered);
    }
  }, [calenderSearch, extraEventsData?.length]);
  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      //
    }
  };
  return (
    <Box className="double-calender">
      <Box className="header-text-wrap">
        {userCanView || !selectedLeave ? (
          <>
            <Typography variant="h6" className="mb16 body-text">
              See who else is on leave
            </Typography>
          </>
        ) : (
          <>
            <Typography variant="h6" className="mb16 body-text color-red">
              You cannot view the leaves of other users
            </Typography>
          </>
        )}
      </Box>
      <Box className="double-calender-wrap">
        {((userDetails && type === 'staff') ||
          (userDetails && type === 'own')) && (
          <FullCalendar
            plugins={[
              resourceTimelinePlugin,
              dayGridPlugin,
              interactionPlugin,
              timeGridPlugin,
              multiMonthPlugin,
            ]}
            headerToolbar={headerToolbarConfig}
            initialDate={userDetails}
            initialView="multiMonthYear"
            events={
              userCanView
                ? [
                    ...calenderData?.calenderData?.holidayEvents,
                    ...calenderData?.calenderData?.coloredEvents,
                  ]
                : []
            }
            eventOrder={['holiday', 'leave']}
            moreLinkClick={handleMoreClick}
            eventLimit={true}
            datesSet={handleDateRangeChange}
            eventContent={renderEventContentDetails}
            dayCellClassNames={(arg) => highlightInitialDate(arg.date)}
            views={{
              multiMonthYear: {
                type: 'multiMonth',
                duration: { month: 2 },
                buttonText: '2 Months',
                multiMonthColumns: 2,
              },
            }}
            height="auto"
            handleWindowResize={true}
            dayMaxEvents={1}
          />
        )}
        {type === 'apply-leave' && (
          <FullCalendar
            ref={calendarRef}
            plugins={[
              resourceTimelinePlugin,
              dayGridPlugin,
              interactionPlugin,
              timeGridPlugin,
              multiMonthPlugin,
            ]}
            headerToolbar={headerToolbarConfig}
            initialDate={
              userDetails ? userDetails : moment().format('YYYY-MM-DD')
            }
            initialView="multiMonthYear"
            events={
              userCanView
                ? [
                    ...calenderData?.calenderData?.holidayEvents,
                    ...calenderData?.calenderData?.coloredEvents,
                  ]
                : []
            }
            eventOrder={['holiday', 'leave']}
            moreLinkClick={handleMoreClick}
            eventLimit={true}
            datesSet={handleDateRangeChange}
            eventContent={renderEventContentDetails}
            views={{
              multiMonthYear: {
                type: 'multiMonth',
                duration: { month: 2 },
                buttonText: '2 Months',
                multiMonthColumns: 2, // Display two months horizontally
              },
            }}
            height="auto"
            handleWindowResize={true}
            dayMaxEvents={1}
          />
        )}
      </Box>
      <Box className="leave-type-wrap d-flex align-center pb16">
        {identifiers?.LEAVE_TYPES?.map((type, index) => (
          <Box
            key={index}
            className={`d-flex align-center gap-5 ${type?.className} leave-type title-text`}
          >
            <span className={`${type?.colorClass} dot-wrap`}></span>
            {type?.label}
          </Box>
        ))}
      </Box>
      <Divider />
      {userCanView ? (
        <>
          {' '}
          {selectedDate && (
            <Box className="selected-date-details-wrap">
              <Box className="selected-date-details">
                <Box className="show-date-wrap d-flex align-center justify-space-between">
                  <Box className="date-show-wrap d-flex align-center">
                    <Typography variant="h3" className="show-date-text-wrap">
                      Data for{' '}
                      {moment(selectedDate).format('dddd, D MMMM YYYY')}
                    </Typography>
                    <Typography component="p" className="person-count-wrap">
                      {/* {holidayCount > 0
                        ? `${holidayCount} Holiday`
                        : `${leaveCount} Person on Leave`} */}
                      {`${holidayCount} Holiday, ${leaveCount} Person on Leave`}
                    </Typography>
                  </Box>
                  <Box className="calender-search-wrap">
                    <CustomSearch
                      searchValue={calenderSearch}
                      setSearchValue={setCalenderSearch}
                      onKeyPress={handleKeyPress}
                    />
                  </Box>
                </Box>
                {filteredEvents?.length > 0 ? (
                  <Box className="event-list-wrap">
                    {filteredEvents?.map((ev) => {
                      const event = ev?.extendedProps;
                      if (ev?.extendedProps?.eventType !== 'holiday') {
                        return (
                          <Box
                            className="d-flex event-card gap-10"
                            key={event?.id}
                          >
                            <Box className="avatar-wrap">
                              <UserAvatar
                                name={event?.request_from_users?.user_full_name}
                                src={
                                  event?.request_from_users?.user_avatar_link
                                }
                                classname="user-avarar"
                              />
                            </Box>
                            <Box className="w100">
                              <Box className="d-flex align-center justify-space-between">
                                <Typography
                                  variant="h6"
                                  className="event-title-wrap body-text"
                                >
                                  {event?.request_from_users?.user_full_name}
                                </Typography>
                                {/* <ArrowOutwardIcon
                            onClick={() => {
                              if (calenderData?.type === 'staff') {
                                router.push(`/leave-remark/${ev?.publicId}`);
                              } else {
                                router.push(`/own-leave/${ev?.publicId}`);
                              }
                            }}
                            className="right-arrow-icon cursor-pointer"
                          /> */}
                              </Box>
                              <Typography
                                component="p"
                                className="d-flex align-center event-date-wrap title-text"
                              >
                                {moment(event?.start_date).format('ddd, D MMM')}
                                <EastIcon className="east-icon" />
                                {moment(event?.end_date).format('ddd, D MMM')}
                              </Typography>
                            </Box>
                          </Box>
                        );
                      } else {
                        return (
                          <>
                            <Box
                              className="d-flex event-card gap-10"
                              key={event?.id}
                            >
                              <Box className="w100">
                                <Box className="d-flex align-center justify-space-between">
                                  <Typography
                                    variant="h6"
                                    className="event-title-wrap body-text text-ellipsis-line"
                                  >
                                    {event?.holiday_policy_name}
                                  </Typography>
                                  <Typography
                                    className="holiday-text fw600 sub-title-text"
                                    variant="h6"
                                  >
                                    Holiday
                                  </Typography>
                                </Box>
                                <Typography
                                  component="p"
                                  className="d-flex align-center event-date-wrap title-text"
                                >
                                  {moment(
                                    event?.holiday_policy_start_date
                                  ).format('ddd, D MMM')}

                                  <EastIcon className="east-icon" />
                                  {moment(
                                    event?.holiday_policy_end_date
                                  ).format('ddd, D MMM')}
                                </Typography>
                              </Box>
                            </Box>
                          </>
                        );
                      }
                    })}
                  </Box>
                ) : (
                  <Box className="d-flex justify-center align-center flex-col calender-leave-wrap">
                    <CalenderIcon />
                    <Box className="calender-text-wrap text-align">
                      <Typography
                        variant="h6"
                        className="no-booked-wrap body-text"
                      >
                        No booked leave
                      </Typography>
                      <Typography
                        component="p"
                        className="calender-text title-text"
                      >
                        Nobody has been given leave for this date yet.
                      </Typography>
                    </Box>
                  </Box>
                )}
              </Box>
            </Box>
          )}
        </>
      ) : (
        <></>
      )}
    </Box>
  );
};

export default CustomDoubleCalender;
