'use client';
import React, { useEffect, useState } from 'react';
import { MenuItem, Select, InputAdornment } from '@mui/material';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { ORG_URLS } from '@/helper/constants/urls';
import { setApiMessage } from '@/helper/common/commonFunctions';
import CustomTextField from '@/components/UI/CustomTextField';
import './customcountrycode.scss';

const CustomCountryCode = ({
  values,
  setFieldValue,
  touched,
  errors,
  handleBlur,
  handleChange,
  selectedCountryId,
  setSelectedCountryId,
}) => {
  const [countryCode, setCountryCode] = useState([]);

  const fetchCountryCode = async () => {
    try {
      const { status, data } = await axiosInstance.get(ORG_URLS.GET_COUNTRY);

      if (status === 200) {
        setCountryCode(data?.data);
      } else {
        setApiMessage('error', data?.message);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  useEffect(() => {
    fetchCountryCode();
  }, []);

  const handleCountryChange = (event) => {
    const selectedCode = event?.target?.value;
    const selectedCountry = countryCode?.find((country) => {
      return country?.id === selectedCode;
    });
    setSelectedCountryId(selectedCountry?.id);
    setFieldValue('country_code', selectedCode);
  };

  useEffect(() => {
    if (!values.country_code && countryCode?.length > 0) {
      const defaultCountry = countryCode?.find(
        (country) => country?.id === '3'
      );
      if (defaultCountry) {
        setFieldValue('country_code', defaultCountry?.id);
        setSelectedCountryId(defaultCountry?.id);
      }
    }
  }, [countryCode, values.country_code, setFieldValue, selectedCountryId]);
  return (
    <CustomTextField
      InputProps={{
        startAdornment: (
          <InputAdornment position="start" className="country-select-adornment">
            <Select
              value={values?.country_code}
              onChange={handleCountryChange}
              displayEmpty
              inputProps={{ 'aria-label': 'Country code' }}
              className="country-select"
            >
              {countryCode?.map((country, index) => {
                return (
                  <MenuItem key={index} value={country?.id} className="p14">
                    {`${country?.country_region_code} (${country?.country_code})`}
                  </MenuItem>
                );
              })}
            </Select>
          </InputAdornment>
        ),
      }}
      id="phone_number"
      name="phone_number"
      label="Phone Number"
      required
      placeholder="Phone number"
      className="phone-county-wrap"
      value={values?.phone_number}
      error={Boolean(touched?.phone_number && errors?.phone_number)}
      helperText={touched?.phone_number && errors?.phone_number}
      onBlur={handleBlur}
      onChange={handleChange}
      fullWidth
      onInput={(e) => {
        e.target.value = e.target.value.replace(/[^0-9]/g, ''); // Remove non-numeric characters
      }}
    />
  );
};

export default CustomCountryCode;
