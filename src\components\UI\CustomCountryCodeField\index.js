'use client';
import React, { useEffect, useState } from 'react';
import {
  MenuItem,
  Select,
  InputAdornment,
  TextField,
  styled,
} from '@mui/material';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { ORG_URLS } from '@/helper/constants/urls';
import { setApiMessage } from '@/helper/common/commonFunctions';
import './phonenumberwithcountrycode.scss';

const CustomTextField = styled(TextField)(() => ({
  '& .MuiInputBase-root': {
    border: '1px solid #d9dae2', // Border for the TextField
    minHeight: '48px',
    borderRadius: '8px',
    background: '#fff',
    '&:before': {
      border: '0',
      display: 'none',
    },
    '&:after': {
      border: '0',
    },
    '&:hover': {
      background: '#fff',
    },
  },
  '& .Mui-error': {
    color: '#d32f2f !important',
    fontFamily: 'Inter, sans-serif !important',
    fontSize: '12px !important',
    lineHeight: '18px !important',
    letterSpacing: '-0.5px !important',
    fontWeight: '600 !important',
    '& .MuiFilledInput-input': {
      border: '1px solid #d32f2f !important',
    },
  },
  '& .MuiOutlinedInput-input': {
    padding: '0',
  },
  '& .MuiOutlinedInput-notchedOutline': {
    display: 'none !important',
  },
  '& legend': {
    display: 'none !important',
  },
  '& .MuiInputBase-input': {
    fontSize: '16px',
    fontWeight: '400',
    lineHeight: '24px',
    color: '#33343a !important',
    padding: '12.5px 16px 12.5px 0px',
    borderRadius: '8px',
    fontFamily: 'Inter, sans-serif',
  },
  '& ::placeholder': {
    color: '#A6A8B1',
    opacity: 1,
    fontWeight: 400,
    textTransform: 'capitalize',
    fontSize: '15px',
    fontFamily: 'Inter, sans-serif',
    lineHeight: '18px',
  },
  '& .MuiInputBase-root.Mui-focused': {
    border: '1px solid #d9dae2',
  },
  '& .MuiInputBase-root.Mui-error': {
    border: '1px solid red !important',
  },
  '& .MuiInputLabel-root': {
    fontSize: '12px',
    color: '#33343a',
    fontFamily: 'Inter, sans-serif',
    lineHeight: '18px',
    marginBottom: '2px',
    fontWeight: '600',
    top: '-20px',
    left: '2px',
    transform: 'inherit',
    letterSpacing: '-0.5px',
    textTransform: 'capitalize',
  },
  '& .MuiInputLabel-root.Mui-focused': {
    color: '#39596e',
  },
  '& .MuiFormLabel-root.Mui-disabled': {
    background: 'transparent !important',
    color: '#000000 !important',
  },
  '& .Mui-disabled': {
    cursor: 'not-allowed',
    background: '#FFFFFF',
    color: '#000000 !important',
    opacity: 0.9,
    WebkitTextFillColor: '#000000 !important',
  },
}));

const PhoneNumberWithCountryCode = ({
  values,
  setFieldValue,
  touched,
  errors,
  handleBlur,
  handleChange,
  selectedCountryId,
  setSelectedCountryId,
  placeholder,
}) => {
  const [countryCode, setCountryCode] = useState([]);

  const fetchCountryCode = async () => {
    try {
      const { status, data } = await axiosInstance.get(ORG_URLS.GET_COUNTRY);

      if (status === 200) {
        setCountryCode(data?.data);
      } else {
        setApiMessage('error', data?.message);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  useEffect(() => {
    fetchCountryCode();
  }, []);

  const handleCountryChange = (event) => {
    const selectedCode = event?.target?.value;
    const selectedCountry = countryCode?.find((country) => {
      return country?.id === selectedCode;
    });
    setSelectedCountryId(selectedCountry?.id);
    setFieldValue('country_code', selectedCode);
  };

  useEffect(() => {
    if (!values.country_code && countryCode?.length > 0) {
      const defaultCountry = countryCode?.find(
        (country) => country?.id === '3'
      );
      if (defaultCountry) {
        setFieldValue('country_code', defaultCountry?.id);
        setSelectedCountryId(defaultCountry?.id);
      }
    }
  }, [countryCode, values.country_code, setFieldValue, selectedCountryId]);
  return (
    <CustomTextField
      InputProps={{
        startAdornment: (
          <InputAdornment position="start" className="country-select-adornment">
            <Select
              value={values?.country_code}
              onChange={handleCountryChange}
              displayEmpty
              inputProps={{ 'aria-label': 'Country code' }}
              className="country-select"
            >
              {countryCode?.map((country, index) => {
                return (
                  <MenuItem key={index} value={country?.id} className="p14">
                    {`${country?.country_region_code} (${country?.country_code})`}
                  </MenuItem>
                );
              })}
            </Select>
          </InputAdornment>
        ),
      }}
      id="phone_number"
      name="phone_number"
      label="Phone Number*"
      placeholder={placeholder ? placeholder : 'Phone number'}
      className="phone-county-wrap"
      value={values?.phone_number}
      error={Boolean(touched?.phone_number && errors?.phone_number)}
      helperText={touched?.phone_number && errors?.phone_number}
      onBlur={handleBlur}
      onChange={handleChange}
      fullWidth
      onInput={(e) => {
        e.target.value = e.target.value.replace(/[^0-9]/g, ''); // Remove non-numeric characters
      }}
    />
  );
};

export default PhoneNumberWithCountryCode;
