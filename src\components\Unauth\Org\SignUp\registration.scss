@import '@/styles/variable.scss';

.registration-page-container {
  position: relative;

  .registration-page-wrap {
    width: 100%;
    max-width: 790px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
    padding: 0px 14px;

    ::-webkit-scrollbar {
      display: none !important;
    }

    .registration-page {
      background-color: var(--color-white);
      box-shadow: 0px 0px 110px 0px #0260e133;
      height: 100%;
      max-height: 821px;
      overflow: auto;
      padding: 47px 82px 0px;
      border-top-left-radius: 22px;
      border-top-right-radius: 22px;

      .form-wrap {
        .registration-wrap {
          padding-bottom: 32px;

          @media (max-width: 575px) {
            padding-bottom: 25px;
          }
        }

        .input-field-wrap {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 10px;

          .input-wrap {
            padding-bottom: 18px;

            .phone-input-wrap {
              border-bottom: 1px solid #e2e2ea;

              .phone-county-wrap {
                .MuiFormLabel-root {
                  display: none;
                }

                .MuiInputBase-root {
                  border: none;
                  border-radius: 0px;
                  padding-left: 0px;
                  min-height: 40px;

                  .MuiInputAdornment-root {
                    margin: 0px 0px 7px;

                    .MuiInputBase-root {
                      border: none !important;

                      .MuiSelect-select {
                        margin: 0px 30px 0px 0px;
                      }
                    }
                  }

                  .MuiInputBase-input {
                    min-height: 25px !important;
                  }
                }
              }

              .phone-icon {
                padding: 0px 9px;
              }
              .phone-icon-wrap {
                padding: 0px 8px;
                svg {
                  path {
                    stroke: var(--color-danger) !important;
                  }
                }
              }
            }

            .error {
              border-bottom: 1px solid var(--color-danger) !important;
            }

            .error-message {
              font-family: var(--font-family-poly-slim) !important;
              font-weight: 400;
              font-size: 12px;
              line-height: 1.66;
              letter-spacing: 0.03333em;
              text-align: left;
              margin-top: 3px;
            }

            .MuiInputBase-root {
              display: flex;
              align-items: center;

              .eye-wrap {
                svg {
                  cursor: pointer;
                }
              }

              .MuiInputBase-input {
                font-size: 16px;
                line-height: 26px;
                font-weight: 400;
                margin: 5px 0px 10px;
                height: 100%;
                min-height: 26px;
                padding: 0px;
                color: #1d1e25 !important;
                font-family: var(--font-family-poly-slim) !important;

                .image-wrap {
                  height: 24px;
                  width: 24px;
                }
                &::placeholder {
                  font-size: 16px !important;
                  line-height: 26px !important;
                  font-weight: 400 !important;
                  margin: 5px 0px 10px !important;
                  padding: 0px !important;
                  font-family: var(--font-family-poly-slim) !important;
                  text-transform: capitalize !important;
                }
              }

              &::before {
                border-bottom: 1px solid #e2e2ea;
              }
            }

            .Mui-error {
              font-family: var(--font-family-poly-slim) !important;

              .eye-wrap {
                cursor: none !important;

                path {
                  stroke: var(--color-danger);
                }

                svg {
                  fill: none;
                }
              }

              // .verify-eye-wrap {
              //     path {
              //         stroke: $error;
              //     }

              //     svg {
              //         fill: none;
              //     }
              // }

              &::before {
                border-bottom: 1px solid var(--color-danger) !important;
              }
            }
          }

          @media (max-width: 767px) {
            grid-template-columns: repeat(1, 1fr);
          }
        }

        .terms-condition-wrap {
          padding-bottom: 32px;

          .switch-wrap {
            .MuiSwitch-root {
              margin-left: 10px;
            }

            .MuiFormControlLabel-root {
              margin-right: 0px;
            }
          }

          .terms-condition {
            margin-left: 6px;

            .text-wrap {
              font-weight: 300 !important;
              font-family: var(--font-family-poly-neutral) !important;
              color: var(--text-color-primary) !important;
            }
          }
        }

        .registration-btn-wrap {
          .sign-up-btn {
            border-radius: 12px !important;
            background-color: var(--color-primary) !important;
            font-size: 16px !important;
            line-height: 20px !important;
            font-weight: 300 !important;
            padding: 18px !important;
            border: none !important;
            color: var(--text-color-white) !important;
            font-family: var(--font-family-poly-median) !important;

            &:hover {
              background-color: var(--color-primary) !important;
              color: var(--text-color-white) !important;
              box-shadow: none !important;
              font-family: var(--font-family-poly-median) !important;
            }
          }
        }

        .already-have-text {
          padding: 22px 0px;
          text-align: center;
          font-size: 16px;
          line-height: 26px;
          font-weight: 300;
          color: var(--text-lavender-gray);
          font-family: var(--font-family-poly-slim) !important;

          .login-wrap {
            cursor: pointer;
            font-family: var(--font-family-poly-median) !important;
            color: var(--text-color-primary) !important;
            font-weight: 300;
            margin-left: 6px;
          }
        }
      }

      @media (max-width: 840px) {
        padding: 40px 40px 0px;
      }

      @media (max-width: 575px) {
        padding: 20px 25px 0px;
      }

      @media (max-width: 375px) {
        padding: 20px 20px 0px;
      }
    }
  }

  .left-vector {
    position: absolute;
    left: 30px;
    top: 103px;
    z-index: 0;
    .sign-up-left-vector {
      height: 100%;
      width: 100%;
    }
  }

  .right-vector {
    position: absolute;
    right: 10px;
    top: 215px;
    z-index: 0;
  }
}
