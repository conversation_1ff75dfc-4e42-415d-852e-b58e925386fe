'use client';
import React, { useState } from 'react';
import { Form, Formik } from 'formik';
import * as Yup from 'yup';
import {
  Box,
  Typography,
  useTheme,
  useMediaQuery,
  Paper,
  IconButton,
  Alert,
  Divider,
  InputAdornment,
} from '@mui/material';
import { useRouter } from 'next/navigation';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import VisibilityOutlinedIcon from '@mui/icons-material/VisibilityOutlined';
import VisibilityOffOutlinedIcon from '@mui/icons-material/VisibilityOffOutlined';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomSelect from '@/components/UI/CustomSelect';
import CustomCheckbox from '@/components/UI/CustomCheckbox';
import CustomButton from '@/components/UI/CustomButton';
import Icon from '@/components/UI/AppIcon/AppIcon';
import AppImage from '@/components/UI/AppImage/AppImage';
import { supportTicketService } from '@/services/supportTicketService';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { fetchFromStorage, removeFromStorage } from '@/helper/context';
import { identifiers } from '@/helper/constants/identifier';
import './createticket.scss';

export default function CreateTicket() {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down(1500));
  const [attachments, setAttachments] = useState([]);
  const [dragOver, setDragOver] = useState(false);
  const [uploadErrors, setUploadErrors] = useState(null);
  const [showSupportPin, setShowSupportPin] = useState(false);
  const [showVerifyPin, setShowVerifyPin] = useState(false);

  const handleBackNavigation = () => {
    // Check if there's saved filter data from AllTicketsList
    const redirectData = fetchFromStorage(identifiers?.RedirectData);

    if (redirectData && redirectData?.IsFromUser) {
      // Clear the redirect data since we're going back
      removeFromStorage(identifiers?.RedirectData);
      // Navigate back to all tickets list (filters will be restored automatically)
      router.push('/support-ticket/all-tickets');
    } else {
      // No saved data, navigate normally
      router.push('/support-ticket/all-tickets');
    }
  };

  const handleToggleSupportPinVisibility = () => {
    setShowSupportPin(!showSupportPin);
  };

  const handleToggleVerifyPinVisibility = () => {
    setShowVerifyPin(!showVerifyPin);
  };

  const categoryOptions = [
    { label: 'HRMS', value: 'hrms' },
    { label: 'PMS', value: 'pms' },
    { label: 'Other', value: 'other' },
  ];

  const issueTypeOptions = [
    { label: 'Bug Report', value: 'bug_report' },
    { label: 'Feature Request', value: 'feature_request' },
    { label: 'General Inquiry', value: 'general_inquiry' },
    { label: 'Export Help', value: 'export_help' },
    { label: 'Technical Issue', value: 'technical_issue' },
    { label: 'Non-Technical Issue', value: 'non_technical_issue' },
    { label: 'Account Issue', value: 'account_issue' },
    { label: 'Billing', value: 'billing' },
    { label: 'Performance', value: 'performance' },
    { label: 'Notifications', value: 'notifications' },
    { label: 'Support', value: 'support' },
  ];

  const priorityOptions = [
    { label: 'None', value: 'none' },
    { label: 'Low', value: 'low' },
    { label: 'Medium', value: 'medium' },
    { label: 'High', value: 'high' },
    { label: 'Urgent', value: 'urgent' },
  ];

  // File validation constraints
  const FILE_CONSTRAINTS = {
    maxSize: 25 * 1024 * 1024, // 25MB
    types: [
      'image/jpeg',
      'image/png',
      'image/webp',
      'image/gif',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'application/zip',
      'application/x-zip-compressed',
    ],
    maxFiles: 5,
  };

  // Subject text limit
  const SUBJECT_MAX_LENGTH = 120;

  // Get appropriate icon for file type
  const getFileTypeIcon = (file) => {
    const fileType = file?.type?.toLowerCase() || '';
    const fileName = file?.name?.toLowerCase() || '';

    // Images
    if (fileType.startsWith('image/')) {
      return { name: 'Image', color: '#10B981' };
    }

    // PDF files
    if (fileType === 'application/pdf' || fileName.endsWith('.pdf')) {
      return { name: 'FileText', color: '#EF4444' };
    }

    // Word documents
    if (
      fileType.includes('word') ||
      fileName.endsWith('.doc') ||
      fileName.endsWith('.docx')
    ) {
      return { name: 'FileText', color: '#2563EB' };
    }

    // Text files
    if (fileType === 'text/plain' || fileName.endsWith('.txt')) {
      return { name: 'FileText', color: '#6B7280' };
    }

    // Zip files
    if (fileType.includes('zip') || fileName.endsWith('.zip')) {
      return { name: 'Archive', color: '#F59E0B' };
    }

    // Default file icon
    return { name: 'File', color: '#6B7280' };
  };

  // Validate file constraints
  const validateFile = (file) => {
    const errors = [];

    // Check file type
    if (!FILE_CONSTRAINTS.types.includes(file?.type)) {
      errors.push(`File type ${file?.type} not supported`);
    }

    // Check file size
    if (file?.size > FILE_CONSTRAINTS.maxSize) {
      const maxSizeMB = (FILE_CONSTRAINTS.maxSize / 1024 / 1024).toFixed(0);
      errors.push(`File size exceeds ${maxSizeMB}MB limit`);
    }

    return errors;
  };

  const handleFileUpload = (files) => {
    if (!files?.length) return;
    setUploadErrors(null);

    const fileArray = Array.from(files);
    const validFiles = [];
    const fileErrors = [];

    // Check if adding files would exceed limit
    const availableSlots = FILE_CONSTRAINTS.maxFiles - attachments.length;
    if (fileArray.length > availableSlots && availableSlots > 0) {
      fileErrors.push(
        `Cannot add ${fileArray.length} files. Only ${availableSlots} slots available (${attachments.length}/${FILE_CONSTRAINTS.maxFiles} used).`
      );
    } else if (availableSlots <= 0) {
      fileErrors.push(
        `Cannot add more files. Maximum limit reached (${attachments.length}/${FILE_CONSTRAINTS.maxFiles}).`
      );
    }

    // Validate each file
    for (let i = 0; i < fileArray.length; i++) {
      const file = fileArray[i];
      const errors = validateFile(file);
      if (errors.length > 0) {
        fileErrors.push(
          `File ${i + 1} (${file?.name || 'unknown'}): ${errors.join(', ')}`
        );
      } else {
        validFiles.push(file);
      }
    }

    if (fileErrors.length > 0) {
      setUploadErrors(fileErrors);
      return;
    }

    if (validFiles.length > 0) {
      const filesToProcess = validFiles.slice(0, availableSlots);
      const newAttachments = filesToProcess.map((file) => ({
        id: Date.now() + Math.random(),
        name: file.name,
        size: file.size,
        type: file.type,
        url: URL.createObjectURL(file),
        file: file,
      }));

      setAttachments((prev) => [...prev, ...newAttachments]);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setDragOver(false);
    handleFileUpload(e.dataTransfer.files);
  };

  const removeFile = (fileId) => {
    setAttachments((prev) => prev.filter((file) => file.id !== fileId));
    setUploadErrors(null);
  };

  const handleSubmitForm = async (values, { setSubmitting, resetForm }) => {
    try {
      setSubmitting(true);

      // Prepare ticket data object (without verify pin)
      const ticketData = {
        Subject: values.Subject,
        description: values.description,
        category: values.category,
        issueType: values.issueType,
        priority: values.priority,
        Name: values.Name,
        Email: values.Email,
        PhoneNumber: values.PhoneNumber,
        supportPin: values.supportPin,
        // Note: verifyPin is not sent to API as per requirement
      };

      // Prepare files array for service
      const files = attachments.map((attachment) => ({
        file: attachment.file,
      }));

      const result = await supportTicketService.createTicket(ticketData, files);

      if (result) {
        // Handle the complete API response object
        const successMessage = result?.message;
        setApiMessage('success', successMessage);
        // Reset form and attachments
        resetForm();
        setAttachments([]);
        setUploadErrors(null);

        // Check if there's saved filter data and navigate accordingly
        const redirectData = fetchFromStorage(identifiers?.RedirectData);
        if (redirectData && redirectData?.IsFromUser) {
          // Clear the redirect data since we're going back
          removeFromStorage(identifiers?.RedirectData);
          // Navigate back to all tickets list (filters will be restored automatically)
          router.push('/support-ticket/all-tickets');
        } else {
          // No saved data, navigate normally
          router.push('/support-ticket/all-tickets');
        }
      }
    } catch (error) {
      console.error('Error creating ticket:', error);
      setApiMessage(
        'error',
        error?.response?.data?.message ||
          'Failed to create ticket. Please try again.'
      );
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Box className="section-wrapper">
      <Box className="section-right">
        <Box className="section-right-tab-header">
          <Box className="d-flex align-center justify-space-between">
            <Box className="section-right-title d-flex align-center">
              <ArrowBackIosIcon
                className="cursor-pointer"
                onClick={handleBackNavigation}
              />
              <Typography className="sub-header-text">
                Create New Ticket
              </Typography>
            </Box>
          </Box>
          <Divider />
        </Box>
        <Box className="section-right-content">
          {/* --- FORM CONTENT STARTS HERE --- */}
          <Formik
            initialValues={{
              Subject: '',
              category: '',
              issueType: '',
              priority: '',
              description: '',
              Name: '',
              Email: '',
              PhoneNumber: '',
              supportPin: '',
              verifyPin: '',
              term_condition: false,
            }}
            validationSchema={Yup.object({
              Subject: Yup.string()
                .max(120, 'Subject must not exceed 120 characters')
                .required('Subject is required'),
              category: Yup.string().required('Category is required'),
              issueType: Yup.string().required('Issue Type is required'),
              priority: Yup.string().required('Priority is required'),
              description: Yup.string().required('Description is required'),
              Name: Yup.string().required('Name is required'),
              Email: Yup.string()
                .email('Invalid email')
                .required('Email is required'),
              PhoneNumber: Yup.string().required('Phone Number is required'),
              supportPin: Yup.string()
                .min(4, 'Support Pin must be at least 4 characters')
                .required('Support Pin is required'),
              verifyPin: Yup.string()
                .oneOf([Yup.ref('supportPin')], 'Pins must match')
                .required('Verify Pin is required'),
              term_condition: Yup.bool().oneOf(
                [true],
                'You must agree to the terms'
              ),
            })}
            onSubmit={handleSubmitForm}
          >
            {({
              values,
              errors,
              touched,
              handleBlur,
              handleChange,
              setFieldValue,
              handleSubmit,
              isSubmitting,
            }) => (
              <Form onSubmit={handleSubmit}>
                <Box className="support-ticket-wrap">
                  <Box className="support-ticket-form">
                    <Box
                      className={`ticket-form-first-row ${isMobile ? 'mobile-layout' : ''}`}
                    >
                      <Box className="subject-field-wrap">
                        <Box className="subject-label-row">
                          <Typography variant="body2" className="field-label">
                            Subject
                            <span style={{ color: 'var(--color-danger)' }}>
                              *
                            </span>
                          </Typography>
                          <Typography
                            variant="body2"
                            className="content-text-sm"
                          >
                            {(values.Subject || '').length}/{SUBJECT_MAX_LENGTH}
                          </Typography>
                        </Box>
                        <CustomTextField
                          fullWidth
                          name="Subject"
                          label=""
                          placeholder="Enter subject"
                          value={values.Subject}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          error={touched.Subject && Boolean(errors.Subject)}
                          helperText={touched.Subject && errors.Subject}
                          inputProps={{ maxLength: SUBJECT_MAX_LENGTH }}
                          required
                        />
                      </Box>

                      <Box className="category-issue-fields-wrap">
                        <Box className="category-field-wrap">
                          <CustomSelect
                            label="Category"
                            name="category"
                            placeholder="Select Category"
                            options={categoryOptions}
                            value={
                              categoryOptions?.find(
                                (opt) => opt?.value === values?.category
                              ) || ''
                            }
                            onChange={(selectedOption) =>
                              setFieldValue(
                                'category',
                                selectedOption?.value || ''
                              )
                            }
                            error={touched?.category && errors?.category}
                            helperText={touched?.category && errors?.category}
                            required
                          />
                        </Box>

                        <Box className="issue-type-field-wrap">
                          <CustomSelect
                            label="Issue Type"
                            name="issueType"
                            placeholder="Select Issue Type"
                            options={issueTypeOptions}
                            value={
                              issueTypeOptions?.find(
                                (opt) => opt?.value === values?.issueType
                              ) || ''
                            }
                            onChange={(selectedOption) =>
                              setFieldValue(
                                'issueType',
                                selectedOption?.value || ''
                              )
                            }
                            error={touched?.issueType && errors?.issueType}
                            helperText={touched?.issueType && errors?.issueType}
                            required
                          />
                        </Box>
                      </Box>
                    </Box>

                    {/* Second row: Priority */}
                    <Box
                      className={`ticket-form-second-row ${isMobile ? 'mobile-layout' : ''}`}
                    >
                      <Box className="priority-field-wrap">
                        <CustomSelect
                          label="Priority"
                          name="priority"
                          placeholder="Select Priority"
                          options={priorityOptions}
                          value={
                            priorityOptions?.find(
                              (opt) => opt?.value === values?.priority
                            ) || ''
                          }
                          onChange={(selectedOption) =>
                            setFieldValue(
                              'priority',
                              selectedOption?.value || ''
                            )
                          }
                          error={touched?.priority && errors?.priority}
                          helperText={touched?.priority && errors?.priority}
                          required
                        />
                      </Box>
                    </Box>

                    {/* Description and Attachment 50-50 Layout */}
                    <Box
                      className={`description-attachment-row ${isMobile ? 'mobile-layout' : ''}`}
                    >
                      {/* File Upload Zone */}
                      <Box className="attachment-field-wrap">
                        <Box className="ticket-upload-zone">
                          <Box className="ticket-zone-header">
                            <Typography
                              variant="h6"
                              className="body-sm attachment-label"
                            >
                              Attachments
                            </Typography>
                            <Typography
                              variant="body2"
                              className="content-text-sm"
                            >
                              {attachments.length}/{FILE_CONSTRAINTS.maxFiles}{' '}
                              files
                            </Typography>
                          </Box>

                          <Paper
                            onDragOver={handleDragOver}
                            onDragLeave={handleDragLeave}
                            onDrop={handleDrop}
                            className={`ticket-drop-area ${
                              dragOver ? 'ticket-drop-area--drag-over' : ''
                            } ${
                              attachments.length >= FILE_CONSTRAINTS.maxFiles
                                ? 'ticket-drop-area--disabled'
                                : ''
                            }`}
                            elevation={0}
                            component="label"
                            sx={{ cursor: 'pointer' }}
                          >
                            <input
                              type="file"
                              multiple={true}
                              accept={FILE_CONSTRAINTS.types.join(',')}
                              onChange={(e) => handleFileUpload(e.target.files)}
                              className="ticket-drop-input"
                              disabled={
                                attachments.length >= FILE_CONSTRAINTS.maxFiles
                              }
                              style={{ display: 'none' }}
                            />

                            <Box className="ticket-drop-content">
                              <Icon
                                name="Upload"
                                size={32}
                                className="ticket-drop-icon"
                              />
                              <Box className="ticket-drop-text">
                                <Typography
                                  variant="body1"
                                  className="body-sm ticket-drop-placeholder-primary"
                                >
                                  {dragOver
                                    ? 'Drop files here'
                                    : 'Drag & drop files or click to browse'}
                                </Typography>
                                <Typography
                                  variant="body2"
                                  className="content-text-sm"
                                >
                                  Images, PDFs, Documents up to 25MB each
                                </Typography>
                              </Box>
                            </Box>
                          </Paper>

                          {/* File Preview Grid */}
                          {attachments.length > 0 && (
                            <Box className="ticket-preview-grid">
                              {attachments.map((file) => (
                                <Paper
                                  key={file.id}
                                  className="ticket-preview-item"
                                  elevation={1}
                                >
                                  <Box className="ticket-preview-container">
                                    {file.type.startsWith('image/') ? (
                                      <AppImage
                                        src={file.url}
                                        alt={file.name || 'Preview'}
                                        className="ticket-preview-image"
                                      />
                                    ) : (
                                      <Box className="ticket-preview-placeholder">
                                        {(() => {
                                          const iconData =
                                            getFileTypeIcon(file);
                                          return (
                                            <Icon
                                              name={iconData.name}
                                              size={24}
                                              color={iconData.color}
                                            />
                                          );
                                        })()}
                                      </Box>
                                    )}
                                  </Box>

                                  <IconButton
                                    size="small"
                                    className="ticket-preview-remove"
                                    onClick={() => removeFile(file.id)}
                                  >
                                    <Icon name="X" size={16} />
                                  </IconButton>

                                  <Box className="ticket-preview-info">
                                    <Typography
                                      variant="caption"
                                      className="content-text-sm"
                                      title={file.name}
                                    >
                                      {file.name || 'Unknown file'}
                                    </Typography>
                                    <Typography
                                      variant="caption"
                                      className="content-text-sm"
                                    >
                                      {((file.size || 0) / 1024 / 1024).toFixed(
                                        1
                                      )}{' '}
                                      MB
                                    </Typography>
                                  </Box>
                                </Paper>
                              ))}
                            </Box>
                          )}

                          {/* Error Display */}
                          {uploadErrors && (
                            <Alert
                              severity="error"
                              className="ticket-upload-error"
                              icon={<Icon name="AlertCircle" size={16} />}
                            >
                              <Typography
                                variant="body2"
                                className="other-field-error-text"
                              >
                                {Array.isArray(uploadErrors)
                                  ? uploadErrors.join(', ')
                                  : uploadErrors}
                              </Typography>
                            </Alert>
                          )}
                        </Box>
                      </Box>

                      <Box className="description-field-wrap">
                        <CustomTextField
                          fullWidth
                          name="description"
                          label="Description"
                          placeholder="Enter description..."
                          value={values?.description}
                          onBlur={handleBlur}
                          onChange={handleChange}
                          error={Boolean(
                            touched?.description && errors?.description
                          )}
                          helperText={
                            touched?.description && errors?.description
                          }
                          multiline
                          maxRows={4}
                          minRows={3}
                          required
                        />
                      </Box>
                    </Box>

                    {/* Personal Information Section */}
                    <Box className="personal-info-wrap">
                      <Typography className="title-text" component="p">
                        Please provide your personal details for further
                        communication.
                      </Typography>
                      {/* First row: Name, Email, Phone Number */}
                      <Box
                        className={`personal-info-grid-container ${isMobile ? 'mobile-layout' : ''}`}
                      >
                        <Box>
                          <CustomTextField
                            fullWidth
                            name="Name"
                            label="Name"
                            placeholder="Enter your name"
                            value={values.Name}
                            onChange={handleChange}
                            onBlur={handleBlur}
                            error={touched.Name && Boolean(errors.Name)}
                            helperText={touched.Name && errors.Name}
                            required
                          />
                        </Box>

                        <Box>
                          <CustomTextField
                            fullWidth
                            name="Email"
                            label="Email"
                            placeholder="Enter your email"
                            value={values.Email}
                            onChange={handleChange}
                            onBlur={handleBlur}
                            error={touched.Email && Boolean(errors.Email)}
                            helperText={touched.Email && errors.Email}
                            required
                          />
                        </Box>

                        <Box>
                          <CustomTextField
                            fullWidth
                            name="PhoneNumber"
                            label="Phone Number"
                            placeholder="Enter your phone number"
                            value={values.PhoneNumber}
                            onChange={handleChange}
                            onBlur={handleBlur}
                            error={
                              touched.PhoneNumber && Boolean(errors.PhoneNumber)
                            }
                            helperText={
                              touched.PhoneNumber && errors.PhoneNumber
                            }
                            required
                          />
                        </Box>
                      </Box>

                      {/* Second row: Support Pin and Verify Pin */}
                      <Box
                        className={`pin-fields-grid-container ${isMobile ? 'mobile-layout' : ''}`}
                      >
                        <Box>
                          <CustomTextField
                            fullWidth
                            name="supportPin"
                            label="Support Pin"
                            placeholder="Enter support pin"
                            type={showSupportPin ? 'text' : 'password'}
                            value={values.supportPin}
                            onChange={handleChange}
                            onBlur={handleBlur}
                            error={
                              touched.supportPin && Boolean(errors.supportPin)
                            }
                            helperText={touched.supportPin && errors.supportPin}
                            required
                            InputProps={{
                              endAdornment: (
                                <InputAdornment position="end">
                                  <IconButton
                                    onClick={handleToggleSupportPinVisibility}
                                    edge="end"
                                  >
                                    {showSupportPin ? (
                                      <VisibilityOffOutlinedIcon />
                                    ) : (
                                      <VisibilityOutlinedIcon />
                                    )}
                                  </IconButton>
                                </InputAdornment>
                              ),
                            }}
                          />
                        </Box>

                        <Box>
                          <CustomTextField
                            fullWidth
                            name="verifyPin"
                            label="Verify Pin"
                            placeholder="Re-enter support pin"
                            type={showVerifyPin ? 'text' : 'password'}
                            value={values.verifyPin}
                            onChange={handleChange}
                            onBlur={handleBlur}
                            error={
                              touched.verifyPin && Boolean(errors.verifyPin)
                            }
                            helperText={touched.verifyPin && errors.verifyPin}
                            required
                            InputProps={{
                              endAdornment: (
                                <InputAdornment position="end">
                                  <IconButton
                                    onClick={handleToggleVerifyPinVisibility}
                                    edge="end"
                                  >
                                    {showVerifyPin ? (
                                      <VisibilityOffOutlinedIcon />
                                    ) : (
                                      <VisibilityOutlinedIcon />
                                    )}
                                  </IconButton>
                                </InputAdornment>
                              ),
                            }}
                          />
                        </Box>
                      </Box>
                    </Box>

                    {/* Terms and Conditions */}
                    <Box className="pt16">
                      <CustomCheckbox
                        checked={values?.term_condition}
                        onChange={handleChange}
                        name="term_condition"
                        label="I agree to the terms and conditions."
                      />
                      {touched?.term_condition && errors?.term_condition && (
                        <Typography
                          variant="body2"
                          className="other-field-error-text"
                        >
                          {errors?.term_condition}
                        </Typography>
                      )}
                    </Box>

                    {/* Submit Button */}
                    <Box className="pt24">
                      <Box className="d-flex justify-end gap-sm">
                        <CustomButton
                          type="button"
                          title="Cancel"
                          variant="outlined"
                          onClick={handleBackNavigation}
                        />
                        <CustomButton
                          type="submit"
                          title="Create Ticket"
                          variant="contained"
                          disabled={isSubmitting}
                        />
                      </Box>
                    </Box>
                  </Box>
                </Box>
              </Form>
            )}
          </Formik>
          {/* --- FORM CONTENT ENDS HERE --- */}
        </Box>
      </Box>
    </Box>
  );
}
