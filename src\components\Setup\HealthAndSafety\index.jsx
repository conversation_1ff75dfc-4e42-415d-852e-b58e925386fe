'use client';

import React, {
  useContext,
  useEffect,
  useState,
  useCallback,
  useMemo,
} from 'react';
import {
  Box,
  FormGroup,
  FormControlLabel,
  Checkbox,
  Typography,
} from '@mui/material';
import CustomButton from '@/components/UI/CustomButton';
import CustomSearch from '@/components/UI/CustomSearch';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import {
  getBranchManagerInfo,
  setApiMessage,
} from '@/helper/common/commonFunctions';
import AuthContext from '@/helper/authcontext';
import NoDataView from '@/components/UI/NoDataView';
import { FolderDocIcon } from '@/helper/common/images';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import RightDrawer from '@/components/UI/RightDrawer';
import BranchAssign from './BranchAssign';
import { healthSafetyService } from '@/services/healthSafetyService';
import { useRouter } from 'next/navigation';
import InfoIcon from '@mui/icons-material/Info';
import './healthandsafety.scss';

export default function HealthAndSafety() {
  const { authState } = useContext(AuthContext);
  const router = useRouter();
  const [hsCategory, setHsCategory] = useState([]);
  const [loader, setLoader] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [checked, setChecked] = useState([]);
  const [assignEmployee, setAssignEmployee] = useState();
  const [selectedCategory, setSelectedCategory] = useState(null);
  const { isBranchManager, branchId } = getBranchManagerInfo(authState);

  const updateBranchCount = useCallback((categoryId, newCount) => {
    if (!categoryId || typeof newCount !== 'number' || newCount < 0) return;

    setHsCategory((prevCategories) => {
      if (!Array.isArray(prevCategories) || !prevCategories.length)
        return prevCategories;

      const index = prevCategories.findIndex((cat) => cat.id === categoryId);
      if (index === -1) return prevCategories;

      const category = prevCategories[index];
      if (category.assign_branch_count === newCount) return prevCategories;

      // Create a shallow copy and update only the changed item
      const updatedCategories = [...prevCategories];
      updatedCategories[index] = { ...category, assign_branch_count: newCount };
      return updatedCategories;
    });
  }, []);

  const getCategoryList = async (search, isUpdate) => {
    !isUpdate && setLoader(true);
    try {
      const data = await healthSafetyService.getCategoryList(search);
      setHsCategory(data);
    } catch (error) {
      setHsCategory([]);
      setApiMessage('error', error?.response?.data?.message);
    } finally {
      setLoader(false);
    }
  };

  const linkWithCategory = async (checked) => {
    try {
      setLoader(true);
      const data = await healthSafetyService.linkWithCategory(
        checked,
        branchId
      );
      if (data?.status) {
        setApiMessage('success', data?.message);
        getCategoryList(searchValue, true);
      } else {
        setApiMessage('error', data?.message);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    } finally {
      setLoader(false);
    }
  };

  const handleChecked = (value) => {
    const currentIndex = checked.indexOf(value);
    const newChecked = [...checked];
    if (currentIndex === -1) {
      newChecked.push(value);
    } else {
      newChecked.splice(currentIndex, 1);
    }
    setChecked(newChecked);
  };

  // const handleSearch = (value) => {
  //   setSearchValue(value);
  //   getCategoryList(value);
  // };

  useEffect(() => {
    getCategoryList(searchValue, false);
    if (authState?.UserPermission?.branch === 2) {
      //
    }
  }, [authState?.UserPermission?.branch]);

  useEffect(() => {
    if (hsCategory?.length > 0) {
      const healthAndSafetyCategories = hsCategory
        ?.filter((d) => {
          return d?.is_health_and_safety;
        })
        ?.map((d) => d?.id);
      if (healthAndSafetyCategories?.length) {
        setChecked(healthAndSafetyCategories);
      }
    }
  }, [hsCategory]);

  const renderCategoryList = () => {
    if (!hsCategory?.length) {
      return (
        <NoDataView
          title="No Health And Safety Data Found"
          description="There is no Health And Safety Data at the moment."
        />
      );
    }

    return isBranchManager
      ? hsCategory?.map((item) => (
          <FormGroup key={item.id} className="pl-checkbox">
            <FormControlLabel
              control={
                <Checkbox
                  className="check-box"
                  icon={<CheckBoxOutlineBlankIcon className="uncheck-icon" />}
                  checkedIcon={<CheckBoxIcon className="check-icon" />}
                  checked={checked?.includes(item?.id)}
                  onChange={() => handleChecked(item?.id)}
                />
              }
              className="check-box-text"
              name="playlists"
              label={
                <Box className="d-flex gap-sm">
                  <FolderDocIcon className="sidebar-list-icon" />
                  <Typography className="title-text cursor-pointer">
                    {item?.category_name}
                  </Typography>
                </Box>
              }
              onClick={(e) => e.preventDefault()}
            />
          </FormGroup>
        ))
      : hsCategory?.map((item) => (
          <Box
            key={item.id}
            className="d-flex align-center mt8 cursor-pointer health-safety-card-container"
            onClick={() => {
              setAssignEmployee(true);
              setSelectedCategory(item?.id);
            }}
          >
            <Box className="d-flex gap-sm category-name">
              <span>
                <FolderDocIcon className="sidebar-list-icon" />
              </span>
              <Typography className="title-text">
                {item?.category_name}
              </Typography>
            </Box>
            <Box className="d-flex align-center gap-sm branch-count">
              <Typography className="title-text cursor-pointer">
                {item?.assign_branch_count || 0}{' '}
                {item?.assign_branch_count === 1 ? 'Branch' : 'Branches'}
              </Typography>

              <PersonAddIcon className="cursor-pointer assign-branch-icon" />
            </Box>
          </Box>
        ));
  };

  const memoizedCategoryList = useMemo(
    () => renderCategoryList(),
    [hsCategory, checked, isBranchManager]
  );

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      getCategoryList(searchValue, true);
    }
  };

  const handleClearSearch = () => {
    getCategoryList('', true);
  };

  return (
    <Box>
      <Box className="d-flex justify-space-between align-center gap-sm pb16">
        <Box className="d-flex align-start">
          <InfoIcon
            sx={{ marginRight: '8px' }}
            className="info-icon cursor-poniter"
          />
          <Typography className="sub-title-text">
            Assign existing training programs to specific branches. To create or
            manage training materials, please visit the{' '}
            <span
              className="primary-link-text cursor-pointer fw600"
              onClick={() => router.push('/document-staff/all')}
            >
              Document Center
            </span>
            .
          </Typography>
        </Box>
        {/* <CustomButton
          variant="contained"
          title="Create training"
          onClick={() => router.push('/document-staff/all')}
        /> */}
      </Box>
      <Box className="health-safety-addtoplaylist">
        <Box className="health-and-safety-sec-search d-flex allign-center justify-end mb16">
          <CustomSearch
            setSearchValue={setSearchValue}
            searchValue={searchValue}
            onKeyPress={handleKeyPress}
            isClearSearch
            handleClearSearch={() => handleClearSearch()}
          />
          <CustomButton
            variant="outlined"
            title="Search"
            onClick={() => getCategoryList(searchValue, true)}
          />
        </Box>
        <Box>{memoizedCategoryList}</Box>
      </Box>

      {isBranchManager && (
        <Box className="d-flex justify-end">
          <CustomButton
            variant="contained"
            disabled={loader || !checked?.length}
            title={loader ? 'Linking...' : 'Link'}
            onClick={() => linkWithCategory(checked)}
          />
        </Box>
      )}

      <RightDrawer
        anchor="right"
        open={assignEmployee}
        onClose={() => setAssignEmployee(false)}
        title="Assign Branches"
        subTitle="Training Assignments"
        className="assign-employee-drawer"
        content={
          <BranchAssign
            selectedCategory={selectedCategory}
            setClose={setAssignEmployee}
            onAssignmentsUpdate={(newCount) => {
              if (selectedCategory && typeof newCount === 'number') {
                updateBranchCount(selectedCategory, newCount);
              }
              setAssignEmployee(false);
            }}
          />
        }
      />
    </Box>
  );
}
