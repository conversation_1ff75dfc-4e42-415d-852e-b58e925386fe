import React, { useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import Icon from '@/components/UI/AppIcon/AppIcon';
import CustomButton from '@/components/UI/CustomButton';
import { staticOptions } from '@/helper/common/staticOptions';
import {
  getAllergenList,
  getDietaryList,
  getPublicAllergenList,
  getPublicDietaryList,
} from '@/services/recipeService';
import {
  isPublicRoute,
  conditionalApiCall,
} from '@/helper/common/commonFunctions';
import Slider from '@mui/material/Slider';
import './filterpanel.scss';

const FilterPanel = ({
  isOpen,
  filters,
  onFilterChange,
  onClose,
  publicAllergens = [],
  publicDietary = [],
}) => {
  const pathname = usePathname();

  // Use utility function to determine route type
  const isPublic = isPublicRoute(pathname);

  // Show private filters on private routes, hide only on public routes
  const showPrivateFilters = !isPublic;

  // Dynamic state for filter options
  const [allergenOptions, setAllergenOptions] = useState([]);
  const [dietaryOptions, setDietaryOptions] = useState([]);
  const [optionsLoading, setOptionsLoading] = useState(true);

  // API function to get allergen options
  const fetchAllergenOptions = async () => {
    // Use conditional API call utility function
    const allergenData = await conditionalApiCall(
      pathname,
      async () => {
        const response = await getAllergenList(
          '',
          '',
          { status: 'active' },
          '',
          ''
        );

        // Transform allergens to filter options format
        return (
          response?.allergens?.map((allergen) => ({
            id: allergen?.id,
            value: allergen?.id,
            label: allergen?.attribute_title,
          })) || []
        );
      },
      // Fallback: use public API for public routes
      async () => {
        const response = await getPublicAllergenList(
          '',
          '',
          { status: 'active' },
          '',
          ''
        );

        // Transform allergens to filter options format
        return (
          response?.allergens?.map((allergen) => ({
            id: allergen?.id,
            value: allergen?.id,
            label: allergen?.attribute_title,
          })) || []
        );
      }
    );

    setAllergenOptions(allergenData);
  };

  // API function to get dietary options
  const fetchDietaryOptions = async () => {
    // Use conditional API call utility function
    const dietaryData = await conditionalApiCall(
      pathname,
      async () => {
        const response = await getDietaryList(
          '',
          '',
          { status: 'active' },
          '',
          ''
        );

        // Transform dietary options to filter format
        return (
          response?.dietary?.map((dietary) => ({
            id: dietary?.id,
            value: dietary?.id,
            label: dietary?.attribute_title,
          })) || []
        );
      },
      async () => {
        const response = await getPublicDietaryList(
          '',
          '',
          { status: 'active' },
          '',
          ''
        );

        // Transform dietary options to filter format
        return (
          response?.dietary?.map((dietary) => ({
            id: dietary?.id,
            value: dietary?.id,
            label: dietary?.attribute_title,
          })) || []
        );
      },
      publicDietary || [] // fallback for public routes
    );

    setDietaryOptions(dietaryData);
  };

  // Fetch options on component mount
  useEffect(() => {
    const fetchOptions = async () => {
      setOptionsLoading(true);
      fetchAllergenOptions();
      fetchDietaryOptions();
      setOptionsLoading(false);
    };

    fetchOptions();
  }, []);

  // Update options when public data changes (for public routes)
  useEffect(() => {
    if (isPublic) {
      setAllergenOptions(publicAllergens || []);
      setDietaryOptions(publicDietary || []);
      setOptionsLoading(false);
    }
  }, [isPublic, publicAllergens, publicDietary]);

  // Handle filter changes
  const handleAllergenChange = (allergenValue) => {
    const newAllergens = filters.allergens?.includes(allergenValue)
      ? filters.allergens.filter((a) => a !== allergenValue)
      : [...(filters.allergens || []), allergenValue];

    onFilterChange({ ...filters, allergens: newAllergens });
  };

  const handleDietaryChange = (dietaryValue) => {
    const newDietary = filters.dietary?.includes(dietaryValue)
      ? filters.dietary.filter((d) => d !== dietaryValue)
      : [...(filters.dietary || []), dietaryValue];

    onFilterChange({ ...filters, dietary: newDietary });
  };

  const handleCostRangeChange = (range) => {
    onFilterChange({ ...filters, costRange: range });
  };

  const handleDifficultyChange = (difficulty) => {
    onFilterChange({
      ...filters,
      difficulty: filters.difficulty === difficulty ? '' : difficulty,
    });
  };

  const handleCookingTimeChange = (time) => {
    onFilterChange({
      ...filters,
      cookingTime: filters.cookingTime === time ? '' : time,
    });
  };

  const handleOwnershipChange = (ownership) => {
    onFilterChange({
      ...filters,
      ownership: filters.ownership === ownership ? '' : ownership,
    });
  };

  const clearAllFilters = () => {
    onFilterChange({
      allergens: [],
      costRange: [],
      dietary: [],
      difficulty: '',
      cookingTime: '',
      ownership: '',
    });
  };

  const hasActiveFilters =
    filters.allergens?.length > 0 ||
    filters.dietary?.length > 0 ||
    filters.difficulty ||
    filters.cookingTime ||
    filters.ownership ||
    filters.costRange?.[0] ||
    filters.costRange?.[1];

  return (
    <>
      {/* Mobile Overlay */}
      <div
        className={`filter-panel__overlay ${isOpen ? 'filter-panel__overlay--visible' : ''}`}
        onClick={onClose}
      />

      {/* Filter Panel */}
      <div className={`filter-panel ${isOpen ? 'filter-panel--open' : ''}`}>
        {/* Header */}
        <div className="filter-panel__header">
          <h3 className="filter-panel__title">Filters</h3>
          <div className="filter-panel__header-actions">
            {hasActiveFilters && (
              <button
                onClick={clearAllFilters}
                className="filter-panel__clear-btn"
              >
                Clear All
              </button>
            )}
            <button onClick={onClose} className="filter-panel__close-btn">
              <Icon name="X" size={18} />
            </button>
          </div>
        </div>

        {/* Filter Content */}
        <div className="filter-panel__content">
          {/* Ownership Filter - Hidden on public routes */}
          {showPrivateFilters && (
            <div className="filter-panel__section">
              <label className="filter-panel__label">Ownership</label>
              <div className="filter-panel__options">
                {staticOptions?.RECIPE_OWNERSHIP_OPTIONS?.map((ownership) => (
                  <button
                    key={ownership?.label}
                    onClick={() => handleOwnershipChange(ownership?.value)}
                    className={`filter-panel__option-btn ${
                      filters.ownership === ownership?.value
                        ? 'filter-panel__option-btn--active'
                        : ''
                    }`}
                  >
                    {ownership?.label}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Cooking Time */}
          <div className="filter-panel__section">
            <label className="filter-panel__label">Cooking Time</label>
            <div className="filter-panel__options">
              {staticOptions?.RECIPE_COOKING_TIME_OPTIONS.map((time) => (
                <button
                  key={time?.value}
                  onClick={() => handleCookingTimeChange(time?.value)}
                  className={`filter-panel__option-btn ${
                    filters.cookingTime === time?.value
                      ? 'filter-panel__option-btn--active'
                      : ''
                  }`}
                >
                  {time?.label}
                </button>
              ))}
            </div>
          </div>

          {/* Difficulty Level */}
          <div className="filter-panel__section">
            <label className="filter-panel__label">Difficulty Level</label>
            <div className="filter-panel__options">
              {staticOptions?.RECIPE_DIFFICULTY_OPTIONS?.map((difficulty) => (
                <button
                  key={difficulty?.label}
                  onClick={() => handleDifficultyChange(difficulty?.value)}
                  className={`filter-panel__option-btn filter-panel__option-btn${`--${difficulty?.value}`} ${
                    filters.difficulty === difficulty?.value
                      ? 'filter-panel__option-btn--active'
                      : ''
                  }`}
                >
                  {difficulty?.label}
                </button>
              ))}
            </div>
          </div>

          {/* Cost Range - Hidden on public routes */}
          {showPrivateFilters && (
            <div className="filter-panel__section">
              <label className="filter-panel__label">Cost per Portion</label>
              <div className="filter-panel__range-section">
                <div className="filter-panel__range-display">
                  <span>${filters.costRange?.[0] || 0}</span>
                  <span>${filters.costRange?.[1] || 100}</span>
                </div>
                <div className="filter-panel__range-container">
                  <Slider
                    value={[
                      filters.costRange?.[0] || 0,
                      filters.costRange?.[1] || 100,
                    ]}
                    min={0}
                    max={100}
                    step={1}
                    onChange={(_, newValue) => handleCostRangeChange(newValue)}
                    valueLabelDisplay="off"
                    disableSwap
                    // color="primary"
                    sx={{
                      '& .MuiSlider-thumb': {
                        backgroundColor: 'var(--color-primary)',
                        '&:hover, &.Mui-focusVisible, &.Mui-active': {
                          boxShadow: 'none',
                        },
                      },
                      '&:hover': {
                        boxShadow: 'none',
                      },
                      '& .MuiSlider-track': {
                        backgroundColor: 'var(--color-primary)',
                      },
                    }}
                  />
                </div>
                <div className="filter-panel__range-inputs">
                  <input
                    type="number"
                    min="0"
                    max="100"
                    value={filters.costRange?.[0] || 0}
                    onChange={(e) =>
                      handleCostRangeChange([
                        parseInt(e.target.value) || 0,
                        filters.costRange?.[1] || 100,
                      ])
                    }
                    className="filter-panel__range-input"
                  />
                  <span className="filter-panel__range-separator">to</span>
                  <input
                    type="number"
                    min="0"
                    max="100"
                    value={filters.costRange?.[1] || 100}
                    onChange={(e) =>
                      handleCostRangeChange([
                        filters.costRange?.[0] || 0,
                        parseInt(e.target.value) || 100,
                      ])
                    }
                    className="filter-panel__range-input"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Allergens */}
          <div className="filter-panel__section">
            <label className="filter-panel__label">Exclude Allergens</label>
            <div className="filter-panel__chips">
              {optionsLoading ? (
                // Loading skeleton for allergens
                <div className="filter-panel__loading">
                  {[1, 2, 3, 4, 5].map((index) => (
                    <div
                      key={index}
                      className="filter-panel__chip filter-panel__chip--loading"
                    >
                      <div className="skeleton-text"></div>
                    </div>
                  ))}
                </div>
              ) : allergenOptions?.length > 0 ? (
                allergenOptions?.map((allergen) => (
                  <button
                    key={allergen?.id}
                    onClick={() => handleAllergenChange(allergen?.value)}
                    className={`filter-panel__chip ${
                      filters?.allergens?.includes(allergen?.value)
                        ? 'filter-panel__chip--allergen-active'
                        : ''
                    }`}
                  >
                    {allergen?.label}
                  </button>
                ))
              ) : (
                <div className="filter-panel__no-data">
                  No allergen options available
                </div>
              )}
            </div>
          </div>

          {/* Dietary Preferences */}
          <div className="filter-panel__section">
            <label className="filter-panel__label">Dietary Preferences</label>
            <div className="filter-panel__chips">
              {optionsLoading ? (
                // Loading skeleton for dietary options
                <div className="filter-panel__loading">
                  {[1, 2, 3, 4, 5]?.map((index) => (
                    <div
                      key={index}
                      className="filter-panel__chip filter-panel__chip--loading"
                    >
                      <div className="skeleton-text"></div>
                    </div>
                  ))}
                </div>
              ) : dietaryOptions.length > 0 ? (
                dietaryOptions?.map((dietary) => (
                  <button
                    key={dietary?.id}
                    onClick={() => handleDietaryChange(dietary?.value)}
                    className={`filter-panel__chip ${
                      filters.dietary?.includes(dietary?.value)
                        ? 'filter-panel__chip--dietary-active'
                        : ''
                    }`}
                  >
                    {dietary?.label}
                  </button>
                ))
              ) : (
                <div className="filter-panel__no-data">
                  No dietary options available
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Apply Button (Mobile) */}
        <div className="filter-panel__footer">
          <CustomButton
            variant="contained"
            onClick={onClose}
            title="Apply Filters"
            fullWidth
          />
        </div>
      </div>
    </>
  );
};

export default FilterPanel;
