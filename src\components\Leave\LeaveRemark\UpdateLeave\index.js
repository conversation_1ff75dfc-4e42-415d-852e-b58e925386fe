'use client';
import React, { useContext, useEffect, useState, useRef } from 'react';
import { Box, Chip, Divider, Typography } from '@mui/material';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import CustomButton from '@/components/UI/CustomButton';
import CheckIcon from '@mui/icons-material/Check';
import ClearIcon from '@mui/icons-material/Clear';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import PendingActionsIcon from '@mui/icons-material/PendingActions';
import RemoveCircleIcon from '@mui/icons-material/RemoveCircle';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import CustomTextField from '@/components/UI/CustomTextField';
import { useParams, useRouter } from 'next/navigation';
import CustomDoubleCalender from '@/components/UI/DoubleCalender';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import { setApiMessage } from '@/helper/common/commonFunctions';
import moment from 'moment';
import UserAvatar from '@/components/UI/Avatar/UserAvatar';
import AuthContext from '@/helper/authcontext';
import LeaveCard from '@/components/Leave/MyLeave/LeaveCard';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import dayjs from 'dayjs';
import {
  getDatesInRange,
  checkWeekends,
  checkHoliday,
  getHolidaysWeekends,
} from '../../ApplyLeave/leavefunctions';
import { differenceInDays } from 'date-fns';
import CustomSelect from '@/components/UI/CustomSelect';
import './updateleave.scss';
const leaveTypes = [
  { label: 'Paid', value: 'paid' },
  { label: 'Unpaid', value: 'unpaid' },
];

export default function UpdateLeave() {
  const formikRef = useRef(null);
  const [selectedDate, setSelectedDate] = useState(null);
  const [searchValue, setSearchValue] = useState('');
  const [userDetails, setUserDetails] = useState([]);
  const { authState, setAuthState } = useContext(AuthContext);
  const [leaveBalanceDetails, setLeaveBalanceDetails] = useState([]);
  const [selectedLeave, setSelectedLeave] = useState();
  const [leaveApproval, setLeaveApproval] = useState();
  const [userHolidayList, setUserHolidayList] = useState([]);
  const [calenderData, setCalenderData] = useState({
    coloredEvents: [],
    holidayEvents: [],
    allEvents: [],
  });
  let router = useRouter();
  let params = useParams();
  let userId = params?.id;
  const leave_calculation_type = authState?.generalSeetings
    ?.leave_calculation_type
    ? authState?.generalSeetings?.leave_calculation_type
    : 'auto';
  const leave_type = authState?.generalSeetings?.leave_period_type
    ? authState?.generalSeetings?.leave_period_type
    : 'day';
  // const leave_type = 'day';
  //leave update
  const [leaveHours, setLeaveHours] = useState('total');
  const dayMenuOptions = [
    { label: 'Full Day', value: 'full_day' },
    { label: '1st Half', value: 'first_half' },
    { label: '2nd Half', value: 'second_half' },
  ];
  const [selectedWeekOff, setSelectedWeekOff] = useState({});
  const [weekOffTotal, setWeekOffTotal] = useState({});
  const [dateRange, setDateRange] = useState([]);
  dayjs.extend(utc);
  dayjs.extend(timezone);
  const daysOfWeek = [
    {
      name: 'Sunday',
      dayOff:
        userDetails?.request_from_users?.user_week_day?.['sunday'] === 'dayoff',
    },
    {
      name: 'Monday',
      dayOff:
        userDetails?.request_from_users?.user_week_day?.['monday'] === 'dayoff',
    },
    {
      name: 'Tuesday',
      dayOff:
        userDetails?.request_from_users?.user_week_day?.['tuesday'] ===
        'dayoff',
    },
    {
      name: 'Wednesday',
      dayOff:
        userDetails?.request_from_users?.user_week_day?.['wednesday'] ===
        'dayoff',
    },
    {
      name: 'Thursday',
      dayOff:
        userDetails?.request_from_users?.user_week_day?.['thursday'] ===
        'dayoff',
    },
    {
      name: 'Friday',
      dayOff:
        userDetails?.request_from_users?.user_week_day?.['friday'] === 'dayoff',
    },
    {
      name: 'Saturday',
      dayOff:
        userDetails?.request_from_users?.user_week_day?.['saturday'] ===
        'dayoff',
    },
  ];
  const getSingleUserList = async () => {
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_SINGLE_USER + `/${userId}`
      );
      if (status === 200) {
        setUserDetails({
          ...data?.data,
          leave_days_obj: data?.data?.leave_days_obj
            ? JSON.parse(data?.data?.leave_days_obj)
            : null,
        });
        data?.data?.leave_days_obj
          ? setLeaveHours('daily')
          : data?.data?.leave_days
            ? setLeaveHours('total')
            : '';
        data?.data?.request_from_users?.id &&
          getSingleUserBalance(
            data?.data?.request_from_users?.id,
            data?.data?.leave_request_type_list?.id,
            data?.data?.start_date
          );
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const getSingleUserBalance = async (id, leaveId, year) => {
    const leaveYear = moment(year).format('YYYY');
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.LEAVE_BALANCE + `${id}?year=${leaveYear}&leave_period_type=`
      );
      if (status === 200) {
        const userBalance =
          data?.data &&
          data?.data?.length > 0 &&
          data?.data?.find((leveTypeId) => {
            return leveTypeId?.id === leaveId;
          });
        userBalance
          ? setLeaveBalanceDetails(userBalance)
          : setLeaveBalanceDetails([]);
        userBalance && setSelectedLeave(userBalance);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  useEffect(() => {
    if (userId) {
      getSingleUserList();
    }
  }, [userId]);

  // useEffect(() => {
  //   if (userId && yearFilterdata) {
  //     getSingleUserBalance(userId);
  //   }
  // }, [userId, yearFilterdata]);

  const cancelLeave = async (remark) => {
    const requestData = {
      remark: remark,
      request_id: params?.id,
    };
    try {
      const { status, data } = await axiosInstance.post(
        URLS?.DELETE_LEAVE,
        requestData
      );
      if (status === 200) {
        setApiMessage('success', data?.message);
        router.push('/leave-remark');
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const getLeaveDetails = async (view) => {
    if (!view && !calenderSearch) return;
    try {
      let apiUrl = `${URLS?.CALENDER_WISE_LEAVE}?list_type=staff&search=`;

      if (view?.Start && view?.End) {
        apiUrl += `&start_date=${view?.Start}&end_date=${view?.End}`;
      } else if (view?.Start) {
        apiUrl += `&start_date=${view?.Start}`;
      } else if (view?.End) {
        apiUrl += `&end_date=${view?.End}`;
      }

      const { status, data } = await axiosInstance.get(apiUrl);

      if (status === 200) {
        const coloredEvents = data?.data?.calenderLeaves?.map((event) => ({
          ...event,
          start: moment(event?.start_date).format('YYYY-MM-DD'),
          end: event?.end_date
            ? moment(event?.start_date).format('YYYY-MM-DD') ===
              moment(event?.end_date).format('YYYY-MM-DD')
              ? moment(event?.end_date).format('YYYY-MM-DD')
              : moment(event?.end_date).add(1, 'day').format('YYYY-MM-DD')
            : moment(event?.start_date).format('YYYY-MM-DD'),
          allDay: true,
          eventType: 'leave',
        }));

        const holidayEvents = data?.data?.holidayList?.map((event) => ({
          ...event,
          id: `holiday-${event?.id}`,
          start: moment(event?.holiday_policy_start_date).format('YYYY-MM-DD'),
          end: moment(event?.holiday_policy_end_date).format('YYYY-MM-DD'),
          title: event?.holiday_policy_name,
          backgroundColor: event?.holiday_policy_colour,
          borderColor: event?.holiday_policy_colour,
          allDay: true,
          eventType: 'holiday',
        }));

        setCalenderData({
          coloredEvents,
          holidayEvents,
          allEvents: [...coloredEvents, ...holidayEvents],
        });
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const Inputype = (e, types) => {
    let value = e.target.value;
    if (types === 'hour') {
      {
        // Remove non-numeric characters
        if (value === '' || /^\d*\.?\d{0,2}$/.test(value)) {
          e.target.value = value;
        } else {
          e.target.value = value.slice(0, -1);
        }
      }
    } else {
      {
        e.target.value = e.target.value.replace(/[^0-9]/g, ''); // Remove non-numeric characters
      }
      // {
      //   // Remove non-numeric characters
      //   if (value === '' || /^\d*\.?\d{0,2}$/.test(value)) {
      //     e.target.value = value;
      //   } else {
      //     e.target.value = value.slice(0, -1);
      //   }
      // }
    }
  };
  // const getHolidaysWeekends = (dateRanges) => {
  //   const filterWeekends = dateRanges?.filter((leave) => {
  //     return (
  //       leave?.type === 'day_off' ||
  //       leave?.type === 'holiday' ||
  //       leave?.type === 'day_off_holiday'
  //     );
  //   });
  //   return filterWeekends;
  // };
  const getUserHolidayList = async (id, sdate, edate, dateRanges) => {
    try {
      const { status, data } = await axiosInstance.get(
        `${URLS?.GET_USER_HOLIDAY_POLICY}?user_id=${id}&search=&start_date=${sdate}&end_date=${edate}`
      );
      if (status === 200) {
        setUserHolidayList(data?.data || []);
        let weekss = {};
        dateRanges?.map((date) => {
          const isWeekend = checkWeekends(date, daysOfWeek);
          const isHoliday =
            data?.data && data?.data?.length > 0
              ? checkHoliday(date, data?.data)
              : false;
          if (isWeekend || isHoliday) {
            weekss[date] =
              isWeekend && isHoliday
                ? 'day_off_holiday'
                : isWeekend
                  ? 'day_off'
                  : 'holiday';
          }
          return date;
        });
        setWeekOffTotal(weekss);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const getStatusLabel = (status) => {
    switch (status) {
      case 'holiday':
        return 'Holiday';
      case 'day_off':
        return 'Day Off';
      case 'day_off_holiday':
        return 'Day off with Holiday';
      default:
        return '';
    }
  };
  useEffect(() => {
    if (
      dayjs.utc(userDetails?.start_date).format('DD-MM-YYYY') &&
      dayjs.utc(userDetails?.end_date).format('DD-MM-YYYY') &&
      leave_calculation_type === 'manual' &&
      userDetails?.request_status === 'pending'
    ) {
      const dateRanges = getDatesInRange(
        dayjs.utc(userDetails?.start_date).format('YYYY-MM-DD'),
        dayjs.utc(userDetails?.end_date).format('YYYY-MM-DD')
      );
      if (
        dayjs.utc(userDetails?.start_date).format('DD-MM-YYYY') !==
          dateRange?.[0] ||
        dayjs.utc(userDetails?.end_date).format('DD-MM-YYYY') !==
          dateRanges?.[dateRanges?.length - 1]
      ) {
        getUserHolidayList(
          userDetails?.request_from_users?.id,
          dayjs.utc(userDetails?.start_date).format('YYYY-MM-DD'),
          dayjs.utc(userDetails?.end_date).format('YYYY-MM-DD'),
          dateRanges
        );
      }

      if (leaveHours === 'total') {
        formikRef?.current?.setFieldValue(
          'leaveday',
          userDetails?.leave_days ? userDetails?.leave_days : 0
        );
      } else if (leaveHours === 'daily') {
        // const workingHoursPerDay =
        //   authState?.generalSeetings?.working_hours_per_day;
        formikRef?.current?.setFieldValue('leaveday', '');
        setDateRange(dateRanges);
        if (leave_type === 'hour') {
          let weeks = {};
          userDetails?.leave_days_obj &&
            userDetails?.leave_days_obj?.length > 0 &&
            userDetails?.leave_days_obj?.map((leave) => {
              weeks[leave?.date] = leave?.total_hours;
            });
          setSelectedWeekOff(weeks);
          formikRef?.current?.setFieldValue('selectedWeekOff', weeks);
        } else if (leave_type === 'day') {
          let weeks = {};
          userDetails?.leave_days_obj &&
            userDetails?.leave_days_obj?.length > 0 &&
            userDetails?.leave_days_obj?.map((leave) => {
              weeks[leave?.date] = leave?.type;
            });
          setSelectedWeekOff(weeks);
          formikRef?.current?.setFieldValue('selectedWeekOff', weeks);
        }
      }
    } else {
      setDateRange([]);
    }
  }, [
    dayjs.utc(userDetails?.start_date).format('DD-MM-YYYY'),
    dayjs.utc(userDetails?.end_date).format('DD-MM-YYYY'),
    leaveHours,
    leave_calculation_type,
  ]);
  return (
    <Box>
      <Box className="">
        <Formik
          innerRef={formikRef}
          initialValues={{
            leaveApproval: '', // Leave Approval input
            leaveType: userDetails?.leave_request_type_list
              ?.leave_deduction_type
              ? userDetails?.leave_request_type_list?.leave_deduction_type
              : '', // Leave Type input
            reason: '',
            leaveday: userDetails?.leave_days ? userDetails?.leave_days : '',
            selectedWeekOff: {},
          }}
          enableReinitialize={true}
          validationSchema={Yup.object().shape({
            leaveApproval: Yup.string().required('Leave Approval is required'),
            leaveType: Yup.string().required('Leave Type is required'),
            reason: Yup.string().trim().required('Remark is required'),
            leaveday:
              userDetails?.request_status === 'pending' &&
              leaveApproval === 'approved' &&
              leave_calculation_type === 'manual' &&
              leaveHours === 'total' &&
              Yup.string()
                .trim()
                .required('This field is required')
                .test(
                  'leave-day-match',
                  'Please select valid time',
                  function (value) {
                    // const { sdate, edate } = this.parent;
                    // selectedWeekOff const hasSelectedWeekOff =
                    //   selectedWeekOff &&
                    //   Object.keys(selectedWeekOff).length > 0;
                    // if (hasSelectedWeekOff) return true;
                    // if (!hasSelectedWeekOff && !value) {
                    //   return this.createError({
                    //     message: 'This field is required',
                    //   });
                    // }
                    if (value === '0') {
                      return this.createError({
                        message: `You can't apply for leave with 0 ${leave_type} selected.`,
                      });
                    }
                    if (!value) return true;

                    const startDate = new Date(userDetails?.start_date);
                    const endDate = new Date(userDetails?.end_date);
                    // const startDate = dayjs
                    //   .utc(userDetails?.start_date)
                    //   .format('DD-MM-YYYY');
                    // const endDate = dayjs
                    //   .utc(userDetails?.end_date)
                    //   .format('DD-MM-YYYY');
                    if (leave_type === 'day') {
                      const leaveDays = parseFloat(value);
                      const allowedDayss =
                        differenceInDays(endDate, startDate) + 1;
                      const getWeekOfHoliday = getHolidaysWeekends(
                        dateRange,
                        daysOfWeek,
                        userHolidayList
                      );
                      const allowedDays =
                        allowedDayss - getWeekOfHoliday?.length;
                      // if (selectedLeave?.has_leave_unlimited) {
                      //   return true;
                      // }
                      if (
                        leaveDays > selectedLeave?.leave_balance &&
                        !selectedLeave?.has_leave_unlimited
                      ) {
                        return this.createError({
                          message: `Your leave balance is only ${selectedLeave?.leave_balance} days.`,
                        });
                      }
                      if (leaveDays > allowedDays) {
                        if (allowedDayss > 0 && allowedDays === 0) {
                          return this.createError({
                            message: `You cannot apply for leave on a holiday or scheduled day off.`,
                          });
                        }
                        return this.createError({
                          message: `You can only take up to ${allowedDays} days. Please ensure holidays and day offs are not included.`,
                        });
                      }
                      return true;
                    } else if (leave_type === 'hour') {
                      const leaveHours = parseFloat(value);
                      const allowedHourss = endDate - startDate;
                      const allowedHoursss =
                        allowedHourss / (1000 * 60 * 60) + 24;
                      const getWeekOfHoliday = getHolidaysWeekends(
                        dateRange,
                        daysOfWeek,
                        userHolidayList
                      );
                      const allowedHours =
                        allowedHoursss - getWeekOfHoliday?.length * 24;

                      // if (selectedLeave?.has_leave_unlimited) {
                      //   return true;
                      // }
                      if (
                        leaveHours > selectedLeave?.leave_balance &&
                        !selectedLeave?.has_leave_unlimited
                      ) {
                        return this.createError({
                          message: `Your leave balance is only ${selectedLeave?.leave_balance} hours.`,
                        });
                      }
                      if (leaveHours > allowedHours) {
                        if (allowedHoursss > 0 && allowedHours === 0) {
                          return this.createError({
                            message: `You cannot apply for leave on a holiday or scheduled day off.`,
                          });
                        }
                        return this.createError({
                          message: `You can only take up to ${parseFloat(
                            allowedHours.toFixed(2)
                          )} hours. Please ensure holidays and day offs are not included.`,
                        });
                      }
                    }
                    return true;
                  }
                ),
            selectedWeekOff:
              userDetails?.request_status === 'pending' &&
              leaveHours === 'daily' &&
              leaveApproval === 'approved' &&
              leave_calculation_type === 'manual' &&
              Yup.object(
                dateRange?.reduce((acc, date) => {
                  acc[date] =
                    // formikRef?.current?.values?.leaveday
                    //   ? Yup.string().nullable()
                    //   :
                    leave_type === 'day' &&
                    selectedLeave?.leave_balance === 0 &&
                    !selectedLeave?.has_leave_unlimited
                      ? Yup.string()
                          .required('This field is required')
                          .test(
                            'max-hours',
                            `Your leave balance is only ${selectedLeave?.leave_balance} days.`,
                            (val) => {
                              if (!val) return true; // required handles empty
                              const num = parseFloat(7);
                              return num <= selectedLeave?.leave_balance;
                            }
                          )
                      : leave_type === 'hour' &&
                          !selectedLeave?.has_leave_unlimited &&
                          selectedLeave?.leave_balance < 24 &&
                          (selectedLeave?.leave_balance ||
                            selectedLeave?.leave_balance === 0)
                        ? Yup.string()
                            .required('This field is required')
                            .test(
                              'max-hours',
                              `Your leave balance is only ${selectedLeave?.leave_balance} hours.`,
                              (val) => {
                                if (!val) return true; // required handles empty
                                const num = parseFloat(val);
                                return (
                                  !isNaN(num) &&
                                  num <= selectedLeave?.leave_balance
                                );
                              }
                            )
                        : leave_type === 'hour'
                          ? Yup.string()
                              .required('This field is required')
                              .test(
                                'max-hours',
                                'Cannot exceed 24hrs',
                                (val) => {
                                  if (!val) return true; // required handles empty
                                  const num = parseFloat(val);
                                  return !isNaN(num) && num <= 24;
                                }
                              )
                          : Yup.string().required('This field is required');
                  return acc;
                }, {})
              ),
          })}
          onSubmit={async (values) => {
            const datesarray = dateRange?.map((date) => {
              const isWeekend =
                userDetails?.leave_days_obj &&
                userDetails?.leave_days_obj?.length > 0 &&
                userDetails?.leave_days_obj?.find(
                  (leave) => leave?.type === 'day_off' && date === leave?.date
                );
              const isHoliday =
                userDetails?.leave_days_obj &&
                userDetails?.leave_days_obj?.length > 0 &&
                userDetails?.leave_days_obj?.find(
                  (leave) => leave?.type === 'holiday' && date === leave?.date
                );
              const isBoth =
                userDetails?.leave_days_obj &&
                userDetails?.leave_days_obj?.length > 0 &&
                userDetails?.leave_days_obj?.find(
                  (leave) =>
                    leave?.type === 'day_off_holiday' && date === leave?.date
                );
              if (leave_type === 'hour') {
                return {
                  date: date,
                  total_hours:
                    isBoth || isHoliday || isWeekend
                      ? 0
                      : values?.selectedWeekOff[date],
                  ...((isBoth || isHoliday || isWeekend) && {
                    type: isBoth
                      ? 'day_off_holiday'
                      : isHoliday
                        ? 'holiday'
                        : isWeekend
                          ? 'day_off'
                          : '',
                  }),
                };
              }
              return {
                date: date,
                ...((isBoth ||
                  isHoliday ||
                  isWeekend ||
                  values?.selectedWeekOff[date]) && {
                  type: isBoth
                    ? 'day_off_holiday'
                    : isHoliday
                      ? 'holiday'
                      : isWeekend
                        ? 'day_off'
                        : values?.selectedWeekOff[date],
                }),
              };
            });
            const payload = {
              action: values?.leaveApproval,
              remark: values?.reason,
              request_id: userId,
              leave_deduction_type: values?.leaveType,
              ...(leave_calculation_type === 'manual' &&
                values?.leaveApproval === 'approved' &&
                !values?.leaveday &&
                values?.leaveday !== 0 &&
                leaveHours === 'daily' && {
                  leave_days_obj: JSON.stringify(datesarray),
                }),
              ...(leave_calculation_type === 'manual' &&
                values?.leaveApproval === 'approved' &&
                (values?.leaveday || values?.leaveday === 0) &&
                leaveHours === 'total' && {
                  leave_days: values?.leaveday,
                }),
            };

            try {
              const { status, data } = await axiosInstance.post(
                URLS?.LEAVE_ACTION,
                payload
              );
              if (status === 200) {
                setApiMessage('success', data?.message);
                router.push('/leave-remark');
              }
            } catch (error) {
              setApiMessage('error', error?.response?.data?.message);
            }
          }}
        >
          {({
            errors,
            touched,
            handleBlur,
            values,
            handleSubmit,
            handleChange,
            validateForm,
            setFieldTouched,
            setFieldError,
            setFieldValue,
          }) => (
            <Form onSubmit={handleSubmit}>
              <Box className="update-leave-wrap">
                <Box className="update-leave-header-text d-flex">
                  <ArrowBackIosIcon
                    className="cursor-pointer"
                    onClick={() => {
                      if (authState?.listViewMode === 'calendar') {
                        router.push('/leave-remark');
                        setAuthState({
                          ...authState,
                          listViewMode: 'calendar',
                        });
                      } else {
                        router.push('/leave-remark');
                        setAuthState({ ...authState, listViewMode: 'list' });
                      }
                    }}
                  />
                  <Typography className="title-sm">Update Leave</Typography>
                </Box>
                <Divider />
                <Box className="leave-container d-flex">
                  <Box className="update-leave">
                    <Box className="leave-left-wrap d-flex align-center justify-space-between mb8 mt8">
                      <Box className="leave-left d-flex">
                        <Box className="leave-icons-wrap">
                          <UserAvatar
                            name={
                              userDetails?.request_from_users?.user_full_name
                            }
                            src={
                              userDetails?.request_from_users?.user_avatar_link
                            }
                            classname=""
                          />
                          {/* {renderIcon(
                            userDetails?.request_from_users?.user_avatar_link
                          )} */}
                        </Box>
                        <Box className="leave-left">
                          <Box className="user-detail-wrap">
                            <Typography className="body-text">
                              {userDetails?.request_from_users?.user_full_name}
                            </Typography>
                            <Typography className="user-email sub-title-text">
                              {userDetails?.request_from_users?.user_email}
                            </Typography>
                            <Typography className="sub-title-text">
                              {userDetails?.leave_request_type_list?.name}
                            </Typography>
                            <Typography className="user-branch-name sub-title-text fw400">
                              {
                                userDetails?.request_from_users?.branch
                                  ?.branch_name
                              }
                            </Typography>
                          </Box>
                          <Box className="leave-info">
                            <Typography className="sub-title-text text-capital">
                              {userDetails?.leaveRequest}
                            </Typography>
                            <Typography className="sub-title-text d-flex align-center gap-5">
                              Applied As
                              <span className="role-wrap fw600">
                                {userDetails?.leave_role_request?.role_name}
                              </span>
                            </Typography>
                          </Box>
                        </Box>
                      </Box>
                      <Box className="leave-right">
                        <Typography
                          className={`title-text fw500 leave-status text-capital ${
                            userDetails?.request_status === 'rejected'
                              ? 'leave-rejected'
                              : userDetails?.request_status === 'approved'
                                ? 'leave-success'
                                : userDetails?.request_status === 'cancelled'
                                  ? 'leave-cancelled'
                                  : 'leave-draft'
                          }`}
                        >
                          {userDetails?.request_status}
                        </Typography>
                      </Box>
                    </Box>
                    <Divider />
                    <Box className="body-text fw600 leave-date-wrap mt8 mb8">
                      <Box className="leave-date d-flex align-center">
                        <Box className="start-date-wrap">
                          <Typography
                            component="p"
                            className="text-wrap sub-title-text"
                          >
                            Start Date
                          </Typography>
                          <Typography className="start-date title-text fw400">
                            {/* Day & Hours dates */}
                            {/* {userDetails?.leave_period_type === 'hour' ||
                            userDetails?.duration_type === 'Hours'
                              ? dayjs
                                  .utc(userDetails?.start_date)
                                  .format('DD-MM-YYYY hh:mm A')
                              : dayjs
                                  .utc(userDetails?.start_date)
                                  .format('DD-MM-YYYY')} */}
                            {dayjs
                              .utc(userDetails?.start_date)
                              .format('DD-MM-YYYY')}
                          </Typography>
                        </Box>
                        <Box className="end-date-wrap">
                          <Typography className="text-wrap sub-title-text">
                            End Date
                          </Typography>
                          <Typography className="start-date title-text fw400">
                            {/* Day & Hours dates */}
                            {/* {userDetails?.duration_type === 'Hours' ||
                            userDetails?.leave_period_type === 'hour'
                              ? dayjs
                                  .utc(userDetails?.end_date)
                                  .format('DD-MM-YYYY hh:mm A')
                              : dayjs
                                  .utc(userDetails?.end_date)
                                  .format('DD-MM-YYYY')} */}
                            {dayjs
                              .utc(userDetails?.end_date)
                              .format('DD-MM-YYYY')}
                          </Typography>
                        </Box>
                      </Box>
                      <Box className="icon-wrap d-flex align-center ">
                        <Typography className="text-wrap sub-title-text mt-8">
                          {`${userDetails?.leave_days} ${
                            userDetails?.leave_period_type === 'day'
                              ? userDetails?.leave_days === 1
                                ? 'Day'
                                : 'Days'
                              : userDetails?.leave_period_type === 'hour'
                                ? userDetails?.leave_days === 1
                                  ? 'Hour'
                                  : 'Hours'
                                : userDetails?.duration_type === 'Days'
                                  ? userDetails?.leave_days === 1
                                    ? 'Day'
                                    : 'Days'
                                  : userDetails?.leave_days === 1
                                    ? 'Hour'
                                    : 'Hours'
                          }`}
                        </Typography>
                        {/* <Box className="d-flex align-center gap-5">
                          <CircleIcon className="circle-icon" />
                          <Typography className="text-wrap sub-title-text" component="p">
                            {userDetails?.workingDayStatus}
                          </Typography>
                        </Box> */}
                      </Box>
                    </Box>
                    {userDetails?.request_status === 'pending' && (
                      <>
                        <Divider />
                        <Box className="subject-reason-wrap mt8 ">
                          <Typography className="title-text">
                            <span className="fw500"> Subject :</span>{' '}
                            {userDetails?.request_subject}
                          </Typography>
                          <Typography className="title-text mb8">
                            <span className="fw500">Reason :</span>{' '}
                            {userDetails?.request_reason}
                          </Typography>
                        </Box>
                        <Divider />
                        <Box className="leave-type-wrap mb8 mt8">
                          <Typography
                            className={`field-label ${
                              touched?.leaveType && errors?.leaveType
                                ? 'Mui-error'
                                : ''
                            }`}
                            component="p"
                          >
                            Leave Type <span className="required"> *</span>
                          </Typography>
                          <Box className="leave-type d-flex">
                            {leaveTypes?.map((type, index) => {
                              return (
                                <Chip
                                  key={index}
                                  className={`leave-type-text ${
                                    values?.leaveType == type?.value
                                      ? 'chip-selected'
                                      : ''
                                  } ${type?.label?.toLowerCase()}-leave ${
                                    touched?.leaveType && errors?.leaveType
                                      ? 'chip-error'
                                      : ''
                                  }`}
                                  label={type?.label}
                                  clickable
                                  variant="outlined"
                                  onClick={() =>
                                    handleChange({
                                      target: {
                                        name: 'leaveType',
                                        value: type?.value,
                                      },
                                    })
                                  }
                                />
                              );
                            })}
                          </Box>
                          {touched?.leaveType && errors?.leaveType && (
                            <Typography
                              variant="body2"
                              color="error"
                              className="other-field-error-text "
                            >
                              {errors?.leaveType}
                            </Typography>
                          )}
                        </Box>
                        <Divider />
                        <Box className="chip-container-wrap mt8 mb8">
                          <Typography
                            className={`field-label ${
                              touched?.leaveApproval && errors?.leaveApproval
                                ? 'Mui-error'
                                : ''
                            }`}
                            component="p"
                          >
                            Leave Approval <span className="required"> *</span>
                          </Typography>
                          <Box className="chip-container d-flex align-center">
                            <Chip
                              className={`approved-leave ${
                                values?.leaveApproval === 'approved'
                                  ? 'chip-selected approved-selected'
                                  : ''
                              } ${
                                touched?.leaveApproval && errors?.leaveApproval
                                  ? 'chip-error'
                                  : ''
                              }`}
                              label="Approved"
                              clickable
                              variant="outlined"
                              icon={<CheckIcon className="check-icon" />}
                              onClick={() => {
                                handleChange({
                                  target: {
                                    name: 'leaveApproval',
                                    value: 'approved',
                                  },
                                });
                                setLeaveApproval('approved');
                              }}
                            />
                            <Chip
                              className={`rejected-leave ${
                                values?.leaveApproval === 'rejected'
                                  ? 'chip-selected'
                                  : ''
                              }`}
                              label="Rejected"
                              clickable
                              variant="outlined"
                              icon={<ClearIcon className="clear-icon" />}
                              onClick={() => {
                                handleChange({
                                  target: {
                                    name: 'leaveApproval',
                                    value: 'rejected',
                                  },
                                });
                                setLeaveApproval('rejected');
                              }}
                            />
                          </Box>
                          {touched?.leaveApproval && errors?.leaveApproval && (
                            <Typography
                              variant="body2"
                              color="error"
                              className="other-field-error-text "
                            >
                              {errors?.leaveApproval}
                            </Typography>
                          )}
                        </Box>
                        {/* {values?.leaveApproval === 'approved' ? (
                          <>
                            <Divider />
                            {leave_calculation_type === 'manual' ? (
                              <>
                                {' '}
                                <Box className="input-wrap pt24 pb8">
                                  <CustomTextField
                                    InputLabelProps={{
                                      shrink: true,
                                    }}
                                    fullWidth
                                    id="leaveday"
                                    name="leaveday"
                                    value={values?.leaveday}
                                    label={`${
                                      userDetails?.duration_type === 'Hours'
                                        ? 'hours'
                                        : 'days'
                                    }*`}
                                    variant="filled"
                                    placeholder={`Enter ${
                                      userDetails?.duration_type === 'Hours'
                                        ? 'hours'
                                        : 'days'
                                    }`}
                                    error={Boolean(
                                      touched.leaveday && errors.leaveday
                                    )}
                                    helperText={
                                      touched.leaveday && errors.leaveday
                                    }
                                    onBlur={handleBlur}
                                    onChange={handleChange}
                                    onInput={(e) =>
                                      Inputype(e, userDetails?.duration_type)
                                    }
                                  />
                                </Box>
                              </>
                            ) : (
                              <></>
                            )}
                          </>
                        ) : (
                          <></>
                        )} */}
                        {values?.leaveApproval === 'approved' &&
                        leave_calculation_type === 'manual' ? (
                          <>
                            <Divider />
                            {leaveHours === 'total' ? (
                              <>
                                {Object.entries(weekOffTotal)?.length > 0 ? (
                                  <Box className="pt8">
                                    <Typography className="title-text fw600">
                                      {' '}
                                      Holiday & Day off
                                    </Typography>
                                    {Object.entries(weekOffTotal).map(
                                      ([date, status]) => (
                                        <Typography
                                          key={date}
                                          className="sub-title-text fw500"
                                        >
                                          <span className="sub-title-text fw400">
                                            {dayjs(date).format('dddd D MMMM')}
                                          </span>{' '}
                                          : {getStatusLabel(status)}
                                        </Typography>
                                      )
                                    )}
                                  </Box>
                                ) : (
                                  <></>
                                )}

                                <Box className="pt8 pb8">
                                  <CustomTextField
                                    fullWidth
                                    id="leaveday"
                                    name="leaveday"
                                    value={values?.leaveday}
                                    label={`Total ${
                                      leave_type === 'hour' ? 'hours' : 'days'
                                    }`}
                                    required
                                    placeholder={`Enter ${
                                      leave_type === 'hour' ? 'hours' : 'days'
                                    }`}
                                    error={Boolean(
                                      touched.leaveday && errors.leaveday
                                    )}
                                    helperText={
                                      touched.leaveday && errors.leaveday
                                    }
                                    onBlur={handleBlur}
                                    onChange={(e) => {
                                      handleChange(e);
                                    }}
                                    onInput={(e) => Inputype(e, leave_type)}
                                  />
                                </Box>
                              </>
                            ) : leaveHours === 'daily' &&
                              leave_type === 'day' &&
                              dateRange &&
                              dateRange?.length > 0 ? (
                              <>
                                <Box className="leave-add-inputs-wrap mb8 pt8">
                                  {dateRange?.map((date) => {
                                    const isWeekend =
                                      userDetails?.leave_days_obj?.find(
                                        (leave) =>
                                          leave?.type === 'day_off' &&
                                          date === leave?.date
                                      );
                                    const isHoliday =
                                      userDetails?.leave_days_obj?.find(
                                        (leave) =>
                                          leave?.type === 'holiday' &&
                                          date === leave?.date
                                      );
                                    const isBoth =
                                      userDetails?.leave_days_obj?.find(
                                        (leave) =>
                                          leave?.type === 'day_off_holiday' &&
                                          date === leave?.date
                                      );
                                    const optionList =
                                      !isBoth && !isWeekend && !isHoliday
                                        ? dayMenuOptions
                                        : isBoth
                                          ? [
                                              {
                                                label: 'Day off with Holiday',
                                                value: 'day_off_holiday',
                                                disabled: true,
                                              },
                                            ]
                                          : isWeekend
                                            ? [
                                                {
                                                  label: 'Day off',
                                                  value: 'day_off',
                                                  disabled: true,
                                                },
                                              ]
                                            : isHoliday
                                              ? [
                                                  {
                                                    label: 'Holiday',
                                                    value: 'holiday',
                                                    disabled: true,
                                                  },
                                                ]
                                              : [];
                                    const valuesOption = isBoth
                                      ? 'day_off_holiday'
                                      : isHoliday
                                        ? 'holiday'
                                        : isWeekend
                                          ? 'day_off'
                                          : selectedWeekOff?.[date] || '';
                                    return (
                                      <Box
                                        key={date}
                                        className="leave-add-inputs"
                                      >
                                        <Box className="date-text-wrap">
                                          <Typography className="field-label">
                                            {dayjs(date).format('dddd D MMMM')}
                                          </Typography>
                                        </Box>
                                        <Box className="leave-input-wrap">
                                          <CustomSelect
                                            placeholder={leave_type}
                                            options={optionList}
                                            value={
                                              optionList?.find((opt) => {
                                                return (
                                                  opt?.value === valuesOption
                                                );
                                              }) || ''
                                            }
                                            name={`selectedWeekOff.${date}`}
                                            error={
                                              errors?.selectedWeekOff?.[date]
                                            }
                                            helperText={
                                              errors?.selectedWeekOff?.[date]
                                            }
                                            onChange={(e) => {
                                              setSelectedWeekOff({
                                                ...selectedWeekOff,
                                                [date]: e?.value,
                                              });
                                              setFieldValue('selectedWeekOff', {
                                                ...selectedWeekOff,
                                                [date]: e?.value,
                                              });
                                            }}
                                            disabled={
                                              isWeekend || isHoliday || isBoth
                                            }
                                          />
                                        </Box>
                                      </Box>
                                    );
                                  })}
                                </Box>
                              </>
                            ) : leaveHours === 'daily' &&
                              leave_type === 'hour' &&
                              dateRange &&
                              dateRange?.length > 0 ? (
                              <>
                                <Box className="leave-add-inputs-wrap mb8 pt8">
                                  {dateRange?.map((date) => {
                                    const isWeekend =
                                      userDetails?.leave_days_obj?.find(
                                        (leave) =>
                                          leave?.type === 'day_off' &&
                                          date === leave?.date
                                      );
                                    const isHoliday =
                                      userDetails?.leave_days_obj?.find(
                                        (leave) =>
                                          leave?.type === 'holiday' &&
                                          date === leave?.date
                                      );
                                    const isBoth =
                                      userDetails?.leave_days_obj?.find(
                                        (leave) =>
                                          leave?.type === 'day_off_holiday' &&
                                          date === leave?.date
                                      );
                                    return (
                                      <Box
                                        key={date}
                                        className="leave-add-inputs"
                                      >
                                        <Box className="input-hours input-wrap pt16">
                                          <CustomTextField
                                            fullWidth
                                            id={`selectedWeekOff.${date}`}
                                            name={`selectedWeekOff.${date}`}
                                            value={
                                              isBoth
                                                ? 'Day off with Holiday'
                                                : isHoliday
                                                  ? 'Holiday'
                                                  : isWeekend
                                                    ? 'Day-off'
                                                    : selectedWeekOff?.[date] ||
                                                      ''
                                            }
                                            label={dayjs(date).format(
                                              'dddd D MMMM'
                                            )}
                                            placeholder={`Enter ${
                                              leave_type === 'hour'
                                                ? 'hours'
                                                : 'days'
                                            }`}
                                            error={Boolean(
                                              // !selectedWeekOff?.[date] &&
                                              errors?.selectedWeekOff?.[date]
                                            )}
                                            helperText={
                                              // !selectedWeekOff?.[date] &&
                                              errors?.selectedWeekOff?.[date]
                                            }
                                            onBlur={handleBlur}
                                            onChange={(e) => {
                                              const selectweeks = {
                                                ...selectedWeekOff,
                                                [date]: e.target.value,
                                              };
                                              setSelectedWeekOff(selectweeks);
                                              setFieldValue(
                                                'selectedWeekOff',
                                                selectweeks
                                              );
                                              // const total = Object.values(
                                              //   selectweeks
                                              // ).reduce((sum, val) => {
                                              //   const num =
                                              //     parseFloat(val);
                                              //   return (
                                              //     sum +
                                              //     (isNaN(num) ? 0 : num)
                                              //   );
                                              // }, 0);
                                              // setFieldValue(
                                              //   'leaveday',
                                              //   total
                                              // );
                                            }}
                                            onInput={(e) =>
                                              Inputype(e, leave_type)
                                            }
                                            disabled={
                                              isWeekend || isHoliday || isBoth
                                            }
                                          />
                                        </Box>
                                      </Box>
                                    );
                                  })}
                                </Box>
                              </>
                            ) : (
                              <></>
                            )}
                          </>
                        ) : (
                          <></>
                        )}

                        <Divider />
                        <Box className="pt8 pb8">
                          <CustomTextField
                            fullWidth
                            id="reason"
                            name="reason"
                            value={values?.reason}
                            label="Remark"
                            required
                            rows={2}
                            placeholder="Enter remark"
                            error={Boolean(touched?.reason && errors?.reason)}
                            helperText={touched?.reason && errors?.reason}
                            onBlur={handleBlur}
                            onChange={(e) => {
                              if (event.target.value.length <= 160) {
                                handleChange(e);
                              }
                            }}
                            multiline
                          />
                          <Typography className="sub-title-text text-align-end">
                            {values?.reason?.length + '/ 160'}
                          </Typography>
                        </Box>
                      </>
                    )}

                    {userDetails?.request_status !== 'pending' && (
                      <>
                        <Divider />
                        <Typography className="subject-wrap title-text mt8">
                          <span className="fw500"> Subject :</span>{' '}
                          {userDetails?.request_subject}
                        </Typography>
                        <Typography className="reason-wrap title-text mb8">
                          <span className="fw500">Reason :</span>{' '}
                          {userDetails?.request_reason}
                        </Typography>
                        <Divider />
                        <Box className="leave-status-text-wrap mt8 mb8 d-flex">
                          <Box className="status-icon-wrap">
                            {userDetails?.request_status === 'approved' ? (
                              <CheckCircleIcon className="approved-icon" />
                            ) : userDetails?.request_status === 'rejected' ? (
                              <CancelIcon className="rejected-icon" />
                            ) : userDetails?.request_status === 'pending' ? (
                              <PendingActionsIcon className="pending-icon" />
                            ) : userDetails?.request_status === 'cancelled' ? (
                              <RemoveCircleIcon className="cancelled-icon" />
                            ) : null}
                          </Box>{' '}
                          {userDetails?.request_approved_users
                            ?.user_full_name ? (
                            <Typography className="title-text fw400">
                              Your Request Is
                              <span
                                className={`fw600 ${
                                  userDetails?.request_status === 'approved'
                                    ? 'approved-status'
                                    : userDetails?.request_status === 'rejected'
                                      ? 'rejected-status'
                                      : userDetails?.request_status ===
                                          'pending'
                                        ? 'pending-status'
                                        : userDetails?.request_status ===
                                            'cancelled'
                                          ? 'cancelled-status'
                                          : ''
                                }`}
                              >
                                {userDetails?.request_status}
                              </span>
                              On
                              <span className="fw600">
                                {moment(userDetails?.updatedAt).format(
                                  'DD/MM/YYYY hh:mm A'
                                )}
                              </span>
                              By
                              <span className="fw600">
                                {
                                  userDetails?.request_approved_users
                                    ?.user_full_name
                                }
                              </span>
                              With Below Comments
                              <span className="fw600">
                                {userDetails?.approved_by_reason}
                              </span>
                            </Typography>
                          ) : (
                            <Typography className="title-text fw400">
                              Your request was auto
                              <span
                                className={`fw600 ${
                                  userDetails?.request_status === 'approved'
                                    ? 'approved-status'
                                    : userDetails?.request_status === 'rejected'
                                      ? 'rejected-status'
                                      : userDetails?.request_status ===
                                          'pending'
                                        ? 'pending-status'
                                        : userDetails?.request_status ===
                                            'cancelled'
                                          ? 'cancelled-status'
                                          : ''
                                }`}
                              >
                                {userDetails?.request_status}
                              </span>
                              On
                              <span className="fw600">
                                {moment(userDetails?.updatedAt).format(
                                  'DD/MM/YYYY hh:mm A'
                                )}
                              </span>
                              By system
                            </Typography>
                          )}
                        </Box>
                      </>
                    )}
                    {userDetails?.request_status === 'pending' && (
                      <Box className="create-cancel-button mt16 justify-end mt16">
                        <CustomButton
                          variant="contained"
                          title="Update"
                          type="submit"
                        />
                        <CustomButton
                          variant="contained"
                          className="red-button"
                          title="Cancel Leave"
                          onClick={() => {
                            setLeaveApproval('cancelled');
                            handleChange({
                              target: {
                                name: 'leaveApproval',
                                value: 'cancelled',
                              },
                            });
                            setTimeout(async () => {
                              const errors = await validateForm();
                              if (!errors.reason) {
                                cancelLeave(values.reason);
                              } else {
                                setFieldTouched('reason', true);
                                setFieldError('reason', 'Remark is required');
                              }
                            }, 400);
                          }}
                        />
                      </Box>
                    )}
                    {userDetails?.request_status === 'approved' && (
                      <>
                        <Box className="pt8 pb8">
                          <CustomTextField
                            fullWidth
                            id="reason"
                            name="reason"
                            value={values?.reason}
                            label="Remark"
                            required
                            rows={2}
                            placeholder="Enter remark"
                            error={Boolean(touched?.reason && errors?.reason)}
                            helperText={touched?.reason && errors?.reason}
                            onBlur={handleBlur}
                            onChange={(e) => {
                              if (event.target.value.length <= 160) {
                                handleChange(e);
                              }
                            }}
                            multiline
                          />
                          <Typography className="sub-title-text text-align-end">
                            {values?.reason?.length + '/ 160'}
                          </Typography>
                        </Box>
                        <Box className="create-cancel-button mt16 justify-end">
                          <CustomButton
                            variant="contained"
                            title="Cancel Leave"
                            className="red-button"
                            onClick={async () => {
                              const errors = await validateForm();
                              if (!errors.reason) {
                                cancelLeave(values.reason);
                              } else {
                                setFieldTouched('reason', true);
                                setFieldError('reason', 'Remark is required');
                              }
                            }}
                          />
                        </Box>
                      </>
                    )}
                  </Box>
                  <Divider />
                  <Box className="right-side-update-leave-wrap">
                    <Box className="calendar-sec-wrap">
                      <LeaveCard userDetails={leaveBalanceDetails} />
                      <Box className="calendar-sec-wrap">
                        <>
                          <CustomDoubleCalender
                            selectedDate={selectedDate}
                            setSelectedDate={setSelectedDate}
                            searchValue={searchValue}
                            setSearchValue={setSearchValue}
                            calenderData={{ calenderData, type: 'staff' }}
                            getLeaveDetails={getLeaveDetails}
                            userCanView={true}
                            selectedLeave={true}
                            userDetails={userDetails?.start_date}
                            type="staff"
                          />
                        </>
                      </Box>
                    </Box>
                  </Box>
                </Box>
              </Box>
            </Form>
          )}
        </Formik>
      </Box>
    </Box>
  );
}
