@import '../../../styles/variable.scss';

.select-wrap {
  .MuiFormLabel-root {
    left: 3px;
    top: -21px;
    transform: none;
  }
  .MuiSelect-select {
    padding: 7px 7px 7px 15px;
    z-index: 999;
    position: relative;
    margin-top: 2px;
  }
  .MuiSvgIcon-root {
    z-index: 999;
    color: $color-Dark-80 !important;
  }
  fieldset {
    height: 45px;
    background-color: $color-White;
    padding: 1px 16px;
    border-radius: 8px;
    border: 1px solid $color-Dark-10;
    margin-top: 2px;
    border-color: $color-Dark-10 !important;
    border-width: 1px !important;
  }

  .MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline {
    border: 1px solid $color-Dark-10;
  }
  legend {
    display: none !important;
  }
  .MuiFormLabel-root {
    font-family: $PrimaryFont;
    font-size: 12px;
    font-style: normal;
    color: $color-Dark-80;
    font-weight: 500;
    line-height: 18px;
    letter-spacing: -0.5px;
    text-transform: capitalize;
  }
  .MuiFormLabel-root {
    color: $color-Black !important;
  }
  .placeholder {
    color: #a6a8b1;
    opacity: 0.8;
    font-weight: 100 !important;
    font-size: 16px;
    line-height: 18px;
    text-transform: capitalize;
    // font-family: 'Inter, sans-serif';
  }
  .Mui-disabled {
    .MuiOutlinedInput-notchedOutline {
      background-color: $color-Dark-10;
      border: none;
    }
  }
}
.select-dropdown-menu {
  margin-top: 7px;
  max-height: 155px;

  .select-placeholder.Mui-disabled {
    display: none;
  }
  .MuiList-root {
    max-height: 230px;
  }
}
.disabled-select {
  .Mui-disabled {
    cursor: not-allowed !important;
    opacity: 0.9;
  }
  .MuiSelect-select {
    -webkit-text-fill-color: $color-Black !important;
  }
  .MuiOutlinedInput-notchedOutline {
    cursor: not-allowed;
    background-color: $color-White !important;
    border: 1px solid $color-Dark-10 !important;
  }
}
.MuiPopover-root.MuiMenu-root {
  z-index: 999999 !important;
}
/// Small select box design
.model-select-box {
  .MuiSelect-select {
    padding: 3px 1px 1px 10px;
    font-family: $PrimaryFont !important;
    font-size: 14px !important;
    line-height: 21px !important;
    letter-spacing: -0.5px !important;
  }
  fieldset {
    height: 35px;
  }
  .placeholder {
    font-size: 14px;
    line-height: 21px;
    text-transform: capitalize;
  }
}
.model-dropdown-menu.MuiMenu-root {
  .model-menu-item {
    padding: 4px 12px;
  }
}
