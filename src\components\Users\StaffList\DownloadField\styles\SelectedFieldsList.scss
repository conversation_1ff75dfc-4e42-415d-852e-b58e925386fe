@import '@/app/_globals.scss';

// Selected Fields List Component Styles
.selected-fields-list {
  background: var(--color-off-white);
  border-radius: var(--border-radius-md);
  border: var(--normal-sec-border);

  // Empty State
  .empty-state {
    padding: var(--spacing-2xl);
    text-align: center;

    .empty-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--spacing-lg);

      .empty-icon {
        width: var(--spacing-4xl);
        height: var(--spacing-4xl);
        background: var(--color-secondary);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        .icon {
          width: var(--icon-size-md);
          height: var(--icon-size-md);
          color: var(--text-slate-gray);
          stroke-width: 2;
        }
      }

      .empty-text {
        .empty-title {
          font-size: var(--font-size-lg);
          font-weight: var(--font-weight-medium);
          margin-bottom: var(--spacing-sm);
          color: var(--text-color-black);
        }

        .empty-description {
          color: var(--text-light-dark);
          font-size: var(--font-size-sm);
          line-height: var(--line-height-base);
        }
      }
    }
  }

  // List Header
  .list-header {
    padding: var(--spacing-lg);
    border-bottom: var(--normal-sec-border);

    .header-content {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;

      .header-title {
        margin: 0;
      }

      .header-hint {
        color: var(--text-light-dark);
      }
      .required-count {
        color: var(--text-light-dark);
      }
    }
  }

  // Fields Container
  .fields-container {
    padding: var(--spacing-lg);
    max-height: 28rem;
    overflow-y: auto;

    .fields-list {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-sm);

      .field-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: var(--spacing-md);
        border-radius: var(--border-radius-md);
        border: var(--normal-sec-border);
        cursor: move;
        transition: all 0.15s ease;

        &.dragging {
          border-color: var(--color-primary);
          background: rgba(19, 94, 150, 0.05);
          box-shadow: var(--box-shadow-xs);
        }

        &:not(.dragging) {
          &:hover {
            border-color: var(--border-color-light-gray);
            background: rgba(19, 94, 150, 0.02);
          }
        }

        .field-content {
          display: flex;
          align-items: center;
          gap: 0.75rem;

          .drag-controls {
            display: flex;
            align-items: center;
            gap: 0.5rem;

            .drag-handle {
              .drag-icon {
                width: var(--icon-size-xs);
                height: var(--icon-size-xs);
                color: var(--text-slate-gray);
                stroke-width: 2;
              }
            }

            .order-badge {
              color: var(--color-primary);
              background: var(--color-secondary);
              padding: var(--spacing-xs) var(--spacing-sm);
              border-radius: var(--border-radius-xs);
              min-width: var(--spacing-2xl);
              text-align: center;
            }
          }

          .field-info {
            flex: 1;

            .field-header {
              display: flex;
              align-items: center;
              gap: 0.5rem;

              .field-name {
                margin: 0;
              }

              .required-badge {
                background: var(--color-danger-background);
                color: var(--color-danger);
                padding: var(--spacing-xxs) var(--spacing-xsm);
                border-radius: var(--border-radius-xs);
              }
            }

            .field-category {
              color: var(--text-light-dark);
              margin: 0;
            }
          }
        }

        .field-actions {
          display: flex;
          align-items: center;
          gap: 0.25rem;

          // Mobile controls
          .mobile-controls {
            display: flex;
            align-items: center;
            gap: 0.25rem;

            @media (min-width: 1024px) {
              display: none;
            }

            .move-button {
              padding: var(--spacing-xs);
              color: var(--text-light-dark);
              background: transparent;
              border: none;
              cursor: pointer;
              transition: color 0.15s ease;

              &:hover:not(:disabled) {
                color: var(--text-color-black);
              }

              &:disabled {
                opacity: var(--opacity-5);
                cursor: not-allowed;
              }

              &:focus {
                outline: none;
                box-shadow:
                  0 0 0 2px var(--color-primary),
                  0 0 0 4px rgba(19, 94, 150, 0.1);
                border-radius: var(--border-radius-xs);
              }

              .move-icon {
                width: 1rem;
                height: 1rem;
                stroke-width: 2;
              }
            }
          }

          // Remove button
          .remove-button {
            padding: var(--spacing-xs);
            color: var(--text-light-dark);
            background: transparent;
            border: none;
            cursor: pointer;
            transition: color 0.15s ease;

            &:hover:not(:disabled) {
              color: var(--color-danger);
            }

            &:disabled {
              opacity: var(--opacity-5);
              cursor: not-allowed;
            }

            &:focus {
              outline: none;
              box-shadow:
                0 0 0 2px var(--color-danger),
                0 0 0 4px rgba(211, 47, 47, 0.1);
              border-radius: var(--border-radius-xs);
            }

            .remove-icon {
              width: 1rem;
              height: 1rem;
              stroke-width: 2;
            }
          }
        }
      }
    }
  }

  // List Footer
  .list-footer {
    padding: var(--spacing-lg);
    border-top: var(--normal-sec-border);
    background: rgba(19, 94, 150, 0.02);

    .footer-stats {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: var(--font-size-sm);

      .total-count,
      .required-count {
        color: var(--text-light-dark);
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .selected-fields-list {
    .list-header {
      .header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
      }
    }

    .fields-container {
      .fields-list {
        .field-item {
          .field-content {
            .field-info {
              .field-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.25rem;
              }
            }
          }

          .field-actions {
            .mobile-controls {
              display: flex;
            }
          }
        }
      }
    }

    .list-footer {
      .footer-stats {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
      }
    }
  }
}
