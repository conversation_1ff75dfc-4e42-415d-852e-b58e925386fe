'use client';
import React, { useState } from 'react';
import { Box, Typography, InputAdornment } from '@mui/material';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomButton from '@/components/UI/CustomButton';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { ORG_URLS } from '@/helper/constants/urls';
import { setApiMessage } from '@/helper/common/commonFunctions';
import DialogBox from '@/components/UI/Modalbox';
import CloseAccount from '../CloseAccount';
import './changepass.scss';

const ChangePassword = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [showVerifyPassword, setShowVerifyPassword] = useState(false);
  const [passwordTouched, setPasswordTouched] = useState(false);
  const [PINPassword, setPINPassword] = useState('');
  const [validationFeedback, setValidationFeedback] = useState({
    length: false,
    lowercase: false,
    uppercase: false,
    number: false,
    specialChar: false,
  });

  // Validation schema
  // const validationSchema = Yup.object({
  //   password: Yup.string()
  //     .required('Password is required')
  //     .min(8, 'Password must be at least 8 characters')
  //     .matches(
  //     /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*?&#])[A-Za-z\d@$!%*?&#]+$/
  //       'Password must include one uppercase, one lowercase, one number, and one special character'
  //     ),
  //   verify_password: Yup.string()
  //     .required('Please confirm your password')
  //     .oneOf([Yup.ref('password'), null], 'Passwords must match'),
  // });

  // // Initial values
  // const initialValues = {
  //   password: '',
  //   verify_password: '',
  // };

  return (
    <Box className="right-password-sec d-flex flex-col">
      <Formik
        initialValues={{
          password: '',
          verify_password: '',
        }}
        validationSchema={Yup.object({
          password: Yup.string()
            .required('This field is required')
            .min(8, 'Password must be at least 8 characters')
            .matches(
              /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*?&#])[A-Za-z\d@$!%*?&#]+$/,
              'Password must include one uppercase, one lowercase, one number, and one special character'
            ),
          verify_password: Yup.string()
            .required('This field is required')
            .oneOf([Yup.ref('password'), null], 'Passwords must match'),
        })}
        onSubmit={async (requestData, { resetForm }) => {
          let sendData = {
            password: requestData.password,
          };
          try {
            const { status, data } = await axiosInstance.put(
              ORG_URLS?.CHANGE_USER_PASSWORD,
              sendData
            );

            if (status === 200) {
              setApiMessage('success', data?.message);
            } else {
              setApiMessage('error', data?.message);
            }
          } catch (error) {
            setApiMessage('error', error?.response?.data?.message);
          }

          resetForm();
          setPasswordTouched(false);
          setValidationFeedback({
            length: false,
            lowercase: false,
            uppercase: false,
            number: false,
            specialChar: false,
          });
        }}
      >
        {({
          values,
          touched,
          errors,
          handleBlur,
          handleChange,
          handleSubmit,
        }) => (
          <Form onSubmit={handleSubmit} className="change-pass-wrap">
            <Box className="change-pass">
              <Typography className="change-pass-title" variant="h5">
                Change Password
              </Typography>

              <Box className="pt16">
                <CustomTextField
                  fullWidth
                  required
                  id="password"
                  name="password"
                  label="Password"
                  placeholder="Enter password"
                  type={showPassword ? 'text' : 'password'}
                  value={values?.password}
                  error={Boolean(touched?.password && errors?.password)}
                  helperText={touched?.password && errors?.password}
                  onBlur={handleBlur}
                  onChange={(e) => {
                    handleChange(e);
                    setPasswordTouched(true);

                    const password = e.target.value;

                    // Update validation feedback
                    setValidationFeedback({
                      length: password.length >= 8,
                      lowercase: /[a-z]/.test(password),
                      uppercase: /[A-Z]/.test(password),
                      number: /\d/.test(password),
                      specialChar: /[@$!%*?&]/.test(password),
                    });
                  }}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end" className="eye-icon">
                        <Box
                          className="eye-wrap"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? (
                            <VisibilityIcon sx={{ cursor: 'pointer' }} />
                          ) : (
                            <VisibilityOffIcon sx={{ cursor: 'pointer' }} />
                          )}
                        </Box>
                      </InputAdornment>
                    ),
                  }}
                />
              </Box>

              {/* Password feedback */}
              {passwordTouched && (
                <Box className="pt8">
                  <Typography
                    className="validation-text"
                    style={{
                      color: validationFeedback.length ? 'green' : 'red',
                    }}
                  >
                    {validationFeedback.length ? '✔' : '✖'} At least 8
                    characters long
                  </Typography>
                  <Typography
                    className="validation-text"
                    style={{
                      color: validationFeedback.lowercase ? 'green' : 'red',
                    }}
                  >
                    {validationFeedback.lowercase ? '✔' : '✖'} At least one
                    lowercase character
                  </Typography>
                  <Typography
                    className="validation-text"
                    style={{
                      color: validationFeedback.uppercase ? 'green' : 'red',
                    }}
                  >
                    {validationFeedback.uppercase ? '✔' : '✖'} At least one
                    uppercase character
                  </Typography>
                  <Typography
                    className="validation-text"
                    style={{
                      color: validationFeedback.number ? 'green' : 'red',
                    }}
                  >
                    {validationFeedback.number ? '✔' : '✖'} At least one
                    number
                  </Typography>
                  <Typography
                    className="validation-text"
                    style={{
                      color: validationFeedback.specialChar ? 'green' : 'red',
                    }}
                  >
                    {validationFeedback.specialChar ? '✔' : '✖'} At least one
                    special character
                  </Typography>
                </Box>
              )}

              <Box className="pt16">
                <CustomTextField
                  fullWidth
                  id="verify_password"
                  name="verify_password"
                  label="Re-enter Password"
                  required
                  placeholder="Re-enter password"
                  type={showVerifyPassword ? 'text' : 'password'}
                  value={values?.verify_password}
                  error={Boolean(
                    touched?.verify_password && errors?.verify_password
                  )}
                  helperText={
                    touched?.verify_password && errors?.verify_password
                  }
                  className={
                    touched?.password && errors?.password
                      ? 'w100 password-textfield password-error input-field-wrap'
                      : 'w100 password-textfield input-field-wrap'
                  }
                  onBlur={handleBlur}
                  onChange={handleChange}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end" className="eye-icon">
                        <Box
                          className="eye-wrap"
                          onClick={() =>
                            setShowVerifyPassword(!showVerifyPassword)
                          }
                        >
                          {showVerifyPassword ? (
                            <VisibilityIcon sx={{ cursor: 'pointer' }} />
                          ) : (
                            <VisibilityOffIcon sx={{ cursor: 'pointer' }} />
                          )}
                        </Box>
                      </InputAdornment>
                    ),
                  }}
                />
              </Box>
              {/* Password feedback */}
              {/* {passwordTouched && (
                <Box className="pt8">
                  <Typography
                    className=" fw600"
                    style={{
                      color: validationFeedback.length ? 'green' : '#d32f2f',
                    }}
                  >
                    {validationFeedback.length ? '✔' : '✖'} At least 12
                    characters long
                  </Typography>
                  <Typography
                    className=" fw600"
                    style={{
                      color: validationFeedback.lowercase ? 'green' : '#d32f2f',
                    }}
                  >
                    {validationFeedback.lowercase ? '✔' : '✖'} At least one
                    lowercase character
                  </Typography>
                  <Typography
                    className=" fw600"
                    style={{
                      color: validationFeedback.uppercase ? 'green' : '#d32f2f',
                    }}
                  >
                    {validationFeedback.uppercase ? '✔' : '✖'} At least one
                    uppercase character
                  </Typography>
                  <Typography
                    className=" fw600"
                    style={{
                      color: validationFeedback.number ? 'green' : '#d32f2f',
                    }}
                  >
                    {validationFeedback.number ? '✔' : '✖'} At least one
                    number
                  </Typography>
                  <Typography
                    className=" fw600"
                    style={{
                      color: validationFeedback.specialChar
                        ? 'green'
                        : '#d32f2f',
                    }}
                  >
                    {validationFeedback.specialChar ? '✔' : '✖'} At least one
                    special character
                  </Typography>
                </Box>
              )} */}
              <Box className="change-btn-wrap">
                <Box>
                  <CustomButton
                    fullWidth
                    variant="contained"
                    type="submit"
                    title="Change Password"
                  />
                </Box>
              </Box>
            </Box>
          </Form>
        )}
      </Formik>

      <Box className="close-acc-wrap">
        <Typography className="change-pass-title" variant="h5">
          Close Account
        </Typography>
        <Typography component="p" variant="p" className="text-wrap">
          You can permanently delete or temporarily freeze your account.
        </Typography>
        <Box className="change-btn-wrap">
          <CustomButton
            fullWidth
            type="submit"
            variant="outlined"
            title="Close Account"
            onClick={() => {
              setPINPassword(true);
            }}
          />
        </Box>
      </Box>
      <DialogBox
        open={PINPassword}
        handleClose={() => {
          setPINPassword(false);
        }}
        title={'Close Account'}
        content={
          <>
            <Box className="close-aacount-dialog">
              <CloseAccount setPINPassword={setPINPassword} />
            </Box>
          </>
        }
      />
    </Box>
  );
};

export default ChangePassword;
