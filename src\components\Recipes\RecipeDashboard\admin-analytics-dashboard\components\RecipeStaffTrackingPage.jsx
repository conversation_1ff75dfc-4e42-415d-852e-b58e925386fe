'use client';

import React, { useRef, useState } from 'react';
import { Box, Tooltip, Typography } from '@mui/material';
import { useRouter, useSearchParams } from 'next/navigation';
import Icon from '@/components/UI/AppIcon/AppIcon';
import CustomButton from '@/components/UI/CustomButton';
import RecipeStaffTrackingTable from './RecipeStaffTrackingTable';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import DialogBox from '@/components/UI/Modalbox';
import DeleteModal from '@/components/UI/DeleteModal';
import { resetRecipeViewStatistics } from '@/services/recipeService';
import { setApiMessage } from '@/helper/common/commonFunctions';

const RecipeStaffTrackingPage = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const recipeId = searchParams.get('recipeId') || 'demo';
  const tableRef = useRef(null);

  // State for delete confirmation modal
  const [showResetConfirmation, setShowResetConfirmation] = useState(false);

  // Show confirmation modal for reset all
  const handleResetAllClick = () => {
    setShowResetConfirmation(true);
  };

  // Close confirmation modal
  const handleCloseConfirmation = () => {
    setShowResetConfirmation(false);
  };

  // Reset all function that will be called after confirmation
  const handleConfirmResetAll = async () => {
    try {
      const data = await resetRecipeViewStatistics(recipeId, ['all']);
      // Refresh table data after reset
      if (tableRef.current?.refreshData) {
        await tableRef.current.refreshData();
      }
      setApiMessage('success', data?.message);
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    } finally {
      setShowResetConfirmation(false);
    }
  };

  return (
    <div className="section-right-content">
      {/* Header */}
      <Box className="d-flex justify-space-between align-center mb-20">
        <Box className="d-flex align-center">
          <ArrowBackIosIcon
            className="cursor-pointer"
            onClick={() => router.back()}
          />
          <Typography variant="h4" className="body-semibold">
            Recipe Staff Tracking
          </Typography>
        </Box>
        <Tooltip
          title={
            <Typography className="sub-title-text">
              Reset All Statistics
            </Typography>
          }
          arrow
          classes={{
            tooltip: 'info-tooltip-container',
          }}
        >
          <span>
            <CustomButton
              variant="outlined"
              isIconOnly
              startIcon={<Icon name="RotateCcw" size={18} />}
              onClick={handleResetAllClick}
              className="refresh-btn"
            />
          </span>
        </Tooltip>
      </Box>

      {/* Staff Tracking Table */}
      <RecipeStaffTrackingTable recipeId={recipeId} ref={tableRef} />

      {/* Reset All Delete Modal */}
      <DialogBox
        open={showResetConfirmation}
        handleClose={handleCloseConfirmation}
        title="Confirmation"
        className="delete-modal"
        dividerClass="delete-modal-divider"
        content={
          <DeleteModal
            handleCancel={handleCloseConfirmation}
            handleConfirm={handleConfirmResetAll}
            text="Are you sure you want to reset all user statistics for this recipe?"
            confirmText="Yes, Reset All"
            cancelText="Cancel"
          />
        }
      />
    </div>
  );
};

export default RecipeStaffTrackingPage;
