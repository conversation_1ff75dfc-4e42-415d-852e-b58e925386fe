'use client';
import React, { useContext, useState } from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import AppImage from '@/components/UI/AppImage/AppImage';
import RecipeMenu from '../RecipeMenu';
import Link from 'next/link';
import DialogBox from '@/components/UI/Modalbox';
import DeleteModal from '@/components/UI/DeleteModal';
import { DateFormat, getCurrencySymbol } from '@/helper/common/commonFunctions';
// import { trackRecipeView } from '@/services/recipeService';
import AuthContext from '@/helper/authcontext';
import { Tooltip } from '@mui/material';
import { RecipePlaceholder } from '@/helper/common/recipesImgPlaceholder';
import './recipecard.scss';

const RecipeCard = ({
  recipe,
  // viewMode = 'grid',
  isSelected = false,
  isPublicPage = false, // New prop to determine if this is a public page
  onBookmarkToggle,
  onDuplicate,
  onDelete,
  onShare,
  onExport,
  onEdit,
  onView,
  onCardClick,
  onViewHistory,
  onViewActivity,
  onAssign, // New prop for assign functionality
  isAllPermission,
}) => {
  const { authState } = useContext(AuthContext);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const currency = getCurrencySymbol(authState?.currency_details);

  // Show features when NOT on public page (i.e., on private routes)
  const showPrice = !isPublicPage;
  const showRecipeStatus = !isPublicPage;
  const showPrivateFeatures = !isPublicPage;

  // Handle card click for public routes (navigate to view page)
  const handleCardClick = async (e) => {
    // Only handle click on public pages and if onView is provided
    if (isPublicPage && onCardClick) {
      // Prevent click if user clicked on a button or interactive element
      const target = e.target;
      const isInteractiveElement =
        target.closest('button') ||
        target.closest('a') ||
        target.closest('[role="button"]');

      if (!isInteractiveElement) {
        // Track recipe view for analytics on public views
        // if (recipe?.id) {
        //   await trackRecipeView({
        //     recipe_id: recipe?.id,
        //     recipe_name: recipe?.recipe_public_title || recipe?.recipe_title,
        //   });
        // }
        onCardClick(recipe);
      }
    } else {
      onCardClick(recipe);
    }
  };

  // Handle recipe actions with optional chaining
  const handleDuplicate = () => {
    onDuplicate?.(recipe);
  };

  const handleDelete = () => {
    onDelete?.(recipe);
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: recipe?.recipe_title || 'Recipe',
        text: recipe?.recipe_description || '',
        url: window.location.origin + `/recipe/${recipe?.id}`,
      });
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(
        window.location.origin + `/recipe/${recipe?.id}`
      );
      alert('Recipe link copied to clipboard!');
    }
    onShare?.(recipe);
  };

  const handleExport = () => {
    onExport?.(recipe);
  };

  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case 'low':
        return 'recipe-card__difficulty--low';
      case 'medium':
        return 'recipe-card__difficulty--medium';
      case 'hard':
        return 'recipe-card__difficulty--hard';
      default:
        return 'recipe-card__difficulty--default';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'publish':
        return 'recipe-card__status--published';
      case 'draft':
        return 'recipe-card__status--draft';
      default:
        return 'recipe-card__status--default';
    }
  };
  // #TODO:Do not remove the list view code. Once a list view is added, just uncomment it
  // if (viewMode === 'list') {
  //   return (
  //     <div
  //       className={`recipe-card recipe-card--list ${
  //         isSelected ? 'recipe-card--selected' : ''
  //       }`}
  //     >
  //       <div className="recipe-card__list-content">
  //         {/* Selection Checkbox */}
  //         <button
  //           onClick={() => onRecipeSelect?.(recipe?.id)}
  //           className={`recipe-card__checkbox ${
  //             isSelected ? 'recipe-card__checkbox--selected' : ''
  //           }`}
  //         >
  //           {isSelected && <Icon name="Check" size={12} />}
  //         </button>

  //         {/* Recipe Image */}
  //         <div className="recipe-card__list-image">
  //           <Image
  //             src={recipe?.recipe_placeholder?.item_link}
  //             alt={recipe?.recipe_title || 'Recipe'}
  //             className="recipe-card__image"
  //           />
  //         </div>

  //         {/* Recipe Info */}
  //         <div className="recipe-card__list-info">
  //           <div className="recipe-card__list-header">
  //             <h3 className="recipe-card__title">
  //               {recipe?.recipe_title || 'Untitled Recipe'}
  //             </h3>
  //             <div className="recipe-card__list-badges">
  //               <span
  //                 className={`recipe-card__status ${getStatusColor(recipe?.recipe_status)}`}
  //               >
  //                 {recipe?.recipe_status || 'draft'}
  //               </span>
  //             </div>
  //           </div>

  //           <p className="recipe-card__description">
  //             {recipe?.recipe_description || ''}
  //           </p>

  //           <div className="recipe-card__meta">
  //             <span className="recipe-card__meta-item">
  //               <Icon name="DollarSign" size={12} />
  //               <span>
  //                 {currency}
  //                 {recipe?.total_cost?.toFixed(2) || '0.00'}
  //               </span>
  //             </span>
  //             <span className="recipe-card__meta-item">
  //               <Icon name="Users" size={12} />
  //               <span>{recipe?.total_portions || 0} portions</span>
  //             </span>
  //             <span className="recipe-card__meta-item">
  //               <Icon name="Eye" size={12} />
  //               <span>{recipe?.recipe_impression}</span>
  //             </span>
  //             <span
  //               className={`recipe-card__difficulty ${getDifficultyColor(recipe?.recipe_complexity_level)}`}
  //             >
  //               {recipe?.recipe_complexity_level || 'N/A'}
  //             </span>
  //           </div>

  //           {/* Allergen Icons */}
  //           {recipe?.allergens?.length > 0 && (
  //             <div className="recipe-card__allergens">
  //               {recipe?.allergens?.map((allergen, index) => (
  //                 <span key={index} className="recipe-card__allergen">
  //                   <Image
  //                     src={allergen?.icon}
  //                     alt={allergen?.title}
  //                     className="recipe-card__allergen-icon"
  //                   />
  //                 </span>
  //               ))}
  //             </div>
  //           )}
  //         </div>

  //         {/* Actions */}
  //         <div className="recipe-card__list-actions">
  //           <button
  //             onClick={() => onBookmarkToggle?.(recipe?.id)}
  //             className={`recipe-card__action-btn ${
  //               recipe?.is_bookmarked
  //                 ? 'recipe-card__action-btn--bookmarked'
  //                 : ''
  //             }`}
  //           >
  //             <Icon
  //               name={recipe?.is_bookmarked ? 'Bookmark' : 'BookmarkPlus'}
  //               size={16}
  //             />
  //           </button>

  //           <Link
  //             href={`/recipes/recipe-update/${recipe?.slug || recipe?.id}`}
  //             className="recipe-card__action-btn"
  //           >
  //             <Icon name="Edit3" size={16} />
  //           </Link>

  //           <RecipeMenu
  //             recipe={recipe}
  //             onDuplicate={handleDuplicate}
  //             onDelete={() => setDeleteDialogOpen(true)}
  //             onBookmark={() => onBookmarkToggle?.(recipe?.id)}
  //             onShare={handleShare}
  //             onExport={handleExport}
  //             onEdit={onEdit}
  //             onView={onView}
  //             position="right"
  //           />
  //         </div>
  //       </div>
  //     </div>
  //   );
  // }

  // Grid view
  return (
    <>
      <div
        className={`recipe-card recipe-card--grid ${
          isSelected ? 'recipe-card--selected' : ''
        }`}
        onClick={handleCardClick}
        style={{
          cursor: isPublicPage ? 'pointer' : 'default',
        }}
      >
        {/* Recipe Image */}
        <div className="recipe-card__image-container">
          <AppImage
            src={recipe?.recipe_placeholder?.item_link}
            alt={recipe?.recipe_title || 'Recipe'}
            className="recipe-card__image"
            isSvg={true}
            SvgImg={RecipePlaceholder}
          />
          <div className="card-img-overlay"></div>
          {/* Status Badge - Hidden on public pages */}
          {showRecipeStatus && (
            <div className="recipe-card__status-badge">
              <span
                className={`recipe-card__status ${getStatusColor(recipe?.recipe_status)}`}
              >
                {recipe?.recipe_status || 'draft'}
              </span>
            </div>
          )}

          {/* Bookmark Button - Hidden on public pages */}
          {showPrivateFeatures && (
            <Tooltip
              title={<p className="sub-title-text">Bookmark</p>}
              arrow
              classes={{
                tooltip: 'info-tooltip-container',
              }}
            >
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onBookmarkToggle?.(recipe?.id);
                }}
                className={`recipe-card__bookmark-btn ${
                  recipe?.is_bookmarked
                    ? 'recipe-card__bookmark-btn--active'
                    : ''
                }`}
              >
                <Icon
                  name={recipe?.is_bookmarked ? 'Bookmark' : 'BookmarkPlus'}
                  size={16}
                />
              </button>
            </Tooltip>
          )}

          {/* Allergen Icons */}
          {recipe?.allergens?.length > 0 && (
            <div className="recipe-card__allergens-overlay">
              {recipe.allergens.slice(0, 3).map((allergen, index) => (
                <Tooltip
                  key={index}
                  title={
                    <p className="sub-title-text">Contains {allergen?.title}</p>
                  }
                  arrow
                  classes={{
                    tooltip: 'info-tooltip-container',
                  }}
                >
                  <span className="recipe-card__allergen recipe-card__allergen--overlay">
                    <AppImage
                      key={allergen?.icon}
                      src={allergen?.icon}
                      alt={allergen?.title}
                      className="recipe-card__allergen-icon"
                      isSvg={true}
                      SvgImg={RecipePlaceholder}
                    />
                  </span>
                </Tooltip>
              ))}
              {recipe?.allergens?.length > 3 && (
                <Tooltip
                  title={
                    <p className="sub-title-text">
                      {recipe?.allergens?.length - 3} more allergens
                    </p>
                  }
                  arrow
                  classes={{
                    tooltip: 'info-tooltip-container',
                  }}
                >
                  <span className="recipe-card__allergen-tag">
                    +{recipe?.allergens?.length - 3}
                  </span>
                </Tooltip>
              )}
            </div>
          )}
        </div>

        {/* Recipe Content */}
        <div className="recipe-card__content">
          <div>
            <div className="recipe-card__header">
              <h3 className="recipe-card__title">
                <Tooltip
                  title={
                    <p className="sub-title-text">
                      {recipe?.recipe_title || 'Untitled Recipe'}
                    </p>
                  }
                  arrow
                  classes={{
                    tooltip: 'info-tooltip-container',
                  }}
                >
                  <span>{recipe?.recipe_title || 'Untitled Recipe'}</span>
                </Tooltip>
              </h3>
              {recipe?.recipe_complexity_level && (
                <span
                  className={`recipe-card__difficulty ${getDifficultyColor(recipe?.recipe_complexity_level)}`}
                >
                  {recipe?.recipe_complexity_level || 'N/A'}
                </span>
              )}
            </div>

            <p className="recipe-card__description">
              {recipe?.recipe_description || ''}
            </p>

            {/* Recipe Meta */}
            <div className="recipe-card__meta-container">
              <div className="recipe-card__meta">
                <span className="recipe-card__meta-item">
                  <Icon name="Clock" size={12} />
                  <span>
                    {recipe?.recipe_cook_time || recipe?.recipe_preparation_time
                      ? `${Math.round((recipe?.recipe_cook_time / 60 || 0) + (recipe?.recipe_preparation_time / 60 || 0))} min`
                      : 'N/A'}
                  </span>
                </span>
                {recipe?.recipe_impression ? (
                  <span className="recipe-card__meta-item">
                    <Icon name="Eye" size={12} />
                    <span>{recipe?.recipe_impression || 0}</span>
                  </span>
                ) : null}
              </div>
              {/* Price - Hidden on public pages */}
              {showPrice && (
                <span className="recipe-card__price">
                  {currency}
                  {recipe?.total_cost?.toFixed(2) || '0.00'}
                </span>
              )}
            </div>

            {/* Categories */}
            <div className="recipe-card__tags">
              {recipe?.categories?.slice(0, 3).map((category, index) => (
                <span key={index} className="recipe-card__tag">
                  {category}
                </span>
              ))}
              {recipe?.categories?.length > 3 && (
                <span className="recipe-card__tag">
                  +{recipe.categories.length - 3}
                </span>
              )}
            </div>
          </div>
          <div>
            {/* Actions */}
            <div className="recipe-card__actions">
              {/* Visibility Icons - Hidden on public pages */}
              {showPrivateFeatures && (
                <div className="recipe-card__likes">
                  {recipe?.has_recipe_private_visibility && (
                    <Tooltip
                      title={<p className="sub-title-text">Private</p>}
                      arrow
                      classes={{
                        tooltip: 'info-tooltip-container',
                      }}
                    >
                      <span>
                        <Icon name="Lock" size={14} />
                      </span>
                    </Tooltip>
                  )}
                  {recipe?.has_recipe_public_visibility && (
                    <Tooltip
                      title={<p className="sub-title-text">Public</p>}
                      arrow
                      classes={{
                        tooltip: 'info-tooltip-container',
                      }}
                    >
                      <span>
                        <Icon name="Unlock" size={14} />
                      </span>
                    </Tooltip>
                  )}
                </div>
              )}

              <div className="recipe-card__action-buttons">
                {/* Assign Button - Hidden on public pages */}
                {showPrivateFeatures &&
                  isAllPermission &&
                  recipe?.recipe_status === 'publish' && (
                    <Tooltip
                      title={
                        <p className="sub-title-text">
                          Assigned {recipe?.assigned_users?.length} employee
                          {recipe?.assigned_users?.length > 1 ? 's' : ''}
                        </p>
                      }
                      arrow
                      classes={{
                        tooltip: 'info-tooltip-container',
                      }}
                    >
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          onAssign(recipe);
                        }}
                        className="recipe-card__action-btn"
                      >
                        <Icon name="Users" size={14} />
                      </button>
                    </Tooltip>
                  )}

                {/* Edit Link - Hidden on public pages */}
                {showPrivateFeatures && isAllPermission && (
                  <Link
                    href={`/recipes/recipe-update/${recipe?.recipe_slug || recipe?.id}`}
                    className="recipe-card__action-btn"
                  >
                    <Icon name="Edit3" size={14} />
                  </Link>
                )}
                {/* Recipe Menu - Hidden on public pages */}
                {showPrivateFeatures && (
                  <RecipeMenu
                    recipe={recipe}
                    onDuplicate={handleDuplicate}
                    onDelete={() => setDeleteDialogOpen(true)}
                    onBookmark={() => onBookmarkToggle?.(recipe?.id)}
                    onShare={handleShare}
                    onExport={handleExport}
                    onEdit={onEdit}
                    onView={onView}
                    onViewHistory={onViewHistory}
                    onViewActivity={onViewActivity}
                    onAssign={onAssign}
                    position="right"
                    isAllPermission={isAllPermission}
                  />
                )}
              </div>
            </div>

            {/* Last Updated */}
            <div className="recipe-card__updated">
              Updated{' '}
              {/* {new Date(recipe?.updated_at).toLocaleDateString() || 'Unknown'} */}
              {DateFormat(recipe?.updated_at, 'datesWithhour')}
            </div>
          </div>
        </div>
      </div>
      <DialogBox
        open={deleteDialogOpen}
        handleClose={() => setDeleteDialogOpen(false)}
        title="Confirmation"
        className="delete-modal"
        dividerClass="delete-modal-divider"
        content={
          <DeleteModal
            handleCancel={() => setDeleteDialogOpen(false)}
            handleConfirm={() => handleDelete()}
            text={`Are you sure you want to delete "${recipe?.recipe_title || 'this recipe'}"?`}
          />
        }
      />
    </>
  );
};

export default RecipeCard;
