@import '@/styles/variable.scss';

.discount-form-wrap {
  box-shadow: 0px 0px 2px $color-Dark-20;
  padding: var(--spacing-md);
  border-radius: 12px;

  .input-wrap {
    .custom-discount-wrap {
      min-height: 40px;

      .MuiInputBase-root {
        min-height: 40px;

        .MuiInputBase-input {
          padding: 7px 14px !important;
        }
      }
    }

    .apply-btn {
      height: 100%;
      min-height: 40px;
      padding: 7px 24px !important;
    }

    @media (max-width: 575px) {
      flex-direction: column;
    }
  }

  .discount-code-text {
    width: 27%;
  }

  .custom-applied-discount {
    color: $color-medium-sea-green;
    padding-top: 10px;
  }
  .coupon-code-input {
    width: 60%;
  }
  .discount-inner-content-wrap {
    .team-plan-wrap {
      padding: var(--spacing-md) var(--spacing-none);

      .team-plan-text {
        .team-details-wrap {
          font-size: var(--font-size-sm);
          color: $color-Dark-30;
        }
      }
    }

    .discount-number,
    .discount-number-wrap {
      font-size: 20px;
      color: $color-medium-sea-green;
    }

    .total-wrap {
      padding: var(--spacing-md) var(--spacing-none);

      .next-payment-wrap {
        font-size: 14px;
        color: $color-Dark-30;

        @media (max-width: 575px) {
          display: none;
        }
      }

      .total-number-wrap {
        font-size: var(--font-size-lg);
        font-family: var(--font-family-primary);
        font-weight: var(--font-weight-semibold);
        color: var(--text-color-black);
        line-height: var(--line-height-base);
        @media (max-width: 575px) {
          font-size: 20px;
        }
      }

      @media (max-width: 575px) {
        align-items: baseline !important;
        padding: 15px 0px 5px 0px;
      }
    }

    // .next-payment {
    //   display: none;
    //   font-size: 14px;
    //   color: $color-Dark-30;

    //   @media (max-width: 575px) {
    //     display: block;
    //     padding-bottom: 30px;
    //   }
    // }
    .next-payment-text {
      color: var(--text-color-danger);
    }
  }

  .apply-btn,
  .pay-now-btn {
    padding: 7px 24px !important;

    &:hover {
      color: $color-secondary !important;
      box-shadow: none !important;
    }
  }
}
