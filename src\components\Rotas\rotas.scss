.dashboard-filter-section {
  background-color: var(--color-white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-xs);
  padding: var(--spacing-lg);
  .filter-wrap {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    align-items: center;
    gap: var(--spacing-sm);
    overflow: auto;
    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
    }
    .select-box {
      max-width: max-content;
      @media (max-width: 1024px) {
        max-width: 100%;
      }
    }
    .datepicker-filter {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: 10px;
      .today-icon {
        cursor: pointer;
        border: var(--field-border);
        padding: var(--field-padding);
        border-radius: var(--field-radius);
        display: flex;
      }
      .calendar-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 10px;
        border: var(--field-border);
        padding: var(--btn-padding-sm);
        border-radius: var(--field-radius);
        width: 100%;
        .current-month-year-text {
          display: flex;
          justify-content: center;
          width: max-content;
          font-family: var(--font-family-primary);
          color: var(--text-color-black);
          font-weight: var(--font-weight-semibold);
          line-height: var(--line-height-xs);
          gap: 5px;
        }
        svg {
          height: var(--field-icon-size);
          width: var(--field-icon-size);
        }
      }
    }
    .calender-filter {
      justify-content: end;
      @media (max-width: 1024px) {
        justify-content: center;
      }
    }
    .select-wrap {
      max-height: 42px;
      .MuiInputBase-root {
        min-height: 42px;
        margin-top: 0px;
        .MuiSelect-select {
          font-size: var(--font-size-sm);
          line-height: var(--line-height-xs);
          margin-top: 0px;
          min-height: auto;
          .placeholder {
            font-size: var(--font-size-sm);
            line-height: var(--line-height-xs);
          }
        }
        .MuiSvgIcon-root {
          margin-top: 0px;
        }
      }
      fieldset {
        height: 42px;
        margin-top: 0px;
      }
      .MuiOutlinedInput-notchedOutline {
        top: 0;
      }
    }
    .location-select-field {
      .select-wrap {
        width: 150px;
      }
    }
    .sort-by-select-field {
      .select-wrap {
        width: 110px;
      }
    }
    .calender-filter {
      display: flex;
      align-items: center;
      gap: 5px;
      .calender-day-month-btn {
        // padding: var(--field-padding-sm);
        &.active {
          background-color: var(--btn-color-primary);
          color: var(--btn-text-color-white);
        }
      }
    }
    .rota-filter-left-section {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      gap: var(--spacing-sm);
    }
    .rota-filter-right-section {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      gap: var(--spacing-sm);
      @media (max-width: 1024px) {
        justify-content: center;
      }
      .filter-options-menu {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 2px;
        cursor: pointer;
        border: var(--field-border);
        padding: var(--btn-padding-sm);
        border-radius: var(--field-radius);
        min-height: 36px;
        svg {
          width: var(--field-icon-size);
          height: var(--field-icon-size);
          fill: var(--icon-color-primary);
        }
      }
      .header-shift-publish-button {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 2px;
        border: var(--field-border);
        border-radius: var(--field-radius);
        background: var(--btn-color-primary);
        color: var(--btn-text-color-white);
        cursor: pointer;
        min-height: 36px;
        svg {
          width: var(--field-icon-size);
          height: var(--field-icon-size);
        }
        .shift-publish-button {
          display: flex;
          justify-content: center;
          align-items: center;
          gap: 5px;
          padding: var(--btn-padding-sm);
          background: var(--btn-color-primary);
          color: var(--btn-text-color-white);
          border-radius: var(--field-radius-left);

          &:hover {
            opacity: 0.7;
          }
          .shift-publish-text {
            color: var(--text-color-white);
            line-height: var(--line-height-xs);
            width: max-content;
          }
        }
        .shift-publish-other-options-button {
          display: flex;
          justify-content: center;
          align-items: center;
          border-left: var(--field-border);
          padding: var(--field-padding);
          border-radius: var(--field-radius-right);
          background: var(--btn-color-primary);
          &:hover {
            opacity: 0.7;
          }
        }
        &.shift-publish-button-disabled {
          .shift-publish-button {
            opacity: 0.5;
            cursor: not-allowed;
            &:hover {
              opacity: 0.5;
            }
          }
        }
      }
    }
  }
}
.dashboard-calendar-section {
  background-color: var(--color-white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-xs);
  padding: var(--spacing-lg);
  margin-top: var(--spacing-sm);
  display: flex;
  .fc .fc-scrollgrid-section-header.fc-scrollgrid-section-sticky > * {
    top: -30px;
  }
  .fc-daygrid-day {
    cursor: pointer;
    &.fc-day-today {
      background-color: var(--color-secondary);
    }
  }
  .fc-dayGridMonth-view {
    .fc-daygrid-day-events {
      display: none;
    }
  }
  .fc-timeGridDay-view {
    .fc-timegrid-cols {
      .fc-day-today {
        background-color: transparent;
      }
    }
    .fc-timegrid-slot {
      height: 40px;
    }
    .fc-daygrid-day-events {
      display: block;
    }
    .fc-timegrid-event-harness {
      // width: 200px;
      .fc-timegrid-event {
        background: var(--color-light-gray);
        border: var(--border-width-sm) var(--border-style-solid)
          var(--color-light-gray);
      }
    }
  }
  .fc-daygrid-day-number {
    width: 100%;
    text-align: left;
  }
  .event-shifts-count-wrap {
    height: 80px;
    .event-count-wrap {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
      .event-count {
        background: var(--color-dark);
        border-radius: var(--border-radius-full);
        min-width: 20px;
        height: 20px;
        padding: var(--spacing-xxs);
        font-size: var(--font-size-xs);
        color: var(--text-color-white);
        font-weight: var(--font-size-semibold);
        display: flex;
        justify-content: center;
        align-items: center;
        font-family: var(--font-family-primary);
      }
    }
    .event-users-open-shifts-count-wrap {
      display: flex;
      gap: 10px;
      position: absolute;
      bottom: 0px;
      svg {
        width: var(--icon-size-xxs);
        height: var(--icon-size-xxs);
      }
      .calendar-event-list-total-users,
      .calendar-event-list-open-shifts {
        display: flex;
        align-items: center;
        gap: 3px;
      }
      .calendar-event-list-emp-total {
        font-size: var(--font-size-sm);
        line-height: var(--line-height-xs);
        font-weight: var(--font-weight-semibold);
        font-family: var(--font-family-primary);
      }
    }
  }
  .event-shifts-details-container {
    height: 80px;
    overflow: auto;
    p {
      font-family: var(--font-family-primary);
      font-size: var(--font-size-xs);
      line-height: var(--line-height-xs);
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
    .event-shifts-details-wrap {
      margin-bottom: var(--spacing-xs);
      border-radius: var(--border-radius-sm);
      color: var(--text-color-white);
      background: var(--color-light-dark);
      text-align: left;
      .event-shifts-details-header {
        background: var(--color-dark);
        border-radius: var(--border-radius-sm-top);
        .event-shifts-details-title {
          padding: var(--spacing-sm);
        }
      }
      .event-shifts-details-body {
        padding: var(--spacing-sm);
        .event-shifts-details-time {
          font-weight: var(--font-weight-semibold);
        }
        .event-shifts-details-role {
          margin-top: var(--spacing-xxs);
        }
      }
    }
  }
  .event-calendar-shifts-day-wrap {
    padding: var(--spacing-sm);
    overflow: auto;
    height: 100%;
    .event-calendar-shifts-day-details {
      display: flex;
      gap: 10px;
      .event-calendar-shifts-avatar {
        height: fit-content;
        .MuiAvatar-root {
          background-color: var(--color-white);
          color: var(--color-light-gray);
          font-weight: var(--font-weight-semibold);
          width: var(--icon-size-lg);
          height: var(--icon-size-lg);
          font-size: var(--font-size-base);
          font-family: var(--font-family-primary);
        }
      }
    }
  }
  .dashboard-calendar {
    width: 80%;
  }
  .dashboard-right-side-filter-section {
    width: 20%;
    border: var(--border-width-xs) var(--border-style-solid)
      var(--color-light-gray);
    padding: var(--spacing-lg);
    .dashboard-right-filter {
      .dashboard-right-filter-checkbox {
        .MuiButtonBase-root {
          padding: var(--spacing-xxs);
        }
        .dashboard-right-filter-check-box-label {
          display: flex;
          align-items: center;
          gap: 5px;
          text-transform: capitalize;
          font-family: var(--font-family-primary);
          font-size: var(--font-size-sm);
          svg {
            width: var(--icon-size-xxs);
            height: var(--icon-size-xxs);
          }
        }
        .MuiFormControlLabel-root {
          flex-direction: row-reverse;
          width: 100%;
          margin-left: var(--spacing-none);
          margin-right: var(--spacing-none);
          .MuiFormControlLabel-label {
            width: 100%;
          }
        }
      }
    }
  }
  // rota clandar view
  .rota-calendar {
    width: 100%;
  }
}
// avilability clandar view
.availability-calendar-view {
  background-color: var(--color-white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-xs);
  padding: var(--spacing-lg);
  margin-top: var(--spacing-sm);
  display: flex;
  .fc .fc-scrollgrid-section-header.fc-scrollgrid-section-sticky > * {
    top: -30px;
  }
  .availability-calendar {
    width: 100%;
    cursor: pointer;
    .fc-daygrid-day {
      background: var(--color-secondary);
    }
    .fc-daygrid-day-events {
      display: none;
    }
    .fc-day-today {
      .fc-daygrid-day-number-text {
        background: var(--color-primary);
        color: var(--text-color-white);
      }
    }
    .fc-daygrid-day-top {
      flex-direction: row;
      .fc-daygrid-day-number {
        width: 100%;
        &:hover {
          background: var(--color-light-blue);
        }
      }
      .availability-calendar-shifts-day-wrap {
        .availability-calendar-shifts-header {
          .fc-daygrid-day-number-text {
            font-family: var(--font-family-primary);
            border-radius: var(--border-radius-full);
            padding: 2px 8px;
            font-size: var(--font-size-sm);
            line-height: var(--line-height-xs);
            width: fit-content;
          }
          .availability-menu-icon {
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: var(--border-radius-xs);
            position: absolute;
            top: 3px;
            right: 3px;
            padding: var(--spacing-xs);
            background: var(--color-light-gray);
            cursor: pointer;
            svg {
              width: var(--icon-size-xs);
              height: var(--icon-size-xs);
              fill: var(--icon-color-black);
            }
          }
        }
        .availability-calendar-shifts-day-details {
          height: 70px;
          .availability-calendar-shifts-day-no-event-details {
            padding-top: var(--spacing-sm);
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            .add-icon {
              fill: var(--color-primary);
            }
          }
          .availability-calendar-shifts-day-event-details {
            padding-top: var(--spacing-sm);
            width: 100%;
            height: 100%;
            overflow: auto;
            .availability-event-details {
              .availability-event {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                font-family: var(--font-family-primary);
                border-radius: var(--border-radius-lg);
                gap: 5px;
                padding: var(--spacing-xs) var(--spacing-sm);
                width: fit-content;
                margin-bottom: var(--spacing-xs);
                span {
                  font-size: var(--font-size-sm);
                  line-height: var(--line-height-sm);
                  font-weight: var(--font-weight-medium);
                  letter-spacing: var(--letter-spacing-wide);
                }
                svg {
                  width: var(--icon-size-xxs);
                  height: var(--icon-size-xxs);
                }
                &.onDay {
                  background: var(--color-success-opacity);
                  color: var(--color-success);
                }
                &.offDay {
                  background: var(--color-danger-opacity);
                  color: var(--color-danger);
                }
              }
            }
          }
        }
      }
    }
  }
}
.calendar-year-month-select-field {
  margin-top: 10px !important;
  z-index: 1600 !important;
  .MuiPickersLayout-root {
    padding: var(--spacing-sm);
    .MuiDateCalendar-root {
      max-height: max-content;
      .MuiDayCalendar-header {
        justify-content: space-evenly;
      }
      .MuiPickersCalendarHeader-root {
        .MuiPickersFadeTransitionGroup-root {
          .MuiPickersCalendarHeader-label {
            font-family: var(--font-family-primary);
          }
        }
      }
      .MuiPickersFadeTransitionGroup-root {
        .MuiPickersMonth-monthButton,
        .MuiPickersYear-yearButton,
        .MuiPickersDay-root,
        .MuiDayCalendar-weekNumber {
          font-family: var(--font-family-primary);
          width: 100%;
          border: var(--field-border);
          border-radius: var(--border-radius-xs);
          margin: var(--spacing-xxs);
          &.Mui-selected {
            background: var(--color-primary);
            color: var(--text-color-white);
          }
        }
      }
    }
  }
}
.calendar-event-list-tooltip-custom {
  width: 200px;
  background-color: var(--color-black) !important;
  .MuiTooltip-arrow {
    color: var(--color-black);
  }
  .calendar-event-list-tooltip-role {
    font-weight: var(--font-weight-semibold);
    letter-spacing: var(--letter-spacing-normal) !important;
  }
  .calendar-event-list-tooltip-user-details {
    margin-top: var(--spacing-lg);
    .calendar-event-list-tooltip-location {
      letter-spacing: var(--letter-spacing-normal) !important;
      font-style: italic;
    }
    .calendar-event-list-tooltip-users {
      letter-spacing: var(--letter-spacing-normal) !important;
      margin-left: var(--spacing-sm);
      list-style-type: none;
      span {
        font-style: italic;
      }
    }
  }
}
.calendar-event-other-details-tooltip {
  width: auto;
  .p12 {
    letter-spacing: 0px !important;
  }
}
.calendar-event-list-no-role-tooltip-custom {
  background-color: var(--color-dark) !important;
  .MuiTooltip-arrow {
    color: var(--color-dark);
  }
}

// common header modal
.common-header-menu-modal {
  .MuiPaper-root {
    position: unset !important;
    transform: none !important;
    padding: 20px !important;
  }
}
// Copy Range Modal
.custom-range-modal {
  .MuiPaper-root {
    max-width: 50% !important;
    @media (max-width: 991px) {
      max-width: 60% !important;
    }
    @media (max-width: 768px) {
      max-width: 80% !important;
    }
  }
}
