.version-title-header {
  // margin-bottom: 16px;
  padding: var(--spacing-xs) var(--spacing-lg);
}
.recipe-history-main-container {
  height: 100%;
  padding: var(--spacing-none) !important;
  .recipe-history-container {
    height: 100%;
    overflow: auto;
    padding: var(--spacing-xl) !important;
    width: 100%;
  }

  // Card-based layout styles
  .recipe-history-cards-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
  }

  .recipe-history-card {
    border: var(--normal-sec-border);
    border-radius: var(--border-radius-sm);
    background-color: var(--color-white);
    padding: var(--spacing-lg);
    // cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      // border-color: var(--color-primary-light);
    }

    &.active {
      border-color: var(--color-primary);
      background-color: var(--color-primary-opacity);
    }

    .recipe-history-card-header {
      margin-bottom: var(--spacing-sm);

      .recipe-history-card-id {
        color: var(--color-primary);
        font-weight: 600;
        font-size: var(--font-size-sm);
      }

      .recipe-history-card-title {
        font-family: var(--font-family-primary);
        color: var(--text-color-primary);
        font-weight: 600;
        font-size: var(--font-size-base);
      }

      .recipe-history-status-badge {
        padding: 2px 8px;
        border-radius: var(--border-radius-xs);
        font-size: var(--font-size-xs);
        font-weight: 500;

        &.latest {
          background-color: var(--color-success-light);
          color: var(--color-success);
          border: 1px solid var(--color-success);
        }
      }
    }

    .recipe-history-card-content {
      margin-bottom: var(--spacing-sm);

      .recipe-history-card-changes {
        font-family: var(--font-family-primary);
        color: var(--text-color-secondary);
        font-size: var(--font-size-sm);
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    }

    .recipe-history-card-footer {
      .recipe-history-card-user {
        font-family: var(--font-family-primary);
        color: var(--text-color-primary);
        font-size: var(--font-size-sm);
        font-weight: 500;
      }

      .recipe-history-card-date {
        font-family: var(--font-family-primary);
        color: var(--text-color-slate-gray);
        font-size: var(--font-size-sm);
      }

      .recipe-history-card-action {
        font-family: var(--font-family-primary);
        color: var(--text-color-slate-gray);
        font-size: var(--font-size-sm);
        font-weight: 500;
        text-transform: capitalize;
      }
    }
  }
  .recipes-history-end-message {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--text-color-secondary);
    font-size: var(--font-size-sm);

    p {
      margin: 0;
      font-weight: 500;
      font-family: var(--font-family-primary);
    }
  }
}
