.haccp-section-wrapper {
  .haccp-section__header {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xxl);
    @media (max-width: 768px) {
      flex-wrap: wrap;
    }
    .haccp-section__use-from {
      max-width: 250px;
      flex-grow: 1;
    }
  }

  .haccp-section__card {
    display: flex;
    gap: var(--spacing-lg);
    background-color: var(--color-secondary);
    border: 1px solid var(--border-color-light-gray);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    @media (max-width: 768px) {
      flex-direction: column;
      position: relative;
      gap: var(--spacing-md);
    }
  }

  .haccp-section__card-left {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    max-width: 200px;
    width: 100%;
    @media (max-width: 768px) {
      gap: var(--spacing-md);
    }
  }

  .haccp-section__card-middle {
    flex-grow: 1;
  }

  .haccp-section__card-actions {
    display: flex;
    gap: var(--spacing-sm);
    height: max-content;
    cursor: pointer;
    @media (max-width: 768px) {
      position: absolute;
      top: var(--spacing-lg);
      right: var(--spacing-lg);
    }
  }

  .haccp-section__add-button {
    margin-top: var(--spacing-lg);
  }
}
