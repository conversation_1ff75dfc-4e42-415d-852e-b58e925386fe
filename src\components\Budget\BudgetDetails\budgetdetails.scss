@import '@/styles/variable.scss';

.Budget-details-section {
  .Budget-details {
    padding: 12px 16px 16px;
    margin: 32px 5px 2px 3px;
    border-radius: 8px;
    border-bottom-left-radius: 8px !important;
    border-bottom-right-radius: 8px !important;
    box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
    position: relative;
    .budget-status {
      // position: absolute;
      // right: 10px;
      // top: 0;
      margin-left: 8px;
      max-width: max-content;
      display: flex;
      align-items: center;
      .invited-ongoing {
        border: 0;
        padding: 1.5px 8px;
        border-radius: 3px;
        background-color: var(--color-warning);
        color: var(--text-color-black);
      }
      .invited-accepted {
        border: 0;
        padding: 1.5px 8px;
        border-radius: 3px;
        background-color: var(--color-green);
        color: var(--text-color-white);
      }
    }
    .Budget-table {
      margin-top: 12px;
      background-color: var(--color-white);
    }
    .Budget-details-action {
      display: flex;
      justify-content: space-between;
      gap: 8px;
      @media (max-width: 768px) {
        flex-direction: column-reverse;
      }
      .budget-branch-filter {
        width: 50%;
        display: flex;
        .branch-name-year {
          margin-right: 12px;
          min-width: 200px;
          width: 50%;
          border-right: var(--normal-sec-border);
        }
        .branch-filter {
          width: 50%;
          border-right: var(--normal-sec-border);
        }
        @media (max-width: 1800px) {
          width: 70%;
          .branch-name-year {
            width: 50%;
          }
          .branch-filter {
            width: 50%;
          }
        }
        @media (max-width: 1490px) {
          width: 60%;
          display: block;
          .branch-name-year,
          .branch-filter {
            border-right: 0;
          }

          .branch-name-year,
          .branch-filter {
            width: 100%;
          }
        }
        @media (max-width: 768px) {
          width: 100%;
        }
      }
      .all-branch-filter {
        width: calc(100% - 55px);
        .all-branch {
          width: 100% !important;
          border-right: 0 !important;
        }
        @media (max-width: 768px) {
          width: 100%;
        }
      }
      .budget-save-actions {
        width: 40%;
        display: flex;
        justify-content: flex-end;
        flex-wrap: wrap;
        gap: var(--spacing-sm);
        // .save-buttons {
        //   margin-left: 16px;
        //   // width: 50%;
        //   .custom-btns {
        //     justify-content: flex-end;
        //   }
        // }
        .budget-icons {
          justify-content: center;
        }
        @media (max-width: 1800px) {
          width: 40%;
        }
        // @media (max-width: 1490px) {
        //   width: 30%;
        // }
        @media (max-width: 1490px) {
          width: 50%;
          display: block;
          .save-buttons,
          .budget-icons {
            width: 100%;
          }
          // .save-buttons {
          //   margin-left: 0;
          // }
          .budget-icons {
            justify-content: flex-end;
            padding-right: 0;
            border-right: 0;
          }
        }
        @media (max-width: 768px) {
          width: 100%;
        }
      }
      .all-branch-actions {
        width: 50px;
        @media (max-width: 768px) {
          width: 24px;
        }
      }
    }
    @media (max-width: 767px) {
      padding: 12px 16px 16px;
    }
  }
  .budget-graphs {
    border-radius: 8px;
    padding: 2px;
    // border-bottom-left-radius: 8px !important;
    // border-bottom-right-radius: 8px !important;
    // box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
    border: var(--normal-sec-border);
    .graphs-title {
      color: var(--color-dark-blue);
    }
  }
  .budget-icons {
    height: max-content;
    gap: 12px;
    justify-content: flex-end;
    .action-icon {
      cursor: pointer;
    }
    .lock-icon {
      path {
        stroke: var(--color-black);
        stroke-width: 1.5;
      }
    }
    .history-icon {
      svg {
        fill: var(--color-black) !important;
        stroke-width: 0 !important;
      }
    }
  }
  // .budget-target-sec {
  //   display: flex;
  //   gap: 12px;
  //   .budget-target {
  //     max-width: 230px;
  //   }
  // }
  .save-btns-icon {
    .save-btn {
      max-width: 185px;
      min-width: max-content;
    }
    // flex-direction: column;
  }
  // .infinite-scroll-component {
  //   overflow: hidden !important;
  // }
  .assign-icons {
    path {
      stroke-width: 1.6px;
    }
  }
}
.budget-graphs-section {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  margin-top: 12px;
  .area-chart {
    width: calc(100% - 340px - 24px);
    @media (max-width: 1399px) {
      width: 100%;
    }
  }
  .radial-chart {
    width: 340px;
    @media (max-width: 1399px) {
      width: 100%;
    }
  }
}
.border-right-0 {
  border-right: 0 !important;
}
.seasonality-tooltip-container {
  background-color: var(--color-white) !important;
  color: var(--text-color-black) !important;
  box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
  // box-shadow: va;
  .tooltip-seasonality-menu {
    .MuiMenuItem-root {
      padding: 4px 0;
      &:hover {
        background-color: var(--color-white);
      }
    }
  }
  .MuiTooltip-arrow {
    color: var(--color-white) !important;
    &::before {
      box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
    }
  }
}
