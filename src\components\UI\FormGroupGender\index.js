import React from 'react';
import { Box, Typography } from '@mui/material';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomCheckbox from '../CustomCheckbox';
import './gender.scss';

export default function Gender({
  setFieldValue,
  isRequire,
  keyName,
  keyValue,
  disable,
}) {
  return (
    <Box className="gender-section">
      <Typography className="field-label" id={keyName}>
        Gender
        {isRequire && <span className="required">*</span>}
      </Typography>
      <Box className="gender-option">
        <Box>
          <CustomCheckbox
            name="gender"
            onChange={() => {
              setFieldValue(keyName, 'male');
            }}
            disabled={disable}
            checked={keyValue === 'male'}
            label="Male"
            labelPlacement="start"
          />
        </Box>
        <Box>
          <CustomCheckbox
            disabled={disable}
            name="gender"
            checked={keyValue === 'female'}
            onChange={() => {
              setFieldValue(keyName, 'female');
            }}
            label="Female"
            labelPlacement="start"
          />
        </Box>
        <Box className="other-field">
          <Typography className="title-text">Other</Typography>
          <CustomTextField
            id="other"
            name="gender"
            placeholder="Other"
            value={keyValue !== 'female' && keyValue !== 'male' ? keyValue : ''}
            onChange={(e) => {
              setFieldValue(keyName, e.target.value);
            }}
            disabled={disable}
            className="other-textfield"
          />
        </Box>
      </Box>
    </Box>
  );
}
