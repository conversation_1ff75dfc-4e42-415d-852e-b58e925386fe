// src/pages/user-limit-exceeded-dialog/index.jsx
import React from 'react';
import PurchasedPlan from '../../../../../public/images/PurchasedPlan.jpg';
import CustomButton from '@/components/UI/CustomButton';
import Image from 'next/image';
import { Box, Typography } from '@mui/material';
import { useRouter } from 'next/navigation';
import { fetchFromStorage } from '@/helper/context';
import { identifiers } from '@/helper/constants/identifier';
import { handleLogout } from '@/services/authService';
import '../StorageLimit/storagelimit.scss';

const PlanExpiredDialog = ({ isOrgView, setRestrictedModal }) => {
  const router = useRouter();
  const handleUpgradePlan = () => {
    if (isOrgView) {
      setRestrictedModal(false);
      router.push('/org/organization?tab=2');
    } else {
      setRestrictedModal(false);
    }
  };
  const isDeviceId = fetchFromStorage(identifiers?.DEVICEID);

  const handleLogoutClick = () => {
    setRestrictedModal(false);
    handleLogout(router, isDeviceId);
  };

  return (
    <Box className="user-limit-container storage-limit-container">
      {/* Dialog Overlay */}
      <Box className={`dialog-overlay visible`}>
        <Box className="dialog-container">
          <Box className={`dialog-content visible`}>
            {/* Dialog Content */}
            <Box className="dialog-body">
              {/* Image */}
              <Box className="dialog-image-container">
                <Box className="dialog-image-wrapper">
                  <Image
                    src={PurchasedPlan}
                    alt="profile photo"
                    className="dialog-image"
                  />
                </Box>
              </Box>

              {/* Text Content */}
              <Box className="dialog-text">
                <Typography className="title-text fw600 dialog-message">
                  Oops! Plan has expired
                </Typography>
                <Typography className="title-text dialog-description">
                  It looks like your current plan has expired.
                </Typography>
                <Typography className="title-text dialog-description">
                  Don't miss out! Renew your plan now to keep enjoying all
                  awesome features and stay connected with your team.
                </Typography>
              </Box>

              {/* Action Buttons */}
              <Box className="dialog-actions profile-incomplete-actions">
                <CustomButton
                  variant="contained"
                  title={isOrgView ? 'Upgrade Plan' : 'Contact Admin'}
                  fullWidth
                  onClick={handleUpgradePlan}
                />
                <CustomButton
                  variant="outlined"
                  fullWidth
                  title="Logout"
                  onClick={handleLogoutClick}
                />
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default PlanExpiredDialog;
