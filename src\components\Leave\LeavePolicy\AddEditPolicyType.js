'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Box, Typography } from '@mui/material';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import { useRouter, useSearchParams } from 'next/navigation';
import { identifiers } from '@/helper/constants/identifier';
import PreLoader from '@/components/UI/Loader';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomSelect from '@/components/UI/CustomSelect';
import CustomButton from '@/components/UI/CustomButton';
import CustomCheckbox from '@/components/UI/CustomCheckbox';
import SelectableChip from '@/components/UI/SelectableChip';
import CustomColorPicker from '@/components/UI/CustomColorPicker';

const AddEditPolicyType = ({ getLeaveTypeList }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const policyId = searchParams.get('policy_id');
  const anuualExist = searchParams.get('anuualExist');
  const isEdit = searchParams.get('is_edit');
  const formikRef = useRef(null);
  const [loader, setLoader] = useState(false);
  const [policyTypeData, setPolicyTypeData] = useState('');

  // GET POLICY TYPE DETAILS
  const getPolicytypeDetails = async () => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_LEAVE_TYPE + `?id=${policyId}`
      );

      if (status === 200) {
        setLoader(false);
        const policyData = data?.data?.[0];
        setPolicyTypeData(policyData);

        // formikRef.current.setFieldValue('policyName', policyData?.name || '');
        // formikRef.current.setFieldValue(
        //   'policyStatus',
        //   policyData?.status || 'active'
        // );
        // formikRef.current.setFieldValue(
        //   'leaveType',
        //   policyData?.leave_deduction_type || 'unpaid'
        // );
        // formikRef.current.setFieldValue('remark', policyData?.remark || '');
        // formikRef.current.setFieldValue(
        //   'annualLeave',
        //   policyData?.has_annual_leave || false
        // );
      }
    } catch (error) {
      setLoader(false);
      setPolicyTypeData('');
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  useEffect(() => {
    if (isEdit === 'true') {
      getPolicytypeDetails();
    }
  }, [isEdit]);

  const leaveTypeOpt = [
    { label: 'Paid', value: 'paid' },
    { label: 'Unpaid', value: 'unpaid' },
  ];

  return (
    <>
      {loader && <PreLoader />}
      <Formik
        innerRef={formikRef}
        initialValues={{
          policyName: policyTypeData?.name || '',
          policyStatus: policyTypeData?.status || 'active',
          leaveType: policyTypeData?.leave_deduction_type || 'unpaid',
          remark: policyTypeData?.remark || '',
          annualLeave: policyTypeData?.has_annual_leave ? true : false,
          leave_color: policyTypeData?.leave_type_color || '#006bff',
        }}
        enableReinitialize
        validationSchema={Yup.object().shape({
          policyName: Yup.string().trim().required('This field is required'),
          policyStatus: Yup.string().trim().required('This field is required'),
        })}
        onSubmit={async (requestData) => {
          const sendData = {
            name: requestData?.policyName,
            status: requestData?.policyStatus,
            remark: requestData?.remark,
            leave_type_color: requestData?.leave_color,
            leave_deduction_type: requestData?.leaveType,
            ...((!anuualExist || anuualExist === 'false') && {
              has_annual_leave: requestData?.annualLeave,
            }),
          };
          let url = '';
          if (isEdit === 'true') {
            url = URLS.LEAVE_TYPE + `/${policyId}`;
          } else {
            url = URLS.LEAVE_TYPE;
          }
          try {
            setLoader(true);
            const { status, data } = await axiosInstance.post(url, sendData);

            if (status === 200 || status === 201) {
              if (data?.status) {
                setApiMessage('success', data?.message);
                getLeaveTypeList('', true);
                setTimeout(() => {
                  router.back();
                }, 1000);
              } else {
                setApiMessage('error', data?.message);
              }
              setLoader(false);
            }
          } catch (error) {
            setLoader(false);
            setApiMessage('error', error?.response?.data?.message);
          }
        }}
      >
        {({
          errors,
          touched,
          handleBlur,
          values,
          handleSubmit,
          handleChange,
          setFieldValue,
          dirty,
          isValid,
        }) => (
          <Form onSubmit={handleSubmit}>
            <Box className="pb24 add-leave-wrap">
              <Box className="display-grid">
                <Box>
                  <CustomTextField
                    fullWidth
                    id="policyName"
                    name="policyName"
                    label="Name"
                    placeholder="Name"
                    value={values?.policyName}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={Boolean(touched?.policyName && errors?.policyName)}
                    helperText={touched?.policyName && errors?.policyName}
                    required
                  />
                </Box>
                <Box>
                  <CustomSelect
                    id="policyStatus"
                    name="policyStatus"
                    label="Status"
                    placeholder="Status"
                    options={identifiers?.CARD_STATUS}
                    // value={values?.policyStatus}
                    value={
                      identifiers?.CARD_STATUS?.find((opt) => {
                        return opt?.value === values?.policyStatus;
                      }) || ''
                    }
                    error={Boolean(
                      touched?.policyStatus && errors?.policyStatus
                    )}
                    helperText={touched?.policyStatus && errors?.policyStatus}
                    onChange={(e) => {
                      setFieldValue('policyStatus', e?.value);
                    }}
                    isClearable={false}
                    required
                    disabled={
                      values?.policyStatus === 'active' &&
                      policyTypeData?.has_annual_leave
                    }
                  />
                </Box>
                <Box>
                  <CustomColorPicker
                    id="leave_color"
                    name="leave_color"
                    label="Leave color"
                    required
                    value={values?.leave_color}
                    onChange={(val) => {
                      setFieldValue('leave_color', val);
                    }}
                  />
                </Box>

                <Box className="leave-type">
                  <Typography className="other-field-label">
                    Consider Leave As<span className="required">*</span>
                  </Typography>
                  <Box className="d-flex chip-container gap-sm">
                    {leaveTypeOpt?.map((option) => (
                      <SelectableChip
                        key={option?.value}
                        label={option?.label}
                        selected={option?.value === values?.leaveType}
                        onClick={() =>
                          setFieldValue('leaveType', option?.value)
                        }
                      />
                    ))}
                  </Box>
                </Box>
              </Box>
              <Box className="pt16">
                <CustomTextField
                  fullWidth
                  id="remark"
                  name="remark"
                  label="Remark"
                  placeholder="Remark"
                  value={values?.remark}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  multiline
                  rows={4}
                />
              </Box>
              {(!anuualExist ||
                anuualExist === 'false' ||
                policyTypeData?.has_annual_leave) &&
                values?.policyStatus === 'active' && (
                  <Box className="pt16">
                    <CustomCheckbox
                      disabled={policyTypeData?.has_annual_leave}
                      name="annualLeave"
                      checked={values?.annualLeave}
                      disableRipple
                      onChange={(e) => {
                        setFieldValue('annualLeave', e.target.checked);
                      }}
                      label={
                        <Typography className="sub-title-text">
                          Annual leave
                        </Typography>
                      }
                    />
                  </Box>
                )}
            </Box>
            <Box className="form-actions-btn">
              <CustomButton
                title={`${loader ? 'Saving...' : 'Save'}`}
                disabled={!(dirty && isValid) || loader}
                fullWidth={false}
                type="submit"
              />
            </Box>
          </Form>
        )}
      </Formik>
    </>
  );
};

export default AddEditPolicyType;
