.export-popover {
  .MuiPaper-root {
    width: 120px !important;
    margin-top: 8px;
    padding: 10px 20px;
  }

  .export-option {
    p {
      text-align: center;
    }
  }
}
.All-filter-list {
  .Filter-list {
    cursor: pointer;
    background: var(--color-white);
    padding: 5px 10px;
    margin-bottom: 10px;
    border: var(--normal-sec-border);
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
    svg {
      width: 21px;
      height: 21px;
      fill: var(--color-black);
    }
    .filtername {
      width: calc(100% - 21px - 2px);
    }
  }
  .selected-list {
    background-color: var(--color-primary);
    color: var(--text-color-white);
    p {
      color: var(--text-color-white);
    }
    svg {
      fill: var(--color-white);
    }
  }
}
