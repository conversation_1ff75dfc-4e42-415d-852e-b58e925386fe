.Add-model-section {
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: calc(100% - 8px);

  .model-list {
    margin: 2px 0;
    cursor: pointer;
    .check-icon-box {
      width: 21px;
      height: 21px;
      margin-right: 8px;
    }
    .check-icon {
      fill: var(--color-primary);
    }
    .uncheck-icon {
      fill: var(--color-dark);
    }
  }
  .Inactive-axis {
    margin-left: 6px;
    padding: 0.5px 4px !important;
    color: var(--color-danger) !important;
    font-weight: 500 !important;
  }
  .axis-selection {
    height: calc(
      100vh - 202px - 67px - 40px - 50px - 100px - var(--banner-height)
    );
    overflow: auto;
  }
  .xaxis-selection {
    height: calc(100vh - 202px - 67px - 40px - 100px - var(--banner-height));
    overflow: auto;
  }
  .naxis-selection {
    height: calc(100vh - 202px - 67px - var(--banner-height));
    overflow: auto;
  }
}
