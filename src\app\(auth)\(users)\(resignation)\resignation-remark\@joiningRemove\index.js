'use client';

import React, { useEffect, useState } from 'react';
import { Box } from '@mui/material';
import CustomButton from '@/components/UI/CustomButton';
import CustomCheckbox from '@/components/UI/CustomCheckbox';
import { setApiMessage, getFields } from '@/helper/common/commonFunctions';
import PreLoader from '@/components/UI/Loader';
import NoDataView from '@/components/UI/NoDataView';
import {
  getLeavingChecklist,
  verifyLeavingChecklist,
} from '@/services/resignationService';

export default function JoiningRemovingChecklist({
  resignId,
  Userdetails,
  getResignationList,
  currentPage,
  searchValue,
  filterData,
  setJoiningChecklist,
}) {
  const [loader, setLoader] = useState(false);
  const [JoiningRemovingList, setJRList] = useState();
  const [checkId, setCheckId] = useState([]);
  const [isLoader, setIsLoader] = useState(false);

  const handleCheck = (value) => {
    const currentIndex = checkId.indexOf(value);
    const newChecked = [...checkId];
    if (currentIndex === -1) {
      newChecked.push(value);
    } else {
      newChecked.splice(currentIndex, 1);
    }
    setCheckId(newChecked);
  };

  const getJoiningRemovingChecklist = async (id) => {
    setLoader(true);
    try {
      const response = await getLeavingChecklist(id);
      if (response.success) {
        setJRList(response.data);
        const filterdata = response.data.filter(
          (f) => f?.leaving_checklist_status === 1
        );
        const selectedID =
          filterdata && filterdata.length > 0 && getFields(filterdata, 'id');
        selectedID && setCheckId(selectedID);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    } finally {
      setLoader(false);
    }
  };

  const checklistverification = async (id) => {
    setIsLoader(true);
    try {
      const response = await verifyLeavingChecklist(id, {
        verified_checklist: checkId,
      });
      if (response.success) {
        setJoiningChecklist(false);
        getResignationList(currentPage, searchValue, filterData);
      } else {
        setApiMessage('error', response.message);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    } finally {
      setLoader(false);
      setIsLoader(false);
    }
  };

  useEffect(() => {
    if (resignId !== undefined) {
      getJoiningRemovingChecklist(resignId);
    }
  }, [resignId]);

  return (
    <Box>
      {loader ? (
        <PreLoader />
      ) : JoiningRemovingList && JoiningRemovingList.length > 0 ? (
        <>
          {JoiningRemovingList?.map((item) => (
            <Box key={item.id} className="checklist-item">
              <CustomCheckbox
                checked={checkId.indexOf(item.id) !== -1}
                onChange={() => handleCheck(item.id)}
                label={item?.checkList_name}
                disabled={
                  Userdetails?.resignation_status === 'accepted' &&
                  Userdetails?.leaving_checklist_status === 'completed'
                }
              />
            </Box>
          ))}
        </>
      ) : (
        <NoDataView
          title="No checklist found"
          description="There is no checklist available at the moment."
        />
      )}
      {Userdetails?.resignation_status === 'accepted' &&
      Userdetails?.leaving_checklist_status === 'completed' ? (
        <></>
      ) : (
        <Box className="form-actions-btn">
          <CustomButton
            type="submit"
            variant="contained"
            title="Cancel"
            onClick={() => setJoiningChecklist(false)}
          />
          <CustomButton
            type="submit"
            variant="outlined"
            disabled={
              (Userdetails?.resignation_status === 'accepted' &&
                Userdetails?.leaving_checklist_status === 'completed') ||
              checkId?.length === 0 ||
              isLoader
            }
            title={`${isLoader ? 'Saving...' : 'Save'}`}
            onClick={() => checklistverification(resignId)}
          />
        </Box>
      )}
    </Box>
  );
}
