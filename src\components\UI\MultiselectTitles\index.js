import React from 'react';
import Select, { components } from 'react-select';
import { Box, FormLabel } from '@mui/material';
import '../CustomSelect/customSelect.scss';

const MultiselectTitles = ({
  label,
  options = [],
  value,
  onChange,
  className = '',
  menuPoisition,
  placeholder = 'Select...',
  disabled = false,
  isHeight = '230px',
  isOptionWithColor = false,
  isAreaManager = false,
}) => {
  const dot = (color = 'transparent') => ({
    alignItems: 'center',
    display: 'flex',
    ':before': {
      backgroundColor: color,
      borderRadius: 10,
      content: '" "',
      display: 'block',
      marginRight: 8,
      height: 10,
      width: 10,
    },
  });

  const defaultTag = () => ({
    alignItems: 'center',
    display: 'flex',
    ':after': {
      content: '" default"',
      display: 'block',
      marginLeft: 8,
      backgroundColor: 'var(--text-muted-mustard)',
      color: '#fff',
      border: '1px solid var(--text-muted-mustard)',
      padding: '0px 5px',
      borderRadius: '4px',
      fontSize: '12px',
    },
  });

  const customStylesWithColor = {
    menu: (provided) => ({
      ...provided,
      maxHeight: '230px',
      boxShadow: '4px 4px 6px rgba(0, 0, 0, 0.1)',
      border: '1px solid #a6a8b1',
    }),
    menuList: (provided) => ({
      ...provided,
      maxHeight: isHeight,
    }),
    menuPortal: (base) => ({
      ...base,
      zIndex: 99999,
    }),
    multiValue: (provided, { data }) => ({
      ...provided,
      ...defaultTag(data.color),
    }),
    option: (provided, { data }) => ({
      ...provided,
      ...dot(data.color || 'var(--color-primary)'),
    }),
  };

  const customStyles = {
    menu: (provided) => ({
      ...provided,
      maxHeight: '230px',
      boxShadow: '4px 4px 6px rgba(0, 0, 0, 0.1)',
      border: '1px solid #a6a8b1',
    }),
    menuList: (provided) => ({
      ...provided,
      maxHeight: isHeight,
    }),
    menuPortal: (base) => ({
      ...base,
      zIndex: 99999,
    }),
    option: (provided, { data }) => {
      return data.has_leave_policy_default
        ? {
            ...provided,
            ...defaultTag(),
          }
        : {
            ...provided,
          };
    },
  };

  const MultiValueRemove = (props) => {
    const { data, selectProps } = props;
    const firstOptionValue = selectProps.value?.[0]?.value;

    if (data.value === firstOptionValue) {
      return null;
    }

    return <components.MultiValueRemove {...props} />;
  };

  return (
    <Box className={`custom-select-wrapper ${className}`}>
      {label && (
        <FormLabel htmlFor={label} className="field-label">
          {label}
        </FormLabel>
      )}

      <Select
        isMulti
        isDisabled={disabled}
        options={options}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        className="select-container"
        classNamePrefix="select"
        menuPortalTarget={document.body}
        menuPlacement={menuPoisition ? 'top' : 'auto'}
        menuPosition="fixed"
        styles={isOptionWithColor ? customStylesWithColor : customStyles}
        components={isAreaManager ? { MultiValueRemove } : undefined}
      />
    </Box>
  );
};

export default MultiselectTitles;
