'use client';

import React, { useContext } from 'react';
import {
  Box,
  TableHead,
  TableCell,
  TableRow,
  TableBody,
  Table,
  Typography,
  TableContainer,
  InputAdornment,
  Tooltip,
} from '@mui/material';
import AuthContext from '@/helper/authcontext';
import { CustomTextField } from '@/components/UI/CommonField/index';
import '../BudgetTable/budgetTable.scss';

export default function BudgetTableBM({
  BudgetData,
  setBudgetData,
  BudgetDataReal,
  parentIndex,
  setRandom,
  isFromHistory,
}) {
  const { authState } = useContext(AuthContext);

  const cellWidth = 100;
  const PerCellWidth = BudgetData?.columns_group?.length > 2 ? 120 : 100;
  const FixedcellWidth = 100;
  const isChildren = BudgetData?.columns_group?.find(
    (f) => f?.children && f?.children?.length > 0
  );
  const isOneCell =
    BudgetData?.column && Object.keys(BudgetData?.column).length === 2;
  const currency =
    authState?.currency_details && authState?.currency_details?.symbol
      ? authState?.currency_details?.symbol
      : '£';
  // const mainheadwidth = (group) => {
  //   return group?.key === 'col1'
  //     ? `${FixedcellWidth}px`
  //     : group?.children?.length > 0
  //       ? `${group?.children?.map((r) => !r?.is_percentage)?.length
  //         ? group?.children?.map((r) => !r?.is_percentage)?.length
  //         : 0 * cellWidth +
  //           group?.children?.map((r) => r?.is_percentage)?.length
  //           ? group?.children?.map((r) => r?.is_percentage)?.length
  //           : 0 * PerCellWidth
  //       }px`
  //       : `${cellWidth}px`;
  // };
  const mainheadwidth = (group) => {
    return group?.key === 'col1'
      ? `${FixedcellWidth}px`
      : group?.children?.length > 0
        ? `${
            group?.children?.map((r) => !r?.is_percentage)?.length
              ? group?.children?.map((r) => !r?.is_percentage)?.length *
                cellWidth
              : 0 * cellWidth +
                  group?.children?.map((r) => r?.is_percentage)?.length
                ? group?.children?.map((r) => r?.is_percentage)?.length *
                  PerCellWidth
                : 0 * PerCellWidth
          }px`
        : `${cellWidth}px`;
  };
  // const headwidth = (group) => {
  //   return group?.children?.length > 0
  //     ? `${group?.children?.map((r) => !r?.is_percentage)?.length
  //       ? group?.children?.map((r) => !r?.is_percentage)?.length
  //       : 0 * cellWidth +
  //         group?.children?.map((r) => r?.is_percentage)?.length
  //         ? group?.children?.map((r) => r?.is_percentage)?.length
  //         : 0 * PerCellWidth
  //     }px`
  //     : `${cellWidth}px`;
  // };
  const headwidth = (group) => {
    return group?.children?.length > 0
      ? `${
          group?.children?.map((r) => !r?.is_percentage)?.length
            ? group?.children?.map((r) => !r?.is_percentage)?.length * cellWidth
            : 0 * cellWidth +
                group?.children?.map((r) => r?.is_percentage)?.length
              ? group?.children?.map((r) => r?.is_percentage)?.length *
                PerCellWidth
              : 0 * PerCellWidth
        }px`
      : `${cellWidth}px`;
  };

  const TotalValue = (key, totalkey) => {
    const total = BudgetData?.data?.reduce((sum, item) => {
      const value =
        totalkey !== item?.forecast_category_type
          ? 0
          : parseFloat(item[key]) || 0;
      return sum + value;
    }, 0);
    // Return total rounded to 2 decimal places
    return total.toFixed(2);
  };
  const getTotals = (data, index) => {
    const firstChildKeys = BudgetData?.columns_group
      .map((col) => col?.group_type !== 'Total' && col.children?.[index]?.key)
      .filter(Boolean);
    const total = firstChildKeys.reduce(
      (sum, key) => sum + (parseFloat(data[key]) || 0),
      0
    );
    return total.toFixed(2);
  };

  const TotalValueDiff = (totalkey, actual, budget) => {
    // Return total rounded to 2 decimal places
    const diffValue = /^0+(\.0+)?$/.test(
      parseFloat(TotalValue(budget, totalkey))
    )
      ? 0
      : ((parseFloat(TotalValue(budget, totalkey)) -
          parseFloat(TotalValue(actual, totalkey))) /
          parseFloat(TotalValue(budget, totalkey))) *
        100;
    // Return total rounded to 2 decimal places
    return diffValue ? diffValue?.toFixed(2) : 0;
  };
  const TotalValueGroup = (gindex, totalkey) => {
    const total = BudgetData?.data?.reduce((sum, item) => {
      const value =
        totalkey !== item?.forecast_category_type
          ? 0
          : parseFloat(getTotals(item, gindex) ? getTotals(item, gindex) : 0); // getTotals(dataRow, gindex)
      return sum + value;
    }, 0);
    // Return total rounded to 2 decimal places
    return total ? total?.toFixed(2) : 0;
  };
  const TotalValueGroupDiff = (gindex, totalkey) => {
    const diffValue = /^0+(\.0+)?$/.test(
      parseFloat(TotalValueGroup(1, totalkey))
    )
      ? 0
      : ((parseFloat(TotalValueGroup(1, totalkey)) -
          parseFloat(TotalValueGroup(0, totalkey))) /
          parseFloat(TotalValueGroup(1, totalkey))) *
        100;
    // Return total rounded to 2 decimal places
    return diffValue ? diffValue?.toFixed(2) : 0;
  };
  const ShowValue = (
    dataRow,
    group,
    item,
    IsDifference,
    gindex,
    tooltip,
    isHeader
  ) => {
    const totalkey =
      dataRow?.col1 === 'Total income'
        ? 'income'
        : dataRow?.col1 === 'Total other'
          ? 'other'
          : dataRow?.col1 === 'Total expense'
            ? 'expense'
            : 'other';
    if (
      item?.category_name === 'Total' &&
      dataRow?.type === 'mainTotal' &&
      group?.group_type === 'Total' &&
      item?.key !== 'col1' &&
      !IsDifference
    ) {
      const totalValueGroup = TotalValueGroup(gindex, totalkey);
      return (
        <span className={`sub-title-text text-ellipsis-line`}>
          {totalValueGroup
            ? totalValueGroup
            : dataRow[item?.key] || dataRow[item?.key] === 0
              ? dataRow[item?.key]
              : '-'}
        </span>
      );
    } else if (
      item?.category_name === 'Total' &&
      dataRow?.type === 'mainTotal' &&
      group?.group_type === 'Total' &&
      item?.key !== 'col1' &&
      IsDifference
    ) {
      const diffValueGroupDiff = TotalValueGroupDiff(gindex, totalkey);

      return (
        <span
          className={`sub-title-text text-ellipsis-line d-flex align-center difference-cell ${
            !tooltip && diffValueGroupDiff > 0
              ? 'color-green' //color-green
              : !tooltip && 'color-red' //color-red
          } `}
        >
          <span
            className={`sub-title-text fw500 text-ellipsis-line ${
              !tooltip && diffValueGroupDiff > 0
                ? 'color-green' //color-green
                : !tooltip && 'color-red' //color-red
            }`}
          >
            {diffValueGroupDiff
              ? Math.abs(diffValueGroupDiff) + '%'
              : dataRow[item?.key] || dataRow[item?.key] === 0
                ? dataRow[item?.key] + '%'
                : '-'}
          </span>
        </span>
      );
    } else if (
      dataRow?.type === 'mainTotal' &&
      item?.key !== 'col1' &&
      !IsDifference
    ) {
      const totalValue = TotalValue(item?.key, totalkey);
      return (
        <span className={`sub-title-text text-ellipsis-line`}>
          {totalValue || totalValue === 0
            ? totalValue
            : dataRow[item?.key] || dataRow[item?.key] === 0
              ? dataRow[item?.key]
              : '-'}
        </span>
      );
    } else if (
      dataRow?.type === 'mainTotal' &&
      item?.key !== 'col1' &&
      IsDifference
    ) {
      const totalkey =
        dataRow?.col1 === 'Total income'
          ? 'income'
          : dataRow?.col1 === 'Total other'
            ? 'other'
            : dataRow?.col1 === 'Total expense'
              ? 'expense'
              : 'other';

      const actualKey = `col${parseFloat(item?.key?.replace('col', '')) - 2}`;
      const budgetKey = `col${parseFloat(item?.key?.replace('col', '')) - 1}`;
      const totalValueDiff = TotalValueDiff(totalkey, actualKey, budgetKey);
      return (
        <span
          className={`sub-title-text text-ellipsis-line d-flex align-center difference-cell ${
            !tooltip && totalValueDiff > 0
              ? 'color-green' //color-green
              : !tooltip && 'color-red' //color-red
          }`}
        >
          <span
            className={`sub-title-text fw500 text-ellipsis-line ${
              !tooltip && totalValueDiff > 0
                ? 'color-green' //color-green
                : !tooltip && 'color-red' //color-red
            }`}
          >
            {totalValueDiff || totalValueDiff === 0
              ? Math.abs(totalValueDiff) + '%'
              : dataRow[item?.key] || dataRow[item?.key] === 0
                ? dataRow[item?.key]
                : '-'}
          </span>
        </span>
      );
    }
    if (
      (dataRow['payment_type_id'] || dataRow['type'] === 'mainHeader') &&
      item?.key === 'col1'
    ) {
      const isMainCurrency =
        item?.key === 'col1' &&
        dataRow?.has_field_currency &&
        dataRow?.type &&
        dataRow?.type === 'subHeader';
      return (
        <span
          className={`sub-title-text d-flex ${isHeader ? 'text-capital' : ''} ${isMainCurrency ? 'justify-space-between' : 'justify-center'}`}
        >
          <span className="sub-title-text pr4 text-ellipsis-line">
            {' '}
            {dataRow[item?.key] || dataRow[item?.key] === 0
              ? dataRow[item?.key]
              : ''}
          </span>
          {isMainCurrency ? (
            <span className="sub-title-text currency-symbol text-ellipsis-line">
              {'(' + currency + ')'}
            </span>
          ) : (
            ''
          )}
        </span>
      );
    }
    if (
      (dataRow?.type &&
        dataRow?.type === 'subHeader' &&
        dataRow?.forecast_category_status !== 'combined') ||
      (dataRow['type'] && dataRow['type'] === 'mainHeader')
    ) {
      return (
        <span
          className={`sub-title-text text-ellipsis-line main-category`}
        ></span>
      );
    }

    if (IsDifference) {
      // const diffValue = (
      //   parseFloat(getTotals(dataRow, 1)) - parseFloat(getTotals(dataRow, 0))
      // ).toFixed(2); // Difference
      // const prevKey = `col${parseFloat(item?.key?.replace('col', '')) - 2}`;
      // const budgetKey = `col${parseFloat(item?.key?.replace('col', '')) - 1}`;

      const budgetValue = getTotals(dataRow, 1);
      const prevValue = getTotals(dataRow, 0);
      const diffPerTotal =
        parseFloat(budgetValue) === 0
          ? 0
          : ((parseFloat(budgetValue) - parseFloat(prevValue)) /
              parseFloat(budgetValue)) *
            100;
      return (
        <span
          className={`sub-title-text difference-cell w100 `} //text-ellipsis-line d-flex align-center
        >
          {group?.group_type === 'Total' ? (
            <>
              {dataRow[item?.key]?.type !== 'total' && (
                <span
                  className={`sub-title-text fw500 w text-ellipsis-line table-per-diff ${
                    !tooltip && diffPerTotal > 0
                      ? 'color-green' //color-green
                      : !tooltip && 'color-red' //color-red
                  } `}
                >
                  {' '}
                  {(diffPerTotal !== 0
                    ? Math.abs(diffPerTotal.toFixed(2))
                    : 0) + '%'}{' '}
                </span>
              )}
            </>
          ) : (
            <>
              {dataRow[item?.key]?.type !== 'total' && (
                <span
                  className={`sub-title-text fw500 w text-ellipsis-line table-per-diff ${
                    item?.key &&
                    dataRow[item?.key] &&
                    !tooltip &&
                    parseFloat(dataRow[item?.key]) > 0
                      ? 'color-green' //color-green
                      : !tooltip && 'color-red' //color-red
                  } `}
                >
                  {dataRow[item?.key] || dataRow[item?.key] === 0
                    ? Math.abs(dataRow[item?.key]) + '%'
                    : '-'}
                </span>
              )}
            </>
          )}
        </span>
      );
    }
    return (
      <span className={`sub-title-text text-ellipsis-line`}>
        {group?.group_type === 'Total' &&
        (getTotals(dataRow, gindex) || getTotals(dataRow, gindex) === 0)
          ? getTotals(dataRow, gindex)
          : dataRow[item?.key] || dataRow[item?.key] === 0
            ? dataRow[item?.key]
            : '-'}
      </span>
    );
  };
  return (
    <Box className="Budget-table">
      <TableContainer className="Budget-table-container">
        <Table className="dsr-table">
          <TableHead>
            {BudgetData?.columns_group &&
              BudgetData?.columns_group?.length > 0 &&
              BudgetData?.columns_group?.map((group, groupIndex) => (
                <TableCell
                  colSpan={
                    group?.children?.length ? group?.children?.length : null
                  }
                  align="center"
                  className={`table-head ${
                    group?.key === 'col1' && isChildren
                      ? 'fixed-header-cell fixed-header-cell-children text-align'
                      : group?.key === 'col1'
                        ? 'table-head fixed-header-cell text-align'
                        : isOneCell
                          ? 'w100'
                          : ''
                  } ${
                    BudgetData?.columns_group?.length - 1 === groupIndex
                      ? 'table-head fixed-header-cell-last text-align'
                      : ''
                  } ${
                    BudgetData?.columns_group?.length - 2 === groupIndex
                      ? 'table-head fixed-header-cell-last-secound text-align'
                      : ''
                  }  ${
                    BudgetData?.columns_group?.length - 3 === groupIndex
                      ? 'border-right-0'
                      : ''
                  }`}
                  style={{
                    minWidth:
                      // group?.key === 'col1'
                      //   ? `${cellWidth - 1}px`
                      //   :
                      mainheadwidth(group),
                    maxWidth: mainheadwidth(group),
                  }}
                >
                  <Box className="d-flex justify-center">
                    <Typography className="sub-title-text fw500 w100">
                      <Tooltip
                        title={<Typography>{group?.content}</Typography>}
                        arrow
                        classes={{
                          tooltip: 'info-tooltip-container ',
                        }}
                      >
                        {' '}
                        <span className="sub-title-text fw500 w text-ellipsis-line">
                          {group?.content}
                        </span>
                      </Tooltip>
                    </Typography>
                  </Box>
                  {group?.children && group?.children?.length > 0 && (
                    <TableRow
                      className="group-table-row"
                      style={{
                        flex: 1,
                        minWidth: headwidth(group),
                        // maxWidth: headwidth(group)
                      }}
                    >
                      {group?.children &&
                        group?.children?.length > 0 &&
                        group?.children?.map((item) => (
                          <TableCell
                            className="table-cell heading-cell text-align"
                            style={{
                              flex: 1,
                              minWidth: item?.is_percentage
                                ? `${PerCellWidth}px`
                                : `${cellWidth}px`,
                              maxWidth:
                                BudgetData?.columns_group?.length > 2 &&
                                item?.is_percentage &&
                                item?.is_editable
                                  ? `${PerCellWidth + 8}px`
                                  : 'auto',
                            }}
                          >
                            <Tooltip
                              title={<Typography>{item?.content}</Typography>}
                              arrow
                              classes={{
                                tooltip: 'info-tooltip-container ',
                              }}
                            >
                              <span className="sub-title-text fw500 w text-ellipsis-line">
                                {' '}
                                {item?.content}
                              </span>
                            </Tooltip>
                          </TableCell>
                        ))}
                    </TableRow>
                  )}
                </TableCell>
              ))}
          </TableHead>

          <TableBody>
            {BudgetData?.data?.map((dataRow, rowIndex) => (
              <TableRow
                key={rowIndex}
                className={
                  dataRow?.type === 'mainTotal'
                    ? 'total-row'
                    : dataRow?.type && dataRow?.type === 'mainHeader'
                      ? 'main-header-row'
                      : dataRow?.type &&
                          dataRow?.type === 'subHeader' &&
                          dataRow?.forecast_category_status !== 'combined'
                        ? 'sub-header-row'
                        : dataRow?.type && dataRow?.type === 'subHeader'
                          ? 'sub-header-row-combined'
                          : ''
                }
              >
                {BudgetData?.columns_group &&
                  BudgetData?.columns_group?.length > 0 && (
                    <>
                      {(dataRow?.type && dataRow?.type === 'mainHeader') ||
                      (dataRow?.type &&
                        dataRow?.type === 'subHeader' &&
                        dataRow?.forecast_category_status !== 'combined') ? (
                        <>
                          {BudgetData?.columns_group?.map((group, index) => (
                            <>
                              {group?.children &&
                              group?.children?.length > 0 ? (
                                <>
                                  {group?.children?.map((item, findex) => (
                                    <TableCell
                                      className={`table-cell table-value table-child  ${
                                        BudgetData?.columns_group?.length -
                                          1 ===
                                          index && 'fixed-value-last'
                                      }  ${
                                        BudgetData?.columns_group?.length -
                                          1 ===
                                        index
                                          ? 'fixed-cell-last'
                                          : ''
                                      } ${
                                        BudgetData?.columns_group?.length -
                                          2 ===
                                        index
                                          ? `fixed-cell-last-secound-${findex}`
                                          : ''
                                      }  ${
                                        dataRow?.type &&
                                        dataRow?.type === 'mainHeader' &&
                                        rowIndex === 0
                                          ? `group-border-top-0`
                                          : ''
                                      }`}
                                    ></TableCell>
                                  ))}
                                </>
                              ) : (
                                <>
                                  <TableCell
                                    style={{
                                      minWidth:
                                        // group?.key === 'col1'
                                        //   ? `${cellWidth - 1}px`
                                        //   :
                                        index === 0
                                          ? `${FixedcellWidth}px`
                                          : `${cellWidth}px`,
                                      maxWidth:
                                        index === 0
                                          ? `${FixedcellWidth}px`
                                          : `${cellWidth}px`,
                                    }}
                                    className={`table-cell table-value  ${
                                      isOneCell && index !== 0
                                        ? 'text-align firstValue-cell one-cell'
                                        : index === 1
                                          ? ' firstValue-cell' //text-align-end
                                          : group?.key === 'col1'
                                            ? 'text-align fixed-value'
                                            : '' //text-align-end
                                    } ${
                                      !isFromHistory &&
                                      !dataRow[group?.key]?.isPast &&
                                      group?.is_editable && // dataRow[group?.key]?.isEditable &&
                                      dataRow[group?.key]?.type !== 'total' &&
                                      'text-field-cell'
                                    } ${dataRow?.type && dataRow?.type === 'mainHeader' && 'text-capital'} 
                                      ${
                                        BudgetData?.columns_group?.length -
                                          1 ===
                                        index
                                          ? 'fixed-cell-last'
                                          : ''
                                      } ${
                                        BudgetData?.columns_group?.length -
                                          2 ===
                                        index
                                          ? `fixed-cell-last-secound-${findex}`
                                          : ''
                                      } ${
                                        BudgetData?.columns_group?.length -
                                          3 ===
                                        index
                                          ? 'border-right-0'
                                          : ''
                                      }
                                      `}
                                  >
                                    {' '}
                                    {BudgetData?.columns_group?.length - 1 ===
                                    index ? (
                                      <></>
                                    ) : (
                                      <>
                                        <Tooltip
                                          title={ShowValue(
                                            dataRow,
                                            '',
                                            group,
                                            '',
                                            '',
                                            true,
                                            dataRow?.type === 'mainHeader'
                                          )}
                                          arrow
                                          classes={{
                                            tooltip: 'info-tooltip-container ',
                                          }}
                                        >
                                          {ShowValue(dataRow, '', group)}
                                        </Tooltip>
                                      </>
                                    )}
                                  </TableCell>
                                </>
                              )}
                            </>
                          ))}
                        </>
                      ) : (
                        <>
                          {' '}
                          {BudgetData?.columns_group?.map((group, index) => (
                            <>
                              {group?.children &&
                              group?.children?.length > 0 ? (
                                <>
                                  {group?.children?.map((item, findex) => (
                                    <TableCell
                                      key={item?.key}
                                      style={{
                                        flex: 1,
                                        minWidth:
                                          group?.children?.length - 1 === findex
                                            ? `${cellWidth}px`
                                            : `${cellWidth}px`,
                                        maxWidth:
                                          group?.children?.length - 1 === findex
                                            ? `${cellWidth}px`
                                            : `${cellWidth}px`,
                                      }}
                                      className={`table-cell table-value  ${
                                        //text-align-end
                                        index === 1 &&
                                        findex === 0 &&
                                        'firstValue-cell'
                                      }
                                         ${
                                           !group?.isPast &&
                                           (findex === 1 || findex === 2) &&
                                           BudgetData?.columns_group?.length -
                                             2 !==
                                             index &&
                                           dataRow?.type !== 'mainTotal' &&
                                           'text-field-cell'
                                         } ${
                                           group?.children?.length - 1 ===
                                             findex && 'difference-cells'
                                         }  ${
                                           BudgetData?.columns_group?.length -
                                             1 ===
                                           index
                                             ? 'fixed-cell-last'
                                             : ''
                                         } ${
                                           BudgetData?.columns_group?.length -
                                             2 ===
                                           index
                                             ? `fixed-cell-last-secound-${findex}`
                                             : ''
                                         } ${
                                           BudgetData?.columns_group?.length -
                                             3 ===
                                           index
                                             ? 'border-right-0'
                                             : ''
                                         }`}
                                    >
                                      {(findex === 1 || findex === 2) &&
                                      !group?.isPast &&
                                      BudgetData?.columns_group?.length - 2 !==
                                        index &&
                                      !isFromHistory &&
                                      dataRow?.type !== 'mainTotal' ? ( //dataRow[item?.key]?.isEditable &&    dataRow[item?.key]?.type !== 'total' //!isFromHistory &&
                                        <Box className="budget-input-value text-align-end">
                                          <CustomTextField
                                            InputLabelProps={{
                                              shrink: true,
                                            }}
                                            fullWidth
                                            id="ename"
                                            name="ename"
                                            value={dataRow[item?.key]}
                                            label={
                                              item?.is_percentage
                                                ? 'Percentage *'
                                                : 'Amount *'
                                            }
                                            variant="filled"
                                            placeholder={
                                              item?.is_percentage
                                                ? 'Percentage'
                                                : 'Amount'
                                            }
                                            onInput={(e) => {
                                              // Remove non-numeric characters
                                              let value = e.target.value;
                                              if (
                                                value === '' ||
                                                /^-?\d*\.?\d{0,2}$/.test(value)
                                              ) {
                                                e.target.value = value;
                                              } else {
                                                e.target.value = value.slice(
                                                  0,
                                                  -1
                                                );
                                              }
                                            }}
                                            onPaste={(e) => {
                                              if (
                                                /^-?[0-9]*\.?[0-9]{0,2}$/.test(
                                                  e.clipboardData.getData(
                                                    'text'
                                                  )
                                                )
                                              ) {
                                                // setValue(pasteData);
                                              } else {
                                                e.preventDefault();
                                              }
                                            }}
                                            InputProps={{
                                              // ...(!item?.is_percentage &&
                                              //   item?.has_field_currency && {
                                              //   startAdornment: (
                                              //     <InputAdornment position="start">
                                              //       <Typography className="sub-title-text currency">
                                              //         {currency}
                                              //       </Typography>{' '}
                                              //     </InputAdornment>
                                              //   ),
                                              // }),
                                              ...(findex === 2 && {
                                                endAdornment: (
                                                  <InputAdornment position="start">
                                                    <Typography className="sub-title-text currency">
                                                      %
                                                    </Typography>{' '}
                                                  </InputAdornment>
                                                ),
                                              }),
                                            }}
                                            onChange={(e) => {
                                              if (findex === 1) {
                                                let budget = BudgetDataReal;
                                                budget[
                                                  parentIndex
                                                ].forecast_bugdet_data.data[
                                                  rowIndex
                                                ][item?.key] = e.target.value;
                                                const actualKey = `col${
                                                  parseFloat(
                                                    item?.key?.replace(
                                                      'col',
                                                      ''
                                                    )
                                                  ) - 1
                                                }`;
                                                const diffKey = `col${
                                                  parseFloat(
                                                    item?.key?.replace(
                                                      'col',
                                                      ''
                                                    )
                                                  ) + 1
                                                }`;
                                                if (e.target.value !== '') {
                                                  let actual =
                                                    BudgetDataReal[parentIndex]
                                                      .forecast_bugdet_data
                                                      .data[rowIndex][
                                                      actualKey
                                                    ];
                                                  let budgetValue =
                                                    e.target.value;
                                                  let diff =
                                                    /^0+(\.0+)?$/.test(
                                                      budgetValue
                                                    ) ||
                                                    isNaN(
                                                      parseFloat(budgetValue)
                                                    ) ||
                                                    budgetValue === 0 ||
                                                    parseFloat(budgetValue) ===
                                                      0
                                                      ? 0
                                                      : isNaN(
                                                            ((parseFloat(
                                                              budgetValue
                                                            ) -
                                                              parseFloat(
                                                                actual
                                                              )) /
                                                              parseFloat(
                                                                budgetValue
                                                              )) *
                                                              100
                                                          )
                                                        ? 0
                                                        : ((parseFloat(
                                                            budgetValue
                                                          ) -
                                                            parseFloat(
                                                              actual
                                                            )) /
                                                            parseFloat(
                                                              budgetValue
                                                            )) *
                                                          100;
                                                  budget[
                                                    parentIndex
                                                  ].forecast_bugdet_data.data[
                                                    rowIndex
                                                  ][diffKey] = isNaN(
                                                    diff.toFixed(2)
                                                  )
                                                    ? 0
                                                    : diff.toFixed(2);
                                                } else {
                                                  budget[
                                                    parentIndex
                                                  ].forecast_bugdet_data.data[
                                                    rowIndex
                                                  ][diffKey] = 0;
                                                }
                                                setBudgetData(budget);
                                                setRandom(Math.random());
                                              } else if (findex === 2) {
                                                let budget = BudgetDataReal;
                                                budget[
                                                  parentIndex
                                                ].forecast_bugdet_data.data[
                                                  rowIndex
                                                ][item?.key] = e.target.value;
                                                const actualKey = `col${
                                                  parseFloat(
                                                    item?.key?.replace(
                                                      'col',
                                                      ''
                                                    )
                                                  ) - 2
                                                }`;
                                                const budgetKey = `col${
                                                  parseFloat(
                                                    item?.key?.replace(
                                                      'col',
                                                      ''
                                                    )
                                                  ) - 1
                                                }`;
                                                if (e.target.value !== '') {
                                                  let actual =
                                                    budget[parentIndex]
                                                      .forecast_bugdet_data
                                                      .data[rowIndex][
                                                      actualKey
                                                    ];
                                                  let percentage =
                                                    e.target.value;

                                                  budget[
                                                    parentIndex
                                                  ].forecast_bugdet_data.data[
                                                    rowIndex
                                                  ][budgetKey] =
                                                    /^0+(\.0+)?$/.test(
                                                      actual
                                                    ) ||
                                                    isNaN(
                                                      parseFloat(percentage)
                                                    ) ||
                                                    parseFloat(percentage) ===
                                                      0 ||
                                                    percentage === 0 ||
                                                    parseFloat(percentage) ===
                                                      100
                                                      ? 0
                                                      : isNaN(
                                                            parseFloat(actual) /
                                                              (1 -
                                                                parseFloat(
                                                                  percentage
                                                                ) /
                                                                  100)
                                                          )
                                                        ? 0
                                                        : (
                                                            parseFloat(actual) /
                                                            (1 -
                                                              parseFloat(
                                                                percentage
                                                              ) /
                                                                100)
                                                          ).toFixed(2);
                                                } else {
                                                  let actual =
                                                    budget[parentIndex]
                                                      .forecast_bugdet_data
                                                      .data[rowIndex][
                                                      actualKey
                                                    ];
                                                  budget[
                                                    parentIndex
                                                  ].forecast_bugdet_data.data[
                                                    rowIndex
                                                  ][budgetKey] =
                                                    /^0+(\.0+)?$/.test(actual)
                                                      ? 0
                                                      : parseFloat(actual);
                                                }
                                                setBudgetData(budget);
                                                setRandom(Math.random());
                                              }
                                            }}
                                          />
                                        </Box>
                                      ) : (
                                        <Tooltip
                                          title={ShowValue(
                                            dataRow,
                                            group,
                                            item,
                                            group?.children?.length - 1 ===
                                              findex,
                                            findex,
                                            true
                                          )}
                                          arrow
                                          classes={{
                                            tooltip: 'info-tooltip-container ',
                                          }}
                                        >
                                          {ShowValue(
                                            dataRow,
                                            group,
                                            item,
                                            group?.children?.length - 1 ===
                                              findex,
                                            findex
                                          )}
                                        </Tooltip>
                                      )}
                                    </TableCell>
                                  ))}
                                </>
                              ) : (
                                <>
                                  <TableCell
                                    style={{
                                      minWidth:
                                        // group?.key === 'col1'
                                        //   ? `${cellWidth - 1}px`
                                        //   :
                                        index === 0
                                          ? `${FixedcellWidth}px`
                                          : `${cellWidth}px`,
                                      maxWidth:
                                        index === 0
                                          ? `${FixedcellWidth}px`
                                          : `${cellWidth}px`,
                                    }}
                                    className={`table-cell table-value  ${
                                      isOneCell && index !== 0
                                        ? 'text-align firstValue-cell one-cell'
                                        : index === 1
                                          ? 'firstValue-cell' //text-align-end
                                          : group?.key === 'col1'
                                            ? 'text-align fixed-value'
                                            : '' //text-align-end
                                    } ${
                                      group?.key !== 'col1' &&
                                      dataRow?.type !== 'mainTotal' && // dataRow[group?.key]?.isEditable &&!dataRow[group?.key]?.isPast && !isFromHistory &&dataRow[group?.key]?.type !== 'total'
                                      'text-field-cell'
                                    } ${
                                      BudgetData?.columns_group?.length - 1 ===
                                      index
                                        ? 'fixed-cell-last'
                                        : ''
                                    } ${
                                      BudgetData?.columns_group?.length - 2 ===
                                      index
                                        ? `fixed-cell-last-secound-${findex}`
                                        : ''
                                    } ${
                                      BudgetData?.columns_group?.length - 3 ===
                                      index
                                        ? 'border-right-0'
                                        : ''
                                    }`}
                                  >
                                    {' '}
                                    {group?.key !== 'col1' &&
                                    !group?.isPast &&
                                    dataRow?.type !== 'mainTotal' &&
                                    !isFromHistory ? ( // dataRow[group?.key]?.isEditable && !isFromHistory && dataRow[group?.key]?.type !== 'total'
                                      <Box className="budget-input-value text-align-end">
                                        <CustomTextField
                                          InputLabelProps={{
                                            shrink: true,
                                          }}
                                          fullWidth
                                          id="ename"
                                          name="ename"
                                          label={
                                            group?.is_percentage
                                              ? 'Percentage *'
                                              : 'Amount *'
                                          }
                                          variant="filled"
                                          placeholder={
                                            group?.is_percentage
                                              ? 'Percentage'
                                              : 'Amount'
                                          }
                                          value={dataRow[group?.key]}
                                          // InputProps={{
                                          //   ...(!group?.is_percentage &&
                                          //     group?.has_field_currency && {
                                          //       startAdornment: (
                                          //         <InputAdornment position="start">
                                          //           <Typography className="sub-title-text currency">
                                          //             {currency}
                                          //           </Typography>{' '}
                                          //         </InputAdornment>
                                          //       ),
                                          //     }),
                                          //   ...(group?.is_percentage && {
                                          //     endAdornment: (
                                          //       <InputAdornment position="start">
                                          //         <Typography className="sub-title-text currency">
                                          //           %
                                          //         </Typography>{' '}
                                          //       </InputAdornment>
                                          //     ),
                                          //   }),
                                          // }}
                                          onInput={(e) => {
                                            // Remove non-numeric characters
                                            let value = e.target.value;
                                            if (
                                              value === '' ||
                                              /^-?\d*\.?\d{0,2}$/.test(value)
                                            ) {
                                              e.target.value = value;
                                            } else {
                                              e.target.value = value.slice(
                                                0,
                                                -1
                                              );
                                            }
                                          }}
                                          onPaste={(e) => {
                                            if (
                                              /^-?[0-9]*\.?[0-9]{0,2}$/.test(
                                                e.clipboardData.getData('text')
                                              )
                                            ) {
                                              // setValue(pasteData);
                                            } else {
                                              e.preventDefault();
                                            }
                                          }}
                                          onChange={(e) => {
                                            let budget = BudgetDataReal;
                                            budget[
                                              parentIndex
                                            ].forecast_bugdet_data.data[
                                              rowIndex
                                            ][group?.key] = e.target.value;
                                            setBudgetData(budget);
                                            setRandom(Math.random());
                                          }}
                                        />
                                      </Box>
                                    ) : (
                                      <Tooltip
                                        title={ShowValue(
                                          dataRow,
                                          '',
                                          group,
                                          '',
                                          '',
                                          '',
                                          true
                                        )}
                                        arrow
                                        classes={{
                                          tooltip: 'info-tooltip-container ',
                                        }}
                                      >
                                        {ShowValue(dataRow, '', group)}
                                      </Tooltip>
                                    )}
                                  </TableCell>
                                </>
                              )}
                            </>
                          ))}
                        </>
                      )}
                    </>
                  )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
}
