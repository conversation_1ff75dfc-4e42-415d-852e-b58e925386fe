// search modal
// custom dropdown list
.header-dropdown-menu {
  .search-input-wrapper {
    display: flex;
    align-items: center;
    background: var(--color-white);
    padding-bottom: var(--spacing-sm);
    border-bottom: var(--field-border);
    gap: 5px;
    .search-icon {
      color: var(--icon-color-light-dark);
      height: var(--icon-size-sm);
      width: var(--icon-size-sm);
    }
    .search-input {
      width: 100%;
      border: none;
      background: none;
      outline: none;
      font-size: var(--font-size-sm);
      line-height: var(--line-height-sm);
      font-family: var(--font-family-primary);
    }
  }

  ul {
    list-style: none;
    padding: var(--spacing-xs);
    margin: var(--spacing-none);
    max-height: 200px;
    overflow-y: auto;
    font-family: var(--font-family-primary);
    li {
      padding: var(--spacing-sm);
      cursor: pointer;
      font-size: var(--font-size-sm);
      line-height: var(--line-height-sm);
      border-radius: var(--border-radius-xs);
      margin-bottom: var(--spacing-xs);
      &:hover,
      &.selected {
        background: var(--color-primary);
        color: var(--text-color-white);
      }
    }
  }
}

// copy custom range modal
.copy-range-modal {
  .description-section {
    margin-bottom: var(--spacing-xxl);
    .copy-range-modal-body-text {
      font-family: var(--font-family-primary);
      font-size: var(--font-size-sm);
      line-height: var(--line-height-sm);
      font-weight: var(--font-weight-medium);
      color: var(--text-color-black);
    }
    .copy-range-modal-body-sm {
      font-family: var(--font-family-primary);
      font-size: var(--font-size-xs);
      line-height: var(--line-height-xs);
      font-weight: var(--font-weight-semibold);
      color: var(--text-color-slate-gray);
      margin-top: var(--spacing-md);
    }
  }

  .section-title {
    display: flex;
    align-items: center;
    gap: 5px;
    font-family: var(--font-family-primary);
    font-size: var(--font-size-lg);
    line-height: var(--line-height-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color-black);
    margin-bottom: var(--spacing-md);
  }

  .help-icon {
    color: var(--icon-color-primary);
    height: var(--icon-size-sm);
    width: var(--icon-size-sm);
    cursor: pointer;
  }

  .copy-paste-section {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: var(--spacing-xxl);
    margin-bottom: var(--spacing-xxl);

    .arrow-section {
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--color-primary);
      margin-right: 50px;
      @media (max-width: 1300px) {
        margin-right: 0px;
      }
    }

    .copy-section,
    .paste-section {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-md);
    }
  }

  .employees-section {
    margin-bottom: var(--spacing-xxl);
    .search-box {
      margin-bottom: var(--spacing-md);
      .action-link {
        color: var(--color-primary);
        cursor: pointer;
        font-size: var(--font-size-sm);
        &:hover {
          text-decoration: underline;
        }
      }
    }
    .select-actions {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      margin-top: var(--spacing-xs);
    }
    .employees-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
      gap: var(--spacing-md);
      margin-top: var(--spacing-md);
      .employee-item {
        display: flex;
        align-items: center;
        .employee-details {
          flex: 1;
          display: flex;
          align-items: center;
          gap: 5px;
          .me-tag {
            color: var(--text-color-slate-gray);
            font-size: var(--font-size-xs);
          }
        }
        .MuiFormControlLabel-root {
          margin-left: 0;
          margin-right: 0;
          span {
            font-family: var(--font-family-primary);
            font-size: var(--font-size-sm);
            line-height: var(--line-height-sm);
          }
        }
        .MuiCheckbox-root {
          padding: var(--spacing-xs);
        }
      }
    }
  }

  .options-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xxl);
    .copy-options {
      .MuiCheckbox-root {
        padding: var(--spacing-xs);
      }
      .MuiFormControlLabel-root {
        margin-left: 0;
        margin-right: 0;
        span {
          font-family: var(--font-family-primary);
          font-size: var(--font-size-sm);
          line-height: var(--line-height-sm);
        }
      }
    }
  }
  .copy-action-section {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 5px;
    margin-top: var(--spacing-xxl);
    border-top: var(--field-border);
    padding-top: var(--spacing-xxl);
  }
}

// Tooltip styles
.calendar-event-list-tooltip-custom {
  .p12 {
    letter-spacing: 0px !important;
  }
}

// Keyboard shortcut
.keyboard-shortcuts-modal {
  padding: var(--spacing-xl);
  color: var(--text-color-white);
  background-color: var(--color-black);
  border-radius: var(--border-radius-md);
  max-width: 800px;
  margin: 0 auto;
  .shortcuts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
  }
  .shortcut-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-none);
  }
  .shortcut-action {
    font-family: var(--font-family-primary);
    color: var(--text-color-white);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-sm);
  }
  .shortcut-keys {
    display: flex;
    gap: 4px;
    .kbd-key {
      font-family: var(--font-family-primary);
      color: var(--text-color-white);
      font-size: var(--font-size-xs);
      line-height: var(--line-height-xs);
      border-radius: var(--border-radius-xs);
      border: var(--field-border);
      padding: 2px 6px;
      min-width: 20px;
      text-align: center;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      svg {
        width: var(--icon-size-sm);
        height: var(--icon-size-sm);
      }
    }
    .separator {
      font-family: var(--font-family-primary);
      color: var(--text-color-slate-gray);
      font-size: var(--font-size-xs);
      line-height: var(--line-height-xs);
    }
  }
}

// Employee group modal
.emp-group-modal {
  .rota-group-info-section {
    .rota-group-info-text {
      font-family: var(--font-family-primary);
      color: var(--text-color-primary);
      font-size: var(--font-size-sm);
      line-height: var(--line-height-sm);
      border: var(--field-border-primary);
      background: var(--color-primary-opacity);
      padding: var(--spacing-md);
      border-radius: var(--field-radius);
      margin-bottom: var(--spacing-xxl);
    }
  }
  .emp-grp-action-section {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 5px;
    margin-top: var(--spacing-xxl);
    border-top: var(--field-border);
    padding-top: var(--spacing-xxl);
  }
}
