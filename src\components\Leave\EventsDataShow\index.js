import {
  Box,
  Divider,
  InputAdornment,
  TextField,
  Typography,
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import UserAvatar from '@/components/UI/Avatar/UserAvatar';
import moment from 'moment';
import ArrowOutwardIcon from '@mui/icons-material/ArrowOutward';
import SearchIcon from '@mui/icons-material/Search';
import { useRouter } from 'next/navigation';
import NoDataView from '@/components/UI/NoDataView';
import { saveToStorage } from '@/helper/context';
import { identifiers } from '@/helper/constants/identifier';
import './eventshow.scss';
export default function EventsDataShow({ extraEventsData, type }) {
  let [search, setSearch] = useState('');
  const [filteredEvents, setFilteredEvents] = useState(
    extraEventsData?.extraEventsData
  );

  let router = useRouter();
  useEffect(() => {
    if (!search) {
      setFilteredEvents(extraEventsData?.extraEventsData);
    } else {
      const lowerSearch = search.toLowerCase();
      const filtered = extraEventsData?.extraEventsData?.filter(
        (filterItem) => {
          // const item = filterItem?.extendedProps;
          let item =
            type === 'multiMonthYear'
              ? filterItem?.extendedProps
              : filterItem?.extendedProps;
          return (
            item?.request_from_users?.user_full_name
              ?.toLowerCase()
              .includes(lowerSearch) ||
            item?.holiday_policy_name?.toLowerCase().includes(lowerSearch) ||
            item?.role?.role_name?.toLowerCase().includes(lowerSearch) ||
            item?.request_type?.toLowerCase().includes(lowerSearch) ||
            item?.request_status?.toLowerCase().includes(lowerSearch)
          );
        }
      );
      setFilteredEvents(filtered);
    }
  }, [search, extraEventsData?.extraEventsData]);
  return (
    <Box className="user-leave-wrap">
      <Divider />
      <Box className="user-leave-details-wrap mt16 mb16 pb32">
        <Box className="calender-search-wrap">
          <TextField
            variant="outlined"
            className="search-bar"
            placeholder="Search"
            value={search}
            onChange={(e) => setSearch(e?.target?.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="end">
                  <SearchIcon className="cursor-pointer" />
                </InputAdornment>
              ),
            }}
          />
        </Box>
        {filteredEvents?.length > 0 ? (
          filteredEvents?.map((obj, index) => {
            let item =
              type === 'multiMonthYear'
                ? obj?.extendedProps
                : obj?.extendedProps;
            if (item?.eventType !== 'holiday') {
              return (
                <>
                  <Box className="user-extra-data-wrap mt8" key={index}>
                    <Box className="user-details-wrap d-flex align-center justify-space-between mb8">
                      <Box className=" user-details d-flex align-center justify-space-between">
                        <Box className="user-avatar-wrap">
                          <UserAvatar
                            name={item?.request_from_users?.user_full_name}
                            src={
                              item?.request_from_users?.user_avatar_link
                                ? item?.request_from_users?.user_avatar_link
                                : null
                            }
                            classname="user-avarar"
                          />
                        </Box>
                        <Box className="user-name-wrap">
                          <Typography
                            variant="h6"
                            className="popover-title-wrap p14"
                          >
                            {item?.request_from_users?.user_full_name}
                          </Typography>
                          <Typography className="user-role p12" variant="h6">
                            {item?.leave_role_request?.role_name}
                          </Typography>
                        </Box>
                      </Box>
                      <Box className="right-arrow-wrap">
                        <ArrowOutwardIcon
                          onClick={() => {
                            if (extraEventsData?.type === 'staff') {
                              router.push(`/leave-remark/${obj?.publicId}`);
                              saveToStorage(identifiers?.RedirectData, {
                                viewMode: 'calendar',
                              });
                            } else if (extraEventsData?.type === 'own') {
                              router.push(`/own-leave/${obj?.publicId}`);
                              saveToStorage(identifiers?.RedirectData, {
                                viewMode: 'calendar',
                              });
                            }
                          }}
                          className="right-arrow-icon"
                        />
                      </Box>
                    </Box>

                    <Box className="leave-wrap d-flex align-start justify-space-between">
                      <Box className="leave-type-wrap">
                        <Typography
                          variant="h6"
                          className="leave-type-text p14"
                        >
                          Leave Type
                        </Typography>
                        <Typography
                          className="leave-text-wrap leave-type-text p12"
                          variant="h6"
                        >
                          {item?.leave_request_type_list?.name}
                        </Typography>
                      </Box>
                      <Box className="leave-date-wrap">
                        <Typography
                          variant="h6"
                          className="leave-date-text p14"
                        >
                          Date
                        </Typography>
                        <Typography
                          component="p"
                          className="leave-text-wrap p12"
                        >
                          {moment(item?.start_date).format('DD/MM')}-
                          {moment(item?.end_date).format('DD/MM/YYYY')}
                        </Typography>
                      </Box>
                      <Box className="total-days-text-wrap">
                        <Typography
                          variant="h6"
                          className="days-text-wrap p14 text-ellipsis-line"
                        >
                          {item?.leave_period_type === 'day'
                            ? 'Total Days'
                            : item?.leave_period_type === 'hour'
                              ? 'Total Hours'
                              : item?.duration_type === 'Days'
                                ? 'Total Days'
                                : 'Total Hours'}
                        </Typography>
                        <Typography
                          variant="h6"
                          className="leave-text-wrap p12"
                        >
                          {`${item?.leave_days} ${
                            item?.leave_period_type === 'day'
                              ? item?.leave_days === 1
                                ? 'Day'
                                : 'Days'
                              : item?.leave_period_type === 'hour'
                                ? item?.leave_days === 1
                                  ? 'Hour'
                                  : 'Hours'
                                : item?.duration_type === 'Days'
                                  ? item?.leave_days === 1
                                    ? 'Day'
                                    : 'Days'
                                  : item?.leave_days === 1
                                    ? 'Hour'
                                    : 'Hours'
                          }`}
                        </Typography>
                      </Box>
                      <Box className="leave-status-wrap">
                        <Typography variant="h6" className="leave-status p14">
                          Status
                        </Typography>
                        <Typography
                          variant="h6"
                          className={`p12 ${
                            item?.request_status === 'approved'
                              ? 'approved-status'
                              : item?.request_status === 'pending'
                                ? 'pending-status'
                                : item?.request_status === 'rejected'
                                  ? 'rejected-status'
                                  : 'canceled-status'
                          }`}
                        >
                          {item?.request_status}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                </>
              );
            } else {
              return (
                <>
                  <Box
                    className="user-extra-data-wrap mt8 holiday-wrap"
                    key={index}
                  >
                    <Box className="user-details-wrap d-flex align-center justify-space-between">
                      <Box className=" user-details d-flex align-center justify-space-between">
                        <Box className="user-name-wrap">
                          <Typography
                            variant="h6"
                            className="popover-title-wrap p14"
                          >
                            {item?.holiday_policy_name}
                          </Typography>
                        </Box>
                      </Box>
                      <Box className="">
                        <Typography
                          className="holiday-text fw600 p12"
                          variant="h6"
                        >
                          Holiday
                        </Typography>
                      </Box>
                    </Box>
                    <Box className="leave-date-wrap">
                      {/* <Typography variant="h6" className="leave-date-text p14">
                        Date
                      </Typography> */}
                      <Typography component="p" className="leave-text-wrap p12">
                        {moment(item?.holiday_policy_start_date).format(
                          'DD/MM'
                        )}
                        -
                        {moment(item?.holiday_policy_end_date).format(
                          'DD/MM/YYYY'
                        )}
                      </Typography>
                    </Box>
                  </Box>
                </>
              );
            }
          })
        ) : (
          <Box className="pt32 mt32">
            <NoDataView />
          </Box>
        )}
      </Box>
    </Box>
  );
}
