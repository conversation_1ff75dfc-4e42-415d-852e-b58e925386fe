import moment from 'moment';

export const getShiftsForDate = (shifts, date) => {
  return shifts.filter(
    (shift) =>
      moment(shift.startTime).utc().format('YYYY-MM-DD') ===
      date.format('YYYY-MM-DD')
  );
};

export const calculateTotalTime = (shifts) => {
  const totalMinutes = shifts.reduce((total, shift) => {
    const start = moment(shift.startTime);
    const end = moment(shift.endTime);
    return total + end.diff(start, 'minutes');
  }, 0);

  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;

  return `${hours}h ${minutes}m`;
};

export const getRoleUserCount = (shifts) => {
  return Object.values(
    shifts.reduce((acc, shift) => {
      const roleName = shift.role?.role_name || 'No Role';

      if (!acc[roleName]) {
        acc[roleName] = { roleName, shiftCount: 0 };
      }

      acc[roleName].shiftCount += 1;
      return acc;
    }, {})
  );
};

export const getTotalBreakMinutes = (shifts) => {
  return shifts.reduce((total, shift) => total + (shift.minutesBreak || 0), 0);
};

export const formatBreakTime = (minutes) => {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return `${hours}h ${mins}m`;
};

export const convertShiftTimeToDate = (currDate, shiftTime) => {
  if (!shiftTime) return { error: 'Invalid shift time' };

  const currentDate =
    currDate?.format('YYYY-MM-DD') || moment().format('YYYY-MM-DD'); // Get current date
  const [startTime, endTime] = shiftTime.split('-').map((t) => t.trim());

  // Convert to 24-hour format using moment.js
  const formattedStart = moment(startTime, ['h:mm A', 'H:mm']).format('HH:mm');
  const formattedEnd = moment(endTime, ['h:mm A', 'H:mm']).format('HH:mm');

  return {
    start_date: `${currentDate}T${formattedStart}:00.000Z`,
    end_date: `${currentDate}T${formattedEnd}:00.000Z`,
  };
};
