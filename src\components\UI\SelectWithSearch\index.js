import React from 'react';
import Box from '@mui/material/Box';
import InputLabel from '@mui/material/InputLabel';
// import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
// import Select from '@mui/material/Select';
// import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
// import { Typography } from '@mui/material';
import Select from 'react-select';
import './selectbox.scss';

const SingleSelect = ({
  label,
  options,
  value,
  onChange,
  className,
  menuPoisition,
  placeholder,
  disabled,
  isHeight,
  isOptionWithColor,
  // placeholder,
  // disabled
}) => {
  const dot = (color = 'transparent') => ({
    alignItems: 'center',
    display: 'flex',

    ':before': {
      backgroundColor: color,
      borderRadius: 10,
      content: '" "',
      display: 'block',
      marginRight: 8,
      height: 10,
      width: 10,
    },
  });
  const dots = (color = 'transparent') => ({
    alignItems: 'center',
    display: 'flex',

    ':before': {
      backgroundColor: color,
      borderRadius: 10,
      content: '" "',
      display: 'block',
      marginLeft: 8,
      height: 10,
      width: 10,
    },
  });
  const customStylesWithColor = {
    menu: (provided) => ({
      ...provided,
      maxHeight: '230px',
      boxShadow: '4px 4px 6px rgba(0, 0, 0, 0.1)',
      border: '1px solid #a6a8b1',
    }),
    placeholder: (provided) => ({
      ...provided,
      color: '#a6a8b1',
      fontSize: '16px',
      lineHeight: '18px',
      fontWeight: '400',
      opacity: '1',
      fontFamily: 'Inter, sans-serif',
      textTransform: 'capitalize',
    }),
    menuList: (provided) => ({
      ...provided,
      maxHeight: isHeight ? isHeight : '230px',
    }),
    menuPortal: (base) => ({
      ...base,
      zIndex: 99999,
    }),
    multiValue: (provided, { data }) => ({
      ...provided,
      ...dots(data.color ? data.color : '#39596e'),
    }),
    option: (provided, { data }) => ({
      ...provided,
      ...dot(data.color ? data.color : '#39596e'),
    }),
  };

  const customStyles = {
    menu: (provided) => ({
      ...provided,
      maxHeight: '230px',
      boxShadow: '4px 4px 6px rgba(0, 0, 0, 0.1)',
      border: '1px solid #a6a8b1',
    }),
    placeholder: (provided) => ({
      ...provided,
      color: '#a6a8b1',
      fontSize: '16px',
      lineHeight: '18px',
      fontWeight: '400',
      opacity: '1',
      fontFamily: 'Inter, sans-serif',
      textTransform: 'capitalize',
    }),
    singleValue: (provided, { isDisabled }) => ({
      ...provided,
      color: '#000000',
      fontSize: '16px',
      fontWeight: '400',
      lineHeight: '24px',
      // color: '#33343a !important',
      fontFamily: 'Inter, sans-serif',
    }),
    menuList: (provided) => ({
      ...provided,
      maxHeight: isHeight ? isHeight : '230px',
    }),
    menuPortal: (base) => ({
      ...base,
      zIndex: 99999,
    }),
    option: (provided, { data, isSelected }) => ({
      ...provided,
      backgroundColor: isSelected ? '#39596e' : provided.backgroundColor,
      color: isSelected ? '#FFFFFF' : provided.color,
    }),
  };
  return (
    <Box className={className ? `${className} select-wrap` : 'select-wrap'}>
      <FormControl fullWidth>
        <InputLabel id="custom-select-label">{label}</InputLabel>

        <Select
          className="basic-multi-select"
          classNamePrefix="select"
          menuPlacement={menuPoisition ? 'top' : 'auto'}
          menuPosition="fixed"
          isDisabled={disabled}
          value={value}
          placeholder={placeholder}
          onChange={onChange}
          options={options}
          menuPortalTarget={document.body}
          styles={isOptionWithColor ? customStylesWithColor : customStyles}
        />
      </FormControl>
    </Box>
  );
};

export default SingleSelect;
