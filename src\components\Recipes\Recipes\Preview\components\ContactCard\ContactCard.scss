.contact-card {
  background-color: var(--color-white);
  border-radius: var(--border-radius-md);
  border: var(--border-width-xs) solid var(--color-light-gray);
  box-shadow: var(--box-shadow-xs);

  &__header {
    padding: var(--spacing-lg) var(--spacing-xxl);
    border-bottom: var(--border-width-xs) solid var(--color-light-gray);
  }

  &__title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-lg);
    color: var(--text-color-black);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }

  &__content {
    padding: var(--spacing-lg);
  }

  // Contact Info Styles
  &__info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
  }

  &__info-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    transition: background-color 0.2s ease;

    &:hover {
      background-color: var(--color-off-white);
    }
  }

  &__info-icon {
    flex-shrink: 0;
    color: var(--color-primary);
  }

  &__info-text {
    font-family: var(--font-family-primary);
    color: var(--text-color-black);
    font-weight: var(--font-weight-medium);
  }

  &__info-link {
    font-family: var(--font-family-primary);
    color: var(--color-primary);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    transition: color 0.2s ease;

    &:hover {
      color: var(--color-primary-dark);
      text-decoration: underline;
    }
  }

  // Contact Form Styles
  &__form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  &__form-row {
    display: flex;
    flex-direction: column;

    &--two-columns {
      flex-direction: row;
      gap: var(--spacing-md);

      @media (max-width: 768px) {
        flex-direction: column;
        gap: var(--spacing-lg);
      }
    }
  }

  &__form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: var(--spacing-sm);
  }

  // Custom CTA Styles
  &__cta {
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-lg);
  }

  &__cta-text {
    font-family: var(--font-family-primary);
    color: var(--text-color-slate-gray);
    line-height: 1.6;
    margin-bottom: var(--spacing-sm);
  }

  .send-message-btn-wrap {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    .send-message-btn {
      padding: var(--spacing-sm);
    }
  }

  // Responsive Design
  @media (max-width: 768px) {
    &__header {
      padding: var(--spacing-md) var(--spacing-lg);
    }

    &__content {
      padding: var(--spacing-md);
    }

    &__form {
      gap: var(--spacing-md);
    }

    &__form-actions {
      justify-content: stretch;
    }
  }
}
