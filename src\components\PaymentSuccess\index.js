'use client';
import { Box, Typography } from '@mui/material';
import Image from 'next/image';
import React from 'react';
import CustomButton from '../UI/button';
import VerifiedIcon from '@mui/icons-material/Verified';
import { useRouter } from 'next/navigation';
import '../PasswordResetSuccess/resetpassword.scss';

export default function PassResetSuccess() {
  const router = useRouter();

  const handleNavigate = () => {
    router.push('/login');
  };
  return (
    <Box className="reset-password-wrap">
      <Box className="reset-password password-success">
        <VerifiedIcon className="verified-icon" />
        <Box className="reset-text-wrap">
          <Typography className="text-wrap heading-text" variant="h5">
            Payment Successful
          </Typography>

          <Typography className="sub-heading-text reset-text">
            Thank you! Your payment has been successfully processed.
          </Typography>
        </Box>
        {/* <Box className="back-to-login-btn-wrap" textAlign="center">
          <CustomButton
            className="back-to-login-btn"
            fontWeight="600"
            variant="contained"
            background="#39596e"
            backgroundhover="#39596e"
            colorhover="#FFFFFF"
            title="Back To Login"
            type="submit"
            onClick={() => handleNavigate()}
          />
        </Box> */}
      </Box>
    </Box>
  );
}
