'use client';

import React, { useContext, useEffect, useState } from 'react';
import { Box } from '@mui/material';
import AuthContext from '@/helper/authcontext';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import DashboardBYID from '@/components/Dashboard/DashboardBYID/index';
import '../dashboard.scss';

export default function ChartDashboard({ params }) {
  const { authState, AllListsData } = useContext(AuthContext);
  const [IsDefaultFilter, setIsDefaultFilter] = useState(false);
  const DashbaordId = params?.id;
  const [loader, setLoader] = useState(false);
  const [DashboardData, setDashboardData] = useState();

  // Get One Dashboard by ID
  const getOneDashboardList = async (
    ID,
    date,
    branch,
    isDefault,
    timeperiod
  ) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_DASHBOARD_BY_ID +
          `${ID}?branch_id=${branch}&date_filter=${date}&filter_time_period=${timeperiod}`
      );
      if (status === 200) {
        setLoader(false);
        const modelData =
          data?.data &&
          data?.data?.dashboard_models &&
          data?.data?.dashboard_models?.length > 0 &&
          data?.data?.dashboard_models?.map((item) => {
            if (
              item?.model_type === 'bar_chart' ||
              item?.model_type === 'line_chart'
            ) {
              const grpahsData = item?.dashboard_data;
              grpahsData.series = item?.dashboard_data?.series?.map((s) => {
                return {
                  ...s,
                  interpolation: {
                    type: 'smooth',
                  },
                };
              });
              return {
                ...item,
                dashboard_data: {
                  ...item?.dashboard_data,
                  title: {
                    text: item?.model_title,
                  },
                },
              };
            } else if (item?.model_type === 'pie_chart') {
              const series =
                item?.dashboard_data &&
                item?.dashboard_data?.series &&
                item?.dashboard_data?.series?.length > 0 &&
                item?.dashboard_data?.series?.map((s) => {
                  const fills = s?.fills?.map((f) => f?.branch_color);
                  return {
                    ...s,
                    type: s?.type,
                    angleKey: s?.yKey,
                    calloutLabelKey: s?.xKey,
                    sectorLabelKey: s?.yKey,
                    fills: fills,
                  };
                });
              const dashboard = {
                ...item?.dashboard_data,
                series:
                  series && series?.length > 0
                    ? series
                    : item?.dashboard_data?.series,
                title: {
                  text: item?.model_title,
                },
                width: 600,
                height: 450,
              };
              return { ...item, dashboard_data: dashboard };
            }
            return { ...item };
          });
        setDashboardData({
          ...data?.data,
          dashboard_models: modelData,
          dashboard_filter: data?.data?.dashboard_filter
            ? JSON.parse(data?.data?.dashboard_filter)
            : '',
        });
        setIsDefaultFilter(!isDefault ? true : false);
      }
    } catch {
      setLoader(false);
      setDashboardData([]);
    }
  };

  useEffect(() => {
    if (
      DashbaordId &&
      (authState?.UserPermission?.dashboard === 1 ||
        authState?.UserPermission?.dashboard === 2)
    ) {
      getOneDashboardList(DashbaordId, '', '', true, '');
    }
  }, [authState?.UserPermission?.dashboard, DashbaordId]);

  return (
    <>
      <Box className="dashboard-section">
        <DashboardBYID
          DashboardData={DashboardData}
          branchList={AllListsData?.ActiveBranchList}
          isDefault={false}
          DashbaordId={DashbaordId}
          getOneDashboardList={getOneDashboardList}
          IsDefaultFilter={IsDefaultFilter}
          checkLoader={loader}
        />
      </Box>
    </>
  );
}
