import { Box, CircularProgress, Tooltip, Typography } from '@mui/material';
import { DataGrid, gridClasses } from '@mui/x-data-grid';
import React, { useState, useEffect, useContext } from 'react';
import { URLS } from '@/helper/constants/urls';
import axiosInstance from '@/helper/axios/axiosInstance';
import {
  generateYearsFromJoiningDate,
  setApiMessage,
  DateFormat,
} from '@/helper/common/commonFunctions';
import { saveToStorage } from '@/helper/context';
import { identifiers } from '@/helper/constants/identifier';
import CustomSelect from '@/components/UI/CustomSelect';
import LeavePopOver from '@/components/Leave/LeavePopOver';
import Searchbar from '@/components/UI/CustomSearch';
import CustomButton from '@/components/UI/CustomButton';
import ClearOutlinedIcon from '@mui/icons-material/ClearOutlined';
import CheckIcon from '@mui/icons-material/Check';
import ViewIcon from '@/components/ActionIcons/ViewIcon';
import AuthContext from '@/helper/authcontext';
import CustomPagination from '@/components/UI/customPagination';
import { useRouter } from 'next/navigation';
import { staticOptions } from '@/helper/common/staticOptions';
import './listview.scss';
import NoDataView from '@/components/UI/NoDataView';

export default function ListView() {
  const [loader, setLoader] = useState(false);
  const [leaveRemarkList, setLeaveRemarkList] = useState([]);
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [leaveList, setLeaveList] = useState([]);
  const { authState } = useContext(AuthContext);
  const [filterDataApplied, setFilterDataApplied] = useState({
    year: '',
    status: '',
    leaveType: '',
    searchValue: '',
  });

  const [filterData, setFilterData] = useState({
    year: '',
    status: '',
    leaveType: '',
    searchValue: '',
  });

  let router = useRouter();

  const columns = [
    {
      field: 'Leave Type',
      headerName: 'Leave Type',
      width: 130,
      minWidth: 130,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        let leaveType = params?.row?.leave_request_type_list?.name || '';

        return (
          <>
            {/* <Box className="d-flex align-center justify-center h100">
              <Box className="pl8">
                <Typography className="title-text">
                  {params?.row?.leave_request_type_list?.name}
                </Typography>
              </Box>
            </Box> */}
            <Box className="d-flex align-center justify-center h100 w100">
              {leaveType !== '-' ? (
                <Typography className="title-text">
                  {leaveType?.length > 20 ? (
                    <>
                      {leaveType?.substring(0, 15)}
                      <Tooltip
                        title={<Typography>{leaveType}</Typography>}
                        classes={{
                          tooltip: 'info-tooltip-container ',
                        }}
                        placement="bottom"
                        arrow
                      >
                        <span className="more-text-wrap cursor-pointer">
                          ... More
                        </span>
                      </Tooltip>
                    </>
                  ) : (
                    leaveType
                  )}
                </Typography>
              ) : (
                '-'
              )}
            </Box>
          </>
        );
      },
    },
    {
      field: 'start_date',
      headerName: 'From',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100">
            {params?.value !== '-' ? (
              <Tooltip
                arrow
                title={
                  <Typography>
                    {/* Day & Hours dates */}
                    {params?.row?.duration_type === 'Hours' &&
                    params?.row?.start_date
                      ? DateFormat(params?.row?.start_date, 'datesUTC')
                      : params?.row?.start_date
                        ? DateFormat(params?.row?.start_date, 'dates')
                        : ''}
                  </Typography>
                }
                placement="bottom-start"
                classes={{
                  tooltip: 'info-tooltip-container ',
                }}
              >
                <Typography className="title-text text-ellipsis cursor-pointer">
                  {params?.row?.duration_type === 'Hours' &&
                  params?.row?.start_date
                    ? DateFormat(params?.row?.start_date, 'datesUTC')
                    : params?.row?.start_date
                      ? DateFormat(params?.row?.start_date, 'dates')
                      : ''}{' '}
                </Typography>
              </Tooltip>
            ) : (
              '-'
            )}
          </Box>
        );
      },
    },
    {
      field: 'end_date',
      headerName: 'To',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100">
            {params?.value !== '-' ? (
              <Tooltip
                arrow
                title={
                  <Typography>
                    {/* Day & Hours dates */}
                    {params?.row?.duration_type === 'Hours' &&
                    params?.row?.end_date
                      ? DateFormat(params?.row?.end_date, 'datesUTC')
                      : params?.row?.end_date
                        ? DateFormat(params?.row?.end_date, 'dates')
                        : ''}
                  </Typography>
                }
                classes={{
                  tooltip: 'info-tooltip-container ',
                }}
                placement="bottom-start"
              >
                <Typography className="title-text text-ellipsis cursor-pointer">
                  {/* Day & Hours dates */}
                  {params?.row?.duration_type === 'Hours' &&
                  params?.row?.end_date
                    ? DateFormat(params?.row?.end_date, 'datesUTC')
                    : params?.row?.end_date
                      ? DateFormat(params?.row?.end_date, 'dates')
                      : ''}
                </Typography>
              </Tooltip>
            ) : (
              '-'
            )}
          </Box>
        );
      },
    },

    {
      field: 'leave_days',
      // headerName: 'No. of Days/Hours',
      headerName: 'No. of Days/Hours',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100">
            <Box sx={{ boxShadow: 'none' }} className="cursor-pointer">
              <Tooltip
                className="event-title text-ellipsis title-text"
                variant="h6"
                arrow
                interactive
                title={
                  <LeavePopOver
                    title={`Total Leave ${params?.row?.leave_period_type === 'day' ? 'Days' : 'Hours'}`}
                    Leavedays={`Dates`} //${params?.row?.leave_period_type === 'day' ? 'Days' : 'Hours'}
                    leave="Leave"
                    startDate={params?.row?.start_date}
                    endDate={params?.row?.end_date}
                    leaveTypes={params?.row?.leave_period_type}
                    leave_days={params?.row?.leave_days}
                    leave_days_obj={
                      params?.row?.leave_days_obj
                        ? JSON.parse(params?.row?.leave_days_obj)
                        : null
                    }
                  />
                }
                classes={{
                  tooltip: 'leave-days-popover',
                }}
              >
                <Typography className="leave-days-wrap">
                  {params?.row?.leave_days}
                </Typography>
              </Tooltip>
            </Box>
          </Box>
        );
      },
    },

    {
      field: 'Added date',
      headerName: 'Added date',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100">
            <Typography className="title-text text-ellipsis pr4">
              {DateFormat(params?.row?.createdAt, 'dates')}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'request_reason',
      headerName: 'Reason',
      width: 160,
      minWidth: 160,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100">
            {params?.value !== '-' ? (
              <Typography className="title-text">
                {params?.value?.length > 15 ? (
                  <>
                    {params?.value.substring(0, 15)}
                    <Tooltip
                      title={<Typography>{params?.value}</Typography>}
                      classes={{
                        tooltip: 'info-tooltip-container ',
                      }}
                      placement="bottom"
                      arrow
                    >
                      <span className="more-text-wrap cursor-pointer">
                        ... More
                      </span>
                    </Tooltip>
                  </>
                ) : (
                  params?.value
                )}
              </Typography>
            ) : (
              '-'
            )}
          </Box>
        );
      },
    },
    {
      field: 'Action By',
      headerName: 'Action By',
      width: 160,
      minWidth: 160,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return params?.row?.request_status === 'pending' ? (
          <Box className="d-flex align-center justify-center h100 flex-col cursor-pointer">
            <Typography variant="h6" className="title-text fw400 text-ellipsis">
              -
            </Typography>
          </Box>
        ) : (
          <Tooltip
            arrow
            title={
              <Box className="d-flex align-center flex-col justify-center h100">
                {params?.row?.request_approved_users?.user_full_name ? (
                  <Typography className="title-text fw400 text-ellipsis">
                    {params?.row?.request_approved_users?.user_full_name}
                  </Typography>
                ) : (
                  <Typography className="title-text fw400 text-ellipsis">
                    System Auto-Approval
                  </Typography>
                )}

                {/* updatedAt will always be displayed */}
                <Typography className="title-text fw400 text-ellipsis">
                  {DateFormat(params?.row?.updatedAt, 'datesUTC')}
                </Typography>
              </Box>
            }
            classes={{
              tooltip: 'info-tooltip-container ',
            }}
          >
            <Box className="d-flex align-center justify-center h100 flex-col cursor-pointer">
              {params?.row?.request_approved_users?.user_full_name ? (
                <Typography
                  variant="h6"
                  className="title-text fw400 text-ellipsis"
                >
                  {params?.row?.request_approved_users?.user_full_name}
                </Typography>
              ) : (
                <Typography
                  variant="h6"
                  className="title-text fw400 text-ellipsis"
                >
                  System Auto-Approval
                </Typography>
              )}

              {/* updatedAt will always be displayed */}
              <Typography
                variant="h6"
                className="title-text fw400 text-ellipsis"
              >
                {DateFormat(params?.row?.updatedAt, 'datesUTC')}
              </Typography>
            </Box>
          </Tooltip>
        );
      },
    },
    {
      field: 'request_status',
      headerName: 'Status',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        const statusStyles = {
          rejected: 'leave-rejected',
          pending: 'leave-draft',
          cancelled: 'leave-cancelled',
          approved: 'leave-success',
        };

        return (
          <Box className="d-flex align-center justify-center h100 text-capital leave-status-wrap">
            <Typography
              className={`sub-title-text ${statusStyles[params?.value] || statusStyles.approved} fw600`}
            >
              {params?.value}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'actions',
      headerName: 'Action',
      width: 90,
      minWidth: 90,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        // Custom actions icons for each row
        return (
          <Box className="d-flex justify-center actions view-icon-wrap">
            <Tooltip
              title={<Typography>View</Typography>}
              arrow
              classes={{
                tooltip: 'info-tooltip-container ',
              }}
            >
              <Box>
                <ViewIcon
                  onClick={() => {
                    router.push(`/own-leave/${params?.row?.id}`);
                    saveToStorage(identifiers?.RedirectData, {
                      viewMode: 'list',
                    });
                    // setAuthState({ ...authState, listViewMode: 'list' });
                  }}
                />
              </Box>
            </Tooltip>
          </Box>
        );
      },
    },
  ];

  const getLeaveRemarkList = async (page, filter, Rpp) => {
    // let calenderStarDate = startDate;
    // let calenderEndDate = endDate;
    // setLoader(true);
    const rowPer = Rpp ? Rpp : rowsPerPage;
    try {
      const { status, data } = await axiosInstance.get(
        `${URLS?.GET_OWN_LEAVE}?size=${rowPer}&page=${page}&year=${filter?.year}&leave_type_id=${filter?.leaveType}&search=${filter?.searchValue}&request_status=${filter?.status}`
      );
      if (status === 200) {
        setLoader(false);
        setTotalCount(data?.count);
        setLeaveRemarkList(data?.data);
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const getLeaveList = async (id, dType) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.USER_LEAVE_POLICIES + `${id}?duration_type=${dType}`
      );

      if (status === 200) {
        setLoader(false);
        let filterUserList = data?.data?.map((user) => ({
          ...user,
          label: user?.type_name,
          value: user?.id,
        }));
        setLeaveList(filterUserList);
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const handleFilterData = (type) => {
    if (type === 'apply') {
      setFilterDataApplied(filterData);
      getLeaveRemarkList(
        1,
        filterData
        // getDateByfilterApi().start,
        // getDateByfilterApi().end
      );
    } else {
      const clearFilter = {
        year: '',
        status: '',
        leaveType: '',
        searchValue: '',
      };
      // setDateSelectedOption('this_month');
      setFilterData(clearFilter);
      setFilterDataApplied(clearFilter);
      getLeaveRemarkList(
        1,
        clearFilter
        // moment().startOf('day').format('YYYY-MM-DD'),
        // moment().startOf('day').format('YYYY-MM-DD')
      );
    }
  };

  // Handle Pagination
  const onPageChange = (newPage) => {
    setPage(newPage);
    getLeaveRemarkList(
      newPage,
      filterDataApplied
      // getDateByfilterApi().start,
      // getDateByfilterApi().end
    );
  };

  const OnRowPerPage = (newRowsPerPage) => {
    setPage(1);
    setRowsPerPage(newRowsPerPage);
    getLeaveRemarkList(
      1,
      filterDataApplied,
      // getDateByfilterApi().start,
      // getDateByfilterApi().end,
      newRowsPerPage
    );
  };
  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      handleFilterData();
    }
  };

  useEffect(() => {
    getLeaveRemarkList(page, filterDataApplied, rowsPerPage);
  }, []);

  useEffect(() => {
    getLeaveList(authState?.id, 'Days');
  }, [authState?.id]);

  return (
    <Box className="view-list-table">
      <Box className="search-section-wrap">
        <Box className="search-section-fields">
          <Searchbar
            setSearchValue={(e) => {
              setFilterData({
                ...filterData,
                searchValue: e,
              });
            }}
            searchValue={filterData?.searchValue}
            onKeyPress={handleKeyPress}
          />
        </Box>
        {/* Select for Leave Type */}
        <Box className="search-section-fields">
          <CustomSelect
            placeholder="Select Year"
            options={generateYearsFromJoiningDate(
              authState?.user_joining_date
                ? authState?.user_joining_date
                : authState?.createdAt
                  ? authState?.createdAt
                  : ''
            )}
            value={
              generateYearsFromJoiningDate(
                authState?.user_joining_date
                  ? authState?.user_joining_date
                  : authState?.createdAt
                    ? authState?.createdAt
                    : ''
              )?.find((opt) => {
                return opt?.value === filterData?.year;
              }) || ''
            }
            onChange={(e) => {
              setFilterData({
                ...filterData,
                year: e?.value,
              });
            }}
          />
        </Box>
        <Box className="search-section-fields">
          <CustomSelect
            placeholder="Select Leave Type"
            options={leaveList}
            value={
              leaveList?.find((opt) => {
                return opt?.value === filterData?.leaveType;
              }) || ''
            }
            onChange={(e) => {
              setFilterData({
                ...filterData,
                leaveType: e?.value,
              });
            }}
          />
        </Box>
        {/* Select for Leave Status */}
        <Box className="search-section-fields">
          <CustomSelect
            placeholder="Leave Status"
            options={staticOptions?.STATUS_OPTIONS}
            value={
              staticOptions?.STATUS_OPTIONS?.find((opt) => {
                return opt?.value === filterData?.status;
              }) || ''
            }
            onChange={(e) => {
              setFilterData({
                ...filterData,
                status: e?.value,
              });
            }}
          />
        </Box>
        {/* <Box className="d-flex">
          <DateFilter
            dateSelectedOption={dateSelectedOption}
            setDateSelectedOption={setDateSelectedOption}
            customStartDate={customStartDate}
            setCustomStartDate={setCustomStartDate}
            customEndDate={customEndDate}
            setCustomEndDate={setCustomEndDate}
            dateFilterList={dateFilterList}
          />
        </Box> */}
        <Box className="d-flex align-center gap-sm">
          <CustomButton
            variant="contained"
            isIconOnly
            startIcon={
              <Tooltip
                title={<Typography>Apply Filter</Typography>}
                arrow
                classes={{
                  tooltip: 'info-tooltip-container',
                }}
              >
                <CheckIcon />
              </Tooltip>
            }
            onClick={() => {
              setPage(1);
              handleFilterData('apply');
            }}
          />
          <CustomButton
            variant="outlined"
            isIconOnly
            startIcon={
              <Tooltip
                title={<Typography>Clear Filter</Typography>}
                arrow
                classes={{
                  tooltip: 'info-tooltip-container',
                }}
              >
                <ClearOutlinedIcon />
              </Tooltip>
            }
            onClick={() => {
              handleFilterData('cancel');
            }}
          />
        </Box>
      </Box>

      <Box className="table-container table-layout">
        {loader ? (
          <Box className="content-loader">
            <CircularProgress className="loader" color="inherit" />
          </Box>
        ) : (
          <>
            {leaveRemarkList?.length === 0 ? (
              <Box className="no-data d-flex align-center justify-center">
                <NoDataView
                  title="No leave Records Found"
                  description="There is no leave data available at the moment."
                  value={leaveRemarkList?.length === 0 ? 'no-data' : ''}
                />
              </Box>
            ) : (
              <>
                <DataGrid
                  rows={leaveRemarkList || []}
                  columns={columns}
                  pageSize={rowsPerPage}
                  checkboxSelection={false}
                  disableSelectionOnClick
                  // columnVisibilityModel={{
                  //   actions:
                  //     authState?.UserPermission?.leave_center === 2
                  //       ? true
                  //       : false,
                  // }}
                  rowHeight={70}
                  autoHeight
                  // getRowHeight={() => 'auto'}
                  sx={{
                    transition: 'none', // Disables transition effects
                    [`& .${gridClasses.cell}`]: {
                      py: 1,
                    },
                  }}
                  hideFooter
                />
                <CustomPagination
                  currentPage={page}
                  totalCount={totalCount}
                  rowsPerPage={rowsPerPage}
                  onPageChange={onPageChange}
                  OnRowPerPage={OnRowPerPage}
                />
              </>
            )}
          </>
        )}
      </Box>
    </Box>
  );
}
