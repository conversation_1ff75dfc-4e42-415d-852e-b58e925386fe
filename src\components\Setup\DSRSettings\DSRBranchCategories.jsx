'use client';
import React from 'react';
import { Box, Typography } from '@mui/material';
import PreLoader from '@/components/UI/Loader';
import DSRCategoryMap from '@/components/DSR/BranchDSR/Categorymap';
import UpdateCategory from '@/components/DSR/BranchDSR/Category';
// import CustomCheckbox from '@/components/UI/CustomCheckbox';
import CustomSelect from '@/components/UI/CustomSelect';
import InfoIcon from '@mui/icons-material/Info';
import { getBranchManagerInfo } from '@/helper/common/commonFunctions';

export default function DSRBranchCategories({
  loader,
  dsrCatData,
  AddSubCategory,
  AddDSR,
  setAddDSR,
  ReorderBranchPayment,
  getCategoriesPaymentList,
  updateBranchActive,
  AllListsData,
  selectedBranch,
  setSelectedBranch,
  authState,
}) {
  const { isBranchManager } = getBranchManagerInfo(authState);

  return (
    <>
      {loader && <PreLoader />}

      <Box className="d-flex align-start pb24">
        {/* <CustomCheckbox
          checked={true}
          // onChange={(e) => {
          //   setDeactive(e?.target?.checked);
          // }}
          disableRipple
          label={
            <Typography className="sub-content-text">
              When adding DSR, WSR, or Payroll, the default open will be
              displayed. You must save first.
            </Typography>
          }
        /> */}
        <InfoIcon
          sx={{ marginRight: '8px' }}
          className="info-icon cursor-poniter"
        />

        <Typography className="sub-title-text">
          Select the branch and the categories from the list below to enable
          them for DSR, WSR, and Expense Reports in the selected branch. Click
          'Save' to apply your selections.
        </Typography>
      </Box>

      <Box className="branch-list-select-field pb16">
        <CustomSelect
          options={AllListsData?.ActiveBranchList}
          value={
            AllListsData?.ActiveBranchList?.find((opt) => {
              return opt?.value === selectedBranch;
            }) || ''
          }
          name="selectedBranch"
          onChange={(e) => {
            setSelectedBranch(e?.value);
          }}
          label="Branch name"
          isClearable={false}
          required
          showDot={true}
          isDisabled={isBranchManager}
        />
      </Box>

      <Box className="dsr-setting-container">
        <Box className="dsr-settings-right-container">
          {dsrCatData && dsrCatData?.length > 0 && (
            <DSRCategoryMap
              dsrCatData={dsrCatData}
              AddDSR={AddDSR}
              setAddDSR={setAddDSR}
              updateBranchActive={updateBranchActive}
            />
          )}
        </Box>
        <Box className="dsr-settings-left-container">
          <Typography className="title-sm fw600 active-role">
            {AddDSR?.list?.name}
          </Typography>
          <UpdateCategory
            AddDSR={AddDSR}
            ReorderBranchPayment={ReorderBranchPayment}
            getCategoriesPaymentList={getCategoriesPaymentList}
            AddSubCategory={AddSubCategory}
          />
        </Box>
      </Box>
    </>
  );
}
