import React, { useRef, useState, useEffect } from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import RecipesIcon from '@/components/UI/RecipePlaceholderIcon/RecipesIcon';

const SCROLL_AMOUNT = 120;

const CategoryScrollChips = ({
  categories = [],
  selectedCategory = [],
  onCategorySelect,
  loading = false,
}) => {
  const scrollRef = useRef(null);
  const [showLeft, setShowLeft] = useState(false);
  const [showRight, setShowRight] = useState(false);

  // Check if arrows should be shown
  const checkArrows = () => {
    const el = scrollRef.current;
    if (!el) return;
    setShowLeft(el.scrollLeft > 0);
    setShowRight(el.scrollLeft + el.clientWidth < el.scrollWidth - 1);
  };

  useEffect(() => {
    checkArrows();
    const el = scrollRef.current;
    if (!el) return;
    el.addEventListener('scroll', checkArrows);
    window.addEventListener('resize', checkArrows);
    return () => {
      el.removeEventListener('scroll', checkArrows);
      window.removeEventListener('resize', checkArrows);
    };
  }, [categories, loading]);

  const scroll = (dir) => {
    const el = scrollRef.current;
    if (!el) return;
    el.scrollBy({ left: dir * SCROLL_AMOUNT, behavior: 'smooth' });
  };

  return (
    <div className="recipes-listing__categories">
      {showLeft && (
        <button
          className="recipes-listing__category-arrow left"
          onClick={() => scroll(-1)}
          aria-label="Scroll left"
          type="button"
        >
          <Icon name="ChevronLeft" size={24} />
        </button>
      )}
      <div className="recipes-listing__category-chips" ref={scrollRef}>
        {loading ? (
          <div className="recipes-listing__category-loading">
            {[1, 2, 3, 4, 5].map((index) => (
              <div
                key={index}
                className="recipes-listing__category-chip recipes-listing__category-chip--loading"
              >
                <div className="skeleton-icon"></div>
                <div className="skeleton-text"></div>
              </div>
            ))}
          </div>
        ) : (
          categories.map((category) => {
            const isActive = selectedCategory?.includes(category?.id);
            return (
              <button
                key={category.id}
                onClick={() => onCategorySelect(category?.id)}
                className={`recipes-listing__category-chip${isActive ? ' recipes-listing__category-chip--active' : ''}`}
                aria-pressed={isActive}
                tabIndex={0}
                type="button"
              >
                {category?.icon ? (
                  <Icon
                    name={category.icon}
                    size={28}
                    className={`recipes-listing__category-chip-svg${isActive ? ' active' : ''}`}
                  />
                ) : category?.icon_url ? (
                  <RecipesIcon
                    iconUrl={category?.icon_url}
                    altText={category?.label}
                    imgWidth={28}
                    imgHeight={28}
                    className="recipes-listing__category-chip-icon"
                  />
                ) : (
                  ''
                )}
                <span>{category?.label}</span>
              </button>
            );
          })
        )}
      </div>
      {showRight && (
        <button
          className="recipes-listing__category-arrow right"
          onClick={() => scroll(1)}
          aria-label="Scroll right"
          type="button"
        >
          <Icon name="ChevronRight" size={24} />
        </button>
      )}
    </div>
  );
};

export default CategoryScrollChips;
