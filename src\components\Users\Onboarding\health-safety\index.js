'use client';

import React, { useRef, useEffect, useState } from 'react';
import {
  Box,
  Typography,
  FormGroup,
  FormControlLabel,
  Checkbox,
} from '@mui/material';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomDatePicker from '@/components/UI/CustomDatePicker';
import CustomButton from '@/components/UI/CustomButton';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import dayjs from 'dayjs';
import DialogBox from '@/components/UI/Modalbox';
import FileDetails from '@/components/FolderViews/FilleDetailsOnboarding';
import './healthsafety.scss';

export default function HealthandSafety({
  isUserScreen,
  UserDetails,
  UserId,
  isMyProfile,
  getOnboardingChecklist,
  formHealthSafety,
  ViewAccessOnly,
}) {
  const [healthSafechecked, setHealthSafeChecked] = useState([]);
  const formikRef = useRef(null);
  const [loader, setLoader] = useState(false);
  const [hsDetails, setHSDetails] = useState('');
  const [toggleModal, setToggleModal] = useState(false);
  const [CategoryDetails, setCategoryDetails] = useState('');
  const [CategoryDetailsOne, setCategoryDetailsOne] = useState('');
  const [currentHSIndex, setCurrentHSIndex] = useState(0);
  const [dialogTitle, setDialogboxTitle] = useState('');
  const [filesId, setFilesId] = useState([]);

  // User track by category
  const userMediaTrack = async (id, isLast) => {
    const requestData = {
      category_id: id,
    };

    try {
      const { status } = await axiosInstance.post(
        URLS?.USER_MEDIA_TRACK,
        requestData
      );
      if (status === 200) {
        isLast && setToggleModal(false);
        handleHealthSafe(id);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const handleHealthSafe = (value) => {
    const currentHSIndex = healthSafechecked?.indexOf(value);
    const newChecked = [...healthSafechecked];
    if (currentHSIndex === -1) {
      newChecked.push(value);
      setHealthSafeChecked(newChecked);
    } else {
      newChecked.splice(currentHSIndex, 1);
    }
  };

  // List of Category details
  const getDocumentCategory = async (ID) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_ONE_CATEGORY + `${ID}`
      );
      if (status === 200) {
        setLoader(false);

        setCategoryDetailsOne(data?.data);
      }
    } catch (error) {
      setLoader(false);
      setCategoryDetailsOne();
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  function filterFiles(categories) {
    const result = [];

    categories.forEach((category) => {
      if (category?.category_type === 'file') {
        result.push(category);
      }

      if (category?.children && category?.children?.length > 0) {
        result.push(...filterFiles(category?.children));
      }
    });

    return result;
  }
  function filterFilesIdss(categories) {
    const result = [];

    categories.forEach((category) => {
      if (category?.category_type === 'file') {
        result.push(category?.id);
      }

      if (category?.children && category?.children?.length > 0) {
        result.push(...filterFiles(category?.children));
      }
    });

    return result;
  }
  const getStatusText = (isAllCate, healthSafechecked) => {
    if (!isAllCate || isAllCate.length === 0) return 'Completed';
    if (isAllCate.every((id) => healthSafechecked.includes(id)))
      return 'Completed';
    if (isAllCate.some((id) => healthSafechecked.includes(id)))
      return 'Ongoing';
    return 'Pending';
  };

  const getStatusClass = (isAllCate, healthSafechecked) => {
    const baseClass = 'title-text fw600 pt16 text-capital';
    if (!isAllCate || isAllCate?.length === 0)
      return `${baseClass} active-role`;
    if (isAllCate?.every((id) => healthSafechecked?.includes(id)))
      return `${baseClass} active-role`;
    if (isAllCate?.some((id) => healthSafechecked?.includes(id)))
      return `${baseClass} color-blue`;
    return baseClass;
  };
  const CategoryHeading = (Heading, isOpen, item) => {
    let isAllCate = filterFilesIdss(item?.children);

    return (
      <Box className="hs-heading">
        <Typography
          className={
            isMyProfile === undefined || !isOpen
              ? 'body-text text-underline cursor-auto fw600 pt16'
              : item?.is_tracked_by_user
                ? 'body-text text-underline link-text fw600 pt16'
                : 'body-text text-underline link-text fw600 pt16'
          }
        >
          <span
            onClick={() => {
              if (isMyProfile !== undefined && isOpen) {
                // setCategoryDetails(item?.children);
                setCurrentHSIndex(0);
                setDialogboxTitle(Heading);
                const files = filterFiles(item?.children);
                setCategoryDetails(files);
                getDocumentCategory(files?.[0]?.id);
                const fid = files && files?.map((f) => f?.id);
                fid && fid?.length > 0 ? setFilesId(fid) : setFilesId([]);
                setToggleModal(true);
              }
            }}
          >
            {Heading}
          </span>
        </Typography>

        <Typography className={getStatusClass(isAllCate, healthSafechecked)}>
          <span>{getStatusText(isAllCate, healthSafechecked)}</span>
        </Typography>
      </Box>
    );
  };
  const OnboardingForms = async () => {
    setLoader(true);
    let sendData = {
      health_safety_complete: true,
    };
    const method = isMyProfile ? 'post' : 'put';
    const ApiUrl = isMyProfile
      ? URLS.CREATE_ONBOARDING_FORM + `?checklist_id=${3}`
      : URLS.UPDATE_ONBOARDING_FORM +
        `?form_user_id=${UserId}&checklist_id=${3}`;
    try {
      const { status, data } = await axiosInstance[method](ApiUrl, sendData);

      if (status === 200) {
        if (data?.status) {
          setApiMessage('success', data?.message);
          getOnboardingChecklist(UserId);
        } else {
          setApiMessage('error', data?.message);
        }
        setLoader(false);
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const handleSubCate = (item) => {
    return (
      <>
        {item?.children &&
          item?.children?.length > 0 &&
          item?.children?.map((data) => {
            if (data?.category_type === 'folder') {
              return <>{handleSubCate(data)}</>;
            }
            return (
              <FormGroup className="hf-checkbox pt16">
                <FormControlLabel
                  control={
                    <Checkbox
                      className="check-box "
                      icon={
                        <CheckBoxOutlineBlankIcon className="uncheck-icon" />
                      }
                      checkedIcon={<CheckBoxIcon className="check-icon" />}
                    />
                  }
                  className="check-box-text  title-text"
                  name="sl_plan"
                  checked={healthSafechecked?.includes(data?.id)}
                  onChange={() => {
                    // setFieldValue('sl_plan', item?.name);
                    handleHealthSafe(data?.id);
                  }}
                  disabled={
                    ViewAccessOnly || item?.total_items !== item?.total_track
                  }
                  label={data?.category_name}
                />
              </FormGroup>
            );
          })}
      </>
    );
  };

  useEffect(() => {
    setHSDetails(UserDetails);
    formikRef.current.setFieldValue(
      'hs_fname',
      UserDetails?.user_first_name && UserDetails?.user_last_name
        ? UserDetails?.user_first_name + ' ' + UserDetails?.user_last_name
        : UserDetails?.user_first_name
          ? UserDetails?.user_first_name
          : ''
    );
    formikRef.current.setFieldValue(
      'birthdate',
      UserDetails?.date_of_birth
        ? dayjs(UserDetails?.date_of_birth).format('YYYY-MM-DD')
        : null
    );
  }, [UserDetails]);
  function filterFilesId(categories) {
    const result = [];

    categories.forEach((category) => {
      if (category?.category_type === 'file' && category?.is_tracked_by_user) {
        result.push(category?.id);
      }

      if (category?.children && category?.children?.length > 0) {
        result.push(...filterFilesId(category?.children));
      }
    });

    return result;
  }

  // const UserReject = async () => {
  //   setLoader(true);

  //   const sendData = {
  //     user_id: UserId,
  //     verification_status: 'rejected',
  //     checklist_ids: [3],
  //   };

  //   try {
  //     const { status, data } = await axiosInstance.post(
  //       URLS.USER_VERIFICATION,
  //       sendData
  //     );

  //     if (status === 200) {
  //       if (data?.status) {
  //         setApiMessage('success', data?.message);
  //         getOnboardingChecklist(UserId);
  //       } else {
  //         setApiMessage('error', data?.message);
  //       }
  //       setLoader(false);
  //     }
  //   } catch (error) {
  //     setLoader(false);
  //     setApiMessage('error', error?.response?.data?.message);
  //   }
  // };
  useEffect(() => {
    if (formHealthSafety && formHealthSafety?.length > 0) {
      const filesId = filterFilesId(formHealthSafety);
      setHealthSafeChecked(filesId);
    } else {
      setHealthSafeChecked([]);
    }
  }, [formHealthSafety]);
  const Total_filesId =
    formHealthSafety &&
    formHealthSafety?.length > 0 &&
    filterFiles(formHealthSafety);
  const isDisabled =
    Total_filesId && Total_filesId?.length > 0
      ? Total_filesId?.length !== healthSafechecked?.length
      : false;

  return (
    <>
      <Box className={!isUserScreen && 'page-container'}>
        <Formik
          innerRef={formikRef}
          initialValues={{
            hs_fname:
              hsDetails?.user_first_name && hsDetails?.user_last_name
                ? hsDetails?.user_first_name + ' ' + hsDetails?.user_last_name
                : hsDetails?.user_first_name
                  ? hsDetails?.user_first_name
                  : '',
            birthdate: hsDetails?.date_of_birth
              ? dayjs(hsDetails?.date_of_birth).format('YYYY-MM-DD')
              : '',
          }}
          enableReinitialize={true}
          validationSchema={Yup.object().shape({
            hs_fname: Yup.string().trim().required('This field is required'),
            birthdate: Yup.string().trim().required('This field is required'),
          })}
          onSubmit={async () => {
            OnboardingForms();
          }}
        >
          {({
            errors,
            touched,
            handleBlur,
            values,
            handleSubmit,
            handleChange,
            setFieldValue,
          }) => (
            <Form onSubmit={handleSubmit}>
              <Box
                className={
                  isUserScreen
                    ? 'right-to-work-page health-safety-sec'
                    : 'page-section right-to-work-page health-safety-sec'
                }
              >
                <Box className="display-grid">
                  <Box>
                    <CustomTextField
                      fullWidth
                      id="hs_fname"
                      name="hs_fname"
                      value={values?.hs_fname}
                      label="Full Name"
                      placeholder="Enter full name"
                      error={Boolean(touched.hs_fname && errors.hs_fname)}
                      helperText={touched.hs_fname && errors.hs_fname}
                      onBlur={handleBlur}
                      onChange={handleChange}
                      disabled={ViewAccessOnly}
                      required
                    />
                  </Box>
                  <Box>
                    <CustomDatePicker
                      label={<span>Date of Birth (DD/MM/YYYY)</span>}
                      required
                      disabled={ViewAccessOnly}
                      name="birthdate"
                      value={dayjs(values?.birthdate)}
                      error={Boolean(touched.birthdate && errors.birthdate)}
                      helperText={touched.birthdate && errors.birthdate}
                      onBlur={handleBlur}
                      onChange={(date) => {
                        setFieldValue('birthdate', date);
                      }}
                      disableFuture={true}
                      inputVariant="outlined"
                      format="DD/MM/YYYY"
                      minDate={dayjs().subtract(18, 'year')}
                    />
                  </Box>
                </Box>
                {/* {authState &&
                  !isMyProfile &&
                  !IsPending &&
                  (authState?.UserPermission?.staff === 2 ||
                    authState?.web_user_active_role_id === 19) && (
                    <Box className="">
                      <Typography className="title-text pt16 fw600 color-primary">
                        {`Reset ${user_full_name}'s Training :`}{' '}
                        <span
                          className="title-text fw600 cursor-pointer color-red"
                          onClick={() => UserReject()}
                        >
                          Click here
                        </span>
                      </Typography>
                    </Box>
                  )} */}

                {formHealthSafety &&
                  formHealthSafety?.length > 0 &&
                  formHealthSafety?.map((item) => {
                    return (
                      <>
                        <Typography className="title-sm fw600 pt16">
                          {item?.category_name}
                        </Typography>
                        <Box className="health-safety-checkbox-details">
                          {item?.children &&
                            item?.children?.length > 0 &&
                            item?.children?.map((data) => {
                              if (data?.category_type === 'folder') {
                                return (
                                  <>
                                    {CategoryHeading(
                                      data?.category_name,
                                      true,
                                      data
                                    )}
                                    {handleSubCate(data)}
                                  </>
                                );
                              }
                              return (
                                <FormGroup className="hf-checkbox pt16">
                                  <FormControlLabel
                                    control={
                                      <Checkbox
                                        className="check-box "
                                        icon={
                                          <CheckBoxOutlineBlankIcon className="uncheck-icon" />
                                        }
                                        checkedIcon={
                                          <CheckBoxIcon className="check-icon" />
                                        }
                                      />
                                    }
                                    className="check-box-text  title-text"
                                    name="sl_plan"
                                    checked={healthSafechecked?.includes(
                                      data?.id
                                    )}
                                    onChange={() => {
                                      // setFieldValue('sl_plan', item?.name);
                                      handleHealthSafe(data?.id);
                                    }}
                                    disabled={
                                      ViewAccessOnly ||
                                      item?.total_items !== item?.total_track
                                    }
                                    label={data?.category_name}
                                  />
                                </FormGroup>
                              );
                            })}
                        </Box>
                      </>
                    );
                  })}
                <Box className={isUserScreen ? 'pt16' : 'pt40'}>
                  <CustomButton
                    variant="contained"
                    className="red-button"
                    type="submit"
                    title={`${loader && !isDisabled ? 'Saving...' : 'Save'}`}
                    fullWidth={false}
                    disabled={ViewAccessOnly || loader || isDisabled}
                  />
                </Box>
              </Box>
            </Form>
          )}
        </Formik>
      </Box>
      <DialogBox
        open={toggleModal}
        handleClose={() => {
          setToggleModal(!toggleModal);
        }}
        title={dialogTitle ? dialogTitle : 'Introduction to health & safety'}
        className="media-view-dialogbox"
        content={
          <FileDetails
            CategoryDetails={CategoryDetailsOne}
            DocumentList={CategoryDetails}
            getDocumentCategory={getDocumentCategory}
            setCurrentHSIndex={setCurrentHSIndex}
            currentHSIndex={currentHSIndex}
            filesId={filesId}
            userMediaTrack={userMediaTrack}
          />
        }
      />
    </>
  );
}
