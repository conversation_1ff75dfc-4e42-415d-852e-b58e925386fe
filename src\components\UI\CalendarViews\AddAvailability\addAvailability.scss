.add-availability-container {
  min-width: 350px;
  .add-availability-header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
    padding: var(--spacing-md);
    .add-availability-header-wrap {
      color: var(--text-color-black);
      .add-availability-header-text {
        font-size: var(--font-size-md);
        line-height: var(--line-height-md);
        font-weight: var(--font-weight-semibold);
      }
      .add-availability-header-date {
        font-size: var(--font-size-sm);
        line-height: var(--line-height-sm);
      }
    }
    .add-availability-header-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 10px;
      .close-availability-tooltip {
        color: var(--icon-color-light-dark);
        height: var(--icon-size-md);
        width: var(--icon-size-md);
        cursor: pointer;
      }
    }
  }
  .add-availability-details-container {
    // padding: var(--spacing-md);
    .d-center-start {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: 10px;
    }
    // Availability Row
    .availability-row {
      padding: var(--spacing-md);
      width: 100%;
      .availability-event-details {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        gap: 10px;
        .availability-event {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          width: fit-content;
          cursor: pointer;
          gap: 5px;
          font-family: var(--font-family-primary);
          border-radius: var(--border-radius-xl);
          padding: var(--spacing-sm);
          border: var(--field-border);
          span {
            font-size: var(--font-size-sm);
            line-height: var(--line-height-sm);
            letter-spacing: var(--letter-spacing-wide);
          }
          svg {
            width: var(--icon-size-xs);
            height: var(--icon-size-xs);
          }
          &.onDay {
            background: var(--color-success-opacity);
            color: var(--color-success);
            border: var(--border-width-xs) var(--border-style-solid)
              var(--border-color-green);
          }
          &.offDay {
            background: var(--color-danger-opacity);
            color: var(--color-danger);
            border: var(--border-width-xs) var(--border-style-solid)
              var(--border-color-red);
          }
        }
      }
    }
    .availability-row-wrap {
      width: 100%;
      padding: var(--spacing-xs) var(--spacing-md);
    }
    .validation-messages {
      margin-left: var(--spacing-xxl);
      padding-left: var(--spacing-xxl);

      .field-error-msg {
        color: var(--text-color-danger);
        font-size: var(--font-size-xs);
        line-height: var(--line-height-xs);
        margin: var(--spacing-xxs) 0;
      }
    }
    // Shift Toggle
    .availability-toggle-row {
      padding: var(--spacing-md);
      .availability-toggle-container {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        gap: 10px;
        padding: var(--spacing-sm);
        border-radius: var(--border-radius-sm);
        &:hover {
          background: var(--color-light-gray);
        }
        .MuiFormControlLabel-root {
          margin-left: var(--spacing-none);
          margin-right: var(--spacing-none);
        }
        .availability-toggle-title {
          font-size: var(--font-size-base);
          line-height: var(--line-height-base);
          font-weight: var(--font-weight-regular);
        }
      }
    }
    .availability-start-end-time-container {
      max-height: 40vh;
      overflow: auto;
      .availability-start-end-time-wrapper {
        width: 100%;
        display: flex;
        align-items: center;
        gap: 5px;
        .availability-remove-time {
          width: 100%;
          display: flex;
          justify-content: right;
          align-items: center;
          cursor: pointer;
        }
      }
      .availability-custom-time-add {
        margin-left: var(--spacing-xxl);
        padding-left: var(--spacing-xxl);
        padding-bottom: var(--spacing-xxl);
        display: flex;
        justify-content: left;
        align-items: center;
        cursor: pointer;
      }
    }
    .dropdown-wrapper {
      width: 100px;
      .dropdown-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--spacing-sm);
        border-radius: var(--border-radius-sm);
        cursor: pointer;
        font-size: var(--font-size-base);
        font-weight: var(--font-weight-medium);
        &:hover {
          background: var(--color-light-gray);
        }
        .arrow {
          height: var(--icon-size-md);
          width: var(--icon-size-md);
          color: var(--icon-color-light-dark);
          margin-left: var(--spacing-xs);
        }
      }
    }
  }
  .icon {
    height: var(--icon-size-md);
    width: var(--icon-size-md);
    color: var(--icon-color-light-dark);
    svg {
      height: var(--icon-size-md);
      width: var(--icon-size-md);
    }
  }
  .error-message {
    color: var(--text-color-danger);
    font-size: var(--font-size-xs);
    line-height: var(--line-height-xs);
    margin-top: var(--spacing-xxs);
  }
  .error-message-row {
    display: flex;
    align-items: flex-start;
    margin: var(--spacing-xs) var(--spacing-none);
  }
}

// custom dropdown list
.availability-dropdown-popper {
  z-index: 1300;
  .dropdown-content {
    background: var(--color-white);
    border: var(--field-border);
    border-radius: var(--border-radius-sm);
    box-shadow: var(--box-shadow-xs);
    min-width: 70px;
    max-width: 70px;
    margin-top: var(--spacing-xs);

    ul {
      list-style: none;
      padding: var(--spacing-xs);
      margin: var(--spacing-none);
      max-height: 400px;
      overflow-y: auto;
      li {
        font-family: var(--font-family-primary);
        padding: var(--spacing-sm);
        cursor: pointer;
        font-size: var(--font-size-sm);
        border-radius: var(--border-radius-sm);
        margin-bottom: var(--spacing-xs);
        &:hover,
        &.selected {
          background: var(--color-primary);
          color: var(--text-color-white);
        }
      }
    }
  }
}
