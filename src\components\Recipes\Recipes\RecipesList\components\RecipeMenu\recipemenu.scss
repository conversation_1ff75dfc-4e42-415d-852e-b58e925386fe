.recipe-menu {
  position: relative;
  display: inline-block;

  &__trigger {
    padding: var(--spacing-xs);
    color: var(--text-color-slate-gray);
    border-radius: var(--border-radius-md);
    background: transparent;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      color: var(--color-primary);
      background-color: var(--color-primary-opacity);
    }
  }

  &__dropdown {
    position: absolute;
    width: 180px;
    background-color: var(--color-white);
    border: 1px solid var(--border-color-light-gray);
    border-radius: var(--border-radius-md);
    box-shadow: var(--box-shadow-xs);
    z-index: 1000;
    overflow: hidden;
    animation: slideDown 0.2s ease-out;

    // Default positioning (below trigger)
    top: 100%;
    margin-top: var(--spacing-xxs);

    &--right {
      right: 0;
    }

    &--left {
      left: 0;
    }

    // Positioning above trigger
    &--right-top {
      bottom: 100%;
      top: auto;
      margin-bottom: var(--spacing-xxs);
      margin-top: 0;
      right: 0;
    }

    &--left-top {
      bottom: 100%;
      top: auto;
      margin-bottom: var(--spacing-xxs);
      margin-top: 0;
      left: 0;
    }

    // Ensure dropdown is visible even if parent has overflow hidden
    &--right,
    &--right-top {
      right: 0;
      transform: translateX(0);
    }

    &--left,
    &--left-top {
      left: 0;
      transform: translateX(0);
    }
  }

  &__item {
    width: 100%;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--text-color-slate-gray);
    background: transparent;
    border: none;
    cursor: pointer;
    text-align: left;

    &:hover {
      background-color: var(--color-primary-opacity);
      color: var(--color-primary);
    }

    &--danger {
      color: var(--color-danger);

      &:hover {
        background-color: var(--color-danger-opacity);
        color: var(--color-danger);
      }
    }

    &:first-child {
      border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
    }

    &:last-child {
      border-radius: 0 0 var(--border-radius-md) var(--border-radius-md);
    }
  }
}

// Animations
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
