'use client';

import React from 'react';
import {
  Box,
  Typography,
  InputAdornment,
  Checkbox,
  FormControlLabel,
  FormGroup,
  Tooltip,
} from '@mui/material';
import { TotalOfExpenseObj } from '@/helper/common/commonFunctions';
import CustomTextField from '@/components/UI/CustomTextField';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import InfoIcon from '@mui/icons-material/Info';

export default function ExpenseCategory({
  setIsRemarkView,
  expenseData,
  IsRemarkView,
  setExpenseData,
  setRandom,
  currency,
  // isEdit,
}) {
  return (
    <Box>
      {' '}
      {expenseData && expenseData?.length > 0 && (
        <>
          <FormGroup className="form-checkbox pt16">
            <FormControlLabel
              control={
                <Checkbox
                  className="check-box "
                  icon={<CheckBoxOutlineBlankIcon className="uncheck-icon" />}
                  checkedIcon={<CheckBoxIcon className="check-icon" />}
                  disableRipple
                />
              }
              name="weekly"
              className="check-box-form form-row max-content sub-title-text"
              checked={IsRemarkView}
              onChange={(e) => {
                setIsRemarkView(e.target.checked);
              }}
              label="View Remark"
            />
          </FormGroup>
          <Box className="dsr-add-view pt16 dsr-request-view">
            {expenseData &&
              expenseData?.length > 0 &&
              expenseData?.map((item, dindex) => {
                return (
                  <Box className="dsr-main-category income">
                    <Typography className="body-text fw600 text-capital main-category-text expense-text">
                      {item?.payment_type_title}
                    </Typography>
                    <span className="category-status income">
                      <span className="sub-title-text category-draft  fw600 text-capital">
                        {' '}
                        Expense
                      </span>
                    </span>
                    {item?.payment_type_category?.length > 0 &&
                      item?.payment_type_category?.map((citem) => {
                        return (
                          <Box className="">
                            {' '}
                            {citem?.payment_type_category_pattern ===
                              'multiple' && (
                              <Box className="d-flex align-center gap-5 pt16">
                                <Typography className="title-text fw600 text-capital">
                                  {citem?.payment_type_category_title}
                                </Typography>
                                {citem?.payment_type_category_remarks &&
                                citem?.payment_type_category_remarks?.trim() !==
                                  '' ? (
                                  <Tooltip
                                    arrow
                                    title={citem?.payment_type_category_remarks}
                                    classes={{
                                      tooltip: 'info-tooltip-container ',
                                    }}
                                  >
                                    <InfoIcon className="info-icon-wrap cursor-pointer" />
                                  </Tooltip>
                                ) : (
                                  <></>
                                )}
                              </Box>
                            )}
                            {citem?.payment_type_category_pattern ===
                            'multiple' ? (
                              <>
                                {citem?.categoryBranchValue &&
                                  citem?.categoryBranchValue?.length > 0 &&
                                  citem?.categoryBranchValue?.map((csitem) => {
                                    return (
                                      <>
                                        <Box>
                                          <Typography className="title-text">
                                            {csitem?.first_field_value}
                                          </Typography>
                                        </Box>
                                        <Box className="card-details-section card-request-sec">
                                          <Box className="old-value">
                                            {(csitem?.old_expense_amount ||
                                              csitem?.old_expense_amount ===
                                                0) &&
                                            currency?.symbol ? (
                                              <Typography className="title-text">
                                                {item?.has_field_currency ? (
                                                  <span className="symbol">
                                                    {currency?.symbol}
                                                  </span>
                                                ) : (
                                                  <></>
                                                )}
                                                <span>
                                                  {csitem?.old_expense_amount}
                                                </span>
                                              </Typography>
                                            ) : csitem?.old_expense_amount ||
                                              csitem?.old_expense_amount ===
                                                0 ? (
                                              <Typography className="title-text">
                                                {item?.has_field_currency ? (
                                                  <span className="symbol">
                                                    £
                                                  </span>
                                                ) : (
                                                  <></>
                                                )}
                                                <span>
                                                  {csitem?.old_expense_amount}
                                                </span>
                                              </Typography>
                                            ) : csitem?.expense_amount ||
                                              csitem?.expense_amount === 0 ? (
                                              <>
                                                {item?.has_field_currency ? (
                                                  <span className="symbol">
                                                    {currency?.symbol
                                                      ? currency?.symbol
                                                      : '£'}
                                                  </span>
                                                ) : (
                                                  <></>
                                                )}
                                                <span>
                                                  {' '}
                                                  {csitem?.expense_amount}
                                                </span>
                                              </>
                                            ) : (
                                              <></>
                                            )}
                                            {(csitem?.old_expense_amount ||
                                              csitem?.expense_amount ||
                                              csitem?.old_expense_amount ===
                                                0) &&
                                              (csitem?.old_expense_amount ||
                                                csitem?.old_expense_amount ===
                                                  0) &&
                                              csitem?.old_expense_amount !==
                                                csitem?.expense_amount && (
                                                <Typography className="sub-title-text">
                                                  Old
                                                </Typography>
                                              )}
                                          </Box>
                                          <Box className="amount-field">
                                            {(csitem?.old_expense_amount ||
                                              csitem?.old_expense_amount ===
                                                0) &&
                                              csitem?.old_expense_amount !==
                                                csitem?.expense_amount && (
                                                <Box className="new-value">
                                                  {(csitem?.expense_amount ||
                                                    csitem?.expense_amount ===
                                                      0) &&
                                                  currency?.symbol ? (
                                                    <Typography className="title-text">
                                                      {item?.has_field_currency ? (
                                                        <span className="symbol">
                                                          {currency?.symbol}
                                                        </span>
                                                      ) : (
                                                        <></>
                                                      )}
                                                      <span>
                                                        {csitem?.expense_amount}
                                                      </span>
                                                    </Typography>
                                                  ) : csitem?.expense_amount ||
                                                    csitem?.expense_amount ===
                                                      0 ? (
                                                    <Typography className="title-text">
                                                      {item?.has_field_currency ? (
                                                        <span className="symbol">
                                                          £
                                                        </span>
                                                      ) : (
                                                        <></>
                                                      )}
                                                      <span>
                                                        {csitem?.expense_amount}
                                                      </span>
                                                    </Typography>
                                                  ) : (
                                                    <></>
                                                  )}
                                                </Box>
                                              )}

                                            <Typography className="sub-title-text new">
                                              {' '}
                                              New{' '}
                                            </Typography>
                                          </Box>
                                        </Box>
                                      </>
                                    );
                                  })}
                              </>
                            ) : (
                              <>
                                <Box className="d-flex align-center gap-5">
                                  <Typography className="title-text fw600 text-capital">
                                    {citem?.payment_type_category_title}
                                  </Typography>
                                  {citem?.payment_type_category_remarks &&
                                  citem?.payment_type_category_remarks?.trim() !==
                                    '' ? (
                                    <Tooltip
                                      arrow
                                      title={
                                        citem?.payment_type_category_remarks
                                      }
                                      classes={{
                                        tooltip: 'info-tooltip-container ',
                                      }}
                                    >
                                      <InfoIcon className="info-icon-wrap cursor-pointer" />
                                    </Tooltip>
                                  ) : (
                                    <></>
                                  )}
                                </Box>
                                <Box className="card-details-section card-request-sec">
                                  <Box className="old-value">
                                    {(citem?.old_expense_amount ||
                                      citem?.old_expense_amount === 0) &&
                                    currency?.symbol ? (
                                      <Typography className="title-text">
                                        {item?.has_field_currency ? (
                                          <span className="symbol">
                                            {currency?.symbol}
                                          </span>
                                        ) : (
                                          <></>
                                        )}
                                        <span>{citem?.old_expense_amount}</span>
                                      </Typography>
                                    ) : citem?.old_expense_amount ||
                                      citem?.old_expense_amount === 0 ? (
                                      <Typography className="title-text">
                                        {item?.has_field_currency ? (
                                          <span className="symbol">£</span>
                                        ) : (
                                          <></>
                                        )}
                                        <span>{citem?.old_expense_amount}</span>
                                      </Typography>
                                    ) : citem?.expense_amount ||
                                      citem?.expense_amount === 0 ? (
                                      <>
                                        {item?.has_field_currency ? (
                                          <span className="symbol">
                                            {currency?.symbol
                                              ? currency?.symbol
                                              : '£'}
                                          </span>
                                        ) : (
                                          <></>
                                        )}
                                        <span>{citem?.expense_amount}</span>
                                      </>
                                    ) : (
                                      <></>
                                    )}
                                    {(citem?.old_expense_amount ||
                                      citem?.expense_amount ||
                                      citem?.old_expense_amount === 0) &&
                                      (citem?.old_expense_amount ||
                                        citem?.old_expense_amount === 0) &&
                                      citem?.expense_amount !==
                                        citem?.old_expense_amount && (
                                        <Typography className="sub-title-text">
                                          Old
                                        </Typography>
                                      )}
                                  </Box>
                                  <Box className="amount-field">
                                    {(citem?.old_expense_amount ||
                                      citem?.old_expense_amount === 0) &&
                                      citem?.expense_amount !==
                                        citem?.old_expense_amount && (
                                        <Box className="new-value">
                                          {(citem?.expense_amount ||
                                            citem?.expense_amount === 0) &&
                                          currency?.symbol ? (
                                            <Typography className="title-text">
                                              {item?.has_field_currency ? (
                                                <span className="symbol">
                                                  {currency?.symbol}
                                                </span>
                                              ) : (
                                                <></>
                                              )}
                                              <span>
                                                {citem?.expense_amount}
                                              </span>
                                            </Typography>
                                          ) : citem?.expense_amount ||
                                            citem?.expense_amount === 0 ? (
                                            <Typography className="title-text">
                                              {item?.has_field_currency ? (
                                                <span className="symbol">
                                                  £
                                                </span>
                                              ) : (
                                                <></>
                                              )}
                                              <span>
                                                {citem?.expense_amount}
                                              </span>
                                            </Typography>
                                          ) : (
                                            <></>
                                          )}
                                          {(citem?.expense_amount ||
                                            citem?.expense_amount === 0) && (
                                            <Typography className="sub-title-text new">
                                              {' '}
                                              New{' '}
                                            </Typography>
                                          )}
                                        </Box>
                                      )}
                                  </Box>
                                </Box>
                              </>
                            )}
                          </Box>
                        );
                      })}
                    <>
                      <Box className="card-details-section pt16 totol-dsr">
                        <Box>
                          <Typography className="title-text fw600">
                            Total
                          </Typography>
                        </Box>
                        <Box className="amount-field">
                          <CustomTextField
                            value={TotalOfExpenseObj(expenseData, dindex)}
                            name={`amount `}
                            disabled={true}
                            placeholder={
                              item?.has_field_currency
                                ? 'Amount'
                                : 'Enter value'
                            }
                            className={'amount-textfield'}
                            InputProps={{
                              ...(item?.has_field_currency && {
                                startAdornment: (
                                  <InputAdornment position="start">
                                    <Typography className="title-text currency">
                                      {currency?.symbol
                                        ? currency?.symbol
                                        : '£'}
                                    </Typography>{' '}
                                  </InputAdornment>
                                ),
                              }),
                            }}
                          />
                        </Box>
                      </Box>
                      {IsRemarkView && (
                        <Box className="card-details-section  card-details-remark-section pt16">
                          <Box>
                            <Typography className="title-text fw600">
                              Remark
                            </Typography>
                          </Box>
                          <Box className="amount-field">
                            <CustomTextField
                              value={item?.payment_type_remark}
                              onChange={(e) => {
                                if (
                                  e.target.value === '' ||
                                  e.target.value?.length < 161
                                ) {
                                  const income = expenseData;
                                  income[dindex].payment_type_remark =
                                    e.target.value;
                                  setExpenseData(income);
                                  setRandom(Math.random());
                                }
                              }}
                              onPaste={(e) => {
                                if (
                                  e.target.value === '' ||
                                  e.target.value?.length < 161
                                ) {
                                  setIsSubmit(false);
                                  const income = DsrDataIncome;
                                  income[dindex].payment_type_remark =
                                    e.target.value;
                                  setDsrDataIncome(income);
                                  setRandom(Math.random());
                                }
                              }}
                              multiline
                              rows={2}
                              disabled={true}
                              name={`amount ${dindex}`}
                              placeholder={'Enter value'}
                              className={
                                'amount-textfield additional-textfeild'
                              }
                            />
                          </Box>
                          <Typography className="sub-title-text text-align-end">
                            {(item?.payment_type_remark?.length
                              ? item?.payment_type_remark?.length
                              : 0) + ' / 160'}
                          </Typography>
                        </Box>
                      )}
                    </>
                  </Box>
                );
              })}
          </Box>
        </>
      )}
    </Box>
  );
}
