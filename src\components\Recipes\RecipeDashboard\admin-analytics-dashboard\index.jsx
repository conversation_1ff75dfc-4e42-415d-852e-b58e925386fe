'use client';

import React, { useState, useEffect } from 'react';
import { Box, Tooltip, Typography, Popover } from '@mui/material';
// import Icon from '@/components/UI/AppIcon/AppIcon';
import CustomButton from '@/components/UI/CustomButton';
import RefreshIcon from '@mui/icons-material/Refresh';
import ContentLoader from '@/components/UI/ContentLoader';
// Note: Header and Sidebar components can be added when needed
import StatisticsTile from './components/StatisticsTile';
import ChartWidget from './components/ChartWidget';
import QuickActionPanel from './components/QuickActionPanel';
import RecentActivity from './components/RecentActivity';
import RecipeAnalyticsDashboard from './components/PublicRecipeDashboard';

import {
  getPublicAnalyticsOverview,
  exportRecipeDashboard,
} from '@/services/recipeService';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { recipeMenuList } from '@/helper/common/commonMenus';
import DateRangePicker from '@/components/UI/DateRangePicker/DateRangePicker';
// import { getModulePermissionsObject } from '@/helper/common/roleUtils';
// import { MODULE_SLUGS } from '@/helper/common/roleConstants';
// import AuthContext from '@/helper/authcontext';
import './adminanalyticsdashboard.scss';

const AdminAnalyticsDashboard = () => {
  // const theme = useTheme();
  // const isMobile = useMediaQuery(theme.breakpoints.down(1500));
  // const { authState } = useContext(AuthContext);
  const [selectedDateRange, setSelectedDateRange] = useState('last_30_days');
  const [realTimeData, setRealTimeData] = useState({});
  const [showPublicDashboard, setShowPublicDashboard] = useState(false);

  // API Data State
  const [isLoading, setIsLoading] = useState(true);
  const [apiData, setApiData] = useState(null);
  const [statisticsData, setStatisticsData] = useState([]);
  const [chartWidgets, setChartWidgets] = useState([]);
  const [quickActions, setQuickActions] = useState([]);
  const [recentActivityData, setRecentActivityData] = useState([]);

  // Export popover state
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);
  const id = open ? 'export-popover' : undefined;
  // const recipeDashboardPermissions = getModulePermissionsObject(
  //   authState,
  //   MODULE_SLUGS.RECIPE_DASHBOARD
  // );

  // Helper function to generate sparkline data based on current value
  const generateSparklineData = (currentValue, trend = 'up') => {
    const baseValue = parseInt(currentValue) || 0;

    // If there's no meaningful data (0 or very small values), return flat line in middle
    if (baseValue === 0 || baseValue < 1) {
      return Array(10).fill(50); // Middle value instead of 0
    }

    const variation = Math.max(baseValue * 0.1, 5); // 10% variation or minimum 5
    const data = [];

    for (let i = 0; i < 10; i++) {
      const randomVariation = (Math.random() - 0.5) * variation;
      const trendFactor =
        trend === 'up' ? i * 0.5 : trend === 'down' ? -i * 0.5 : 0;
      data.push(Math.max(0, baseValue + randomVariation + trendFactor));
    }

    return data;
  };

  // Transform API stats data to component format
  const transformStatsData = (apiStats) => {
    const statsArray = [
      {
        id: 'total-recipes',
        title: 'Total Recipes',
        value: apiStats?.totalRecipes?.toString() || '0',
        sparklineData: generateSparklineData(apiStats?.totalRecipes, 'up'),
        icon: 'ChefHat',
        color: 'primary',
        description: 'Recipes across all categories',
      },
      {
        id: 'popular-categories',
        title: 'Top Category',
        value: apiStats?.topCategory?.name || 'N/A',
        sparklineData: generateSparklineData(
          apiStats?.topCategory?.count,
          'up'
        ),
        icon: 'TrendingUp',
        color: 'accent',
        description: `${apiStats?.topCategory?.count || 0} times used`,
      },
    ];

    // Add highest impression recipe if available
    if (apiStats?.highestImpressionRecipe) {
      statsArray.push({
        id: 'highest-impression',
        title: 'Top Recipe',
        value: apiStats?.highestImpressionRecipe?.name || 'N/A',
        sparklineData: generateSparklineData(
          apiStats?.highestImpressionRecipe?.impressions,
          'up'
        ),
        icon: 'Star',
        color: 'warning',
        description: `${(apiStats?.highestImpressionRecipe?.impressions || 0).toLocaleString()} views`,
      });
    } else {
      // Fallback to engagement rate if highestImpressionRecipe not available
      statsArray.push({
        id: 'engagement-rate',
        title: 'Engagement',
        value: `${apiStats?.engagementRate || 0}%`,
        sparklineData: generateSparklineData(apiStats?.engagementRate, 'up'),
        icon: 'Heart',
        color: 'error',
        description: 'User engagement rate',
      });
    }

    return statsArray;
  };

  // Transform analytics data to additional statistics
  const transformAnalyticsData = (apiAnalytics) => {
    if (!apiAnalytics) return [];

    return [
      {
        id: 'total-views',
        title: 'Total Views',
        value: (apiAnalytics?.totalViews || 0).toLocaleString(),
        sparklineData: generateSparklineData(apiAnalytics?.totalViews, 'up'),
        icon: 'Eye',
        color: 'primary',
        description: 'Total recipe views',
      },
      {
        id: 'total-bookmarks',
        title: 'Bookmarks',
        value: (apiAnalytics?.totalBookmarks || 0).toLocaleString(),
        sparklineData: generateSparklineData(
          apiAnalytics?.totalBookmarks,
          'up'
        ),
        icon: 'Bookmark',
        color: 'accent',
        description: 'Recipe bookmarks',
      },
      {
        id: 'contact-submissions',
        title: 'Contact Forms',
        value: (apiAnalytics?.totalContactSubmissions || 0).toLocaleString(),
        sparklineData: generateSparklineData(
          apiAnalytics?.totalContactSubmissions,
          'up'
        ),
        icon: 'MessageSquare',
        color: 'warning',
        description: 'Contact form submissions',
      },
    ];
  };

  // Transform API chart data to component format

  const transformChartData = (apiCharts, recentActivity) => {
    const widgets = [];

    // Recipe Views Trend Chart - Use API data first, then fallback to recent activity
    let recipeViewsData = [];

    // First check if API has recipeViewsTrend data
    if (
      apiCharts?.recipeViewsTrend &&
      Array.isArray(apiCharts?.recipeViewsTrend)
    ) {
      recipeViewsData = apiCharts?.recipeViewsTrend?.map((item) => {
        // Handle both old format (recipe_names array) and new format (recipe_name string)
        let recipeDisplay = 'No recipes';
        let fullRecipeName = 'No recipes';

        if (item?.recipe_name) {
          // New format: single recipe_name field
          fullRecipeName = item?.recipe_name;
          recipeDisplay =
            item?.recipe_name?.length > 15
              ? item?.recipe_name?.substring(0, 15) + '...'
              : item?.recipe_name;
        } else if (item?.recipe_names && Array.isArray(item?.recipe_names)) {
          // Old format: recipe_names array
          const recipeNames = item?.recipe_names;
          if (recipeNames?.length > 0) {
            // Full name includes all recipe names
            fullRecipeName = recipeNames?.join(', ');

            // Start with the first recipe name
            recipeDisplay = recipeNames?.[0];

            // If there are more recipes and space allows, add them
            for (let i = 1; i < recipeNames?.length && i < 2; i++) {
              const newDisplay = `${recipeDisplay}, ${recipeNames?.[i]}`;
              // Limit total length to prevent text cutoff
              if (newDisplay?.length <= 25) {
                recipeDisplay = newDisplay;
              } else {
                recipeDisplay += '...';
                break;
              }
            }

            // If we have more than 2 recipes and didn't add "..." yet
            if (recipeNames?.length > 2 && !recipeDisplay?.includes('...')) {
              recipeDisplay += '...';
            }
          }
        }

        return {
          name: recipeDisplay,
          fullName: fullRecipeName, // Preserve full name for tooltip
          views: item?.views || 0,
          // Remove likes as it's not in API response
        };
      });
    } else if (recentActivity && Array.isArray(recentActivity)) {
      // Fallback: Extract from recent activity
      const recipeViewsMap = {};

      recentActivity?.forEach((activity) => {
        if (activity?.event_type === 'recipe_view') {
          let metadata = {};
          try {
            metadata = activity?.metadata ? JSON.parse(activity?.metadata) : {};
          } catch (e) {
            console.error('Failed to parse metadata:', e);
          }

          const fullRecipeName =
            activity?.recipe_name ||
            metadata?.recipe_name ||
            `Recipe ${activity?.entity_id || activity?.recipe_id}`;

          const recipeName =
            fullRecipeName?.length > 15
              ? fullRecipeName?.substring(0, 15) + '...'
              : fullRecipeName;

          if (!recipeViewsMap?.[recipeName]) {
            recipeViewsMap[recipeName] = {
              name: recipeName,
              fullName: fullRecipeName, // Preserve full name for tooltip
              views: 0,
            };
          }

          recipeViewsMap[recipeName].views += 1;
        }
      });

      // Convert to array and sort by views (descending)
      recipeViewsData = Object.values(recipeViewsMap)
        .sort((a, b) => b.views - a.views)
        .slice(0, 10); // Show top 10 recipes
    }

    // Always add Recipe Views Trend widget (even if no data for "No data" message)
    widgets.push({
      id: 'recipe-views',
      title: 'Recipe Views Trend',
      type: 'line',
      data: recipeViewsData,
    });

    // Category Performance Chart - Always show (even if no data)
    let categoryData = [];

    if (
      apiCharts?.categoryPerformance &&
      Array.isArray(apiCharts?.categoryPerformance)
    ) {
      categoryData = apiCharts?.categoryPerformance?.map((item) => {
        const fullCategoryName =
          item?.category_name || item?.category || 'Unknown'; // Handle both new (category_name) and old (category) formats
        const categoryName =
          fullCategoryName?.length > 15
            ? fullCategoryName?.substring(0, 15) + '...'
            : fullCategoryName;

        return {
          name: categoryName,
          fullName: fullCategoryName, // Preserve full name for tooltip
          recipes: item?.recipe_count || item?.recipes || 0, // Handle both new (recipe_count) and old (recipes) formats
          views: item?.views || 0,
          // Remove likes as it's not in API response
        };
      });
    }

    widgets.push({
      id: 'category-performance',
      title: 'Category Performance',
      type: 'bar',
      data: categoryData,
    });

    // User Engagement Heatmap - Hidden for now
    // if (apiCharts?.userEngagementHeatmap) {
    //   const heatmapData = apiCharts.userEngagementHeatmap.map((item) => ({
    //     hour: `${item.hour}:00`,
    //     day:
    //       ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][item.day_of_week || item.day] ||
    //       'Unknown',
    //     value: item.activity_count || item.count || 0,
    //   }));

    //   widgets.push({
    //     id: 'user-engagement',
    //     title: 'User Engagement Heatmap',
    //     type: 'heatmap',
    //     data: heatmapData,
    //   });
    // }

    // Conversion Funnel - Always show (even if no data)
    let funnelData = [];
    if (
      apiCharts?.conversionFunnel &&
      Array.isArray(apiCharts?.conversionFunnel)
    ) {
      funnelData = apiCharts?.conversionFunnel?.map((item) => ({
        stage: item?.stage || item?.step || 'Unknown', // Handle both new (stage) and old (step) formats
        count: item?.count || 0,
        percentage: item?.conversion_rate || 0, // Use 'conversion_rate' from API instead of 'percentage'
      }));
    }

    widgets.push({
      id: 'conversion-analytics',
      title: 'Conversion Analytics',
      type: 'funnel',
      data: funnelData,
    });

    return widgets;
  };

  // Generate quick actions based on recipeMenuList from commonMenus
  const generateQuickActions = () => {
    // Map recipeMenuList to QuickActionPanel format, excluding Dashboard
    return (
      recipeMenuList
        ?.filter((menuItem) => menuItem?.name !== 'Dashboard') // Exclude Dashboard
        ?.map((menuItem) => {
          // Map icons from Material-UI components to string names for QuickActionPanel
          const iconMap = {
            BarChartIcon: 'BarChart',
            ChecklistRtlIcon: 'ListChecks',
            MenuBookIcon: 'BookOpen',
            GppMaybeIcon: 'ShieldAlert',
            IngredientcategoryIcon: 'Leaf',
            RecipeCategoryIcon: 'ChefHat',
            SettingsIcon: 'Settings',
          };

          // Extract icon name from the component
          let iconName = 'Settings'; // default fallback
          if (menuItem?.icon?.type) {
            const iconType =
              menuItem?.icon?.type?.name ||
              menuItem?.icon?.type?.displayName ||
              menuItem?.icon?.type?.render?.name;
            iconName = iconMap[iconType] || 'Settings';
          }

          // Fallback based on menu item name if icon extraction fails
          if (iconName === 'Settings' && menuItem?.name) {
            const nameBasedIconMap = {
              'Ingredients Category': 'Package',
              'Recipe Category': 'ChefHat',
              Allergens: 'ShieldAlert',
              Ingredients: 'Leaf',
              Recipes: 'BookOpen',
              'Recipe Settings': 'Settings',
            };

            iconName = nameBasedIconMap[menuItem?.name] || 'Settings';
          }

          // Map colors based on menu item id (updated to exclude Dashboard)
          const colorMap = {
            2: 'primary', // Ingredients Category
            3: 'accent', // Recipe Category
            4: 'warning', // Allergens
            5: 'success', // Ingredients
            6: 'secondary', // Recipes
            7: 'error', // Recipe Settings
          };

          return {
            id:
              menuItem?.slug?.replace('/recipes/', '')?.replace('/', '') ||
              menuItem?.id?.toString(),
            moduleSlug: menuItem?.moduleSlug,
            title: menuItem?.name,
            // icon: iconName,
            icon: menuItem?.icon,
            color: colorMap[menuItem?.id] || 'primary',
            action: menuItem?.slug,
          };
        }) || []
    );
  };

  // Fetch analytics overview data
  const fetchAnalyticsOverview = async () => {
    try {
      setIsLoading(true);
      const filters = {
        date_range: selectedDateRange,
      };

      const response = await getPublicAnalyticsOverview(filters);
      setApiData(response);

      // Transform and set data
      if (response?.stats) {
        const transformedStats = transformStatsData(response?.stats);
        const transformedAnalytics = response?.analytics
          ? transformAnalyticsData(response?.analytics)
          : [];
        // Combine both statistics arrays
        setStatisticsData([...transformedStats, ...transformedAnalytics]);
      }

      if (response?.charts) {
        const transformedCharts = transformChartData(
          response?.charts,
          response?.recentActivity
        );
        setChartWidgets(transformedCharts);
      }

      if (response?.recentActivity) {
        // Pass raw API data directly without transformation
        setRecentActivityData(response?.recentActivity);
      }

      // Generate quick actions
      const generatedActions = generateQuickActions();
      setQuickActions(generatedActions);
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Simulate real-time data updates
  useEffect(() => {
    const interval = setInterval(() => {
      if (apiData?.stats) {
        setRealTimeData({
          timestamp: new Date().toLocaleTimeString(),
          activeUsers:
            (apiData?.stats?.activeUsers || 0) +
            Math.floor(Math.random() * 10) -
            5,
          newRecipes: Math.floor(Math.random() * 5),
          pendingReviews: Math.floor(Math.random() * 10) + 20,
        });
      }
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, [apiData]);

  // Fetch data on component mount and when date range changes
  useEffect(() => {
    fetchAnalyticsOverview();
  }, [selectedDateRange]);

  const handleStatisticClick = () => {
    // Navigate to detailed report
    // TODO: Implement navigation logic
  };

  const handleDateRangeChange = (range) => {
    setSelectedDateRange(range);
    // Data will be fetched automatically via useEffect
  };

  const handleRefresh = () => {
    // Refresh the page data
    fetchAnalyticsOverview();
  };

  // Open export popover
  // const handleExportClick = (event) => {
  //   setAnchorEl(event.currentTarget);
  // };

  // Close export popover
  const handleExportClose = () => {
    setAnchorEl(null);
  };

  // Handle export download
  const handleExportDownload = async (format) => {
    try {
      // Call the export API with current date range
      const exportFilters = {
        date_range: selectedDateRange,
      };

      const response = await exportRecipeDashboard(format, exportFilters);

      // Create blob and trigger download from API response
      const blob = response.data;
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // Determine file extension based on format
      const fileExtension = format === 'excel' ? 'xlsx' : 'csv';

      link.setAttribute(
        'download',
        `analytics_dashboard_${new Date().toISOString().split('T')[0]}.${fileExtension}`
      );
      document.body.appendChild(link);
      link.click();
      link.parentNode.removeChild(link);
      window.URL.revokeObjectURL(url);

      setApiMessage('success', 'Export completed successfully');
    } catch (error) {
      console.error('Export failed:', error);
      setApiMessage('error', 'Export failed. Please try again.');
    } finally {
      handleExportClose();
    }
  };

  // Handle list view click for Recipe Views Trend chart
  const handleListViewClick = (chartId) => {
    if (chartId === 'recipe-views') {
      setShowPublicDashboard(true);
    }
  };

  // If showing public dashboard, render it instead
  if (showPublicDashboard) {
    return (
      <>
        <div className="section-right-content">
          <RecipeAnalyticsDashboard
            setShowPublicDashboard={setShowPublicDashboard}
          />
        </div>
      </>
    );
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className="section-right-content">
        <div className="admin-analytics-dashboard">
          <main className="admin-analytics-dashboard__main">
            <div className="admin-analytics-dashboard__container">
              <ContentLoader />
            </div>
          </main>
        </div>
      </div>
    );
  }

  return (
    <div className="section-right-content">
      <div className="admin-analytics-dashboard">
        <main className="admin-analytics-dashboard__main">
          <div className="admin-analytics-dashboard__container">
            {/* Header Section */}
            <div className="admin-analytics-dashboard__header">
              <div className="admin-analytics-dashboard__header-content">
                <div className="admin-analytics-dashboard__header-info">
                  <h1 className="admin-analytics-dashboard__main-title">
                    Analytics Dashboard
                  </h1>
                  <p className="admin-analytics-dashboard__subtitle ">
                    Comprehensive insights into recipe performance and user
                    engagement
                  </p>
                  {realTimeData.timestamp && (
                    <div className="admin-analytics-dashboard__live-indicator">
                      <div className="admin-analytics-dashboard__live-dot"></div>
                      <span className="admin-analytics-dashboard__live-text">
                        Live data • Last updated {realTimeData.timestamp}
                      </span>
                    </div>
                  )}
                </div>

                <div className="admin-analytics-dashboard__header-actions">
                  <DateRangePicker
                    value={selectedDateRange}
                    onChange={handleDateRangeChange}
                  />
                  <div className="admin-analytics-dashboard__export-buttons">
                    <Box>
                      <Tooltip
                        title={
                          <Typography className="sub-title-text">
                            Refresh
                          </Typography>
                        }
                        arrow
                        classes={{
                          tooltip: 'info-tooltip-container',
                        }}
                      >
                        <span>
                          <CustomButton
                            variant="outlined"
                            isIconOnly
                            startIcon={<RefreshIcon />}
                            onClick={handleRefresh}
                          />
                        </span>
                      </Tooltip>
                    </Box>
                    {/* <Box>
                    <Tooltip
                      title={
                        <Typography className="sub-title-text">
                          Export
                        </Typography>
                      }
                      arrow
                      classes={{
                        tooltip: 'info-tooltip-container',
                      }}
                    >
                      <span>
                        <CustomButton
                          variant="outlined"
                          isIconOnly
                          startIcon={<Icon name="Download" size={16} />}
                          onClick={handleExportClick}
                        />
                      </span>
                    </Tooltip>
                  </Box> */}
                  </div>
                </div>
              </div>
            </div>

            {/* Statistics Tiles Grid */}
            <div className="admin-analytics-dashboard__stats-grid">
              {statisticsData.map((stat) => (
                <StatisticsTile
                  key={stat?.id}
                  {...stat}
                  onClick={() => handleStatisticClick(stat?.id)}
                  realTimeValue={realTimeData[stat?.id]}
                />
              ))}
            </div>

            {/* Chart Widgets Grid */}
            <div className="admin-analytics-dashboard__charts-grid">
              {chartWidgets.map((widget) => (
                <ChartWidget
                  key={widget?.id}
                  {...widget}
                  dateRange={selectedDateRange}
                  onListViewClick={handleListViewClick}
                />
              ))}
              {/* Recent Activity - positioned next to Conversion Analytics */}
              <RecentActivity activities={recentActivityData} />
            </div>

            {/* Bottom Section */}
            <div className="admin-analytics-dashboard__bottom-section">
              {/* Quick Action Panels */}
              <QuickActionPanel actions={quickActions} />
              {/* Empty placeholder to maintain grid layout */}
              <div className="admin-analytics-dashboard__placeholder"></div>
            </div>
          </div>
        </main>

        {/* Export Popover */}
        <Popover
          className="export-popover"
          id={id}
          open={open}
          anchorEl={anchorEl}
          onClose={handleExportClose}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'left',
          }}
        >
          <Box className="">
            <Typography
              className="title-text pb8 cursor-pointer"
              onClick={() => handleExportDownload('excel')}
            >
              Excel
            </Typography>
            <Typography
              className="title-text cursor-pointer"
              onClick={() => handleExportDownload('csv')}
            >
              CSV
            </Typography>
          </Box>
        </Popover>
      </div>
    </div>
  );
};

export default AdminAnalyticsDashboard;
