'use client';
// import CustomSelect from '@/components/UI/selectbox';
import { useContext, useEffect, useState } from 'react';
import CustomButton from '@/components/UI/CustomButton';
import KeyboardArrowLeftIcon from '@mui/icons-material/KeyboardArrowLeft';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import moment from 'moment';
import { DesktopDatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import axiosInstance from '@/helper/axios/axiosInstance';
import { setApiMessage } from '@/helper/common/commonFunctions';
import PreLoader from '@/components/UI/Loader';
import { ROTA_URLS, URLS } from '@/helper/constants/urls';
import AuthContext from '@/helper/authcontext';
import RotasCalendar from './RotasCalendar';
import TodayIcon from '@mui/icons-material/Today';
import { Box, Tooltip, Typography } from '@mui/material';
import updateLocale from 'dayjs/plugin/updateLocale';
import { useRouter, useSearchParams } from 'next/navigation';
import SearchOutlinedIcon from '@mui/icons-material/SearchOutlined';
import ArrowCircleUpIcon from '@mui/icons-material/ArrowCircleUp';
import {
  CopyMenu,
  PublishingMenu,
  ShortFilterMenu,
  ToolsMenu,
} from '@/components/UI/CalendarViews/ShiftMenu';
import axiosInstanceOrg from '@/helper/axios/axiosInstanceOrg';
import DialogBox from '@/components/UI/Modalbox';
import SearchModal from '../RotaMenuModal/SearchModal';
import CopyRangeModal from '../RotaMenuModal/CopyRangeModal';
import KeyboardShortcutModal from '../RotaMenuModal/KeyboardShortcutModal';
import DeleteModal from '@/components/UI/DeleteModal';
import EmployeesGroupingModal from '../RotaMenuModal/EmployeesGroupingModal';
import CustomSelect from '@/components/UI/CustomSelect';
import ContentLoader from '@/components/UI/ContentLoader';

dayjs.extend(updateLocale);

dayjs.updateLocale('en', {
  // Sunday = 0, Monday = 1.
  weekStart: 1,
});

const RotasFilter = () => {
  const navigate = useRouter();
  const searchParams = useSearchParams();
  const isViewType = searchParams.get('is_view_type');
  const isCurrentDate = searchParams.get('is_current_date');
  const { authState, AllListsData } = useContext(AuthContext);

  const viewAccessOnly = authState?.web_user_active_role_id
    ? authState.web_user_active_role_id === 1 ||
      authState.web_user_active_role_id === 7 ||
      authState.web_user_active_role_id === 14
    : '';

  const [filterData, setFilterData] = useState({
    currentDate: isCurrentDate
      ? isCurrentDate
      : moment(new Date()).format('YYYY-MM-DD'),
    location: '',
    dayMonth: isViewType || 'month',
    isGroupBy: '',
  });
  const [anchorEl, setAnchorEl] = useState(null);
  const [openDatePicker, setOpenDatePicker] = useState(false);
  const [isLoader, setIsLoader] = useState(false);
  const [loader, setLoader] = useState(false);
  const [staffListData, setStaffListData] = useState([]);
  const [shiftList, setShiftList] = useState([]);
  const [dayOffList, setDayOffList] = useState([]);
  const [locationList, setLocationList] = useState([]);
  const [rolesList, setRolesList] = useState([]);
  const [departmentList, setDepartmentRolesList] = useState([]);
  const [copyMenuToggle, setCopyMenuToggle] = useState(false);
  const [toolsMenuToggle, setToolsMenuToggle] = useState(false);
  const [shortFilterMenuToggle, setShortFilterMenuToggle] = useState(false);
  const [publishingMenuToggle, setPublishingMenuToggle] = useState(false);
  const [availabilityList, setAvailabilityList] = useState([]);
  const [shortFilterChanged, setShortFilterChanged] = useState({
    availability: false,
  });
  const [isUpdatedShift, setIsUpdatedShift] = useState('');
  const [isSearchModal, setIsSearchModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isCopyModal, setIsCopyModal] = useState({
    isCopyRangeModal: false,
  });
  const [isToolsModal, setIsToolsModal] = useState({
    isKeyboardModal: false,
    isClearShiftModal: false,
    isEmpGroupModal: false,
  });
  const [copyRangeStaffList, setCopyRangeStaffList] = useState([]);
  const [isSearchCollapse, setIsSearchCollapse] = useState('');
  const [userLeaveList, setUserLeaveList] = useState([]);

  var weekStart = moment(filterData?.currentDate || new Date())
    .clone()
    .startOf('isoWeek')
    .format('YYYY-MM-DD');
  var weekEnd = moment(filterData?.currentDate || new Date())
    .clone()
    .endOf('isoWeek')
    .format('YYYY-MM-DD');

  var firstDay = moment(filterData?.currentDate)
    .startOf('month')
    .format('YYYY-MM-DD');
  var lastDay = moment(filterData?.currentDate)
    .endOf('month')
    .format('YYYY-MM-DD');

  // user list
  const getStaffList = async (branchID) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstanceOrg.get(
        ROTA_URLS?.SHIFT_STAFF_LIST +
          `?isAdmin=false&branch_id=${branchID}&isRotaList=true`
      );

      if (status === 200) {
        setLoader(false);
        const uniqueStaffList = data?.userList?.filter(
          (item, index, self) =>
            index === self.findIndex((t) => t.id === item.id)
        );

        setStaffListData(uniqueStaffList);
        const copyRangeEmp = [...uniqueStaffList];
        // copyRangeEmp.unshift({
        //   id: 'open_shift',
        //   user_full_name: 'Open Shifts',
        // });
        setCopyRangeStaffList(copyRangeEmp);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setLoader(false);
      setStaffListData([]);
      setCopyRangeStaffList([]);
    }
  };

  // day off list
  const getDayOffList = async () => {
    setLoader(true);

    let filterList = '';

    if (filterData?.dayMonth === 'week') {
      filterList = `?from=${weekStart}&to=${weekEnd}`;
    } else if (filterData?.dayMonth === 'month') {
      filterList = `?from=${firstDay}&to=${lastDay}`;
    }

    try {
      const { status, data } = await axiosInstanceOrg.get(
        ROTA_URLS?.DAY_OFF_URL + filterList
      );

      if (status === 200) {
        setLoader(false);
        setDayOffList(data?.data);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setLoader(false);
      setDayOffList([]);
    }
  };

  // List of roles
  const getRoleList = async () => {
    setIsLoader(true);
    try {
      const { status, data } = await axiosInstance.get(URLS?.GET_ROLE_LIST);

      if (status === 200) {
        setIsLoader(false);

        const alloption = [{ label: 'No Role', value: 0 }];
        let filterUserList = data?.data?.map((user) => ({
          label: user?.role_name,
          value: user?.id,
        }));
        let mergeList = _.concat(alloption, filterUserList);

        setRolesList(mergeList);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setRolesList([]);
      setIsLoader(false);
    }
  };

  // List of rota shift
  const getRotaShiftList = async () => {
    let filterList = '';

    if (filterData?.dayMonth === 'week') {
      filterList = `?from=${weekStart}&to=${weekEnd}`;
    } else if (filterData?.dayMonth === 'month') {
      filterList = `?from=${firstDay}&to=${lastDay}`;
    }

    filterList += `&isAdmin=${viewAccessOnly}&branchId=${filterData?.location === '' ? locationList?.[0]?.value : filterData?.location}`;

    try {
      setIsLoader(true);

      const { status, data } = await axiosInstanceOrg.get(
        ROTA_URLS?.SHIFT_URL + filterList
      );
      if (status === 200) {
        setShiftList(data?.data.length > 0 ? data?.data : []);
        setFilterData({
          ...filterData,
          isGroupBy: data?.groupBy ?? '',
        });
        setIsLoader(false);
      }
    } catch (err) {
      setApiMessage('error', err?.response?.data?.message);
      setIsLoader(false);
    }
  };

  // List of user leave
  const getUserLeaveList = async () => {
    let filterList = '';

    if (filterData?.dayMonth === 'week') {
      filterList = `?start_date=${weekStart}&end_date=${weekEnd}`;
    } else if (filterData?.dayMonth === 'month') {
      filterList = `?start_date=${firstDay}&end_date=${lastDay}`;
    }

    filterList += `&list_type=staff`;

    try {
      setLoader(true);
      const { status, data } = await axiosInstance.get(
        URLS?.CALENDER_WISE_LEAVE + filterList
      );
      if (status === 200) {
        setLoader(false);

        const monthShiftEventsUpdate = data?.data?.calenderLeaves?.flatMap(
          (event) => {
            const startDate = moment(event?.start_date);
            const endDate = moment(event?.end_date);

            if (!endDate || startDate.isSame(endDate, 'day')) {
              return [
                {
                  ...event,
                  start: startDate.toDate(),
                  allDay: true,
                },
              ];
            }

            return Array.from(
              { length: endDate.diff(startDate, 'days') + 1 },
              (_, i) => ({
                ...event,
                start: startDate.clone().add(i, 'days').toDate(),
                allDay: true,
              })
            );
          }
        );

        setUserLeaveList(monthShiftEventsUpdate);
      }
    } catch (err) {
      setApiMessage('error', err?.response?.data?.message);
      setLoader(false);
    }
  };

  // List of availability
  const getAvailabilityList = async () => {
    let filterList = '';

    const firstDay = moment(filterData?.currentDate)
      .startOf('month')
      .format('YYYY-MM-DD');
    const lastDay = moment(filterData?.currentDate)
      .endOf('month')
      .format('YYYY-MM-DD');

    filterList = `?startDate=${firstDay}&endDate=${lastDay}`;

    filterList += `&isAdmin=true`;

    try {
      setLoader(true);
      const { status, data } = await axiosInstanceOrg.get(
        ROTA_URLS?.AVAILABILITY_URL + filterList
      );
      if (status === 200) {
        setLoader(false);

        const monthShiftEventsUpdate = data?.data?.map((event) => {
          return {
            ...event,
            start: event?.date,
            allDay: true,
          };
        });

        setAvailabilityList(monthShiftEventsUpdate);
      }
    } catch (err) {
      setApiMessage('error', err?.response?.data?.message);
      setLoader(false);
    }
  };

  useEffect(() => {
    if (shortFilterChanged?.availability) {
      getAvailabilityList();
    }
  }, [shortFilterChanged?.availability]);

  useEffect(() => {
    getRoleList();
  }, []);
  useEffect(() => {
    if (
      AllListsData?.ActiveBranchList &&
      AllListsData?.ActiveBranchList?.length > 0
    ) {
      const departmentFilterOption = [...AllListsData.ActiveDepartmentList];

      departmentFilterOption.unshift({
        label: 'No Department',
        value: 0,
      });

      setLocationList([...AllListsData.ActiveBranchList]);

      setDepartmentRolesList(departmentFilterOption);

      setFilterData({
        ...filterData,
        location: [...AllListsData.ActiveBranchList]?.[0]?.value,
      });
    }
  }, [AllListsData?.ActiveBranchList]);

  useEffect(() => {
    // setLoader(true);
    if (filterData?.location && viewAccessOnly !== '') {
      getDayOffList();
      getRotaShiftList();
      getUserLeaveList();
    }
  }, [
    filterData?.dayMonth,
    filterData?.currentDate,
    filterData?.location,
    viewAccessOnly,
  ]);
  useEffect(() => {
    if (filterData?.location && viewAccessOnly !== '') {
      getStaffList(filterData?.location);
    }
  }, [filterData?.location, viewAccessOnly]);

  const handlePrev = () => {
    setFilterData((prev) => {
      const updatedDate = moment(prev.currentDate || new Date())
        .subtract(1, prev.dayMonth === 'week' ? 'weeks' : 'months')
        .format('YYYY-MM-DD');

      const updatedFilterData = { ...prev, currentDate: updatedDate };

      // Ensure navigation happens after state update
      navigate.push(
        `/rotas?is_view_type=${updatedFilterData.dayMonth}&is_current_date=${updatedFilterData.currentDate}`
      );

      return updatedFilterData;
    });
  };

  const handleNext = () => {
    setFilterData((prev) => {
      const updatedDate = moment(prev.currentDate || new Date())
        .add(1, prev.dayMonth === 'week' ? 'weeks' : 'months')
        .format('YYYY-MM-DD');

      const updatedFilterData = { ...prev, currentDate: updatedDate };

      // Ensure navigation happens after state update
      navigate.push(
        `/rotas?is_view_type=${updatedFilterData.dayMonth}&is_current_date=${updatedFilterData.currentDate}`
      );

      return updatedFilterData;
    });
  };

  const handleDateClick = (selectedDate, isView) => {
    setFilterData({
      ...filterData,
      dayMonth: isView, // Switch to day view
      currentDate: moment(selectedDate).format('YYYY-MM-DD'), // Update date
    });

    navigate.push(
      `/rotas?is_view_type=${filterData?.dayMonth}&is_current_date=${moment(selectedDate).format('YYYY-MM-DD')}`
    );
  };

  var weekStartDate = moment(filterData.currentDate || new Date())
    .clone()
    .startOf('isoWeek');
  var weekEndDate = moment(filterData.currentDate || new Date())
    .clone()
    .endOf('isoWeek');

  const unpublishedShift = shiftList?.filter((item) => item?.isPublished === 0);

  // Update published/unpublished shift list
  useEffect(() => {
    setIsUpdatedShift('');
  }, [isUpdatedShift]);

  // handle publish and unpublished shift
  const handlePublishAndUnpublishedShift = async (isShiftStatus) => {
    const shiftIds = isShiftStatus
      ? unpublishedShift?.map((i) => i?.id)
      : shiftList?.map((i) => i?.id);

    const sendData = {
      ids: shiftIds,
      isPublished: isShiftStatus,
    };

    try {
      const { status, data } = await axiosInstanceOrg.put(
        ROTA_URLS?.SHIFT_PUBLISHE_UNPUBLISH,
        sendData
      );
      if (status === 200 || status === 201) {
        getRotaShiftList();
        setApiMessage('success', data?.message);
        setPublishingMenuToggle(false);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // Filter staffListOptions based on search
  const filteredStaffList = staffListData?.filter((emp) =>
    emp?.user_full_name?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // handle search scroll to staff
  const handleSearchToScroll = (staffId) => {
    setIsSearchCollapse(staffId);
    setTimeout(() => {
      const searchElement = document.getElementById(`search-staff-${staffId}`);
      if (searchElement) {
        searchElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }

      // Add highlight class
      searchElement?.classList?.add('highlight-row');

      // Remove it after 2 seconds
      setTimeout(() => {
        searchElement?.classList?.remove('highlight-row');
      }, 2000);
    }, 500);
    setSearchTerm('');
    setIsSearchModal(false);
  };

  // handle clear shift
  const handleClearShift = async () => {
    let sendData = '';
    if (filterData?.dayMonth === 'week') {
      sendData = { startTime: weekStart, endTime: weekEnd };
    } else if (filterData?.dayMonth === 'month') {
      sendData = { startTime: firstDay, endTime: lastDay };
    }

    try {
      const { status, data } = await axiosInstanceOrg.delete(
        ROTA_URLS?.CLEAR_SHIFT,
        { data: sendData }
      );
      if (status === 200 || status === 201) {
        setApiMessage('success', data?.message);
        getRotaShiftList();
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
    setIsToolsModal({ isClearShiftModal: false });
  };

  return (
    <div>
      {isLoader && <PreLoader />}
      <div className="dashboard-filter-section">
        <div className="filter-wrap">
          <div className="rota-filter-left-section">
            <div className="select-box location-select-field">
              <CustomSelect
                placeholder="All Branches"
                options={locationList}
                value={locationList?.find(
                  (item) => item?.value === filterData?.location
                )}
                onChange={(e) => {
                  setFilterData({
                    ...filterData,
                    location: e.value,
                  });
                }}
                menuPosition="fixed"
                isClearable={false}
              />
            </div>
            <div className="calender-filter">
              <CustomButton
                className={`calender-day-month-btn ${filterData?.dayMonth === 'week' ? 'active' : ''}`}
                variant="outlined"
                title="Week"
                onClick={() => {
                  setFilterData({
                    ...filterData,
                    dayMonth: 'week',
                  });
                  navigate.push(`/rotas?is_view_type=week`);
                }}
              />

              <CustomButton
                className={`calender-day-month-btn ${filterData?.dayMonth === 'month' ? 'active' : ''}`}
                variant="outlined"
                title="Month"
                onClick={() => {
                  setFilterData({
                    ...filterData,
                    dayMonth: 'month',
                  });
                  navigate.push(`/rotas?is_view_type=month`);
                }}
              />
            </div>
          </div>
          <div className="datepicker-filter">
            <CustomButton
              variant="outlined"
              isIconOnly
              startIcon={
                <Tooltip
                  title={
                    <Typography className="sub-title-text">
                      {filterData?.dayMonth === 'week'
                        ? 'Go to current week'
                        : 'Go to current month'}
                    </Typography>
                  }
                  classes={{
                    tooltip: 'info-tooltip-container',
                  }}
                  arrow
                >
                  <TodayIcon />
                </Tooltip>
              }
              onClick={() => {
                handleDateClick(new Date(), filterData?.dayMonth);
              }}
            />

            <div className="calendar-header">
              <KeyboardArrowLeftIcon
                className="cursor-pointer"
                onClick={handlePrev}
              />
              <div
                onClick={(event) => {
                  setAnchorEl(event.currentTarget);
                  setOpenDatePicker(true);
                }}
                className="cursor-pointer current-month-year-text"
              >
                {filterData.dayMonth === 'week'
                  ? weekStartDate.month() === weekEndDate.month()
                    ? weekStartDate.format('D') +
                      ' - ' +
                      weekEndDate.format('D MMM YYYY')
                    : weekStartDate.format('D MMM') +
                      ' - ' +
                      weekEndDate.format('D MMM YYYY')
                  : moment(filterData.currentDate || new Date()).format(
                      'MMMM YYYY'
                    )}
                <ExpandMoreIcon size="small" />
              </div>
              <KeyboardArrowRightIcon
                className="cursor-pointer"
                onClick={handleNext}
              />
            </div>
            <LocalizationProvider
              dateAdapter={AdapterDayjs}
              dateFormats={{ monthShort: 'MMMM' }}
            >
              <DesktopDatePicker
                open={openDatePicker}
                onClose={() => setOpenDatePicker(false)}
                // className="calendar-year-month-select-field"
                value={
                  filterData?.currentDate
                    ? dayjs(filterData?.currentDate, 'YYYY-MM-DD')
                    : null
                }
                onChange={(newValue) => {
                  if (newValue) {
                    setFilterData((prev) => ({
                      ...prev,
                      currentDate: dayjs(newValue).format('YYYY-MM-DD'),
                    }));
                    navigate.push(
                      `/rotas?is_view_type=${filterData?.dayMonth}&is_current_date=${dayjs(newValue).format('YYYY-MM-DD')}`
                    );
                  }
                  setOpenDatePicker(false);
                }}
                slotProps={{
                  textField: { style: { display: 'none' } }, // Hide input field
                  popper: {
                    anchorEl,
                    placement: 'bottom',
                    className: 'calendar-year-month-select-field',
                  },
                }}
                views={
                  filterData.dayMonth === 'week' ? ['day'] : ['month', 'year']
                }
                // displayWeekNumber
              />
            </LocalizationProvider>
          </div>
          <div className="rota-filter-right-section">
            <div
              className="filter-options-menu"
              onClick={() => setIsSearchModal(true)}
            >
              <Tooltip
                title={
                  <Typography className="sub-title-text">Search</Typography>
                }
                classes={{
                  tooltip: 'info-tooltip-container',
                }}
                placement="top"
                arrow
              >
                <SearchOutlinedIcon />
              </Tooltip>
            </div>

            {/* <CustomButton
              variant="outlined"
              isIconOnly
              startIcon={
                <Tooltip
                  title={
                    <Typography className="sub-title-text">Search</Typography>
                  }
                  classes={{
                    tooltip: 'info-tooltip-container',
                  }}
                  placement="top"
                  arrow
                >
                  <SearchOutlinedIcon />
                </Tooltip>
              }
              onClick={() => setIsSearchModal(true)}
            /> */}

            {viewAccessOnly && (
              <Tooltip
                title={<Typography className="sub-title-text">Copy</Typography>}
                placement="top"
                arrow
                classes={{
                  tooltip: 'info-tooltip-container',
                }}
              >
                <div>
                  <CopyMenu
                    isOpen={copyMenuToggle}
                    onOpen={() => setCopyMenuToggle(true)}
                    onClose={() => setCopyMenuToggle(false)}
                    isViewType={filterData?.dayMonth}
                    setIsCopyModal={setIsCopyModal}
                  />
                </div>
              </Tooltip>
            )}

            <Tooltip
              title={<Typography className="sub-title-text">Tools</Typography>}
              placement="top"
              arrow
              classes={{
                tooltip: 'info-tooltip-container',
              }}
            >
              <div>
                <ToolsMenu
                  isOpen={toolsMenuToggle}
                  onOpen={() => setToolsMenuToggle(true)}
                  onClose={() => setToolsMenuToggle(false)}
                  isViewType={filterData?.dayMonth}
                  setIsToolsModal={setIsToolsModal}
                  filterData={filterData}
                  setIsLoader={setIsLoader}
                  viewAccessOnly={viewAccessOnly}
                />
              </div>
            </Tooltip>

            <Tooltip
              title={
                <Typography className="sub-title-text">
                  Short & Filter
                </Typography>
              }
              placement="top"
              arrow
              classes={{
                tooltip: 'info-tooltip-container',
              }}
            >
              <div>
                <ShortFilterMenu
                  shift={shiftList}
                  isOpen={shortFilterMenuToggle}
                  onOpen={() => setShortFilterMenuToggle(true)}
                  onClose={() => setShortFilterMenuToggle(false)}
                  setShortFilterChanged={setShortFilterChanged}
                  shortFilterChanged={shortFilterChanged}
                  viewAccessOnly={viewAccessOnly}
                />
              </div>
            </Tooltip>

            <div
              className={`header-shift-publish-button ${unpublishedShift?.length === 0 ? 'shift-publish-button-disabled' : ''} `}
            >
              <div
                className="shift-publish-button"
                onClick={() => {
                  unpublishedShift?.length === 0
                    ? null
                    : handlePublishAndUnpublishedShift(true);
                }}
              >
                <ArrowCircleUpIcon />
                <p className="title-text shift-publish-text">
                  Publish{' '}
                  {unpublishedShift?.length !== 0
                    ? unpublishedShift?.length
                    : ''}
                </p>
              </div>
              <PublishingMenu
                isOpen={publishingMenuToggle}
                onOpen={() => setPublishingMenuToggle(true)}
                onClose={() => setPublishingMenuToggle(false)}
                isViewType={filterData?.dayMonth}
                shiftList={shiftList}
                unpublishedShift={unpublishedShift}
                handlePublishAndUnpublishedShift={
                  handlePublishAndUnpublishedShift
                }
              />
            </div>
          </div>
        </div>
      </div>
      <div className="dashboard-calendar-section">
        <div className="rota-calendar">
          {loader ? (
            <Box className="w100">
              <ContentLoader />
            </Box>
          ) : (
            <RotasCalendar
              filterData={filterData}
              staffListData={staffListData}
              shiftList={shiftList}
              rolesList={rolesList}
              departmentList={departmentList}
              locationList={locationList}
              getRotaShiftList={getRotaShiftList}
              dayOffList={dayOffList}
              setDayOffList={setDayOffList}
              viewAccessOnly={viewAccessOnly}
              availabilityList={availabilityList}
              setIsUpdatedShift={setIsUpdatedShift}
              isSearchCollapse={isSearchCollapse}
              userLeaveList={userLeaveList}
            />
          )}
        </div>
      </div>
      {/* Search modal */}
      {isSearchModal && (
        <DialogBox
          open={isSearchModal}
          handleClose={() => {
            setIsSearchModal(false);
            setIsSearchCollapse('');
          }}
          className="common-header-menu-modal"
          title="Search"
          content={
            <>
              <SearchModal
                searchTerm={searchTerm}
                setSearchTerm={setSearchTerm}
                filteredStaffList={filteredStaffList}
                handleSearchToScroll={handleSearchToScroll}
              />
            </>
          }
        />
      )}
      {/* Copy Range modal */}
      {isCopyModal?.isCopyRangeModal && (
        <DialogBox
          open={isCopyModal?.isCopyRangeModal}
          handleClose={() => {
            setIsCopyModal({ isCopyRangeModal: false });
          }}
          className="common-header-menu-modal custom-range-modal"
          title="Copy Custom Range"
          content={
            <>
              <CopyRangeModal
                copyRangeStaffList={copyRangeStaffList}
                setIsCopyModal={setIsCopyModal}
                getRotaShiftList={getRotaShiftList}
              />
            </>
          }
        />
      )}
      {/* Keyboard Shortcuts */}
      {isToolsModal?.isKeyboardModal && (
        <DialogBox
          open={isToolsModal?.isKeyboardModal}
          handleClose={() => {
            setIsToolsModal({ isKeyboardModal: false });
          }}
          className="common-header-menu-modal"
          title="Keyboard shortcuts"
          content={
            <>
              <KeyboardShortcutModal />
            </>
          }
        />
      )}
      {/* Clear Shift Confirmation modal */}
      {isToolsModal?.isClearShiftModal && (
        <DialogBox
          open={isToolsModal?.isClearShiftModal}
          handleClose={() => {
            setIsToolsModal({ isClearShiftModal: false });
          }}
          className="delete-modal"
          dividerClass="delete-modal-divider"
          title="Confirmation"
          content={
            <>
              <DeleteModal
                text={`Are you sure you wish to remove all shifts from the ${
                  filterData.dayMonth === 'week'
                    ? weekStartDate.month() === weekEndDate.month()
                      ? weekStartDate.format('D') +
                        ' - ' +
                        weekEndDate.format('D MMM YYYY')
                      : weekStartDate.format('D MMM') +
                        ' - ' +
                        weekEndDate.format('D MMM YYYY')
                    : moment(filterData.currentDate || new Date()).format(
                        'MMMM YYYY'
                      )
                } ${isViewType === 'week' ? 'week' : 'month'}?`}
                handleCancel={() =>
                  setIsToolsModal({ isClearShiftModal: false })
                }
                handleConfirm={() => {
                  handleClearShift();
                }}
                confirmText="Yes, Remove it"
              />
            </>
          }
        />
      )}
      {/* Employee Group modal */}
      {isToolsModal?.isEmpGroupModal && (
        <DialogBox
          open={isToolsModal?.isEmpGroupModal}
          handleClose={() => {
            setIsToolsModal({ isEmpGroupModal: false });
          }}
          className="common-header-menu-modal"
          title="Employee Grouping"
          content={
            <>
              <EmployeesGroupingModal
                setIsToolsModal={setIsToolsModal}
                getRotaShiftList={getRotaShiftList}
                setFilterData={setFilterData}
                filterData={filterData}
              />
            </>
          }
        />
      )}
    </div>
  );
};
export default RotasFilter;
