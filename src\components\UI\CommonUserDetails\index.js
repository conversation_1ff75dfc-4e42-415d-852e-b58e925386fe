import React from 'react';
import { Box, Typography } from '@mui/material';
import UserAvatar from '@/components/UI/Avatar/UserAvatar';
import VerifiedIcon from '@mui/icons-material/Verified';
import { useRouter } from 'next/navigation';
import { saveToStorage } from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';

const CommonUserDetails = ({
  userData,
  filterDataApplied,
  searchValue,
  page,
  rowsPerPage,
  setUserdata,
  authState,
  navigationProps = {},
  isRedirect = false,
  onClick,
}) => {
  const router = useRouter();

  const handleUserClick = () => {
    const userDataToStore = {
      id: userData?.id,
      filterData: filterDataApplied,
      searchValue: searchValue,
      page: page,
      rowperpage: rowsPerPage,
      ...navigationProps,
    };

    setUserdata(userDataToStore);
    saveToStorage(identifiers?.RedirectData, userDataToStore);

    if (userData?.id === authState?.id) {
      router.push('/myprofile');
    } else if (navigationProps?.IsAdmin) {
      router.push(`/user/${userData?.id}?IsAdmin=true`);
    } else {
      router.push(`/user/${userData?.id}`);
    }
  };

  return (
    <Box className="d-flex align-center justify-start h100 w100 user-avatar-details">
      <UserAvatar
        name={userData?.user_full_name}
        src={userData?.user_avatar_link}
        classname="list-user-icon"
      />
      <Box
        className="pl8 user-name-wrap"
        onClick={() => {
          if (isRedirect) {
            onClick();
          } else {
            handleUserClick();
          }
        }}
      >
        <Box className="d-flex align-center mw100">
          <Typography className="title-text fw600 cursor-pointer link-text text-ellipsis-line">
            {userData?.user_full_name}
          </Typography>
          {userData?.user_status === 'verified' && (
            <VerifiedIcon className="list-verified-icon" />
          )}
        </Box>
        <Typography className="sub-title-text cursor-pointer text-ellipsis-line">
          {userData?.user_email}
        </Typography>
      </Box>
    </Box>
  );
};

export default CommonUserDetails;
