'use client';
import React, { useEffect, useState, useContext } from 'react';
import {
  Box,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  IconButton,
  Tooltip,
  Typography,
} from '@mui/material';
import { useRouter } from 'next/navigation';
import CustomButton from '@/components/UI/button';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import { CustomTextField } from '@/components/UI/CommonField';
import SearchIcon from '@mui/icons-material/Search';
import AddIcon from '@mui/icons-material/Add';
import { ORG_URLS } from '@/helper/constants/urls';
import { formatDuration, setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import CustomPagination from '@/components/UI/pagination';
import AuthContext from '@/helper/authcontext';
import {
  saveToStorage,
  fetchFromStorage,
  removeFromStorage,
} from '@/helper/context';
import { identifiers } from '@/helper/constants/identifier';
import './subplan.scss';

export default function SubHistory() {
  const { authState, userdata, setUserdata } = useContext(AuthContext);
  const [search, setSearch] = useState('');
  const [planData, setPlanData] = useState([]);
  const [page, setPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  // const [loader, setLoader] = useState(false);
  const router = useRouter();

  const currency =
    authState?.currency_details && authState?.currency_details?.symbol
      ? authState?.currency_details?.symbol
      : '£';
  // Navigate to the edit subscription plan page.
  const handleEdit = (id) => {
    setUserdata({
      planEdit: true,
      page: page,
      rowsPerPage: rowsPerPage,
      search: search,
      isCreate: true,
    });
    saveToStorage(identifiers?.RedirectData, {
      planEdit: true,
      page: page,
      rowsPerPage: rowsPerPage,
      search: search,
      isCreate: true,
    });
    router.push(`/sorg/create-plan/${id}`);
  };
  // Fetch subscription plan details from the API.
  const getSubPlanDetails = async (search, page, Rpp) => {
    // setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        `${ORG_URLS.GET_ALL_SUBSCRIPTION_PLAN}?page=${page}&size=${
          Rpp ? Rpp : rowsPerPage
        }&searchString=${search}`
      );
      if (status === 200) {
        setPlanData(data?.data || []);
        setTotalCount(data?.count);
        removeFromStorage(identifiers?.RedirectData);
        setUserdata();
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // Delete the selected subscription plan.
  const handleDelete = async (planId) => {
    try {
      const { status, data } = await axiosInstance.put(
        `${ORG_URLS.UPDATE_SUBSCRIPTION_PLAN}/${planId}`,
        { subs_plan_status: 'deleted' }
      );

      if (status === 200) {
        if (data?.status) {
          setApiMessage('success', data?.message);
          getSubPlanDetails(search, page);
        } else {
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // Update the current page for pagination.
  const onPageChange = (newPage) => {
    setPage(newPage);
    getSubPlanDetails(search, newPage);
  };

  // Update the number of rows per page for pagination.
  const OnRowPerPage = (newRowsPerPage) => {
    setRowsPerPage(newRowsPerPage);
    setPage(1);
    getSubPlanDetails(search, 1, newRowsPerPage);
  };
  useEffect(() => {
    if (
      fetchFromStorage(identifiers?.RedirectData) &&
      fetchFromStorage(identifiers?.RedirectData)?.planEdit &&
      !fetchFromStorage(identifiers?.RedirectData)?.isCreate
    ) {
      const plandata = fetchFromStorage(identifiers?.RedirectData);
      setPage(plandata?.page);
      setRowsPerPage(plandata?.rowsPerPage);
      setSearch(plandata?.search);
      getSubPlanDetails(
        plandata?.search,
        plandata?.page,
        plandata?.rowsPerPage
      );
    } else if (userdata && userdata?.planEdit && !userdata?.isCreate) {
      setPage(userdata?.page);
      setRowsPerPage(userdata?.rowsPerPage);
      setSearch(userdata?.search);
      getSubPlanDetails(
        userdata?.search,
        userdata?.page,
        userdata?.rowsPerPage
      );
    } else if (
      !userdata?.isCreate ||
      !fetchFromStorage(identifiers?.RedirectData)?.isCreate
    ) {
      getSubPlanDetails('', page);
    }
  }, [
    fetchFromStorage(identifiers?.RedirectData)?.planEdit,
    userdata?.planEdit,
  ]);

  return (
    <Box className="sub-plan-wrap">
      <Box className="org-header">
        {/* <Box className="header-wrap">
          <Typography variant="h4" className="header-text p16 fw600">
               Manage Plans
          </Typography>
          <Typography component="p" className="header-sub-text p14">
                      View and manage your subscription plans. Create, edit, or deactivate
            plans as needed.
          </Typography>
        </Box> */}
        <Box />
        <Box className="d-flex gap-5 filter-input-wrap justify-end">
          <CustomTextField
            InputLabelProps={{ shrink: true }}
            size="small"
            placeholder="Enter plan name..."
            value={search}
            onChange={(e) => setSearch(e?.target?.value)}
            className="filter-input"
            variant="filled"
          />
          <CustomButton
            className="p16 search-btn"
            type="submit"
            fontWeight="600"
            variant="contained"
            background="#39596e"
            backgroundhover="#39596e"
            colorhover="#FFFFFF"
            leftIcon={<SearchIcon className="search-icon" />}
            title="search"
            onClick={() => {
              getSubPlanDetails(search, 1);
              setPage(1);
            }}
          />
          <Box className="create-sub-plan-btn-wrap">
            <CustomButton
              className="p16 create-sub-plan-btn"
              type="submit"
              fontWeight="600"
              variant="contained"
              background="#39596e"
              backgroundhover="#39596e"
              colorhover="#FFFFFF"
              leftIcon={<AddIcon className="add-icon" />}
              title="Create Plan"
              onClick={() => {
                setUserdata({
                  planEdit: true,
                  page: page,
                  rowsPerPage: rowsPerPage,
                  search: search,
                  isCreate: true,
                });
                saveToStorage(identifiers?.RedirectData, {
                  planEdit: true,
                  page: page,
                  rowsPerPage: rowsPerPage,
                  search: search,
                  isCreate: true,
                });
                setTimeout(() => {
                  router.push('/sorg/create-plan');
                }, [1000]);
              }}
            />
          </Box>
        </Box>
      </Box>
      <Box className="divider" />

      {planData && planData?.length > 0 ? (
        <Box className="subplan-table-container">
          <Table className="sub-plan-table">
            <TableHead>
              <TableRow>
                <TableCell className="p14 fw600 pl24">Name</TableCell>
                <TableCell className="p14 fw600">Description</TableCell>
                <TableCell className="p14 fw600">Limit</TableCell>
                {/* <TableCell align="center">Duration</TableCell> */}
                <TableCell className="p14 fw600">Cost</TableCell>
                <TableCell className="p14 fw600">Total Days</TableCell>
                <TableCell align="center" className="p14 fw600">
                  Status
                </TableCell>
                <TableCell align="center" className="p14 fw600">
                  Visibility
                </TableCell>
                <TableCell align="center" className="p14 fw600">
                  Action
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody className="table-body-wrap">
              {planData?.map((plan, index) => {
                return (
                  <TableRow key={index}>
                    <TableCell className="plan-name-wrap p14 text-capital pl24">
                      {plan?.subs_is_free_trial ? (
                        <span className="plan-badge free-plan"></span>
                      ) : (
                        <span className="plan-badge paid-plan"> </span>
                      )}
                      {plan?.subs_plan_name}
                    </TableCell>
                    <TableCell className="p14">
                      {plan?.subs_plan_description
                        ? plan?.subs_plan_description
                        : '-'}
                    </TableCell>
                    <TableCell className="p14">
                      {/* {plan?.subs_limit_min} - {plan?.subs_limit_max} */}
                      {plan?.subs_plan_category === 'core' ? (
                        <span>
                          {plan?.subs_limit_min}
                          {plan?.subs_limit_max && ' - '}
                          {plan?.subs_limit_max} Employees
                        </span>
                      ) : (
                        <span>
                          {plan?.subs_limit_min}
                          {plan?.subs_limit_max && ' - '}
                          {plan?.subs_limit_max} {plan?.sub_storage_size}
                        </span>
                      )}
                    </TableCell>
                    {/* <TableCell align="center">
                      {plan?.subs_plan_duration}
                    </TableCell> */}
                    <TableCell className="p14">
                      {currency + ' ' + plan?.subs_plan_cost.toFixed(2)}
                      {plan?.subs_total_days > 0 && (
                        <span className="text-capital">
                          / {formatDuration(plan?.subs_total_days)}
                        </span>
                      )}
                    </TableCell>
                    <TableCell className="p14">
                      {plan?.subs_total_days ? plan?.subs_total_days : '-'}
                    </TableCell>
                    <TableCell align="center" className="d-flex justify-center">
                      <Typography
                        className={`max-content text-capital p12 fw600 ${
                          plan?.subs_plan_status === 'active'
                            ? 'active-onboarding'
                            : plan?.subs_plan_status === 'inactive'
                              ? 'cancelled'
                              : 'failed'
                        }`}
                      >
                        {plan?.subs_plan_status === 'inactive'
                          ? 'In-active'
                          : plan?.subs_plan_status}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Box className="d-flex justify-center">
                        <Typography
                          className={`max-content text-capital p14 fw600 ${
                            plan?.subs_plan_visibility === 'public'
                              ? 'public-text'
                              : plan?.subs_plan_visibility === 'private'
                                ? 'private-text'
                                : ''
                          }`}
                        >
                          {plan?.subs_plan_visibility}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell
                      align="center"
                      className="d-flex justify-center "
                    >
                      {' '}
                      <Box className="action-icons">
                        <IconButton
                          onClick={() => handleEdit(plan?.id)}
                          aria-label="edit"
                          className="icon"
                        >
                          <Tooltip
                            title={<Typography>Edit</Typography>}
                            arrow
                            placement="bottom"
                            classes={{ tooltip: 'info-tooltip-container' }}
                          >
                            <EditIcon className="edit-icon" />
                          </Tooltip>
                        </IconButton>
                        {plan?.subs_plan_status !== 'deleted' && (
                          <IconButton
                            onClick={() => handleDelete(plan?.id)}
                            aria-label="delete"
                            className="icon"
                          >
                            <Tooltip
                              title={<Typography>Delete</Typography>}
                              arrow
                              placement="bottom"
                              classes={{ tooltip: 'info-tooltip-container' }}
                            >
                              <DeleteIcon className="delete-icon" />
                            </Tooltip>
                          </IconButton>
                        )}
                      </Box>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
          <CustomPagination
            currentPage={page}
            onPageChange={onPageChange}
            totalCount={totalCount}
            rowsPerPage={rowsPerPage}
            OnRowPerPage={OnRowPerPage}
          />
        </Box>
      ) : (
        <Box className="mt32">
          <Typography className="text-align h6 color-gray">
            No data found
          </Typography>
        </Box>
      )}
    </Box>
  );
}
