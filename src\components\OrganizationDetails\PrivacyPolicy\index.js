'use client';
import React, { useState, useRef } from 'react';
import CustomButton from '@/components/UI/button';
import CustomEditor from '@/components/UI/CustomEditor';
import { Box } from '@mui/material';
import './privacypolicy.scss';

export default function PrivacyPolicy() {
  const [editorContent, setEditorContent] = useState('');
  const formikRef = useRef(null);

  const descriptionValue = (value) => {
    setEditorContent(value);
    if (formikRef.current) {
      formikRef.current.setFieldValue('description', value);
    }
  };

  return (
    <Box className="privacy-policy-wrap">
      <CustomEditor
        content={editorContent}
        setContent={descriptionValue}
        height="400px"
      />
      <Box className="save-btn-wrap d-flex justify-end">
        <CustomButton
          className="p16 save-btn"
          type="submit"
          fontWeight="600"
          variant="contained"
          background="#39596e"
          backgroundhover="#39596e"
          colorhover="#FFFFFF"
          title="Save"
        />
      </Box>
    </Box>
  );
}
