.emp-contract-history {
  .employee-contract-checklist .rtwc-upload {
    max-width: 400px;
  }
  .delete-button {
    background-color: var(--text-color-danger) !important;
    border-color: var(--text-color-danger) !important;
    color: var(--color-white) !important;
  }
  .download-button {
    background-color: var(--color-green) !important;
    border-color: var(--color-green) !important;
  }
  .contract-name {
    color: var(--color-green) !important;
  }
  .contract-history-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 22px;

    .contract-history-wrap {
      display: flex !important;
      flex-direction: column !important;
      box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
      padding: 12px 12px;
      border-radius: 8px;

      .custom-button {
        padding: 6px 26px !important;
        width: 100px !important;
      }

      .custom-button :hover {
        svg {
          fill: var(--color-white) !important;
        }
      }

      .title-section {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        column-gap: 10px;

        .title {
          text-align: start;
          width: calc(100% - 100px);
        }
      }

      .action-sec {
        display: flex;
        margin-top: 6px;
        align-items: center;

        .delete-icon,
        .view-icon {
          margin-left: 10px;
          width: 18px;
          height: 18px;
          cursor: pointer;
        }

        .view-icon {
          margin-right: 5px;
        }
      }

      .upload-sec,
      .download-sec {
        width: 60px;
        height: 60px;
        margin: 0 !important;

        svg {
          width: 24px;
          height: 24px;
        }

        .upload-text,
        .download-text {
          display: none;
        }
      }

      .upload-sec {
        border: 3px dashed var(--color-green);
      }

      .download-sec {
        border: 3px solid var(--color-danger);
      }
    }

    .contract-history-wrap {
      .action-sec {
        color: var(--color-green);
        align-items: center;

        .inactive-check-box {
          padding: 0px;
        }

        .custom-button {
          width: 80px !important;
        }

        svg {
          fill: var(--color-green) !important;
        }

        .delete-icon {
          fill: var(--color-danger) !important;
          width: 17px;
          height: 17px;
        }
      }
    }
  }
}
