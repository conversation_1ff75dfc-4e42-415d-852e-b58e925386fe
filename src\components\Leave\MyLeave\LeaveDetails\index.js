import { Box, Tooltip, Typography } from '@mui/material';
import CustomPagination from '@/components/UI/pagination';
import { DataGrid, gridClasses } from '@mui/x-data-grid';
import React, { useContext } from 'react';
import CommonUserDetails from '@/components/CommonUserDetails';
import AuthContext from '@/helper/authcontext';
import LeavePopOver from '../../LeavePopOver';
import BranchDepartmentDisplay from '@/components/BranchDepartmentDisplay';

export default function LeaveDetails() {
  const { authState, setUserdata } = useContext(AuthContext);
  const columns = [
    {
      field: 'id',
      headerName: 'ID',
      width: 80,
      minWidth: 80,
      flex: 0,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-start h100 gap-sm flex-wrap">
            <Typography className="p14">
              <span>{params?.row?.employment_number}</span>
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'user_full_name',
      headerName: 'User',
      width: 250,
      minWidth: 250,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',

      renderCell: (params) => {
        let userData = params?.row;
        return (
          <CommonUserDetails
            userData={userData}
            filterDataApplied={leavefilterDataApplied}
            searchValue={leavefilterDataApplied?.searchValue}
            page={leavePage}
            rowsPerPage={leaveBalanceRowsPerPage}
            setUserdata={setUserdata}
            authState={authState}
            navigationProps={{ leaveBal: true }}
          />
        );
      },
    },
    {
      field: 'branch?.branch_name',
      headerName: 'Branch / Dep.',
      width: branchColumnWidth,
      minWidth: branchColumnWidth,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return <BranchDepartmentDisplay row={params.row} />;
      },
    },
    {
      field: 'total_leave_days',
      headerName: 'Leaves',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-start h100 gap-sm flex-wrap p14">
            <Box sx={{ boxShadow: 'none' }}>
              <Typography className="p14">
                <Tooltip
                  className="event-title text-ellipsis"
                  variant="h6"
                  interactive
                  // open
                  title={
                    <LeavePopOver
                      title="Total Leave Days"
                      Leavedays="Leave Type"
                      leave="No of leaves"
                      startDate={params?.row?.start_date}
                      endDate={params?.row?.end_date}
                      leaveTypes={params?.row?.total_leave_days}
                    />
                  }
                  classes={{
                    tooltip: 'leave-days-popover',
                  }}
                >
                  <span>{params?.row?.total_leave_days}</span>
                </Tooltip>
              </Typography>
            </Box>
          </Box>
        );
      },
    },
    {
      field: 'total_used_leave_days',
      headerName: 'Used Leave',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-start h100 gap-sm flex-wrap p14">
            <Typography className="p14">
              <Tooltip
                className="event-title text-ellipsis"
                variant="h6"
                interactive
                // open
                title={
                  <LeavePopOver
                    title="Total Leave Days"
                    Leavedays="Leave Type"
                    leave="No of leaves"
                    startDate={params?.row?.start_date}
                    endDate={params?.row?.end_date}
                    leaveTypes={params?.row?.total_used_leave_days}
                  />
                }
                classes={{
                  tooltip: 'leave-days-popover',
                }}
              >
                <span>{params?.row?.total_used_leave_days}</span>
              </Tooltip>
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'available_balance',
      headerName: 'Balance',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-start h100 gap-sm flex-wrap p14">
            <Typography className="p14">
              <Tooltip
                className="event-title text-ellipsis"
                variant="h6"
                interactive
                // open
                title={
                  <LeavePopOver
                    title="Total Leave Days"
                    Leavedays="Leave Type"
                    leave="No of leaves"
                    startDate={params?.row?.start_date}
                    endDate={params?.row?.end_date}
                    leaveTypes={params?.row?.available_balance}
                  />
                }
                classes={{
                  tooltip: 'leave-days-popover',
                }}
              >
                <span>{params?.row?.available_balance}</span>
              </Tooltip>
            </Typography>
          </Box>
        );
      },
    },
  ];
  return (
    <Box>
      <Box className="data-grid-wrap">
        <DataGrid
          rows={leaveBalance}
          columns={columns}
          pageSize={leaveBalanceRowsPerPage}
          checkboxSelection={false}
          disableSelectionOnClick
          getRowHeight={() => 'auto'}
          sx={{
            [`& .${gridClasses.cell}`]: {
              py: 1,
            },
          }}
        />
      </Box>
      <CustomPagination
        currentPage={leavePage}
        totalCount={leaveTotalCount}
        rowsPerPage={leaveBalanceRowsPerPage}
        onPageChange={onPageChange}
        OnRowPerPage={OnRowPerPage}
        className="leave-pagination"
      />
    </Box>
  );
}
