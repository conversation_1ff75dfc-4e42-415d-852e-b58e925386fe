.history-log {
  font-family: var(--font-family-primary);
  max-width: 600px;
  margin: 0 auto;
  .log-item {
    display: flex;
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0px;
    }
    .timeline {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 20px;
      position: relative;
      .avatar {
        width: 40px;
        height: 40px;
        z-index: 2;
      }
      .line {
        position: absolute;
        top: 40px;
        width: 2px;
        height: calc(100% + 20px);
        background-color: var(--color-light-grayish-blue);
        z-index: 1;
      }
    }
  }
}

.content {
  flex: 1;
  padding-bottom: 20px;
  .header {
    margin-bottom: 12px;
    h3 {
      margin: 0 0 5px;
      font-size: 16px;
      font-weight: 500;
      color: var(--color-black);
    }
    .timestamp {
      margin: 0;
      font-size: 14px;
      color: var(--text-light-dark);
    }
  }
  .details {
    background-color: var(--color-secondary);
    border-radius: 8px;
    padding: 15px;
    .detail-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }
    }
    .history-label {
      font-size: 12px;
      color: var(--text-light-dark);
      font-weight: 500;
      text-transform: uppercase;
    }
    .history-value {
      font-size: 14px;
      color: var(--color-black);
      font-weight: 400;
      text-align: right;
    }
  }
}
