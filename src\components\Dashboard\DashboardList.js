import React, { useContext } from 'react';
import { Box, Typography } from '@mui/material';
import AuthContext from '@/helper/authcontext';
import CustomButton from '@/components/UI/CustomButton';
import { useRouter } from 'next/navigation';
import CancelIcon from '@mui/icons-material/Cancel';
import AddIcon from '@mui/icons-material/Add';
import PushPinIcon from '@mui/icons-material/PushPin';
import NoDataView from '@/components/UI/NoDataView';
import './dashboard.scss';

const DashboardSidebar = ({ dashboardList, DeleteDashboard }) => {
  const router = useRouter();
  const { authState } = useContext(AuthContext);
  return (
    <Box className="saved-dashboard-list">
      {authState?.UserPermission?.dashboard === 2 && (
        <Box className="d-flex align-center justify-end">
          <CustomButton
            variant="outlined"
            startIcon={<AddIcon />}
            title={'Create Dashboard'}
            onClick={() => router.push('/chart-dashboard/template')}
          />
        </Box>
      )}

      {dashboardList && dashboardList?.length > 0 ? (
        <>
          {dashboardList?.map((item) => {
            return (
              <Box className="dashboard-list">
                <Typography
                  className="title-text dashboard-text text-ellipsis-line"
                  onClick={() => {
                    router.push(`/chart-dashboard/${item?.id}`);
                  }}
                >
                  {item?.dashboard_name}
                </Typography>
                {item?.has_dashboard_default ? (
                  <PushPinIcon className="pin" />
                ) : (
                  <>
                    {authState?.UserPermission?.dashboard === 2 && (
                      <CancelIcon
                        className="cancel"
                        onClick={() => DeleteDashboard(item?.id)}
                      />
                    )}
                  </>
                )}
              </Box>
            );
          })}
        </>
      ) : (
        <>
          <Box className="no-data d-flex align-center justify-center">
            <NoDataView
              title="No Dashboard Found"
              description="There is no dashboard available at the moment."
            />
          </Box>
        </>
      )}
    </Box>
  );
};

export default DashboardSidebar;
