@import '@/styles/variable.scss';

.logo-section-wrap {
  width: 45%;
  padding: 20px;
  gap: 20px;

  .logo-wrap {
    img {
      width: 85px;
      height: 85px;
      border-radius: 50px;
      border: 3px solid white;
      box-shadow: 0px 0px 5px lightgray;
    }
    .profile-image,
    .MuiAvatar-root {
      width: 85px;
      height: 85px;
      border-radius: 50px;
      //   border: 3px solid white;
    }
  }

  .upload-sec {
    width: 100%;
    border: 1px solid $color-Dark-10;
    height: 110px;
    border-radius: 10px;
    display: flex;
    justify-content: center;
    align-items: center;

    .upload-area {
      height: 100%;
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      .upload-text {
        color: $color-primary;
      }
    }

    svg {
      width: 30px;
      height: 30px;
      fill: $color-primary !important;
    }

    .upload-text {
      color: $color-primary;
    }
  }

  @media (max-width: 1023px) {
    width: 60% !important;
  }

  @media (max-width: 991px) {
    padding: 20px 0px 20px 20px;
    width: 70% !important;
  }

  @media (max-width: 767px) {
    width: 100% !important;
  }

  @media (max-width: 424px) {
    flex-direction: column;

    .logo-wrap {
      display: flex;
      justify-content: center;
    }
  }
}

.dropzone-media-container {
  border: 2px dashed var(--border-color-primary);
  width: 256px;
  height: 110px;
  border-radius: var(--border-radius-lg);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px;
  svg {
    width: 30px;
    height: 30px;
    fill: var(--icon-color-primary);
  }

  .upload-text {
    color: var(--text-color-slate-gray);
    padding: 0px 10px;
    .blue-text {
      color: var(--text-color-primary);
    }
  }
}

.dropzone-media-preview-container {
  max-width: max-content;
  max-height: max-content;
  position: relative;
  width: 97%;
  img {
    width: 100%;
    max-height: 110px;
    object-fit: cover;
    border-radius: var(--border-radius-lg);
  }
  .cancel-icon {
    position: absolute;
    top: -11px;
    right: -11px;
    width: var(--icon-size-sm);
    height: var(--icon-size-sm);
    color: var(--icon-bold-red-color);
  }
}
