'use client';

import React, { useContext } from 'react';
import {
  Box,
  TableHead,
  TableCell,
  TableRow,
  TableBody,
  Table,
  Typography,
  TableContainer,
  Tooltip,
} from '@mui/material';
import AuthContext from '@/helper/authcontext';
import './budgetTable.scss';
import '../../BudgetTable/budgetTable.scss';

export default function BudgetTable({ BudgetData, isFromHistory }) {
  const { authState } = useContext(AuthContext);

  const cellWidth = 100;
  const PerCellWidth = BudgetData?.columns_group?.length > 2 ? 120 : 100;
  const FixedcellWidth = 100;
  const isChildren = BudgetData?.columns_group?.find(
    (f) => f?.children && f?.children?.length > 0
  );
  const isOneCell =
    BudgetData?.column && Object.keys(BudgetData?.column).length === 2;
  const currency =
    authState?.currency_details && authState?.currency_details?.symbol
      ? authState?.currency_details?.symbol
      : '£';
  // const mainheadwidth = (group) => {
  //   return group?.key === 'col1'
  //     ? `${FixedcellWidth}px`
  //     : group?.children?.length > 0
  //       ? `${group?.children?.map((r) => !r?.is_percentage)?.length
  //         ? group?.children?.map((r) => !r?.is_percentage)?.length
  //         : 0 * cellWidth +
  //           group?.children?.map((r) => r?.is_percentage)?.length
  //           ? group?.children?.map((r) => r?.is_percentage)?.length
  //           : 0 * PerCellWidth
  //       }px`
  //       : `${cellWidth}px`;
  // };
  const mainheadwidth = (group) => {
    return group?.key === 'col1'
      ? `${FixedcellWidth}px`
      : group?.children?.length > 0
        ? `${
            group?.children?.map((r) => !r?.is_percentage)?.length
              ? group?.children?.map((r) => !r?.is_percentage)?.length *
                cellWidth
              : 0 * cellWidth +
                  group?.children?.map((r) => r?.is_percentage)?.length
                ? group?.children?.map((r) => r?.is_percentage)?.length *
                  PerCellWidth
                : 0 * PerCellWidth
          }px`
        : `${cellWidth}px`;
  };
  // const headwidth = (group) => {
  //   return group?.children?.length > 0
  //     ? `${group?.children?.map((r) => !r?.is_percentage)?.length
  //       ? group?.children?.map((r) => !r?.is_percentage)?.length
  //       : 0 * cellWidth +
  //         group?.children?.map((r) => r?.is_percentage)?.length
  //         ? group?.children?.map((r) => r?.is_percentage)?.length
  //         : 0 * PerCellWidth
  //     }px`
  //     : `${cellWidth}px`;
  // };
  const headwidth = (group) => {
    return group?.children?.length > 0
      ? `${
          group?.children?.map((r) => !r?.is_percentage)?.length
            ? group?.children?.map((r) => !r?.is_percentage)?.length * cellWidth
            : 0 * cellWidth +
                group?.children?.map((r) => r?.is_percentage)?.length
              ? group?.children?.map((r) => r?.is_percentage)?.length *
                PerCellWidth
              : 0 * PerCellWidth
        }px`
      : `${cellWidth}px`;
  };

  const TotalValue = (key, totalkey) => {
    const total = BudgetData?.data?.reduce((sum, item) => {
      const value =
        totalkey !== item?.forecast_category_type
          ? 0
          : parseFloat(item[key]) || 0;
      return sum + value;
    }, 0);
    // Return total rounded to 2 decimal places
    return total.toFixed(2);
  };
  const getTotals = (data, index) => {
    const firstChildKeys = BudgetData?.columns_group
      .map((col) => col?.group_type !== 'Total' && col.children?.[index]?.key)
      .filter(Boolean);
    const total = firstChildKeys.reduce(
      (sum, key) => sum + (parseFloat(data[key]) || 0),
      0
    );
    return total.toFixed(2);
  };

  const TotalValueDiff = (totalkey, actual, budget) => {
    // const total = BudgetData?.data?.reduce((sum, item) => {
    //   const value =
    //     totalkey !== item?.forecast_category_type
    //       ? 0
    //       : parseFloat(item[key]) || 0;
    //   return sum + value;
    // }, 0);
    // Return total rounded to 2 decimal places
    const diffValue = /^0+(\.0+)?$/.test(
      parseFloat(TotalValue(budget, totalkey))
    )
      ? 0
      : ((parseFloat(TotalValue(budget, totalkey)) -
          parseFloat(TotalValue(actual, totalkey))) /
          parseFloat(TotalValue(budget, totalkey))) *
        100;
    // Return total rounded to 2 decimal places
    return diffValue ? diffValue?.toFixed(2) : 0;
  };
  const TotalValueGroup = (gindex, totalkey) => {
    const total = BudgetData?.data?.reduce((sum, item) => {
      const value =
        totalkey !== item?.forecast_category_type
          ? 0
          : parseFloat(getTotals(item, gindex) ? getTotals(item, gindex) : 0); // getTotals(dataRow, gindex)
      return sum + value;
    }, 0);
    // Return total rounded to 2 decimal places
    return total ? total?.toFixed(2) : 0;
  };
  const TotalValueGroupDiff = (gindex, totalkey) => {
    const diffValue = /^0+(\.0+)?$/.test(
      parseFloat(TotalValueGroup(1, totalkey))
    )
      ? 0
      : ((parseFloat(TotalValueGroup(1, totalkey)) -
          parseFloat(TotalValueGroup(0, totalkey))) /
          parseFloat(TotalValueGroup(1, totalkey))) *
        100;
    // Return total rounded to 2 decimal places
    return diffValue ? diffValue?.toFixed(2) : 0;
  };
  const ShowValue = (
    dataRow,
    group,
    item,
    IsDifference,
    gindex,
    tooltip,
    isHeader
  ) => {
    const totalkey =
      dataRow?.col1 === 'Total income'
        ? 'income'
        : dataRow?.col1 === 'Total other'
          ? 'other'
          : dataRow?.col1 === 'Total expense'
            ? 'expense'
            : 'other';
    if (
      item?.category_name === 'Total' &&
      dataRow?.type === 'mainTotal' &&
      group?.group_type === 'Total' &&
      item?.key !== 'col1' &&
      !IsDifference
    ) {
      return (
        <span className={`sub-title-text text-ellipsis-line`}>
          {TotalValueGroup(gindex, totalkey)
            ? TotalValueGroup(gindex, totalkey)
            : dataRow[item?.key] || dataRow[item?.key] === 0
              ? dataRow[item?.key]
              : '-'}
        </span>
      );
    } else if (
      item?.category_name === 'Total' &&
      dataRow?.type === 'mainTotal' &&
      group?.group_type === 'Total' &&
      item?.key !== 'col1' &&
      IsDifference
    ) {
      return (
        <span
          className={`sub-title-text text-ellipsis-line d-flex align-center difference-cell ${
            !tooltip && TotalValueGroupDiff(gindex, totalkey) > 0
              ? 'color-green' //color-green
              : !tooltip && 'color-red' //color-red
          } `}
        >
          <span className={`sub-title-text fw500 text-ellipsis-line`}>
            {TotalValueGroupDiff(gindex)
              ? Math.abs(TotalValueGroupDiff(gindex, totalkey)) + '%'
              : dataRow[item?.key] || dataRow[item?.key] === 0
                ? dataRow[item?.key] + '%'
                : '-'}
          </span>
        </span>
      );
    } else if (
      dataRow?.type === 'mainTotal' &&
      item?.key !== 'col1' &&
      !IsDifference
    ) {
      return (
        <span className={`sub-title-text text-ellipsis-line`}>
          {TotalValue(item?.key, totalkey) ||
          TotalValue(item?.key, totalkey) === 0
            ? TotalValue(item?.key, totalkey)
            : dataRow[item?.key] || dataRow[item?.key] === 0
              ? dataRow[item?.key]
              : '-'}
        </span>
      );
    } else if (
      dataRow?.type === 'mainTotal' &&
      item?.key !== 'col1' &&
      IsDifference
    ) {
      const totalkey =
        dataRow?.col1 === 'Total income'
          ? 'income'
          : dataRow?.col1 === 'Total other'
            ? 'other'
            : dataRow?.col1 === 'Total expense'
              ? 'expense'
              : 'other';

      const actualKey = `col${parseFloat(item?.key?.replace('col', '')) - 2}`;
      const budgetKey = `col${parseFloat(item?.key?.replace('col', '')) - 1}`;
      return (
        <span
          className={`sub-title-text text-ellipsis-line d-flex align-center difference-cell ${
            !tooltip && TotalValueDiff(totalkey, actualKey, budgetKey) > 0
              ? 'color-green' //color-green
              : !tooltip && 'color-red' //color-red
          }`}
        >
          <span className={`sub-title-text fw500 text-ellipsis-line`}>
            {TotalValueDiff(totalkey, actualKey, budgetKey) ||
            TotalValueDiff(totalkey, actualKey, budgetKey) === 0
              ? Math.abs(TotalValueDiff(totalkey, actualKey, budgetKey)) + '%'
              : dataRow[item?.key] || dataRow[item?.key] === 0
                ? dataRow[item?.key]
                : '-'}
          </span>
        </span>
      );
    }
    if (
      (dataRow['payment_type_id'] || dataRow['type'] === 'mainHeader') &&
      item?.key === 'col1'
    ) {
      const isMainCurrency =
        item?.key === 'col1' &&
        dataRow?.has_field_currency &&
        dataRow?.type &&
        dataRow?.type === 'subHeader';
      return (
        <span
          className={`sub-title-text  d-flex ${isHeader ? 'text-capital' : ''} ${isMainCurrency ? 'justify-space-between' : 'justify-center'}`}
        >
          <span className="sub-title-text pr4 text-ellipsis-line">
            {' '}
            {dataRow[item?.key] || dataRow[item?.key] === 0
              ? dataRow[item?.key]
              : ''}
          </span>
          {isMainCurrency ? (
            <span className="sub-title-text currency-symbol text-ellipsis-line">
              {'(' + currency + ')'}
            </span>
          ) : (
            ''
          )}
        </span>
      );
    }
    if (
      (dataRow?.type &&
        dataRow?.type === 'subHeader' &&
        dataRow?.forecast_category_status !== 'combined') ||
      (dataRow['type'] && dataRow['type'] === 'mainHeader')
    ) {
      return (
        <span
          className={`sub-title-text text-ellipsis-line main-category`}
        ></span>
      );
    }

    if (IsDifference) {
      // const diffValue = (
      //   parseFloat(getTotals(dataRow, 1)) - parseFloat(getTotals(dataRow, 0))
      // ).toFixed(2); // Difference
      // const prevKey = `col${parseFloat(item?.key?.replace('col', '')) - 2}`;
      // const budgetKey = `col${parseFloat(item?.key?.replace('col', '')) - 1}`;

      const budgetValue = getTotals(dataRow, 1);
      const prevValue = getTotals(dataRow, 0);
      const diffPerTotal =
        parseFloat(budgetValue) === 0
          ? 0
          : ((parseFloat(budgetValue) - parseFloat(prevValue)) /
              parseFloat(budgetValue)) *
            100;
      return (
        <span
          className={`sub-title-text difference-cell w100 `} //text-ellipsis-line d-flex align-center
        >
          {group?.group_type === 'Total' ? (
            <>
              {dataRow[item?.key]?.type !== 'total' && (
                <span
                  className={`sub-title-text fw500 w text-ellipsis-line table-per-diff ${
                    !tooltip && diffPerTotal > 0
                      ? 'color-green' //color-green
                      : !tooltip && 'color-red' //color-red
                  } `}
                >
                  {' '}
                  {(diffPerTotal !== 0
                    ? Math.abs(diffPerTotal.toFixed(2))
                    : 0) + '%'}{' '}
                </span>
              )}
            </>
          ) : (
            <>
              {dataRow[item?.key]?.type !== 'total' && (
                <span
                  className={`sub-title-text fw500 w text-ellipsis-line table-per-diff ${
                    item?.key &&
                    dataRow[item?.key] &&
                    !tooltip &&
                    parseFloat(dataRow[item?.key]) > 0
                      ? 'color-green' //color-green
                      : !tooltip && 'color-red' //color-red
                  } `}
                >
                  {dataRow[item?.key] || dataRow[item?.key] === 0
                    ? Math.abs(dataRow[item?.key]) + '%'
                    : '-'}
                </span>
              )}
            </>
          )}
        </span>
      );
    }
    const old_key = item?.key + '_old';
    return (
      <>
        {/* <span className={`sub-title-text text-ellipsis-line`}>
        {group?.group_type === 'Total' &&
        (getTotals(dataRow, gindex) || getTotals(dataRow, gindex) === 0)
          ? getTotals(dataRow, gindex)
          : dataRow[item?.key] || dataRow[item?.key] === 0
            ? dataRow[item?.key]
            : '-'}
      </span> */}
        <span className={`sub-title-text table-old-new`}>
          {dataRow[old_key] || dataRow[old_key] === 0 ? (
            <>
              {' '}
              <span
                className={
                  tooltip
                    ? 'sub-title-text old-value text-ellipsis-line'
                    : 'sub-title-text old-value text-ellipsis-line'
                }
              >
                {group?.group_type === 'Total' &&
                (getTotals(dataRow, gindex) || getTotals(dataRow, gindex) === 0)
                  ? getTotals(dataRow, gindex)
                  : dataRow[old_key] || dataRow[old_key] === 0
                    ? dataRow[old_key]
                    : '-'}
              </span>
              {tooltip ? ' -> ' : ''}
            </>
          ) : (
            ''
          )}
          <span
            className={`sub-title-text text-ellipsis-line ${dataRow[old_key] || dataRow[old_key] === 0 ? 'new-value ' : ''} `}
          >
            {group?.group_type === 'Total' &&
            (getTotals(dataRow, gindex) || getTotals(dataRow, gindex) === 0)
              ? getTotals(dataRow, gindex)
              : dataRow[item?.key] || dataRow[item?.key] === 0
                ? dataRow[item?.key]
                : '-'}
          </span>
        </span>
      </>
    );
  };
  return (
    <Box className="Budget-table">
      <TableContainer className="Budget-table-container">
        <Table className="dsr-table">
          <TableHead>
            {BudgetData?.columns_group &&
              BudgetData?.columns_group?.length > 0 &&
              BudgetData?.columns_group?.map((group, groupIndex) => (
                <TableCell
                  colSpan={
                    group?.children?.length ? group?.children?.length : null
                  }
                  align="center"
                  className={`table-head ${
                    group?.key === 'col1' && isChildren
                      ? 'fixed-header-cell fixed-header-cell-children text-align'
                      : group?.key === 'col1'
                        ? 'table-head fixed-header-cell text-align'
                        : isOneCell
                          ? 'w100'
                          : ''
                  } ${
                    BudgetData?.columns_group?.length - 1 === groupIndex
                      ? 'table-head fixed-header-cell-last text-align'
                      : ''
                  } ${
                    BudgetData?.columns_group?.length - 2 === groupIndex
                      ? 'table-head fixed-header-cell-last-secound text-align'
                      : ''
                  }  ${
                    BudgetData?.columns_group?.length - 3 === groupIndex
                      ? 'border-right-0'
                      : ''
                  }`}
                  style={{
                    minWidth:
                      // group?.key === 'col1'
                      //   ? `${cellWidth - 1}px`
                      //   :
                      mainheadwidth(group),
                    maxWidth: mainheadwidth(group),
                  }}
                >
                  <Box className="d-flex justify-center">
                    <Typography className="sub-title-text fw500 w100">
                      <Tooltip
                        title={<Typography>{group?.content}</Typography>}
                        arrow
                        classes={{
                          tooltip: 'info-tooltip-container ',
                        }}
                      >
                        {' '}
                        <span className="sub-title-text fw500 w text-ellipsis-line">
                          {group?.content}
                        </span>
                      </Tooltip>
                    </Typography>
                  </Box>
                  {group?.children && group?.children?.length > 0 && (
                    <TableRow
                      className="group-table-row"
                      style={{
                        flex: 1,
                        minWidth: headwidth(group),
                        // maxWidth: headwidth(group)
                      }}
                    >
                      {group?.children &&
                        group?.children?.length > 0 &&
                        group?.children?.map((item) => (
                          <TableCell
                            className="table-cell heading-cell text-align"
                            style={{
                              flex: 1,
                              minWidth: item?.is_percentage
                                ? `${PerCellWidth}px`
                                : `${cellWidth}px`,
                              maxWidth:
                                BudgetData?.columns_group?.length > 2 &&
                                item?.is_percentage &&
                                item?.is_editable
                                  ? `${PerCellWidth + 8}px`
                                  : 'auto',
                            }}
                          >
                            <Tooltip
                              title={<Typography>{item?.content}</Typography>}
                              arrow
                              classes={{
                                tooltip: 'info-tooltip-container ',
                              }}
                            >
                              <span className="sub-title-text fw500 w text-ellipsis-line">
                                {' '}
                                {item?.content}
                              </span>
                            </Tooltip>
                          </TableCell>
                        ))}
                    </TableRow>
                  )}
                </TableCell>
              ))}
          </TableHead>

          <TableBody>
            {BudgetData?.data?.map((dataRow, rowIndex) => (
              <TableRow
                key={rowIndex}
                className={
                  dataRow?.type === 'mainTotal'
                    ? 'total-row'
                    : dataRow?.type && dataRow?.type === 'mainHeader'
                      ? 'main-header-row'
                      : dataRow?.type &&
                          dataRow?.type === 'subHeader' &&
                          dataRow?.forecast_category_status !== 'combined'
                        ? 'sub-header-row'
                        : dataRow?.type && dataRow?.type === 'subHeader'
                          ? 'sub-header-row-combined'
                          : ''
                }
              >
                {BudgetData?.columns_group &&
                  BudgetData?.columns_group?.length > 0 && (
                    <>
                      {(dataRow?.type && dataRow?.type === 'mainHeader') ||
                      (dataRow?.type &&
                        dataRow?.type === 'subHeader' &&
                        dataRow?.forecast_category_status !== 'combined') ? (
                        <>
                          {BudgetData?.columns_group?.map((group, index) => (
                            <>
                              {group?.children &&
                              group?.children?.length > 0 ? (
                                <>
                                  {group?.children?.map((item, findex) => (
                                    <TableCell
                                      className={`table-cell table-value table-child  ${
                                        BudgetData?.columns_group?.length -
                                          1 ===
                                          index && 'fixed-value-last'
                                      }  ${
                                        BudgetData?.columns_group?.length -
                                          1 ===
                                        index
                                          ? 'fixed-cell-last'
                                          : ''
                                      } ${
                                        BudgetData?.columns_group?.length -
                                          2 ===
                                        index
                                          ? `fixed-cell-last-secound-${findex}`
                                          : ''
                                      }  ${
                                        dataRow?.type &&
                                        dataRow?.type === 'mainHeader' &&
                                        rowIndex === 0
                                          ? `group-border-top-0`
                                          : ''
                                      }`}
                                    ></TableCell>
                                  ))}
                                </>
                              ) : (
                                <>
                                  <TableCell
                                    style={{
                                      minWidth:
                                        // group?.key === 'col1'
                                        //   ? `${cellWidth - 1}px`
                                        //   :
                                        index === 0
                                          ? `${FixedcellWidth}px`
                                          : `${cellWidth}px`,
                                      maxWidth:
                                        index === 0
                                          ? `${FixedcellWidth}px`
                                          : `${cellWidth}px`,
                                    }}
                                    className={`table-cell table-value  ${
                                      isOneCell && index !== 0
                                        ? 'text-align firstValue-cell one-cell'
                                        : index === 1
                                          ? ' firstValue-cell' //text-align-end
                                          : group?.key === 'col1'
                                            ? 'text-align fixed-value'
                                            : '' //text-align-end
                                    } ${
                                      !isFromHistory &&
                                      !dataRow[group?.key]?.isPast &&
                                      group?.is_editable && // dataRow[group?.key]?.isEditable &&
                                      dataRow[group?.key]?.type !== 'total' &&
                                      'text-field-cell'
                                    } ${dataRow?.type && dataRow?.type === 'mainHeader' && 'text-capital'} 
                                      ${
                                        BudgetData?.columns_group?.length -
                                          1 ===
                                        index
                                          ? 'fixed-cell-last'
                                          : ''
                                      } ${
                                        BudgetData?.columns_group?.length -
                                          2 ===
                                        index
                                          ? `fixed-cell-last-secound-${findex}`
                                          : ''
                                      } ${
                                        BudgetData?.columns_group?.length -
                                          3 ===
                                        index
                                          ? 'border-right-0'
                                          : ''
                                      }
                                      `}
                                  >
                                    {BudgetData?.columns_group?.length - 1 ===
                                    index ? (
                                      <></>
                                    ) : (
                                      <>
                                        <Tooltip
                                          title={ShowValue(
                                            dataRow,
                                            '',
                                            group,
                                            '',
                                            '',
                                            true,
                                            dataRow?.type === 'mainHeader'
                                          )}
                                          arrow
                                          classes={{
                                            tooltip: 'info-tooltip-container ',
                                          }}
                                        >
                                          {ShowValue(dataRow, '', group)}
                                        </Tooltip>
                                      </>
                                    )}
                                  </TableCell>
                                </>
                              )}
                            </>
                          ))}
                        </>
                      ) : (
                        <>
                          {' '}
                          {BudgetData?.columns_group?.map((group, index) => (
                            <>
                              {group?.children &&
                              group?.children?.length > 0 ? (
                                <>
                                  {group?.children?.map((item, findex) => (
                                    <TableCell
                                      key={item?.key}
                                      style={{
                                        flex: 1,
                                        minWidth:
                                          group?.children?.length - 1 === findex
                                            ? `${cellWidth}px`
                                            : `${cellWidth}px`,
                                        maxWidth:
                                          group?.children?.length - 1 === findex
                                            ? `${cellWidth}px`
                                            : `${cellWidth}px`,
                                      }}
                                      className={`table-cell table-value  ${
                                        //text-align-end
                                        index === 1 &&
                                        findex === 0 &&
                                        'firstValue-cell'
                                      }
                                         ${
                                           !group?.isPast &&
                                           (findex === 1 || findex === 2) &&
                                           BudgetData?.columns_group?.length -
                                             2 !==
                                             index &&
                                           dataRow?.type !== 'mainTotal' &&
                                           'text-field-cell'
                                         } ${
                                           group?.children?.length - 1 ===
                                             findex && 'difference-cells'
                                         }  ${
                                           BudgetData?.columns_group?.length -
                                             1 ===
                                           index
                                             ? 'fixed-cell-last'
                                             : ''
                                         } ${
                                           BudgetData?.columns_group?.length -
                                             2 ===
                                           index
                                             ? `fixed-cell-last-secound-${findex}`
                                             : ''
                                         } ${
                                           BudgetData?.columns_group?.length -
                                             3 ===
                                           index
                                             ? 'border-right-0'
                                             : ''
                                         }`}
                                    >
                                      <Tooltip
                                        title={ShowValue(
                                          dataRow,
                                          group,
                                          item,
                                          group?.children?.length - 1 ===
                                            findex,
                                          findex,
                                          true
                                        )}
                                        arrow
                                        classes={{
                                          tooltip: 'info-tooltip-container ',
                                        }}
                                      >
                                        <Typography className="sub-title-text">
                                          {ShowValue(
                                            dataRow,
                                            group,
                                            item,
                                            group?.children?.length - 1 ===
                                              findex,
                                            findex
                                          )}
                                        </Typography>
                                      </Tooltip>
                                    </TableCell>
                                  ))}
                                </>
                              ) : (
                                <>
                                  <TableCell
                                    style={{
                                      minWidth:
                                        // group?.key === 'col1'
                                        //   ? `${cellWidth - 1}px`
                                        //   :
                                        index === 0
                                          ? `${FixedcellWidth}px`
                                          : `${cellWidth}px`,
                                      maxWidth:
                                        index === 0
                                          ? `${FixedcellWidth}px`
                                          : `${cellWidth}px`,
                                    }}
                                    className={`table-cell table-value  ${
                                      isOneCell && index !== 0
                                        ? 'text-align firstValue-cell one-cell'
                                        : index === 1
                                          ? 'firstValue-cell' //text-align-end
                                          : group?.key === 'col1'
                                            ? 'text-align fixed-value'
                                            : '' //text-align-end
                                    } ${
                                      group?.key !== 'col1' &&
                                      dataRow?.type !== 'mainTotal' && // dataRow[group?.key]?.isEditable &&!dataRow[group?.key]?.isPast && !isFromHistory &&dataRow[group?.key]?.type !== 'total'
                                      'text-field-cell'
                                    } ${
                                      BudgetData?.columns_group?.length - 1 ===
                                      index
                                        ? 'fixed-cell-last'
                                        : ''
                                    } ${
                                      BudgetData?.columns_group?.length - 2 ===
                                      index
                                        ? `fixed-cell-last-secound-${findex}`
                                        : ''
                                    } ${
                                      BudgetData?.columns_group?.length - 3 ===
                                      index
                                        ? 'border-right-0'
                                        : ''
                                    }`}
                                  >
                                    <Tooltip
                                      title={ShowValue(
                                        dataRow,
                                        '',
                                        group,
                                        '',
                                        '',
                                        true
                                      )}
                                      arrow
                                      classes={{
                                        tooltip: 'info-tooltip-container ',
                                      }}
                                    >
                                      <Typography className="sub-title-text">
                                        {ShowValue(dataRow, '', group)}
                                      </Typography>
                                    </Tooltip>
                                  </TableCell>
                                </>
                              )}
                            </>
                          ))}
                        </>
                      )}
                    </>
                  )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
}
