'use client';

import {
  Box,
  // Checkbox,
  Divider,
  // FormControlLabel,
  // FormGroup,
  Typography,
} from '@mui/material';
// import CheckBoxIcon from '@mui/icons-material/CheckBox';
// import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CircleIcon from '@mui/icons-material/Circle';
import CustomCheckbox from '@/components/UI/CustomCheckbox';

const RotaDashboardRightSideFilter = ({
  filterData,
  handleRolesSelection,
  shiftList,
}) => {
  const getUniqueRoles = () => {
    const allShifts = _.flatMap(shiftList, (day) => day.shifts);
    const allRoles = _.compact(_.map(allShifts, 'role'));
    const uniqueRoles = _.uniqBy(allRoles, 'id');
    // return _.uniqBy(allRoles, 'id');

    return [{ id: null, role_name: 'No Role' }, ...uniqueRoles];
  };

  return (
    <div className="dashboard-right-filter">
      <Typography className="sub-header-text section-left-title">
        Roles filter
      </Typography>
      <Divider className="mt8 mb8" />
      {getUniqueRoles()?.map((event) => (
        <div key={event?.id} className="dashboard-right-filter-checkbox">
          <CustomCheckbox
            disableRipple
            name={`${event?.id}`}
            checked={filterData.selectedRoles.includes(event?.id)}
            onChange={() => handleRolesSelection(event?.id)}
            label={
              <Box className="dashboard-right-filter-check-box-label">
                <CircleIcon />
                <span>{event?.role_name}</span>
              </Box>
            }
          />
        </div>
      ))}
    </div>
  );
};
export default RotaDashboardRightSideFilter;
