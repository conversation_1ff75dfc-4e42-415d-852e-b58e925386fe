import React, { useEffect, useState } from 'react';

function AppImage({
  src,
  alt = 'Image Name',
  className = '',
  fallback = '/images/app-logo.svg',
  isSvg = false,
  SvgImg = null,
  ...props
}) {
  const [errorMap, setErrorMap] = useState({});

  useEffect(() => {
    // Reset error when src changes
    setErrorMap((prev) => ({ ...prev, [src]: false }));
  }, [src]);
  const handleError = () => {
    setErrorMap((prev) => ({ ...prev, [src]: true }));
  };

  const isError = errorMap[src];

  return (
    <>
      {isError && !isSvg ? (
        <img src={fallback} alt={alt} className={className} {...props} />
      ) : isError && isSvg ? (
        <SvgImg className={className} {...props} />
      ) : (
        <img
          src={src || ''}
          alt={alt}
          className={className}
          onError={handleError}
          {...props}
        />
      )}
    </>
  );
}

export default AppImage;
