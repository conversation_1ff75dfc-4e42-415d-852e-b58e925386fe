'use client';

import React from 'react';
import {
  Box,
  Typography,
  InputAdornment,
  Checkbox,
  FormControlLabel,
  FormGroup,
  Tooltip,
} from '@mui/material';
import CustomTextField from '@/components/UI/CustomTextField';
import {
  removeVAT,
  TotalOfWSRObj,
  TotalOfWSRArray,
} from '@/helper/common/commonFunctions';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import InfoIcon from '@mui/icons-material/Info';

export default function WSRCategory({
  DsrDataIncome,
  setVatIsView,
  VatIsView,
  setIsRemarkView,
  IsRemarkView,
  setIsSubmit,
  setDsrDataIncome,
  setRandom,
  setDsrDataOther,
  setRandomother,
  DsrDataOther,
  currency,
  setVAT,
  VAT,
  setRandomvat,
  VatValue,
  ValueOfTotal,
  totalofALLVat,
  isSubmit,
  isEdit,
}) {
  //Total without VAT
  const TotalWithoutVAT = () => {
    return ValueOfTotal(
      parseFloat(
        removeVAT(
          ValueOfTotal(parseFloat(VAT?.VAT2)),
          VatValue && VatValue?.[1] ? VatValue?.[1] : 0
        )?.netAmount +
          ValueOfTotal(parseFloat(VAT?.VAT1)) +
          ValueOfTotal(parseFloat(VAT?.NoneVat))
      )
    ).toFixed(2);
  };
  return (
    <Box>
      {' '}
      {DsrDataIncome && DsrDataIncome?.length > 0 && (
        <>
          <FormGroup className="form-checkbox pt16">
            <FormControlLabel
              control={
                <Checkbox
                  className="check-box "
                  icon={<CheckBoxOutlineBlankIcon className="uncheck-icon" />}
                  checkedIcon={<CheckBoxIcon className="check-icon" />}
                  disableRipple
                />
              }
              name="weekly"
              className="check-box-form form-row max-content sub-title-text"
              checked={VatIsView}
              onChange={(e) => {
                setVatIsView(e.target.checked);
              }}
              label="View VAT Details"
            />
          </FormGroup>
          <FormGroup className="form-checkbox ">
            <FormControlLabel
              control={
                <Checkbox
                  className="check-box "
                  icon={<CheckBoxOutlineBlankIcon className="uncheck-icon" />}
                  checkedIcon={<CheckBoxIcon className="check-icon" />}
                  disableRipple
                />
              }
              name="weekly"
              className="check-box-form form-row max-content sub-title-text"
              checked={IsRemarkView}
              onChange={(e) => {
                setIsRemarkView(e.target.checked);
              }}
              label="View Remark"
            />
          </FormGroup>
          <Box className="dsr-add-view pt16">
            {DsrDataIncome &&
              DsrDataIncome?.length > 0 &&
              DsrDataIncome?.map((item, dindex) => {
                return (
                  <Box className="dsr-main-category income">
                    <Typography className="body-text fw600 text-capital main-category-text income-text text-ellipsis-line">
                      {item?.payment_type_title}
                    </Typography>
                    <span className="category-status income">
                      <span className="sub-title-text category-accepted  fw600 text-capital">
                        {' '}
                        Income
                      </span>
                    </span>
                    {item?.payment_type_category?.length > 0 &&
                      item?.payment_type_category?.map((citem, cindex) => {
                        return (
                          <Box className="">
                            {' '}
                            {citem?.payment_type_category_pattern ===
                              'multiple' && (
                              <Box className="d-flex align-center gap-5 pt16">
                                <Typography className="title-text fw600 text-capital text-ellipsis-line">
                                  {citem?.payment_type_category_title}
                                </Typography>
                                {citem?.payment_type_category_remarks &&
                                citem?.payment_type_category_remarks?.trim() !==
                                  '' ? (
                                  <Tooltip
                                    arrow
                                    title={citem?.payment_type_category_remarks}
                                  >
                                    <InfoIcon className="info-icon-wrap cursor-pointer" />
                                  </Tooltip>
                                ) : (
                                  <></>
                                )}
                              </Box>
                            )}
                            {citem?.payment_type_category_pattern ===
                            'multiple' ? (
                              <>
                                {citem?.categoryBranchValue &&
                                  citem?.categoryBranchValue?.length > 0 &&
                                  citem?.categoryBranchValue?.map(
                                    (csitem, subIndex) => {
                                      return (
                                        <>
                                          <Box className="card-details-section pt16">
                                            <Box>
                                              <Typography className="title-text text-ellipsis-line2">
                                                {csitem?.first_field_value}
                                              </Typography>
                                            </Box>
                                            <Box className="amount-field">
                                              <CustomTextField
                                                value={csitem?.wsr_amount}
                                                onChange={(e) => {
                                                  setIsSubmit(false);
                                                  const income = DsrDataIncome;
                                                  if (
                                                    income[dindex]
                                                      .payment_type_category[
                                                      cindex
                                                    ].categoryBranchValue[
                                                      subIndex
                                                    ].old_wsr_amount ===
                                                      undefined &&
                                                    isEdit
                                                  ) {
                                                    income[
                                                      dindex
                                                    ].payment_type_category[
                                                      cindex
                                                    ].categoryBranchValue[
                                                      subIndex
                                                    ].old_wsr_amount =
                                                      csitem?.wsr_amount;
                                                  }
                                                  income[
                                                    dindex
                                                  ].payment_type_category[
                                                    cindex
                                                  ].categoryBranchValue[
                                                    subIndex
                                                  ].wsr_amount = e.target.value;
                                                  setDsrDataIncome(income);
                                                  setRandom(Math.random());
                                                }}
                                                name={`amount ${subIndex}`}
                                                placeholder={
                                                  item?.has_field_currency
                                                    ? 'Amount'
                                                    : 'Enter value'
                                                }
                                                className={'amount-textfield'}
                                                onInput={(e) => {
                                                  // Remove non-numeric characters
                                                  let value = e.target.value;
                                                  if (
                                                    value === '' ||
                                                    /^\d*\.?\d{0,2}$/.test(
                                                      value
                                                    )
                                                  ) {
                                                    e.target.value = value;
                                                  } else {
                                                    e.target.value =
                                                      value.slice(0, -1);
                                                  }
                                                }}
                                                InputProps={{
                                                  ...(item?.has_field_currency && {
                                                    startAdornment: (
                                                      <InputAdornment position="start">
                                                        <Typography className="title-text currency">
                                                          {currency?.symbol
                                                            ? currency?.symbol
                                                            : '£'}
                                                        </Typography>{' '}
                                                      </InputAdornment>
                                                    ),
                                                  }),
                                                }}
                                              />
                                            </Box>
                                          </Box>
                                        </>
                                      );
                                    }
                                  )}
                              </>
                            ) : (
                              <>
                                <Box className="card-details-section pt16">
                                  <Box className="d-flex align-center gap-5">
                                    <Typography className="title-text fw600 text-capital text-ellipsis-line2">
                                      {citem?.payment_type_category_title}
                                    </Typography>
                                    {citem?.payment_type_category_remarks &&
                                    citem?.payment_type_category_remarks?.trim() !==
                                      '' ? (
                                      <Tooltip
                                        arrow
                                        title={
                                          citem?.payment_type_category_remarks
                                        }
                                      >
                                        <InfoIcon className="info-icon-wrap cursor-pointer" />
                                      </Tooltip>
                                    ) : (
                                      <></>
                                    )}
                                  </Box>
                                  <Box className="amount-field">
                                    <CustomTextField
                                      value={citem?.wsr_amount}
                                      onChange={(e) => {
                                        setIsSubmit(false);
                                        const income = DsrDataIncome;
                                        if (
                                          income[dindex].payment_type_category[
                                            cindex
                                          ].old_wsr_amount === undefined &&
                                          isEdit
                                        ) {
                                          income[dindex].payment_type_category[
                                            cindex
                                          ].old_wsr_amount = citem?.wsr_amount;
                                        }
                                        income[dindex].payment_type_category[
                                          cindex
                                        ].wsr_amount = e.target.value;
                                        setDsrDataIncome(income);
                                        setRandom(Math.random());
                                      }}
                                      name={`amount ${cindex}`}
                                      placeholder={
                                        item?.has_field_currency
                                          ? 'Amount'
                                          : 'Enter value'
                                      }
                                      className={'amount-textfield'}
                                      onInput={(e) => {
                                        let value = e.target.value;
                                        if (
                                          value === '' ||
                                          /^\d*\.?\d{0,2}$/.test(value)
                                        ) {
                                          e.target.value = value;
                                        } else {
                                          e.target.value = value.slice(0, -1);
                                        }
                                      }}
                                      InputProps={{
                                        ...(item?.has_field_currency && {
                                          startAdornment: (
                                            <InputAdornment position="start">
                                              <Typography className="title-text currency">
                                                {currency?.symbol
                                                  ? currency?.symbol
                                                  : '£'}
                                              </Typography>{' '}
                                            </InputAdornment>
                                          ),
                                        }),
                                      }}
                                    />
                                  </Box>
                                </Box>
                              </>
                            )}
                          </Box>
                        );
                      })}
                    <>
                      <Box className="card-details-section pt16 totol-dsr">
                        <Box>
                          <Typography className="title-text fw600">
                            Total
                          </Typography>
                        </Box>
                        <Box className="amount-field">
                          <CustomTextField
                            value={TotalOfWSRObj(DsrDataIncome, dindex)}
                            name={`amount `}
                            disabled={true}
                            placeholder={
                              item?.has_field_currency
                                ? 'Amount'
                                : 'Enter value'
                            }
                            className={'amount-textfield'}
                            onInput={(e) => {
                              // Remove non-numeric characters
                              let value = e.target.value;
                              if (
                                value === '' ||
                                /^\d*\.?\d{0,2}$/.test(value)
                              ) {
                                e.target.value = value;
                              } else {
                                e.target.value = value.slice(0, -1);
                              }
                            }}
                            InputProps={{
                              ...(item?.has_field_currency && {
                                startAdornment: (
                                  <InputAdornment position="start">
                                    <Typography className="title-text currency">
                                      {currency?.symbol
                                        ? currency?.symbol
                                        : '£'}
                                    </Typography>{' '}
                                  </InputAdornment>
                                ),
                              }),
                            }}
                          />
                        </Box>
                      </Box>
                      {IsRemarkView && (
                        <Box className="card-details-section  card-details-remark-section pt16">
                          <Box>
                            <Typography className="title-text fw600">
                              Remark
                            </Typography>
                          </Box>
                          <Box className="amount-field">
                            <CustomTextField
                              value={item?.payment_type_remark}
                              onChange={(e) => {
                                if (
                                  e.target.value === '' ||
                                  e.target.value?.length < 161
                                ) {
                                  setIsSubmit(false);
                                  const income = DsrDataIncome;
                                  income[dindex].payment_type_remark =
                                    e.target.value;
                                  setDsrDataIncome(income);
                                  setRandom(Math.random());
                                }
                              }}
                              onPaste={(e) => {
                                if (
                                  e.target.value === '' ||
                                  e.target.value?.length < 161
                                ) {
                                  setIsSubmit(false);
                                  const income = DsrDataIncome;
                                  income[dindex].payment_type_remark =
                                    e.target.value;
                                  setDsrDataIncome(income);
                                  setRandom(Math.random());
                                }
                              }}
                              multiline
                              rows={2}
                              name={`amount ${dindex}`}
                              placeholder={'Enter value'}
                              className={
                                'amount-textfield additional-textfeild'
                              }
                            />
                          </Box>
                          <Typography className="sub-title-text text-align-end">
                            {(item?.payment_type_remark?.length
                              ? item?.payment_type_remark?.length
                              : 0) + ' / 160'}
                          </Typography>
                        </Box>
                      )}
                    </>
                  </Box>
                );
              })}
          </Box>
          {VatIsView && (
            <>
              <Box className="vat-calculation-section">
                <Box className="vat-calculation">
                  <Typography className="body-text fw600 text-capital vat-text text-underline">
                    VAT
                  </Typography>
                  <Box className="card-details-section pt16">
                    <Box>
                      <Typography className="title-text fw600">
                        Total of All Income
                      </Typography>
                    </Box>
                    <Box>
                      <Typography className="title-text fw600 text-align">
                        {TotalOfWSRArray(DsrDataIncome)}
                      </Typography>
                    </Box>
                  </Box>
                  <Box className="card-details-section pt16">
                    <Box>
                      <Typography className="title-text">{`Non vatable Amount`}</Typography>
                    </Box>
                    <Box className="amount-field">
                      <CustomTextField
                        value={VAT?.NoneVat}
                        onChange={(e) => {
                          // if (
                          //   e.target.value === '' ||
                          //   parseFloat(e.target.value) <=
                          //     TotalOfWSRArray(DsrDataIncome)
                          // ) {
                          let vatcal = VAT;
                          vatcal.NoneVat = e.target.value;
                          if (
                            ValueOfTotal(parseFloat(VAT?.VAT1)) !== undefined
                          ) {
                            vatcal.VAT1 =
                              // TotalOfWSRArray(DsrDataIncome) -
                              //   ValueOfTotal(parseFloat(e.target.value)) -
                              //   ValueOfTotal(parseFloat(VAT?.VAT2)) >
                              // 0
                              //   ?
                              (
                                TotalOfWSRArray(DsrDataIncome) -
                                ValueOfTotal(parseFloat(e.target.value)) -
                                ValueOfTotal(parseFloat(VAT?.VAT2))
                              ).toFixed(2);
                            // : 0;
                          } else {
                            vatcal.VAT1 = '';
                          }
                          setVAT(vatcal);
                          setRandomvat(Math.random());
                          setIsSubmit(false);
                          // }
                        }}
                        name={`vat10`}
                        placeholder="Amount"
                        className={'amount-textfield'}
                        onInput={(e) => {
                          let value = e.target.value;
                          if (value === '' || /^-?\d*\.?\d{0,2}$/.test(value)) {
                            e.target.value = value;
                          } else {
                            e.target.value = value.slice(0, -1);
                          }
                        }}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <Typography className="title-text">
                                {currency?.symbol ? currency?.symbol : '£'}
                              </Typography>{' '}
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Box>
                  </Box>
                  <Box className="card-details-section pt16">
                    <Box>
                      <Typography className="title-text">{`${
                        VatValue && VatValue?.[0] ? VatValue?.[0] : 0
                      } % VAT Amount`}</Typography>
                    </Box>
                    <Box className="amount-field">
                      <CustomTextField
                        value={VAT?.VAT1}
                        onChange={(e) => {
                          // if (
                          //   e.target.value === '' ||
                          //   parseFloat(e.target.value) <=
                          //     TotalOfWSRArray(DsrDataIncome)
                          // ) {
                          let vatcal = VAT;
                          vatcal.VAT1 = e.target.value;
                          if (
                            ValueOfTotal(parseFloat(VAT?.VAT2)) !== undefined
                          ) {
                            vatcal.VAT2 =
                              // TotalOfWSRArray(DsrDataIncome) -
                              //   ValueOfTotal(parseFloat(e.target.value)) -
                              //   ValueOfTotal(parseFloat(VAT?.NoneVat)) >
                              // 0
                              //   ?
                              (
                                TotalOfWSRArray(DsrDataIncome) -
                                ValueOfTotal(parseFloat(e.target.value)) -
                                ValueOfTotal(parseFloat(VAT?.NoneVat))
                              ).toFixed(2);
                            // : 0;
                          } else {
                            vatcal.VAT2 = '';
                          }
                          setVAT(vatcal);
                          setRandomvat(Math.random());
                          setIsSubmit(false);
                          // }
                        }}
                        name={`vat0`}
                        placeholder="Amount"
                        className={'amount-textfield'}
                        onInput={(e) => {
                          let value = e.target.value;
                          if (value === '' || /^-?\d*\.?\d{0,2}$/.test(value)) {
                            e.target.value = value;
                          } else {
                            e.target.value = value.slice(0, -1);
                          }
                        }}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <Typography className="title-text">
                                {currency?.symbol ? currency?.symbol : '£'}
                              </Typography>{' '}
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Box>
                  </Box>
                  <Box className="card-details-section pt16">
                    <Box>
                      <Typography className="title-text">{`${
                        VatValue && VatValue?.[1] ? VatValue?.[1] : 0
                      } % VAT Amount`}</Typography>
                    </Box>
                    <Box className="amount-field">
                      <CustomTextField
                        value={VAT?.VAT2}
                        onChange={(e) => {
                          // if (
                          //   e.target.value === '' ||
                          //   parseFloat(e.target.value) <=
                          //     TotalOfWSRArray(DsrDataIncome)
                          // ) {
                          let vatcal = VAT;
                          vatcal.VAT2 = e.target.value;
                          if (
                            ValueOfTotal(parseFloat(VAT?.VAT1)) !== undefined
                          ) {
                            vatcal.VAT1 =
                              // TotalOfWSRArray(DsrDataIncome) -
                              //   ValueOfTotal(parseFloat(e.target.value)) -
                              //   ValueOfTotal(parseFloat(VAT?.NoneVat)) >
                              // 0
                              //   ?
                              (
                                TotalOfWSRArray(DsrDataIncome) -
                                ValueOfTotal(parseFloat(e.target.value)) -
                                ValueOfTotal(parseFloat(VAT?.NoneVat))
                              ).toFixed(2);
                            // : 0;
                          } else {
                            vatcal.VAT1 = '';
                          }
                          setVAT(vatcal);
                          setRandomvat(Math.random());
                          setIsSubmit(false);
                          // }
                        }}
                        name={`vat20`}
                        placeholder="Amount"
                        className={'amount-textfield'}
                        onInput={(e) => {
                          // Remove non-numeric characters
                          let value = e.target.value;
                          if (value === '' || /^-?\d*\.?\d{0,2}$/.test(value)) {
                            e.target.value = value;
                          } else {
                            e.target.value = value.slice(0, -1);
                          }
                        }}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <Typography className="title-text">
                                {currency?.symbol ? currency?.symbol : '£'}
                              </Typography>{' '}
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Box>
                  </Box>
                  <Box className="card-details-section pt16">
                    <Box>
                      <Typography className="title-text fw600">
                        Total VAT amount
                      </Typography>
                    </Box>
                    <Box>
                      <Typography className="title-text fw600 text-align">
                        {ValueOfTotal(parseFloat(VAT?.VAT2)) &&
                        VatValue &&
                        VatValue?.[1]
                          ? removeVAT(
                              ValueOfTotal(parseFloat(VAT?.VAT2)),
                              VatValue?.[1]
                            )?.vatAmount
                          : 0}
                      </Typography>
                    </Box>
                  </Box>
                  <Box className="card-details-section pt16">
                    <Box>
                      <Typography className="title-text fw600">
                        Difference VAT amount
                      </Typography>
                    </Box>
                    <Box className="difference-grid">
                      <Box className="amount-field">
                        <CustomTextField
                          value={VAT?.diff1}
                          onChange={(e) => {
                            let vatcal = VAT;
                            vatcal.diff1 = e.target.value;
                            if (
                              removeVAT(
                                ValueOfTotal(parseFloat(VAT?.VAT2)),
                                VatValue?.[1]
                              )?.vatAmount -
                              ValueOfTotal(parseFloat(e.target.value))
                            ) {
                              vatcal.diff2 = Math.abs(
                                (
                                  removeVAT(
                                    ValueOfTotal(parseFloat(VAT?.VAT2)),
                                    VatValue?.[1]
                                  )?.vatAmount -
                                  ValueOfTotal(parseFloat(e.target.value))
                                ).toFixed(2)
                              );
                            } else {
                              vatcal.diff2 = 0;
                            }
                            setVAT(vatcal);
                            setRandomvat(Math.random());
                            setIsSubmit(false);
                          }}
                          name={`diff1`}
                          placeholder="Amount"
                          className={'amount-textfield'}
                          onInput={(e) => {
                            let value = e.target.value;
                            if (value === '' || /^\d*\.?\d{0,2}$/.test(value)) {
                              e.target.value = value;
                            } else {
                              e.target.value = value.slice(0, -1);
                            }
                          }}
                        />
                      </Box>
                      <Box className="amount-field">
                        <CustomTextField
                          value={
                            VAT?.diff1 && VAT?.diff1 !== ''
                              ? Math.abs(
                                  (
                                    removeVAT(
                                      ValueOfTotal(parseFloat(VAT?.VAT2)),
                                      VatValue?.[1]
                                    )?.vatAmount -
                                    ValueOfTotal(parseFloat(VAT?.diff1))
                                  ).toFixed(2)
                                )
                              : ''
                          }
                          name={`diff2`}
                          placeholder="Amount"
                          className={'amount-textfield'}
                          onInput={(e) => {
                            let value = e.target.value;
                            if (value === '' || /^\d*\.?\d{0,2}$/.test(value)) {
                              e.target.value = value;
                            } else {
                              e.target.value = value.slice(0, -1);
                            }
                          }}
                          disabled={true}
                        />
                      </Box>
                    </Box>
                  </Box>
                  <Box className="card-details-section pt16">
                    <Box>
                      <Typography className="title-text fw600">
                        Total without VAT
                      </Typography>
                    </Box>
                    <Box>
                      <Typography className="title-text fw600 text-align">
                        {(ValueOfTotal(parseFloat(VAT?.VAT2)) ||
                          ValueOfTotal(parseFloat(VAT?.VAT1)) ||
                          ValueOfTotal(parseFloat(VAT?.NoneVat))) &&
                        VatValue &&
                        VatValue?.[1]
                          ? TotalWithoutVAT()
                          : 0}
                      </Typography>
                    </Box>
                  </Box>
                </Box>
              </Box>
              {(totalofALLVat() !== TotalOfWSRArray(DsrDataIncome) ||
                (isNaN(parseFloat(VAT?.VAT1)) &&
                  isNaN(parseFloat(VAT?.VAT2)) &&
                  isNaN(parseFloat(VAT?.NoneVat)) &&
                  TotalOfWSRArray(DsrDataIncome) !== 0)) &&
                isSubmit && (
                  <Typography
                    variant="body2"
                    color="error"
                    className="field-error"
                  >
                    The total of the Non-vatable Amount, 0% VAT Amount, and 20%
                    VAT Amount must equal the Total Income. Please ensure that
                    all values are balanced for accuracy.
                  </Typography>
                )}
            </>
          )}
        </>
      )}
      {DsrDataOther &&
        DsrDataOther?.length > 0 &&
        DsrDataIncome &&
        DsrDataIncome?.length === 0 && (
          <FormGroup className="form-checkbox">
            <FormControlLabel
              control={
                <Checkbox
                  className="check-box "
                  icon={<CheckBoxOutlineBlankIcon className="uncheck-icon" />}
                  checkedIcon={<CheckBoxIcon className="check-icon" />}
                  disableRipple
                />
              }
              name="weekly"
              className="check-box-form form-row max-content sub-title-text"
              checked={IsRemarkView}
              onChange={(e) => {
                setIsRemarkView(e.target.checked);
              }}
              label="View Remark"
            />
          </FormGroup>
        )}
      <Box className="dsr-add-view pt16 mb32">
        {DsrDataOther &&
          DsrDataOther?.length > 0 &&
          DsrDataOther?.map((item, dindex) => {
            return (
              <Box className="dsr-main-category other">
                <Typography className="body-text fw600 text-capital main-category-text text-ellipsis-line other-text">
                  {item?.payment_type_title}
                </Typography>
                <span className="category-status other">
                  <span className="sub-title-text category-ongoing fw600 text-capital">
                    Other
                  </span>
                </span>
                {item?.payment_type_category?.length > 0 &&
                  item?.payment_type_category?.map((citem, cindex) => {
                    return (
                      <Box className="">
                        {' '}
                        {citem?.payment_type_category_pattern ===
                          'multiple' && (
                          <Box className="d-flex align-center gap-5 pt16">
                            <Typography className="title-text fw600 text-capital text-ellipsis-line">
                              {citem?.payment_type_category_title}
                            </Typography>
                            {citem?.payment_type_category_remarks &&
                            citem?.payment_type_category_remarks?.trim() !==
                              '' ? (
                              <Tooltip
                                arrow
                                title={citem?.payment_type_category_remarks}
                              >
                                <InfoIcon className="info-icon-wrap cursor-pointer" />
                              </Tooltip>
                            ) : (
                              <></>
                            )}
                          </Box>
                        )}
                        {citem?.payment_type_category_pattern === 'multiple' ? (
                          <>
                            {citem?.categoryBranchValue &&
                              citem?.categoryBranchValue?.length > 0 &&
                              citem?.categoryBranchValue?.map(
                                (csitem, subIndex) => {
                                  return (
                                    <>
                                      <Box className="card-details-section pt16">
                                        <Box>
                                          <Typography className="title-text text-ellipsis-line2">
                                            {csitem?.first_field_value}
                                          </Typography>
                                        </Box>
                                        <Box className="amount-field">
                                          <CustomTextField
                                            value={csitem?.wsr_amount}
                                            onChange={(e) => {
                                              const income = DsrDataOther;
                                              if (
                                                income[dindex]
                                                  .payment_type_category[cindex]
                                                  .categoryBranchValue[subIndex]
                                                  .old_wsr_amount ===
                                                  undefined &&
                                                isEdit
                                              ) {
                                                income[
                                                  dindex
                                                ].payment_type_category[
                                                  cindex
                                                ].categoryBranchValue[
                                                  subIndex
                                                ].old_wsr_amount =
                                                  csitem?.wsr_amount;
                                              }
                                              income[
                                                dindex
                                              ].payment_type_category[
                                                cindex
                                              ].categoryBranchValue[
                                                subIndex
                                              ].wsr_amount = e.target.value;
                                              setDsrDataOther(income);
                                              setRandomother(Math.random());
                                            }}
                                            name={`amount ${subIndex}`}
                                            placeholder={
                                              item?.has_field_currency
                                                ? 'Amount'
                                                : 'Enter value'
                                            }
                                            className={'amount-textfield'}
                                            onInput={(e) => {
                                              // e.target.value = e.target.value.replace(
                                              //   /[^0-9]/g,
                                              //   ''
                                              // ); // Remove non-numeric characters
                                              let value = e.target.value;
                                              if (
                                                value === '' ||
                                                /^\d*\.?\d{0,2}$/.test(value)
                                              ) {
                                                e.target.value = value;
                                              } else {
                                                e.target.value = value.slice(
                                                  0,
                                                  -1
                                                );
                                              }
                                            }}
                                            InputProps={{
                                              ...(item?.has_field_currency && {
                                                startAdornment: (
                                                  <InputAdornment position="start">
                                                    <Typography className="title-text currency">
                                                      {currency?.symbol
                                                        ? currency?.symbol
                                                        : '£'}
                                                    </Typography>{' '}
                                                  </InputAdornment>
                                                ),
                                              }),
                                            }}
                                          />
                                        </Box>
                                      </Box>
                                    </>
                                  );
                                }
                              )}
                          </>
                        ) : (
                          <>
                            <Box className="card-details-section pt16">
                              <Box className="d-flex align-center gap-5">
                                <Typography className="title-text fw600 text-capital text-ellipsis-line2">
                                  {citem?.payment_type_category_title}
                                </Typography>
                                {citem?.payment_type_category_remarks &&
                                citem?.payment_type_category_remarks?.trim() !==
                                  '' ? (
                                  <Tooltip
                                    arrow
                                    title={citem?.payment_type_category_remarks}
                                  >
                                    <InfoIcon className="info-icon-wrap cursor-pointer" />
                                  </Tooltip>
                                ) : (
                                  <></>
                                )}
                              </Box>
                              <Box className="amount-field">
                                <CustomTextField
                                  value={citem?.wsr_amount}
                                  onChange={(e) => {
                                    const income = DsrDataOther;
                                    if (
                                      income[dindex].payment_type_category[
                                        cindex
                                      ].old_wsr_amount === undefined &&
                                      isEdit
                                    ) {
                                      income[dindex].payment_type_category[
                                        cindex
                                      ].old_wsr_amount = citem?.wsr_amount;
                                    }
                                    income[dindex].payment_type_category[
                                      cindex
                                    ].wsr_amount = e.target.value;
                                    setDsrDataOther(income);
                                    setRandomother(Math.random());
                                  }}
                                  name={`amount ${cindex}`}
                                  placeholder={
                                    citem?.has_field_currency
                                      ? 'Amount'
                                      : 'Enter value'
                                  }
                                  className={'amount-textfield'}
                                  onInput={(e) => {
                                    // Remove non-numeric characters
                                    let value = e.target.value;
                                    if (
                                      value === '' ||
                                      /^\d*\.?\d{0,2}$/.test(value)
                                    ) {
                                      e.target.value = value;
                                    } else {
                                      e.target.value = value.slice(0, -1);
                                    }
                                  }}
                                  InputProps={{
                                    ...(citem?.has_field_currency && {
                                      startAdornment: (
                                        <InputAdornment position="start">
                                          <Typography className="title-text currency">
                                            {currency?.symbol
                                              ? currency?.symbol
                                              : '£'}
                                          </Typography>{' '}
                                        </InputAdornment>
                                      ),
                                    }),
                                  }}
                                />
                              </Box>
                            </Box>
                          </>
                        )}
                      </Box>
                    );
                  })}
                <>
                  <Box className="card-details-section pt16 totol-dsr">
                    <Box>
                      <Typography className="title-text fw600">
                        Total
                      </Typography>
                    </Box>
                    <Box className="amount-field">
                      <CustomTextField
                        value={TotalOfWSRObj(DsrDataOther, dindex)}
                        disabled={true}
                        name={`amount `}
                        placeholder={
                          item?.has_field_currency ? 'Amount' : 'Enter value'
                        }
                        className={'amount-textfield'}
                        onInput={(e) => {
                          // Remove non-numeric characters
                          let value = e.target.value;
                          if (value === '' || /^\d*\.?\d{0,2}$/.test(value)) {
                            e.target.value = value;
                          } else {
                            e.target.value = value.slice(0, -1);
                          }
                        }}
                        InputProps={{
                          ...(item?.has_field_currency && {
                            startAdornment: (
                              <InputAdornment position="start">
                                <Typography className="title-text currency">
                                  {currency?.symbol ? currency?.symbol : '£'}
                                </Typography>{' '}
                              </InputAdornment>
                            ),
                          }),
                        }}
                      />
                    </Box>
                  </Box>
                  {IsRemarkView && (
                    <Box className="card-details-section  card-details-remark-section pt16">
                      <Box>
                        <Typography className="title-text fw600">
                          Remark
                        </Typography>
                      </Box>
                      <Box className="amount-field">
                        <CustomTextField
                          value={item?.payment_type_remark}
                          onChange={(e) => {
                            if (
                              e.target.value === '' ||
                              e.target.value?.length < 161
                            ) {
                              setIsSubmit(false);
                              const income = DsrDataOther;
                              income[dindex].payment_type_remark =
                                e.target.value;
                              setDsrDataOther(income);
                              setRandomother(Math.random());
                            }
                          }}
                          onPaste={(e) => {
                            if (
                              e.target.value === '' ||
                              e.target.value?.length < 161
                            ) {
                              setIsSubmit(false);
                              const income = DsrDataOther;
                              income[dindex].payment_type_remark =
                                e.target.value;
                              setDsrDataOther(income);
                              setRandomother(Math.random());
                            }
                          }}
                          multiline
                          rows={2}
                          name={`amount ${dindex}`}
                          placeholder={'Enter value'}
                          className={'amount-textfield additional-textfeild'}
                        />
                      </Box>
                      <Typography className="sub-title-text text-align-end">
                        {(item?.payment_type_remark?.length
                          ? item?.payment_type_remark?.length
                          : 0) + ' / 160'}
                      </Typography>
                    </Box>
                  )}
                </>
              </Box>
            );
          })}
      </Box>
    </Box>
  );
}
