import React, { useState, useRef } from 'react';
import { Box, IconButton, Typography } from '@mui/material';
import CustomButton from '@/components/UI/CustomButton';
import Cropper from 'react-cropper';
import { Add, Remove } from '@mui/icons-material';
import DialogBox from '@/components/UI/Modalbox';
import 'cropperjs/dist/cropper.css';
import './cropimage.scss';

const CropImage = ({ file, getCropData, onhandleclose, isProfile }) => {
  const [cropper, setCropper] = useState();
  const [crop, setCrop] = useState(false);
  const [zoom, setZoom] = useState(0);
  const cropperRef = useRef(null);
  const [disModal, setDisModal] = useState(false);
  // const [cropData, setCropData] = useState(null);
  const zoomIn = () => {
    setZoom((parseFloat(zoom) + 0.1).toFixed(1));
    if (cropperRef.current) {
      cropperRef.current.cropper.zoom(0.1);
    }
  };

  const zoomOut = () => {
    setZoom((parseFloat(zoom) - 0.1).toFixed(1));
    if (cropperRef.current) {
      cropperRef.current.cropper.zoom(-0.1);
    }
  };

  const customZoom = (zoomValue) => {
    setZoom(zoomValue);
    if (cropperRef.current) {
      cropperRef.current.cropper.zoom((zoomValue - zoom).toFixed(1));
    }
  };
  const discardModalDetails = () => {
    return (
      <Box className="block-top-wrap">
        <Typography className="body-text color-gray">
          Are you sure you want to discard your changes?
        </Typography>

        <Box className="form-actions-btn">
          <CustomButton
            fullWidth
            variant="outlined"
            title="Cancel"
            onClick={() => handleDiscardModal()}
          />
          <CustomButton
            fullWidth
            variant="contained"
            title={'okay'}
            onClick={() => {
              handleDiscardModal();
              onhandleclose();
            }}
          />
        </Box>
      </Box>
    );
  };
  const handleDiscardModal = () => {
    setDisModal(!disModal);
  };
  const cropImage = () => {
    // setZoom(0)
    if (cropperRef.current) {
      // const croppedData = cropperRef.current.cropper
      //   .getCroppedCanvas()
      //   .toDataURL();
      // setCropData(croppedData);
    }
  };

  const undoCrop = () => {
    setZoom(0);
    // setCropData(null);
    if (cropperRef.current) {
      cropperRef.current.cropper.reset();
    }
  };

  return (
    <Box className="crop-image">
      <Box className={crop ? 'cropped-image' : 'without-crop'}>
        <Cropper
          ref={cropperRef}
          style={{ height: 300, maxWidth: '100%' }}
          // zoomable={false}
          aspectRatio={1}
          initialAspectRatio={1}
          // src={crop ? cropData : file?.preview}
          src={file?.preview}
          viewMode={1}
          // minCropBoxHeight={10}
          // minCropBoxWidth={10}
          background={false}
          responsive={true}
          autoCropArea={1}
          className={isProfile && 'profile-cropper'}
          checkOrientation={false}
          onInitialized={(instance) => {
            setCropper(instance);
          }}
          guides={true}
          zoomOnWheel={false}
          dragMode="move"
          cropBoxMovable={false}
          cropBoxResizable={false}
        />
      </Box>
      <Box className="crop-image-top">
        <Box className="zoom-plus-minus">
          <Box className="icon-btn">
            <IconButton
              disabled={zoom > 0 ? false : true}
              className={zoom > 0 ? '' : 'disabled-btn'}
              onClick={() => zoom > 0 && zoomOut()}
            >
              <Remove />
            </IconButton>
          </Box>
          <Box className="zoom-bar">
            <input
              type="range"
              min={0}
              max={1}
              step={0.1}
              value={zoom}
              // min="0"
              // max="1"
              // step="0.1"
              defaultValue={0}
              onChange={(e) => {
                customZoom(e.target.value);
              }}
            />
          </Box>
          <Box className="icon-btn">
            <IconButton
              disabled={zoom < 1 ? false : true}
              className={zoom < 1 ? '' : 'disabled-btn'}
              onClick={() => zoom < 1 && zoomIn()}
            >
              <Add />
            </IconButton>
          </Box>
        </Box>
      </Box>
      <Box className="form-actions-btn justify-space-between">
        <CustomButton
          fullWidth
          variant="outlined"
          title={!crop ? 'Crop photo' : 'Undo crop'}
          onClick={() => {
            if (!crop) {
              cropImage();
              setCrop(!crop);
            } else {
              undoCrop();
              setCrop(!crop);
            }
          }}
        />
        <Box className="d-flex gap-sm">
          <CustomButton
            fullWidth
            variant="outlined"
            title="Cancel"
            onClick={() => handleDiscardModal()}
          />
          <CustomButton
            fullWidth
            variant="contained"
            title={'Save'}
            onClick={() => getCropData(cropper)}
          />
        </Box>
      </Box>
      <DialogBox
        handleClose={() => handleDiscardModal()}
        open={disModal}
        title={'Discard changes ?'}
        className="small-dialog-box-container"
        content={discardModalDetails()}
      />
    </Box>
  );
};

export default CropImage;
