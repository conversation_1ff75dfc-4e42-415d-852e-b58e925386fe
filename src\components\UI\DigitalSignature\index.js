import React, { useState, useRef } from 'react';
import { Box, Typography } from '@mui/material';
import SignaturePad from 'react-signature-canvas';
import CustomButton from '@/components/UI/button';
import './digitalsignature.scss';

const DigitalSignature = ({
  setSignValue,
  formikRef,
  setSign,
  setSignApi,
  isSetapi,
}) => {
  // const [imageURL, setImageURL] = useState(null);
  const [error, setError] = useState('');
  const sigCanvas = useRef({});

  const clear = () => {
    sigCanvas.current.clear();
    setError('');
  };
  const save = () => {
    if (sigCanvas.current.isEmpty()) {
      setError('This field is required.');
      return;
    }

    // setImageURL(sigCanvas.current.getTrimmedCanvas().toDataURL('image/png'));

    setSignValue({
      url: sigCanvas.current.getTrimmedCanvas().toDataURL('image/png'),
    });
    formikRef.current.setFieldValue(
      'emp_sign',
      sigCanvas.current.getTrimmedCanvas().toDataURL('image/png')
    );
    isSetapi && setSignApi(false);
    setSign(false);
    setError('');
  };
  return (
    <Box>
      <SignaturePad
        ref={sigCanvas}
        canvasProps={{
          className: 'signatureCanvas',
        }}
      />
      {error && (
        <Typography variant="body2" color="error" className="field-error">
          {error}
        </Typography>
      )}
      <Box className="create-cancel-button pt32">
        <CustomButton
          fullWidth
          className="p16 secondary-button"
          fontWeight="600"
          type="submit"
          variant="contained"
          background="#FFFFFF"
          backgroundhover="#39596e"
          colorhover="#FFFFFF"
          title="Clear"
          onClick={clear}
        />
        <CustomButton
          fullWidth
          className="p16"
          type="submit"
          fontWeight="600"
          variant="contained"
          background="#39596e"
          backgroundhover="#FFFFFF"
          colorhover="#000000"
          onClick={save}
          title="Save"
        />
      </Box>
      {/* {imageURL ? (
        <img
          src={imageURL}
          alt="my signature"
          style={{
            display: 'block',
            margin: '0 auto',
            border: '1px solid black',
            width: '150px'
          }}
        />
      ) : null} */}
    </Box>
  );
};

export default DigitalSignature;
