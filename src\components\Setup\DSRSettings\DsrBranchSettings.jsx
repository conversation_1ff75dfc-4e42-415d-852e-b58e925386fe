'use client';
import React, { useContext, useEffect, useState } from 'react';
import { URLS } from '@/helper/constants/urls';
import axiosInstance from '@/helper/axios/axiosInstance';
import { setApiMessage } from '@/helper/common/commonFunctions';
import DSRBranchCategories from './DSRBranchCategories';
import AuthContext from '@/helper/authcontext';
import { fetchFromStorage } from '@/helper/context';
import { identifiers } from '@/helper/constants/identifier';

export default function DsrBranchSettingsPage() {
  const { userdata, authState, setUserdata, AllListsData } =
    useContext(AuthContext);

  const [dsrCatData, setdsrCatData] = useState([]);
  const [loader, setLoader] = useState(false);
  const [addDSR, setAddDSR] = useState('');
  const [selectedBranch, setSelectedBranch] = useState(
    AllListsData?.ActiveBranchList?.[0]?.value || ''
  );

  const getCategoriesPaymentList = async (isUpdate) => {
    !isUpdate && setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_PAYMENT_BRANCH_ALL_LIST + `${selectedBranch}`
      );
      if (status === 200 || status === 304) {
        setLoader(false);
        const catmodified = data?.data?.map((paymentType) => {
          return {
            ...paymentType,
            id: `a-${paymentType?.id}`,
            payment_type_category:
              paymentType?.payment_type_category &&
              paymentType?.payment_type_category?.length > 0
                ? paymentType?.payment_type_category.map((category) => {
                    return {
                      ...category,
                      categoryBranchValue:
                        category?.categoryBranchValue &&
                        category?.categoryBranchValue?.length > 0
                          ? category?.categoryBranchValue.map((branchValue) => {
                              return {
                                ...branchValue,
                                id: branchValue?.payment_type_category_branch_id,
                              };
                            })
                          : [],
                      catList:
                        category?.categoryBranchValue &&
                        category?.categoryBranchValue?.length > 0
                          ? category?.categoryBranchValue.map((branchValue) => {
                              return {
                                ...branchValue,
                                id: branchValue?.payment_type_category_branch_id,
                              };
                            })
                          : [],
                    };
                  })
                : [],
            catList:
              paymentType?.payment_type_category &&
              paymentType?.payment_type_category?.length > 0
                ? paymentType?.payment_type_category.map((category) => {
                    return {
                      ...category,
                      categoryBranchValue:
                        category?.categoryBranchValue &&
                        category?.categoryBranchValue?.length > 0
                          ? category?.categoryBranchValue.map((branchValue) => {
                              return {
                                ...branchValue,
                                id: branchValue?.payment_type_category_branch_id,
                              };
                            })
                          : [],
                      catList:
                        category?.categoryBranchValue &&
                        category?.categoryBranchValue?.length > 0
                          ? category?.categoryBranchValue.map((branchValue) => {
                              return {
                                ...branchValue,
                                id: branchValue?.payment_type_category_branch_id,
                              };
                            })
                          : [],
                    };
                  })
                : [],
          };
        });
        setdsrCatData(catmodified);
      }
    } catch (error) {
      setLoader(false);
      setdsrCatData([]);

      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const handleAddSubCategory = async (categories, isUpdate) => {
    const list = categories?.payment_type_category_field?.map((item) => {
      if (isUpdate) {
        return {
          category_value_id: item?.category_value_id
            ? item?.category_value_id
            : null,
          field_value: item?.field_value,
          payment_type_category_field_id: item?.payment_type_category_field_id
            ? item?.payment_type_category_field_id
            : null,
          payment_type_category_branch_id: item?.payment_type_category_branch_id
            ? item?.payment_type_category_branch_id
            : null,
        };
      } else {
        return {
          payment_type_category_field_id: item?.id ? item?.id : null,
          field_value: item?.field_value,
        };
      }
    });
    const requestData = {
      payment_type_category_id: categories?.cateogory_id,
      payment_type_category_value: list,
    };
    const ApiUrl = isUpdate
      ? URLS.UPDATE_SUB_PAYMENT
      : URLS.ADD_SUB_PAYMENT + selectedBranch;
    const method = isUpdate ? 'put' : 'post';
    try {
      const { status, data } = await axiosInstance[method](ApiUrl, requestData);
      if (status === 200) {
        getCategoriesPaymentList(true);
        setAddDSR('');
        setApiMessage('success', data?.message);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // Reorder main category
  const handleReorderBranchPayment = async (index, id, mainId) => {
    const requestData = {
      payment_type_category_branch_id: id,
      order: Number(index) + 1,
      payment_type_category_id: mainId,
    };
    try {
      const { status } = await axiosInstance.put(
        URLS?.PAYMENT_VALUE_ORDER,
        requestData
      );
      if (status === 200) {
        getCategoriesPaymentList(true);
        // setAddDSR();
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // Reorder main category
  const updateBranchActive = async (checked) => {
    const requestData = {
      checked_category_ids: checked,
      branch_id: selectedBranch,
    };
    try {
      const { status, data } = await axiosInstance.put(
        URLS?.PAYMENT_DEFAULT_ACTIVE,
        requestData
      );
      if (status === 200) {
        if (data?.status) {
          setApiMessage('success', data?.message);
          setTimeout(() => {
            getCategoriesPaymentList(true);
          }, 500);
        } else {
          setApiMessage('error', data?.message);
        }
        // setAddDSR();
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const oldBranch = fetchFromStorage(identifiers?.RedirectData)
    ? fetchFromStorage(identifiers?.RedirectData)
    : userdata;

  useEffect(() => {
    if (oldBranch?.IsFromUser === undefined && oldBranch?.page !== undefined) {
      setUserdata({ ...userdata, IsFromUser: true });
      saveToStorage(identifiers?.RedirectData, {
        ...oldBranch,
        IsFromUser: true,
      });
    }
  }, [oldBranch]);
  useEffect(() => {
    if (selectedBranch) {
      getCategoriesPaymentList(false);
    }
  }, [selectedBranch]);
  useEffect(() => {
    setSelectedBranch(AllListsData?.ActiveBranchList?.[0]?.value);
  }, [AllListsData?.ActiveBranchList]);
  return (
    <>
      <DSRBranchCategories
        loader={loader}
        dsrCatData={dsrCatData}
        AddSubCategory={handleAddSubCategory}
        AddDSR={addDSR}
        setAddDSR={setAddDSR}
        ReorderBranchPayment={handleReorderBranchPayment}
        getCategoriesPaymentList={getCategoriesPaymentList}
        updateBranchActive={updateBranchActive}
        AllListsData={AllListsData}
        selectedBranch={selectedBranch}
        setSelectedBranch={setSelectedBranch}
        authState={authState}
      />
    </>
  );
}
