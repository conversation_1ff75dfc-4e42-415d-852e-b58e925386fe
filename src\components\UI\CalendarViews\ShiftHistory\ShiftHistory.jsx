import { useEffect, useState } from 'react';
import axiosInstanceOrg from '@/helper/axios/axiosInstanceOrg';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { ROTA_URLS } from '@/helper/constants/urls';
import PreLoader from '@/components/UI/Loader';
import UserAvatar from '../../Avatar/UserAvatar';
import moment from 'moment';
import './shiftHistory.scss';

export const ShiftHistory = ({
  shiftId,
  staffListData,
  rolesList,
  departmentList,
  locationList,
}) => {
  const [isLoader, setIsLoader] = useState(false);
  const [historyList, setHistoryList] = useState([]);

  // Shift History List
  const fetchHistoryList = async () => {
    setIsLoader(true);
    try {
      const { status, data } = await axiosInstanceOrg.get(
        ROTA_URLS?.SHIFT_HISTORY_URL + `/${shiftId}`
      );

      if (status === 200) {
        setIsLoader(false);
        setHistoryList(data?.data?.history);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setIsLoader(false);
      setHistoryList([]);
    }
  };

  useEffect(() => {
    fetchHistoryList();
  }, []);

  // Sort logs by createdAt in descending order (newest first)
  const sortedLogs = [...historyList].sort(
    (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
  );

  // Function to format date using moment
  const formatDate = (dateString) => {
    return moment(dateString).format('D MMM YYYY [at] h:mm A');
  };

  // Function to get user name
  const getUserName = (userId) => {
    const filteredUser = staffListData?.find((user) => user?.id === userId);

    if (filteredUser) {
      return filteredUser?.user_full_name || 'Open Shifts';
    } else {
      return userId || 'Open Shifts';
    }
  };

  // Find the CREATE log for a specific shift to extract reference data
  const getCreateLog = (shiftId) => {
    return historyList?.find(
      (log) => log?.shiftId === shiftId && log?.action === 'CREATE'
    );
  };

  // Extract role, branch, department info from the CREATE log
  const getEntityNames = (shiftId, field, entityId) => {
    if (!entityId) return '';

    // Find the create log for this shift
    const createLog = getCreateLog(shiftId);
    if (field === 'branch') {
      const branchFilter = locationList?.find(
        (branch) => branch?.value === entityId
      );
      if (branchFilter) return branchFilter?.label;
    }
    if (field === 'department') {
      const departmentFilter = departmentList?.find(
        (department) => department?.value === entityId
      );
      if (departmentFilter) return departmentFilter?.label;
    }
    if (field === 'role') {
      const roleFilter = rolesList?.find((role) => role?.value === entityId);
      if (roleFilter) return roleFilter?.label;
    }

    if (!createLog) return `${entityId}`;

    // First try to find an explicit name in the log's changes
    const nameChange = createLog?.changes?.find(
      (c) => c?.field === `${field}Name`
    );

    if (nameChange?.newValue) return nameChange?.newValue;

    // Otherwise return a generic name
    return `${entityId}`;
  };

  // Function to get display content based on action type
  const getActionContent = (log) => {
    const full_name = log?.user?.user_full_name || 'Open Shifts';

    const userId = log?.changes.find((c) => c?.field === 'userId')?.newValue;
    const roleId = log?.changes.find((c) => c?.field === 'roleId')?.newValue;
    const branchId = log?.changes.find(
      (c) => c?.field === 'branchId'
    )?.newValue;
    const departmentId = log?.changes?.find(
      (c) => c?.field === 'departmentId'
    )?.newValue;

    const arrayOfEntities = [
      {
        id: 'isPublished',
        label: 'PUBLISHED',
        value: log?.changes?.find((c) => c?.field === 'isPublished')?.newValue
          ? 'Yes'
          : 'No',
      },
      {
        id: 'startTime',
        label: 'DATE',
        value: formatShiftDate(
          log?.changes?.find((c) => c?.field === 'startTime')?.newValue
        ),
      },
      log?.changes?.find((c) => c?.field === 'startTime')?.newValue &&
        log?.changes?.find((c) => c?.field === 'endTime')?.newValue && {
          id: 'startTime',
          label: 'TIME',
          value: formatShiftTime(
            log?.changes?.find((c) => c?.field === 'startTime')?.newValue,
            log?.changes?.find((c) => c?.field === 'endTime')?.newValue
          ),
        },
      {
        id: 'minutesBreak',
        label: 'BREAK',
        value: `${log?.changes?.find((c) => c?.field === 'minutesBreak')?.newValue} minutes`,
      },
      { id: 'userId', label: 'EMPLOYEE', value: getUserName(userId) },
      {
        id: 'branchId',
        label: 'LOCATION',
        value: getEntityNames(log?.shiftId, 'branch', branchId),
      },
      {
        id: 'departmentId',
        label: 'DEPARTMENT',
        value: getEntityNames(log?.shiftId, 'department', departmentId),
      },
      {
        id: 'roleId',
        label: 'ROLE',
        value: getEntityNames(log?.shiftId, 'role', roleId),
      },
      {
        id: 'notes',
        label: 'NOTES',
        value: log?.changes?.find((c) => c?.field === 'notes')?.newValue || '-',
      },
      log?.action !== 'CREATE' && {
        id: 'isDropped',
        label: 'DROPPED',
        value: log?.changes?.find((c) => c?.field === 'isDropped')?.newValue
          ? 'Yes'
          : 'No',
      },
      log?.action !== 'CREATE' && {
        id: 'isSwap',
        label: 'SWAP',
        value: log?.changes?.find((c) => c?.field === 'isSwap')?.newValue
          ? 'Yes'
          : 'No',
      },
    ];

    const filteredEntities = arrayOfEntities.filter((entity) =>
      log?.changes?.some((c) => c?.field === entity?.id)
    );

    switch (log.action) {
      // case 'CREATE':
      //   return {
      //     title: `Created by ${full_name}`,
      //     details: filteredEntities,
      //   };
      default:
        return {
          title: `${log?.action} by ${full_name}`,
          details: filteredEntities,
        };
    }
  };

  // Helper functions for formatting
  const formatShiftDate = (dateString) => {
    if (!dateString) return '';
    return moment(dateString).format('dddd D MMMM YYYY');
  };

  const formatShiftTime = (startTime, endTime) => {
    if (!startTime || !endTime) return '';
    return `${moment(startTime).format('HH:mm')} - ${moment(endTime).format('HH:mm')}`;
  };

  return (
    <div>
      {isLoader && <PreLoader />}
      <div className="history-log">
        {sortedLogs?.map((log, index) => {
          const content = getActionContent(log);
          return (
            <div key={log?.id} className="log-item">
              <div className="timeline">
                <div className="avatar">
                  <UserAvatar
                    name={log?.user?.user_full_name}
                    src={log?.user?.user_avatar_link}
                    classname=""
                  />
                </div>
                {index < sortedLogs?.length - 1 && <div className="line"></div>}
              </div>
              <div className="content">
                <div className="header">
                  <h3>{content?.title}</h3>
                  <p className="timestamp">{formatDate(log?.createdAt)}</p>
                </div>
                {content?.details?.length > 0 && (
                  <div className="details">
                    {content?.details?.map((detail, idx) => (
                      <div key={idx} className="detail-item">
                        <span className="history-label">{detail?.label}</span>
                        <span className="history-value">{detail?.value}</span>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
