import React from 'react';
import { Drawer, Box, Typography } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import './customdrawer.scss';

const CustomDrawer = ({
  className,
  anchor,
  open,
  onClose,
  title,
  content,
  subTitle,
}) => {
  return (
    <Drawer
      className={`custom-drawer-sec ${className}`}
      anchor={anchor}
      onClose={onClose}
      open={open}
      transitionDuration={600}
      SlideProps={{
        timeout: 600,
        easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
      }}
    >
      <Box
        className="custom-drawer-header d-flex align-center gap-sm"
        onClick={() => onClose()}
      >
        <CloseIcon className="cursor-pointer close-icon-wrap" />
        <Box>
          <Typography className="brown-text cursor-pointer text-capitalize close-title">
            {title}
          </Typography>
          {subTitle ? (
            <Typography className="content-text">{subTitle}</Typography>
          ) : (
            ''
          )}
        </Box>
      </Box>

      <Box className="custom-drawer-content">{content}</Box>
    </Drawer>
  );
};

export default CustomDrawer;
