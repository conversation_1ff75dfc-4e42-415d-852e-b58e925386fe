import React, { useEffect } from 'react';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { Box, Tooltip, Typography } from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import CustomRadio from '@/components/UI/CustomRadio';
import MultiSelect from '@/components/UI/CustomMultiSelect';
import './leaveapproval.scss';

export default function LeaveApproval({
  setLeavePolicyDetails,
  formikRefApproval,
  leavePolicyDetails,
  staffList,
  setStaffList,
  setLeavePolicySubmit,
  leavePolicySubmit,
  leavePolicyBack,
  setLeavePolicyBack,
}) {
  useEffect(() => {
    if (
      leavePolicyDetails?.leaveApproval?.leave_approval_meta_form?.[0]?.user_id
    ) {
      formikRefApproval.current.setFieldValue(
        'individualuser',
        leavePolicyDetails?.leaveApproval?.leave_approval_meta_form?.[0]
          ?.user_id
      );
    }
    setLeavePolicySubmit({
      ...leavePolicySubmit,
      leaveApproval: false,
    });
    setLeavePolicyBack({
      ...leavePolicyBack,
      leaveApproval: false,
    });
  }, []);

  useEffect(() => {
    if (leavePolicyDetails?.leaveApproval) {
      const users = staffList?.filter((f) =>
        leavePolicyDetails?.leaveApproval?.leave_approval_meta_form?.[0]?.user_id?.includes(
          f?.value
        )
      );

      Object.entries({
        approvalRequired:
          leavePolicyDetails?.leaveApproval &&
          leavePolicyDetails?.leaveApproval?.leave_request_require_approval !==
            undefined
            ? leavePolicyDetails?.leaveApproval?.leave_request_require_approval
              ? 'yes'
              : 'no'
            : 'yes',
        individualuser: users && users?.length > 0 ? users : [],
      }).forEach(([key, value]) => {
        formikRefApproval.current.setFieldValue(key, value);
      });
    }
  }, [leavePolicyDetails?.leaveApproval]);

  return (
    <Formik
      innerRef={formikRefApproval}
      enableReinitialize
      initialValues={{
        approvalRequired: leavePolicyDetails?.leaveApproval
          ?.leave_request_require_approval
          ? 'yes'
          : 'no',
        individualuser: [],
      }}
      validationSchema={Yup.object().shape({
        individualuser: Yup.lazy((value, context) => {
          const approvalRequired = context.parent.approvalRequired;
          return approvalRequired === 'yes'
            ? Yup.array()
                .min(1, 'At least one user is required')
                .required('This field is required')
            : Yup.array().nullable().notRequired();
        }),
      })}
      onSubmit={(values) => {
        let getIdss = values?.individualuser?.map((i) => i?.value);
        const leaveApproval = {
          leave_request_require_approval:
            values?.approvalRequired === 'yes' ? true : false,
          leave_approval_meta_form:
            values?.approvalRequired === 'yes'
              ? [
                  {
                    leave_policy_leval: 1,
                    user_id: getIdss && getIdss?.length > 0 ? getIdss : [],
                  },
                ]
              : [],
        };
        setLeavePolicyDetails((prevState) => ({
          ...prevState,
          leaveApproval: leaveApproval,
        }));
      }}
    >
      {({
        values,
        errors,
        touched,
        setFieldValue,
        // handleChange,
        // handleBlur,
      }) => (
        <Form>
          <Box className="leave-approval-wrap">
            <Box>
              <Typography className="sub-content-text">
                Does this Leave request require an approval?
              </Typography>
            </Box>
            <Box className="mt4">
              <Box className="d-flex align-center gap-sm">
                <CustomRadio
                  disableRipple
                  name="approvalRequired"
                  value="no"
                  checked={values?.approvalRequired === 'no'}
                  onChange={(e) => {
                    setFieldValue('approvalRequired', e?.target?.value);
                    setLeavePolicySubmit({
                      ...leavePolicySubmit,
                      leaveApproval: false,
                    });
                    setLeavePolicyBack({
                      ...leavePolicyBack,
                      leaveApproval: false,
                    });
                  }}
                  label={
                    <Typography className="sub-content-text">No</Typography>
                  }
                />
                <Tooltip
                  arrow
                  title={
                    <Typography>
                      Leave requests will get auto approved without taking any
                      action from the concerned person.
                    </Typography>
                  }
                  classes={{ tooltip: 'info-tooltip-container' }}
                >
                  <InfoIcon className="info-icon cursor-pointer" />
                </Tooltip>
              </Box>
              <Box className="d-flex align-center gap-sm">
                <CustomRadio
                  disableRipple
                  value="yes"
                  name="approvalRequired"
                  checked={values?.approvalRequired === 'yes'}
                  onChange={(e) => {
                    setFieldValue('approvalRequired', e?.target?.value);
                    setLeavePolicySubmit({
                      ...leavePolicySubmit,
                      leaveApproval: false,
                    });
                    setLeavePolicyBack({
                      ...leavePolicyBack,
                      leaveApproval: false,
                    });
                  }}
                  label={
                    <Typography className="sub-content-text">Yes</Typography>
                  }
                />
                <Tooltip
                  arrow
                  title={
                    <Typography>
                      Leave requests will be sent to the concerned person and
                      they will take action accordingly.
                    </Typography>
                  }
                  classes={{ tooltip: 'info-tooltip-container' }}
                >
                  <InfoIcon className="info-icon cursor-pointer" />
                </Tooltip>
              </Box>
            </Box>

            {values?.approvalRequired === 'yes' && (
              <>
                <Box className="pt8">
                  <MultiSelect
                    placeholder="Staff user"
                    options={staffList}
                    value={values?.individualuser}
                    error={Boolean(
                      touched.individualuser && errors.individualuser
                    )}
                    helperText={touched.individualuser && errors.individualuser}
                    onChange={(e) => {
                      setLeavePolicySubmit({
                        ...leavePolicySubmit,
                        leaveApproval: false,
                      });
                      setLeavePolicyBack({
                        ...leavePolicyBack,
                        leaveApproval: false,
                      });
                      const filterValue = e?.find((f) => f?.value === 'all');
                      const branchValue = staffList?.filter(
                        (f) => f?.value !== 'all'
                      );
                      if (filterValue || e?.length === branchValue?.length) {
                        const filterall = staffList?.filter(
                          (b) => b?.value !== 'all'
                        );
                        setStaffList(filterall);
                        const selectedValue = filterValue ? filterall : e;
                        setFieldValue('individualuser', selectedValue);
                      }
                      if (!filterValue) {
                        const isAll = staffList?.find(
                          (f) => f?.value === 'all'
                        );
                        if (!isAll) {
                          const alloption = [
                            { label: 'Select all', value: 'all' },
                          ];
                          let mergeList = _.concat(alloption, staffList);
                          setStaffList(mergeList);
                        }
                        setFieldValue('individualuser', e);
                      }
                    }}
                    label="Staff user"
                    required
                    isDisabled={
                      values?.branch?.length > 0 ||
                      values?.department?.length > 0 ||
                      values?.role?.length > 0
                    }
                  />
                </Box>
                <Box>
                  <Typography className="sub-title-text note-text mb8 mt8">
                    <span className="sub-content-text">Note: </span> Super
                    admins have all rights by default. Make sure you only assign
                    employees to the approval process who have the rights to
                    approve or reject at their assigned access levels.
                  </Typography>
                  <Typography className="sub-title-text note-text">
                    <span className="sub-content-text">Note: </span>If you have
                    add Line Manager in Access Level then make sure that Line
                    Manager is available in Custom UAC OR Default Manager UAC OR
                    Admin UAC. So. That Line Manager have Approve & Reject
                    Access.
                  </Typography>
                </Box>
              </>
            )}
          </Box>
        </Form>
      )}
    </Formik>
  );
}
