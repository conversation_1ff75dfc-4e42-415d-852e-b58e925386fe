import React, { useState, useEffect, useContext } from 'react';
import { Box, Typography, IconButton } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { ORG_URLS } from '@/helper/constants/urls';
import { setApiMessage } from '@/helper/common/commonFunctions';
import AuthContext from '@/helper/authcontext';
import { fetchFromStorage } from '@/helper/context';
import { identifiers } from '@/helper/constants/identifier';
import './notificationbanner.scss';

export default function NotificationBanner() {
  const [hiddenBanners, setHiddenBanners] = useState({});
  const [notificationBanner, setNotificationBanner] = useState([]);
  const { authState } = useContext(AuthContext);

  const authdata =
    typeof window !== 'undefined' && fetchFromStorage(identifiers?.AUTH_DATA);

  const userId = authState?.id;
  const handleClose = (ids) => {
    setHiddenBanners((prev) => ({
      ...prev,
      [ids]: true,
    }));
    setTimeout(() => {
      const banner = document.getElementById('site-banner');
      if (banner) {
        document.documentElement.style.setProperty(
          '--banner-height',
          `${banner.offsetHeight}px`
        );
      }
    }, 200);
  };

  const getAllbanner = async () => {
    try {
      const { status, data } = await axiosInstance.get(
        ORG_URLS.GET_USER_BANNER_NOTIFICATION +
          `/${userId}?read_notification=false`
      );
      if (status === 200) {
        setNotificationBanner(data?.data);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const readBannerNotification = async (banner) => {
    try {
      const { status } = await axiosInstance.put(
        ORG_URLS.MARK_AS_READ_BANNER_NOTIFICATION + `/${banner?.id}`
      );
      if (status === 200) {
        handleClose(banner?.id);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  useEffect(() => {
    const isNormalLogin =
      authdata?.organizationId || authdata?.organizationStatus;
    if (userId && isNormalLogin) {
      getAllbanner();
    }
  }, [userId]);

  useEffect(() => {
    const updateBannerHeight = () => {
      const banner = document.getElementById('site-banner');
      if (banner) {
        const height = banner.offsetHeight;
        document.documentElement.style.setProperty(
          '--banner-height',
          `${height}px`
        );
      }
    };

    if (notificationBanner.length > 0) {
      // Wait for DOM to render banner
      setTimeout(updateBannerHeight, 10); // small timeout to let layout update
    }
  }, [notificationBanner]);

  useEffect(() => {
    const updateBannerHeight = () => {
      const banner = document.getElementById('site-banner');
      if (banner) {
        document.documentElement.style.setProperty(
          '--banner-height',
          `${banner.offsetHeight}px`
        );
      }
    };

    updateBannerHeight();
    window.addEventListener('resize', updateBannerHeight);
    return () => window.removeEventListener('resize', updateBannerHeight);
  }, []);

  return (
    <Box id="site-banner">
      {notificationBanner?.map((banner, index) => {
        const bannerConfig =
          typeof banner?.banner_config?.value === 'string'
            ? JSON.parse(banner?.banner_config?.value)
            : banner?.banner_config?.value;

        const isHidden = hiddenBanners[banner?.id];
        if (isHidden) return null;
        return (
          <Box
            key={index}
            className="notification-banner-container"
            style={{
              backgroundColor: bannerConfig?.bgColor,
              color: bannerConfig?.textColor,
              marginBottom: notificationBanner?.length <= 1 ? '0px' : '',
              textAlign: bannerConfig?.alignment,
            }}
          >
            <Box>
              <Typography
                className="sub-title-text notification-title"
                style={{
                  color: bannerConfig?.textColor,
                  textAlign: bannerConfig?.alignment,
                }}
              >
                {banner?.title}
              </Typography>
              <Typography
                className="caption-text notification-description"
                style={{
                  color: bannerConfig?.textColor,
                  textAlign: bannerConfig?.alignment,
                }}
              >
                {banner?.description}
              </Typography>
            </Box>

            {bannerConfig?.isClosable && (
              <IconButton
                className="notification-close-icon"
                onClick={() => readBannerNotification(banner)}
                sx={{ color: bannerConfig?.textColor }}
              >
                <CloseIcon />
              </IconButton>
            )}
          </Box>
        );
      })}
    </Box>
  );
}
