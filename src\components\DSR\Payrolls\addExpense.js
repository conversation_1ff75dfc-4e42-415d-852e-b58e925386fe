'use client';

import React, { useEffect, useState, useContext } from 'react';
import { Box, Typography } from '@mui/material';
import CustomButton from '@/components/UI/CustomButton';
import CustomSelect from '@/components/UI/CustomSelect';
import CustomDatePicker from '@/components/UI/CustomDatePicker';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { useRouter } from 'next/navigation';
import { URLS } from '@/helper/constants/urls';
import AuthContext from '@/helper/authcontext';
import ExpenseCategory from '@/components/DSR/Payrolls/expenseCat';
import dayjs from 'dayjs';
import moment from 'moment';
import NoDataView from '@/components/UI/NoDataView';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import { fetchFromStorage, saveToStorage } from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import '../dsr.scss';

export default function AddExpensesPage() {
  const { authState, AllListsData, userdata, setUserdata } =
    useContext(AuthContext);
  const router = useRouter();
  const [DsrData, setDsrData] = useState([]);
  const [expenseData, setExpenseData] = useState([]);
  const [loader, setLoader] = useState(false);
  const [checkloader, setCheckLoader] = useState(false);
  const [branch, setBranch] = useState();
  const [sdate, setDate] = useState();
  const [random, setRandom] = useState();
  const [DsrExist, setISDsr] = useState(false);
  const [currency, setCurrency] = useState();
  const [IsRemarkView, setIsRemarkView] = useState(true);

  // get Payroll Data
  const getPayrollData = async (branchID) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_DSR_DATA + `${branchID}?payment_type=payroll`
      );
      if (status === 200) {
        setLoader(false);
        setDsrData(data?.data);
        const ExpenseData =
          data?.data &&
          data?.data?.length > 0 &&
          data?.data?.filter((f) => f?.payment_type_usage === 'expense');
        ExpenseData && ExpenseData?.length > 0
          ? setExpenseData(ExpenseData)
          : setExpenseData([]);
      }
    } catch (error) {
      setLoader(false);
      setDsrData([]);
      setExpenseData([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const ExpenseCheck = async (branch, date) => {
    setCheckLoader(true);
    const sendData = {
      branch_id: branch,
      expense_month: date
        ? dayjs(date).format('MM')
        : moment(Date()).format('MM'),
      expense_year: date
        ? dayjs(date).format('YYYY')
        : moment(Date()).format('YYYY'),
    };
    try {
      const { status, data } = await axiosInstance.post(
        URLS?.GET_EXPENSE_CHECK,
        sendData
      );
      if (status === 200 || status === 202) {
        if (data?.status) {
          setCheckLoader(false);
          setISDsr(false);
          getPayrollData(branch);
          // setApiMessage('success', data?.message);
        } else {
          setCheckLoader(false);
          setISDsr(true);
          setApiMessage('error', data?.message);
          setDsrData([]);
          setExpenseData([]);
        }
      }
    } catch (error) {
      setCheckLoader(false);
      setISDsr(true);
      setDsrData([]);
      setExpenseData([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const AddExpenses = async () => {
    const sendData = {
      branch_id:
        authState?.web_user_active_role_id === 7 ||
        authState?.web_user_active_role_id === 14
          ? authState?.branch?.id
          : branch,
      expense_month: sdate
        ? dayjs(sdate).format('MM')
        : moment(Date()).format('MM'),
      expense_year: sdate
        ? dayjs(sdate).format('YYYY')
        : moment(Date()).format('YYYY'),
      data: expenseData,
      current_datetime: moment(Date()).format('YYYY-MM-DD HH:mm:ss'),
    };
    try {
      const { status, data } = await axiosInstance.post(
        URLS?.ADD_EXPENSE,
        sendData
      );

      if (status === 200) {
        if (data?.status) {
          setLoader(false);
          setApiMessage('success', data?.message);
          router?.push('/payroll');
        } else {
          setLoader(false);
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  useEffect(() => {
    setExpenseData(expenseData);
  }, [random]);

  useEffect(() => {
    authState?.currency_details && setCurrency(authState?.currency_details);
  }, [authState]);

  // const calculateDisplayDate = (isUpdate) => {
  //   const now = new Date();

  //   // const eightAM = new Date();
  //   // eightAM.setHours(8, 0, 0, 0);

  //   // if (now < eightAM) {
  //   //   now.setDate(now.getDate() - 1);
  //   // }

  //   return isUpdate
  //     ? moment(now).format('MM-YYYY')
  //     : moment(now).format('YYYY-MM');
  // };
  const getStartOfPreviousMonth = () => {
    const today = new Date();
    const startOfPreviousMonth = new Date(
      today.getFullYear(),
      today.getMonth() - 1,
      1
    );
    return startOfPreviousMonth;
  };
  useEffect(() => {
    if (
      authState?.web_user_active_role_id === 7 ||
      authState?.web_user_active_role_id === 14
    ) {
      getPayrollData(authState?.branch?.id);
    }
  }, [authState?.web_user_active_role_id]);
  const oldDSR = fetchFromStorage(identifiers?.RedirectData)
    ? fetchFromStorage(identifiers?.RedirectData)
    : userdata;

  useEffect(() => {
    if (oldDSR?.IsFromUser === undefined && oldDSR?.page !== undefined) {
      setUserdata({ ...userdata, IsFromUser: true });
      saveToStorage(identifiers?.RedirectData, {
        ...oldDSR,
        IsFromUser: true,
      });
    }
  }, [oldDSR]);
  return (
    <Box>
      <Box className="dsr-page-section Add-dsr-section">
        <ArrowBackIosIcon
          className="mb8 cursor-pointer"
          onClick={() => {
            setTimeout(() => {
              router?.push('/payroll');
            }, 1000);
          }}
        />
        {authState?.web_user_active_role_id === 7 ||
        authState?.web_user_active_role_id === 14 ? (
          <>
            {authState?.branch && authState?.branch?.branch_name && (
              <Box className="d-flex align-center">
                <Typography className="title-text fw400 user-date">
                  <span className="fw600">Branch : </span>
                  <span>{authState?.branch?.branch_name}</span>
                </Typography>
              </Box>
            )}

            {/* <Box className="d-flex align-center">
              <Typography className="title-text fw400 user-date">
                <span className="fw600">Month : </span>
                <span>{calculateDisplayDate(true)}</span>
              </Typography>
            </Box> */}
            <Box className="display-grid-branch pt32">
              <Box>
                <CustomDatePicker
                  label={<span>Month (MM/YYYY)</span>}
                  name="date"
                  value={dayjs(sdate)}
                  disabled={checkloader}
                  disableFuture={true}
                  onChange={(date) => {
                    setDate(date);
                    branch && ExpenseCheck(authState?.branch?.id, date);
                  }}
                  inputVariant="outlined"
                  months={true}
                  format={'M/YYYY'}
                  views={['month', 'year']}
                  disablePast={false}
                  minDate={dayjs(getStartOfPreviousMonth())}
                  error={false}
                />
              </Box>
            </Box>
          </>
        ) : (
          <>
            <Box className="display-grid-branch">
              <Box>
                <CustomSelect
                  placeholder="Select Branch"
                  options={AllListsData?.ActiveBranchList}
                  value={
                    AllListsData?.ActiveBranchList?.find((opt) => {
                      return opt?.value === branch;
                    }) || ''
                  }
                  name="branch"
                  disabled={checkloader}
                  onChange={(e) => {
                    setBranch(e?.value);
                    ExpenseCheck(e?.value, sdate);
                  }}
                  label={<span>Branch</span>}
                />
              </Box>
              <Box>
                <CustomDatePicker
                  label={<span>Month (MM/YYYY)</span>}
                  name="date"
                  value={dayjs(sdate)}
                  disabled={checkloader}
                  disableFuture={true}
                  onChange={(date) => {
                    setDate(date);
                    branch && ExpenseCheck(branch, date);
                  }}
                  inputVariant="outlined"
                  months={true}
                  format={'M/YYYY'}
                  disablePast={false}
                  views={['month', 'year']}
                />
              </Box>
            </Box>
          </>
        )}
        {(!DsrData || DsrData?.length === 0) &&
          !DsrExist &&
          !loader &&
          (branch ||
            authState?.web_user_active_role_id === 7 ||
            authState?.web_user_active_role_id === 14) && (
            <Box className="no-data d-flex align-center justify-center">
              <NoDataView title="Not any categories created/selected in this branch." />
            </Box>
          )}
        <ExpenseCategory
          expenseData={expenseData}
          isEdit={false}
          setIsRemarkView={setIsRemarkView}
          IsRemarkView={IsRemarkView}
          setExpenseData={setExpenseData}
          setRandom={setRandom}
          currency={currency}
        />
        {/* {expenseData && expenseData?.length > 0 && (
          <>
            <FormGroup className="pt16">
              <FormControlLabel
                control={
                  <Checkbox
                    className="check-box "
                    icon={<CheckBoxOutlineBlankIcon className="uncheck-icon" />}
                    checkedIcon={<CheckBoxIcon className="check-icon" />}
                    disableRipple
                  />
                }
                name="weekly"
                className="weekly-check-box p12"
                checked={IsRemarkView}
                onChange={(e) => {
                  setIsRemarkView(e.target.checked);
                }}
                label="View Remark"
              />
            </FormGroup>
            <Box className="dsr-add-view pt16">
              {expenseData &&
                expenseData?.length > 0 &&
                expenseData?.map((item, dindex) => {
                  return (
                    <Box className="dsr-main-category income">
                      <Typography className="p16 fw600 text-capital main-category-text expense-text">
                        {item?.payment_type_title}
                      </Typography>
                      <span className="category-status income">
                        <span className="p12 category-draft  fw600 text-capital">
                          {' '}
                          Expense
                        </span>
                      </span>
                      {item?.payment_type_category?.length > 0 &&
                        item?.payment_type_category?.map((citem, cindex) => {
                          return (
                            <Box className="">
                              {' '}
                              {citem?.payment_type_category_pattern ===
                                'multiple' && (
                                <Typography className="title-text fw600 text-capital pt16">
                                  {citem?.payment_type_category_title}
                                </Typography>
                              )}
                              {citem?.payment_type_category_pattern ===
                              'multiple' ? (
                                <>
                                  {citem?.categoryBranchValue &&
                                    citem?.categoryBranchValue?.length > 0 &&
                                    citem?.categoryBranchValue?.map(
                                      (csitem, subIndex) => {
                                        return (
                                          <>
                                            <Box className="card-details-section pt16">
                                              <Box>
                                                <Typography className="title-text">
                                                  {csitem?.first_field_value}
                                                </Typography>
                                              </Box>
                                              <Box className="amount-field">
                                                <CustomTextField
                                                  InputLabelProps={{
                                                    shrink: true
                                                  }}
                                                  // fullWidth
                                                  // id=""
                                                  value={csitem?.expense_amount}
                                                  onChange={(e) => {
                                                    const income = expenseData;
                                                    income[
                                                      dindex
                                                    ].payment_type_category[
                                                      cindex
                                                    ].categoryBranchValue[
                                                      subIndex
                                                    ].expense_amount =
                                                      e.target.value;
                                                    setExpenseData(income);
                                                    setRandom(Math.random());
                                                  }}
                                                  name={`amount ${subIndex}`}
                                                  variant="filled"
                                                  placeholder={
                                                    item?.has_field_currency
                                                      ? 'Amount'
                                                      : 'Enter value'
                                                  }
                                                  className={'amount-textfield'}
                                                  onInput={(e) => {
                                                    // Remove non-numeric characters
                                                    let value = e.target.value;
                                                    if (
                                                      value === '' ||
                                                      /^\d*\.?\d{0,2}$/.test(
                                                        value
                                                      )
                                                    ) {
                                                      e.target.value = value;
                                                    } else {
                                                      e.target.value =
                                                        value.slice(0, -1);
                                                    }
                                                  }}
                                                  InputProps={{
                                                    ...(item?.has_field_currency && {
                                                      startAdornment: (
                                                        <InputAdornment position="start">
                                                          <Typography className="title-text currency">
                                                            {currency?.symbol
                                                              ? currency?.symbol
                                                              : '£'}
                                                          </Typography>{' '}
                                                        </InputAdornment>
                                                      )
                                                    })
                                                  }}
                                                />
                                              </Box>
                                            </Box>
                                          </>
                                        );
                                      }
                                    )}
                                </>
                              ) : (
                                <>
                                  <Box className="card-details-section pt16">
                                    <Box>
                                      <Typography className="title-text fw600 text-capital">
                                        {citem?.payment_type_category_title}
                                      </Typography>
                                    </Box>
                                    <Box className="amount-field">
                                      <CustomTextField
                                        InputLabelProps={{
                                          shrink: true
                                        }}
                                        // fullWidth
                                        // id=""
                                        value={citem?.expense_amount}
                                        onChange={(e) => {
                                          const income = expenseData;
                                          income[dindex].payment_type_category[
                                            cindex
                                          ].expense_amount = e.target.value;
                                          setExpenseData(income);
                                          setRandom(Math.random());
                                        }}
                                        name={`amount ${cindex}`}
                                        variant="filled"
                                        placeholder={
                                          item?.has_field_currency
                                            ? 'Amount'
                                            : 'Enter value'
                                        }
                                        className={'amount-textfield'}
                                        onInput={(e) => {
                                          let value = e.target.value;
                                          if (
                                            value === '' ||
                                            /^\d*\.?\d{0,2}$/.test(value)
                                          ) {
                                            e.target.value = value;
                                          } else {
                                            e.target.value = value.slice(0, -1);
                                          }
                                        }}
                                        InputProps={{
                                          ...(item?.has_field_currency && {
                                            startAdornment: (
                                              <InputAdornment position="start">
                                                <Typography className="title-text currency">
                                                  {currency?.symbol
                                                    ? currency?.symbol
                                                    : '£'}
                                                </Typography>{' '}
                                              </InputAdornment>
                                            )
                                          })
                                        }}
                                      />
                                    </Box>
                                  </Box>
                                </>
                              )}
                            </Box>
                          );
                        })}
                      <>
                        <Box className="card-details-section pt16 totol-dsr">
                          <Box>
                            <Typography className="title-text fw600">Total</Typography>
                          </Box>
                          <Box className="amount-field">
                            <CustomTextField
                              InputLabelProps={{
                                shrink: true
                              }}
                              // fullWidth
                              // id=""
                              value={TotalOfIncome(dindex)}
                              name={`amount `}
                              variant="filled"
                              disabled={true}
                              placeholder={
                                item?.has_field_currency
                                  ? 'Amount'
                                  : 'Enter value'
                              }
                              className={'amount-textfield'}
                              onInput={(e) => {
                                // Remove non-numeric characters
                                let value = e.target.value;
                                if (
                                  value === '' ||
                                  /^\d*\.?\d{0,2}$/.test(value)
                                ) {
                                  e.target.value = value;
                                } else {
                                  e.target.value = value.slice(0, -1);
                                }
                              }}
                              InputProps={{
                                ...(item?.has_field_currency && {
                                  startAdornment: (
                                    <InputAdornment position="start">
                                      <Typography className="title-text currency">
                                        {currency?.symbol
                                          ? currency?.symbol
                                          : '£'}
                                      </Typography>{' '}
                                    </InputAdornment>
                                  )
                                })
                              }}
                            />
                          </Box>
                        </Box>
                        {IsRemarkView && (
                          <Box className="card-details-section  card-details-remark-section pt16">
                            <Box>
                              <Typography className="title-text fw600">
                                Remark
                              </Typography>
                            </Box>
                            <Box className="amount-field">
                              <CustomTextField
                                InputLabelProps={{
                                  shrink: true
                                }}
                                value={item?.payment_type_remark}
                                onChange={(e) => {
                                  if (
                                    e.target.value === '' ||
                                    e.target.value?.length < 161
                                  ) {
                                    const income = expenseData;
                                    income[dindex].payment_type_remark =
                                      e.target.value;
                                    setExpenseData(income);
                                    setRandom(Math.random());
                                  }
                                }}
                                onPaste={(e) => {
                                  if (
                                    e.target.value === '' ||
                                    e.target.value?.length < 161
                                  ) {
                                    setIsSubmit(false);
                                    const income = DsrDataIncome;
                                    income[dindex].payment_type_remark =
                                      e.target.value;
                                    setDsrDataIncome(income);
                                    setRandom(Math.random());
                                  }
                                }}
                                multiline
                                rows={2}
                                name={`amount ${dindex}`}
                                variant="filled"
                                placeholder={'Enter value'}
                                className={
                                  'amount-textfield additional-textfeild'
                                }
                              />
                            </Box>
                            <Typography className="p12 text-align-end">
                              {(item?.payment_type_remark?.length
                                ? item?.payment_type_remark?.length
                                : 0) + ' / 160'}
                            </Typography>
                          </Box>
                        )}
                      </>
                    </Box>
                  );
                })}
            </Box>
          </>
        )} */}
        <Box className="mt24">
          {' '}
          <CustomButton
            fullWidth={false}
            variant="contained"
            disabled={
              loader ||
              DsrExist ||
              checkloader ||
              !DsrData ||
              DsrData?.length === 0 ||
              (!branch &&
                authState?.web_user_active_role_id !== 7 &&
                authState?.web_user_active_role_id !== 14)
            }
            onClick={() => AddExpenses()}
            title={`${loader ? 'Adding...' : 'Add Expenses'}`}
          />
        </Box>
      </Box>
    </Box>
  );
}
