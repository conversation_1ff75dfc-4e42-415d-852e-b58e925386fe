'use client';

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Tooltip,
  InputAdornment,
  IconButton,
} from '@mui/material';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomButton from '@/components/UI/CustomButton';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import CachedIcon from '@mui/icons-material/Cached';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import {
  setApiMessage,
  GeneratePassword,
} from '@/helper/common/commonFunctions';

export default function UserGeneratePassword({
  Randompassword,
  setRandomPassword,
  isReset,
  setIsReset,
  ViewAccessOnly,
  validationFeedback,
  setValidationFeedback,
  UserId,
  setLoader,
  handleClosePin,
}) {
  const [showEnterPassword, setShowCurrentPassword] = useState(false);
  const [genPassword, setGenPassword] = useState(false);
  const copyToClipboard = () => {
    navigator.clipboard.writeText(Randompassword).then(
      () => {
        setApiMessage('success', 'Password copied');
      },
      () => {
        setApiMessage('error', 'Failed to copy password');
      }
    );
  };
  const ResetPassword = async (Password, isNotify) => {
    let sendData = {
      user_password: Password,
      user_id: UserId,
      reset_type: 'password',
      ...(isNotify && { isNotify: true }),
    };
    try {
      setLoader(true);
      handleClosePin();
      const { status } = await axiosInstance.post(
        URLS.RESET_USER_PASSWORD,
        sendData
      );

      if (status === 200) {
        setLoader(false);
        setIsReset(false);
        setRandomPassword();
        handleClosePin();
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setLoader(false);
    }
  };
  const validatePassword = (pwd) => {
    const feedback = {
      length: pwd.length >= 8,
      lowercase: /[a-z]/.test(pwd),
      uppercase: /[A-Z]/.test(pwd),
      number: /\d/.test(pwd),
      specialChar: /[@$!%*?&]/.test(pwd),
    };
    setValidationFeedback(feedback);
    return feedback;
  };
  return (
    <>
      <Box className="generate-password-section">
        <Box className="reset-password-display-grid">
          <Box className="reset-password-textfield-sec">
            <CustomTextField
              id="new_password"
              name="new_password"
              value={Randompassword}
              label="Password"
              type={showEnterPassword ? 'text' : 'password'}
              className={
                (!Randompassword || Randompassword?.length < 8) && isReset
                  ? 'w100 password-textfield password-error reset-password-textfield'
                  : 'w100 password-textfield reset-password-textfield'
              }
              error={Boolean(
                (!Randompassword || Randompassword?.length < 8) && isReset
              )}
              helperText={
                !Randompassword && isReset
                  ? 'This field is required'
                  : Randompassword?.length < 8 && isReset
                    ? 'Password length must be minimum 8 character'
                    : null
              }
              placeholder="Enter Password"
              onChange={(e) => {
                setIsReset(false);
                setRandomPassword(e.target.value);
                validatePassword(e.target.value);
                setGenPassword(false);
              }}
              disabled={ViewAccessOnly}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end" className="eye-icon">
                    <IconButton
                      disableRipple
                      onClick={() => setShowCurrentPassword(!showEnterPassword)}
                    >
                      <Box className="eye-wrap">
                        {showEnterPassword ? (
                          <VisibilityIcon />
                        ) : (
                          <VisibilityOffIcon />
                        )}
                      </Box>
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
          </Box>
          <Box className="generate-password-btn">
            {Randompassword ? (
              <Tooltip title="Copy password" arrow>
                <ContentCopyIcon
                  className={'cursor-pointer'}
                  onClick={() => {
                    !ViewAccessOnly && copyToClipboard();
                  }}
                />
              </Tooltip>
            ) : (
              <ContentCopyIcon className={'disabled-copy'} />
            )}
            {ViewAccessOnly ? (
              <CachedIcon />
            ) : (
              <Tooltip title="Generate password" arrow>
                <CachedIcon
                  className={'cursor-pointer '}
                  onClick={() => {
                    setRandomPassword(GeneratePassword(8));
                    validatePassword(GeneratePassword(8));
                    setRandomPassword(GeneratePassword(8));
                    validatePassword(GeneratePassword(8));
                    setGenPassword(true);
                  }}
                />
              </Tooltip>
            )}
          </Box>
        </Box>
        {Randompassword && !genPassword && (
          <Box className="pt8">
            <Typography
              className="p12"
              style={{
                color: validationFeedback.length ? 'green' : 'red',
              }}
            >
              {validationFeedback.length ? '✔' : '✖'} At least 8 characters
              long
            </Typography>
            <Typography
              className="p12"
              style={{
                color: validationFeedback.lowercase ? 'green' : 'red',
              }}
            >
              {validationFeedback.lowercase ? '✔' : '✖'} At least one
              lowercase character
            </Typography>
            <Typography
              className="p12"
              style={{
                color: validationFeedback.uppercase ? 'green' : 'red',
              }}
            >
              {validationFeedback.uppercase ? '✔' : '✖'} At least one
              uppercase character
            </Typography>
            <Typography
              className="p12"
              style={{
                color: validationFeedback.number ? 'green' : 'red',
              }}
            >
              {validationFeedback.number ? '✔' : '✖'} At least one number
            </Typography>
            <Typography
              className="p12"
              style={{
                color: validationFeedback.specialChar ? 'green' : 'red',
              }}
            >
              {validationFeedback.specialChar ? '✔' : '✖'} At least one
              special character
            </Typography>
          </Box>
        )}

        <Box className="create-cancel-button mt32 justify-center">
          <CustomButton
            variant="outlined"
            title="Save"
            fullWidth
            disabled={
              ViewAccessOnly ||
              !validationFeedback?.length ||
              !validationFeedback?.lowercase ||
              !validationFeedback?.uppercase ||
              !validationFeedback?.number ||
              !validationFeedback?.specialChar
            }
            onClick={() => {
              if (
                validationFeedback?.length &&
                validationFeedback?.lowercase &&
                validationFeedback?.uppercase &&
                validationFeedback?.number &&
                validationFeedback?.specialChar
              ) {
                ResetPassword(Randompassword);
              } else {
                setIsReset(true);
              }
            }}
          />
          <CustomButton
            variant="contained"
            onClick={() => {
              if (
                validationFeedback?.length &&
                validationFeedback?.lowercase &&
                validationFeedback?.uppercase &&
                validationFeedback?.number &&
                validationFeedback?.specialChar
              ) {
                ResetPassword(Randompassword, true);
              } else {
                setIsReset(true);
              }
            }}
            title="Save & Send Email"
            fullWidth
            disabled={
              ViewAccessOnly ||
              !validationFeedback?.length ||
              !validationFeedback?.lowercase ||
              !validationFeedback?.uppercase ||
              !validationFeedback?.number ||
              !validationFeedback?.specialChar
            }
          />
        </Box>
      </Box>
    </>
  );
}
