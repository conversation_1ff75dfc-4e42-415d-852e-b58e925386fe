'use client';

import React, { useContext, useEffect, useState } from 'react';
import { Box } from '@mui/material';
import AuthContext from '@/helper/authcontext';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import DashboardBYID from '@/components/Dashboard/DashboardBYID/index';
import './dashboard.scss';

export default function ChartDashboard() {
  const { AllListsData } = useContext(AuthContext);
  const [loader, setLoader] = useState(false);
  const [IsDefaultFilter, setIsDefaultFilter] = useState(false);
  const [screenHeight, setScreenHeight] = useState(null);
  const [screenWidth, setScreenWidth] = useState(null);
  const [DashboardData, setDashboardData] = useState();
  const [DashboardId, setDashboardID] = useState('');

  // Get One Dashboard by ID
  const getOneDashboardList = async (
    ID,
    date,
    branch,
    isDefault,
    timeperiod
  ) => {
    setLoader(true);
    setDashboardID(ID);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_DASHBOARD_BY_ID +
          `${ID}?branch_id=${branch}&date_filter=${date}&filter_time_period=${timeperiod}`
      );
      if (status === 200 || status === 201 || status === 304) {
        setLoader(false);
        if (
          data?.data &&
          data?.data?.dashboard_models &&
          data?.data?.dashboard_models?.length > 0
        ) {
          const modelData =
            data?.data &&
            data?.data?.dashboard_models &&
            data?.data?.dashboard_models?.length > 0 &&
            data?.data?.dashboard_models?.map((item) => {
              if (
                item?.model_type === 'bar_chart' ||
                item?.model_type === 'line_chart'
              ) {
                const grpahsData = item?.dashboard_data;
                grpahsData.series = item?.dashboard_data?.series?.map((s) => {
                  return {
                    ...s,
                    interpolation: {
                      type: 'smooth',
                    },
                  };
                });

                return {
                  ...item,
                  dashboard_data: {
                    ...grpahsData,
                    title: {
                      text: item?.model_title,
                    },
                  },
                };
              } else if (item?.model_type === 'pie_chart') {
                const series =
                  item?.dashboard_data &&
                  item?.dashboard_data?.series &&
                  item?.dashboard_data?.series?.length > 0 &&
                  item?.dashboard_data?.series?.map((s) => {
                    const fills = s?.fills?.map((f) => f?.branch_color);
                    return {
                      ...s,
                      angleKey: s?.yKey,
                      calloutLabelKey: s?.xKey,
                      sectorLabelKey: s?.yKey,
                      fills: fills,
                    };
                  });
                const dashboard = {
                  ...item?.dashboard_data,
                  series:
                    series && series?.length > 0
                      ? series
                      : item?.dashboard_data?.series,
                  title: {
                    text: item?.model_title,
                  },
                  width: 600,
                  height: 450,
                };
                return { ...item, dashboard_data: dashboard };
              }
              return { ...item };
            });
          setDashboardData({
            ...data?.data,
            dashboard_models: modelData,
            dashboard_filter: data?.data?.dashboard_filter
              ? JSON.parse(data?.data?.dashboard_filter)
              : '',
          });
        } else {
          setDashboardData([]);
        }
        setIsDefaultFilter(!isDefault ? true : false);
      }
    } catch {
      setLoader(false);
      setDashboardData([]);
    }
  };

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const handleResize = () => {
        setScreenHeight(window.innerHeight);
        setScreenWidth(window.innerWidth);
      };

      window.addEventListener('resize', handleResize);
      handleResize();
      return () => {
        window.removeEventListener('resize', handleResize);
      };
    }
  }, [screenHeight, screenWidth]);

  return (
    <>
      <Box className="dashboard-section">
        <DashboardBYID
          DashboardData={DashboardData}
          branchList={AllListsData?.ActiveBranchList}
          isDefault={true}
          getOneDashboardList={getOneDashboardList}
          DashbaordId={DashboardId}
          IsDefaultFilter={IsDefaultFilter}
          setDashboardData={setDashboardData}
          checkLoader={loader}
        />
      </Box>
    </>
  );
}
