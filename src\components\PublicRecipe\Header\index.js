'use client';
import { useState } from 'react';
import { Box, Typography } from '@mui/material';
import HeaderImage from '@/components/UI/ImageSecurity';
import MenuOutlinedIcon from '@mui/icons-material/MenuOutlined';
import CustomDrawer from '@/components/UI/CustomDrawer';
import logo from '../../../../public/images/app-logo.svg';
import PublicLinksDrawer from './components/PublicLinksDrawer';
import { fetchFromStorage } from '@/helper/context';
import { identifiers } from '@/helper/constants/identifier';
import './publicheader.scss';

export default function PublicRecipeHeader() {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const orgData =
    typeof window !== 'undefined' &&
    fetchFromStorage(identifiers.RECIPE_PUBLIC_ORG_DATA);

  const handleDrawerToggle = () => {
    setIsDrawerOpen(!isDrawerOpen);
  };

  return (
    <>
      <header
        className="public-recipe-header d-flex align-center"
        id="public-recipe-header"
      >
        <Box
          className="d-flex align-center gap-sm public-menu-wrap"
          onClick={handleDrawerToggle}
        >
          <MenuOutlinedIcon className="public-menu-icon cursor-pointer" />
          <Typography className="brown-text menu-text cursor-pointer text-capital">
            MENU
          </Typography>
        </Box>
        <Box className="public-logo-wrap">
          <HeaderImage
            imageUrl={orgData?.logo || logo}
            type="lazyload"
            alt="logo"
            className="public-logo"
          />
        </Box>
      </header>

      <CustomDrawer
        anchor="left"
        open={isDrawerOpen}
        onClose={handleDrawerToggle}
        title="CLOSE"
        content={
          <Box className="public-menu-content">
            <PublicLinksDrawer />
          </Box>
        }
      />
    </>
  );
}
