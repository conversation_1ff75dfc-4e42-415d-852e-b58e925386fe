import { Box, Typography } from '@mui/material';
import CustomTextField from '@/components/UI/CustomTextField';
import Gender from '@/components/UI/FormGroupGender';
import MaritalStatus from '@/components/UI/FormGroupMarital';
import CustomRadio from '@/components/UI/CustomRadio';

export default function PersonalHealthDetails({
  touched,
  errors,
  values,
  handleChange,
  setFieldValue,
  handleBlur,
  ViewAccessOnly,
}) {
  return (
    <Box className="pt16">
      <Gender
        keyValue={values?.ph_gender}
        keyName="ph_gender"
        setFieldValue={setFieldValue}
        isRequire={true}
        disable={ViewAccessOnly}
      />
      {touched.ph_gender && errors.ph_gender && (
        <Typography variant="body2" className="other-field-error-text">
          {errors.ph_gender}
        </Typography>
      )}
      <MaritalStatus
        keyName="ph_marital"
        keyValue={values?.ph_marital}
        setFieldValue={setFieldValue}
        isRequire={true}
        disable={ViewAccessOnly}
      />
      {touched.ph_marital && errors.ph_marital && (
        <Typography variant="body2" className="other-field-error-text">
          {errors.ph_marital}
        </Typography>
      )}
      <Typography className="field-label pt16" id={'ph_health'}>
        Do you consider yourself to have a disability or any long term health
        conditions? <span className="required">*</span>
      </Typography>

      <Box>
        <CustomRadio
          disabled={ViewAccessOnly}
          name="ph_health1"
          value={values?.ph_health}
          checked={values?.ph_health === 'yes'}
          onChange={() => setFieldValue('ph_health', 'yes')}
          disableRipple
          label={<Typography className="sub-title-text">Yes</Typography>}
        />
      </Box>
      <Box>
        <CustomRadio
          disabled={ViewAccessOnly}
          name="ph_health1"
          value={values?.ph_health}
          checked={values?.ph_health === 'no'}
          onChange={() => setFieldValue('ph_health', 'no')}
          disableRipple
          label={<Typography className="sub-title-text">No</Typography>}
        />
      </Box>
      {touched.ph_health && errors.ph_health && (
        <Typography variant="body2" className="other-field-error-text">
          {errors.ph_health}
        </Typography>
      )}

      {values?.ph_health !== 'no' && (
        <>
          <Typography className="field-label">
            If yes, Please provide details
            {values?.ph_health === 'yes' && <span className="required">*</span>}
          </Typography>
          <Box className="">
            <CustomTextField
              id="ph_healthdetails"
              name="ph_healthdetails"
              multiline
              rows={2}
              fullWidth
              placeholder=""
              className="additional-textfeild"
              label=""
              value={values?.ph_healthdetails}
              error={Boolean(
                touched.ph_healthdetails && errors.ph_healthdetails
              )}
              helperText={touched.ph_healthdetails && errors.ph_healthdetails}
              onBlur={handleBlur}
              onChange={handleChange}
              disabled={ViewAccessOnly}
            />
          </Box>
        </>
      )}
    </Box>
  );
}
