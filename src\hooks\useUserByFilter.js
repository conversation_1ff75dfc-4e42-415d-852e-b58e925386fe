import { useState } from 'react';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';

const useUserByFilter = () => {
  const [userList, setUserList] = useState([]);
  const [loader, setLoader] = useState(false);

  const getUserList = async (search, pageNo, filter = {}, Rpp, empcontract) => {
    setLoader(true);

    const { selectedBranches, selectedRole, selectedDepartment, status } =
      filter;
    const params = new URLSearchParams();

    const appendParam = (key, value) => {
      if (value) params.append(key, value.toString());
    };

    appendParam('search', search);
    appendParam('page', pageNo);
    appendParam('size', Rpp);
    appendParam(
      'branch_id',
      selectedBranches?.length ? selectedBranches : null
    );
    appendParam('role_id', selectedRole?.length ? selectedRole : null);
    appendParam('status', status);
    appendParam(
      'department_id',
      selectedDepartment?.length ? selectedDepartment : null
    );

    try {
      const { status: responseStatus, data } = await axiosInstance.get(
        `${URLS.GET_USER_LIST}?${params.toString()}`
      );

      if (responseStatus === 200) {
        if (
          (empcontract?.assigntype === 'additionduties' ||
            empcontract?.assigntype === 'jobrole') &&
          data?.userList &&
          data?.userList?.length > 0
        ) {
          const users = data?.userList;
          if (empcontract?.assigntype === 'jobrole') {
            const filterUsers = users?.filter(
              (f) => !empcontract?.additional_user_id?.includes(f?.id)
            );
            setUserList(filterUsers);
          } else if (empcontract?.assigntype === 'additionduties') {
            const filterUsers = users?.filter(
              (f) => !empcontract?.department_user_id?.includes(f?.id)
            );
            setUserList(filterUsers);
          }
        } else {
          setUserList(data?.userList || []);
        }
      }
    } catch (error) {
      setUserList([]);
      setApiMessage(
        'error',
        error?.response?.data?.message || 'Failed to fetch users.'
      );
    } finally {
      setLoader(false);
    }
  };

  return {
    userList,
    getUserList,
    loader,
  };
};

export default useUserByFilter;
