@import '@/styles/variable.scss';

.login-page {
  height: 100vh;
  .login-block {
    width: 30%;
    margin: 0 auto;
    height: 70%;
    flex-direction: column;
    @media (max-width: 1299px) {
      width: 50%;
    }
    @media (max-width: 799px) {
      width: 70%;
    }
    @media (max-width: 499px) {
      width: 90%;
    }
  }
  .otp-section {
    justify-content: center;
    div {
      justify-content: center;
    }
    input {
      width: 50px !important;
      height: 62px !important;
      border-radius: 10px;
      border-color: $color-Dark-60-60;
      margin-right: 25px;
      font-size: 20px;
    }
    input:last-child {
      margin-right: 0 !important;
    }
  }
  .otp-error {
    input {
      border-color: var(--color-danger);
    }
  }
  .verify-button {
    width: 290px;
    max-width: 100%;
  }
}
.login-screen {
  text-align: center;
  .header-logo {
    width: 200px;
    height: 72px;
  }
}
.pt-32 {
  padding-top: 32px;
}
.otp-error {
  text-align: center !important;
}
