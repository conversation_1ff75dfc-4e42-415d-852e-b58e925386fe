// Import the ingredient list container styles
body {
  .recipe-analytics-container {
    .arrow-wrap {
      line-height: 0;
      display: inline-block;
    }
    .recipe-analytics-tables {
      // display: flex;
      // gap: var(--spacing-2xl);
      // .recipe-cta-analytics-table {
      //   width: 50%;
      // }
      // .contact-submissions-table {
      //   width: 50%;
      // }
      // @media (max-width: 1499px) {
      //   display: flex;
      //   flex-direction: column;
      //   gap: var(--spacing-2xl);
      //   .recipe-cta-analytics-table {
      //     width: 100%;
      //   }
      //   .contact-submissions-table {
      //     width: 100%;
      //   }
      // }
      .recipe-analytics-tabs-container {
        display: flex;
        align-items: center;
        margin-bottom: var(--spacing-lg);

        .recipe-analytics-tabs {
          flex: 1;
          border-bottom: var(--normal-sec-border);

          .recipe-analytics-tabs-wrap {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            width: 100%;

            .MuiTabs-root {
              border-bottom: none;
            }
          }
          @media (max-width: 899px) {
            overflow-x: auto;
            ::-webkit-scrollbar {
              display: none;
            }
            -ms-overflow-style: none;  /* IE and Edge */
            scrollbar-width: none;  /* Firefox */
          }
        }
      }
    }
    // Ensure dropdowns are not clipped
    .search-section-wrap {
      position: relative;
      z-index: 1;

      .search-section-fields {
        position: relative;
        z-index: 1;
      }
    }

    // Override height restrictions for analytics dashboard - removed to allow natural scroll
  }

  // Ensure react-select dropdowns are properly positioned
  // .select__menu-portal {
  //   z-index: 99999 !important;
  // }

  // .select__menu {
  //   z-index: 99999 !important;
  //   position: fixed !important;
  // }
}

.public-recipe-dashboard {
  width: 100%;
  // min-height: 100vh; // Removed to allow natural scroll
  background-color: var(--color-secondary);
  padding: var(--spacing-xxl);

  @media (max-width: 1499px) {
    padding: var(--spacing-lg);
  }

  @media (max-width: 899px) {
    padding: var(--spacing-md);
  }

  &__container {
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
  }

  &__header {
    margin-bottom: var(--spacing-2xl);

    &-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: var(--spacing-lg);

      @media (max-width: 899px) {
        align-items: center;
      }
    }

    h1 {
      font-family: var(--font-family-primary);
      font-size: var(--font-size-2xl);
      font-weight: var(--font-weight-bold);
      color: var(--text-color-black);
      margin-bottom: var(--spacing-sm);

      @media (max-width: 899px) {
        font-size: var(--font-size-xl);
      }
    }
  }

  &__subtitle {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--text-color-gray);
    margin: 0;
  }

  &__content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);

    @media (max-width: 1199px) {
      grid-template-columns: 1fr;
      gap: var(--spacing-xl);
    }
  }

  &__column {
    display: flex;
    flex-direction: column;
    height: fit-content;
  }

  &__section {
    background-color: var(--color-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-xs);
    padding: var(--spacing-xxl);
    height: 100%;
    // Ensure dropdowns are not clipped
    overflow: visible !important;

    @media (max-width: 899px) {
      padding: var(--spacing-lg);
    }

    &-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-xl);
      padding-bottom: var(--spacing-lg);
      border-bottom: var(--normal-sec-border);
    }

    &-title {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);

      h2 {
        font-family: var(--font-family-primary);
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-semibold);
        color: var(--text-color-black);
        margin: 0;
      }

      .svg-icon {
        color: var(--icon-color-primary);
      }
    }
  }

  &__filters {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
    align-items: center;

    @media (max-width: 1499px) {
      display: none;
    }

    .custom-search-container,
    .custom-select-container {
      min-width: 200px;
      flex: 1;

      @media (max-width: 1299px) {
        min-width: 150px;
      }
    }
  }

  &__data-grid {
    .text-ellipsis {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 100%;
    }

    .title-text {
      font-family: var(--font-family-primary);
      font-size: var(--font-size-sm);
      color: var(--text-color-black);

      &.fw600 {
        font-weight: var(--font-weight-semibold);
      }
    }

    .sub-title-text {
      font-family: var(--font-family-primary);
      font-size: var(--font-size-xs);
      color: var(--text-color-gray);
    }

    .actions {
      gap: var(--spacing-sm);

      .MuiSvgIcon-root {
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          transform: scale(1.1);
        }
      }
    }
  }

  &__pagination {
    margin-top: var(--spacing-lg);
    display: flex;
    justify-content: flex-end;
  }

  &__export-btn {
    .MuiSvgIcon-root {
      color: var(--icon-color-primary);
    }

    &:hover {
      .MuiSvgIcon-root {
        color: var(--icon-color-white);
      }
    }
  }
}
// Arrow icons for sortable headers
.arrow-wrap {
  margin-left: var(--spacing-xs);

  .arrow-icon {
    color: var(--icon-color-light-dark);
    transition: all 0.2s ease;

    &:hover {
      color: var(--icon-color-primary);
    }
  }
}

.wrap-header-text {
  gap: var(--spacing-xs);
}

// Responsive adjustments
@media (max-width: 1499px) {
  .public-recipe-dashboard {
    &__filters {
      display: none !important;
    }
  }
}

@media (max-width: 1199px) {
  .public-recipe-dashboard {
    &__content {
      grid-template-columns: 1fr;
    }
  }
}

@media (max-width: 899px) {
  .public-recipe-dashboard {
    &__section {
      padding: var(--spacing-lg);

      &-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);

        .public-recipe-dashboard__section-title {
          width: 100%;
        }
      }
    }

    &__table-container {
      .MuiDataGrid-root {
        .MuiDataGrid-columnHeader,
        .MuiDataGrid-cell {
          padding: var(--spacing-sm);
        }
      }
    }
  }
}
.recipe-analytics-export-popover{
  .export-option{
    padding: 0px;
  }
}