@import '../../../styles/variable.scss';

.org-custom-pagination-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  @media (max-width: 499px) {
    display: flex;
    flex-direction: column;
    row-gap: 20px;
  }
  .page-number {
    color: $color-Dark-40;
  }

  .arrow-icon {
    background-color: transparent !important;
    padding: 0px;
    svg {
      width: 16px;
      height: 16px;
    }
  }

  .Mui-disabled {
    svg {
      //   opacity: 0.4;
    }
  }

  .ml24 {
    margin-left: 24px;
  }

  .mr8 {
    margin-right: 8px;
  }

  .page-select {
    min-width: 61px;
    border-radius: var(--border-radius-md);
    color: var(--text-dark);

    .MuiSelect-select {
      padding: 8px 5px 8px 12px;
      //   padding: 9.5px 11px;
      //   font-family: $PrimaryFont;
      //   font-size: 14px;
      //   line-height: 21px;
      //   letter-spacing: -0.5px;
    }

    .MuiSelect-nativeInput {
      padding: 0px;
      border: none;
    }

    svg {
      top: 5px;
      @media (max-width: 599px) {
        top: 8px;
      }
    }

    .MuiOutlinedInput-input:hover {
      .MuiOutlinedInput-notchedOutline {
        border-color: transparent;
      }
    }
  }

  .Mui-focused {
    .MuiOutlinedInput-notchedOutline {
      border: 1px solid $color-Dark-10;
    }
  }
  .page-limit {
    width: 70px;
    margin-left: 16px;
    .MuiFormLabel-root {
      display: none;
    }
    .MuiInputBase-root {
      min-height: 40px !important;
    }
    .MuiInputBase-input {
      padding: 7.5px;
      text-align: center;
    }
  }
  .per-page-text {
    margin-right: 8px;
  }
  .MuiStack-root {
    .MuiButtonBase-root {
      border-radius: var(--border-radius-md);
      font-family: var(--font-family-primary);
      font-size: var(--font-size-sm);
      color: var(--text-color-slate-gray);
      margin: var(--spacing-none) var(--spacing-xs);
    }
    .MuiPaginationItem-previousNext {
      border: var(--normal-sec-border);
      border-radius: var(--border-radius-md);
      color: var(--icon-color-primary);
      margin: var(--spacing-none) var(--spacing-sm);
      // color: var(--text-color-primary);
      .MuiSvgIcon-root {
        height: 16px;
        width: 16px;
      }
    }
    .Mui-disabled {
      .MuiSvgIcon-root {
        fill: var(--text-color-slate-gray);
      }
    }
    // border: 1px solid #006BFF33
    .Mui-selected {
      background-color: var(--color-white);
      border: var(--normal-sec-border);
      color: var(--text-color-primary);
    }
  }
}
.bt0 {
  border-top: 0 !important;
}
.custom-pagination-select {
  max-width: none;
  width: auto;
  margin-top: 5px;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  border: var(--normal-sec-border);

  .MuiButtonBase-root {
    padding: 2.5px 11px;
    font-family: $PrimaryFont;
    font-size: 14px;
    line-height: 21px;
    letter-spacing: -0.5px;
    font-weight: 600;
    color: $color-Dark-80;

    &:hover {
      background-color: var(--color-primary) !important; // $color-Primary-100;
      color: var(--text-color-white);

      svg {
        path {
          stroke: $color-White;
        }
      }
    }
  }

  .Mui-selected {
    background-color: var(--color-primary) !important; // $color-Primary-100;
    color: $color-White;
  }
}
::-webkit-scrollbar-thumb {
  background: var(--color-primary);
  border-radius: 3px;
}
