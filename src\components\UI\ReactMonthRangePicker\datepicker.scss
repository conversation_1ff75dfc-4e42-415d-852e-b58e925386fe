@import '@/styles/variable.scss';

.react-datepicker-sec {
  .react-datepicker {
    font-family: var(--font-family-primary);
  }

  .react-datepicker-wrapper {
    width: 100%;

    .react-datepicker__close-icon {
      right: 40px;
      top: 1px;
      cursor: pointer;
      z-index: 999;

      &::after {
        background-color: rgba(0, 0, 0, 0.54);
      }
    }

    .MuiInputBase-input {
      padding: 15px 7px 15px 15px;
    }
    fieldset,
    .MuiOutlinedInput-notchedOutline {
      border: var(--field-border) !important;
      border-radius: 8px !important;
      margin-top: 4px !important;
    }
  }
  .MuiFormLabel-root,
  .MuiFormLabel-root span {
    font-family: var(--font-family-primary);
    margin-bottom: var(--spacing-xxs);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-bright-blue);
    &.error-label {
      color: var(--text-error);
    }
    .required {
      color: var(--text-error);
      margin-left: var(--spacing-xxs);
    }
  }
  .react-datepicker__tab-loop {
    .react-datepicker__month-container {
      .react-datepicker__day {
        border-radius: 20px;
      }

      .react-datepicker__day--today {
        font-weight: 600;
      }

      .react-datepicker__day--selected {
        background-color: var(--color-primary);
        color: var(--color-white);
        font-weight: 600;
      }

      .react-datepicker__week {
        .react-datepicker__day--keyboard-selected {
          background-color: var(--color-primary);
          color: var(--color-white);
          border: 1px solid var(--color-primary);
          border-radius: 20px;
          font-weight: 600;

          &:hover {
            background-color: var(--color-primary);
            color: var(--color-white);
          }
        }
      }
    }

    .react-datepicker__time {
      .react-datepicker__time-box {
        .react-datepicker__time-list {
          .react-datepicker__time-list-item {
            align-content: center;
          }

          .react-datepicker__time-list-item--selected {
            background-color: $color-light-gray;
            font-weight: 600;
            color: var(--color-black);

            &:hover {
              background-color: $color-light-gray;
            }
          }
        }
      }
    }
  }

  .MuiFormControl-root {
    .MuiInputBase-root {
      .MuiSelect-select {
        z-index: 1;
      }
    }

    .MuiFormLabel-root {
      z-index: -1;
    }
  }
}
.react-Monthpicker-sec {
  .date-picker-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
