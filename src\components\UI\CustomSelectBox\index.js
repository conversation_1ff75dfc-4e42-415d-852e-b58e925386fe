import React from 'react';
import Box from '@mui/material/Box';
import InputLabel from '@mui/material/InputLabel';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { Typography } from '@mui/material';
import './customselectbox.scss';

const CustomSelectBox = ({
  label,
  options,
  value,
  onChange,
  placeholder,
  disabled,
  className,
  menuItem,
  name,
  dropDownClass,
}) => {
  return (
    <Box
      className={className ? `${className} select-box-wrap` : 'select-box-wrap'}
    >
      <FormControl fullWidth>
        <InputLabel id="custom-select-label">{label}</InputLabel>
        <Select
          labelId="custom-select-label"
          value={value}
          label={label}
          onChange={onChange}
          IconComponent={ExpandMoreIcon}
          disabled={disabled}
          displayEmpty
          name={name}
          MenuProps={{
            classes: {
              paper: menuItem
                ? 'select-dropdown-menu model-dropdown-menu'
                : dropDownClass
                  ? `${dropDownClass} select-dropdown-menu`
                  : 'select-dropdown-menu',
            },
            // anchorOrigin: {
            //   vertical: 'bottom',
            //   // horizontal: 'right',
            // },
            // getContentAnchorEl: null, // Prevents default behavior
          }}
        >
          {/* / {/ placeholder option /} */}
          <MenuItem value="" className="select-placeholder" disabled>
            <Typography variant="inherit" className="fw400 placeholder">
              {placeholder}
            </Typography>
          </MenuItem>
          {options?.length === 0 ? (
            <MenuItem className="empty-option" disabled>
              <Typography variant="inherit" className="p16 fw400 placeholder">
                No Data Available
              </Typography>
            </MenuItem>
          ) : (
            options?.map((option) => (
              <MenuItem
                key={option?.value}
                value={option?.value}
                className={menuItem ? `${menuItem} p14 fw400` : 'p16 fw400'}
              >
                {option?.label}
              </MenuItem>
            ))
          )}
        </Select>
      </FormControl>
    </Box>
  );
};

export default CustomSelectBox;
