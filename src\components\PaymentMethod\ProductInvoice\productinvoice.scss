@import '@/styles/variable.scss';

.invoice-container {
  box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.05);
  width: 100%;
  max-width: 600px;
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-md);
  background-color: var(--color-white);

  .status-sent {
    position: absolute;
    left: 13px;
    top: 20px;
    color: var(--text-color-white);
  }
  .status-badge {
    width: 120px;
    background-color: var(--color-green);
    color: var(--color-white);
    padding: var(--spacing-sm);
    margin: var(--spacing-base) var(--spacing-none);
    .status-badge-text {
      font-size: var(--font-size-xs);
      font-family: var(--font-family-primary);
      line-height: var(--line-height-xxs);
    }
    .status-icon {
      margin-right: 5px;
    }
  }
  .company-logo-wrap {
    padding-bottom: var(--spacing-none);
    height: 70px;
  }
  .invoice-number {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    color: var(--text-color-primary);
    font-weight: var(--font-weight-light);
    text-shadow: 0px 0px 1px var(--text-color-primary);
  }
  .invoice-number-text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--text-color-black);
    font-weight: var(--font-weight-semibold);
    letter-spacing: 1px;
  }
  .invoice-text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    color: var(--text-color-black);
    font-weight: var(--font-weight-regular);
  }
  .invoice-header-wrap {
    border-bottom: var(--field-border-primary);
    padding-bottom: var(--spacing-base);
    margin-bottom: var(--spacing-base);
    .active-status {
      background-color: var(--color-green);
      color: var(--text-color-white);
    }
    .fail-status {
      background-color: var(--text-error);
      color: var(--text-color-white);
    }

    .invoice-number-wrap {
      width: 73%;
    }
    .status-chip {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      max-width: fit-content;
      margin-bottom: var(--spacing-sm);
      .MuiChip-label {
        font-family: var(--font-family-primary);
        font-size: var(--font-size-xs);
        font-weight: var(--font-weight-regular);
        padding-left: var(--spacing-xsm);
      }
      .MuiSvgIcon-root {
        font-size: var(--icon-size-xs);
        fill: var(--icon-color-white);
      }
    }
    .invoice-summary-wrap .address-wrap {
      width: 23%;
      line-height: 0px;
    }
    .address-text {
      font-family: var(--font-family-primary);
      font-size: var(--font-size-xs);
      color: var(--text-color-primary);
      font-weight: var(--font-weight-light);
      text-shadow: 0px 0px 1px var(--text-color-primary);
    }
    .company-email {
      color: var(--text-color-primary);
      .text-link {
        color: var(--color-primary);
        text-decoration: none;
        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
  .invoice-table-wrap {
    padding: var(--spacing-none);
    box-shadow: none;
    border-bottom: var(--field-border-primary);
    border-radius: var(--border-radius-none);
    .MuiDataGrid-root {
      border: none;
      border-radius: var(--border-radius-none);
      padding: var(--spacing-none);
      .MuiDataGrid-columnHeaders {
        min-height: 35px !important;
        max-height: 35px !important;
        line-height: 35px !important;
        border-bottom: none !important;

        .MuiDataGrid-columnHeader {
          height: 35px !important;
        }
        .MuiDataGrid-row--borderBottom {
          min-height: 35px !important;
          background-color: none !important;
        }
        .MuiDataGrid-columnHeader {
          background-color: var(--color-primary);
          border-bottom: none !important;
        }
        .MuiDataGrid-columnHeaderTitle {
          color: var(--text-color-white);
          font-size: var(--font-size-sm);
          font-weight: var(--font-weight-medium);
          overflow: visible !important;
        }
        .MuiDataGrid-columnHeaderTitleContainerContent{
          overflow: visible !important;
        }
        .MuiDataGrid-scrollbarFiller {
          border-bottom: none;
        }
      }
      .MuiDataGrid-row {
        .MuiDataGrid-scrollbarFiller {
          border: none;
        }
      }
      .MuiDataGrid-cell {
        display: flex;
        align-items: center;
        border: none;
        .table-body-text {
          font-family: var(--font-family-primary);
          font-size: var(--font-size-xs);
          font-weight: var(--font-weight-regular);
        }
        .table-body-status-text {
          font-family: var(--font-family-primary);
          font-size: var(--font-size-xs);
          font-weight: var(--font-weight-regular);
        }
      }
      .descriptio-text-wrap {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        .descriptio-text {
          width: 100%;
          white-space: normal;
          word-break: break-word;
          font-size: 14px;
          line-height: 1.5;
          padding: 8px 0;
        }
      }
      ::-webkit-scrollbar {
        width: 2px;
        height: 2px;
      }
      ::-webkit-scrollbar-thumb {
        background-color: var(--color-primary);
      }
    }
  }
  .invoice-details {
    width: 100%;
    .invoice-summary-wrap {
      width: 30%;
    }
    .client-address-info {
      width: 50%;
    }
    .balance-due-wrap {
      .invoice-status {
        color: var(--text-green);
      }
    }
    .invoice-summary {
      width: 55%;
      text-align: left;
    }
    .client-text {
      margin-bottom: var(--spacing-xs);
    }
    .skynet-digital-text {
      font-size: var(--font-size-xs);
      font-weight: var(--font-weight-medium);
      color: var(--text-color-black);
      font-family: var(--font-family-primary);
    }
    .due-date-text {
      background-color: var(--color-ice-blue);
      padding: var(--spacing-xsm) var(--spacing-none) var(--spacing-xsm)
        var(--spacing-sm);
    }

    @media (max-width: 899px) {
      .MuiDataGrid-columnHeader {
        height: 35px !important;
        min-height: 35px !important;
      }

      .MuiDataGrid-cell {
        height: 40px !important;
        line-height: 38px !important;
        display: flex;
        align-items: center;
      }

      .MuiDataGrid-row {
        height: 40px !important;
        min-height: 40px !important;
      }
    }
  }
  .balance-due-text {
    padding-bottom: var(--spacing-sm);
    font-size: var(--font-size-base);
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color-black);
  }
  .sub-total-wrap {
    width: 95%;
    margin-top: var(--spacing-base);
    // text-align: right;
    font-weight: bold;
    .summery-total-wrap {
      width: 47%;
      text-align: right;
    }
    // .summery-text-wrap {
    //   width: 21%;
    //   text-align: right;
    // }
    .cost-text-wrap {
      width: 44% !important;
    }
    .summery-text {
      width: 56%;
      padding-bottom: var(--spacing-xs);
      font-size: var(--font-size-sm);
      font-family: var(--font-family-primary);
      font-weight: var(--font-weight-medium);
      color: var(--text-color-black);
    }
    // .balance-due {
    //   color: red;
    // }
  }
  .invoice-number-text-wrap {
    color: var(--text-color-primary);
  }
  .vat-summary-table {
    margin-top: var(--spacing-sm);
    border-bottom: none;
  }
}
