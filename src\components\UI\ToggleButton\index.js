'use client';
import React from 'react';
import { styled } from '@mui/system';
import { Tooltip, Typography } from '@mui/material';

const ToggleContainer = styled('div')(() => ({
  display: 'flex',
  alignItems: 'center',
  position: 'relative',
  width: '100%',
  maxWidth: '150',
  height: '30px',
  backgroundColor: 'white',
  borderRadius: '4px',
  cursor: 'pointer',
  overflow: 'hidden',
  boxShadow: '0px 0px 2px gray',
  fontFamily: 'var(--font-family-primary)',
  fontSize: 'var(--font-size-sm)',
}));

const ToggleBackground = styled('div')(({ width }) => ({
  position: 'absolute',
  top: 0,
  left: 0,
  width: width,
  height: '100%',
  backgroundColor: 'var(--color-primary)',
  borderRadius: '4px',
  transition: 'transform 0.4s ease',
}));

const ToggleOption = styled('div')(({ active }) => ({
  flex: 1,
  textAlign: 'center',
  zIndex: 1,
  fontWeight: '500',
  fontSize: '14px',
  lineHeight: '32px',
  color: active ? '#fff' : '#757575',
  transition: 'color 0.3s ease',
  cursor: 'pointer',
  whiteSpace: 'nowrap',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
}));

const SwitchToggle = ({ options = [], selected, setSelected }) => {
  return (
    <ToggleContainer>
      <ToggleBackground />
      {options?.map((option) => (
        <Tooltip
          key={option?.value}
          title={<Typography>{option?.label}</Typography>}
          arrow
          placement="bottom"
          classes={{ tooltip: 'info-tooltip-container' }}
        >
          <ToggleOption
            className={option?.value === selected ? 'active' : ''}
            active={selected === option?.value}
            onClick={() => setSelected(option?.value)}
          >
            {option?.label}
          </ToggleOption>
        </Tooltip>
      ))}
    </ToggleContainer>
  );
};

export default SwitchToggle;
