import { useState, useEffect, useRef, useCallback } from 'react';
const useTimer = (initialSeconds = 60) => {
  const [timerCount, setTimerCount] = useState(initialSeconds);
  const [isTimerActive, setIsTimerActive] = useState(false);
  const intervalRef = useRef(null);

  const clearTimerInterval = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  useEffect(() => {
    if (isTimerActive) {
      intervalRef.current = setInterval(() => {
        setTimerCount((prevCount) => {
          if (prevCount <= 1) {
            clearTimerInterval();
            setIsTimerActive(false);
            // Don't reset count here, let resetTimer handle it or keep it at 0
            return 0;
          }
          return prevCount - 1;
        });
      }, 1000);
    }

    // Cleanup function to clear interval
    return clearTimerInterval;
  }, [isTimerActive]);

  // Function to start the timer
  const startTimer = useCallback(() => {
    if (!isTimerActive) {
      // Reset count to initial value before starting
      setTimerCount(initialSeconds);
      setIsTimerActive(true);
    }
  }, [isTimerActive, initialSeconds]);

  // Function to reset the timer manually if needed
  const resetTimer = useCallback(() => {
    clearTimerInterval();
    setIsTimerActive(false);
    setTimerCount(initialSeconds);
  }, [initialSeconds]);

  // Ensure cleanup on unmount even if timer wasn't explicitly stopped
  useEffect(() => {
    return clearTimerInterval;
  }, []);

  return { timerCount, isTimerActive, startTimer, resetTimer };
};

export default useTimer;
