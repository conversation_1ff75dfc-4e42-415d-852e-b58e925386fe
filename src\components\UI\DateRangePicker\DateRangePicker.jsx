'use client';

import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import Icon from '@/components/UI/AppIcon/AppIcon';
import CustomButton from '@/components/UI/CustomButton';
// import CustomDatePicker from '@/components/UI/CustomDatePicker';
import { staticOptions } from '@/helper/common/staticOptions';
import dayjs from 'dayjs';
import './daterangepicker.scss';

const DateRangePicker = ({
  value,
  onChange,
  className = '',
  placeholder = 'Select range',
  maxWidth = 'max-content',
  disabled = false,
  presetRanges = null, // Allow custom preset ranges
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [customRange, setCustomRange] = useState({
    startDate: null,
    endDate: null,
  });
  const [dropdownPosition, setDropdownPosition] = useState({
    placement: 'bottom-left',
    style: {},
  });
  const [isPortalReady, setIsPortalReady] = useState(false);
  const dropdownRef = useRef(null);
  const triggerRef = useRef(null);

  const ranges = presetRanges || staticOptions?.ANALYTICS_DATE_RANGES;

  // Check if we're in browser environment
  useEffect(() => {
    setIsPortalReady(typeof window !== 'undefined');
  }, []);

  // Calculate optimal dropdown position
  const calculateDropdownPosition = () => {
    if (!triggerRef.current) return;

    const triggerRect = triggerRef.current.getBoundingClientRect();
    const dropdownHeight = 400; // Estimated height
    const dropdownWidth = 320;
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight,
    };

    const spacing = 8; // margin spacing
    let placement = 'bottom-left';
    let style = {};

    // Check if there's enough space below
    const spaceBelow = viewport.height - triggerRect.bottom - spacing;
    const spaceAbove = triggerRect.top - spacing;

    // Determine vertical position (always use absolute positioning relative to trigger)
    if (spaceBelow >= dropdownHeight || spaceBelow >= spaceAbove) {
      // Place below
      style.top = triggerRect.bottom + spacing;
      placement = 'bottom-left';
    } else {
      // Place above
      style.top = triggerRect.top - dropdownHeight - spacing;
      placement = 'top-left';
    }

    // Determine horizontal position
    const spaceRight = viewport.width - triggerRect.left - spacing;
    const spaceLeft = triggerRect.right - spacing;

    if (spaceRight >= dropdownWidth) {
      // Align to left of trigger
      style.left = triggerRect.left;
      placement = placement.includes('top') ? 'top-left' : 'bottom-left';
    } else if (spaceLeft >= dropdownWidth) {
      // Align to right of trigger
      style.left = triggerRect.right - dropdownWidth;
      placement = placement.includes('top') ? 'top-right' : 'bottom-right';
    } else {
      // Center horizontally with viewport constraints
      style.left = Math.max(
        spacing,
        Math.min(
          triggerRect.left - dropdownWidth / 2 + triggerRect.width / 2,
          viewport.width - dropdownWidth - spacing
        )
      );
    }

    // Add additional classes
    const classes = [`common-date-range-picker__dropdown--${placement}`];

    // Special handling for admin dashboard
    if (document.querySelector('.admin-analytics-dashboard__header')) {
      classes.push('common-date-range-picker__dropdown--admin-dashboard');
    }

    setDropdownPosition({
      placement,
      style,
      classes: classes.join(' '),
    });
  };

  // Update position when dropdown opens or on scroll/resize
  useEffect(() => {
    if (isOpen && isPortalReady) {
      calculateDropdownPosition();

      const handleUpdate = () => {
        calculateDropdownPosition();
      };

      // Add event listeners for scroll and resize
      window.addEventListener('scroll', handleUpdate, true); // Use capture to catch all scroll events
      window.addEventListener('resize', handleUpdate);

      return () => {
        window.removeEventListener('scroll', handleUpdate, true);
        window.removeEventListener('resize', handleUpdate);
      };
    }
  }, [isOpen, isPortalReady]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      // Check if click is inside any MUI date picker popup/portal
      const isDatePickerClick =
        event.target.closest('.MuiPickersPopper-root') ||
        event.target.closest('.MuiPickersLayout-root') ||
        event.target.closest('.MuiDialog-root') ||
        event.target.closest('.MuiPickersDay-root') ||
        event.target.closest('.MuiDayCalendar-root') ||
        event.target.closest('.MuiDateCalendar-root') ||
        event.target.closest('.MuiYearCalendar-root') ||
        event.target.closest('.MuiMonthCalendar-root') ||
        event.target.closest('.MuiCalendarPicker-root') ||
        event.target.closest('.MuiPickersArrowSwitcher-root') ||
        event.target.closest('.MuiIconButton-root') ||
        event.target.closest('[role="dialog"]') ||
        event.target.closest('[data-mui-picker]');

      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target) &&
        triggerRef.current &&
        !triggerRef.current.contains(event.target) &&
        !isDatePickerClick
      ) {
        setIsOpen(false);
      }
    };

    const handleEscapeKey = (event) => {
      if (event.key === 'Escape') {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscapeKey);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isOpen]);

  const handleRangeSelect = (rangeValue) => {
    // if (rangeValue === 'custom') {
    //   // Keep dropdown open for custom range selection
    //   return;
    // }

    onChange?.(rangeValue);
    setIsOpen(false);
  };

  // const handleCustomRangeSubmit = () => {
  //   if (customRange.startDate && customRange.endDate) {
  //     const startDateFormatted = dayjs(customRange.startDate).format(
  //       'YYYY-MM-DD'
  //     );
  //     const endDateFormatted = dayjs(customRange.endDate).format('YYYY-MM-DD');

  //     onChange?.({
  //       type: 'custom',
  //       startDate: startDateFormatted,
  //       endDate: endDateFormatted,
  //     });
  //     setIsOpen(false);
  //   }
  // };

  const getSelectedLabel = () => {
    if (
      typeof value === 'object'
      //  && value?.type === 'custom'
    ) {
      return `${value.startDate} - ${value.endDate}`;
    }

    const preset = ranges.find((range) => range?.value === value);
    return preset ? preset.label : placeholder;
  };

  const getDefaultDates = () => {
    const today = dayjs();
    const lastWeek = today.subtract(7, 'day');

    return {
      startDate: lastWeek,
      endDate: today,
    };
  };

  // Initialize custom range with default dates
  useEffect(() => {
    if (!customRange.startDate && !customRange.endDate) {
      setCustomRange(getDefaultDates());
    }
  }, [customRange.startDate, customRange.endDate]);

  const renderDropdown = () => {
    if (!isOpen || !isPortalReady) return null;

    return (
      <div
        className={`common-date-range-picker__dropdown ${dropdownPosition.classes || ''}`}
        style={{
          position: 'absolute',
          ...dropdownPosition.style,
          zIndex: 1300, // Above MUI date picker poppers (1300) and modals (1000)
        }}
        ref={dropdownRef}
      >
        <div
          className="common-date-range-picker__content"
          onClick={(e) => e.stopPropagation()}
        >
          <h4 className="common-date-range-picker__title">Select Date Range</h4>

          {/* Preset Ranges */}
          <div className="common-date-range-picker__presets">
            {ranges?.map((range) => (
              <CustomButton
                key={range?.value}
                onClick={() => handleRangeSelect(range?.value)}
                className={`common-date-range-picker__preset-option${
                  value === range?.value
                    ? ' common-date-range-picker__preset-option--active'
                    : ''
                }`}
                variant={value === range?.value ? 'contained' : 'outlined'}
                fullWidth
                startIcon={<Icon name="Calendar" size={14} />}
              >
                <div className="common-date-range-picker__preset-content">
                  <div className="common-date-range-picker__preset-label">
                    {range?.label}
                  </div>
                  <div className="common-date-range-picker__preset-description">
                    {range?.description}
                  </div>
                </div>
              </CustomButton>
            ))}
          </div>

          {/* Custom Range */}
          {/* <div className="common-date-range-picker__custom-section">
            <h5 className="common-date-range-picker__custom-title">
              Custom Range
            </h5>

            <div className="common-date-range-picker__custom-inputs">
              <div
                className="common-date-range-picker__input-group"
                onClick={(e) => e.stopPropagation()}
              >
                <CustomDatePicker
                  label="Start Date"
                  value={customRange.startDate}
                  onChange={(newValue) =>
                    setCustomRange({
                      ...customRange,
                      startDate: newValue,
                    })
                  }
                  placeholder="Select start date"
                  format="DD/MM/YYYY"
                  fullWidth
                  className="common-date-range-picker__custom-date-picker"
                  slotProps={{
                    popper: {
                      style: { zIndex: 1400 }, // Higher than dropdown
                    },
                  }}
                />
              </div>

              <div
                className="common-date-range-picker__input-group"
                onClick={(e) => e.stopPropagation()}
              >
                <CustomDatePicker
                  label="End Date"
                  value={customRange.endDate}
                  onChange={(newValue) =>
                    setCustomRange({ ...customRange, endDate: newValue })
                  }
                  placeholder="Select end date"
                  format="DD/MM/YYYY"
                  fullWidth
                  minDate={customRange.startDate}
                  className="common-date-range-picker__custom-date-picker"
                  slotProps={{
                    popper: {
                      style: { zIndex: 1400 }, // Higher than dropdown
                    },
                  }}
                />
              </div>

              <CustomButton
                onClick={handleCustomRangeSubmit}
                className="common-date-range-picker__apply-btn"
                variant="contained"
                fullWidth
                disabled={!customRange.startDate || !customRange.endDate}
              >
                Apply Custom Range
              </CustomButton>
            </div>
          </div> */}
        </div>
      </div>
    );
  };

  return (
    <div
      className={`common-date-range-picker ${className}`}
      style={{ maxWidth }}
    >
      <div ref={triggerRef}>
        <CustomButton
          onClick={() => !disabled && setIsOpen(!isOpen)}
          className="common-date-range-picker__trigger"
          variant="outlined"
          fullWidth
          disabled={disabled}
          startIcon={<Icon name="Calendar" size={16} />}
          endIcon={
            <Icon
              name={isOpen ? 'ChevronUp' : 'ChevronDown'}
              size={16}
              className={`common-date-range-picker__trigger-chevron ${isOpen ? 'common-date-range-picker__trigger-chevron--open' : ''}`}
            />
          }
        >
          <span className="common-date-range-picker__trigger-text">
            {getSelectedLabel()}
          </span>
        </CustomButton>
      </div>

      {/* Portal-based dropdown that follows scroll */}
      {isPortalReady && createPortal(renderDropdown(), document.body)}
    </div>
  );
};

export default DateRangePicker;
