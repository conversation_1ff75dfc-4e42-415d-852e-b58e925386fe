'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Box, Divider, Tooltip, Typography } from '@mui/material';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
// import CustomSelect from '@/components/UI/selectbox';
// import CustomButton from '@/components/UI/button';
// import { CustomTextField } from '@/components/UI/CommonField/index';
import CustomEditor from '@/components/UI/CustomEditor';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import { useParams, useRouter } from 'next/navigation';
// import DialogBox from '../UI/Modalbox';
// import MultiSelect from '../UI/Multiselect';
import RightDrawer from '@/components/UI/RightDrawer';
import _ from 'lodash';
import AssignEmployee from './AssignEmployee';
import CustomTextField from '../UI/CustomTextField';
import CustomSelect from '../UI/CustomSelect';
import CustomButton from '../UI/CustomButton';
import PreLoader from '../UI/Loader';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import PreviewIcon from '@mui/icons-material/Preview';
import HistoryIcon from '@mui/icons-material/History';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import DialogBox from '../UI/Modalbox';
import DeleteModal from '../UI/DeleteModal';

const AddEditEMPContract = ({ isEdit }) => {
  const router = useRouter();
  const { templateId } = useParams();
  const formikRef = useRef(null);
  const initialContentRef = useRef('');
  // const modalFormikRef = useRef(null);
  const [departmentList, setDepartmentList] = useState([]);
  const [loader, setLoader] = useState(false);
  const [editioContent, setEditorContent] = useState('');
  const [templateData, setTemplateData] = useState('');
  const [actionMSGModal, setActionMSGModal] = useState(false);
  const [modalLoader, setModalLoader] = useState(false);

  // const [setStaffList] = useState([]);
  // const [setBranchList] = useState([]);
  // const [setSelectAllDepartment] = useState([]);
  // const [setRoleList] = useState([]);
  const [UserList, setUserList] = useState([]);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  const handleOpenDeleteDialog = () => {
    setDeleteDialogOpen(true);
  };
  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
  };

  // LIST OF DEPARTMENT
  const getDepartmentList = async () => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_DEPARTMENT_LIST +
          `?search=${''}&page=${1}&size=${''}&departmentStatus=active`
      );

      if (status === 200) {
        setLoader(false);
        const alloption = [{ label: 'General', value: 'general' }];
        let filterDepList = data?.data?.map((dept) => ({
          label: dept?.department_name,
          value: dept?.id,
        }));
        let mergeList = _.concat(alloption, filterDepList);
        setDepartmentList(mergeList);

        // const newOption = [{ label: 'Select all', value: 'all' }];
        // let allList = _.concat(newOption, filterDepList);
        // setSelectAllDepartment(allList);
      }
    } catch (error) {
      setLoader(false);
      // setSelectAllDepartment([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // GET TEMPLATE DETAILS
  const getTemplateDetails = async () => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_FILE_LIST + `?template_id=${templateId}`
      );

      if (status === 200) {
        setLoader(false);

        const tempData = data?.data?.map((item) => {
          return {
            ...item,
            cat_id: item?.emp_contract_category?.id,
            cat_name: item?.emp_contract_category?.name,
            cat_type: item?.emp_contract_category?.type,
            dept_id: item?.emp_contract_category?.department_id,
          };
        });

        setTemplateData(tempData?.[0]);
        setEditorContent(tempData?.[0]?.content);
        initialContentRef.current = tempData?.[0]?.content;
      }
    } catch (error) {
      setLoader(false);
      setTemplateData('');
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  useEffect(() => {
    getDepartmentList();
  }, []);

  useEffect(() => {
    if (departmentList.length > 0 && isEdit) {
      getTemplateDetails();
    }
  }, [departmentList, isEdit]);

  // USERS LIST
  // const getUserListDetails = async () => {
  //   setModalLoader(true);
  //   try {
  //     const { status, data } = await axiosInstance.get(URLS?.GET_RENEWL_LIST);
  //     if (status === 200) {
  //       setModalLoader(false);
  //       const newOption = [{ label: 'Select all', value: 'all' }];
  //       let filterUserList = data?.data?.map((user) => ({
  //         label: user?.user_employment_contract?.user_full_name,
  //         value: user?.user_employment_contract?.id,
  //       }));
  //       let mergeList = _.concat(newOption, filterUserList);
  //       setStaffList(mergeList);
  //     }
  //   } catch (error) {
  //     setModalLoader(false);
  //     setStaffList([]);
  //     setApiMessage('error', error?.response?.data?.message);
  //   }
  // };
  // BRNACHS LIST
  // const getBranchList = async () => {
  //   setModalLoader(true);
  //   try {
  //     const { status, data } = await axiosInstance.get(
  //       URLS?.GET_BRANCH_LIST +
  //         `?search=${''}&page=${1}&size=${''}&branchStatus=active`
  //     );
  //     if (status === 200) {
  //       setModalLoader(false);
  //       const alloption = [{ label: 'Select all', value: 'all' }];
  //       let filterUserList = data?.data?.map((user) => ({
  //         label: user?.branch_name,
  //         value: user?.id,
  //         color: user?.branch_color,
  //       }));
  //       let mergeList = _.concat(alloption, filterUserList);
  //       setBranchList(mergeList);
  //     }
  //   } catch (error) {
  //     setModalLoader(false);
  //     setBranchList([]);
  //     setApiMessage('error', error?.response?.data?.message);
  //   }
  // };
  // ROLES LIST
  // const getRoleList = async () => {
  //   setModalLoader(true);
  //   try {
  //     const { status, data } = await axiosInstance.get(URLS?.GET_ROLE_LIST);

  //     if (status === 200) {
  //       setModalLoader(false);
  //       const alloption = [{ label: 'Select all', value: 'all' }];
  //       let filterUserList = data?.data?.map((user) => ({
  //         label: user?.role_name,
  //         value: user?.id,
  //       }));
  //       let mergeList = _.concat(alloption, filterUserList);
  //       setRoleList(mergeList);
  //     }
  //   } catch (error) {
  //     setRoleList([]);
  //     setModalLoader(false);
  //     setApiMessage('error', error?.response?.data?.message);
  //   }
  // };
  // User by template LIST
  const getUserList = async () => {
    setModalLoader(true);
    const sendData = {
      ...(templateData?.emp_contract_category?.type === 'department' && {
        department_user_id: templateData?.department_user_id,
      }),
      ...(templateData?.emp_contract_category?.type === 'general' && {
        general_user_id: templateData?.general_user_id,
      }),
      ...(templateData?.emp_contract_category?.type === 'department' && {
        additional_user_id: templateData?.additional_user_id,
      }),
      template_id: templateData?.id,
    };
    try {
      const { status, data } = await axiosInstance.post(
        URLS?.GET_USER_CONTRACT,
        sendData
      );

      if (status === 200) {
        setModalLoader(false);
        setUserList(data?.data);
        setLoader(false);
        if (
          data?.data?.general_user_id?.length > 0 &&
          templateData?.emp_contract_category?.type === 'general'
        ) {
          setActionMSGModal(true);
        } else if (
          (data?.data?.department_user_id?.length > 0 ||
            data?.data?.additional_user_id?.length > 0) &&
          templateData?.emp_contract_category?.type === 'department'
        ) {
          setActionMSGModal(true);
        } else {
          setTimeout(() => {
            router.push('/empcontracts');
          }, 1000);
        }
      }
    } catch (error) {
      setUserList([]);
      setModalLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  useEffect(() => {
    if (isEdit && actionMSGModal) {
      // getUserListDetails();
      // getBranchList();
      // getRoleList();
    }
  }, [isEdit, actionMSGModal]);

  const handleEditContractFor = async (
    department_user_id,
    additional_user_id
  ) => {
    // const extractIds = (array) => array?.map((item) => item.value);

    // const departmentValues = extractIds(
    //   requestdata?.department ? requestdata?.department : []
    // );

    const sendData = {
      ...(templateData?.emp_contract_category?.type !== 'general' && {
        department_user_id: department_user_id,
      }),
      ...(templateData?.emp_contract_category?.type !== 'general' && {
        additional_user_id: additional_user_id,
      }),
      ...(templateData?.emp_contract_category?.type === 'general' && {
        general_user_id: department_user_id,
      }),
      template_id: templateId,
    };
    try {
      setModalLoader(true);
      const { status, data } = await axiosInstance.put(
        URLS.REGENERATE_CONTRACT_FORM,
        sendData
      );

      if (status === 200) {
        setModalLoader(false);
        setApiMessage('success', data?.message);
        setActionMSGModal(false);
        setTimeout(() => {
          router.push('/empcontracts');
        }, 2000);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setModalLoader(false);
      setActionMSGModal(false);
    }
  };

  const handleCloseModal = () => {
    setActionMSGModal(false);
  };

  const handleContract = (type) => {
    if (type === 'preview') {
      router.push(`/contract-view/${templateId}`);
    } else if (type === 'history') {
      router.push(`/contract-view/${templateId}?history=true`);
    } else if (type === 'delete') {
      handleOpenDeleteDialog();
    }
  };

  const handleDeleteTemplate = async () => {
    const sendData = {
      template_ids: [templateId],
      status: 'deleted',
    };

    try {
      setModalLoader(true);
      const { status, data } = await axiosInstance.delete(
        URLS?.DELETE_TEMPLATES,
        {
          data: sendData,
        }
      );
      if (status === 200) {
        if (data.status) {
          setApiMessage('success', data?.message);
          router.push(`/empcontracts`);
        } else {
          setApiMessage('error', data?.message);
        }
      } else {
        setApiMessage('error', data?.message);
      }
      handleCloseDeleteDialog();
      setModalLoader(false);
    } catch (error) {
      setModalLoader(false);
      handleCloseDeleteDialog();
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  return (
    <Box className="create-emp-contract">
      {modalLoader && <PreLoader />}
      {!isEdit && (
        <Box className="d-flex align-center">
          <ArrowBackIosIcon
            className="cursor-pointer mt4"
            onClick={() => {
              router.back();
            }}
          />
          <Typography className="title-sm pr8">Create EMP Contract</Typography>
        </Box>
      )}

      {isEdit && (
        <Box className="d-flex justify-space-between align-center">
          <Box className="d-flex align-center">
            <ArrowBackIosIcon
              className="cursor-pointer mt4"
              onClick={() => {
                router.back();
              }}
            />
            <Typography className="title-sm pr8">Edit EMP Contract</Typography>
          </Box>
          <Box className="d-flex justify-start align-center gap-sm contract-view-btn-list ">
            <Box
              className="d-flex contract-view-btn"
              onClick={() => handleContract('preview')}
            >
              <Tooltip
                title={<Typography>Preview</Typography>}
                arrow
                classes={{ tooltip: 'info-tooltip-container' }}
              >
                <PreviewIcon />
              </Tooltip>
            </Box>
            <Divider orientation="vertical" flexItem />
            <Box
              className="d-flex contract-view-btn"
              onClick={() => handleContract('history')}
            >
              <Tooltip
                title={<Typography>Version History</Typography>}
                arrow
                classes={{ tooltip: 'info-tooltip-container' }}
              >
                <HistoryIcon />
              </Tooltip>
            </Box>
            <Divider orientation="vertical" flexItem />
            <Box
              className="d-flex contract-view-btn"
              onClick={() => handleContract('delete')}
            >
              <Tooltip
                title={<Typography>Delete</Typography>}
                arrow
                classes={{ tooltip: 'info-tooltip-container' }}
              >
                <DeleteOutlineIcon />
              </Tooltip>
            </Box>
          </Box>
        </Box>
      )}

      <Divider className="mb16 mt16" />

      <Formik
        innerRef={formikRef}
        initialValues={{
          contractName: templateData?.name ? templateData?.name : '',
          contractType:
            templateData && templateData?.cat_type === 'general'
              ? 'general'
              : templateData && templateData?.dept_id,
          remark: templateData?.remark ? templateData?.remark : '',
        }}
        enableReinitialize={true}
        validationSchema={Yup.object().shape({
          contractName: Yup.string().trim().required('This field is required'),
          contractType: Yup.string().trim().required('This field is required'),
        })}
        onSubmit={async (requestData) => {
          const sendData = {
            name: requestData?.contractName,
            type:
              requestData?.contractType === 'general'
                ? 'general'
                : 'department',
            department_id:
              requestData?.contractType === 'general'
                ? null
                : requestData?.contractType,
            remark: requestData?.remark,
            content: editioContent,
            ...(isEdit && { template_id: Number(templateId) }),
          };

          try {
            setLoader(true);
            const { status, data } = await axiosInstance.post(
              URLS.CREATE_EMP_CONTRACT,
              sendData
            );

            if (status === 200 || status === 201) {
              if (data?.status) {
                setApiMessage('success', data?.message);

                if (isEdit) {
                  getUserList();
                } else {
                  setLoader(false);
                  setTimeout(() => {
                    router.push('/empcontracts');
                  }, 4000);
                }
              } else {
                setApiMessage('error', data?.message);
                setLoader(false);
              }
            }
          } catch (error) {
            setLoader(false);
            setApiMessage('error', error?.response?.data?.message);
          }
        }}
      >
        {({
          errors,
          touched,
          handleBlur,
          values,
          setFieldValue,
          handleSubmit,
          handleChange,
          dirty,
          isValid,
        }) => (
          <Form onSubmit={handleSubmit}>
            <Box className="">
              <Box className="display-grid">
                <Box>
                  <CustomTextField
                    fullWidth
                    id="contractName"
                    name="contractName"
                    label="Contract Name"
                    placeholder="Contract Name"
                    value={values?.contractName}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={Boolean(touched.contractName && errors.contractName)}
                    helperText={touched.contractName && errors.contractName}
                    required
                  />
                </Box>
                <Box>
                  <CustomSelect
                    name="contractType"
                    label="Position"
                    placeholder="Position"
                    options={departmentList}
                    value={departmentList?.find(
                      (opt) => opt?.value === values?.contractType
                    )}
                    onChange={(e) => setFieldValue('contractType', e?.value)}
                    onBlur={handleBlur}
                    error={Boolean(touched.contractType && errors.contractType)}
                    helperText={touched.contractType && errors.contractType}
                    required
                    isClearable={false}
                  />
                </Box>
              </Box>
              <Box className="mt16">
                <Box>
                  <CustomTextField
                    fullWidth
                    id="remark"
                    name="remark"
                    label="Remark"
                    placeholder="Remark"
                    onChange={handleChange}
                    onBlur={handleBlur}
                    value={values?.remark}
                  />
                </Box>
              </Box>

              <Box className="mt16">
                <Typography className="other-field-label">
                  Content
                  <span className="required">*</span>
                </Typography>
                <CustomEditor
                  content={editioContent}
                  setContent={setEditorContent}
                  height="400px"
                />
              </Box>
            </Box>

            <Box className="form-actions-btn">
              <CustomButton
                title={`${loader ? 'Saving...' : 'Save'}`}
                disabled={
                  loader ||
                  !isValid ||
                  (!dirty &&
                    editioContent.trim() === initialContentRef.current?.trim())
                }
                type="submit"
              />
              {/* <CustomButton
                title="Preview"
                disabled={loader}
                fullWidth={false}
              /> */}
            </Box>
          </Form>
        )}
      </Formik>
      <RightDrawer
        anchor={'right'}
        open={actionMSGModal}
        onClose={() => {
          handleCloseModal(false);
        }}
        className="assign-employee-drawer"
        title="Assign contract"
        subTitle={
          templateData?.emp_contract_category?.type === 'general'
            ? 'General Contract'
            : 'Job Role & Additional Duties'
        }
        content={
          <>
            <AssignEmployee
              setClose={setActionMSGModal}
              AssignEmployees={handleEditContractFor}
              assignId={[]}
              SelectedID={''}
              templateData={templateData}
              UserList={UserList}
            />
          </>
        }
      />

      <DialogBox
        open={deleteDialogOpen}
        handleClose={handleCloseDeleteDialog}
        className="delete-modal"
        dividerClass="delete-modal-divider"
        title="Confirmation"
        content={
          <DeleteModal
            handleCancel={handleCloseDeleteDialog}
            handleConfirm={handleDeleteTemplate}
            text="Are you sure you want to delete?"
          />
        }
      />
    </Box>
  );
};

export default AddEditEMPContract;
