import React from 'react';
import { Box } from '@mui/material';
import { generateMetadata } from '@/helper/common/commonFunctions';
import WsrReq from '@/components/DSR/WSRRequest/WsrReq';

export const metadata = generateMetadata({
  pageTitle: 'WSR',
});

const WSRPage = ({ params }) => {
  return (
    <Box className="main-page-container">
      <WsrReq params={params} />
    </Box>
  );
};

export default WSRPage;
