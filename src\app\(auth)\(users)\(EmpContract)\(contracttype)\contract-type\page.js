'use client';
import React, { useEffect, useState } from 'react';
import { Box, CircularProgress, Typography, Tooltip } from '@mui/material';
import CustomButton from '@/components/UI/button';
import DialogBox from '@/components/UI/Modalbox';
import Searchbar from '@/components/UI/SearchBar';
import CustomSelect from '@/components/UI/selectbox';
import { DataGrid } from '@mui/x-data-grid';
import CustomPagination from '@/components/UI/pagination';
import TuneIcon from '@mui/icons-material/Tune';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import moment from 'moment';
import { identifiers } from '@/helper/constants/identifier';
import EditIcon from '@/components/ActionIcons/EditIcon';
import { useRouter } from 'next/navigation';
import DeleteIcon from '@/components/ActionIcons/DeleteIcon';
import './contracttype.scss';

export default function ContractType() {
  const router = useRouter();
  const [filter, setFilter] = useState(false);
  const [loader, setLoader] = useState(true);
  const [leaveTypeList, setLeaveTypeList] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [searchValue, setSearchValue] = useState('');
  const [filterData, setFilterData] = useState({ status: '' });
  const [filterDataApplied, setFilterDataApplied] = useState({ status: '' });
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const columns = [
    {
      field: 'id',
      headerName: 'ID',
      width: 48,
      minWidth: 48,
      flex: 0,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
    },
    {
      field: 'name',
      headerName: 'Name',
      width: 200,
      minWidth: 200,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box
            className="d-flex align-center justify-start h100 cursor-pointer"
            onClick={() => handleRedirect(params?.row?.id)}
          >
            <Tooltip
              title={params?.value}
              classes={{
                tooltip: 'table-list-tooltip',
              }}
              placement="bottom-start"
            >
              <Typography className="p14 text-ellipsis">
                {params?.value}
              </Typography>
            </Tooltip>
          </Box>
        );
      },
    },
    {
      field: 'working_hours',
      headerName: 'Working Hours',
      width: 120,
      minWidth: 120,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box
            className="d-flex align-center justify-start h100 cursor-pointer"
            onClick={() => handleRedirect(params?.row?.id)}
          >
            <Tooltip
              title={
                <Typography className="p14 text-ellipsis">
                  {params?.value}
                  <span className="pl4">
                    hours working in {params?.row?.duration_type}
                  </span>
                </Typography>
              }
              classes={{
                tooltip: 'table-list-tooltip',
              }}
              placement="bottom-start"
            >
              <Typography className="p14 text-ellipsis">
                {params?.value}
                <span className="pl4 text-capital">
                  ({params?.row?.duration_type?.charAt(0)})
                </span>
              </Typography>
            </Tooltip>
          </Box>
        );
      },
    },
    {
      field: 'wage_per_hour',
      headerName: 'Wage Per Hour',
      width: 120,
      minWidth: 120,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box
            className="d-flex align-center justify-start h100 cursor-pointer"
            onClick={() => handleRedirect(params?.row?.id)}
          >
            <Tooltip
              title={
                <Typography className="p14 text-ellipsis">
                  <span className="pl4 text-capital">
                    {params?.row?.wage_type !== 'fixed'
                      ? ''
                      : params?.row?.wage_type}
                  </span>{' '}
                  {params?.value}
                  <span className="pl4">wage per hour</span>
                </Typography>
              }
              classes={{
                tooltip: 'table-list-tooltip',
              }}
              placement="bottom-start"
            >
              <Typography className="p14 text-ellipsis">
                {params?.value}
                <span className="pl4 text-capital">
                  ({params?.row?.wage_type?.charAt(0)})
                </span>
              </Typography>
            </Tooltip>
          </Box>
        );
      },
    },
    {
      field: 'remark',
      headerName: 'Remark',
      width: 250,
      minWidth: 250,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box
            className="d-flex align-center justify-start h100 cursor-pointer"
            onClick={() => handleRedirect(params?.row?.id)}
          >
            {params?.value !== '-' ? (
              <Tooltip
                title={params?.value}
                classes={{
                  tooltip: 'table-list-tooltip',
                }}
                placement="bottom-start"
              >
                <Typography className="p14 text-ellipsis">
                  <span>{params?.value}</span>
                </Typography>
              </Tooltip>
            ) : (
              '-'
            )}
          </Box>
        );
      },
    },
    {
      field: 'created_by',
      headerName: 'Created By',
      width: 150,
      minWidth: 150,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box
            className="d-flex align-center justify-start h100 cursor-pointer"
            onClick={() => handleRedirect(params?.row?.id)}
          >
            <Typography className="p14 text-ellipsis">
              {params?.row?.created_by?.user_full_name}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'createdAt',
      headerName: 'Created Date',
      width: 120,
      minWidth: 120,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box
            className="d-flex align-center justify-start h100 cursor-pointer"
            onClick={() => handleRedirect(params?.row?.id)}
          >
            <Typography className="p14 text-ellipsis">
              {params?.value ? moment(params?.value)?.format('DD-MM-YYYY') : ''}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box
            className="d-flex align-center justify-center h100 text-capital cursor-pointer"
            onClick={() => handleRedirect(params?.row?.id)}
          >
            {params?.value === 'inactive' ? (
              <Typography className="p12 failed fw600">
                {params?.value}
              </Typography>
            ) : (
              <Typography className="p12 success fw600">
                {params?.value}
              </Typography>
            )}
          </Box>
        );
      },
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex justify-center">
            <Box className="d-flex justify-start actions">
              <Tooltip title="Edit" arrow className="action-tooltip">
                <Box>
                  <EditIcon
                    onClick={() => {
                      handleRedirect(params?.row?.id);
                      // router.push(`/edit-contract-type/${params?.row?.id}`);
                    }}
                  />
                </Box>
              </Tooltip>
              {params?.row?.status !== 'inactive' && (
                <Tooltip title="Delete" arrow className="action-tooltip">
                  <Box>
                    <DeleteIcon
                      onClick={() => {
                        handleDelete(params?.row);
                      }}
                    />
                  </Box>
                </Tooltip>
              )}
            </Box>
          </Box>
        );
      },
    },
  ];

  const handleRedirect = (id) => {
    router.push(`/edit-contract-type/${id}`);
  };
  const getContractTypeList = async (page, searchValue, filter, Rpp) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_CONTRACT_TYPE +
          `?search=${searchValue}&size=${
            Rpp ? Rpp : rowsPerPage
          }&page=${page}&status=${filter?.status}`
      );
      if (status === 200) {
        setLoader(false);
        const leaveList = data?.data;
        setTotalCount(data?.total);
        setLeaveTypeList(leaveList ? leaveList : []);
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const onPageChange = (newPage) => {
    setCurrentPage(newPage);
    getContractTypeList(newPage, searchValue, filterDataApplied);
  };
  const OnRowPerPage = (newPage) => {
    setRowsPerPage(newPage);
    setCurrentPage(1);
    getContractTypeList(1, searchValue, filterDataApplied, newPage);
  };
  const handleFilterData = (type) => {
    setFilter(false);
    if (type === 'apply') {
      getContractTypeList(1, searchValue, filterData);
      setFilterDataApplied(filterData);
    } else {
      const clearFilter = {
        status: '',
      };
      setFilterData(clearFilter);
      setFilterDataApplied(clearFilter);
      getContractTypeList(1, searchValue, clearFilter);
    }
  };
  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      handleFilterData('apply');
    }
  };
  const handleDelete = async (item) => {
    try {
      setLoader(true);
      const { status, data } = await axiosInstance.delete(
        URLS?.CONTRACT_TYPE + `/${item?.id}`
      );

      if (status === 200) {
        if (data.status) {
          setApiMessage('success', data?.message);
          getContractTypeList(currentPage, searchValue, filterDataApplied);
        } else {
          setApiMessage('error', data?.message);
          getContractTypeList(currentPage, searchValue, filterDataApplied);
        }
      } else {
        setApiMessage('error', data?.message);
      }
      setLoader(false);
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  useEffect(() => {
    getContractTypeList(1, searchValue, filterDataApplied);
  }, []);
  return (
    <>
      <Box className="page-container contract-type">
        <Box className="search-filter-section">
          <Searchbar
            searchclass="staff-leave-search"
            setSearchValue={setSearchValue}
            onKeyPress={handleKeyPress}
          />
          <TuneIcon
            onClick={() => {
              setFilter(!filter);
            }}
            className="filter-icon"
          />
          <CustomButton
            variant="contained"
            background="#39596e"
            backgroundhover="#FFFFFF"
            colorhover="#000000"
            className="p14"
            fontWeight="600"
            title="Apply filter"
            fullWidth={false}
            onClick={() => handleFilterData('apply')}
          />
          <CustomButton
            variant="contained"
            background="#39596e"
            backgroundhover="#FFFFFF"
            colorhover="#000000"
            className="p14"
            fontWeight="600"
            title="Add Contract Type"
            fullWidth={false}
            onClick={() => {
              router.push('/create-contract-type');
            }}
          />
        </Box>
        <Box className="table-container">
          {loader ? (
            <Box className="content-loader">
              <CircularProgress className="loader" color="inherit" />
            </Box>
          ) : (
            <>
              {leaveTypeList && leaveTypeList?.length === 0 ? (
                <Box className="">
                  <Typography className="text-align h6">
                    No data found
                  </Typography>
                </Box>
              ) : (
                <>
                  <DataGrid
                    rows={leaveTypeList && leaveTypeList}
                    columns={columns}
                    pageSize={rowsPerPage}
                    checkboxSelection={false}
                    disableSelectionOnClick
                    hideMenuIcon
                  />
                  <CustomPagination
                    currentPage={currentPage}
                    totalCount={totalCount}
                    rowsPerPage={rowsPerPage}
                    onPageChange={onPageChange}
                    OnRowPerPage={OnRowPerPage}
                  />
                </>
              )}
            </>
          )}
        </Box>
        <DialogBox
          open={filter}
          handleClose={() => {
            setFilter(!filter);
          }}
          title="Leave type filter"
          content={
            <>
              <Box className="staff-filter">
                <Box className="pt32">
                  <Box className="select-box pb32">
                    <CustomSelect
                      placeholder="Status"
                      options={identifiers?.CARD_STATUS}
                      value={filterData?.status}
                      onChange={(e) => {
                        setFilterData({
                          ...filterData,
                          status: e.target.value,
                        });
                      }}
                      label={<span>Status</span>}
                    />
                  </Box>
                  <Box className="create-cancel-button">
                    <CustomButton
                      fullWidth
                      className="p12 secondary-button"
                      type="submit"
                      fontWeight="600"
                      variant="contained"
                      background="#FFFFFF"
                      backgroundhover="#39596e"
                      colorhover="#FFFFFF"
                      title="Cancel"
                      onClick={() => {
                        handleFilterData('cancel');
                      }}
                    />
                    <CustomButton
                      fullWidth
                      className="p12"
                      type="submit"
                      fontWeight="600"
                      variant="contained"
                      background="#39596e"
                      backgroundhover="#FFFFFF"
                      colorhover="#000000"
                      title="Apply"
                      onClick={() => {
                        handleFilterData('apply');
                      }}
                    />
                  </Box>
                </Box>
              </Box>
            </>
          }
        />
      </Box>
    </>
  );
}
