'use client';
import React from 'react';
import { Box, Tooltip, Typography } from '@mui/material';
import { DataGrid, gridClasses } from '@mui/x-data-grid';
import InfoIcon from '@mui/icons-material/Info';
import NoDataView from '@/components/UI/NoDataView';
import CustomSearch from '@/components/UI/CustomSearch';
import CustomButton from '@/components/UI/CustomButton';
import { DateFormat } from '@/helper/common/commonFunctions';
import './holidaylist.scss';

export default function HolidayList({
  userId,
  getUserHolidayList,
  searchValue,
  userHolidayList,
  setSearchValue,
}) {
  const handleFilterData = (type) => {
    // setFilter(false);
    if (type === 'apply') {
      getUserHolidayList(searchValue, userId);
    } else {
      getUserHolidayList(searchValue, userId);
    }
  };
  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      getUserHolidayList(searchValue, userId);
    }
  };

  const columns = [
    {
      field: 'id',
      headerName: 'ID',
      width: 100,
      minWidth: 100,
      flex: 0,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Typography className="title-text">{params?.row?.id}</Typography>
        );
      },
    },
    {
      field: 'holiday_policy_name',
      headerName: 'Holiday Name',
      width: 260,
      minWidth: 260,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center gap-sm">
            <Typography className="title-text text-ellipsis d-flex align-center gap-5">
              <span
                className="holiday-color-dot"
                style={{
                  backgroundColor: params?.row?.holiday_policy_colour
                    ? params?.row?.holiday_policy_colour
                    : '#006bff',
                }}
              ></span>
              {params?.row?.holiday_policy_name}
            </Typography>
            {params?.row?.holiday_policy_description && (
              <Tooltip
                title={params?.row?.holiday_policy_description}
                arrow
                classes={{ tooltip: 'info-tooltip-container' }}
              >
                <InfoIcon className="info-icon cursor-pointer" />
              </Tooltip>
            )}
          </Box>
        );
      },
    },
    {
      field: 'holiday_policy_start_date',
      headerName: 'Date',
      width: 260,
      minWidth: 260,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-start h100 gap-sm flex-wrap">
            <Typography className="title-text">
              {DateFormat(params?.value, 'dates') ===
              DateFormat(params?.row?.holiday_policy_end_date, 'dates')
                ? DateFormat(params?.row?.holiday_policy_end_date, 'dates')
                : `${DateFormat(params?.value, 'dates')} - ${DateFormat(params?.row?.holiday_policy_end_date, 'dates')}`}
            </Typography>{' '}
          </Box>
        );
      },
    },
    {
      field: 'holiday_policy_description',
      headerName: 'Description',
      width: 260,
      minWidth: 260,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-start h100 gap-sm flex-wrap">
            <Typography className="title-text">
              {params?.row?.holiday_policy_description?.length > 50 ? (
                <>
                  {params?.row?.holiday_policy_description?.substring(0, 50)}
                  <Tooltip
                    title={
                      <Box className="tooltip-text-wrap title-text">
                        <Typography className="text-wrap title-text">
                          {params?.row?.holiday_policy_description}
                        </Typography>
                      </Box>
                    }
                    classes={{
                      tooltip: 'table-list-tooltip info-tooltip-container',
                    }}
                    placement="bottom"
                    arrow
                  >
                    <span className="more-text-wrap cursor-pointer">
                      ... More
                    </span>
                  </Tooltip>
                </>
              ) : (
                params?.row?.holiday_policy_description
              )}
            </Typography>{' '}
          </Box>
        );
      },
    },
  ];

  return (
    <Box className="holiday-list-wrap">
      <Box className="d-flex justify-end pb16 gap-10">
        <CustomSearch
          setSearchValue={setSearchValue}
          searchValue={searchValue}
          onKeyPress={handleKeyPress}
        />{' '}
        <Box>
          <CustomButton
            variant="contained"
            title="Search"
            fullWidth={false}
            onClick={() => handleFilterData('apply')}
          />
        </Box>
      </Box>
      {userHolidayList?.length > 0 ? (
        <Box className="table-container table-layout ">
          <DataGrid
            rows={userHolidayList}
            columns={columns}
            pageSize={''}
            autoHeight
            checkboxSelection={false} // Disable default checkbox column
            disableSelectionOnClick // Disable row selection on click
            hideMenuIcon
            getRowHeight={() => 'auto'}
            sx={{
              [`& .${gridClasses.cell}`]: {
                py: 1,
              },
            }}
          />
        </Box>
      ) : (
        <Box className="no-data d-flex align-center justify-center">
          <NoDataView
            title="No Holiday Found"
            description="There is no Holiday data available at the moment."
          />
        </Box>
      )}
    </Box>
  );
}
