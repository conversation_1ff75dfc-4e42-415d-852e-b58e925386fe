import React from 'react';
import { Box, Divider, Typography } from '@mui/material';
import moment from 'moment';
import ArrowOutwardIcon from '@mui/icons-material/ArrowOutward';
import UserAvatar from '@/components/UI/Avatar/UserAvatar';
import { useRouter } from 'next/navigation';
import { saveToStorage } from '@/helper/context';
import { identifiers } from '@/helper/constants/identifier';
import './calenderpopover.scss';
export default function CalenderPopover({ params }) {
  const popoverData = params?.ev?.extendedProps;

  let router = useRouter();
  // Function to generate dates in the range

  // function calculateHoursAndMinutes(startDate, endDate) {
  //   const start = moment(startDate, 'YYYY-MM-DD HH:mm');
  //   const end = moment(endDate, 'YYYY-MM-DD HH:mm');

  //   // Calculate total duration
  //   const duration = moment.duration(end.diff(start));
  //   const totalHours = Math.floor(duration.asHours());
  //   const totalMinutes = duration.minutes();
  //   // Final output format
  //   return `${String(totalHours).padStart(2, '0')}H:${String(totalMinutes).padStart(2, '0')}M`;
  // }
  return (
    <Box className="user-leave-details-wrap">
      <Box className="d-flex align-center justify-space-between mb8">
        <Box className="user-details-wrap d-flex align-center">
          <Box className="user-avatar-wrap">
            <UserAvatar
              name={popoverData.request_from_users?.user_full_name}
              src={popoverData.request_from_users?.user_avatar_link}
              classname="user-avarar"
            />
          </Box>
          <Box className="user-name-wrap">
            <Typography
              variant="h6"
              className="popover-title-wrap title-text-wrap p16"
            >
              {popoverData.request_from_users?.user_full_name}
            </Typography>
            <Typography className="user-role p12" variant="h6">
              {popoverData.leave_role_request?.role_name}
            </Typography>
          </Box>
        </Box>
        <Box className="right-arrow-wrap">
          <ArrowOutwardIcon
            onClick={() => {
              if (params?.type === 'staff') {
                router.push(`/leave-remark/${params?.ev?.publicId}`);
                saveToStorage(identifiers?.RedirectData, {
                  viewMode: 'calendar',
                });
                // setAuthState({ ...authState, listViewMode: 'calendar' });
              } else if (params?.type === 'own') {
                router.push(`/own-leave/${params?.ev?.publicId}`);
                saveToStorage(identifiers?.RedirectData, {
                  viewMode: 'calendar',
                });
                // setAuthState({ ...authState, listViewMode: 'calendar' });
              }
            }}
            className="right-arrow-icon"
          />
        </Box>
      </Box>
      <Divider />
      <Box className="leave-type-wrap d-flex align-center mt8 mb8">
        <Box className="leave-type">
          <Typography variant="h6" className="title-text-wrap p14">
            Leave Type
          </Typography>
          <Typography
            className="leave-text-wrap leave-type-text p12"
            variant="h6"
          >
            {popoverData?.leave_request_type_list?.name}
          </Typography>
        </Box>
        <Box className="leave-date-wrap">
          <Typography variant="h6" className="title-text-wrap p14">
            Start Date
          </Typography>
          <Typography component="p" className="leave-text-wrap p12">
            {moment(popoverData?.start_date).format('DD/MM/YYYY')}
          </Typography>
        </Box>
      </Box>
      <Box className="total-days-wrap d-flex align-center mb8">
        {/* <Box className="leave-date-wrap">
          <Typography variant="h6" className="title-text-wrap p14">
            End Date
          </Typography>
          <Typography component="p" className="leave-text-wrap p12">
            {moment(popoverData?.end_date).format('DD/MM/YYYY')}
          </Typography>
        </Box> */}
        <Box className="total-days-text-wrap">
          <Typography variant="h6" className="title-text-wrap p14">
            {popoverData?.leave_period_type === 'day'
              ? 'Total Days'
              : popoverData?.leave_period_type === 'hour'
                ? 'Total Hours'
                : popoverData?.duration_type === 'Days'
                  ? 'Total Days'
                  : 'Total Hours'}
          </Typography>
          <Typography variant="h6" className="leave-text-wrap p12">
            {`${popoverData?.leave_days} ${
              popoverData?.leave_period_type === 'day'
                ? popoverData?.leave_days === 1
                  ? 'Day'
                  : 'Days'
                : popoverData?.leave_period_type === 'hour'
                  ? popoverData?.leave_days === 1
                    ? 'Hour'
                    : 'Hours'
                  : popoverData?.duration_type === 'Days'
                    ? popoverData?.leave_days === 1
                      ? 'Day'
                      : 'Days'
                    : popoverData?.leave_days === 1
                      ? 'Hour'
                      : 'Hours'
            }`}
            {/* {popoverData?.duration_type === 'Days'
              ? popoverData?.leave_days
              : calculateHoursAndMinutes(
                  popoverData?.start_date,
                  popoverData?.end_date
                )} */}
          </Typography>
        </Box>
        <Box className="leave-status-wrap">
          <Typography variant="h6" className="title-text-wrap p14">
            Status
          </Typography>
          <Typography
            variant="h6"
            className={`p12 ${
              popoverData?.request_status === 'approved'
                ? 'approved-leave'
                : popoverData?.request_status === 'pending'
                  ? 'pending-leave'
                  : popoverData?.request_status === 'rejected'
                    ? 'rejected-leave'
                    : 'canceled-leave'
            }`}
          >
            {popoverData?.request_status}
          </Typography>
        </Box>
      </Box>
      <Box className="leave-reason-wrap">
        <Typography variant="h6" className="title-text-wrap p14">
          Reason
        </Typography>
        <Typography variant="h6" className="leave-text-wrap p12">
          {popoverData?.request_reason}
        </Typography>
      </Box>
    </Box>
  );
}
