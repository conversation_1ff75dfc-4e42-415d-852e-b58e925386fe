@import '@/styles/variable.scss';

.card-container {
  .choose-pln-header-wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-bottom: var(--spacing-base);
    @media (max-width: 768px) {
      flex-direction: column;
      align-items: start;
      gap: var(--spacing-sm);
    }
    .toggle-switch-wrap {
      width: 50%;
      .active {
        background: var(--color-primary);
        border-radius: var(--border-radius-sm);
      }
    }
  }
  .pricing-card {
    // gap: 16px;
    // margin-top: var(--spacing-xl);
    .card-container {
      background-color: var(--color-white);
      padding: 16px 24px;
      border: var(--normal-sec-border);
      border-radius: var(--border-radius-lg);
      box-shadow: var(--box-shadow-xs);
      cursor: pointer;
      .MuiButtonBase-root {
        // color: $color-Primary-100 !important;
        color: var(--icon-color-primary);
      }
      .card-accordian-wrap {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-top: var(--spacing-xs);
        @media (max-width: 575px) {
          display: block;
        }
        .card-wrap {
          gap: var(--spacing-base);
          width: calc(100% - 200px);
          align-items: flex-start;
          @media (max-width: 575px) {
            width: 100%;
            gap: 10px;
          }
          .choose-plan-btn {
            padding: var(--spacing-none);
            margin-top: var(--spacing-xsm);
          }
          .Mui-checked {
            color: var(--icon-color-primary) !important;
          }
          .details {
            width: calc(100% - 24px - 15px);
            @media (max-width: 575px) {
              width: 100%;
            }
          }
        }
        .pricing {
          text-align: right;
          .content-text {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: var(--spacing-xs);
          }
          .storage {
            text-transform: uppercase;
          }
          .employees-icon {
            width: var(--icon-size-xs);
            height: var(--icon-size-xs);
            fill: var(--icon-color-primary);
          }
          .limit-wrap {
            color: var(--text-bright-blue);
            &.storage {
              text-transform: uppercase;
            }
          }
        }
      }
      .renew-payment-text {
        color: var(--text-color-danger);
        margin-top: var(--spacing-sm);
      }
      .plan-offers-wrap {
        .plan-details-section {
          padding-top: var(--spacing-xl);
          margin-left: var(--spacing-lg);
          font-family: var(--font-family-primary);
          p {
            margin-bottom: var(--spacing-sm);
          }
          // .detail-item {
          // gap: var(--spacing-sm);
          // .check-icon {
          //   height: var(--icon-size-xsm);
          //   width: var(--icon-size-xsm);
          //   fill: var(--icon-color-green);
          // }
          // }
        }
      }

      @media (max-width: 575px) {
        padding: 16px 14px;
      }
    }
    .selected {
      border: var(--border-width-xs) var(--border-style-solid)
        var(--border-color-primary);
      padding-top: var(--spacing-2xl);
      position: relative;
      .active-plan {
        position: absolute;
        right: var(--spacing-xl);
        top: 0;
        border-radius: 0 0 var(--field-radius) var(--field-radius);
        border: 1px solid var(--border-color-green);
        border-top: 0;
        padding: var(--spacing-xxs) var(--spacing-sm);
        background-color: var(--color-success-opacity);
        color: var(--text-green);
        text-transform: capitalize;
      }
    }
  }
  .category-group {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  .category-title {
    font-size: var(--font-size-base);
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-base);
    color: var(--text-color-slate-gray);
    margin-top: var(--spacing-xl);
    margin-bottom: var(--spacing-sm);
    // text-decoration: underline;
    // text-underline-offset: 2px;
  }
}
