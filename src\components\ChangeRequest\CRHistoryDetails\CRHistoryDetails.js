import React from 'react';
import { Box, Typography, Divider } from '@mui/material';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';
import RemoveRedEyeIcon from '@mui/icons-material/RemoveRedEye';
import DownloadIcon from '@mui/icons-material/Download';
import HeaderImage from '@/components/UI/ImageSecurity';
import CommentIcon from '@mui/icons-material/Comment';
import { DateFormat } from '@/helper/common/commonFunctions';
import { Config } from '@/helper/context';
import './crhistorydetails.scss';

const CRHistoryDetails = ({ cdata, crData }) => {
  // Array of keys that should be formatted as dates
  const dateKeys = [
    'date_of_birth',
    'createdAt',
    'updatedAt',
    'start_date',
    'end_date',
    'joining_date',
    'expiry_date',
    'issued_date',
    'validity',
    'professional1_start_date',
    'professional1_end_date',
    'professional2_start_date',
    'professional2_end_date',
  ];

  // image upload key - base keys (without _name suffix)
  const imageKeys = [
    'hmrc_p45_form',
    'photoID',
    'brp_back',
    'brp_front',
    'ni_letter',
    'p45',
    'student_letter',
    'statements_dl_utility',
    'share_code',
    'passport_front',
    'passport_back',
    'cv',
    'user_signature',
    'user_avatar',
  ];

  // Helper function to check if a key should be formatted as date
  const isDateKey = (key) => dateKeys.includes(key);

  // Helper function to check if a key is an image key
  const isImageKey = (key) => imageKeys.includes(key);

  // Helper function to get the corresponding _name key for image URL
  const getImageUrlKey = (baseKey) => `${baseKey}_name`;
  const getImageUrl = (baseKey) =>
    Config?.baseURL +
    '/backend-api/v1/public/user/get-file?location=' +
    baseKey;
  // Helper function to format value based on key type
  const formatValue = (key, value) => {
    if (isDateKey(key)) {
      return value ? DateFormat(value, 'dates') : '-';
    }
    return value ? value : '-';
  };
  const download = async (urls, name) => {
    const response = await fetch(urls);
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', name);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };
  return (
    <Box className="change-request-history-details">
      {cdata?.change_request_subject && (
        <Typography className="body-text  fw400">
          <span className="fw500"> Subject : </span>
          <span className="title-text">{cdata?.change_request_subject}</span>
        </Typography>
      )}
      {cdata?.change_request_status === 'pending' ? (
        <Box className="old-new-info-box d-flex">
          {cdata?.old_data && (
            <Box className="old-info-box">
              <Typography className="body-text fw600 pt4 pb8">
                Old information
              </Typography>
              <Box>
                {crData?.old_data && typeof crData?.old_data === 'object' ? (
                  Object.entries(crData?.old_data)
                    .map(([key, value]) => {
                      // Check if this key has a corresponding image URL key in crData?.old_data
                      if (
                        key.endsWith('_name') &&
                        crData?.old_data[key.replace(/_name$/, '')]
                      ) {
                        return null;
                      }
                      // Check if this is an image key
                      if (isImageKey(key)) {
                        const imageUrlKey = getImageUrlKey(key);
                        const imageUrl = getImageUrl(
                          crData?.old_data[imageUrlKey]
                        );
                        return (
                          <div
                            key={key}
                            className={
                              value
                                ? 'file-grid-container file-grid-container-old'
                                : 'd-flex'
                            }
                          >
                            <Typography className="title-text fw500 pb4">
                              {key ? key?.replace(/_/g, ' ') : ''} :
                            </Typography>
                            {value ? (
                              <Box className="selected-files selected-view-files file-grid-item">
                                <Box className="file-name">
                                  <Box className="d-flex align-center gap-sm">
                                    <InsertDriveFileIcon className="file-icon" />
                                    <Typography className="title-text text-ellipsis-line text-capital">
                                      {(key
                                        ? key?.replace(/_/g, ' ') + ' '
                                        : '') + value}
                                    </Typography>
                                  </Box>
                                </Box>
                                {imageUrl && (
                                  <HeaderImage
                                    type="url"
                                    imageUrl={imageUrl}
                                    Content={<RemoveRedEyeIcon />}
                                    className="d-flex align-center"
                                  />
                                )}
                                <DownloadIcon
                                  className="ml8"
                                  onClick={() => download(imageUrl, value)}
                                />
                              </Box>
                            ) : (
                              <span className="title-text fw400 pl4">-</span>
                            )}
                          </div>
                        );
                      }

                      // Regular key-value display
                      return (
                        <div key={key}>
                          <Typography className="title-text fw500 pb4">
                            {key ? key?.replace(/_/g, ' ') : ''} :{' '}
                            <span className="title-text fw400">
                              {formatValue(key, value)}
                            </span>
                          </Typography>
                        </div>
                      );
                    })
                    .filter(Boolean) // Remove null entries
                ) : (
                  <>
                    {crData?.old_data}
                    <br />
                  </>
                )}
              </Box>
            </Box>
          )}
          {cdata?.old_data && cdata?.new_data && (
            <Divider
              className="cr-history-divider"
              orientation="vertical"
              flexItem
            />
          )}
          {cdata?.new_data && (
            <Box className="new-info-box">
              <Typography className="body-text  fw600 pt4 pb8">
                New information
              </Typography>
              <Box>
                {crData?.new_data && typeof crData?.new_data === 'object' ? (
                  Object.entries(crData?.new_data)
                    .map(([key, value]) => {
                      // Skip _name keys as they will be handled with their base keys
                      if (
                        key.endsWith('_name') &&
                        crData?.new_data[key.replace(/_name$/, '')]
                      ) {
                        return null;
                      }

                      // Check if this is an image key
                      if (isImageKey(key)) {
                        const imageUrlKey = getImageUrlKey(key);
                        const imageUrl = getImageUrl(
                          crData?.new_data[imageUrlKey]
                        );
                        return (
                          <div
                            key={key}
                            className={
                              value
                                ? 'file-grid-container file-grid-container-old'
                                : 'd-flex'
                            }
                          >
                            <Typography className="title-text fw500 pb4">
                              {key ? key?.replace(/_/g, ' ') : ''} :
                            </Typography>
                            {value ? (
                              <Box className="selected-files selected-view-files file-grid-item">
                                <Box className="file-name">
                                  <Box className="d-flex align-center gap-sm">
                                    <InsertDriveFileIcon className="file-icon" />
                                    <Typography className="title-text text-ellipsis-line text-capital">
                                      {(key
                                        ? key?.replace(/_/g, ' ') + ' '
                                        : '') + value}
                                    </Typography>
                                  </Box>
                                </Box>
                                {imageUrl && (
                                  <HeaderImage
                                    type="url"
                                    imageUrl={imageUrl}
                                    Content={<RemoveRedEyeIcon />}
                                    className="d-flex align-center"
                                  />
                                )}
                                <DownloadIcon
                                  className="ml8"
                                  onClick={() => download(imageUrl, value)}
                                />
                              </Box>
                            ) : (
                              <span className="title-text fw400 pl4">-</span>
                            )}
                          </div>
                        );
                      }

                      // Regular key-value display
                      return (
                        <div key={key}>
                          <Typography className="title-text fw500 pb4">
                            {key ? key?.replace(/_/g, ' ') : ''} :{' '}
                            <span className="title-text fw400">
                              {formatValue(key, value)}
                            </span>
                          </Typography>
                        </div>
                      );
                    })
                    .filter(Boolean) // Remove null entries
                ) : (
                  <>
                    {crData?.new_data}
                    <br />
                  </>
                )}
              </Box>
            </Box>
          )}
        </Box>
      ) : (
        <></>
      )}

      {/* {cdata &&
        cdata?.change_request_files &&
        cdata?.change_request_files?.length > 0 && (
          <Box className="pt4">
            <Typography className="body-text  fw500 pt4">
              Attached file
            </Typography>
            <Box className="file-grid-container">
              {cdata?.change_request_files?.map((f) => {
                const url = crData?.baseUrl + f;
                return (
                  <Box
                    key={f}
                    className="selected-files selected-view-files file-grid-item"
                  >
                    <Box className="file-name">
                      <Box className="d-flex align-center gap-sm">
                        <InsertDriveFileIcon className="file-icon" />
                        <Typography className="title-text text-ellipsis-line">
                          {getImageName(f)}
                        </Typography>
                      </Box>
                    </Box>
                    <HeaderImage
                      type="url"
                      imageUrl={url}
                      Content={<RemoveRedEyeIcon />}
                      className="d-flex align-center"
                    />
                  </Box>
                );
              })}
            </Box>
          </Box>
        )} */}

      {cdata?.change_request_remark && (
        <Box>
          <Typography className="body-text fw500 pt8">Remark</Typography>
          <Box className="d-flex align-start">
            <CommentIcon className="res-remark-icon" />
            <Typography className="title-text">
              {cdata?.change_request_remark}
            </Typography>
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default CRHistoryDetails;
