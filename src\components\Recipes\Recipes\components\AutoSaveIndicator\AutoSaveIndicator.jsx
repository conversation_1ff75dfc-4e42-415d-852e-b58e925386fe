import React from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import './AutoSaveIndicator.scss';

const AutoSaveIndicator = ({ status, lastSaved = new Date() }) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'saving':
        return {
          icon: 'Loader2',
          text: 'Saving...',
          iconClass: 'auto-save-indicator__icon--spinning',
        };
      case 'saved':
        return {
          icon: 'CheckCircle',
          text: 'Saved',
          iconClass: '',
        };
      case 'draft':
        return {
          icon: 'FileText',
          text: 'Draft',
          iconClass: '',
        };
      case 'publish':
        return {
          icon: 'Globe',
          text: 'Published',
          iconClass: '',
        };
      case 'unpublished':
        return {
          icon: 'EyeOff',
          text: 'Unpublished',
          iconClass: '',
        };
      case 'archived':
        return {
          icon: 'Archive',
          text: 'Archived',
          iconClass: '',
        };
      case 'error':
        return {
          icon: 'AlertCircle',
          text: 'Save failed',
          iconClass: '',
        };
      default:
        return;
    }
  };

  const formatLastSaved = () => {
    if (!lastSaved) return '';

    // Convert string to Date object if needed
    const lastSavedDate =
      typeof lastSaved === 'string' ? new Date(lastSaved) : lastSaved;

    // Check if date is valid
    if (isNaN(lastSavedDate.getTime())) return '';

    const now = new Date();
    const diffInMinutes = Math.floor((now - lastSavedDate) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes === 1) return '1 minute ago';
    if (diffInMinutes < 60) return `${diffInMinutes} minutes ago`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours === 1) return '1 hour ago';
    if (diffInHours < 24) return `${diffInHours} hours ago`;

    return lastSavedDate.toLocaleDateString();
  };

  const shouldShowTimestamp = () => {
    return ['saved', 'draft', 'publish', 'unpublished', 'archived'].includes(
      status
    );
  };

  const config = getStatusConfig();

  return (
    <div className={`auto-save-indicator auto-save-indicator--${status}`}>
      {/* Desktop: Always visible indicator */}
      <div className="auto-save-indicator__desktop">
        {status && (
          <div className="auto-save-indicator__status">
            <Icon
              name={config?.icon}
              size={14}
              color="currentColor"
              className={`auto-save-indicator__icon ${config?.iconClass}`}
            />
            <span className="auto-save-indicator__text">{config?.text}</span>
          </div>
        )}

        {shouldShowTimestamp() && lastSaved && (
          <div className="auto-save-indicator__timestamp">
            <Icon name="Clock" size={12} color="currentColor" />
            <span className="auto-save-indicator__timestamp-text">
              {formatLastSaved()}
            </span>
          </div>
        )}
      </div>

      {/* Mobile: Temporary indicator that appears during save operations */}
      <div className={`auto-save-indicator__mobile`}>
        <div className="auto-save-indicator__status">
          {status && (
            <Icon
              name={config?.icon}
              size={16}
              color="currentColor"
              className={`auto-save-indicator__icon ${config?.iconClass}`}
            />
          )}
          <div className="auto-save-indicator__mobile-content">
            {status && (
              <span className="auto-save-indicator__text">{config?.text}</span>
            )}
            {shouldShowTimestamp() && lastSaved && (
              <p className="auto-save-indicator__timestamp-text">
                {formatLastSaved()}
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AutoSaveIndicator;
