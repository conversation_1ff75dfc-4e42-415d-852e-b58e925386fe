import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { removeFromStorage } from '@/helper/context';
import { identifiers } from '@/helper/constants/identifier';
import { checkOrganizationRole } from '@/helper/common/commonFunctions';

export const handleLogout = async (router, isDeviceId) => {
  if (isDeviceId) {
    const sendData = {
      webAppToken: isDeviceId,
    };
    try {
      const { status } = await axiosInstance.post(URLS?.LOGOUT, sendData);

      if (status === 200) {
        setTimeout(() => {
          if (
            checkOrganizationRole('super_admin') ||
            checkOrganizationRole('org_master') ||
            checkOrganizationRole('staff')
          ) {
            router.push('/login');
          } else {
            router.push('/');
          }
          removeFromStorage(identifiers?.AUTH_DATA);
          removeFromStorage(identifiers?.USER_DATA);
          removeFromStorage(identifiers?.RedirectData);
          removeFromStorage(identifiers?.DEVICEID);
          removeFromStorage(identifiers?.DEVICEDATA);
        }, 100);
        setTimeout(() => {
          setApiMessage('success', 'Logout successfully');
        }, 1000);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setTimeout(() => {
        if (
          checkOrganizationRole('super_admin') ||
          checkOrganizationRole('org_master') ||
          checkOrganizationRole('staff')
        ) {
          router.push('/login');
        } else {
          router.push('/');
        }
        removeFromStorage(identifiers?.AUTH_DATA);
        removeFromStorage(identifiers?.USER_DATA);
        removeFromStorage(identifiers?.RedirectData);
        removeFromStorage(identifiers?.DEVICEID);
        removeFromStorage(identifiers?.DEVICEDATA);
      }, 100);
      setTimeout(() => {
        setApiMessage('success', 'Logout successfully');
      }, 1000);
    }
  } else {
    setTimeout(() => {
      if (
        checkOrganizationRole('super_admin') ||
        checkOrganizationRole('org_master') ||
        checkOrganizationRole('staff')
      ) {
        router.push('/login');
      } else {
        router.push('/');
      }
      removeFromStorage(identifiers?.AUTH_DATA);
      removeFromStorage(identifiers?.USER_DATA);
      removeFromStorage(identifiers?.RedirectData);
      removeFromStorage(identifiers?.DEVICEID);
      removeFromStorage(identifiers?.DEVICEDATA);
    }, 100);
    setTimeout(() => {
      setApiMessage('success', 'Logout successfully');
    }, 1000);
  }
};
