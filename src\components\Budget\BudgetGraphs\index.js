'use client';

import React, { useContext } from 'react';
import { Box, Typography } from '@mui/material';
import AuthContext from '@/helper/authcontext';
import InfiniteScroll from 'react-infinite-scroll-component';
import AreaChart from '@/components/UI/AreaChart/index';
import RadialChart from '@/components/UI/RadialChart/index';
import _ from 'lodash';
import '../BudgetDetails/budgetdetails.scss';

export default function BudgetGraphs({
  BudgetChartData,
  fetchMoreChartData,
  hasMoreChart,
}) {
  const { userdata, setUserdata, authState, setIsDrawer } =
    useContext(AuthContext);
  return (
    <Box className="Budget-details-section">
      <Box
        id="scrollableSidebar"
        style={{
          height: 'calc(100vh - 200px - var(--banner-height))',
          overflow: 'auto',
        }}
      >
        <InfiniteScroll
          dataLength={BudgetChartData?.length}
          next={fetchMoreChartData}
          hasMore={hasMore<PERSON>hart}
          loader={<Typography>Loading more budget data...</Typography>}
          scrollableTarget="scrollableSidebar"
        >
          {BudgetChartData?.map((item) => {
            return (
              <Box className="Budget-details">
                <Box className="Budget-details-action">
                  <Box>
                    {item?.forecast_branch?.branch_name && (
                      <Typography className="p14 fw600">
                        Branch :{' '}
                        <span className="fw400">
                          {' '}
                          {item?.forecast_branch?.branch_name}
                        </span>
                      </Typography>
                    )}
                    {item?.forecast_year && (
                      <Typography className="p14 fw600">
                        Year :{' '}
                        <span className="fw400">{item?.forecast_year}</span>
                      </Typography>
                    )}
                  </Box>
                </Box>
                <Box className="budget-graphs-section">
                  {item &&
                    item?.forecastBudgetChartData?.map((charts) => {
                      if (charts?.chart_type === 'line') {
                        return (
                          <Box className="budget-graphs area-chart">
                            <AreaChart item={charts} />{' '}
                          </Box>
                        );
                      } else if (charts?.chart_type === 'meter') {
                        return (
                          <Box className="budget-graphs radial-chart">
                            <RadialChart item={charts} />{' '}
                          </Box>
                        );
                      } else {
                        return <></>;
                      }
                    })}
                </Box>
              </Box>
            );
          })}{' '}
        </InfiniteScroll>
      </Box>
    </Box>
  );
}
