'use client';

import React from 'react';
import { Box, Typography } from '@mui/material';
import CustomButton from '../CustomButton';
import './deleteModal.scss';

export default function DeleteModal({
  text,
  handleCancel,
  handleConfirm,
  confirmText = 'Yes, Delete it',
  cancelText = 'Cancel',
}) {
  return (
    <Box className="delete-modal-wrap">
      <Typography className="delete-text">{text}</Typography>
      <Box className="delete-action">
        <CustomButton
          fullWidth
          className="cancel-button"
          variant="outlined"
          title={cancelText}
          onClick={() => handleCancel()}
        />
        <CustomButton
          fullWidth
          className="delete-button"
          variant="contained"
          title={confirmText}
          onClick={() => handleConfirm()}
        />
      </Box>
    </Box>
  );
}
