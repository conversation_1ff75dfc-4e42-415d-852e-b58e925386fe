'use client';
import { useState, useRef } from 'react';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin from '@fullcalendar/interaction';
import resourceTimelinePlugin from '@fullcalendar/resource-timeline';
import timeGridPlugin from '@fullcalendar/timegrid';
import multiMonthPlugin from '@fullcalendar/multimonth';
import { Box, Typography, Tooltip } from '@mui/material';
import moment from 'moment';
import CalenderPopover from '@/components/Leave/CalenderPopover';
import EventsDataShow from '@/components/Leave/EventsDataShow';
import RightDrawer from '@/components/UI/RightDrawer';
import { identifiers } from '@/helper/constants/identifier';
import './calender.scss';

const CustomFullCalendar = ({
  onEventClick,
  getLeaveDetails,
  calenderData,
  getEventColor,
  viewData,
  setViewData,
}) => {
  // const currentDate = moment().format('YYYY-MM-DD');
  const [extraEventsData, setExtraEventsData] = useState([]);
  const [openLeaveDrawer, setOpenLeaveDrawer] = useState(false);
  const calendarRef = useRef(null);
  const toggleDrawer = () => {
    setOpenLeaveDrawer(!openLeaveDrawer);
  };

  const getEventTitleColor = (event) => {
    switch (event) {
      case 'pending':
        return '#db972c';
      case 'approved':
        return '#038d2a';
      case 'rejected':
        return '#d93434';
      case 'cancelled':
        return '#000000';
      default:
        return 'red';
    }
  };

  const renderEventContent = (eventInfo) => {
    const { event } = eventInfo;

    const isHoliday = event.extendedProps.eventType === 'holiday';
    if (isHoliday) {
      // return (
      //   <Box
      //     className="holiday-event-wrap p12"
      //     key={event.id}
      //     style={{
      //       backgroundColor: event?.backgroundColor,
      //       border: `1px solid ${event?.borderColor}`,
      //     }}
      //   >
      //     {event.extendedProps?.holiday_policy_name}
      //   </Box>
      // );
      return (
        <Tooltip
          arrow
          title={
            <Typography>{event.extendedProps?.holiday_policy_name}</Typography>
          }
          classes={{
            tooltip: 'info-tooltip-container ',
          }}
        >
          <Box
            className="holiday-event-wrap p12 text-ellipsis cursor-pointer"
            key={event?.id}
          >
            {event?.extendedProps?.holiday_policy_name}
          </Box>
        </Tooltip>
      );
    }
    const userEvents = event?.extendedProps;
    return (
      <Box className="events-wrap">
        <Box className="event-container-wrap" key={userEvents?.id}>
          <Tooltip
            className="event-title text-ellipsis p12"
            variant="h6"
            style={{
              backgroundColor: getEventColor(userEvents?.request_status),
              color: getEventTitleColor(userEvents?.request_status),
              border: `1px solid ${getEventTitleColor(userEvents?.request_status)}`,
            }}
            interactive
            title={
              <CalenderPopover
                params={{ ev: event?._def, type: calenderData?.type }}
              />
            }
            classes={{
              tooltip: 'calender-leave-popover',
            }}
          >
            {userEvents?.request_from_users?.user_full_name}
          </Tooltip>
        </Box>
      </Box>
    );
  };

  const headerToolbarConfig = {
    left: 'title',
    center: 'timeGridDay,timeGridWeek,dayGridMonth,multiMonthYear',
    right: 'prev,next today',
  };

  const handleMoreClick = (arg) => {
    arg.jsEvent.preventDefault();
    arg.jsEvent.stopPropagation();

    // MutationObserver to remove .fc-popover dynamically when it appears
    const observer = new MutationObserver(() => {
      document.querySelectorAll('.fc-popover').forEach((el) => el.remove());
    });

    // Start observing changes in the body
    observer.observe(document.body, { childList: true, subtree: true });

    let eventsForDate = arg?.allSegs?.map((seg) => seg?.event?._def);

    setExtraEventsData(eventsForDate);
    setOpenLeaveDrawer(true);

    return false;
  };
  const prevDateRef = useRef(null);
  const handleDateRangeChange = (info) => {
    const { view, start, end } = info;

    const currentStart = info?.start;

    // Update previous date reference
    prevDateRef.current = currentStart;
    const dateData = {
      Start: '',
      End: '',
      type: view?.type,
    };
    switch (view?.type) {
      case 'dayGridMonth':
        dateData.Start = moment(start).format('YYYY-MM-DD');
        dateData.End = moment(end).format('YYYY-MM-DD');
        break;
      case 'timeGridWeek':
        dateData.Start = moment(start).format('YYYY-MM-DD');
        dateData.End = moment(end).format('YYYY-MM-DD');
        break;
      case 'timeGridDay':
        dateData.Start = moment(start).format('YYYY-MM-DD');
        dateData.End = moment(end).format('YYYY-MM-DD');
        break;
      case 'multiMonthYear':
        dateData.Start = moment(start).format('YYYY-MM-DD');
        dateData.End = moment(end).format('YYYY-MM-DD');
        break;
      default:
        // Reset data if no match
        dateData.day = '';
        dateData.month = '';
        dateData.year = '';
        dateData.weekStart = '';
        dateData.weekEnd = '';
        dateData.week = '';
    }
    setViewData(dateData);
    getLeaveDetails(dateData);
  };
  const renderEventContentYear = (eventInfo) => {
    const { event } = eventInfo;

    const isHoliday = event.extendedProps.eventType === 'holiday';
    const userEvents = event?.extendedProps;
    if (isHoliday) {
      return (
        <>
          <Typography className="year-holiday">
            <Tooltip
              arrow
              title={
                <Typography>
                  {event.extendedProps?.holiday_policy_name}
                </Typography>
              }
              classes={{
                tooltip: 'info-tooltip-container ',
              }}
            >
              <span className="holiday-dots"></span>
            </Tooltip>
          </Typography>
          {userEvents ? (
            <Box className="events-wrap">
              <Box className="event-container-wrap" key={userEvents?.id}></Box>
            </Box>
          ) : (
            <></>
          )}
        </>
      );
    }
    return (
      <Box className="events-wrap">
        <Box className="event-container-wrap" key={userEvents?.id}>
          <Tooltip
            className="event-title text-ellipsis p12 year-title"
            variant="h6"
            style={{
              backgroundColor: getEventColor(userEvents?.request_status),
              color: getEventTitleColor(userEvents?.request_status),
              border: `1px solid ${getEventTitleColor(userEvents?.request_status)}`,
            }}
            interactive
            title={
              <CalenderPopover
                params={{ ev: event?._def, type: calenderData?.type }}
              />
            }
            classes={{
              tooltip: 'calender-leave-popover',
            }}
          >
            {userEvents?.request_from_users?.user_full_name}
          </Tooltip>
        </Box>
      </Box>
    );
  };
  return (
    <>
      <Box className="calendar-container">
        <FullCalendar
          ref={calendarRef}
          plugins={[
            resourceTimelinePlugin,
            dayGridPlugin,
            interactionPlugin,
            timeGridPlugin,
            multiMonthPlugin,
          ]}
          headerToolbar={headerToolbarConfig}
          initialView="dayGridMonth"
          initialDate={moment().format('YYYY-MM-DD')}
          events={calenderData?.calenderData?.allEvents}
          eventOrder={['holiday', 'leave']}
          eventClick={onEventClick}
          eventContent={
            viewData?.type !== 'multiMonthYear'
              ? (arg) => renderEventContent(arg)
              : (arg) => renderEventContentYear(arg)
          }
          moreLinkClick={(arg) => handleMoreClick(arg)}
          eventLimit={true}
          views={{
            year: {
              type: 'multiMonthYear',
              duration: { months: 12 },
              buttonText: 'Year',
            },
          }}
          datesSet={handleDateRangeChange}
          stickyHeaderDates={false}
          height="auto"
          dayMaxEvents={viewData?.type !== 'multiMonthYear' ? 2 : 1}
          handleWindowResize={true}
        />
        <Box className="leave-type-search-wrap d-flex align-center justify-space-between mt16">
          <Box className="leave-type d-flex align-center">
            {identifiers?.LEAVE_TYPES?.map((type, index) => (
              <Box
                key={index}
                className={`d-flex align-center gap-5  ${type?.className} leave-type-text p14`}
              >
                <span className={`${type?.colorClass} dot-wrap`}></span>
                {type?.label}
              </Box>
            ))}
          </Box>
        </Box>
        <RightDrawer
          className="leave-drawer"
          anchor={'right'}
          open={openLeaveDrawer}
          onClose={toggleDrawer}
          title="Holiday & Leave Details"
          content={
            <EventsDataShow
              extraEventsData={{ extraEventsData, type: calenderData?.type }}
              type={viewData?.type}
            />
          }
        />
      </Box>
    </>
  );
};

export default CustomFullCalendar;
