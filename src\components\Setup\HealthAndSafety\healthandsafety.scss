.health-safety-addtoplaylist {
  .health-and-safety-sec-search {
    gap: var(--spacing-md);
  }
  .health-safety-card-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    margin-top: var(--spacing-xl);
    width: 50%;
    box-shadow: var(--box-shadow-xs);
    border: var(--normal-sec-border);
    position: relative;
  }
  .add-to-playlist-data {
    margin: var(--spacing-lg) var(--spacing-none);
    // max-height: 70%;
    // overflow-y: scroll;
    .pl-checkbox {
      .check-box-text {
        .MuiTypography-root {
          font-family: var(--font-family-primary);
          font-size: var(--font-size-sm);
        }
      }
    }
  }

  .category-name {
    // width: 30%;
  }
  .assign-branch-icon {
    font-size: var(--icon-size-xsm);
    fill: var(--icon-color-primary);
  }
  .branch-count {
    width: 100%;
    max-width: 110px;
    justify-content: space-between;
  }
}
