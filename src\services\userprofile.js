import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';

export const userProfileService = {
  checkUsernameAvailability: async (username) => {
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_All_USERNAME + `${username}`
      );
      if (status === 200) {
        return data;
      }
      return null;
    } catch (error) {
      throw error;
    }
  },

  updateUserProfile: async (userId, formData, isMyProfile = false) => {
    try {
      const ApiUrl = isMyProfile
        ? URLS.UPDATE_MY_PROFILE
        : URLS.UPDATE_USER + `${userId}`;
      const { status, data } = await axiosInstance.put(ApiUrl, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      if (status === 200 || status === 201) {
        return data;
      }
      return null;
    } catch (error) {
      throw error;
    }
  },

  resetUserDetails: async (userId) => {
    try {
      const { status, data } = await axiosInstance.post(
        URLS?.RESET_USER_DETAILS,
        { user_id: userId }
      );
      if (status === 200) {
        return data;
      }
      return null;
    } catch (error) {
      throw error;
    }
  },
};
