.preview-mode-selector {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);

  // Desktop Button Group
  &__desktop-group {
    display: none;
    align-items: center;
    gap: var(--spacing-sm);
    background-color: var(--color-white);
    border: var(--border-width-xs) solid var(--color-light-gray);
    border-radius: var(--field-radius);
    padding: var(--spacing-xs);
    box-shadow: var(--box-shadow-xs);

    @media (min-width: 640px) {
      display: flex;
    }
  }

  &__button {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--field-padding);
    border-radius: var(--field-radius);
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    border: none;
    background: none;
    cursor: pointer;
    transition: all 0.15s ease-out;
    color: var(--text-color-primary);

    &:hover {
      background-color: var(--color-off-white);
      color: var(--color-primary);
    }

    &--active {
      background-color: var(--color-primary);
      color: var(--text-color-white);
      box-shadow: var(--box-shadow-sm);

      &:hover {
        background-color: var(--color-primary-dark);
        color: var(--text-color-white);
      }
    }

    &--primary.preview-mode-selector__button--active {
      background-color: var(--color-primary);
    }

    &--secondary.preview-mode-selector__button--active {
      background-color: var(--color-success);
    }

    &--accent.preview-mode-selector__button--active {
      background-color: var(--color-warning);
    }
  }

  &__button-text {
    &--desktop {
      display: none;

      @media (min-width: 1024px) {
        display: inline;
      }
    }

    &--mobile {
      display: inline;

      @media (min-width: 1024px) {
        display: none;
      }
    }
  }

  // Mobile Dropdown
  &__mobile-dropdown {
    display: block;
    position: relative;

    @media (min-width: 640px) {
      display: none;
    }
  }

  &__dropdown-trigger {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 12rem; // w-48
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--color-white);
    border: var(--border-width-xs) solid var(--color-light-gray);
    border-radius: var(--border-radius-md);
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-primary);
    box-shadow: var(--box-shadow-xs);
    cursor: pointer;
    transition: all 0.15s ease-out;

    &:hover {
      background-color: var(--color-off-white);
    }
  }

  &__dropdown-trigger-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }

  &__dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    margin-top: var(--spacing-xs);
    background-color: var(--color-white);
    border: var(--border-width-xs) solid var(--color-light-gray);
    border-radius: var(--border-radius-md);
    box-shadow: var(--box-shadow-lg);
    z-index: 200;
    animation: fadeIn 0.15s ease-out;
  }

  &__dropdown-item {
    width: 100%;
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    text-align: left;
    border: none;
    background: none;
    cursor: pointer;
    transition: all 0.15s ease-out;
    color: var(--text-color-primary);

    &:hover {
      background-color: var(--color-off-white);
    }

    &--active {
      background-color: var(--color-primary-opacity);
      color: var(--color-primary);
    }

    &--first {
      border-top-left-radius: var(--border-radius-md);
      border-top-right-radius: var(--border-radius-md);
    }

    &--last {
      border-bottom-left-radius: var(--border-radius-md);
      border-bottom-right-radius: var(--border-radius-md);
    }
  }

  &__dropdown-item-content {
    display: flex;
    flex-direction: column;
    flex: 1;
  }

  &__dropdown-item-label {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
  }

  &__dropdown-item-description {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    color: var(--text-color-slate-gray);
    margin-top: var(--spacing-xs);
  }

  &__dropdown-item-check {
    margin-left: auto;
  }

  // Mode Description
  &__description {
    display: none;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-color-slate-gray);

    @media (min-width: 1280px) {
      display: flex;
    }
  }

  &__description-text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
  }
}

// Animation
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
