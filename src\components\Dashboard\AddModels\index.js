import React, { useEffect, useState } from 'react';
import { Box, Typography, CircularProgress } from '@mui/material';
import CustomSelect from '@/components/UI/CustomSelect';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CustomButton from '@/components/UI/CustomButton';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomTabs from '@/components/UI/CustomTabs';
import './addmodel.scss';

const AddModels = ({
  handleClose,
  onModelAdd,
  modelList,
  IsEditData,
  axisloader,
  AxisList,
  setIsEditData,
}) => {
  const [ModelData, setModelData] = useState({
    model_type: '',
    xaxis_list: '',
    yaxis_list: '',
    model_title: '',
  });
  const [checkXId, setCheckXId] = useState([]);
  const [checkYId, setCheckYId] = useState([]);

  const [tab, setTab] = useState(1);

  const handleTabChange = (newValue) => {
    // const selectedTab = tabView[newValue];
    setTab(newValue);
  };
  const tabView = [
    { id: 1, name: ModelData?.model_type === 'pie_chart' ? 'Label' : 'X-Axis' },
    { id: 2, name: ModelData?.model_type === 'pie_chart' ? 'Value' : 'Y-Axis' },
  ];
  const OnChangeXaxis = (value) => {
    const currentHSIndex = checkXId?.indexOf(value);
    const newChecked = [...checkXId];
    if (currentHSIndex === -1) {
      newChecked.push(value);
    } else {
      newChecked.splice(currentHSIndex, 1);
    }
    setCheckXId(newChecked);
  };
  const OnChangeYaxis = (value) => {
    const currentHSIndex = checkYId?.indexOf(value);
    const newChecked = [...checkYId];
    if (currentHSIndex === -1) {
      newChecked.push(value);
    } else {
      newChecked.splice(currentHSIndex, 1);
    }
    setCheckYId(newChecked);
  };
  useEffect(() => {
    if (IsEditData && IsEditData?.isFirst) {
      const model = {
        model_type: IsEditData?.model_type,
        xaxis_list: IsEditData?.xaxis_list,
        yaxis_list: IsEditData?.yaxis_list,
        model_title: IsEditData?.model_title,
      };
      if (IsEditData?.xAxisSel && IsEditData?.xAxisSel?.length > 0) {
        setCheckXId(IsEditData?.xAxisSel);
      } else if (IsEditData?.xaxis_value) {
        const xarray =
          IsEditData?.model_type === 'number'
            ? IsEditData?.xaxis_value
            : IsEditData?.xaxis_list === 'weekly' ||
                IsEditData?.xaxis_list === 'yearly'
              ? IsEditData?.xaxis_value.split(',')
              : IsEditData?.xaxis_value
                    ?.split(',')
                    ?.map((val) => Number(val)) // Convert each value to a number
                    .some((num) => isNaN(num)) // Check if any value is NaN
                ? IsEditData?.xaxis_value.split(',') // Use the original string array if NaN exists
                : IsEditData?.xaxis_value.split(',').map(Number);
        setCheckXId(xarray);
      } else {
        setCheckXId([]);
      }
      if (IsEditData?.yAxisSel && IsEditData?.yAxisSel?.length > 0) {
        setCheckYId(IsEditData?.yAxisSel);
      } else if (IsEditData?.yaxis_value) {
        const Yarray = IsEditData?.yaxis_value
          ?.split(',')
          ?.map((val) => Number(val)) // Convert each value to a number
          .some((num) => isNaN(num)) // Check if any value is NaN
          ? IsEditData?.yaxis_value.split(',') // Use the original string array if NaN exists
          : IsEditData?.yaxis_value.split(',').map(Number);
        setCheckYId(Yarray);
      } else {
        setCheckYId([]);
      }

      setModelData(model);
    } else if (IsEditData?.isFirst) {
      setCheckXId([]);
      setCheckYId([]);
      setModelData({
        model_type: '',
        xaxis_list: '',
        yaxis_list: '',
        model_title: '',
      });
    }
  }, [IsEditData]);
  const xAxisList =
    AxisList &&
    AxisList &&
    AxisList?.[ModelData?.xaxis_list] &&
    AxisList?.[ModelData?.xaxis_list]?.length > 0
      ? AxisList?.[ModelData?.xaxis_list]
      : [];
  const yAxisList =
    AxisList &&
    AxisList &&
    AxisList?.[ModelData?.yaxis_list] &&
    AxisList?.[ModelData?.yaxis_list]?.length > 0
      ? AxisList?.[ModelData?.yaxis_list]
      : [];
  const selectedNumber =
    ModelData?.model_type === 'number' &&
    AxisList &&
    AxisList &&
    AxisList?.number &&
    AxisList?.number?.length > 0 &&
    checkXId
      ? AxisList?.number?.find((f) => f?.key === checkXId)
      : false;
  const allXChecked =
    xAxisList && xAxisList?.length > 0
      ? xAxisList?.map((item) => item?.key)
      : [];
  const allYChecked =
    yAxisList && yAxisList?.length > 0
      ? yAxisList
          .flatMap((category) =>
            category.payment_type_category?.map(
              (item) => item.payment_type_category_id
            )
          )
          .filter(Boolean)
      : [];
  const getCurrentTabs = () => {
    return tabView?.map((tab) => ({
      id: tab?.id,
      label: tab?.name,
    }));
  };
  const getCurrentContent = () => {
    switch (tab) {
      case 1:
        return (
          <>
            {' '}
            {axisloader ? (
              <Box className="content-loader">
                <CircularProgress className="loader" color="inherit" />
              </Box>
            ) : (
              <Box className="axis-selection">
                {xAxisList && xAxisList?.length > 0 ? (
                  <Box
                    className="d-flex model-list"
                    onClick={() =>
                      xAxisList?.length === checkXId?.length
                        ? setCheckXId([])
                        : setCheckXId(xAxisList?.map((item) => item?.key))
                    }
                  >
                    {xAxisList?.length === checkXId?.length ? (
                      <CheckBoxIcon className="check-icon check-icon-box" />
                    ) : (
                      <CheckBoxOutlineBlankIcon className="uncheck-icon check-icon-box" />
                    )}
                    <Typography className="title-text">Select All</Typography>
                  </Box>
                ) : (
                  <></>
                )}
                {xAxisList &&
                  xAxisList?.length > 0 &&
                  xAxisList?.map((item) => {
                    return (
                      <Box
                        className="d-flex model-list"
                        onClick={() => OnChangeXaxis(item?.key)}
                      >
                        {checkXId?.includes(item?.key) ? (
                          <CheckBoxIcon className="check-icon check-icon-box" />
                        ) : (
                          <CheckBoxOutlineBlankIcon className="uncheck-icon check-icon-box" />
                        )}
                        <Typography className="title-text">
                          {item?.value}{' '}
                          {ModelData?.xaxis_list === 'branch' &&
                          item?.branch_status === 'inactive' ? (
                            <span className="failed Inter12 Inactive-axis">
                              In-Active
                            </span>
                          ) : (
                            <></>
                          )}
                        </Typography>
                      </Box>
                    );
                  })}
              </Box>
            )}
          </>
        );
      case 2:
        return (
          <>
            {' '}
            {axisloader ? (
              <Box className="content-loader">
                <CircularProgress className="loader" color="inherit" />
              </Box>
            ) : (
              <Box className="axis-selection">
                {yAxisList &&
                yAxisList?.length > 0 &&
                yAxisList.some(
                  (category) =>
                    category?.payment_type_category &&
                    category?.payment_type_category.length > 1
                ) ? (
                  <Box
                    className="d-flex model-list"
                    onClick={() => {
                      const allChildIds = yAxisList
                        .flatMap((category) =>
                          category.payment_type_category?.map(
                            (item) => item.payment_type_category_id
                          )
                        )
                        .filter(Boolean); // Ensure valid IDs

                      setCheckYId(
                        allChildIds.length === checkYId.length
                          ? []
                          : allChildIds
                      );
                    }}
                  >
                    {yAxisList
                      .flatMap((category) =>
                        category?.payment_type_category?.map((item) =>
                          checkYId.includes(item?.payment_type_category_id)
                        )
                      )
                      .every(Boolean) ? (
                      <CheckBoxIcon className="check-icon check-icon-box" />
                    ) : (
                      <CheckBoxOutlineBlankIcon className="uncheck-icon check-icon-box" />
                    )}
                    <Typography className="title-text">Select All</Typography>
                  </Box>
                ) : (
                  <></>
                )}
                {yAxisList &&
                  yAxisList?.length > 0 &&
                  yAxisList?.map((category) => {
                    return (
                      <>
                        {category?.payment_type_category?.map((item) => {
                          return (
                            <Box
                              className="d-flex model-list"
                              onClick={() =>
                                OnChangeYaxis(item?.payment_type_category_id)
                              }
                            >
                              {checkYId?.includes(
                                item?.payment_type_category_id
                              ) ? (
                                <CheckBoxIcon className="check-icon check-icon-box" />
                              ) : (
                                <CheckBoxOutlineBlankIcon className="uncheck-icon check-icon-box" />
                              )}
                              <Typography className="title-text">
                                {item?.payment_type_category_title}
                                {category?.payment_type_status === 'inactive' ||
                                item?.payment_type_category_status ===
                                  'inactive' ? (
                                  <span className="failed Inter12 Inactive-axis">
                                    In-Active
                                  </span>
                                ) : (
                                  <></>
                                )}
                              </Typography>
                            </Box>
                          );
                        })}
                      </>
                    );
                  })}
              </Box>
            )}
          </>
        );

        return (
          <Box>
            <SideLetter
              UserId={isMyProfile ? UserDetails?.id : UserId}
              isMyProfile={isMyProfile}
            />
          </Box>
        );
      default:
        return null;
    }
  };
  useEffect(() => {
    if (
      ModelData?.xaxis_list &&
      (!IsEditData?.isFirst ||
        (IsEditData?.xaxis_value === '' &&
          (!IsEditData?.xAxisSel || IsEditData?.xAxisSel?.length === 0)))
    ) {
      setCheckXId(allXChecked);
    }
  }, [ModelData?.xaxis_list]);
  useEffect(() => {
    if (
      ModelData?.yaxis_list &&
      (!IsEditData?.isFirst ||
        (IsEditData?.yaxis_value === '' &&
          (!IsEditData?.yAxisSel || IsEditData?.yAxisSel?.length === 0)))
    ) {
      setCheckYId(allYChecked); // Ensure valid IDs
    }
  }, [ModelData?.yaxis_list]);
  return (
    <Box className="Add-model-section">
      <Box>
        <Box className="model-name">
          <CustomTextField
            fullWidth
            id="ename"
            name="ename"
            value={ModelData?.model_title}
            label="Model Name"
            required
            placeholder="Enter Model Name"
            onChange={(e) => {
              let modeldata = ModelData;
              modeldata = { ...modeldata, model_title: e.target.value };
              setModelData(modeldata);
            }}
          />
        </Box>
        <Box className="pt8">
          <CustomSelect
            placeholder="Model Type"
            options={modelList?.model_list}
            value={
              modelList?.model_list?.find((opt) => {
                return opt?.value === ModelData?.model_type;
              }) || ''
            }
            name="contentType"
            // error={!ModelData?.model_type}
            onChange={(e) => {
              let modeldata = ModelData;
              modeldata = {
                ...modeldata,
                model_type: e?.value,
                xaxis_list: '',
                yaxis_list: '',
              };
              setModelData(modeldata);
              setCheckXId([]);
              setCheckYId([]);
            }}
            label={<span>Model Type</span>}
            required
          />
        </Box>
        {ModelData?.model_type === 'number' ? (
          <>
            {axisloader ? (
              <Box className="content-loader">
                <CircularProgress className="loader" color="inherit" />
              </Box>
            ) : (
              <Box className="pt32 naxis-selection">
                {/* {xAxisList && xAxisList?.length > 0 ? (
                  <Box
                    className="d-flex model-list"
                    onClick={() =>
                      xAxisList?.length === checkXId?.length
                        ? setCheckXId([])
                        : setCheckXId(xAxisList?.map((item) => item?.key))
                    }
                  >
                    {xAxisList?.length === checkXId?.length ? (
                      <CheckBoxIcon className="check-icon check-icon-box" />
                    ) : (
                      <CheckBoxOutlineBlankIcon className="uncheck-icon check-icon-box" />
                    )}
                    <Typography className="title-text">Select All</Typography>
                  </Box>
                ) : (
                  <></>
                )} */}
                {AxisList &&
                  AxisList?.number &&
                  AxisList?.number?.length > 0 &&
                  AxisList?.number?.map((item) => {
                    return (
                      <Box
                        className="d-flex model-list"
                        onClick={() => {
                          item?.key === checkXId
                            ? setCheckXId()
                            : setCheckXId(item?.key);
                        }}
                      >
                        {item?.key === checkXId ? (
                          <CheckBoxIcon className="check-icon check-icon-box" />
                        ) : (
                          <CheckBoxOutlineBlankIcon className="uncheck-icon check-icon-box" />
                        )}
                        <Typography className="title-text">
                          {item?.value}
                        </Typography>
                      </Box>
                    );
                  })}
              </Box>
            )}
          </>
        ) : ModelData?.model_type === 'pie_chart' &&
          ModelData?.yaxis_list === 'user' ? (
          <>
            <Box className="pt8">
              <CustomSelect
                placeholder="Label"
                options={[{ key: 'branch', value: 'branch', label: 'Branch' }]}
                value={
                  [{ key: 'branch', value: 'branch', label: 'Branch' }]?.find(
                    (opt) => {
                      return opt?.value === ModelData?.xaxis_list;
                    }
                  ) || ''
                }
                name="contentType"
                onChange={(e) => {
                  let modeldata = ModelData;
                  modeldata = { ...modeldata, xaxis_list: e?.value };
                  setModelData(modeldata);
                  IsEditData &&
                    setIsEditData({
                      ...IsEditData,
                      model_title: modeldata?.model_title,
                      model_type: modeldata?.model_type,
                      xaxis_list: e?.value,
                      xaxis_value: '',
                      yaxis_list: '',
                      yaxis_value: '',
                      isFirst: false,
                    });
                }}
                label={<span>Label</span>}
                required
              />
            </Box>
            <Box className="pt8">
              <CustomSelect
                placeholder="Value"
                options={modelList?.yaxis_pie_chart}
                value={
                  modelList?.yaxis_pie_chart?.find((opt) => {
                    return opt?.value === ModelData?.yaxis_list;
                  }) || ''
                }
                name="contentType"
                onChange={(e) => {
                  let modeldata = ModelData;
                  modeldata = { ...modeldata, yaxis_list: e?.value };
                  setModelData(modeldata);
                  IsEditData &&
                    setIsEditData({
                      ...IsEditData,
                      model_title: modeldata?.model_title,
                      model_type: modeldata?.model_type,
                      xaxis_list: modeldata?.xaxis_list,
                      yaxis_list: e?.value,
                      isFirst: false,
                    });
                  // ModelData?.xaxis_list &&
                  //   e.target.value &&
                  //   getModelsAxisList(ModelData?.xaxis_list, e.target.value);
                }}
                label={<span>Value</span>}
                required
              />
            </Box>
            {ModelData?.xaxis_list ? (
              <>
                {axisloader ? (
                  <Box className="content-loader">
                    <CircularProgress className="loader" color="inherit" />
                  </Box>
                ) : (
                  <Box className="xaxis-selection pt32">
                    {' '}
                    {xAxisList && xAxisList?.length > 0 ? (
                      <Box
                        className="d-flex model-list"
                        onClick={() =>
                          xAxisList?.length === checkXId?.length
                            ? setCheckXId([])
                            : setCheckXId(xAxisList?.map((item) => item?.key))
                        }
                      >
                        {xAxisList?.length === checkXId?.length ? (
                          <CheckBoxIcon className="check-icon check-icon-box" />
                        ) : (
                          <CheckBoxOutlineBlankIcon className="uncheck-icon check-icon-box" />
                        )}
                        <Typography className="title-text">
                          Select All
                        </Typography>
                      </Box>
                    ) : (
                      <></>
                    )}
                    {xAxisList &&
                      xAxisList?.length > 0 &&
                      xAxisList?.map((item) => {
                        return (
                          <Box
                            className="d-flex model-list"
                            onClick={() => OnChangeXaxis(item?.key)}
                          >
                            {checkXId?.includes(item?.key) ? (
                              <CheckBoxIcon className="check-icon check-icon-box" />
                            ) : (
                              <CheckBoxOutlineBlankIcon className="uncheck-icon check-icon-box" />
                            )}
                            <Typography className="title-text">
                              {item?.value}
                              {ModelData?.xaxis_list === 'branch' &&
                              item?.branch_status === 'inactive' ? (
                                <span className="failed Inter12 Inactive-axis">
                                  In-Active
                                </span>
                              ) : (
                                <></>
                              )}
                            </Typography>
                          </Box>
                        );
                      })}
                  </Box>
                )}
              </>
            ) : (
              <></>
            )}
          </>
        ) : ModelData?.xaxis_list === 'time' ? (
          <>
            <Box className="pt8">
              <CustomSelect
                placeholder={
                  ModelData?.model_type === 'pie_chart' ? 'Label' : 'X-Axis'
                }
                options={
                  ModelData?.model_type === 'pie_chart'
                    ? [{ key: 'branch', value: 'branch', label: 'Branch' }]
                    : modelList?.xaxis_list
                }
                value={
                  (ModelData?.model_type === 'pie_chart'
                    ? [{ key: 'branch', value: 'branch', label: 'Branch' }]
                    : modelList?.xaxis_list
                  )?.find((opt) => {
                    return opt?.value === ModelData?.xaxis_list;
                  }) || ''
                }
                name="contentType"
                onChange={(e) => {
                  let modeldata = ModelData;
                  modeldata = { ...modeldata, xaxis_list: e?.value };
                  setModelData(modeldata);
                  IsEditData &&
                    setIsEditData({
                      ...IsEditData,
                      model_title: modeldata?.model_title,
                      model_type: modeldata?.model_type,
                      xaxis_list: e?.value,
                      yaxis_list: modeldata?.yaxis_list,
                      isFirst: false,
                    });
                  // ModelData?.yaxis_list &&
                  //   e.target.value &&
                  //   getModelsAxisList(e.target.value, ModelData?.yaxis_list);
                }}
                label={
                  <span>
                    {ModelData?.model_type === 'pie_chart' ? 'Label' : 'X-Axis'}
                  </span>
                }
                required
              />
            </Box>
            <Box className="pt8">
              <CustomSelect
                placeholder={
                  ModelData?.model_type === 'pie_chart' ? 'Value' : 'Y-Axis'
                }
                options={
                  ModelData?.model_type === 'pie_chart'
                    ? modelList?.yaxis_pie_chart
                    : modelList?.yaxis_list
                }
                value={
                  (ModelData?.model_type === 'pie_chart'
                    ? modelList?.yaxis_pie_chart
                    : modelList?.yaxis_list
                  )?.find((opt) => {
                    return opt?.value === ModelData?.yaxis_list;
                  }) || ''
                }
                name="contentType"
                onChange={(e) => {
                  let modeldata = ModelData;
                  modeldata = { ...modeldata, yaxis_list: e?.value };
                  setModelData(modeldata);
                  IsEditData &&
                    setIsEditData({
                      ...IsEditData,
                      model_title: modeldata?.model_title,
                      model_type: modeldata?.model_type,
                      xaxis_list: modeldata?.xaxis_list,
                      yaxis_list: e?.value,
                      isFirst: false,
                    });
                  // ModelData?.xaxis_list &&
                  //   e.target.value &&
                  //   getModelsAxisList(ModelData?.xaxis_list, e.target.value);
                }}
                label={
                  <span>
                    {ModelData?.model_type === 'pie_chart' ? 'Value' : 'Y-Axis'}
                  </span>
                }
                required
              />
            </Box>
            {ModelData?.yaxis_list ? (
              <>
                {axisloader ? (
                  <Box className="content-loader">
                    <CircularProgress className="loader" color="inherit" />
                  </Box>
                ) : (
                  <Box className="pt32">
                    <Box className="xaxis-selection">
                      {yAxisList &&
                      yAxisList?.length > 0 &&
                      yAxisList.some(
                        (category) =>
                          category?.payment_type_category &&
                          category?.payment_type_category.length > 1
                      ) ? (
                        <Box
                          className="d-flex model-list"
                          onClick={() => {
                            const allChildIds = yAxisList
                              .flatMap((category) =>
                                category.payment_type_category?.map(
                                  (item) => item.payment_type_category_id
                                )
                              )
                              .filter(Boolean); // Ensure valid IDs

                            setCheckYId(
                              allChildIds.length === checkYId.length
                                ? []
                                : allChildIds
                            );
                          }}
                        >
                          {yAxisList
                            .flatMap((category) =>
                              category?.payment_type_category?.map((item) =>
                                checkYId.includes(
                                  item?.payment_type_category_id
                                )
                              )
                            )
                            .every(Boolean) ? (
                            <CheckBoxIcon className="check-icon check-icon-box" />
                          ) : (
                            <CheckBoxOutlineBlankIcon className="uncheck-icon check-icon-box" />
                          )}
                          <Typography className="title-text">
                            Select All
                          </Typography>
                        </Box>
                      ) : (
                        <></>
                      )}
                      {yAxisList &&
                        yAxisList?.length > 0 &&
                        yAxisList?.map((category) => {
                          return (
                            <>
                              {category?.payment_type_category?.map((item) => {
                                return (
                                  <Box
                                    className="d-flex model-list"
                                    onClick={() =>
                                      OnChangeYaxis(
                                        item?.payment_type_category_id
                                      )
                                    }
                                  >
                                    {checkYId?.includes(
                                      item?.payment_type_category_id
                                    ) ? (
                                      <CheckBoxIcon className="check-icon check-icon-box" />
                                    ) : (
                                      <CheckBoxOutlineBlankIcon className="uncheck-icon check-icon-box" />
                                    )}
                                    <Typography className="title-text">
                                      {item?.payment_type_category_title}
                                      {category?.payment_type_status ===
                                        'inactive' ||
                                      item?.payment_type_category_status ===
                                        'inactive' ? (
                                        <span className="failed Inter12 Inactive-axis">
                                          In-Active
                                        </span>
                                      ) : (
                                        <></>
                                      )}
                                    </Typography>
                                  </Box>
                                );
                              })}
                            </>
                          );
                        })}
                    </Box>
                  </Box>
                )}
              </>
            ) : (
              <></>
            )}
          </>
        ) : ModelData?.model_type ? (
          <>
            <Box className="pt8">
              <CustomSelect
                placeholder={
                  ModelData?.model_type === 'pie_chart' ? 'Label' : 'X-Axis'
                }
                options={
                  ModelData?.model_type === 'pie_chart'
                    ? [{ key: 'branch', value: 'branch', label: 'Branch' }]
                    : modelList?.xaxis_list
                }
                value={
                  (ModelData?.model_type === 'pie_chart'
                    ? [{ key: 'branch', value: 'branch', label: 'Branch' }]
                    : modelList?.xaxis_list
                  )?.find((opt) => {
                    return opt?.value === ModelData?.xaxis_list;
                  }) || ''
                }
                name="contentType"
                onChange={(e) => {
                  let modeldata = ModelData;
                  modeldata = { ...modeldata, xaxis_list: e?.value };
                  setModelData(modeldata);
                  IsEditData &&
                    setIsEditData({
                      ...IsEditData,
                      model_title: modeldata?.model_title,
                      model_type: modeldata?.model_type,
                      xaxis_list: e?.value,
                      yaxis_list: modeldata?.yaxis_list,
                      isFirst: false,
                    });
                  // ModelData?.yaxis_list &&
                  //   e.target.value &&
                  //   getModelsAxisList(e.target.value, ModelData?.yaxis_list);
                }}
                label={
                  <span>
                    {ModelData?.model_type === 'pie_chart'
                      ? 'Label'
                      : 'X-Axis'}{' '}
                  </span>
                }
                required
              />
            </Box>
            <Box className="pt8">
              <CustomSelect
                placeholder={
                  ModelData?.model_type === 'pie_chart' ? 'Value' : 'Y-Axis'
                }
                options={
                  ModelData?.model_type === 'pie_chart'
                    ? modelList?.yaxis_pie_chart
                    : modelList?.yaxis_list
                }
                value={
                  (ModelData?.model_type === 'pie_chart'
                    ? modelList?.yaxis_pie_chart
                    : modelList?.yaxis_list
                  )?.find((opt) => {
                    return opt?.value === ModelData?.yaxis_list;
                  }) || ''
                }
                name="contentType"
                onChange={(e) => {
                  let modeldata = ModelData;
                  modeldata = { ...modeldata, yaxis_list: e?.value };
                  setModelData(modeldata);
                  IsEditData &&
                    setIsEditData({
                      ...IsEditData,
                      model_title: modeldata?.model_title,
                      model_type: modeldata?.model_type,
                      xaxis_list: modeldata?.xaxis_list,
                      yaxis_list: e.target.value,
                      isFirst: false,
                    });
                  // ModelData?.xaxis_list &&
                  //   e.target.value &&
                  //   getModelsAxisList(ModelData?.xaxis_list, e.target.value);
                }}
                label={
                  <span>
                    {ModelData?.model_type === 'pie_chart'
                      ? 'Value'
                      : 'Y-Axis'}{' '}
                  </span>
                }
                required
              />
            </Box>
            {ModelData?.yaxis_list && ModelData?.xaxis_list ? (
              <Box>
                <Box className="section-right">
                  <Box className="section-right-tab-header">
                    <CustomTabs
                      tabs={getCurrentTabs()}
                      initialTab={tab}
                      onTabChange={handleTabChange}
                    />
                  </Box>
                  <Box className="section-right-content pt8">
                    {getCurrentContent()}
                  </Box>
                </Box>
              </Box>
            ) : (
              <></>
            )}
          </>
        ) : (
          <></>
        )}
      </Box>

      <Box className="form-actions-btn pt24">
        <CustomButton
          variant="outlined"
          title="Cancel"
          onClick={() => {
            handleClose();
          }}
        />
        <CustomButton
          variant="contained"
          title={IsEditData ? 'Edit' : 'Add'}
          disabled={
            (checkXId?.length === 0 &&
              ModelData?.model_type !== 'number' &&
              ModelData?.xaxis_list !== 'time') ||
            (ModelData?.model_type === 'number' && !checkXId) ||
            (checkYId?.length === 0 &&
              ModelData?.model_type !== 'pie_chart' &&
              ModelData?.model_type !== 'number') ||
            !ModelData?.model_title ||
            !ModelData?.model_type ||
            (ModelData?.model_type !== 'number' &&
              (!ModelData?.xaxis_list ||
                (!ModelData?.yaxis_list &&
                  ModelData?.model_type !== 'pie_chart')))
          }
          onClick={() => {
            handleClose();
            onModelAdd(
              ModelData,
              checkXId,
              checkYId,
              IsEditData,
              selectedNumber
            );
          }}
        />
      </Box>
    </Box>
  );
};

export default AddModels;
