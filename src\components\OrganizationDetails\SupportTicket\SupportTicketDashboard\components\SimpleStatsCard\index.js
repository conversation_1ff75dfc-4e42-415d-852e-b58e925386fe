'use client';

import React from 'react';
import { Box, Typography } from '@mui/material';
import Icon from '@/components/UI/AppIcon/AppIcon';
import './simplestatscard.scss';

const SimpleStatsCard = ({ title, value, icon, color, onClick }) => {
  const getColorClass = (colorType) => {
    return `simple-stats-card--${colorType}`;
  };

  return (
    <Box
      className={`simple-stats-card ${getColorClass(color)}`}
      onClick={onClick}
    >
      <Box className="simple-stats-card__content">
        <Box className="simple-stats-card__header">
          <Box className="simple-stats-card__text-section">
            <Typography className="body-sm">{title}</Typography>
            <Box className="simple-stats-card__value">{value}</Box>
          </Box>
          <Box className="simple-stats-card__icon">
            <Icon name={icon} size={20} />
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default SimpleStatsCard;
