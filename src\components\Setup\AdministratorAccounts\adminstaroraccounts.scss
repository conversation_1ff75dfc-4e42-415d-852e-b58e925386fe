.admin-accounts-page {
  .selection-staff-count {
    margin-top: 16px;
    .select-all-text {
      color: var(--color-primary);
      cursor: pointer;
      // text-decoration: underline;
      font-weight: var(--font-weight-medium);
    }
  }
  .export-section {
    display: flex;
    // border: 1px solid var(--color-primary);
    // border-radius: var(--border-radius-md);
    .custom-button-wrapper {
      .MuiButtonBase-root {
        border: 0 !important;
        padding: 8px;
        min-width: 0;
      }
    }
  }
  .checkbox svg {
    fill: var(--icon-color-primary);
  }
  .administrator-section {
    display: flex;
    column-gap: var(--spacing-md);
    align-items: center;
    justify-content: flex-end;

    .admin-user-search {
      max-width: calc(100% - 97px - 50px - 167px) !important;
    }

    @media (max-width: 599px) {
      flex-direction: column;
      row-gap: var(--spacing-base);

      .admin-user-search,
      button {
        width: 100% !important;
        max-width: 100% !important;
      }
    }
  }
}

.add-edit-admin-section {
  .display-grid {
    display: grid;
    grid-template-columns: 31% 31% 31%;
    column-gap: 15px;
    row-gap: 15px;

    @media (max-width: 1200px) {
      grid-template-columns: 48% 48%;
    }
    @media (max-width: 599px) {
      grid-template-columns: 100%;
    }
  }
}
.export-popover {
  .MuiPaper-root {
    width: 120px !important;
    margin-top: 8px;
    padding: 10px 20px;
  }

  .export-option {
    p {
      text-align: center;
    }
  }
}
