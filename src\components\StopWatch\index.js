import React, { useState, useEffect } from 'react';
import { Box, Typography, IconButton } from '@mui/material';
import QueryBuilderIcon from '@mui/icons-material/QueryBuilder';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import PauseIcon from '@mui/icons-material/Pause';
import StopIcon from '@mui/icons-material/Stop';
import RefreshIcon from '@mui/icons-material/Refresh';
import './stopwatch.scss';

export default function Stopwatch({
  time,
  setTime,
  running,
  setRunning,
  setSavedTimes,
  savedTimes,
  showButtons,
  setShowButtons,
}) {
  useEffect(() => {
    let timer;
    if (running) {
      timer = setInterval(() => setTime((prev) => prev + 1000), 1000);
    }
    return () => clearInterval(timer);
  }, [running]);

  const handleStartPause = () => {
    setRunning(!running);
    setShowButtons(true);
  };

  const handleStop = () => {
    handleSave();
    setRunning(false);
    setTime(0);
    setShowButtons(false);
  };

  const handleReset = () => {
    setRunning(false);
    setTime(0);
    setShowButtons(false);
  };

  const handleSave = () => {
    if (time > 0) {
      setSavedTimes((prev) => [...prev, formatTime(time)]);
    }
  };

  const formatTime = (ms) => {
    const hours = Math.floor((ms / (1000 * 60 * 60)) % 24);
    const minutes = Math.floor((ms / (1000 * 60)) % 60);
    const seconds = Math.floor((ms / 1000) % 60);
    return `${hours.toString().padStart(2, '0')}:${minutes
      .toString()
      .padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <Box className="stopwatch-wrap d-flex align-center">
      <Box className="play-pause-btn-wrap">
        <Box onClick={handleStartPause} className="play-pause-btn">
          {running ? (
            <PauseIcon className="pause-btn" sx={{ cursor: 'pointer' }} />
          ) : (
            <PlayArrowIcon className="play-btn" sx={{ cursor: 'pointer' }} />
          )}
        </Box>
      </Box>
      <Box className="time-wrap">
        <Typography className="time-show-wrap" variant="h6">
          {formatTime(time)}
        </Typography>
      </Box>
      <Box className="stop-reset-btn-wrap d-flex">
        {showButtons && (
          <>
            <Box className="stop-btn-wrap">
              <StopIcon
                onClick={handleStop}
                className="stop-icon"
                sx={{ cursor: 'pointer' }}
              />
            </Box>
            <Box className="refresh-btn-wrap">
              <RefreshIcon
                onClick={handleReset}
                className="refresh-icon"
                sx={{ cursor: 'pointer' }}
              />
            </Box>
          </>
        )}
      </Box>
    </Box>
  );
}
{
  /* <Box className="saved-times">
  <Typography variant="h6">Saved Times:</Typography>
  {savedTimes.length > 0 ? (
    savedTimes.map((savedTime, index) => (
      <Typography key={index} component="p">
        {savedTime}
      </Typography>
    ))
  ) : (
    <Typography component="p">No times saved yet.</Typography>
  )}
</Box> */
}
