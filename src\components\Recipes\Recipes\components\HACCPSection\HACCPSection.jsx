import React, { useEffect, useState } from 'react';
import { Box, Tooltip, Typography } from '@mui/material';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomSelect from '@/components/UI/CustomSelect';
import CustomButton from '@/components/UI/CustomButton';
import CustomCheckbox from '@/components/UI/CustomCheckbox';
import DeleteIcon from '@/components/ActionIcons/DeleteIcon';
import InfoIcon from '@mui/icons-material/Info';
import AddIcon from '@mui/icons-material/Add';
import { getAttributeList } from '@/services/recipeService';
import { setApiMessage } from '@/helper/common/commonFunctions';
import './haccpection.scss';

const useFromOptions = [
  { value: 'default', label: 'Generic HACCP' },
  { value: 'new', label: 'New HACCP' },
];

const HACCPSection = ({ formData, dispatch }) => {
  const [useFrom, setUseFrom] = useState(() => {
    return formData?.haccp?.useFrom || null;
  });
  const [haccpData, setHaccpData] = useState([]);

  // Get HACCP sections from formData
  const haccpSections = formData?.haccp?.sections || [];

  const fetchHaccpDetails = async () => {
    try {
      const response = await getAttributeList(
        '',
        '',
        { status: 'active' },
        '',
        '',
        'haccp_category' // type
      );
      setHaccpData(response?.attributes || []);
    } catch (error) {
      setApiMessage(
        'error',
        error?.response?.data?.message || 'Failed to fetch HACCP categories'
      );
      setHaccpData([]);
    }
  };

  useEffect(() => {
    fetchHaccpDetails();
  }, []);

  const haccpOptions =
    haccpData?.map((attr) => ({
      value: attr?.id,
      label: attr?.attribute_title,
      description: attr?.attribute_description,
    })) || [];

  useEffect(() => {
    if (!useFrom) return;

    // Only set default sections if sections are empty
    if (
      useFrom?.value === 'default' &&
      (!haccpSections || haccpSections.length === 0)
    ) {
      const allDefaultSections =
        haccpOptions?.map((option) => ({
          id: option?.value,
          haccp_id: option?.value,
          title: option?.label,
          useDefault: true,
          description: option?.description,
        })) || [];

      dispatch({
        type: 'UPDATE_HACCP',
        payload: {
          useFrom: useFrom,
          sections: allDefaultSections,
        },
      });
    } else if (useFrom?.value === 'new' && haccpSections.length !== 0) {
      dispatch({
        type: 'UPDATE_HACCP',
        payload: {
          useFrom: useFrom,
          sections: [],
        },
      });
    }
  }, [useFrom]);

  const handleAddSection = () => {
    const newSection = {
      id: haccpOptions[0]?.value,
      haccp_id: haccpOptions[0]?.value,
      title: haccpOptions?.length > 0 ? haccpOptions[0]?.label : 'General',
      useDefault: false,
      description: '',
    };

    const updatedSections = [...haccpSections, newSection];

    // Update formData through dispatch
    dispatch({
      type: 'UPDATE_HACCP',
      payload: {
        useFrom: useFrom,
        sections: updatedSections,
      },
    });
  };

  const handleInputChange = (index, field, value) => {
    const newSections = [...haccpSections];
    const currentSection = { ...newSections[index] };
    currentSection[field] = value;

    if (field === 'title') {
      currentSection['haccp_id'] = haccpOptions?.find(
        (opt) => opt?.label === value
      )?.value;
    }
    if (field === 'title' && currentSection?.useDefault) {
      const selectedOption = haccpOptions?.find((opt) => opt?.label === value);
      if (selectedOption) {
        currentSection.description = selectedOption?.description;
      }
    }

    if (field === 'useDefault' && value) {
      const selectedOption = haccpOptions?.find(
        (opt) => opt?.label === currentSection?.title
      );
      if (selectedOption) {
        currentSection.description = selectedOption?.description;
      }
    }

    newSections[index] = currentSection;

    // Update formData through dispatch
    dispatch({
      type: 'UPDATE_HACCP',
      payload: {
        useFrom: useFrom,
        sections: newSections,
      },
    });
  };

  const handleDeleteSection = (index) => {
    const newSections = haccpSections?.filter((_, i) => i !== index) || [];

    // Update formData through dispatch
    dispatch({
      type: 'UPDATE_HACCP',
      payload: {
        useFrom: useFrom,
        sections: newSections,
      },
    });
  };

  const handleClear = () => {
    setUseFrom(null);

    // Update formData through dispatch
    dispatch({
      type: 'UPDATE_HACCP',
      payload: {
        useFrom: null,
        sections: [],
      },
    });
  };

  return (
    <div className="haccp-section-wrapper">
      <div className="haccp-section__header">
        <Box className="d-flex align-center justify-start">
          <Typography className="other-field-label">Use From</Typography>
          <Tooltip
            arrow
            title={
              <Typography>
                Select where you want to draw your HACCP from, if anywhere, from
                this box. The values from this source will be pulled through as
                defaults, which you can override below. If you use the defaults,
                any changes to them will be reflected when you print the Recipe
                sheet.
              </Typography>
            }
            classes={{
              tooltip: 'info-tooltip-container',
            }}
          >
            <InfoIcon className="info-icon ml4" />
          </Tooltip>
        </Box>
        <div className="haccp-section__use-from">
          <CustomSelect
            label=""
            name="haccp_use_from"
            fullWidth
            options={useFromOptions}
            value={useFrom}
            onChange={setUseFrom}
            placeholder="Select default values"
            menuPosition="fixed"
            isClearable={false}
          />
        </div>
        <div className="haccp-section__clear-btn">
          <CustomButton
            variant="outlined"
            color="primary"
            onClick={handleClear}
          >
            Clear
          </CustomButton>
        </div>
      </div>

      {haccpSections?.map((section, index) => (
        <div key={index} className="haccp-section__card">
          <div className="haccp-section__card-left">
            <CustomSelect
              name={`haccp_type_${index}`}
              options={haccpOptions}
              value={haccpOptions?.find((opt) => opt?.label === section?.title)}
              onChange={(e) => handleInputChange(index, 'title', e?.label)}
              isClearable={false}
            />
            <div className="haccp-section__card-checkbox">
              <CustomCheckbox
                label="Use default"
                checked={section?.useDefault}
                onChange={(e) =>
                  handleInputChange(index, 'useDefault', e?.target?.checked)
                }
              />
            </div>
          </div>
          <div className="haccp-section__card-middle">
            <CustomTextField
              value={section?.description}
              onChange={(e) =>
                handleInputChange(index, 'description', e?.target?.value)
              }
              placeholder="Enter details here..."
              fullWidth
              multiline
              rows={5}
              disabled={section?.useDefault}
            />
          </div>
          <div className="haccp-section__card-actions">
            {/* <CustomButton
              variant="contained"
              className="action-btn delete-btn"
              onClick={() => handleDeleteSection(index)}
            > */}
            <DeleteIcon onClick={() => handleDeleteSection(index)} />
            {/* </CustomButton> */}
          </div>
        </div>
      ))}

      <div className="haccp-section__add-button">
        <CustomButton
          variant="contained"
          onClick={handleAddSection}
          startIcon={<AddIcon />}
        >
          Add section
        </CustomButton>
      </div>
    </div>
  );
};

export default HACCPSection;
