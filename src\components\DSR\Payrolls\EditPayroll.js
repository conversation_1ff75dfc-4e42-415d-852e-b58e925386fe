'use client';

import React, { useEffect, useState, useContext } from 'react';
import { Box, Typography } from '@mui/material';
import CustomButton from '@/components/UI/CustomButton';
import AuthContext from '@/helper/authcontext';
import { useRouter } from 'next/navigation';
import { fetchFromStorage, saveToStorage } from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import CustomDatePicker from '@/components/UI/CustomDatePicker';
import ExpenseCategory from '@/components/DSR/Payrolls/expenseCat';
import moment from 'moment';
import dayjs from 'dayjs';

export default function EditPayroll({ dsrId }) {
  const { authState, userdata, setUserdata } = useContext(AuthContext);
  const [DsrDetails, setDsrDetails] = useState();
  const [updateLoader, setUpdateLoader] = useState(false);
  const [random, setRandom] = useState();
  const [expenseData, setExpenseData] = useState([]);
  const [currency, setCurrency] = useState();
  const [sdate, setDate] = useState();
  const [checkloader, setCheckLoader] = useState(false);
  const [formated, setFormated] = useState(true);
  const [IsRemarkView, setIsRemarkView] = useState(true);
  const router = useRouter();

  const UpdateDSR = async () => {
    setUpdateLoader(true);
    const sendData = {
      data: expenseData,
      ...(authState?.web_user_active_role_id !== 7 &&
        authState?.web_user_active_role_id !== 14 && {
          expense_month: formated
            ? DsrDetails?.expense_month
            : sdate
              ? dayjs(sdate).format('MM')
              : moment(Date()).format('MM'),
        }),
      ...(authState?.web_user_active_role_id !== 7 &&
        authState?.web_user_active_role_id !== 14 && {
          expense_year: formated
            ? DsrDetails?.expense_year
            : sdate
              ? dayjs(sdate).format('YYYY')
              : moment(Date()).format('YYYY'),
        }),
      current_datetime: moment(Date()).format('YYYY-MM-DD HH:mm:ss'),
    };
    try {
      const { status, data } = await axiosInstance.put(
        URLS?.UPDATE_EXPENSE + `${dsrId}`,
        sendData
      );

      if (status === 200) {
        if (data?.status) {
          setUpdateLoader(false);
          router?.push('/payroll');
          setApiMessage('success', data?.message);
        } else {
          setUpdateLoader(false);
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setUpdateLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // get DSR by id
  const getPayrollData = async () => {
    // setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_EXPENSE_BY_ID + `${dsrId}`
      );
      if (status === 200) {
        // setLoader(false);
        setDsrDetails(data?.data);

        setDate(
          '28/' + data?.data?.expense_month + '/' + data?.data?.expense_year
        );
        const incomeData =
          data?.data &&
          data?.data?.expenseItems &&
          data?.data?.expenseItems?.length > 0 &&
          data?.data?.expenseItems?.filter(
            (f) => f?.payment_type_usage === 'expense'
          );

        incomeData && incomeData?.length > 0
          ? setExpenseData(incomeData)
          : setExpenseData([]);
      }
    } catch (error) {
      // setLoader(false);
      setDsrDetails();
      setExpenseData([]);

      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const ExpenseCheck = async (branch, date) => {
    setCheckLoader(true);
    const sendData = {
      branch_id: branch,
      expense_month: date
        ? dayjs(date).format('MM')
        : moment(Date()).format('MM'),
      expense_year: date
        ? dayjs(date).format('YYYY')
        : moment(Date()).format('YYYY'),
    };
    try {
      const { status, data } = await axiosInstance.post(
        URLS?.GET_EXPENSE_CHECK,
        sendData
      );
      if (status === 200 || status === 202) {
        if (data?.status) {
          setCheckLoader(false);
          // setISDsr(false);
          // setApiMessage('success', data?.message);
        } else {
          setCheckLoader(false);
          // setISDsr(true);
          setDate(
            '28/' + DsrDetails?.expense_month + '/' + DsrDetails?.expense_year
          );
          setFormated(true);
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setCheckLoader(false);
      // setISDsr(true);
      setDate(
        '28/' + DsrDetails?.expense_month + '/' + DsrDetails?.expense_year
      );
      setFormated(true);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  useEffect(() => {
    setExpenseData(expenseData);
  }, [random]);

  const oldDSR = fetchFromStorage(identifiers?.RedirectData)
    ? fetchFromStorage(identifiers?.RedirectData)
    : userdata;

  useEffect(() => {
    if (oldDSR?.IsFromUser === undefined && oldDSR?.page !== undefined) {
      setUserdata({ ...userdata, IsFromUser: true });
      saveToStorage(identifiers?.RedirectData, {
        ...oldDSR,
        IsFromUser: true,
      });
    }
  }, [oldDSR]);

  useEffect(() => {
    if (dsrId) {
      getPayrollData();
    }
  }, [dsrId]);
  useEffect(() => {
    authState?.currency_details && setCurrency(authState?.currency_details);
  }, [authState]);
  const convertDate = (dateString) => {
    if (!dateString) return null;
    // Parse the date string in DD/MM/YYYY format
    const [day, month, year] = dateString?.split('/') || [];
    if (!day || !month || !year) return null;
    return dayjs(`${year}-${month}-${day}`);
  };

  return (
    <Box className="Add-dsr-section">
      <ArrowBackIosIcon
        className="mb8 cursor-pointer"
        onClick={() => {
          setTimeout(() => {
            router?.push('/payroll');
          }, 1000);
        }}
      />
      <Box>
        {(authState?.web_user_active_role_id === 7 ||
          authState?.web_user_active_role_id === 14) && (
          <>
            {DsrDetails?.expense_month && DsrDetails?.expense_year && (
              <Box className="d-flex align-center">
                <Typography className="title-text fw400">
                  <span className="fw600"> Month : </span>
                  <span>
                    {DsrDetails?.expense_month +
                      ' - ' +
                      DsrDetails?.expense_year}
                  </span>
                </Typography>
              </Box>
            )}
          </>
        )}

        {DsrDetails?.expense_branch &&
          DsrDetails?.expense_branch?.branch_name && (
            <Box className="d-flex align-center pt4">
              <Typography className="title-text fw400">
                <span className="fw600"> Branch : </span>
                <span>{DsrDetails?.expense_branch?.branch_name}</span>
              </Typography>
            </Box>
          )}

        {DsrDetails?.expense_user &&
          DsrDetails?.expense_user?.user_full_name && (
            <Box className="d-flex align-center pt4">
              <Typography className="title-text fw400">
                <span className="fw600"> Submitted By : </span>
                <span>{DsrDetails?.expense_user?.user_full_name}</span>
              </Typography>
            </Box>
          )}

        {DsrDetails?.expense_detail_status && (
          <Box className="d-flex align-center pt4">
            <Typography className="title-text fw400">
              <span className="fw600"> Status : </span>
              <span>
                {
                  <span>
                    {DsrDetails?.expense_detail_status === 'rejected' ? (
                      <span className="sub-title-text failed fw600 text-capital">
                        {' '}
                        {DsrDetails?.expense_detail_status}{' '}
                      </span>
                    ) : DsrDetails?.expense_detail_status === 'pending' ? (
                      <span className="sub-title-text draft fw600 text-capital">
                        {' '}
                        {DsrDetails?.expense_detail_status}{' '}
                      </span>
                    ) : (
                      <span className="sub-title-text success fw600 text-capital">
                        {' '}
                        {DsrDetails?.expense_detail_status}{' '}
                      </span>
                    )}
                  </span>
                }
              </span>
            </Typography>
          </Box>
        )}
      </Box>
      {authState?.web_user_active_role_id !== 7 &&
        authState?.web_user_active_role_id !== 14 &&
        authState?.UserPermission?.dsr === 2 && (
          <Box className="display-grid-branch pt32">
            <Box>
              <CustomDatePicker
                label={<span>Month (MM/YYYY)</span>}
                name="date"
                value={
                  !formated && sdate
                    ? dayjs(sdate)
                    : sdate
                      ? convertDate(sdate)
                      : null
                }
                disabled={checkloader}
                disableFuture={true}
                onChange={(date) => {
                  setFormated(false);
                  setDate(date);
                  DsrDetails?.expense_branch?.id &&
                    ExpenseCheck(DsrDetails?.expense_branch?.id, date);
                }}
                inputVariant="outlined"
                months={true}
                format={'M/YYYY'}
                views={['month', 'year']}
                disablePast={false}
                error={false}
              />
            </Box>
            <Box></Box>
          </Box>
        )}
      <ExpenseCategory
        expenseData={expenseData}
        isEdit={true}
        setIsRemarkView={setIsRemarkView}
        IsRemarkView={IsRemarkView}
        setExpenseData={setExpenseData}
        setRandom={setRandom}
        currency={currency}
      />

      <Box className="mt24">
        {' '}
        <CustomButton
          variant="contained"
          onClick={() => UpdateDSR()}
          disabled={updateLoader}
          title={`${updateLoader ? 'Updating...' : 'Update Expense'}`}
        />
      </Box>
    </Box>
  );
}
