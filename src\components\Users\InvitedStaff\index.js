'use client';

import React, { useState, useEffect, useContext } from 'react';
import {
  Box,
  Typography,
  CircularProgress,
  Avatar,
  Tooltip,
} from '@mui/material';
import AuthContext from '@/helper/authcontext';
import {
  setApiMessage,
  checkOrganizationRole,
  DateFormat,
} from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import Searchbar from '@/components/UI/CustomSearch';
import CustomButton from '@/components/UI/CustomButton';
import DialogBox from '@/components/UI/Modalbox';
import CustomSelect from '@/components/UI/CustomSelect';
import { URLS } from '@/helper/constants/urls';
import { useRouter } from 'next/navigation';
import {
  fetchFromStorage,
  removeFromStorage,
  saveToStorage,
} from '@/helper/context/storage';
import { InviteIcon } from '@/helper/common/images';
import HeaderImage from '@/components/UI/ImageSecurity';
import { identifiers } from '@/helper/constants/identifier';
import { staticOptions } from '@/helper/common/staticOptions';
import CustomPagination from '@/components/UI/customPagination';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import NoDataView from '@/components/UI/NoDataView';
import useRoleList from '@/hooks/useRoleList';
// import FilterListIcon from '@mui/icons-material/FilterList';
import TuneIcon from '@mui/icons-material/Tune';
import './invitedstaff.scss';

export default function InvitedUser() {
  const router = useRouter();
  const { roleList, fetchRoleList } = useRoleList();
  const { userdata, setUserdata, authState, AllListsData } =
    useContext(AuthContext);
  const [userList, setUserList] = useState([]);
  const [userListInvite, setUserListInvite] = useState([]);
  const [loaderUser, setLoaderUser] = useState(true);
  // const [loader, setLoader] = useState(true);
  const [filter, setFilter] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [filterData, setFilterData] = useState({
    branch: '',
    role: '',
    department: '',
    status: '',
  });
  const [filterDataApplied, setFilterDataApplied] = useState({
    branch: '',
    role: '',
    department: '',
    status: '',
  });
  const [checkId, setCheckId] = useState([]);
  const [page, setPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(12);

  // List of all staff
  const getUserList = async (
    search,
    pageNo,
    branch,
    role,
    department,
    statusValue,
    Rpp
  ) => {
    // setLoader(true);
    setLoaderUser(true);

    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_INVITATION_USER_LIST +
          `?search=${search}&page=${pageNo}&size=${
            Rpp ? Rpp : rowsPerPage
          }&branch_id=${branch}&invitation_status=${statusValue}&role_id=${role}&department_id=${department}`
      );

      if (status === 200) {
        // setApiMessage('success', data?.message);
        setUserdata();
        removeFromStorage(identifiers?.RedirectData);
        setUserList(data?.data);
        const userlistinvited =
          data?.data &&
          data?.data?.length > 0 &&
          data?.data?.filter((f) => f?.invitation_status !== 'accepted');
        userlistinvited && setUserListInvite(userlistinvited);
        setTotalCount(data?.count);
        setCheckId([]);
        // setLoader(false);
        setLoaderUser(false);
      }
    } catch (error) {
      // setLoader(false);
      setLoaderUser(false);
      setUserList([]);
      setUserListInvite([]);
      setCheckId([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const stringToBrightColor = (string) => {
    let hash = 0;
    let i;

    for (i = 0; i < string?.length; i += 1) {
      hash = string.charCodeAt(i) + ((hash << 5) - hash);
    }

    let color = '#';

    for (i = 0; i < 3; i += 1) {
      let value = (hash >> (i * 8)) & 0xff;
      // Make the color bright by ensuring each color component is at least 128 and at most 255
      value = Math.min(value + 128, 255);
      color += `00${value.toString(16)}`.slice(-2);
    }

    return color;
  };

  const invertColor = (hex) => {
    hex = hex.slice(1); // Remove the '#'
    const r = (255 - parseInt(hex.slice(0, 2), 16))
      .toString(16)
      .padStart(2, '0');
    const g = (255 - parseInt(hex.slice(2, 4), 16))
      .toString(16)
      .padStart(2, '0');
    const b = (255 - parseInt(hex.slice(4, 6), 16))
      .toString(16)
      .padStart(2, '0');
    return `#${r}${g}${b}`;
  };

  const stringAvatar = (name) => {
    const brightColor = stringToBrightColor(name);
    const darkColor = invertColor(brightColor);
    return {
      sx: {
        color: '#FFFFFF',
        bgcolor: darkColor,
      },
      children:
        name?.split(' ')?.[0]?.[0] && name?.split(' ')[1]?.[0]
          ? `${name?.split(' ')?.[0]?.[0].toUpperCase()}${name
              ?.split(' ')[1]?.[0]
              .toUpperCase()}`
          : `${name?.split(' ')?.[0]?.[0].toUpperCase()}`,
    };
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      setPage(1);
      getUserList(
        searchValue,
        1,
        filterDataApplied?.branch,
        filterDataApplied?.role,
        filterDataApplied?.department,
        filterDataApplied?.status
      );
    }
  };
  const handleCheck = (value) => {
    const currentIndex = checkId.indexOf(value);
    const newChecked = [...checkId];
    if (currentIndex === -1) {
      newChecked.push(value);
    } else {
      newChecked.splice(currentIndex, 1);
    }
    setCheckId(newChecked);
  };
  const onPageChange = (newPage) => {
    setPage(newPage);
    getUserList(
      searchValue,
      newPage,
      filterDataApplied?.branch,
      filterDataApplied?.role,
      filterDataApplied?.department,
      filterDataApplied?.status
    );
  };
  const OnRowPerPage = (newPage) => {
    setRowsPerPage(newPage);
    setPage(1);
    getUserList(
      searchValue,
      1,
      filterDataApplied?.branch,
      filterDataApplied?.role,
      filterDataApplied?.department,
      filterDataApplied?.status,
      newPage
    );
  };
  const SendInvitation = async () => {
    // setLoader(true);
    let sendData = { user_ids: checkId };
    const isOrgLogin =
      checkOrganizationRole('org_master') ||
      checkOrganizationRole('super_admin') ||
      checkOrganizationRole('staff');

    try {
      const { status, data } = await axiosInstance.post(
        isOrgLogin ? URLS?.ORG_SEND_INVITATION : URLS?.SEND_INVITATION,
        sendData
      );

      if (status === 200) {
        // setLoader(false);
        setApiMessage('success', data?.message);
        getUserList(
          searchValue,
          1,
          filterDataApplied?.branch,
          filterDataApplied?.role,
          filterDataApplied?.department,
          filterDataApplied?.status
        );
        setCheckId([]);
        setPage(1);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);

      // setLoader(false);
    }
  };
  useEffect(() => {
    if (
      fetchFromStorage(identifiers?.RedirectData) &&
      fetchFromStorage(identifiers?.RedirectData)?.IsFromUser
    ) {
      const fdata = fetchFromStorage(identifiers?.RedirectData);
      setPage(fdata?.page);
      setFilterData(fdata?.filterData);
      setFilterDataApplied(fdata?.filterData);
      setSearchValue(fdata?.searchValue);
      getUserList(
        fdata?.searchValue,
        fdata?.page,
        fdata?.filterData?.branch,
        fdata?.filterData?.role,
        fdata?.filterData?.department,
        fdata?.filterData?.status
      );
    } else if (userdata && userdata?.IsFromUser) {
      const fdata = userdata;
      setPage(fdata?.page);
      setFilterData(fdata?.filterData);
      setFilterDataApplied(fdata?.filterData);
      setSearchValue(fdata?.searchValue);
      getUserList(
        fdata?.searchValue,
        fdata?.page,
        fdata?.filterData?.branch,
        fdata?.filterData?.role,
        fdata?.filterData?.department,
        fdata?.filterData?.status
      );
    }
  }, [
    fetchFromStorage(identifiers?.RedirectData)?.IsFromUser,
    userdata?.IsFromUser,
  ]);
  useEffect(() => {
    if (
      authState?.UserPermission?.user_invitation === 1 ||
      authState?.UserPermission?.user_invitation === 2
    ) {
      fetchRoleList();

      if (
        !fetchFromStorage(identifiers?.RedirectData) &&
        userdata?.page === undefined &&
        fetchFromStorage(identifiers?.RedirectData)?.IsFromUser === undefined &&
        userdata?.IsFromUser === undefined
      ) {
        getUserList(searchValue, page, '', '', '', '');
      }
    }
  }, [authState?.UserPermission?.user_invitation]);
  return (
    <>
      <Box>
        <Box className="invited-staff-section">
          <Box className="d-flex gap-sm align-center flex-wrap justify-end">
            <Searchbar
              setSearchValue={setSearchValue}
              searchValue={searchValue}
              onKeyPress={handleKeyPress}
            />
            <CustomButton
              variant="outlined"
              isIconOnly
              startIcon={
                <Tooltip
                  title={
                    <Typography className="sub-title-text">
                      Apply Filter
                    </Typography>
                  }
                  classes={{
                    tooltip: 'info-tooltip-container',
                  }}
                  arrow
                >
                  <TuneIcon />
                </Tooltip>
              }
              onClick={() => {
                setFilter(!filter);
              }}
            />
            <CustomButton
              variant="contained"
              className="title-text"
              title="Apply filter"
              fullWidth={false}
              onClick={() => {
                setPage(1);
                getUserList(
                  searchValue,
                  1,
                  filterDataApplied?.branch,
                  filterDataApplied?.role,
                  filterDataApplied?.department,
                  filterDataApplied?.status
                );
              }}
            />
          </Box>
          <Box className="mt16">
            {' '}
            {loaderUser ? (
              <Box className="content-loader">
                <CircularProgress className="loader" color="inherit" />
              </Box>
            ) : (
              <>
                {userList && userList?.length === 0 ? (
                  <Box className="no-data d-flex align-center justify-center">
                    <NoDataView
                      // image
                      title="No Staff Invitations Found "
                      description="There are no staff invitations available at the moment."
                    />
                  </Box>
                ) : (
                  <>
                    {/* {checkId && checkId?.length > 0 && ( */}
                    <Box className="d-flex justify-end">
                      <Box className="select-all-text-sec">
                        {' '}
                        {userListInvite?.length === 0 ? (
                          <Typography className="body-text select-all-text select-all-text-remove disabled">
                            {' '}
                            Select All
                          </Typography>
                        ) : checkId?.length === userListInvite?.length ? (
                          <Typography
                            className="body-text select-all-text select-all-text-remove"
                            onClick={() => {
                              setCheckId([]);
                            }}
                          >
                            {' '}
                            Select All
                          </Typography>
                        ) : (
                          <Typography
                            className="body-text select-all-text"
                            onClick={() => {
                              const checkall = userListInvite?.map(
                                (a) => a?.user_invite?.id
                              );
                              setCheckId(checkall);
                            }}
                          >
                            {' '}
                            Select All
                          </Typography>
                        )}
                      </Box>
                      {checkId && checkId?.length === 0 ? (
                        <CustomButton
                          className="title-text resend-invite-button"
                          variant="contained"
                          title="Resend invitation"
                          startIcon={InviteIcon()}
                          fullWidth={false}
                          disabled={checkId && checkId?.length === 0}
                          onClick={() => SendInvitation()}
                        />
                      ) : (
                        <CustomButton
                          className="title-text resend-invite-button"
                          variant="contained"
                          title="Resend invitation"
                          startIcon={InviteIcon()}
                          fullWidth={false}
                          onClick={() => SendInvitation()}
                        />
                      )}
                    </Box>
                    {/* )} */}

                    <Box className="User-list">
                      {userList &&
                        userList?.length > 0 &&
                        userList?.map((item) => {
                          return (
                            <Box
                              className={
                                // checkId && checkId?.length > 0
                                //   ?
                                'user-details-section selected-user'
                                // : 'user-details-section'
                              }
                            >
                              <Box className="users-section">
                                {item?.invitation_status !== 'accepted' && (
                                  <>
                                    {checkId?.includes(
                                      item?.user_invite?.id
                                    ) ? (
                                      <CheckBoxIcon
                                        className="check-icon check-icon-box"
                                        onClick={() => {
                                          handleCheck(item?.user_invite?.id);
                                        }}
                                      />
                                    ) : (
                                      <CheckBoxOutlineBlankIcon
                                        className="uncheck-icon check-icon-box"
                                        onClick={() => {
                                          handleCheck(item?.user_invite?.id);
                                        }}
                                      />
                                    )}
                                  </>
                                )}

                                {item &&
                                item?.user_invite?.user_avatar_link &&
                                item?.user_invite?.user_avatar ? (
                                  <Box className="profile-image">
                                    {/* <LazyLoadImage
                                      src={item?.user_invite?.user_avatar_link}
                                      className="profile cursor-pointer"
                                      alt="not found"
                                      onClick={() => {
                                        setUserdata({
                                          id: item?.user_invite?.id,
                                          filterData: filterDataApplied,
                                          searchValue: searchValue,
                                          IsInvite: true,
                                          page: page
                                        });
                                        saveToStorage(
                                          identifiers?.RedirectData,
                                          {
                                            id: item?.user_invite?.id,
                                            filterData: filterDataApplied,
                                            searchValue: searchValue,
                                            IsInvite: true,
                                            page: page
                                          }
                                        );
                                        router.push(
                                          `/user/${item?.user_invite?.id}?IsInvite=true`
                                        );
                                      }}
                                    /> */}
                                    <HeaderImage
                                      imageUrl={
                                        item?.user_invite?.user_avatar_link
                                      }
                                      className="profile cursor-pointer"
                                      alt="not found"
                                      onClick={() => {
                                        setUserdata({
                                          id: item?.user_invite?.id,
                                          filterData: filterDataApplied,
                                          searchValue: searchValue,
                                          IsInvite: true,
                                          page: page,
                                        });
                                        saveToStorage(
                                          identifiers?.RedirectData,
                                          {
                                            id: item?.user_invite?.id,
                                            filterData: filterDataApplied,
                                            searchValue: searchValue,
                                            IsInvite: true,
                                            page: page,
                                          }
                                        );
                                        router.push(
                                          `/user/${item?.user_invite?.id}?IsInvite=true`
                                        );
                                      }}
                                      type="lazyload"
                                    />
                                  </Box>
                                ) : (
                                  <Avatar
                                    className="profile-image profile-icon cursor-pointer"
                                    {...stringAvatar(
                                      item?.user_invite?.user_full_name
                                    )}
                                    onClick={() => {
                                      setUserdata({
                                        id: item?.user_invite?.id,
                                        filterData: filterDataApplied,
                                        searchValue: searchValue,
                                        IsInvite: true,
                                        page: page,
                                      });
                                      saveToStorage(identifiers?.RedirectData, {
                                        id: item?.user_invite?.id,
                                        filterData: filterDataApplied,
                                        searchValue: searchValue,
                                        IsInvite: true,
                                        page: page,
                                      });
                                      router.push(
                                        `/user/${item?.user_invite?.id}?IsInvite=true`
                                      );
                                    }}
                                  />
                                )}
                                <Box className="user-right-section">
                                  <Box className="w100">
                                    <Box className="user-name">
                                      <Typography
                                        className="body-text username text-ellipses fw600 cursor-pointer link-text-name mw100 text-ellipsis"
                                        onClick={() => {
                                          setUserdata({
                                            id: item?.user_invite?.id,
                                            filterData: filterDataApplied,
                                            searchValue: searchValue,
                                            IsInvite: true,
                                            page: page,
                                          });
                                          saveToStorage(
                                            identifiers?.RedirectData,
                                            {
                                              id: item?.user_invite?.id,
                                              filterData: filterDataApplied,
                                              searchValue: searchValue,
                                              IsInvite: true,
                                              page: page,
                                            }
                                          );
                                          router.push(
                                            `/user/${item?.user_invite?.id}?IsInvite=true`
                                          );
                                        }}
                                      >
                                        {' '}
                                        {item?.user_invite?.user_full_name}
                                      </Typography>

                                      <span className="invited-status">
                                        {item?.invitation_status ===
                                        'invited' ? (
                                          <span className="sub-title-text  invited-ongoing fw600 text-capital">
                                            {' '}
                                            {item?.invitation_status}{' '}
                                          </span>
                                        ) : item?.invitation_status ===
                                          'reinvited' ? (
                                          <span className="sub-title-text invited-draft fw600 text-capital">
                                            {' '}
                                            Re-Invited
                                          </span>
                                        ) : item?.invitation_status ===
                                          'accepted' ? (
                                          <span className="sub-title-text invited-accepted fw600 text-capital">
                                            {' '}
                                            {item?.invitation_status}{' '}
                                          </span>
                                        ) : (
                                          <span className="sub-title-text success fw600 text-capital">
                                            {item?.invitation_status}{' '}
                                          </span>
                                        )}
                                      </span>
                                    </Box>
                                    <Box className="user-emails">
                                      {' '}
                                      <Typography className="title-text email text-ellipses">
                                        {item?.user_invite?.user_email}
                                      </Typography>
                                      <span className="sub-title-text user-date">
                                        {DateFormat(
                                          item?.updatedAt,
                                          'datesUTC'
                                        )}
                                      </span>
                                    </Box>

                                    <Box className="user-emails">
                                      <Typography className="title-text email text-ellipses">
                                        {item?.user_invite?.department
                                          ?.department_name &&
                                        item?.user_invite?.branch?.branch_name
                                          ? item?.user_invite?.department
                                              ?.department_name +
                                            ' @ ' +
                                            item?.user_invite?.branch
                                              ?.branch_name
                                          : item?.user_invite?.department
                                                ?.department_name
                                            ? item?.user_invite?.department
                                                ?.department_name
                                            : item?.user_invite?.branch
                                                  ?.branch_name
                                              ? item?.user_invite?.branch
                                                  ?.branch_name
                                              : ''}
                                      </Typography>

                                      <span className="sub-title-text user-date">
                                        {DateFormat(
                                          item?.updatedAt,
                                          'hoursUTC'
                                        )}
                                      </span>
                                    </Box>
                                  </Box>
                                </Box>
                              </Box>
                            </Box>
                          );
                        })}
                    </Box>
                    <CustomPagination
                      className="bt0"
                      currentPage={page}
                      // totalPages={totalPages}
                      totalCount={totalCount}
                      rowsPerPage={rowsPerPage}
                      onPageChange={onPageChange}
                      OnRowPerPage={OnRowPerPage}
                      isInvited={true}
                    />
                  </>
                )}
              </>
            )}
          </Box>
        </Box>
      </Box>
      <DialogBox
        open={filter}
        handleClose={() => {
          setFilter(!filter);
        }}
        title={'Staff Invitation filter'}
        className="small-dialog-box-container"
        content={
          <>
            <Box className="staff-filter">
              <Box>
                {(authState?.UserPermission?.branch === 2 ||
                  authState?.UserPermission?.branch === 1) && (
                  <Box>
                    <CustomSelect
                      placeholder="Branch name"
                      showDot={true}
                      options={AllListsData?.ActiveBranchList}
                      value={
                        AllListsData?.ActiveBranchList?.find((opt) => {
                          return opt?.value === filterData?.branch;
                        }) || ''
                      }
                      onChange={(e) => {
                        setFilterData({
                          ...filterData,
                          branch: e?.value,
                        });
                      }}
                      label={<span>Branch name</span>}
                      menuPortalTarget={document.body}
                      styles={{
                        menuPortal: (base) => ({
                          ...base,
                          zIndex: 9999,
                        }),
                      }}
                    />
                  </Box>
                )}
                {(authState?.UserPermission?.department === 2 ||
                  authState?.UserPermission?.department === 1) && (
                  <Box className="pt8">
                    <CustomSelect
                      placeholder="Department name"
                      options={AllListsData?.ActiveDepartmentList}
                      value={
                        AllListsData?.ActiveDepartmentList?.find((opt) => {
                          return opt?.value === filterData?.department;
                        }) || ''
                      }
                      onChange={(e) => {
                        setFilterData({
                          ...filterData,
                          department: e?.value,
                        });
                      }}
                      label={<span>Department name</span>}
                      menuPortalTarget={document.body}
                      styles={{
                        menuPortal: (base) => ({
                          ...base,
                          zIndex: 9999,
                        }),
                      }}
                    />
                  </Box>
                )}
                <Box className="pt8">
                  <CustomSelect
                    placeholder="Role"
                    options={roleList}
                    value={
                      roleList?.find((opt) => {
                        return opt?.value === filterData?.role;
                      }) || ''
                    }
                    onChange={(e) => {
                      setFilterData({ ...filterData, role: e?.value });
                    }}
                    label={<span>Role</span>}
                    menuPortalTarget={document.body}
                    styles={{
                      menuPortal: (base) => ({
                        ...base,
                        zIndex: 9999,
                      }),
                    }}
                  />
                </Box>

                <Box className="pt8">
                  <CustomSelect
                    placeholder="Status"
                    options={staticOptions?.INVITE_STATUS}
                    value={
                      staticOptions?.INVITE_STATUS?.find((opt) => {
                        return opt?.value === filterData?.status;
                      }) || ''
                    }
                    onChange={(e) => {
                      setFilterData({ ...filterData, status: e?.value });
                    }}
                    label={<span>Status</span>}
                    menuPortalTarget={document.body}
                    styles={{
                      menuPortal: (base) => ({
                        ...base,
                        zIndex: 9999,
                      }),
                    }}
                  />
                </Box>
                <Box className="form-actions-btn">
                  <CustomButton
                    fullWidth
                    className="sub-title-text"
                    variant="outlined"
                    title="Clear"
                    onClick={() => {
                      setFilter(false);
                      setPage(1);
                      setFilterData({
                        branch: '',
                        role: '',
                        department: '',
                        status: '',
                      });
                      setFilterDataApplied({
                        branch: '',
                        role: '',
                        department: '',
                        status: '',
                      });
                      getUserList(searchValue, 1, '', '', '', '');
                    }}
                  />
                  <CustomButton
                    fullWidth
                    className="sub-title-text"
                    variant="contained"
                    title="Apply"
                    onClick={() => {
                      setFilter(false);
                      setPage(1);
                      setFilterDataApplied({
                        branch: filterData?.branch,
                        role: filterData?.role,
                        department: filterData?.department,
                        status: filterData?.status,
                      });
                      getUserList(
                        searchValue,
                        1,
                        filterData?.branch,
                        filterData?.role,
                        filterData?.department,
                        filterData?.status
                      );
                    }}
                  />
                </Box>
              </Box>
            </Box>
          </>
        }
      />
    </>
  );
}
