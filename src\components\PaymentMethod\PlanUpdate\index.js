'use client';
import React, { useEffect, useState } from 'react';
import {
  Box,
  Checkbox,
  Divider,
  TextareaAutosize,
  Typography,
} from '@mui/material';
import { Form, Formik } from 'formik';
import * as Yup from 'yup';
import CustomSelect from '@/components/UI/selectbox';
import { CustomTextField } from '@/components/UI/CommonField';
import CustomButton from '@/components/UI/button';
import CustomDatePicker from '@/components/UI/datepicker';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { ORG_URLS } from '@/helper/constants/urls';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { useRouter } from 'next/navigation';
import moment from 'moment';
import dayjs from 'dayjs';
import './planupdate.scss';

export default function PlanUpdate({ planData, OrgData }) {
  const router = useRouter();
  const [planList, setPlanData] = useState([]);
  const [planId, setPlanId] = useState();

  // // Fetches the details of a subscription plan by its ID.
  // const getSubPlanDetailsById = async (id) => {
  //   // setLoader(true);
  //   try {
  //     const { status, data } = await axiosInstance.get(
  //       ORG_URLS.GET_SUBSCRIPTION_PLAN_BY_ID + `/${id}`
  //     );
  //     if (status === 200) {
  //       setSubPlanData(data?.data);
  //     }
  //   } catch (error) {
  //     setApiMessage(
  //       'error',
  //       error?.response?.data?.message || 'Error fetching data'
  //     );
  //   }
  // };

  const getSubPlan = async () => {
    try {
      const { status, data } = await axiosInstance.get(
        ORG_URLS.GET_ALL_SUBSCRIPTION_PLAN
      );
      if (status === 200) {
        console.log(data?.data, 'data?.data');
        const plan =
          data?.data &&
          data?.data?.length > 0 &&
          data?.data?.map((p) => {
            return { label: p?.subs_plan_name, value: p?.id };
          });
        plan && setPlanData(plan);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // // useEffect hook that triggers when the `params?.id` changes.
  useEffect(() => {
    getSubPlan();
  }, []);
  useEffect(() => {
    planData?.plan_id && setPlanId(planData?.plan_id);
  }, [planData?.plan_id]);

  console.log(OrgData, planData, 'OrgData');
  console.log(planData, 'planData');

  return (
    <Box className="create-plan-wrap">
      <Formik
        enableReinitialize
        initialValues={{
          plan: planData && planData?.plan_id ? planData?.plan_id : '',
          expiryDate:
            planData && planData?.sp_end_date ? planData?.sp_end_date : '',
          minEmployees:
            planData && (planData?.sp_user_min || planData?.sp_user_min === 0)
              ? planData?.sp_user_min
              : '',
          maxEmployees:
            planData && (planData?.sp_user_max || planData?.sp_user_max === 0)
              ? planData?.sp_user_max
              : '',
        }}
        validationSchema={Yup.object({
          plan: Yup.string().required('Plan is required'),
          expiryDate:
            planData?.plan_id === planId &&
            Yup.string().required('Expiry Date is required'),
          minEmployees:
            planData?.plan_id === planId &&
            Yup.number()
              .typeError('Min Emp must be a number')
              .required('Min Emp is required')
              // .positive('Min Emp must be a positive number')
              .integer('Min Emp must be a whole number'),
          maxEmployees:
            planData?.plan_id === planId &&
            Yup.number()
              .typeError('Max Emp must be a number')
              .required('Max Emp is required')
              // .positive('Max Emp must be a positive number')
              .integer('Max Emp must be a whole number'),
        })}
        onSubmit={async (requestedData) => {
          let sendData = {
            // purchased_id: planData?.id,
            ...(planData?.plan_id === requestedData?.plan && {
              plan_end_date: dayjs(requestedData?.expiryDate).format(
                'YYYY-MM-DD'
              ),
              sp_user_min: Number(requestedData?.minEmployees),
              sp_user_max: Number(requestedData?.maxEmployees),
            }),
            ...(requestedData?.plan !== planData?.plan_id && {
              plan_id: requestedData?.plan,
            }),
            user_id: planData?.admin_id,
            id: planData?.id,
            provider_id: null,
          };
          try {
            const { status, data } = await axiosInstance.put(
              `${ORG_URLS.UPDATE_SUB_PLAN}`,
              sendData
            );

            if (status === 200) {
              router.push('/sorg/organization');
              setApiMessage('success', data?.message);
            } else {
              setApiMessage('error', data?.message);
            }
          } catch (error) {
            setApiMessage('error', error?.response?.data?.message);
          }
        }}
      >
        {({
          values,
          handleChange,
          handleBlur,
          handleSubmit,
          errors,
          touched,
          setFieldValue,
        }) => (
          <Form onSubmit={handleSubmit} className="create-sub-plan-form">
            <Box className="display-grid">
              <Box className="select-box pt16">
                <CustomSelect
                  className={`slected-wrap ${
                    touched?.plan && errors?.plan ? 'textfeild-error' : ''
                  }`}
                  placeholder="Select Plan"
                  options={planList}
                  value={values?.plan}
                  name="plan"
                  onChange={(e) => {
                    setFieldValue('plan', e?.target?.value);
                    setPlanId(e?.target?.value);
                    setFieldValue('minEmployees', '');
                    setFieldValue('maxEmployees', '');
                    setFieldValue('expiryDate', null);
                  }}
                  label={
                    <span
                      className={
                        touched?.plan && errors?.plan
                          ? 'Inter12 error-label'
                          : ''
                      }
                    >
                      Plan*
                    </span>
                  }
                />
                {touched?.plan && errors?.plan && (
                  <Typography
                    variant="body2"
                    color="error"
                    className="field-error"
                  >
                    {errors?.plan}
                  </Typography>
                )}
              </Box>

              <Box className="employee-input-wrap width100 gap-5">
                <Box className="">
                  <Box className="employee-fields-row d-flex pt16">
                    <CustomTextField
                      InputLabelProps={{ shrink: true }}
                      id="minEmployees"
                      name="minEmployees"
                      label={
                        planData?.plan_id === planId
                          ? 'Employee Range*'
                          : 'Employee Range'
                      }
                      placeholder="Min Employees"
                      value={values?.minEmployees}
                      error={
                        touched?.minEmployees && Boolean(errors?.minEmployees)
                      }
                      helperText={touched?.minEmployees && errors?.minEmployees}
                      className="employee-field min-field sub-plan-input-wrap"
                      variant="filled"
                      onBlur={handleBlur}
                      onChange={handleChange}
                      fullWidth
                      disabled={planData?.plan_id !== values?.plan}
                      onInput={(e) => {
                        e.target.value = e?.target?.value.replace(
                          /[^0-9]/g,
                          ''
                        );
                      }}
                    />
                    <CustomTextField
                      InputLabelProps={{ shrink: true }}
                      id="maxEmployees"
                      name="maxEmployees"
                      placeholder="Max Employees"
                      value={values?.maxEmployees}
                      error={
                        touched?.maxEmployees && Boolean(errors?.maxEmployees)
                      }
                      helperText={touched?.maxEmployees && errors?.maxEmployees}
                      className="employee-field max-field sub-plan-input-wrap"
                      variant="filled"
                      onBlur={handleBlur}
                      onChange={handleChange}
                      disabled={planData?.plan_id !== values?.plan}
                      fullWidth
                      onInput={(e) => {
                        e.target.value = e?.target?.value.replace(
                          /[^0-9]/g,
                          ''
                        );
                      }}
                    />
                  </Box>
                </Box>
              </Box>
              <Box className="textfeild date-picker-textfield date-field pt16">
                <CustomDatePicker
                  // label='Date of Decision *'
                  label={
                    <span>
                      Expiry Date
                      {planData?.plan_id === planId && (
                        <span className="primary-color"> *</span>
                      )}
                    </span>
                  }
                  className={
                    touched.expiryDate && errors.expiryDate
                      ? 'textfeild-error'
                      : ''
                  }
                  error={Boolean(touched.expiryDate && errors.expiryDate)}
                  helperText={touched.expiryDate && errors.expiryDate}
                  name="expiryDate"
                  value={dayjs(values?.expiryDate)}
                  onBlur={handleBlur}
                  onChange={(date) => {
                    setFieldValue('expiryDate', date);
                  }}
                  inputVariant="outlined"
                  disablePast
                  disabled={planData?.plan_id !== values?.plan}
                />
                {touched.expiryDate && errors.expiryDate && (
                  <Typography
                    variant="body2"
                    color="error"
                    className="field-error"
                  >
                    {errors.expiryDate}
                  </Typography>
                )}
              </Box>
            </Box>

            <Box className="pt32 d-flex justify-end create-plan-btn-wrap">
              <CustomButton
                className="p16 create-plan-btn"
                type="submit"
                fontWeight="600"
                variant="contained"
                background="#39596e"
                backgroundhover="#39596e"
                colorhover="#FFFFFF"
                title={'Update plan'}
              />
            </Box>
          </Form>
        )}
      </Formik>
    </Box>
  );
}
