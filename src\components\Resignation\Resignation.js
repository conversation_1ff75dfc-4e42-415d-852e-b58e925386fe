'use client';

import React, { useEffect, useState } from 'react';
import { Box } from '@mui/material';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomButton from '@/components/UI/CustomButton';
import CustomCheckbox from '@/components/UI/CustomCheckbox';
import { setApiMessage } from '@/helper/common/commonFunctions';
import PreLoader from '@/components/UI/Loader';
import ResignationDetails from './ResignationDetails/ResignationDetails';
import {
  getResignationDetails,
  sendResignation,
} from '@/services/resignationService';
import './resignationremark.scss';

export default function Resignation() {
  const [loader, setLoader] = useState(true);
  const [resignationDetails, setResignationDetails] = useState();
  const [isLoader, setIsLoader] = useState(false);

  const getResignationdetails = async () => {
    setLoader(true);
    try {
      const response = await getResignationDetails();
      setLoader(false);
      if (
        response.success &&
        response.data &&
        Object.keys(response.data).length > 0
      ) {
        setResignationDetails(response.data);
      } else {
        setResignationDetails(null);
      }
    } catch (error) {
      setLoader(false);
      setResignationDetails(null);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  useEffect(() => {
    getResignationdetails();
  }, []);

  return (
    <>
      <Box className="resignation-page-container">
        {loader ? (
          <PreLoader />
        ) : resignationDetails ? (
          <ResignationDetails
            resignationDetails={resignationDetails}
            customClassName="resignation-view"
          />
        ) : (
          <Formik
            initialValues={{
              subject: '',
              remark: '',
              understandResignation: false,
            }}
            enableReinitialize={true}
            validationSchema={Yup.object().shape({
              subject: Yup.string().trim().required('This field is required'),
              remark: Yup.string().trim().required('This field is required'),
              understandResignation: Yup.boolean()
                .oneOf([true], 'You must acknowledge this before proceeding')
                .required('You must acknowledge this before proceeding'),
            })}
            onSubmit={async (requestData) => {
              setIsLoader(true);
              let sendData = {
                resignation_subject: requestData?.subject
                  ? requestData?.subject
                  : null,
                resignation_reason: requestData?.remark
                  ? requestData?.remark
                  : '',
              };
              try {
                const response = await sendResignation(sendData);
                if (response.success) {
                  getResignationdetails();
                  setApiMessage('success', response.message);
                } else {
                  setApiMessage('error', response.message);
                }
                setIsLoader(false);
              } catch (error) {
                setIsLoader(false);
                getResignationdetails();
                setApiMessage('error', error?.response?.data?.message);
              }
            }}
          >
            {({
              errors,
              touched,
              handleBlur,
              values,
              handleSubmit,
              handleChange,
              dirty,
            }) => (
              <Form onSubmit={handleSubmit}>
                <Box className="resignation-form-container">
                  <Box className="resignation-page-form">
                    <CustomTextField
                      required
                      fullWidth
                      id="subject"
                      name="subject"
                      value={values?.subject}
                      label="Subject"
                      placeholder="Enter Subject"
                      error={Boolean(touched.subject && errors.subject)}
                      helperText={touched.subject && errors.subject}
                      onBlur={handleBlur}
                      onChange={handleChange}
                    />
                    <Box className="mt8">
                      <CustomTextField
                        required
                        fullWidth
                        id="remark"
                        name="remark"
                        multiline
                        rows={3}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={Boolean(touched.remark && errors.remark)}
                        helperText={touched.remark && errors.remark}
                        placeholder="Reason"
                        value={values?.remark}
                        label="Reason"
                      />
                    </Box>
                    <Box className="mt16">
                      <CustomCheckbox
                        name="understandResignation"
                        checked={values.understandResignation}
                        onChange={handleChange}
                        label="I understand that submitting this form initiates the formal resignation process and is considered my official notice"
                        error={Boolean(
                          touched.understandResignation &&
                            errors.understandResignation
                        )}
                        helperText={
                          touched.understandResignation &&
                          errors.understandResignation
                        }
                      />
                    </Box>
                    <Box className="mt16 d-flex justify-end">
                      <CustomButton
                        variant="contained"
                        type="submit"
                        title={`${isLoader ? 'Resigning...' : 'Resign'}`}
                        disabled={
                          isLoader || !dirty || !values.understandResignation
                        }
                        fullWidth={false}
                      />
                    </Box>
                  </Box>
                </Box>
              </Form>
            )}
          </Formik>
        )}
      </Box>
    </>
  );
}
