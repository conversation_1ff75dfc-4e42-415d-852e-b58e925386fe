'use client';
import { Box, Tooltip, Typography } from '@mui/material';
import React, { useState, useEffect, useContext } from 'react';
import { DataGrid, gridClasses } from '@mui/x-data-grid';
import { useRouter } from 'next/navigation';
import BranchDepartmentDisplay from '@/components/BranchDepartmentDisplay';
import CustomPagination from '@/components/UI/customPagination';
import AuthContext from '@/helper/authcontext';
import ViewIcon from '@/components/ActionIcons/ViewIcon';
import NoDataView from '@/components/UI/NoDataView';
import { DateFormat } from '@/helper/common/commonFunctions';

export default function DeletedBudget({
  delBudgetData,
  pageDel,
  // seprateBranch,
  setPageDel,
  totalCountDel,
  // setTotalCountDel,
  RowsPerPageDel,
  setRowsPerPageDel,
  getBudgetListDeleted,
  FilterBudgetStored,
}) {
  const { authState } = useContext(AuthContext);
  const [branchColumnWidth, setBranchColumnWidth] = useState(200);
  const IsAccountant = authState?.web_user_active_role_id === 6;
  const router = useRouter();

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth <= 899) {
        setBranchColumnWidth(350);
      } else {
        setBranchColumnWidth(200);
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize();
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const onPageChange = (newPage) => {
    setPageDel(newPage);
    getBudgetListDeleted(
      authState?.assign_branch_ids,
      FilterBudgetStored,
      newPage
    );
  };
  const OnRowPerPage = (newPage) => {
    setPageDel(1);
    setRowsPerPageDel(newPage);
    getBudgetListDeleted(
      authState?.assign_branch_ids,
      FilterBudgetStored,
      1,
      newPage
    );
  };

  const columns = [
    {
      field: 'id',
      headerName: 'ID',
      width: 80,
      minWidth: 80,
      flex: 0,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-start h100 gap-sm flex-wrap">
            <Typography className="title-text">
              <span>{params?.value}</span>
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'forecastDetails',
      headerName: 'Branch',
      width: branchColumnWidth,
      minWidth: branchColumnWidth,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <BranchDepartmentDisplay
            row={{
              branch: { id: params?.row?.forecastDetails?.branch_id },
            }}
            isBranchOnly
          />
        );
      },
    },
    {
      field: 'forecast_branchs',
      headerName: 'Year',
      width: 150,
      minWidth: 150,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <>
            <Box className="d-flex align-center justify-start h100 gap-sm flex-wrap">
              <Typography className="title-text">
                <span>{params?.row?.forecastDetails?.forecast_year}</span>
              </Typography>
            </Box>
          </>
        );
      },
    },
    // {
    //   field: 'date',
    //   headerName: 'Date',
    //   width: 100,
    //   minWidth: 100,
    //   flex: 1,
    //   sortable: false,
    //   headerAlign: 'start',
    //   align: 'start',
    //   renderCell: (params) => {
    //     return (
    //       <Box className="d-flex align-center justify-start h100 gap-sm flex-wrap title-text">
    //         <Box sx={{ boxShadow: 'none' }}>
    //           <Typography className="title-text">12/12/2222</Typography>
    //         </Box>
    //       </Box>
    //     );
    //   },
    // },
    {
      field: 'Deleted',
      headerName: 'Deleted By',
      width: 250,
      minWidth: 250,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-start h100 gap-sm flex-wrap title-text">
            <Box>
              <Typography className="title-text text-ellipsis-line">
                {params?.row?.forecast_history_created_by?.user_full_name}
              </Typography>
              {params?.row?.updatedAt ? (
                <Typography className="title-text text-ellipsis-line">
                  {DateFormat(params?.row?.updatedAt, 'datesWithhour')}
                </Typography>
              ) : null}
            </Box>
          </Box>
        );
      },
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        // Custom actions icons for each row
        return (
          <Box className="d-flex align-center justify-center actions">
            <Tooltip
              title={<Typography>View</Typography>}
              arrow
              classes={{
                tooltip: 'info-tooltip-container ',
              }}
            >
              <Box>
                <ViewIcon
                  onClick={() => {
                    // setUserdata({
                    //   id: params?.row?.id,
                    //   searchValue: searchValue,
                    //   page: page,
                    //   IsAdmin: true,
                    // });
                    // saveToStorage(identifiers?.RedirectData, {
                    //   id: params?.row?.id,
                    //   searchValue: searchValue,
                    //   page: page,
                    //   IsAdmin: true,
                    // });
                    router.push(
                      `/budget-forecast/${params?.row?.forecast_id}?isFromDelete=true`
                    );
                  }}
                />
              </Box>
            </Tooltip>
          </Box>
        );
      },
    },
  ];
  return (
    <Box className="leave-balance-sec delete-budget-sec">
      <Box className="table-container table-layout">
        <>
          {delBudgetData && delBudgetData?.length === 0 ? (
            <Box className="no-data d-flex align-center justify-center">
              <NoDataView
                title="No Deleted Budget Found"
                description="There is no deleted budget available at the moment."
              />
            </Box>
          ) : (
            <>
              <Box className="data-grid-wrap">
                {/* {seprateBranch ? (
                  <> */}
                <DataGrid
                  rows={delBudgetData}
                  columns={columns}
                  pageSize={RowsPerPageDel}
                  checkboxSelection={false}
                  disableSelectionOnClick
                  rowHeight={70}
                  autoHeight
                  columnVisibilityModel={{
                    actions: !IsAccountant ? true : false,
                  }}
                  // getRowHeight={() => 'auto'}
                  sx={{
                    transition: 'none', // Disables transition effects
                    [`& .${gridClasses.cell}`]: {
                      py: 1,
                    },
                  }}
                />
                {/* </>
                ) : (
                  <></>
                )} */}
              </Box>
              <CustomPagination
                currentPage={pageDel}
                totalCount={totalCountDel}
                rowsPerPage={RowsPerPageDel}
                onPageChange={onPageChange}
                OnRowPerPage={OnRowPerPage}
              />
            </>
          )}
        </>
      </Box>
    </Box>
  );
}
