.contract-view-main-container {
  overflow: hidden;
}
.contract-view-container {
  background-color: var(--color-white);
  padding: 0px 24px;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-xs);
  width: 100%;
  transition: all 0.5s ease;
  overflow: auto;
  // height: calc(100vh - 130px - var(--banner-height));
  height: 100%;
  position: relative;
  @media (max-width: 768px) {
    padding: 18px;
  }
  .version-title-header {
    flex-wrap: wrap;
    gap: 10px;
    position: sticky;
    top: 0;
    background: var(--color-white);
    padding: 24px 0px;
  }
  .contract-view-btn-list {
    background: var(--color-white);
    height: 100%;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-xs);
    border: var(--normal-sec-border);
    .contract-view-btn {
      padding: var(--spacing-xxs);
      cursor: pointer;
      svg {
        width: var(--icon-size-md);
        height: var(--icon-size-md);
      }
      &:hover {
        background: var(--action-icon-bg-color);
        border-radius: var(--border-radius-xs);
      }
    }
  }
  .contract-view-wrap {
    border: var(--normal-sec-border);
    border-radius: var(--border-radius-xs);
    margin: 20px 5px;
    padding: var(--spacing-lg);
    ol,
    ul {
      padding-left: var(--spacing-lg);
    }
  }
}
// .opened-layout {
//   .right-sidebar-container {
//     margin-right: -30px !important;
//   }
// }

.right-sidebar-container {
  max-width: 300px;
  width: 100%;
  // height: calc(100vh - 69px - var(--banner-height));
  background-color: var(--color-white);
  padding-bottom: 12px;
  border-radius: 0px;
  margin-left: 20px;
  margin-top: -10px;
  margin-right: -10px;
  margin-bottom: -30px;
  // overflow: auto;
  // height: 100vh;
  // @media (max-width: 899px) {
  //   margin-right: -30px;
  // }
  // @media (max-width: 599px) {
  //   // height: calc(100vh - 79px);
  //   margin-right: -15px;
  // }
  .contract-sidebar-header {
    padding: 24px 16px;
  }
  .history-log-remark {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  .history-log {
    padding: 10px 20px;
  }
  .history-log.active {
    background-color: var(--color-primary-opacity);
  }
}
