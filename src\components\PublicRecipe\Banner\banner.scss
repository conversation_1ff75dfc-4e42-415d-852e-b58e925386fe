body {
  .public-banner-section {
    position: relative;
    width: 100%;
    overflow: hidden;

    .banner-image-wrap {
      position: relative;
      width: 100%;
      height: 386px;
      overflow: hidden;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.4); // Dark overlay with 40% opacity
        z-index: 1;
      }
    }

    .banner-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .banner-title-text {
      font-size: var(--font-size-6xl);
      color: var(--text-color-white);
      font-family: var(--font-family-brown-regular);
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 2; // Increased z-index to appear above the overlay
    }
  }
}
