.Budget-table {
  position: relative;
  table {
    border-collapse: collapse;
  }
  .Budget-table-container {
    // position: relative;
    margin-left: 99px;
    margin-right: calc(100px + 100px + 100px + 99px);
    width: auto !important;
    box-shadow: none;
    @media (max-width: 700px) {
      margin-left: 0;
      margin-right: 0;
    }
    .table-cell {
      border: var(--table-border);
    }

    .heading-cell {
      padding: 5px 4px;
      font-family: var(--font-family-primary);
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-regular);
      color: var(--text-color-black);
      line-height: var(--line-height-base);
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      overflow: hidden;
      word-break: break-all;
    }
    .fixed-header-cell {
      position: absolute;
      min-width: 150px;
      left: 0px;
      // top: auto;
      top: 0px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding-top: 0 !important;
      min-height: 35px;
      border-bottom: 0 !important;

      // Table Border
      @media (max-width: 700px) {
        position: inherit;
      }
    }
    .fixed-header-cell-children {
      min-height: 63px;
    }
    .fixed-value {
      position: absolute;
      min-width: 150px;
      left: 0px;
      top: auto;
      font-weight: 500; //600
      // z-index: 1;
      @media (max-width: 700px) {
        position: inherit;
      }
    }

    .fixed-header-cell-last {
      position: absolute;
      min-width: 150px;
      right: 0px;
      // top: auto;
      top: 0px;
      height: 63px;
      border-bottom: 0 !important;
      @media (max-width: 700px) {
        position: inherit;
      }
    }
    .fixed-header-cell-last-secound {
      position: absolute;
      // min-width: 150px;
      right: calc(99px);
      // top: auto;
      top: 0px;
      @media (max-width: 700px) {
        position: inherit;
      }
    }
    .fixed-cell-last {
      position: absolute;
      min-width: 150px;
      right: 0px;
      top: auto;
      min-height: 34px;
      @media (max-width: 700px) {
        position: inherit;
      }
    }
    .fixed-cell-last-secound-0 {
      position: absolute;
      // min-width: 150px;
      right: calc(100px + 100px + 99px);
      top: auto;
      min-height: 32px;
      @media (max-width: 700px) {
        position: inherit;
      }
    }
    .fixed-cell-last-secound-1 {
      position: absolute;
      // min-width: 150px;
      right: calc(100px + 99px);
      top: auto;
      min-height: 32px;
      border-left: 0;
      @media (max-width: 700px) {
        position: inherit;
      }
    }
    .fixed-cell-last-secound-2 {
      position: absolute;
      // min-width: 150px;
      right: calc(99px);
      top: auto;
      min-height: 33px;
      //   border-bottom: 0;
      border-left: 0;
      @media (max-width: 700px) {
        position: inherit;
      }
    }
    .fixed-value-last {
      // position: absolute;
      // min-width: 150px;
      // left: 0px;
      // top: auto;
      // font-weight: 500; //600
    }
    .firstValue-cell {
      border-left: 0 !important;
    }

    .group-table-row {
      display: flex;
      margin-top: 8px;

      .table-cell:first-child {
        border-left: 0;
      }

      .table-cell:last-child {
        border-right: 0;
      }

      .heading-cell {
        border-bottom: 0;
        border-right: 0;
      }
    }

    .table-head {
      padding: 6px 0 0px;
      font-weight: 600;
      border: var(--table-border);
      background-color: var(--color-dark-lavender);
    }

    .table-value {
      padding: 7px 2px 7px 7px;
      font-family: var(--font-family-primary);
      font-size: var(--font-size-xs);
      font-weight: var(--font-weight-regular);
      color: var(--text-color-black);
      line-height: var(--line-height-base);
    }
    .total-row {
      .table-value {
        background-color: var(--color-dark-lavender);
        span {
          // font-weight: 500;
          width: 97%;
        }
      }
      .fixed-value.table-value {
        span {
          // font-weight: 600;
          font-weight: 500;
        }
      }
      .one-cell {
        span {
          // font-weight: 500;
          width: 100%;
        }
      }
    }
    .difference-cells {
      //   padding: 0px 2px 0 7px;
      .difference-cell {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: nowrap;

        // .table-value-diff {
        // 	width: calc(60% - 12px);
        // 	max-width: calc(60% - 12px);
        // }
        .table-per-diff {
          //   width: calc(40% - 12px);
          //   max-width: calc(40% - 12px);
        }
        .arrow-down {
          fill: var(--color-danger);
          // width: 24px;
          // height: 24px;
          // path {
          //   width: 24px;
          //   height: 24px;
          // }
        }
        .arrow-up {
          fill: var(--color-green);
          // width: 24px;
          // height: 24px;
          // path {
          //   width: 24px;
          //   height: 24px;
          // }
        }
      }
    }
  }
  .budget-input-value {
    width: 100%;
    // max-width: 300px;
    .MuiInputBase-root {
      min-height: 25px !important;
      padding-left: 0px !important;
      padding-right: 6px !important;
      .MuiInputBase-input {
        padding: 1px 3px 1px !important;
        font-family: var(--font-family-primary);
        font-size: var(--font-size-xs);
        font-weight: var(--font-weight-regular);
        color: var(--text-color-black);
        line-height: var(--line-height-base);
        text-align: end;
      }

      ::placeholder {
        font-weight: 400;
        text-transform: 'capitalize';
        font-size: 12px;
        line-height: 18px;
      }
    }
    .MuiFormLabel-root {
      display: none;
    }
  }
  .text-field-cell {
    // padding: 0px 5px !important;
    padding: 3px 5px 4px !important;
    .MuiInputAdornment-root {
      margin-top: 0 !important;
      width: 0px;
      .currency {
        color: var(--text-color-black);
      }
    }
  }

  .total-more-cell {
    color: var(--color-danger);
  }
  .main-category {
    padding: 21px 0 0 0;
  }
  .group-border-top-0.fixed-cell-last-secound-0.table-value,
  .group-border-top-0.fixed-cell-last-secound-1.table-value,
  .group-border-top-0.fixed-cell-last-secound-2.table-value {
    // @media (min-width: 599px) {
    border-top: 0 !important;
    // }
  }
  .main-header-row {
    .table-value {
      padding: 12px 2px !important;
      background-color: var(--color-light-blue);

      span {
        color: var(--color-dark-blue);
        width: 97%;
      }
    }
    .table-child {
      border-right: 0 !important;
      border-left: 0 !important;
    }
    .fixed-value.table-value {
      padding: 3px 2px !important;
      border-bottom: 0 !important;
      min-width: 100px;
      span {
        font-weight: 600;
      }
    }
    // .fixed-cell-last.table-value,
    .fixed-cell-last-secound-0.table-value,
    .fixed-cell-last-secound-1.table-value,
    .fixed-cell-last-secound-2.table-value {
      padding: 12px 2px !important;
      border-bottom: 0 !important;
      min-width: 100px;
      min-height: auto !important;
      span {
        font-weight: 500;
      }
    }
    .fixed-cell-last.table-value {
      padding: 0px 2px !important;
      min-height: 28px !important;
    }
    .fixed-cell-last-secound-0.table-value {
      border-left: var(--table-border) !important;
    }
    .one-cell {
      span {
        width: 100%;
      }
    }
  }
  .sub-header-row {
    .table-value {
      padding: 12px 2px !important;
      background-color: var(--action-icon-bg-color);
      span {
        // font-weight: 500;
        width: 97%;
      }
    }
    .table-child {
      border-right: 0 !important;
      border-left: 0 !important;
    }
    .fixed-value.table-value {
      padding: 3px 2px !important;
      border-bottom: 0 !important;
      min-width: 100px;
      span {
        font-weight: 500;
      }
    }
    // .fixed-cell-last.table-value,
    .fixed-cell-last-secound-0.table-value,
    .fixed-cell-last-secound-1.table-value,
    .fixed-cell-last-secound-2.table-value {
      padding: 12px 2px !important;
      border-bottom: 0 !important;
      min-width: 100px;
      min-height: auto !important;
      span {
        font-weight: 500;
      }
    }
    .fixed-cell-last-secound-0.table-value {
      border-left: var(--table-border) !important;
    }
    .fixed-cell-last.table-value {
      padding: 0px 2px !important;
      min-height: 26px !important;
    }
    .one-cell {
      span {
        width: 100%;
      }
    }
  }
  .sub-header-row-combined {
    .table-value {
      background-color: var(--action-icon-bg-color);
    }
  }
  .currency-symbol {
    max-width: 24px !important;
    // width: auto !important;
    // align-self: flex-end;
  }
  .border-right-0 {
    border-right: 0 !important;
  }
}
