'use client';
import React, { useEffect, useState, useContext } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Avatar,
  Box,
  Typography,
  Tooltip,
} from '@mui/material';
import { CustomTextField } from '@/components/UI/CommonField';
import CustomButton from '@/components/UI/button';
import HeaderImage from '@/components/UI/ImageSecurity';
import EditIcon from '@mui/icons-material/Edit';
import SearchIcon from '@mui/icons-material/Search';
import AuthContext from '@/helper/authcontext';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { ORG_URLS } from '@/helper/constants/urls';
import { formatDuration, setApiMessage } from '@/helper/common/commonFunctions';
import CustomPagination from '@/components/UI/pagination';
import { useRouter } from 'next/navigation';
import './orglist.scss';

const formatDate = (dateString) => {
  const date = new Date(dateString);
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  return `${day}/${month}/${year}`;
};

export default function OrgList() {
  const [search, setSearch] = useState('');
  const [orgData, setOrgData] = useState();
  const { authState } = useContext(AuthContext);
  const [page, setPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const router = useRouter();
  const currency =
    authState?.currency_details && authState?.currency_details?.symbol
      ? authState?.currency_details?.symbol
      : '£';
  const getOrgData = async (search, page, Rpp) => {
    try {
      const { status, data } = await axiosInstance.get(
        ORG_URLS.GET_ALL_ORGANIZATION_DATA +
          `?searchString=${search}&page=${page}&size=${Rpp ? Rpp : rowsPerPage}`
      );
      if (status === 200) {
        setOrgData(data?.data || []);
        setTotalCount(data?.count);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // const calculateExpiryDate = (registerDate, frequency) => {
  //   const register = new Date(registerDate);
  //   let expiry = new Date(register);

  //   if (frequency === 'Month') {
  //     expiry.setMonth(expiry.getMonth() + 1);
  //   } else if (frequency === 'Quarterly') {
  //     expiry.setMonth(expiry.getMonth() + 3);
  //   } else if (frequency === '2-Month') {
  //     expiry.setMonth(expiry.getMonth() + 2);
  //   } else if (frequency === 'yearly') {
  //     expiry.setFullYear(expiry.getFullYear() + 1);
  //   }

  //   const year = expiry.getFullYear();
  //   const month = (expiry.getMonth() + 1).toString().padStart(2, '0');
  //   const day = expiry.getDate().toString().padStart(2, '0');

  //   return `${year}/${month}/${day}`;
  // };

  const getNextRenewalDate = (expiryDate) => {
    const expiry = new Date(expiryDate);
    expiry.setDate(expiry.getDate() + 1);

    const year = expiry.getFullYear();
    const month = (expiry.getMonth() + 1).toString().padStart(2, '0');
    const day = expiry.getDate().toString().padStart(2, '0');
    const nextRenewalDate = `${day}/${month}/${year}`;

    const today = new Date();
    const diffTime = expiry - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays <= 10 && diffDays > 0) {
      return `${diffDays} days left in your plan`;
    } else if (diffDays === 0) {
      return 'Your plan is expiring today';
    } else if (diffDays < 0) {
      return 'Your plan has expired';
    }

    return nextRenewalDate;
  };
  // Update the current page for pagination.
  const onPageChange = (newPage) => {
    setPage(newPage);
    getOrgData(search, newPage);
  };

  // Update the number of rows per page for pagination.
  const OnRowPerPage = (newRowsPerPage) => {
    setRowsPerPage(newRowsPerPage);
    setPage(1);
    getOrgData(search, 1, newRowsPerPage);
  };

  useEffect(() => {
    getOrgData('', 1);
  }, []);
  return (
    <Box className="org-list-wrap">
      <Box className="org-header">
        {/* <Box className="header-wrap">
          <Typography variant="h4" className="header-text p16 fw600">
            Organizations List
          </Typography>
          <Typography component="p" className="header-sub-text p14">
            Quickly browse, filter, and manage your organizations
          </Typography>
        </Box> */}
        <Box />
        <Box className="d-flex gap-5 filter-input-wrap justify-end">
          <CustomTextField
            InputLabelProps={{ shrink: true }}
            size="small"
            placeholder="Search by organization name"
            value={search}
            onChange={(e) => setSearch(e?.target?.value)}
            className="filter-input"
            variant="filled"
          />
          <CustomButton
            className="p16 search-btn"
            type="submit"
            fontWeight="600"
            variant="contained"
            background="#39596e"
            backgroundhover="#39596e"
            colorhover="#FFFFFF"
            leftIcon={<SearchIcon className="search-icon" />}
            title="search"
            onClick={() => {
              getOrgData(search, 1);
            }}
          />
        </Box>
      </Box>

      <Box className="divider" />
      {orgData && orgData?.length > 0 ? (
        <Box className="org-list-table-wrap">
          <Table className="org-table-wrap">
            <TableHead>
              <TableRow>
                <TableCell className="id-wrap p14 fw600">ID</TableCell>
                <TableCell className="org-name p14 fw600">Name</TableCell>
                <TableCell className="reg-date p14 fw600">
                  Register Date
                </TableCell>
                <TableCell className="org-status p14 fw600">
                  Org Status
                </TableCell>
                <TableCell className="plan-type p14 fw600">Plan Type</TableCell>
                <TableCell className="org-current-plan p14 fw600">
                  Current Plan
                </TableCell>
                <TableCell className="reg-date p14 fw600">
                  Payment Type
                </TableCell>
                <TableCell align="center" className="payment-status p14 fw600">
                  Payment Status
                </TableCell>
                <TableCell className="next-renew-date p14 fw600">
                  Next Renewal
                </TableCell>
                <TableCell align="center" className="action p14 fw600">
                  Action
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody className="table-body-wrap">
              {orgData &&
                orgData?.length > 0 &&
                orgData?.map((org, index) => (
                  <TableRow key={index}>
                    <TableCell className="p14">{org?.user_id}</TableCell>
                    <TableCell className="org-name">
                      <Box className="user-wrap d-flex align-center gap-5">
                        {org?.organization_logo &&
                        org?.organization_logo !== '""' ? (
                          <>
                            <HeaderImage
                              imageUrl={org?.organization_logo}
                              height={100}
                              width={100}
                              alt="not found"
                              className="image-wrap"
                              type="lazyload"
                            />
                            {/* <Image
                            src={org?.organization_logo}
                            alt={org?.name}
                            height={100}
                            width={100}
                            className="image-wrap"
                          /> */}
                          </>
                        ) : (
                          <Avatar
                            className={`avatar ${
                              org?.status?.toLowerCase().replace(' ', '-') ||
                              'default'
                            }`}
                          >
                            {org?.name.slice(0, 2).toUpperCase()}
                          </Avatar>
                        )}
                        <Box>
                          <Box className="org-name-wrap p14 fw600">
                            {org?.name}
                          </Box>
                          <Box className="p12 fw400">{org?.email}</Box>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell className="p14">
                      {formatDate(org?.registerDate)}
                    </TableCell>
                    <TableCell>
                      <Box
                        className={`org-status p14 d-flex align-center gap-5 ${org?.organization_status
                          ?.toLowerCase()
                          .replace(' ', '-')}`}
                      >
                        <span className="org-status-icon"></span>{' '}
                        {org?.organization_status === 'inactive'
                          ? 'In-active'
                          : org?.organization_status}
                      </Box>
                    </TableCell>
                    <TableCell className="plan-type p14">
                      {org?.subscription?.subs_plan_category} <br />
                      {org?.subscription?.subs_plan_type && (
                        <span className="plan-sub-type">
                          {org?.subscription?.subs_plan_type === 'one_time'
                            ? 'One Time'
                            : org?.subscription?.subs_plan_type}
                        </span>
                      )}
                    </TableCell>
                    <TableCell>
                      {org?.subscription?.plan_name ? (
                        <Box className="plan-name p14">
                          {org?.subscription?.plan_name}
                        </Box>
                      ) : (
                        ''
                      )}
                      {org?.subscription?.plan_price &&
                      org?.subscription?.plan_duration ? (
                        <Box className="sub-price p14">{`${currency} ${org?.subscription?.plan_price} ${org?.subscription?.plan_days > 0 && `/ ${formatDuration(org?.subscription?.plan_days)}`}`}</Box>
                      ) : (
                        ''
                      )}

                      {(org?.subscription?.subs_limit_min ||
                        org?.subscription?.subs_limit_min === 0) &&
                      (org?.subscription?.subs_limit_max ||
                        org?.subscription?.subs_limit_max === 0) ? (
                        <Box className="sub-emp p14">
                          {org?.subscription?.subs_limit_min +
                            ' - ' +
                            org?.subscription?.subs_limit_max}{' '}
                          {org?.subscription?.subs_plan_category === 'core'
                            ? 'EMP'
                            : org?.subscription?.sub_storage_size}
                        </Box>
                      ) : (
                        ''
                      )}
                    </TableCell>
                    <TableCell>
                      <Box className="sub-payment-source p14 text-capital text-align-start">
                        {org?.subscription?.plan_payment_type}
                      </Box>
                    </TableCell>
                    {/* <TableCell>{org.paymentStatus}</TableCell> */}
                    <TableCell align="center">
                      <Box className="d-flex justify-center">
                        <Typography
                          className={`max-content text-capital p12 fw600 ${
                            org?.payment_status === 'active'
                              ? 'active-onboarding'
                              : org?.payment_status === 'paused'
                                ? 'cancelled'
                                : org?.payment_status === 'pending'
                                  ? 'ongoing'
                                  : 'failed'
                          }`}
                        >
                          {org?.payment_status === 'inactive'
                            ? 'In-Active'
                            : org?.payment_status}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell className="next-renew-date">
                      {getNextRenewalDate(org?.plan_end_date)}
                    </TableCell>
                    <TableCell align="center">
                      <Tooltip
                        title={<Typography>Edit</Typography>}
                        arrow
                        placement="bottom"
                        classes={{ tooltip: 'info-tooltip-container' }}
                      >
                        <EditIcon
                          className="edit-icon"
                          onClick={() =>
                            router.push(
                              `/sorg/organization/${org?.organization_id}`
                            )
                          }
                        />
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
          <Box className="pb16 pl8 pr8">
            <CustomPagination
              currentPage={page}
              OnRowPerPage={OnRowPerPage}
              totalCount={totalCount}
              rowsPerPage={rowsPerPage}
              onPageChange={onPageChange}
            />
          </Box>
        </Box>
      ) : (
        <Box className="mt32">
          <Typography className="text-align h6 color-gray">
            No data found
          </Typography>
        </Box>
      )}
    </Box>
  );
}
