import React, { useState, useMemo, useEffect } from 'react';
import { Tooltip, Typography } from '@mui/material';
import Icon from '@/components/UI/AppIcon/AppIcon';
import './FormProgressIndicator.scss';

const FormProgressIndicator = ({ formState, validationErrors = {} }) => {
  const [currentSection, setCurrentSection] = useState('');

  // Sync currentSection with formState.activeSection when it changes
  useEffect(() => {
    if (
      formState?.activeSection &&
      formState.activeSection !== currentSection
    ) {
      setCurrentSection(formState.activeSection);
    }
  }, [formState?.activeSection]);

  // Function to check if a section is completed based on formState
  const isSectionCompleted = (sectionId) => {
    if (!formState) return false;

    switch (sectionId) {
      case 'basic-info':
        return !!formState.basicInfo?.recipeName?.trim();

      case 'media':
        return !!formState.media?.mainImage;

      case 'ingredients':
        return !!(formState.ingredients?.length > 0);

      case 'instructions':
        return !!(formState.instructions?.length > 0);

      case 'nutrition':
        // Nutrition is optional, consider completed if any nutrition data is provided
        return !!(
          formState.nutrition?.nutrition?.length > 0 ||
          formState.nutrition?.commonAllergens?.length > 0 ||
          formState.nutrition?.mayContainAllergens?.length > 0
        );

      case 'serving':
        return !!(
          formState.serving?.yield?.value > 0 &&
          formState.serving?.totalPortions > 0
        );

      case 'haccp':
        // HACCP is optional, consider completed if any HACCP data is provided
        return !!(
          formState.haccp?.sections && formState.haccp.sections.length > 0
        );

      default:
        return false;
    }
  };

  // Function to check if a section has errors based on validationErrors
  const sectionHasErrors = (sectionId) => {
    if (!validationErrors) return false;

    switch (sectionId) {
      case 'basic-info':
        return !!(
          validationErrors.recipeName ||
          validationErrors.publicDisplayName ||
          validationErrors.recipeDescription ||
          validationErrors.categories ||
          validationErrors.prepTime ||
          validationErrors.cookTime
        );

      case 'media':
        return !!(
          validationErrors.mainImage ||
          validationErrors.additionalImages ||
          validationErrors.documents ||
          validationErrors.audio ||
          validationErrors.links
        );

      case 'ingredients':
        return !!validationErrors.ingredients;

      case 'instructions':
        return !!validationErrors.instructions;

      case 'nutrition':
        return !!(validationErrors.nutrition || validationErrors.allergens);

      case 'serving':
        return !!(
          validationErrors.serving ||
          validationErrors.yield ||
          validationErrors.totalPortions
        );

      case 'haccp':
        return !!validationErrors.haccp;

      default:
        return false;
    }
  };

  // Dynamic form sections with computed completion and error states
  const formSections = useMemo(
    () => [
      {
        id: 'basic-info',
        label: 'Basic Info',
        shortLabel: 'Basic',
        icon: 'FileText',
        completed: isSectionCompleted('basic-info'),
        hasErrors: sectionHasErrors('basic-info'),
      },
      {
        id: 'media',
        label: 'Media & Photos',
        shortLabel: 'Media',
        icon: 'Camera',
        completed: isSectionCompleted('media'),
        hasErrors: sectionHasErrors('media'),
      },
      {
        id: 'ingredients',
        label: 'Ingredients',
        shortLabel: 'Ingredients',
        icon: 'ShoppingCart',
        completed: isSectionCompleted('ingredients'),
        hasErrors: sectionHasErrors('ingredients'),
      },
      {
        id: 'nutrition',
        label: 'Nutrition',
        shortLabel: 'Nutrition',
        icon: 'Heart',
        completed: isSectionCompleted('nutrition'),
        hasErrors: sectionHasErrors('nutrition'),
      },
      {
        id: 'instructions',
        label: 'Instructions',
        shortLabel: 'Steps',
        icon: 'List',
        completed: isSectionCompleted('instructions'),
        hasErrors: sectionHasErrors('instructions'),
      },
      {
        id: 'serving',
        label: 'Serving',
        shortLabel: 'Serving',
        icon: 'Users',
        completed: isSectionCompleted('serving'),
        hasErrors: sectionHasErrors('serving'),
      },
      {
        id: 'haccp',
        label: 'HACCP',
        shortLabel: 'HACCP',
        icon: 'BadgeAlert',
        completed: isSectionCompleted('haccp'),
        hasErrors: sectionHasErrors('haccp'),
      },
    ],
    [formState, validationErrors]
  );

  const completedSections =
    formSections?.filter((section) => section?.completed)?.length || 0;
  const totalSections = formSections?.length || 0;
  const progressPercentage =
    totalSections > 0 ? (completedSections / totalSections) * 100 : 0;

  // const handleSectionClick = (sectionId) => {
  //   setCurrentSection(sectionId);
  // };

  const getSectionStatus = (section) => {
    if (section?.hasErrors) return 'error';
    if (section?.completed) return 'completed';
    if (section?.id === currentSection) return 'current';
    return 'pending';
  };

  const getSectionStyles = (status) => {
    switch (status) {
      case 'completed':
        return 'form-progress-indicator__section-button--completed';
      case 'current':
        return 'form-progress-indicator__section-button--current';
      case 'error':
        return 'form-progress-indicator__section-button--error';
      default:
        return 'form-progress-indicator__section-button--pending';
    }
  };

  const getMobileDotStyles = (status) => {
    switch (status) {
      case 'completed':
        return 'form-progress-indicator__mobile-dot--completed';
      case 'current':
        return 'form-progress-indicator__mobile-dot--current';
      case 'error':
        return 'form-progress-indicator__mobile-dot--error';
      default:
        return 'form-progress-indicator__mobile-dot--pending';
    }
  };

  return (
    <div className="form-progress-indicator">
      {/* Progress Bar */}
      <div className="form-progress-indicator__progress-section">
        <div className="form-progress-indicator__progress-header">
          <span className="form-progress-indicator__progress-title">
            Recipe Creation Progress
          </span>
          <span className="form-progress-indicator__progress-count">
            {completedSections}/{totalSections} sections completed
          </span>
        </div>
        <div className="form-progress-indicator__progress-bar">
          <div
            className="form-progress-indicator__progress-fill"
            style={{ width: `${progressPercentage}%` }}
          ></div>
        </div>
      </div>

      {/* Desktop: Horizontal Section Navigation */}
      <div className="form-progress-indicator__desktop-nav">
        {formSections?.map((section) => {
          const status = getSectionStatus(section);
          return (
            <button
              key={section?.id}
              // onClick={() => handleSectionClick(section?.id)}
              className={`form-progress-indicator__section-button ${getSectionStyles(status)}`}
            >
              <Tooltip
                title={
                  <Typography className="sub-title-text">{`${section?.label} - ${status === 'completed' ? 'Completed' : status === 'error' ? 'Has errors' : 'In progress'}`}</Typography>
                }
                arrow
                classes={{
                  tooltip: 'info-tooltip-container',
                }}
              >
                <div className="form-progress-indicator__section-content">
                  {status === 'completed' ? (
                    <Icon name="CheckCircle" size={16} color="currentColor" />
                  ) : status === 'error' ? (
                    <Icon name="AlertCircle" size={16} color="currentColor" />
                  ) : (
                    <Icon name={section?.icon} size={16} color="currentColor" />
                  )}
                  <span className="form-progress-indicator__section-label">
                    {section?.label}
                  </span>
                </div>
              </Tooltip>
            </button>
          );
        })}
      </div>

      {/* Mobile: Simplified Progress Dots with Current Section */}
      <div className="form-progress-indicator__mobile-nav">
        <div className="form-progress-indicator__mobile-header">
          <div className="form-progress-indicator__mobile-current">
            <Icon
              name={formSections?.find((s) => s?.id === currentSection)?.icon}
              size={16}
              color="currentColor"
            />
            <span className="form-progress-indicator__mobile-title">
              {formSections?.find((s) => s?.id === currentSection)?.label}
            </span>
          </div>
          <span className="form-progress-indicator__mobile-counter">
            {(formSections?.findIndex((s) => s?.id === currentSection) ?? -1) +
              1}
            /{totalSections}
          </span>
        </div>

        <div className="form-progress-indicator__mobile-dots">
          {formSections?.map((section) => {
            const status = getSectionStatus(section);
            return (
              <button
                key={section?.id}
                // onClick={() => handleSectionClick(section?.id)}
                className={`form-progress-indicator__mobile-dot ${getMobileDotStyles(status)}`}
                title={section?.label}
              />
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default FormProgressIndicator;
