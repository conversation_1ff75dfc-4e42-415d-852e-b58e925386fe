.dsr-page-section {
  background-color: var(--color-white);
  padding: var(--spacing-xxl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-xs);
  position: relative;
  min-height: 100%;
  @media (max-width: 1200px) {
    .folder-desc-divider {
      margin: 16px 0 !important;
    }
  }
  .refresh-icon {
    position: absolute;
    right: 20px;
    width: 33px;
    height: 33px;
    cursor: pointer;
    top: 14px;
  }
  .p0 {
    padding: 0 !important;
  }
  .dsr-tabs {
    .tab-list-sec {
      border-bottom: var(--normal-sec-border);
      .tab-name {
        text-transform: none;
        font-size: var(--font-size-base);
        font-family: var(--font-family-primary);
        font-weight: var(--font-weight-regular);
        line-height: var(--line-height-base);
        color: var(--color-black);
        opacity: 1;
        width: 50%;
        text-transform: uppercase;
      }
      .MuiTabs-indicator {
        background-color: var(--color-primary);
        height: 3px;
      }
      .Mui-selected {
        color: var(--text-color-primary);
        // background-color: var( --color-primary);
        // border-radius: 20px;
      }
    }
  }

  .dsr-search-section {
    display: flex;
    column-gap: 25px;
    align-items: center;
    // .filter-icon {
    //   border: 1px solid var(--color-black);
    //   border-radius: 3px;
    //   width: 25px;
    //   height: 25px;
    //   cursor: pointer;
    // }
    // .dsr-search {
    //   max-width: calc(100% - 50px - 124px - 50px - 132px) !important;
    // }
    // @media (max-width: 599px) {
    //   flex-direction: column;
    //   row-gap: 15px;
    //   .dsr-search,
    //   button {
    //     width: 100% !important;
    //     max-width: 100% !important;
    //   }
    // }
  }
  // .dsr-search-section.dsr-permission {
  //   .dsr-search {
  //     max-width: calc(100% - 50px - 124px - 25px) !important;
  //   }
  //   @media (max-width: 599px) {
  //     flex-direction: column;
  //     row-gap: 15px;
  //     .dsr-search,
  //     button {
  //       width: 100% !important;
  //       max-width: 100% !important;
  //     }
  //   }
  // }
  .payroll-search-section {
    .dsr-search {
      max-width: calc(100% - 50px - 124px - 50px - 160px) !important;
    }
    @media (max-width: 599px) {
      flex-direction: column;
      row-gap: 15px;
      .dsr-search,
      button {
        width: 100% !important;
        max-width: 100% !important;
      }
    }
  }
  .payroll-search-section.dsr-search-section.dsr-permission {
    .dsr-search {
      max-width: calc(100% - 50px - 124px - 25px) !important;
    }
  }
  .DSR-table {
    position: relative;
    border: var(--table-border);
    .dsr-table-container {
      margin-left: 150px;
      margin-right: 120px;
      width: auto !important;
      box-shadow: none;
      .dsr-table {
        .dsr-table-head {
          p {
            font-weight: 600;
          }
          .dsr-cell {
            padding: 10px 5px;
            background-color: var(--color-secondary);
            font-weight: var(--font-weight-semibold);
            // color: var(--color-white);
          }
          .amount-field p {
            font-weight: var(--font-weight-medium) !important;
          }
          .branch-name {
            border-bottom: var(--table-border);
            padding-bottom: 10px;
          }
          .total-amounts {
            padding-top: 10px;
          }
          .date-fixed div,
          .total-day-fixed div {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
          }
        }
        .dsr-row {
        }
        .dsr-cell {
          padding-left: 5px;
          padding-right: 5px;
          text-align: center;
          p {
            font-size: var(--font-size-base);
            font-family: var(--font-family-primary);
            font-weight: var(--font-weight-regular);
            line-height: var(--line-height-base);
            text-overflow: ellipsis;
            overflow: hidden;
            word-break: break-all;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
          }
        }
        // .id-fixed {
        //   position: absolute;
        //   min-width: 50px;
        //   left: 0px;
        //   top: auto;
        // }
        .date-fixed {
          position: absolute;
          min-width: 150px;
          left: 0px;
          top: auto;
          // text-align: start;
          padding-left: 12px !important;
          background-color: var(--color-secondary);
          border-right: var(--table-border);
          // color: var(--color-white);
        }
        .total-day {
          background-color: var(--color-secondary);
        }
        .total-date-extra {
          height: 83.5px;
        }
        .total-branch-extra {
          height: 83.5px;
        }
        .total-day-fixed {
          position: absolute;
          min-width: 120px;
          right: 0px;
          top: auto;
          background-color: var(--color-secondary);
          border-left: var(--table-border);
          // color: var(--color-white);
        }
        // .action-fixed {
        //   position: absolute;
        //   min-width: 100px;
        //   right: 0px;
        //   top: auto;
        //   padding-top: 17px;
        // }
        .branch-cell {
          min-width: 100px;
        }
        .amount-cell {
          min-width: 120px;
        }
        .submited-by-cell {
          min-width: 200px;
        }
        .status-cell {
          min-width: 100px;
        }
        .total-id {
          height: calc(21px + 16px + 16px);
        }
        .total-date {
          height: calc(21px + 16px + 16px);
        }
        .total-actions {
          height: calc(21px + 16px + 16px);
        }
      }
    }
  }
  .display-grid {
    display: grid;
    grid-template-columns: 31% 31% 31%;
    column-gap: 32px;
    row-gap: 32px;
    @media (max-width: 1200px) {
      grid-template-columns: 48% 48%;
      column-gap: 12px;
      row-gap: 32px;
    }
    @media (max-width: 768px) {
      grid-template-columns: 100%;
    }
  }
  .create-cancel-button {
    svg {
      fill: var(--color-black) !important;
    }
    button:hover {
      svg {
        fill: var(--color-white) !important;
      }
    }
  }
  .dsr-view-filter {
    .day-filter-box {
      fieldset {
        margin-top: 5px;
      }
      .MuiSvgIcon-root {
        top: 10px;
      }
      .MuiSelect-select {
        padding-top: 10px;
      }
    }

    .dsr-filter {
      display: flex;
      column-gap: 25px;
      align-items: center;
      .filter-icon {
        border: 1px solid var(--color-black);
        border-radius: 3px;
        width: 25px;
        height: 25px;
        cursor: pointer;
      }
      @media (max-width: 768px) {
        display: grid;
        grid-template-columns: 100%;
        column-gap: 25px;
        row-gap: 25px;
        .filter-icon {
          margin: 0 auto;
        }
      }
    }
  }
  .DSR-filter-section {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    @media (max-width: 666px) {
      display: block;
    }
  }
  .dsr-next-previous {
    display: flex;
    display: flex;
    flex-direction: column-reverse;
    justify-content: flex-end;
    align-items: flex-end;
    row-gap: 12px;

    .week-button {
      // width: 135px;
      // padding: 7px 14px !important;
      width: 125px;
      padding: 7px !important;
    }
    .month-button {
      // width: 145px;
      // padding: 7px 14px !important;
      width: 125px;
      padding: 7px !important;
    }
    .select-box {
      width: 25%;
      @media (max-width: 1200px) {
        width: 30%;
      }
      @media (max-width: 1048px) {
        width: 40%;
      }
    }
    @media (max-width: 768px) {
      // display: block;
      .select-box {
        margin-top: 20px;
        width: 100%;
        max-width: 400px;
      }

      .table-graph-view {
        // margin-top: 15px;
        justify-content: flex-end;
      }
    }
    @media (max-width: 468px) {
      .create-cancel-button {
        display: block;
        width: 100%;
      }
      .week-button,
      .month-button {
        width: 100%;
      }
      .secondary-button:last-child {
        margin-top: 20px;
      }
      .table-graph-view {
        // display: block;
        margin-top: 15px;
        width: 100%;
        .icon-sec {
          width: 100%;
        }
      }
    }
  }
}
.day-filter {
  fieldset {
    height: 52px;
    margin-top: 5px;
  }
  .MuiSelect-select {
    padding: 10px 7px 13px 15px;
  }
}
.export-icon {
  svg {
    fill: var(--color-black) !important;
    width: 21px;
    height: 18px;
    margin-right: 5px;
    margin-bottom: 2px;
  }
}
.export-icon:hover {
  svg {
    fill: var(--color-white) !important;
  }
}

.dsr-view-filter {
  display: flex !important;

  column-gap: 14px !important;
  row-gap: 20px !important;
  flex-wrap: wrap !important;

  .custom-button {
    padding: 9px 12px !important;
  }
  .textfeild {
    width: 160px;
  }
  .day-filter-box {
    width: 120px;
  }
  .multiselect-dsr {
    width: 220px;
  }
  .day-filter-box {
    .select-wrap fieldset {
      height: 40px;
    }
    .MuiSelect-select {
      padding: 7px 7px 7px 15px;
      padding-top: 7px !important;
    }
    .MuiSvgIcon-root {
      top: 7px !important;
    }
  }
  .date-wrap {
    height: 57px;
    fieldset {
      height: 42px;
    }
    .MuiInputBase-input {
      padding: 4px 2px 4px 10px;
    }
    .MuiButtonBase-root {
      padding: 0 8px;
    }
  }
  .multiselect-dsr {
    .select__control {
      padding: 2px !important;
      .select__value-container {
        padding: 0px 6px;
        max-height: 36px;
      }
    }
  }
  @media (max-width: 1200px) {
    display: flex;
    flex-wrap: wrap;
    column-gap: 0;
    row-gap: 24px !important;
    // justify-content: space-between;
    .textfeild,
    .day-filter,
    .select-box {
      width: 32%;
    }
    .dsr-filter {
      width: 100%;
    }
  }
  @media (max-width: 830px) {
    display: flex;
    flex-wrap: wrap;
    column-gap: 15px;
    // justify-content: space-between;
    // row-gap: 24px;
    .textfeild,
    .day-filter,
    .select-box {
      width: calc(50% - 12px);
    }
    .dsr-filter {
      display: flex !important;
      .filter-icon {
        margin: 0 !important;
      }
    }
  }
  @media (max-width: 540px) {
    display: flex;
    flex-wrap: wrap;
    column-gap: 15px;
    justify-content: space-between;
    row-gap: 25px;
    .textfeild,
    .day-filter,
    .select-box {
      width: 100%;
    }
    .dsr-filter {
      display: grid !important;
      align-items: center;
      width: 100%;
      .filter-icon {
        margin: 0 auto !important;
      }
    }
  }
}
.pr20 {
  padding-right: 20px;
}
.actions-dsr {
  gap: 10px;
  svg {
    width: 20px;
    height: 20px;
  }
  .icon-tabler-calendar-time {
    fill: none !important;
    stroke: var(--color-black);
  }
  .edit-icon {
    width: 18px;
    height: 18px;
  }
  .svg-icon {
    width: 20px;
    height: 20px;
    display: flex;
    margin-left: 5px;
  }
}
.dsr-action {
  padding: 6px 0;
}
.DSR-details-section {
  background-color: var(--color-white);
  padding: 26px 40px 56px;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-xs);

  @media (max-width: 1200px) {
    padding: 24px 30px;
    .folder-desc-divider {
      margin: 16px 0 !important;
    }
  }
  @media (max-width: 480px) {
    padding: 24px 15px;
  }
}
// .dsr-filter-button {
//   display: flex;
//   column-gap: 10px;
//   margin-top: 24px;
// }
.export-popover {
  .MuiPaper-root {
    width: 120px !important;
    margin-top: 8px;
    padding: 10px 20px;
  }

  .export-option {
    p {
      text-align: center;
    }
  }
}
.table-graph-view {
  display: flex;
  .icon-sec {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 130px;
    height: 35px;
    border: 1px solid var(--color-primary);
    border-collapse: collapse;
    color: var(--text-color-primary);
    cursor: pointer;
    svg {
      margin-right: 6px;
    }
  }
  .graph {
    border-radius: 0 5px 5px 0;
  }
  .table {
    border-radius: 5px 0 0 5px;
  }
  svg {
    width: 24px !important;
    height: 24px !important;
    fill: none !important;
  }
  .active-svg {
    background-color: var(--color-primary);
    color: var(--color-white);
    svg {
      stroke: var(--color-white);
    }
  }
}

.Unvisible-branch {
  opacity: 0.5;
}
.Add-dsr-section {
  // .display-grid-branch {
  //   display: grid;
  //   grid-template-columns: 31% 31% 31%;
  //   column-gap: 32px;
  //   row-gap: 32px;
  //   @media (max-width: 1200px) {
  //     grid-template-columns: 48% 48%;
  //     column-gap: 12px;
  //     row-gap: 32px;
  //   }
  //   @media (max-width: 768px) {
  //     grid-template-columns: 100%;
  //   }
  // }
  .display-grid-branch {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(0, 250px));
    gap: var(--spacing-lg);
    align-items: flex-start;
    width: 100%;
  }
  // .edit-dsr {
  //   margin-top: 8px;
  //   .date-picker-textfield {
  //     max-width: 200px;
  //   }
  // }
  // .edit-payroll {
  //   margin-top: 8px;
  //   .date-picker-textfield {
  //     max-width: 200px;
  //   }
  // }
  // .edit-wsr {
  //   margin-top: 16px;
  //   .date-picker-textfield {
  //     max-width: 300px;
  //   }
  // }
  .display-grid-card {
    display: grid;
    grid-template-columns: 38% 38% 13.5%;
    justify-content: space-between;
    align-items: flex-end;
    column-gap: 22px;
    row-gap: 30px;
    @media (max-width: 1200px) {
      grid-template-columns: 38% 38% 15.5%;
      column-gap: 12px;
      row-gap: 12px;
    }
    @media (max-width: 768px) {
      grid-template-columns: 100%;
      row-gap: 32px;
      button {
        width: 100%;
      }
    }
  }
  .edit-grid-card {
    max-width: 650px;
    grid-template-columns: 44% 40% 10% !important;

    @media (max-width: 768px) {
      grid-template-columns: calc(50% - 9px - 15px) calc(50% - 9px - 15px) 18px !important;
      row-gap: 15px;
    }
    @media (max-width: 480px) {
      grid-template-columns: 100% !important;
      row-gap: 15px;
    }
  }
  .card-grid-card {
    max-width: 650px;
    grid-template-columns: 36% 36% 17.5%;
    @media (max-width: 1040px) {
      grid-template-columns: 38% 38% 15.5%;
      row-gap: 15px;
    }
    @media (max-width: 768px) {
      grid-template-columns: 38% 38% 15.5%;
      row-gap: 15px;
    }
    @media (max-width: 680px) {
      grid-template-columns: 100% !important;
      row-gap: 32px;
    }
  }

  .total-card {
    @media (max-width: 480px) {
      display: flex;
      justify-content: space-between;
    }
  }
  .border-top {
    border-top: 1px solid var(--color-muted);
    padding-top: 10px;
    width: 100%;
  }
  .dsr-add-view {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    grid-gap: 15px;
    height: 100%;
    @media (max-width: 540px) {
      display: grid;
      grid-template-columns: 100%;
      grid-gap: 15px;
    }
    .card-details-section {
      display: grid;
      grid-template-columns: 40% 50%;
      justify-content: space-between;
      align-items: flex-start;
      column-gap: 15px;

      .amount-field {
        // display: flex;
        // align-items: center;
        .amount-textfield {
          width: 100%;
          .MuiInputBase-root {
            min-height: 32px;
            padding-left: 8px;
            background-color: var(--color-white) !important;
          }
          .MuiInputAdornment-root {
            margin-top: 0;
            .currency {
              color: var(--color-black);
            }
          }
          .MuiInputBase-input {
            padding: 2px 5px 2px 2px;
          }
          .Mui-disabled {
            .MuiInputAdornment-root {
              cursor: not-allowed !important;
            }
          }
        }
      }
      svg {
        width: 18px;
        height: 18px;
        margin-top: 7px;
        @media (max-width: 768px) {
          position: absolute;
          top: 0;
          right: 0;
        }
      }
    }
    .old-value {
      .symbol {
        padding-left: 0px;
        // padding-left: 10px;
        padding-right: 7px;
      }
      p {
        // color: var(--color-black);
        .symbol {
          // text-decoration: none !important;
        }
        span {
          // padding-left: 10px;
          // text-decoration: line-through;
        }
      }
    }
    .new-value {
      .symbol {
        padding-right: 7px;
      }
    }
    .card-details-remark-section {
      display: block;
      .amount-field {
        padding-top: 4px;
      }
    }
    .dsr-main-category {
      margin-top: 16px;
      border: var(--normal-sec-border);
      padding: 16px;
      position: relative;
      .main-category-text {
        text-decoration: underline;
        text-underline-offset: 4px;
        text-align: center;
      }
      .income-text {
        color: var(--text-green);
      }
      .expense-text {
        color: var(--text-color-primary);
      }
      .other-text {
        color: var(--color-light-success);
      }
      .info-icon-wrap {
        height: 18px;
        width: 18px;
        margin-top: 0px;
        fill: var(--color-primary);
      }
      .category-status {
        // min-width: 75px;
        position: absolute;
        top: 3px;
        right: -10px;
        transform: rotate(30deg);
        margin-left: 0;
        min-width: 0 !important;

        .category-draft {
          padding: 1.5px 8px;
          border-radius: 3px;
          background-color: var(--color-primary);
          color: var(--text-color-white);
        }
        .category-ongoing {
          // border: 1px solid  var(--color-light-success);
          padding: 1.5px 8px;
          border-radius: 3px;
          color: var(--text-color-white);
          background-color: var(--color-light-success);
        }
        .category-accepted {
          // border: 1px solid var(--color-green);
          padding: 1.5px 8px;
          border-radius: 3px;
          color: var(--text-color-white);
          background-color: var(--color-green);
        }
      }
      .category-status.income {
        right: -9px;
        margin-left: 0;
      }
      .category-status.other {
        right: -6px;
        margin-left: 0;
      }
    }
    .totol-dsr {
      border-top: var(--normal-sec-border);
      margin-top: 24px;
    }
    .total-amounts {
      justify-content: center;
      .amount-field {
        margin-right: 5px;
        min-width: 100px;
        width: 100%;
        justify-content: center;
      }
    }
  }

  .vat-calculation-section {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    grid-gap: 15px;
    align-items: center;
    @media (max-width: 540px) {
      display: grid;
      grid-template-columns: 100%;
      grid-gap: 15px;
    }

    .vat-calculation {
      margin-top: 16px;
      border: var(--normal-sec-border);
      padding: 16px;
      .vat-text {
        text-align: center;
        color: var(--color-dark-blue);
      }

      .card-details-section {
        display: grid;
        grid-template-columns: 40% 50%;
        justify-content: space-between;
        align-items: center;
        column-gap: 15px;
        .difference-grid {
          display: grid;
          grid-template-columns: calc(50% - 4px) calc(50% - 4px);
          grid-gap: 8px;
        }
        .amount-field {
          // display: flex;
          // align-items: center;
          .amount-textfield {
            width: 100%;
            .MuiInputBase-root {
              min-height: 32px;
              padding-left: 8px;
              background-color: var(--color-white) !important;
            }
            .MuiInputAdornment-root {
              margin-top: 0;
              .currency {
                color: var(--color-black);
              }
            }
            .MuiInputBase-input {
              padding: 2px 5px 2px 2px;
            }
            .Mui-disabled {
              .MuiInputAdornment-root {
                cursor: not-allowed !important;
              }
            }
          }
        }
        svg {
          width: 18px;
          height: 18px;
          margin-top: 7px;
          @media (max-width: 768px) {
            position: absolute;
            top: 0;
            right: 0;
          }
        }
      }
      .card-request-sec {
        .amount-field {
          .amount-textfield {
            .MuiInputBase-root {
              background-color: var(--action-icon-bg-color) !important;
            }
          }
        }
      }
    }
    .old-value {
      .symbol {
        padding-left: 0px;
        padding-right: 7px;
      }
    }
    .new-value {
      .symbol {
        padding-right: 7px;
      }
      p {
        color: var(--text-green);
      }
    }
    .old-diff-value.right-grid p {
      text-align: end;
    }
  }
  .dsr-request-view,
  .vat-calculation-req-section {
    .card-request-sec {
      align-items: center;
      background-color: var(--action-icon-bg-color);
      padding: 7px 10px;
      margin-bottom: 7px;
      margin-top: 7px;
      border-radius: 10px;
      .amount-field {
        display: block !important;
        text-align: end;
        .MuiInputBase-root {
          min-height: 20px !important;
        }
        input {
          text-align: end;
          &::placeholder {
            display: none;
          }
        }
        .new {
          padding-left: 10px;
        }
        .amount-textfield {
          padding: 0;
          .MuiInputBase-root {
            border: 0 !important;
            background-color: var(--action-icon-bg-color) !important;
          }
          .Mui-disabled {
            background-color: var(--action-icon-bg-color) !important;
          }
        }
      }
      .vat-calculation {
        .amount-field {
          display: block !important;
          .amount-textfield {
            .MuiInputBase-root {
              border: 0 !important;
              background-color: var(--action-icon-bg-color) !important;
            }
            .Mui-disabled {
              background-color: var(--action-icon-bg-color) !important;
            }
          }
        }
      }
    }
    .card-diff-request-sec {
      width: 100%;
      display: block !important;
      margin-top: 7px;
      .right-grid {
        input {
          text-align: end;
        }
      }
    }
    .old-value {
      p,
      span {
        font-family: Inter, sans-serif;
        font-size: 16px;
        line-height: 24px;
        letter-spacing: -0.5px;
        color: var(--color-black);
      }
    }
    .new-value {
      p {
        color: var(--text-green);
      }
      p,
      span {
        font-family: Inter, sans-serif;
        font-size: 16px;
        line-height: 24px;
        letter-spacing: -0.5px;
        color: var(--text-green);
      }
    }
  }
}

.form-checkbox {
  display: flex;

  .check-box-form {
    flex-direction: row-reverse;
    margin-left: 0;

    .MuiFormControlLabel-label {
      font-family: var(--font-family-primary);
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-regular);
      color: var(--text-color-black);
      line-height: var(--line-height-base);
    }
    .MuiCheckbox-root.Mui-checked {
      color: var(--color-primary);
    }
  }
  .form-row {
    flex-direction: row !important;
    .MuiButtonBase-root {
      padding-left: 0 !important;
    }
  }
  .approved-checkbox {
    color: var(--text-green);

    .MuiFormControlLabel-label {
      font-weight: 600;
    }

    .Mui-disabled.MuiTypography-root {
      color: var(--text-green) !important;
    }

    .MuiTypography-root {
      color: var(--text-green) !important;
    }

    svg {
      fill: var(--text-green) !important;
    }
  }

  .reject-checkbox {
    color: var(--color-danger);

    .MuiFormControlLabel-label {
      font-weight: 600;
    }

    .Mui-disabled.MuiTypography-root {
      color: var(--color-danger) !important;
    }

    .MuiTypography-root {
      color: var(--color-danger) !important;
    }

    svg {
      fill: var(--color-danger) !important;
    }
  }
  .Mui-disabled {
    opacity: 1;
    cursor: not-allowed !important;
    .MuiCheckbox-root.Mui-checked {
      color: var(--color-primary);
    }
    .MuiTypography-root {
      color: var(--color-black) !important;
    }
  }
}
