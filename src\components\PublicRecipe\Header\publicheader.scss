body {
  .public-recipe-header {
    background-color: var(--color-white);
    padding: var(--spacing-sm) var(--spacing-xxl);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    z-index: 1000;
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    .public-logo-wrap {
      width: calc(100% - 162px);
      display: flex;
      align-items: center;
      justify-content: center;
      height: 64px;
      transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
      .lazy-load-image-background {
        height: 100%;
      }
      img {
        height: 100%;
        transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
      }
      @media (max-width: 767px) {
        height: 50px;
      }
    }
    .public-menu-wrap {
      max-width: max-content;
      transition: all 0.3s ease-in-out;
      &:hover {
        .menu-text {
          color: var(--text-charcoal-gray);
        }
        .public-menu-icon {
          path {
            fill: var(--text-charcoal-gray);
          }
        }
      }
      .menu-text {
        transition: color 0.3s ease-in-out;
        &:hover {
          color: var(--text-charcoal-gray);
        }
      }
      .public-menu-icon {
        height: var(--icon-size-md);
        width: var(--icon-size-md);
        transition: all 0.3s ease-in-out;
        path {
          transition: fill 0.3s ease-in-out;
        }
      }
    }
    // .public-menu-wrap-hide {
    //   min-width: 81px;
    // }
  }

  .public-links-drawer {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    .links-container {
      padding: var(--spacing-xxl) var(--spacing-2xl) var(--spacing-none)
        var(--spacing-2xl);
      flex-grow: 1;
    }
    .contact-us-container {
      padding: var(--spacing-xxl) var(--spacing-2xl);
    }
    .link-item-text {
      &:hover {
        color: var(--text-charcoal-gray) !important;
      }
    }
    .link-item {
      margin-bottom: 18px;
    }
  }
  .public-menu-content {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
  }
}
