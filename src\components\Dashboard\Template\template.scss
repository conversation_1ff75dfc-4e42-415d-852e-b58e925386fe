.dashboard-page {
  background-color: var(--color-white);
  padding: var(--spacing-xxl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-xs);
  min-height: 100%;
  .dashboard-list {
    display: flex;
    justify-content: space-between;
    @media (max-width: 899px) {
      display: block !important;
    }
  }
  .template-text {
    color: var(--text-dark);
  }
  .dashboard-name {
    width: 100%;
    max-width: 300px;

    .MuiFormLabel-root {
      display: none;
    }
  }
  .dashboard-name-sec {
    width: calc(100% - 335px);
    @media (max-width: 899px) {
      width: calc(100%);
      margin-bottom: 8px;
    }
  }
  .default-dashboard-name-sec {
    width: calc(100% - 500px);
    @media (max-width: 768px) {
      width: calc(100%);
      margin-bottom: 8px;
    }
  }
  .default-dashboard {
    width: calc(100%);
    .dashboard-text {
      width: calc(100% - 24px);
    }
  }

  .blank-dashboard {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    margin-top: 24px;
    .graph-icon {
      width: 100px;
      height: 100px;
      // max-width: 40%;
      // max-height: 30%;
      opacity: 0.3;
      @media (max-width: 768px) {
        width: 100px;
        height: 100px;
      }
      @media (max-width: 599px) {
        width: 100px;
        height: 100px;
      }
    }
    .blank-text {
      color: var(--color-primary);
    }
  }
  .blank-dahsboard-temp {
    height: calc(100% - 100px);
  }
}
// .blanck-dashboard-page {
//   height: calc(100% - 10px);
// }
.dashboard-filter {
  display: flex;
  justify-content: space-between;
  margin-top: 24px;
  @media (max-width: 1399px) {
    display: block;
  }
  .subcategories-wrap {
    width: 100%;
    background-color: var(--color-white);
    // max-height: calc(100vh - 193px);
    // overflow: auto;
    display: flex;
    gap: 10px;
    padding-bottom: 2px;
    align-items: flex-start;
    padding-left: 0px !important;

    @media (max-width: 1399px) {
      display: grid;
      grid-template-columns: calc(33% - 7px) calc(33% - 7px) calc(33% - 7px);
      row-gap: 15px;
      margin-bottom: 8px;
    }
    @media (max-width: 1199px) {
      display: grid;
      grid-template-columns: calc(50% - 7px) calc(50% - 7px);
      row-gap: 15px;
    }
    @media (max-width: 768px) {
      display: grid;
      grid-template-columns: 100%;
      row-gap: 15px;
    }
  }
  .timperiod-filters {
    .custom-button-wrapper {
      button {
        border-radius: 0px !important;
      }
    }
    .custom-button-wrapper:first-child {
      button {
        border-radius: 5px 0 0 5px !important;
      }
    }
    .custom-button-wrapper:last-child {
      button {
        border-radius: 0px 5px 5px 0px !important;
      }
    }
  }
}
