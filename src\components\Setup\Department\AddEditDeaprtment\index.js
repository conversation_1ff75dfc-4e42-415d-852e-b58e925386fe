'use client';

import React, { useState } from 'react';
import { Box } from '@mui/material';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomButton from '@/components/UI/CustomButton';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import CustomSelect from '@/components/UI/CustomSelect';
import { identifiers } from '@/helper/constants/identifier';
import PreLoader from '@/components/UI/Loader';

export default function AddEditDeaprtment({
  isUpdate,
  updateItem,
  handleClose,
  getDepartmentList,
  searchValue,
  page,
  getDepartmentLists,
}) {
  const [loader, setLoader] = useState(false);

  return (
    <Box>
      {loader && <PreLoader />}
      <Formik
        initialValues={{
          departmentname: updateItem?.department_name || '',
          remark: updateItem?.department_remark || '',
          status: updateItem?.department_status || 'active',
        }}
        enableReinitialize={true}
        validationSchema={Yup.object().shape({
          departmentname: Yup.string()
            .trim()
            .required('This field is required'),
          status: Yup.string().trim().required('This field is required'),
        })}
        onSubmit={async (requestData) => {
          setLoader(true);
          let sendData = {
            department_name: requestData?.departmentname?.trim(),
            department_remark: requestData?.remark?.trim() || null,
            departmentStatus: requestData?.status || 'active',
          };

          // Validate update data
          if (isUpdate && !updateItem?.id) {
            setApiMessage('error', 'Invalid department ID for update');
            setLoader(false);
            return;
          }

          const ApiUrl = isUpdate
            ? URLS.UPDATE_DEPARTMENT + updateItem?.id
            : URLS.CREATE_DEPARTMENT;
          const method = isUpdate ? 'put' : 'post';

          try {
            const { status, data } = await axiosInstance[method](
              ApiUrl,
              sendData
            );

            if (status === 200 || status === 201) {
              if (data?.status) {
                setApiMessage('success', data?.message);
                getDepartmentList(searchValue, page, '', false);
                getDepartmentLists();
                handleClose();
              } else {
                setApiMessage(
                  'error',
                  data?.message || 'Failed to update department'
                );
              }
            } else {
              setApiMessage('error', 'Failed to update department');
            }
          } catch (error) {
            const errorMessage =
              error?.response?.data?.message ||
              'An error occurred while updating the department';
            setApiMessage('error', errorMessage);
          } finally {
            setLoader(false);
          }
        }}
      >
        {({
          errors,
          touched,
          handleBlur,
          values,
          handleSubmit,
          handleChange,
          setFieldValue,
          dirty,
          isValid,
        }) => (
          <Form onSubmit={handleSubmit}>
            <Box className="">
              <CustomTextField
                required
                fullWidth
                name="departmentname"
                value={values?.departmentname}
                label="Department name"
                placeholder="Enter department name"
                error={touched?.departmentname && errors?.departmentname}
                helperText={touched?.departmentname && errors?.departmentname}
                onBlur={handleBlur}
                onChange={handleChange}
                disabled={loader}
              />
            </Box>
            <Box className="pt8">
              <CustomTextField
                fullWidth
                label="Remark"
                name="remark"
                onChange={handleChange}
                error={Boolean(touched.remark && errors.remark)}
                helperText={touched.remark && errors.remark}
                value={values?.remark}
                placeholder="Remark"
                multiline
                rows={3}
                disabled={loader}
              />
            </Box>
            <Box className="pt8">
              <CustomSelect
                required
                label="Status"
                name="status"
                placeholder="Status"
                options={identifiers?.STATUS}
                value={
                  identifiers?.STATUS?.find(
                    (opt) => opt?.value === values?.status
                  ) || ''
                }
                onChange={(selectedOption) =>
                  setFieldValue('status', selectedOption?.value || '')
                }
                error={touched?.status && errors?.status}
                helperText={touched?.status && errors?.status}
                disabled={loader}
                menuPortalTarget={document.body}
                styles={{
                  menuPortal: (base) => ({
                    ...base,
                    zIndex: 9999,
                  }),
                }}
              />
              {/* {touched.status && errors.status && (
                <Typography
                  variant="body2"
                  color="error"
                  className="field-error"
                >
                  {errors.status}
                </Typography>
              )} */}
            </Box>
            <Box className="form-actions-btn">
              <CustomButton
                fullWidth
                variant="contained"
                type="submit"
                disabled={!(dirty && isValid) || loader}
                title={
                  isUpdate
                    ? `${loader ? 'Updating...' : 'Update'}`
                    : `${loader ? 'Creating...' : 'Create'}`
                }
              />
            </Box>
          </Form>
        )}
      </Formik>
    </Box>
  );
}
