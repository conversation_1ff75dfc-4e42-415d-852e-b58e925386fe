import React from 'react';
import { Drawer, Box, Typography, IconButton } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import './drawer.scss';

const RightDrawer = ({
  className,
  anchor,
  open,
  onClose,
  title,
  content,
  subTitle,
}) => {
  return (
    <Drawer
      className={`drawer-sec ${className}`}
      anchor={anchor}
      onClose={onClose}
      open={open}
    >
      <Box className="drawer-header">
        <Box>
          <Typography className="title-sm">{title}</Typography>
          {subTitle ? (
            <Typography className="content-text">{subTitle}</Typography>
          ) : (
            ''
          )}
        </Box>

        <IconButton className="cross-icon" onClick={() => onClose()}>
          <CloseIcon className="svg-icon" />
        </IconButton>
      </Box>

      <Box className="h100">{content}</Box>
    </Drawer>
  );
};

export default RightDrawer;
