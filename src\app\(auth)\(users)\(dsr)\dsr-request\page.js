import React from 'react';
import { Box } from '@mui/material';
import { generateMetadata } from '@/helper/common/commonFunctions';
import DsrReq from '@/components/DSR/DSRRequest/DsrReq';

export const metadata = generateMetadata({
  pageTitle: 'DSR',
});

const DSRPage = ({ params }) => {
  return (
    <Box className="main-page-container">
      <DsrReq params={params} />
    </Box>
  );
};

export default DSRPage;
