'use client';

import React, { useEffect, useState } from 'react';
import {
  Box,
  Typography,
  TableContainer,
  TableHead,
  TableCell,
  TableRow,
  TableBody,
  Table,
} from '@mui/material';

export default function DSRTable({ dsrData, branch }) {
  const [dsrColumn, setDSRColumn] = useState();

  useEffect(() => {
    if (dsrData) {
      var headArray = [];
      Object.entries(dsrData?.column).forEach(([key, value]) => {
        // if (key !== 'id' && key !== 'name') {
        headArray.push({
          headersValue: value,
          headersKey: key,
        });
        setDSRColumn(headArray);
      });
    }
  }, [dsrData]);

  return (
    <Box className="DSR-table">
      <TableContainer className="dsr-table-container">
        <Table className="dsr-table">
          <TableHead className="dsr-table-head">
            <TableRow className="dsr-row">
              {/* <TableCell className="dsr-cell id-fixed">
                <Box className={''}>
                  <Typography className={''}>ID</Typography>
                </Box>
              </TableCell> */}
              {dsrColumn &&
                dsrColumn?.length > 0 &&
                dsrColumn?.map((item, index) => {
                  if (index === 0) {
                    return (
                      <TableCell
                        className={
                          branch?.length === 1
                            ? 'dsr-cell date-fixed head-date total-date-extra'
                            : 'dsr-cell date-fixed head-date'
                        }
                      >
                        <Box className={''}>
                          <Typography className={''}>
                            {item?.headersValue}
                          </Typography>
                        </Box>
                      </TableCell>
                    );
                  } else if (dsrColumn?.length - 1 === index) {
                    return (
                      <TableCell
                        className={
                          branch?.length === 1
                            ? 'dsr-cell total-day-fixed total-branch-extra'
                            : 'dsr-cell total-day-fixed'
                        }
                      >
                        <Box className={''}>
                          <Typography className={''}>
                            {item?.headersValue}
                          </Typography>
                        </Box>
                      </TableCell>
                    );
                  } else if (branch?.length === 1) {
                    return (
                      <TableCell className="dsr-cell branch-cell">
                        <Box className={'branch-name'}>
                          <Typography className={''}>
                            {' '}
                            {item?.headersValue}
                          </Typography>
                        </Box>
                        <Box className="d-flex align-center total-amounts">
                          <Box className="d-flex align-center amount-field ">
                            <Typography className="p14 ">Cash</Typography>
                          </Box>
                          <Box className="d-flex align-center amount-field ">
                            <Typography className="p14 ">Voucher</Typography>
                          </Box>
                          <Box className="d-flex align-center amount-field ">
                            <Typography className="p14 ">Card</Typography>
                          </Box>
                          <Box className="d-flex align-center amount-field ">
                            <Typography className="p14 ">Bank</Typography>
                          </Box>
                          <Box className="d-flex align-center amount-field ">
                            <Typography className="p14 ">
                              {' '}
                              Total amount
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                    );
                  } else {
                    return (
                      <TableCell className="dsr-cell branch-cell">
                        <Box className={''}>
                          <Typography className={''}>
                            {' '}
                            {item?.headersValue}
                          </Typography>
                        </Box>
                      </TableCell>
                    );
                  }
                })}
            </TableRow>
          </TableHead>
        </Table>
        <Table className="dsr-table">
          <TableBody>
            {dsrData &&
              dsrData?.data?.map((data, index) => {
                return (
                  <TableRow className="dsr-row">
                    {/* <TableCell className="dsr-cell id-fixed">
                      <Box className={''}>
                        <Typography className={''}>{index}</Typography>
                      </Box>
                    </TableCell> */}
                    {dsrColumn &&
                      dsrColumn?.length > 0 &&
                      dsrColumn?.map((item, index) => {
                        if (index === 0) {
                          return (
                            <TableCell className="dsr-cell date-fixed">
                              <Box className={''}>
                                <Typography className={''}>
                                  {data[item?.headersKey]}
                                </Typography>
                              </Box>
                            </TableCell>
                          );
                        } else if (dsrColumn?.length - 1 === index) {
                          return (
                            <TableCell className="dsr-cell total-day-fixed">
                              <Box className={''}>
                                <Typography className={'fw600'}>
                                  {' '}
                                  {data[item?.headersKey]}
                                </Typography>
                              </Box>
                            </TableCell>
                          );
                        } else if (branch?.length === 1) {
                          return (
                            <TableCell className="dsr-cell branch-cell">
                              {/* <Box className={''}>
                                <Typography className={''}>
                                  {data[item?.headersKey]?.total_amount}
                                </Typography>
                              </Box> */}
                              <Box className="d-flex align-center total-amounts">
                                <Box className="d-flex align-center amount-field ">
                                  <Typography className="p14 fw400">
                                    {/* <span className="fw600"> Cash : </span>
                                      <span> */}
                                    {data[item?.headersKey]?.cash_amount}
                                    {/* </span> */}
                                  </Typography>
                                </Box>

                                <Box className="d-flex align-center amount-field">
                                  <Typography className="p14 fw400">
                                    {data[item?.headersKey]?.voucher_amount}
                                  </Typography>
                                </Box>

                                <Box className="d-flex align-center amount-field">
                                  <Typography className="p14 fw400">
                                    {data[item?.headersKey]?.card_amount}
                                  </Typography>
                                </Box>

                                <Box className="d-flex align-center amount-field">
                                  <Typography className="p14 fw400">
                                    {data[item?.headersKey]?.bank_amount}
                                  </Typography>
                                </Box>
                                <Box className="d-flex align-center amount-field">
                                  <Typography className="p14 fw600">
                                    {data[item?.headersKey]?.total_amount}
                                  </Typography>
                                </Box>
                              </Box>
                            </TableCell>
                          );
                        } else {
                          return (
                            <TableCell className="dsr-cell branch-cell">
                              <Box className={''}>
                                <Typography className={''}>
                                  {data[item?.headersKey]?.total_amount}
                                </Typography>
                              </Box>
                            </TableCell>
                          );
                        }
                      })}
                  </TableRow>
                );
              })}
          </TableBody>
          <TableRow className="dsr-row">
            {/* <TableCell className="dsr-cell id-fixed total-id">
              <Box className={''}>
                <Typography className={''}></Typography>
              </Box>
            </TableCell> */}
            {dsrColumn &&
              dsrColumn?.length > 0 &&
              dsrColumn?.map((item, index) => {
                if (index === 0) {
                  return (
                    <TableCell className="dsr-cell date-fixed total-date">
                      <Box className={''}>
                        <Typography className={''}></Typography>
                      </Box>
                    </TableCell>
                  );
                } else if (dsrColumn?.length - 1 === index) {
                  return (
                    <TableCell className="dsr-cell total-day-fixed total-day">
                      <Box className={''}>
                        <Typography className={'fw600'}>
                          {' '}
                          {dsrData?.total?.[item?.headersKey]}
                        </Typography>
                      </Box>
                    </TableCell>
                  );
                } else {
                  return (
                    <TableCell className="dsr-cell branch-cell total-day">
                      <Box className={''}>
                        <Typography className={'fw600'}>
                          {dsrData?.total?.[item?.headersKey]}
                        </Typography>
                      </Box>
                    </TableCell>
                  );
                }
              })}
          </TableRow>
        </Table>
      </TableContainer>
    </Box>
  );
}
