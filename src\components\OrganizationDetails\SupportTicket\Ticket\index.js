import { Box, Tooltip, Typography } from '@mui/material';
import PersonIcon from '@mui/icons-material/Person';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import React, { useState } from 'react';
import CustomSelect from '@/components/UI/CustomSelect';
import { DateFormat, setApiMessage } from '@/helper/common/commonFunctions';
import DeleteIcon from '@mui/icons-material/Delete';
import DialogBox from '@/components/UI/Modalbox';
import DeleteModal from '@/components/UI/DeleteModal';
import { supportTicketService } from '@/services/supportTicketService';
import './ticket.scss';

const statusOptions = [
  { label: 'Open', value: 'open' },
  { label: 'Escalated', value: 'escalated' },
  { label: 'In-Progress', value: 'in_progress' },
  { label: 'Invoiced', value: 'invoiced' },
  { label: 'On Hold', value: 'on_hold' },
  { label: 'QA Review', value: 'qa_review' },
  { label: 'Assigned', value: 'assigned' },
  { label: 'Under Review', value: 'under_review' },
  { label: 'Resolved', value: 'resolved' },
  { label: 'Closed', value: 'closed' },
];

export default function Ticket({
  onTicketClick,
  ticketsList = [], // Accept list of tickets
  selectedTicket, // Track which ticket is selected
  hideDropdown = false, // New prop to conditionally hide dropdown
  hideStatusChip = false, // New prop to conditionally hide status chip
  showBottomPadding = false, // New prop to conditionally show pb8 class
  onTicketStatusChange, // New prop to handle individual ticket status changes
  onTicketDelete, // New prop to handle ticket deletion
}) {
  // State for delete confirmation dialog
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [ticketToDelete, setTicketToDelete] = useState(null);
  const [isDeleting, setIsDeleting] = useState(false);
  // Function to get priority CSS class
  const getPriorityClass = (priority) => {
    const priorityMap = {
      urgent: 'failed',
      high: 'failed',
      medium: 'draft',
      low: 'active-onboarding',
      none: 'draft',
    };
    return priorityMap[priority?.toLowerCase()] || 'draft';
  };

  // Function to format priority display consistently
  const formatPriorityDisplay = (priority) => {
    if (!priority) return '';
    // Convert to lowercase first, then capitalize first letter
    const formatted = priority.toLowerCase();
    return formatted.charAt(0).toUpperCase() + formatted.slice(1);
  };

  // Function to get status CSS class
  const getStatusClass = (status) => {
    const statusMap = {
      open: 'status-yellow',
      escalated: 'failed',
      in_progress: 'draft',
      invoiced: 'success',
      on_hold: 'status-yellow',
      qa_review: 'draft',
      assigned: 'draft',
      under_review: 'draft',
      resolved: 'success',
      closed: 'success',
    };
    return statusMap[status] || 'draft';
  };

  // Use only provided tickets list - no default fallback to avoid duplication
  const ticketsToDisplay = ticketsList;

  const handleTicketClick = (ticket) => (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (onTicketClick) {
      onTicketClick(ticket);
    }
  };

  const handleStatusChange = (ticket, newStatus) => {
    // Call the parent's status change handler if provided
    if (onTicketStatusChange) {
      onTicketStatusChange(ticket?.id, newStatus);
    }
  };

  const handleDeleteClick = (ticket) => (e) => {
    e.preventDefault();
    e.stopPropagation();
    // Show confirmation dialog instead of direct delete
    setTicketToDelete(ticket);
    setDeleteDialogOpen(true);
  };

  // Handle cancel delete dialog
  const handleCancelDelete = () => {
    setDeleteDialogOpen(false);
    setTicketToDelete(null);
  };

  // Handle confirm delete
  const handleConfirmDelete = async () => {
    if (!ticketToDelete) return;

    try {
      setIsDeleting(true);

      // Call API to delete ticket
      const result = await supportTicketService.deleteTicket(
        ticketToDelete?.id
      );
      // Show success message
      setApiMessage('success', result?.message);

      // Call parent's delete handler if provided
      if (onTicketDelete) {
        await onTicketDelete(ticketToDelete);
      }

      // Close dialog
      setDeleteDialogOpen(false);
      setTicketToDelete(null);
    } catch (error) {
      // Show error message to user
      setApiMessage(
        'error',
        error?.response?.data?.message ||
          'Failed to delete ticket. Please try again.'
      );
    } finally {
      setIsDeleting(false);
    }
  };

  // Don't render anything if no tickets provided
  if (!ticketsToDisplay || ticketsToDisplay.length === 0) {
    return null;
  }

  return (
    <Box className="tickets-list-container ticket-list-container-wrap cursor-pointer">
      {ticketsToDisplay?.map((ticketData, index) => {
        return (
          <Box
            key={ticketData?.id || index}
            className={`ticket-wrap ${selectedTicket?.id === ticketData?.id ? 'selected' : ''}`}
            onClick={handleTicketClick(ticketData)}
          >
            <Box className="heading-wrap">
              <Box className="status-delete-wrap d-flex align-center gap-5 justify-space-between mb8">
                {ticketData?.ticket_status && !hideStatusChip && (
                  <Typography
                    className={`sub-title-text fw600 status-chip-wrap ${getStatusClass(ticketData?.ticket_status)}`}
                  >
                    {ticketData?.ticket_status?.replace('_', ' ')}
                  </Typography>
                )}

                <DeleteIcon
                  className="delete-icon cursor-pointer"
                  onClick={handleDeleteClick(ticketData)}
                />
              </Box>

              <Typography className="heading-text-wrap body-sm d-flex align-center gap-5">
                <span>#{ticketData?.id || '-'}</span>{' '}
                {ticketData?.ticket_title || '-'}
              </Typography>
            </Box>

            <Box className="description-wrap">
              {ticketData?.ticket_description ? (
                <Tooltip
                  title={
                    <Typography className="sub-title-text">
                      {ticketData?.ticket_description}
                    </Typography>
                  }
                  arrow
                  classes={{
                    tooltip: 'info-tooltip-container',
                  }}
                >
                  <Typography className="body-sm text-ellipsis description-text">
                    {ticketData?.ticket_description}
                  </Typography>
                </Tooltip>
              ) : (
                <Typography className="description-text body-sm">-</Typography>
              )}
            </Box>

            <Box className="name-time-wrap pb4">
              <Box
                className={`d-flex align-center gap-5 pb4 ${showBottomPadding ? 'pb8' : ''}`}
              >
                <PersonIcon className="user-icon" />
                <Typography className="name-text body-sm">
                  {ticketData?.owner_full_name || '-'}
                </Typography>
              </Box>
              <Box className="d-flex align-center justify-space-between gap-5">
                <Box className="d-flex align-center gap-5">
                  <AccessTimeIcon className="time-icon" />
                  <Typography className="time-text body-sm">
                    {ticketData?.created_at
                      ? DateFormat(ticketData?.created_at, 'datesWithhour')
                      : '-'}
                  </Typography>
                </Box>
                {/* Show priority next to date */}
                {ticketData?.ticket_priority && (
                  <Typography
                    className={`sub-title-text fw600 ${getPriorityClass(ticketData?.ticket_priority)}`}
                  >
                    {formatPriorityDisplay(ticketData?.ticket_priority)}
                  </Typography>
                )}
              </Box>
            </Box>

            {/* {ticketData?.assignedTo && (
            <Box className="assigned-wrap">
              <p className="assigned-text body-sm">
                <span className="assigned-label">Assigned to:</span>{' '}
                {ticketData?.assignedTo}
              </p>
            </Box>
          )} */}

            {!hideDropdown && (
              <Box
                className="ticket-status-select-container"
                onClick={(e) => {
                  e.stopPropagation(); // Prevent ticket click when clicking on select
                }}
              >
                <CustomSelect
                  className="slected-wrap ticket-status-select"
                  placeholder="Select Status"
                  options={statusOptions}
                  value={
                    statusOptions?.find(
                      (opt) =>
                        opt?.value === ticketData?.ticket_status ||
                        opt?.value === ticketData?.ticket_status?.toLowerCase()
                    ) || ''
                  }
                  name="status"
                  onChange={(e) =>
                    handleStatusChange(ticketData, e?.value || '')
                  }
                />
              </Box>
            )}

            {/* <Box className="profile-wrap">
            <Image
              className="profile-image"
              src={ProfileImage}
              alt="Profile Image"
              width={100}
              height={100}
            />
          </Box> */}
          </Box>
        );
      })}

      {/* Delete Confirmation Dialog */}
      <DialogBox
        open={deleteDialogOpen}
        handleClose={handleCancelDelete}
        title="Confirmation"
        className="delete-modal"
        dividerClass="delete-modal-divider"
        content={
          <DeleteModal
            handleCancel={handleCancelDelete}
            handleConfirm={handleConfirmDelete}
            text={`Are you sure you want to delete "${ticketToDelete?.ticket_title || 'this ticket'}"?`}
            confirmText={isDeleting ? 'Deleting...' : 'Yes, Delete it'}
            cancelText="Cancel"
          />
        }
      />
    </Box>
  );
}
