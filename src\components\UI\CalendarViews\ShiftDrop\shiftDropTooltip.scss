.shift-drop-container {
  min-width: 350px;
  font-family: var(--font-family-primary);
  .shift-drop-header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 30px;
    padding: 15px;
    .shift-drop-header-title {
      font-size: 18px;
      line-height: 20px;
      font-weight: 500;
      text-decoration: underline;
      text-decoration-style: dotted;
      text-underline-offset: 8px;
      text-decoration-color: var(--icon-color-slate-gray);
    }
    .shift-drop-header-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 10px;
      .close-shift-tooltip {
        height: var(--icon-size-md);
        width: var(--icon-size-md);
        cursor: pointer;
        color: var(--icon-color-slate-gray);
      }
    }
  }
  .shift-drop-details-container {
    .shift-drop-details-wrapper {
      background: var(--color-light-grayish-blue);
      border-left: 3px solid var(--border-color-primary);
      padding: 15px;
      .shift-drop-details {
        .shift-drop-user-details {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          gap: 15px;
          .shift-drop-avatar {
            .MuiAvatar-root {
              height: var(--icon-size-lg);
              width: var(--icon-size-lg);
              font-size: var(--font-size-sm);
            }
          }
        }
        .shift-drop-shift-details {
          background: var(--color-white);
          margin-top: 10px;
          margin-left: 40px;
          padding: 10px;
          border-radius: 8px;
          .shift-drop-shift-time-role {
            border-left: 3px solid var(--color-light-grayish-blue);
            padding-left: 5px;
            .shift-drop-shift-time {
              color: var(--text-color-black);
              font-size: 14px;
              line-height: 21px;
              letter-spacing: 0px;
              font-weight: 600;
            }
            .shift-drop-shift-date {
              color: var(--text-light-dark);
              font-size: 14px;
              line-height: 15px;
              letter-spacing: 0px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              font-weight: 400;
              margin-top: 2px;
            }
          }
          .shift-drop-shift-icon-text {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            gap: 5px;
            margin-bottom: 2px;
            max-width: 350px;
            svg {
              width: 20px;
              height: 20px;
              fill: var(--icon-color-light-dark);
            }
            span {
              font-size: 14px;
              line-height: 21px;
              color: var(--icon-color-light-dark);
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
    .d-center-start {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: 10px;
    }
    // Shift Note
    .shift-drop-note-row {
      padding: 10px;
      .shift-drop-note-input-wrapper {
        width: 100%;
        .shift-drop-note-input {
          width: 100%;
          border: none;
          background: none;
          outline: none;
          padding: var(--field-padding);
          border-radius: var(--field-radius);
          font-size: var(--font-size-sm);
          line-height: var(--line-height-xs);
          font-family: var(--font-family-primary);
          color: var(--text-color-black);
          resize: none;
          &:hover {
            background: var(--color-secondary);
            color: var(--text-color-black);
          }
          &:focus {
            outline: var(--field-border);
          }
        }
      }
    }
    // Shift Note Char Count
    .shift-drop-note-char-count {
      padding-bottom: 20px;
      .shift-drop-note-visibility-container {
        width: 100%;
        padding: 0px 10px;
        .char-count {
          text-align: right;
          font-size: var(--font-size-xs);
          color: var(--text-color-slate-gray);
          font-weight: var(--font-weight-regular);
        }
      }
    }
    .icon {
      height: var(--icon-size-sm);
      width: var(--icon-size-sm);
      color: var(--icon-color-slate-gray);
      svg {
        height: var(--icon-size-sm);
        width: var(--icon-size-sm);
      }
    }
  }
}
