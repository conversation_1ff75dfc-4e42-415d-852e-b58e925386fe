import React, { useState, useEffect } from 'react';
import { Box } from '@mui/material';
import { AgCharts } from 'ag-charts-react';

const BarChart = ({ item }) => {
  const [options, setOptions] = useState({
    title: {
      text: 'DSR Graph',
    },
    subtitle: {
      text: 'In Billion U.S. Dollars',
    },
    data: [],
    series: [],
    // data: getData(),
    // series: [
    //   {
    //     type: 'bar',
    //     xKey: 'quarter',
    //     yKey: 'iphone',
    //     yName: 'Category',
    //     fill: '#FF5733'
    //   }
    // ]
  });
  useEffect(() => {
    if (item && item?.data) {
      setOptions(item);
    }
  }, [item]);
  return (
    <>
      <Box className="">
        <AgCharts options={options} />
      </Box>
    </>
  );
};

export default BarChart;
