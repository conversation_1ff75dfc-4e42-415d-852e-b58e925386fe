'use client';

import React, { useContext, useEffect, useState, useRef } from 'react';
import { Box, Typography, CircularProgress, Tooltip } from '@mui/material';
import AuthContext from '@/helper/authcontext';
import { DataGrid } from '@mui/x-data-grid';
import CustomPagination from '@/components/UI/customPagination';
import { fetchFromStorage, removeFromStorage } from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import { staticOptions } from '@/helper/common/staticOptions';
import moment from 'moment';
import RemarkIcon from '@/components/ActionIcons/RemarkIcon';
import NotificationsNoneIcon from '@mui/icons-material/NotificationsNone';
import CustomButton from '@/components/UI/CustomButton';
import TuneIcon from '@mui/icons-material/Tune';
import Searchbar from '@/components/UI/CustomSearch';
import DialogBox from '@/components/UI/Modalbox';
import CustomSelect from '@/components/UI/CustomSelect';
import dayjs from 'dayjs';
import CustomDatePicker from '@/components/UI/CustomDatePicker';
import PreLoader from '@/components/UI/Loader';
import NoDataView from '@/components/UI/NoDataView';
import CommonUserDetails from '@/components/UI/CommonUserDetails';
import EmpContract from '../ProfileDetails/EmpContract';
import { setApiMessage } from '@/helper/common/commonFunctions';
import InfoIcon from '@mui/icons-material/Info';
import './contractrenewal.scss';
import {
  getUserDetails,
  getGeneralTemplateDetails,
  getDepartmentTemplateDetailsByID,
  getDepartmentTemplateDetails,
  getContractTypeDetails,
  handleCreateOptionContractPolicy,
  handleContractAction,
  getRenewalList,
} from '@/services/userProfileService';

export default function ContractRenewal() {
  const { userdata, setUserdata } = useContext(AuthContext);
  const { authState } = useContext(AuthContext);
  const [filter, setFilter] = useState(false);
  const [filterDataApplied, setFilterDataApplied] = useState({
    startDate: '',
    endDate: '',
    status: '',
  });
  const [filterData, setFilterData] = useState({
    startDate: '',
    endDate: '',
    status: '',
  });
  const [userList, setUserList] = useState([{ id: '' }]);
  const [loader, setLoader] = useState(true);
  const [searchValue, setSearchValue] = useState('');
  const [page, setPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [actionLoader, setActionLoader] = useState(false);
  const [openContractDialog, setOpenContractDialog] = useState(false);
  const formikRefcontract = useRef();
  const [contractTypeData, setContractTypeData] = useState([]);
  const [leaveTypeList, setLeaveTypeList] = useState([]);
  const [departmentTemplateData, setDepartmentTemplateData] = useState([]);
  const [generalTemplateData, setGeneralTemplateData] = useState([]);
  const [isLoader, setIsLoader] = useState(false);
  const [ContractList, setContractList] = useState([]);
  const [UserDetails, setUserDetails] = useState('');
  const [departmentByID, setDepartmentByID] = useState('');
  const [departmentTemplateDataByID, setDepartmentTemplateDataByID] = useState(
    []
  );
  const [departmentTemByID, setDepartmentTempByID] = useState([]);

  const columns = [
    {
      field: 'id',
      headerName: 'ID',
      width: 48,
      minWidth: 48,
      flex: 0,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
    },
    {
      field: 'user_full_name',
      headerName: 'User',
      width: 260,
      minWidth: 260,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <CommonUserDetails
            userData={params?.row?.user_employment_contract}
            filterDataApplied={filterDataApplied}
            searchValue={searchValue}
            page={page}
            rowsPerPage={rowsPerPage}
            setUserdata={setUserdata}
            authState={authState}
            navigationProps={{ isContract: true }}
          />
        );
      },
    },
    {
      field: 'is_confirm_sign',
      headerName: 'Contract status',
      width: 150,
      minWidth: 150,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        const currentDate = moment(new Date()).startOf('day');
        const expireDate = moment(params?.row?.expire_date).startOf('day');

        const daysDifference = expireDate.diff(currentDate, 'days');

        const isExpiry =
          daysDifference <= 0
            ? 'Expired'
            : daysDifference > 0 && daysDifference <= 15
              ? 'Expiry Soon'
              : params?.row?.is_confirm_sign === 1
                ? params?.row?.user_employment_contract?.is_probation === 1
                  ? 'Probation'
                  : params?.row?.user_employment_contract?.is_probation === 0
                    ? 'Confirmed'
                    : ''
                : params?.row?.is_confirm_sign === null
                  ? 'Pending'
                  : 'Awaiting Signature';

        return (
          <Box className="d-flex align-center justify-center h100 text-capital">
            {isExpiry === 'Expired' ? (
              <Typography className="sub-title-text failed fw600">
                {' '}
                {isExpiry}{' '}
              </Typography>
            ) : isExpiry === 'Expiry Soon' ? (
              <Typography className="sub-title-text status-yellow fw600">
                {isExpiry}
              </Typography>
            ) : isExpiry === 'Probation' ? (
              <Typography className="sub-title-text success fw600">
                {isExpiry}
              </Typography>
            ) : isExpiry === 'Confirmed' ? (
              <Typography className="sub-title-text active-onboarding fw600">
                {isExpiry}
              </Typography>
            ) : (
              <Typography className="sub-title-text draft fw600">
                {isExpiry}
              </Typography>
            )}
          </Box>
        );
      },
    },
    {
      field: 'expire_date',
      headerName: 'Contract Expiry',
      width: 200,
      minWidth: 200,
      flex: 1,
      headerAlign: 'start',
      align: 'start',
      sortable: false,
      renderCell: (params) => {
        const currentDate = moment(new Date()).startOf('day');
        const expireDate = moment(params?.row?.expire_date).startOf('day');
        const daysDifference = expireDate.diff(currentDate, 'days');

        const expSoon = daysDifference > 0 && daysDifference <= 15;
        const expToday = daysDifference === 0;
        const expPast = daysDifference < 0;

        const expData = params?.value
          ? moment(params?.value).format('DD MMM YYYY')
          : '-';

        const isExpiry = expSoon
          ? `Expire in ${daysDifference} days`
          : expToday
            ? `Expires today`
            : expPast
              ? `Expired ${Math.abs(daysDifference)} days ago`
              : `Expire in ${daysDifference} days`;

        return (
          <Box className="d-flex align-center justify-start h100">
            {params?.value !== null ? (
              <Tooltip
                arrow
                title={
                  <Typography>
                    {expSoon
                      ? `Expire in: ${expData}`
                      : expToday
                        ? `Expires: ${expData}`
                        : expPast
                          ? `Expired: ${expData}`
                          : `Expire in: ${expData}`}
                  </Typography>
                }
                classes={{
                  tooltip: 'info-tooltip-container',
                }}
                placement="bottom-start"
              >
                <Typography className="title-text text-ellipsis">
                  <span>{isExpiry}</span>
                </Typography>
              </Tooltip>
            ) : (
              '-'
            )}
          </Box>
        );
      },
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 150,
      minWidth: 150,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex justify-start align-center gap-sm h100">
            <Box className="d-flex cursor-pointer">
              <Tooltip
                title={<Typography>Renew</Typography>}
                arrow
                classes={{
                  tooltip: 'info-tooltip-container ',
                }}
              >
                <Box>
                  <RemarkIcon
                    onClick={() => {
                      if (params?.row?.user_id === authState?.id) {
                        setApiMessage(
                          'error',
                          'Renewal of your own employment contract is not allowed.'
                        );
                      } else {
                        handleRenewAndNotify(params?.row, 'renew');
                      }
                    }}
                  />
                </Box>
              </Tooltip>
            </Box>
            <Box className="d-flex cursor-pointer">
              <Tooltip
                title={<Typography>Notify</Typography>}
                arrow
                classes={{
                  tooltip: 'info-tooltip-container ',
                }}
              >
                <Box
                  className="action-icon d-flex"
                  onClick={() => {
                    if (params?.row?.user_id === authState?.id) {
                      setApiMessage(
                        'error',
                        "You're not allowed to notify about your own employment contract."
                      );
                    } else {
                      handleRenewAndNotify(params?.row, 'notify');
                    }
                  }}
                >
                  <NotificationsNoneIcon className="border-svg notify-svg" />
                </Box>
              </Tooltip>
            </Box>
            {/* {!params?.row?.is_confirm_sign && (
              <Box className="d-flex cursor-pointer">
                <Tooltip title="Remind" arrow   classes={{
                  tooltip: 'info-tooltip-container ',
                }}>
                  <Box>
                    <RequestIcon
                      onClick={() => {
                        handleRenewAndNotify(params?.row, 'remind');
                      }}
                    />
                  </Box>
                </Tooltip>
              </Box>
            )} */}
          </Box>
        );
      },
    },
  ];
  // LIST OF USER
  const getUserList = async (
    search,
    pageNo,
    startDate,
    endDate,
    statusValue,
    Rpp
  ) => {
    setLoader(true);
    try {
      const response = await getRenewalList(
        search,
        pageNo,
        startDate,
        endDate,
        statusValue,
        Rpp ? Rpp : rowsPerPage
      );
      if (response.success) {
        setUserdata();
        removeFromStorage(identifiers?.RedirectData);
        setUserList(response.data.list);
        setTotalCount(response.data.total);
        setLoader(false);
      }
    } catch {
      setLoader(false);
      setUserList([]);
    }
  };
  // GET GENERAL TEMPLATE DETAILS
  const getGeneralTemplateDetailsData = async () => {
    // setLoader(true);
    try {
      const response = await getGeneralTemplateDetails();
      if (response.success) {
        setLoader(false);
        setGeneralTemplateData(response.data);
      }
    } catch {
      setLoader(false);
      setGeneralTemplateData([]);
    }
  };

  const handleCreateOptionContractPolicyData = async (inputValue) => {
    try {
      const response = await handleCreateOptionContractPolicy(inputValue);
      if (response.success) {
        setLoader(false);
        getContractTypeDetailsData(inputValue);
      }
    } catch {
      setLoader(false);
    }
  };
  // GET CONTRACT TYPE DETAILS
  const getContractTypeDetailsData = async (name) => {
    try {
      const response = await getContractTypeDetails();
      if (response.success) {
        setLoader(false);
        setContractTypeData(response.data);
        if (name) {
          const findname = response.data?.find((f) => f?.label === name);
          formikRefcontract.current.setFieldValue(
            'contractName',
            findname?.value
          );
        }
      }
    } catch {
      setLoader(false);
      setContractTypeData([]);
    }
  };
  const onPageChange = (newPage) => {
    setPage(newPage);
    getUserList(
      searchValue,
      newPage,
      filterDataApplied?.startDate,
      filterDataApplied?.endDate,
      filterDataApplied?.status
    );
  };
  const OnRowPerPage = (newPage) => {
    setRowsPerPage(newPage);
    setPage(1);
    getUserList(
      searchValue,
      1,
      filterDataApplied?.startDate,
      filterDataApplied?.endDate,
      filterDataApplied?.status,
      newPage
    );
  };

  useEffect(() => {
    if (
      fetchFromStorage(identifiers?.RedirectData) &&
      fetchFromStorage(identifiers?.RedirectData)?.IsFromUser &&
      !fetchFromStorage(identifiers?.RedirectData)?.isContract
    ) {
      const fdata = fetchFromStorage(identifiers?.RedirectData);
      setPage(fdata?.page);
      setFilterDataApplied(fdata?.filterData);
      setSearchValue(fdata?.searchValue);
      getUserList(
        fdata?.searchValue,
        fdata?.page,
        fdata?.filterData?.startDate,
        fdata?.filterData?.endDate,
        fdata?.filterData?.status
      );
    } else if (userdata && userdata?.IsFromUser && userdata?.isContract) {
      const fdata = userdata;
      setPage(fdata?.page);
      setFilterDataApplied(fdata?.filterData);
      setSearchValue(fdata?.searchValue);
      getUserList(
        fdata?.searchValue,
        fdata?.page,
        fdata?.filterData?.startDate,
        fdata?.filterData?.endDate,
        fdata?.filterData?.status
      );
    }
  }, [
    fetchFromStorage(identifiers?.RedirectData)?.IsFromUser,
    userdata?.IsFromUser,
  ]);
  useEffect(() => {
    getGeneralTemplateDetailsData();
    getContractTypeDetailsData();
    if (
      authState?.UserPermission?.staff === 1 ||
      authState?.UserPermission?.staff === 2
    ) {
      if (
        !fetchFromStorage(identifiers?.RedirectData) &&
        userdata?.page === undefined &&
        fetchFromStorage(identifiers?.RedirectData)?.IsFromUser === undefined &&
        userdata?.IsFromUser === undefined
      ) {
        getUserList(searchValue, page, '', '', '', '');
      }
    }
  }, [authState?.UserPermission?.staff]);

  const handleCloseDialog = () => {
    setOpenContractDialog(false);
  };
  // GET DEPARTMENT TEMPLATE DETAILS
  const getDepartmentTemplateDetailsByIDData = async (id) => {
    try {
      const response = await getDepartmentTemplateDetailsByID(id);
      if (response.success) {
        setLoader(false);
        setDepartmentTemplateDataByID(response.data);
      }
    } catch {
      setLoader(false);
      setDepartmentTemplateDataByID([]);
    }
  };
  // GET DEPARTMENT TEMPLATE DETAILS
  const getDepartmentTemplateDetailsData = async (is, id, allID) => {
    try {
      const response = await getDepartmentTemplateDetails(is, id, allID);
      if (response.success) {
        setLoader(false);
        const { departmentTemplateData, contractList, departmentTempByID } =
          response.data;
        setDepartmentTemplateData(departmentTemplateData);
        setContractList(contractList);
        setDepartmentTempByID(departmentTempByID);
      }
    } catch {
      setLoader(false);
      setDepartmentTemplateData([]);
      setDepartmentTempByID([]);
    }
  };
  // Get user details by user id
  const getUserDetailsData = async (id) => {
    // setLoader(true);
    try {
      const response = await getUserDetails(id);
      if (response.success) {
        setUserDetails(response.data);
        const firstId = response.data?.user_meta?.department_template
          ? response.data?.user_meta?.department_template
          : null;
        const getAlldepart =
          response.data?.user_meta?.additional_template &&
          response.data?.user_meta?.additional_template.split(',').map(Number);

        // Use existing departmentTemByID data if available
        if (departmentTemByID && departmentTemByID.length > 0) {
          const tempData = departmentTemByID;
          if (
            (firstId && !Number.isNaN(Number(firstId))) ||
            (getAlldepart && getAlldepart?.length > 0)
          ) {
            const filteredArray =
              tempData &&
              tempData.length > 0 &&
              getAlldepart?.length > 0 &&
              getAlldepart
                ?.filter((id) => tempData.some((item) => item.id === id))
                .map((id) => tempData.find((item) => item.id === id));

            filteredArray &&
              filteredArray?.length > 0 &&
              setContractList(filteredArray);
            getAlldepart?.length === 0 && setContractList([]);

            const filterCat = tempData?.filter(
              (f) => f?.id !== Number(firstId)
            );
            const filterCats = filterCat?.filter(
              (f) => !getAlldepart?.includes(f?.id)
            );
            setDepartmentTemplateData(filterCats);
          } else {
            setDepartmentTemplateData(tempData);
            setContractList([]);
          }
        } else {
          getDepartmentTemplateDetailsData(
            null,
            firstId,
            getAlldepart ? getAlldepart : []
          );
        }

        if (departmentByID !== response.data?.department?.id) {
          getDepartmentTemplateDetailsByIDData(response.data?.department?.id);
          setDepartmentByID(response.data?.department?.id);
        }
        setLoader(false);
        setOpenContractDialog(true);
      }
    } catch {
      setLoader(false);
    }
  };
  const handleRenewAndNotify = async (renewNotifyData, type) => {
    if (type === 'renew') {
      getUserDetailsData(renewNotifyData?.user_id);
    } else {
      try {
        setActionLoader(true);
        const response = await handleContractAction(
          type === 'regenerate' ? renewNotifyData : renewNotifyData?.user_id,
          type
        );
        if (response.success) {
          setActionLoader(false);
          getUserList(
            searchValue,
            page,
            filterDataApplied?.startDate,
            filterDataApplied?.endDate,
            filterDataApplied?.status
          );
          handleCloseDialog();
        }
      } catch {
        setActionLoader(false);
      }
    }
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      setPage(1);
      getUserList(
        searchValue,
        1,
        filterDataApplied?.startDate,
        filterDataApplied?.endDate,
        filterDataApplied?.status
      );
    }
  };

  return (
    <>
      <Box className="contract-renewal-container">
        <Box className="d-flex gap-sm align-center flex-wrap justify-end">
          <Searchbar
            setSearchValue={setSearchValue}
            searchValue={searchValue}
            onKeyPress={handleKeyPress}
          />
          <CustomButton
            variant="outlined"
            isIconOnly
            startIcon={
              <Tooltip
                title={
                  <Typography className="sub-title-text">
                    Apply Filter
                  </Typography>
                }
                classes={{
                  tooltip: 'info-tooltip-container',
                }}
                arrow
              >
                <TuneIcon />
              </Tooltip>
            }
            onClick={() => {
              setFilter(!filter);
            }}
          />
          <CustomButton
            variant="contained"
            title="Apply filter"
            fullWidth={false}
            onClick={() => {
              setPage(1);
              getUserList(
                searchValue,
                1,
                filterDataApplied?.startDate,
                filterDataApplied?.endDate,
                filterDataApplied?.status
              );
            }}
            // onClick={() => searchdata(search)}
          />
        </Box>
        {actionLoader && <PreLoader />}
        <Box className="table-container table-layout">
          {loader ? (
            <Box className="content-loader">
              <CircularProgress className="loader" color="inherit" />
            </Box>
          ) : (
            <>
              {userList && userList?.length === 0 ? (
                <Box className="no-data d-flex align-center justify-center">
                  <NoDataView
                    // image
                    title="No Contract Renewals Found"
                    description="There are no contract renewals available at the moment."
                  />
                </Box>
              ) : (
                <>
                  <DataGrid
                    rows={userList}
                    columns={columns}
                    pageSize={rowsPerPage}
                    checkboxSelection={false}
                    disableSelectionOnClick
                    hideMenuIcon
                  />
                  <CustomPagination
                    currentPage={page}
                    OnRowPerPage={OnRowPerPage}
                    totalCount={totalCount}
                    rowsPerPage={rowsPerPage}
                    onPageChange={onPageChange}
                  />
                </>
              )}
            </>
          )}
        </Box>
        <DialogBox
          open={filter}
          handleClose={() => {
            setFilter(!filter);
          }}
          title="Contract Filter"
          className="small-dialog-box-container"
          content={
            <>
              <Box className="staff-filter">
                <Box>
                  <Box>
                    <CustomSelect
                      placeholder="Status"
                      options={staticOptions?.CONTRACT_FILTER_STATUS}
                      value={
                        staticOptions?.CONTRACT_FILTER_STATUS?.find((opt) => {
                          return opt?.value === filterData?.status;
                        }) || ''
                      }
                      onChange={(e) => {
                        setFilterData({
                          ...filterData,
                          status: e?.value,
                        });
                      }}
                      label={<span>Status</span>}
                      menuPortalTarget={document.body}
                      styles={{
                        menuPortal: (base) => ({
                          ...base,
                          zIndex: 9999,
                        }),
                      }}
                    />
                  </Box>
                  <Box className="d-flex align-center mt16 gap-5">
                    <Typography className="sub-content-text">
                      Filter based on Contract Expiry Date
                    </Typography>
                    <Tooltip
                      arrow
                      title={
                        <Typography>
                          This filters contracts expiring between the selected
                          Start and End dates, not the user's contract start
                          date.
                        </Typography>
                      }
                      classes={{ tooltip: 'info-tooltip-container' }}
                    >
                      <InfoIcon className="info-icon cursor-pointer" />
                    </Tooltip>
                  </Box>
                  <Box className="pt8">
                    <CustomDatePicker
                      label={<span>Contracts Expiring From (DD/MM/YYYY)</span>}
                      name="startDate"
                      value={dayjs(filterData?.startDate)}
                      error={false}
                      format="DD/MM/YYYY"
                      onChange={(date) => {
                        setFilterData({
                          ...filterData,
                          startDate: date,
                          endDate: '',
                        });
                      }}
                      inputVariant="outlined"
                    />
                  </Box>
                  <Box className="pt8">
                    <CustomDatePicker
                      label={<span>Contracts Expiring To (DD/MM/YYYY)</span>}
                      name="endDate"
                      error={false}
                      format="DD/MM/YYYY"
                      value={dayjs(filterData?.endDate)}
                      onChange={(date) => {
                        setFilterData({
                          ...filterData,
                          endDate: date,
                        });
                      }}
                      minDate={dayjs(filterData?.startDate)}
                      // disablePast
                      inputVariant="outlined"
                      // disabled={isDisabled}
                    />
                  </Box>
                  <Box className="form-actions-btn">
                    <CustomButton
                      fullWidth
                      variant="outlined"
                      title="Clear"
                      onClick={() => {
                        setFilter(false);
                        setPage(1);
                        setFilterData({
                          startDate: '',
                          endDate: '',
                          status: '',
                        });
                        setFilterDataApplied({
                          startDate: '',
                          endDate: '',
                          status: '',
                        });
                        getUserList(searchValue, 1, '', '', '', '');
                      }}
                    />
                    <CustomButton
                      fullWidth
                      variant="contained"
                      title="Apply"
                      onClick={() => {
                        setFilter(false);
                        setPage(1);
                        setFilterDataApplied({
                          startDate: filterData?.startDate,
                          endDate: filterData?.endDate,
                          status: filterData?.status,
                        });
                        getUserList(
                          searchValue,
                          1,
                          filterData?.startDate,
                          filterData?.endDate,
                          filterData?.status
                        );
                      }}
                    />
                  </Box>
                </Box>
              </Box>
            </>
          }
        />
        <DialogBox
          open={openContractDialog}
          handleClose={handleCloseDialog}
          title="Contract Renewal"
          maxWidth="lg"
          className="resignation-dialogbox contract-renewal-dialogbox small-dialog-box-container"
          content={
            <EmpContract
              UserDetails={UserDetails}
              UserId={UserDetails?.id}
              formikRef={formikRefcontract}
              generalTemplateData={generalTemplateData}
              ViewAccessOnly={false}
              contractTypeData={contractTypeData}
              leaveTypeList={leaveTypeList}
              departmentTemplateData={departmentTemplateData}
              loader={loader || actionLoader}
              isLoader={isLoader}
              ContractList={ContractList}
              setContractList={setContractList}
              setIsLoader={setIsLoader}
              setDepartmentTemplateData={setDepartmentTemplateData}
              setLeaveTypeList={setLeaveTypeList}
              isContractRenewal={true}
              departmentTemByID={departmentTemByID}
              departmentTemplateDataByID={departmentTemplateDataByID}
              handleCreateOptionContractPolicy={
                handleCreateOptionContractPolicyData
              }
              handleCloseDialog={handleCloseDialog}
              regenerateEmp={handleRenewAndNotify}
            />
          }
        />
      </Box>
    </>
  );
}
