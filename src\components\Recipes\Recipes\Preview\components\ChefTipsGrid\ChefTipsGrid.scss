.chef-tips-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-xxl);

  @media (min-width: 768px) {
    grid-template-columns: 1fr 1fr;
  }

  &__card-wrap {
    background-color: var(--color-primary-opacity);
  }

  &__card {
    border-radius: var(--border-radius-md);
    border: var(--border-width-xs) solid var(--color-light-gray);
    box-shadow: var(--box-shadow-xs);
  }

  &__header {
    padding: var(--spacing-lg) var(--spacing-xxl);
    border-bottom: var(--border-width-xs) solid var(--color-light-gray);
  }

  &__title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }

  &__head-chef-title-text {
    color: var(--color-primary);
  }

  &__foh-wrap {
    background-color: var(--color-warning-opacity);
  }

  &__foh-title-wrap{
color: var(--color-warning);
  }

  &__content {
    padding: var(--spacing-lg);
  }

  &__text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--text-color-black);
  }
}
