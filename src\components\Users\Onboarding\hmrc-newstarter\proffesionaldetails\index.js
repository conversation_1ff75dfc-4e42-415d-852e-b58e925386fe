import { Box, Typography, Divider } from '@mui/material';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomDatePicker from '@/components/UI/CustomDatePicker';
import dayjs from 'dayjs';

export default function ProffesionalDetails({
  touched,
  errors,
  values,
  handleChange,
  setFieldValue,
  handleBlur,
  ViewAccessOnly,
}) {
  return (
    <Box>
      <Typography className="title-text pt16">
        Please Provide most recent two employer details (If you have not worked
        in recent past you may provide two references of personal or
        professional contacts)
      </Typography>
      <Box className="display-grid-proffesional pt8">
        <Box>
          <CustomTextField
            fullWidth
            id="pf_name1"
            name="pf_name1"
            value={values?.pf_name1}
            label="Name & Contact Details"
            placeholder="Enter Name & Contact Details"
            error={Boolean(touched.pf_name1 && errors.pf_name1)}
            helperText={touched.pf_name1 && errors.pf_name1}
            onBlur={handleBlur}
            rows={3}
            multiline
            onChange={handleChange}
            disabled={ViewAccessOnly}
            className="additional-textfeild"
          />
        </Box>
        <Box>
          <Box>
            <CustomDatePicker
              label={<span>Start Date</span>}
              disabled={ViewAccessOnly}
              error={Boolean(touched.pf_start_date1 && errors.pf_start_date1)}
              helperText={touched.pf_start_date1 && errors.pf_start_date1}
              name="pf_start_date1"
              value={dayjs(values?.pf_start_date1)}
              onBlur={handleBlur}
              disableFuture
              // OpenDialog={handleDatePickerOpen}
              // CloseDialog={handleDatePickerClose}
              onChange={(date) => {
                setFieldValue('pf_start_date1', date);
              }}
              inputVariant="outlined"
              date
              format="DD/MM/YYYY"
              // disabled={isDisabled}
            />
          </Box>
          <Box className="pt8">
            <CustomDatePicker
              label={<span>End date</span>}
              minDate={dayjs(values?.pf_start_date1)}
              error={Boolean(touched.pf_end_date1 && errors.pf_end_date1)}
              helperText={touched.pf_end_date1 && errors.pf_end_date1}
              name="pf_end_date1"
              value={dayjs(values?.pf_end_date1)}
              onBlur={handleBlur}
              // OpenDialog={handleDatePickerOpen}
              // CloseDialog={handleDatePickerClose}
              onChange={(date) => {
                setFieldValue('pf_end_date1', date);
              }}
              format="DD/MM/YYYY"
              disableFuture
              inputVariant="outlined"
              date
              disabled={ViewAccessOnly}
            />
          </Box>
        </Box>
        <Box></Box>
      </Box>
      <Box className="display-grid-proffesional-desc pt8">
        <CustomTextField
          id="pf_description1"
          name="pf_description1"
          onChange={handleChange}
          fullWidth
          multiline
          rows={2}
          error={Boolean(touched.pf_description1 && errors.pf_description1)}
          helperText={touched.pf_description1 && errors.pf_description1}
          placeholder="Enter Your Role Description & Duration you Worked"
          value={values?.pf_description1}
          className="additional-textfeild"
          label="Your Role Description & Duration you Worked"
          disabled={ViewAccessOnly}
        />
      </Box>
      <Divider className="mb16 mt16" />{' '}
      <Box className="display-grid-proffesional pt8">
        <Box>
          <CustomTextField
            fullWidth
            id="pf_name2"
            name="pf_name2"
            value={values?.pf_name2}
            label="Name & Contact Details"
            rows={3}
            className="additional-textfeild"
            multiline
            placeholder="Enter Name & Contact Details"
            error={Boolean(touched.pf_name2 && errors.pf_name2)}
            helperText={touched.pf_name2 && errors.pf_name2}
            onBlur={handleBlur}
            onChange={handleChange}
            disabled={ViewAccessOnly}
          />
        </Box>
        <Box>
          <Box>
            <CustomDatePicker
              label={<span>Start Date</span>}
              error={Boolean(touched.pf_start_date2 && errors.pf_start_date2)}
              helperText={touched.pf_start_date2 && errors.pf_start_date2}
              name="pf_start_date2"
              value={dayjs(values?.pf_start_date2)}
              onBlur={handleBlur}
              disableFuture
              // OpenDialog={handleDatePickerOpen}
              // CloseDialog={handleDatePickerClose}
              onChange={(date) => {
                setFieldValue('pf_start_date2', date);
              }}
              inputVariant="outlined"
              date
              format="DD/MM/YYYY"
              disabled={ViewAccessOnly}
            />
          </Box>
          <Box className="pt8">
            <CustomDatePicker
              label={<span>End date</span>}
              error={Boolean(touched.pf_end_date2 && errors.pf_end_date2)}
              helperText={touched.pf_end_date2 && errors.pf_end_date2}
              name="pf_end_date2"
              value={dayjs(values?.pf_end_date2)}
              onBlur={handleBlur}
              minDate={dayjs(values?.pf_start_date2)}
              // OpenDialog={handleDatePickerOpen}
              // CloseDialog={handleDatePickerClose}
              onChange={(date) => {
                setFieldValue('pf_end_date2', date);
              }}
              inputVariant="outlined"
              date
              disableFuture
              format="DD/MM/YYYY"
              disabled={ViewAccessOnly}
            />
          </Box>
        </Box>
        <Box></Box>
      </Box>
      <Box className="display-grid-proffesional-desc pt8">
        <CustomTextField
          id="pf_description2"
          name="pf_description2"
          onChange={handleChange}
          multiline
          rows={2}
          fullWidth
          error={Boolean(touched.pf_description2 && errors.pf_description2)}
          helperText={touched.pf_description2 && errors.pf_description2}
          placeholder="Enter Your Role Description & Duration you Worked"
          value={values?.pf_description2}
          className="additional-textfeild"
          label="Your Role Description & Duration you Worked"
          disabled={ViewAccessOnly}
        />
      </Box>
    </Box>
  );
}
