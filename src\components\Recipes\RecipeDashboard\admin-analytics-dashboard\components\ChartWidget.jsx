'use client';

import React, { useState } from 'react';
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';
import Icon from '@/components/UI/AppIcon/AppIcon';
import DialogBox from '@/components/UI/Modalbox';
import NoDataView from '@/components/UI/NoDataView';
import './chartwidget.scss';

const ChartWidget = ({
  id,
  title = 'Chart Widget',
  type = 'line',
  data = [],
  dateRange = '7days',
  onListViewClick,
}) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [chartOptions, setChartOptions] = useState({
    showGrid: true,
    showTooltip: true,
    animate: true,
  });

  const colors = {
    primary: 'var(--color-primary)',
    secondary: 'var(--color-secondary)',
    accent: 'var(--color-orange)',
    success: 'var(--color-success)',
    warning: 'var(--color-warning)',
    error: 'var(--color-danger)',
  };

  // Use provided data or show empty state
  const chartData = data?.length > 0 ? data : [];

  const renderLineChart = () => {
    const hasData = chartData?.length > 0;

    // If no data, show clean NoDataView without chart structure
    if (!hasData) {
      return (
        <div
          style={{
            position: 'relative',
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <NoDataView
            image={<Icon name="BarChart3" size={48} />}
            title="No Chart Data Available"
            description="Data will appear here once analytics are collected."
            className="no-data-auto-margin-height-conainer"
          />
        </div>
      );
    }

    // With data, render normal chart
    return (
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={chartData}>
          {chartOptions?.showGrid && (
            <CartesianGrid
              strokeDasharray="3 3"
              stroke="var(--color-light-gray)"
            />
          )}
          <XAxis
            dataKey="name"
            stroke="var(--color-muted)"
            fontSize={12}
            tickLine={false}
            height={80}
            interval={0}
            tick={{ fontSize: 11, textAnchor: 'end' }}
            angle={-45}
          />
          <YAxis
            stroke="var(--color-muted)"
            fontSize={12}
            tickLine={false}
            axisLine={false}
          />
          {chartOptions?.showTooltip && (
            <Tooltip
              contentStyle={{
                backgroundColor: 'var(--color-white)',
                border: '1px solid var(--color-light-gray)',
                borderRadius: 'var(--border-radius-md)',
                boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
              }}
              labelFormatter={(label, payload) => {
                // If payload has fullName, use it, otherwise use the label
                const dataPoint = payload?.[0]?.payload;
                return dataPoint?.fullName || label;
              }}
              formatter={(value, name) => [value, name]}
            />
          )}
          <Line
            type="monotone"
            dataKey="views"
            stroke={colors?.primary}
            strokeWidth={2}
            dot={{ fill: colors?.primary, strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: colors?.primary, strokeWidth: 2 }}
            animationDuration={chartOptions?.animate ? 1000 : 0}
          />
        </LineChart>
      </ResponsiveContainer>
    );
  };

  const renderBarChart = () => {
    const hasData = chartData?.length > 0;

    // If no data, show clean NoDataView without chart structure
    if (!hasData) {
      return (
        <div
          style={{
            position: 'relative',
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <NoDataView
            image={<Icon name="BarChart3" size={48} />}
            title="No Chart Data Available"
            description="Data will appear here once analytics are collected."
            className="no-data-auto-margin-height-conainer"
          />
        </div>
      );
    }

    // With data, render normal chart
    return (
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={chartData}>
          {chartOptions?.showGrid && (
            <CartesianGrid
              strokeDasharray="3 3"
              stroke="var(--color-light-gray)"
            />
          )}
          <XAxis
            dataKey="name"
            stroke="var(--color-muted)"
            fontSize={12}
            tickLine={false}
            height={80}
            interval={0}
            tick={{ fontSize: 11, textAnchor: 'end' }}
            angle={-45}
          />
          <YAxis
            stroke="var(--color-muted)"
            fontSize={12}
            tickLine={false}
            axisLine={false}
          />
          {chartOptions?.showTooltip && (
            <Tooltip
              contentStyle={{
                backgroundColor: 'var(--color-white)',
                border: '1px solid var(--color-light-gray)',
                borderRadius: 'var(--border-radius-md)',
                boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
              }}
              labelFormatter={(label, payload) => {
                // If payload has fullName, use it, otherwise use the label
                const dataPoint = payload?.[0]?.payload;
                return dataPoint?.fullName || label;
              }}
              formatter={(value, name) => [value, name]}
            />
          )}
          <Bar
            dataKey="recipes"
            fill={colors?.primary}
            radius={[4, 4, 0, 0]}
            animationDuration={chartOptions?.animate ? 1000 : 0}
          />
          <Bar
            dataKey="views"
            fill={colors?.accent}
            radius={[4, 4, 0, 0]}
            animationDuration={chartOptions?.animate ? 1000 : 0}
          />
        </BarChart>
      </ResponsiveContainer>
    );
  };

  const renderHeatmap = () => (
    <div className="chart-widget__heatmap-grid">
      {chartData?.map((item, index) => (
        <div
          key={index}
          className="chart-widget__heatmap-cell"
          style={{
            backgroundColor: `rgba(19, 94, 150, ${item?.value / 100})`, // Using primary color with opacity
            color:
              item?.value > 50 ? 'var(--color-white)' : 'var(--color-dark)',
          }}
        >
          <span className="chart-widget__heatmap-hour">{item?.hour}</span>
          <span className="chart-widget__heatmap-day">{item?.day}</span>
          <span className="chart-widget__heatmap-value">{item?.value}</span>
        </div>
      ))}
    </div>
  );

  const renderFunnelChart = () => (
    <div className="chart-widget__funnel-grid" data-stages={chartData?.length}>
      {chartData?.map((stage, index) => {
        // Calculate proper width and opacity for each stage
        const percentage = stage?.percentage || 0;
        // For the first stage, ensure it's always 100% width if it has the highest value
        const isFirstStage = index === 0;
        // Only show progress bar if percentage is greater than 0
        const barWidth =
          percentage > 0
            ? isFirstStage
              ? Math.max(percentage, 95)
              : Math.max(percentage, 5)
            : 0;
        const barOpacity = Math.max(1 - index * 0.1, 0.4); // Better opacity progression

        return (
          <div key={index} className="chart-widget__funnel-stage">
            <div className="chart-widget__funnel-header">
              <span className="chart-widget__funnel-stage-name">
                {stage?.stage}
              </span>
              <span className="chart-widget__funnel-stage-percent">
                {percentage}%
              </span>
            </div>
            <div className="chart-widget__funnel-bar-bg">
              {percentage > 0 && (
                <div
                  className="chart-widget__funnel-bar"
                  style={{
                    width: `${barWidth}%`,
                    backgroundColor: colors?.primary,
                    opacity: barOpacity,
                  }}
                />
              )}
            </div>
            <span className="chart-widget__funnel-count">
              {(stage?.count || 0).toLocaleString()} users
            </span>
          </div>
        );
      })}
    </div>
  );

  const renderEmptyState = () => (
    <NoDataView
      image={<Icon name="BarChart3" size={48} />}
      title="No Chart Data Available"
      description="Data will appear here once analytics are collected."
      className="no-data-auto-margin-height-conainer"
    />
  );

  const renderChart = () => {
    // For line charts, always show the chart structure
    if (type === 'linea') {
      return renderLineChart(); // Line chart will handle no data internally
    }

    // For bar charts, always show the chart structure (same as line chart)
    if (type === 'bar') {
      return renderBarChart(); // Bar chart will handle no data internally
    }

    // For other chart types, use existing logic
    if (!chartData?.length) {
      // For other chart types, show empty state
      return renderEmptyState();
    }

    switch (type) {
      case 'heatmap':
        return renderHeatmap();
      case 'funnel':
        return renderFunnelChart();
      default:
        return renderLineChart();
    }
  };

  const toggleChartOption = (option) => {
    setChartOptions((prev) => ({
      ...prev,
      [option]: !prev[option],
    }));
  };

  return (
    <div
      className={`chart-widget${type === 'funnel' ? ' chart-widget--funnel' : ''}`}
    >
      {/* Widget Header */}
      <div className="chart-widget__header">
        <div className="chart-widget__header-info">
          <h3 className="sub-header-text">{title}</h3>
          <p className="chart-widget__subtitle">
            {dateRange === 'last_7_days'
              ? 'Last 7 days'
              : dateRange === 'last_30_days'
                ? 'Last 30 days'
                : dateRange === 'last_90_days'
                  ? 'Last 90 days'
                  : dateRange === 'last_year'
                    ? 'Last year'
                    : dateRange === 'current_year'
                      ? 'Current year'
                      : 'Custom range'}
          </p>
        </div>

        <div className="chart-widget__options">
          {/* Chart Options - Hide play button for funnel charts (Conversion Analytics) */}
          {type !== 'funnel' && (
            <div className="chart-widget__controls">
              <button
                onClick={() => toggleChartOption('showGrid')}
                className={`chart-widget__option-btn${
                  chartOptions?.showGrid
                    ? ' chart-widget__option-btn--active'
                    : ''
                }`}
                title="Toggle grid"
              >
                <Icon name="Play" size={16} />
              </button>
            </div>
          )}

          {/* List View Button - Only for Recipe Views Trend */}
          {id === 'recipe-views' && onListViewClick && (
            <button
              onClick={() => onListViewClick(id)}
              className="chart-widget__list-view-btn"
              title="View detailed list"
            >
              <Icon name="List" size={16} />
            </button>
          )}

          <button
            onClick={() => setIsDialogOpen(true)}
            className="chart-widget__fullscreen-btn"
            title="View in fullscreen"
          >
            <Icon name="Maximize2" size={16} />
          </button>
        </div>
      </div>

      {/* Chart Content */}
      <div
        className={`chart-widget__content${
          type === 'funnel' ? ' chart-widget__content--funnel' : ''
        }`}
      >
        {renderChart()}
      </div>

      {/* Chart Footer - Hide for funnel charts (Conversion Analytics) */}
      {type !== 'funnel' && (
        <div className="chart-widget__footer">
          <div className="chart-widget__legend">
            {type === 'line' && (
              <>
                <div className="chart-widget__legend-item">
                  <div
                    className="chart-widget__legend-color"
                    style={{ backgroundColor: colors?.primary }}
                  ></div>
                  <span className="chart-widget__legend-text">Views</span>
                </div>
              </>
            )}
            {type === 'bar' && (
              <>
                <div className="chart-widget__legend-item">
                  <div
                    className="chart-widget__legend-color"
                    style={{ backgroundColor: colors?.primary }}
                  ></div>
                  <span className="chart-widget__legend-text">Recipes</span>
                </div>
                <div className="chart-widget__legend-item">
                  <div
                    className="chart-widget__legend-color"
                    style={{ backgroundColor: colors?.accent }}
                  ></div>
                  <span className="chart-widget__legend-text">Views</span>
                </div>
              </>
            )}
          </div>
        </div>
      )}

      {/* Fullscreen Dialog */}
      <DialogBox
        open={isDialogOpen}
        handleClose={() => setIsDialogOpen(false)}
        title={title}
        className="dialog-box-container"
        content={
          <div className="chart-widget__fullscreen-content">
            {/* Chart Content in Dialog */}
            <div className="chart-widget__fullscreen-chart">
              <div className="chart-widget__fullscreen-chart-container">
                {renderChart()}
              </div>
            </div>

            {/* Chart Controls in Dialog */}
            <div className="chart-widget__fullscreen-controls">
              {type !== 'funnel' && (
                <div className="chart-widget__controls">
                  <button
                    onClick={() => toggleChartOption('showGrid')}
                    className={`chart-widget__option-btn${
                      chartOptions?.showGrid
                        ? ' chart-widget__option-btn--active'
                        : ''
                    }`}
                    title="Toggle grid"
                  >
                    <Icon name="Play" size={16} />
                  </button>
                </div>
              )}

              {/* Legend in Dialog */}
              <div className="chart-widget__legend">
                {type === 'line' && (
                  <>
                    <div className="chart-widget__legend-item">
                      <div
                        className="chart-widget__legend-color"
                        style={{ backgroundColor: colors?.primary }}
                      ></div>
                      <span className="chart-widget__legend-text">Views</span>
                    </div>
                  </>
                )}
                {type === 'bar' && (
                  <>
                    <div className="chart-widget__legend-item">
                      <div
                        className="chart-widget__legend-color"
                        style={{ backgroundColor: colors?.primary }}
                      ></div>
                      <span className="chart-widget__legend-text">Recipes</span>
                    </div>
                    <div className="chart-widget__legend-item">
                      <div
                        className="chart-widget__legend-color"
                        style={{ backgroundColor: colors?.accent }}
                      ></div>
                      <span className="chart-widget__legend-text">Views</span>
                    </div>
                  </>
                )}
                {type === 'funnel' && chartData?.length > 0 && (
                  <>
                    <div className="chart-widget__legend-item">
                      <div
                        className="chart-widget__legend-color"
                        style={{ backgroundColor: colors?.primary }}
                      ></div>
                      <span className="chart-widget__legend-text">
                        Conversion Stages
                      </span>
                    </div>
                    <div className="chart-widget__legend-item">
                      <span className="chart-widget__legend-text">
                        {chartData?.length} stages tracked
                      </span>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        }
      />
    </div>
  );
};

export default ChartWidget;
