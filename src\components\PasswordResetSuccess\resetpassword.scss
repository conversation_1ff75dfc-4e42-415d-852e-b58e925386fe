.reset-password-wrap {
  padding: 0px 20px;

  .reset-password {
    width: 100%;
    max-width: 550px;
    margin: 0 auto;
    border-radius: 8px;
    text-align: center;
    background-color: white;
    border-top-left-radius: 22px;
    border-top-right-radius: 22px;
    padding: 52px;
    height: 100%;
    min-height: 500px;
    box-shadow: 0px 0px 110px 0px #0260e133;

    .reset-icon-wrap {
      height: 105px;
      width: 115px;

      @media (max-width: 475px) {
        width: 80px;
        height: 80px;
      }
    }

    .reset-text-wrap {
      .text-wrap {
        padding: 32px 0px 22px;
        color: var(--text-color-primary) !important;
      }

      .reset-text {
        text-align: center;
        padding-bottom: 32px;
        width: 100%;
        max-width: 486px;
        margin: 0 auto;
      }

      @media (max-width: 337px) {
        .heading-text {
          line-height: 36px;
        }
      }
    }

    .back-to-login-btn-wrap {
      .back-to-login-btn {
        font-size: 16px;
        line-height: 20px;
        font-weight: 300;
        padding: 16px !important;
        text-align: center;
        width: 100%;
        max-width: 486px;
        background-color: var(--color-primary) !important;
        color: #ffffff !important;
        border-radius: 12px !important;
        border: none !important;
        font-family: var(--font-family-poly-median) !important;

        &:hover {
          background-color: var(--color-primary) !important;
          color: #ffffff;
          box-shadow: none !important;
          font-family: var(--font-family-poly-median) !important;
        }

        @media (max-width: 575px) {
          padding: 14px 0px !important;
        }
      }
    }

    @media (max-width: 575px) {
      padding: 50px 14px;
      min-height: 100%;
    }
  }
}
