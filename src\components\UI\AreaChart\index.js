import React, { useState, useEffect } from 'react';
import { Box } from '@mui/material';
import { AgCharts } from 'ag-charts-react';

const AreaChart = ({ item }) => {
  const [options, setOptions] = useState();

  useEffect(() => {
    if (item && item?.data) {
      setOptions(item);
    }
  }, [item]);

  return (
    <>
      <Box className="">
        <AgCharts options={options} />
      </Box>
    </>
  );
};

export default AreaChart;
