.download-popover {
  .MuiPaper-root {
    max-width: 350px;
    width: 100%;
    margin-top: var(--spacing-sm);
    padding: 10px 20px;
  }

  .export-option {
    p {
      text-align: center;
    }
  }
}
.add-holiday-popover {
  .MuiPaper-root {
    max-width: 350px;
    width: 100%;
    margin-top: var(--spacing-sm);
    padding: 10px 20px;
  }
  .holiday-option {
    svg {
      margin-right: var(--spacing-sm);
      width: var(--icon-size-sm);
      height: var(--icon-size-sm);
      margin-top: var(--spacing-sm);
      fill: var(--icon-color-primary);
    }
  }
}
.add-leave-wrap {
  .display-grid {
    display: grid;
    grid-template-columns: 49% 49%;
    column-gap: 15px;
    row-gap: 15px;

    @media (max-width: 899px) {
      display: block;
    }
  }
}
.show-details {
  background-color: var(--color-light-gray) !important;
  width: 100%;
  max-width: 250px !important;
  padding: var(--spacing-sm) !important;
  .MuiTooltip-arrow {
    color: var(--color-light-gray) !important;
  }
}
