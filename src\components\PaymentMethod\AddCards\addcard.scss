@import '@/styles/variable.scss';

// .add-card-container {
//   .add-card-header {
//     font-size: 20px;

//     .add-card-text-wrap {
//       @media (max-width: 575px) {
//         width: 100%;
//       }
//     }

//     .card-select {
//       .MuiSelect-select {
//         padding: 3px 7px 0px 15px;
//         min-width: 150px;
//         margin-top: 0px;
//       }

//       fieldset {
//         height: 31px;
//         margin-top: 4px;
//       }

//       .placeholder {
//         font-weight: 300 !important;
//         padding-top: 2px;
//       }

//       .MuiSvgIcon-root {
//         margin-top: 1px;
//       }

//       @media (max-width: 575px) {
//         width: 100%;
//       }
//     }

//     @media (max-width: 575px) {
//       flex-direction: column;
//       row-gap: 10px;
//     }
//   }

//   .add-cards {
//     margin-top: 20px;
//     gap: 15px;

//     .add-card-wrap {
//       background-color: #fff;
//       padding: 17.5px 24px;
//       border: 1px solid #e0e0e0;
//       border-radius: 12px;
//       box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.05);

//       .card-content-wrap {
//         padding: 0px;

//         .card-content {
//           .card-number {
//             font-size: 16px;
//             font-weight: 500;

//             @media (max-width: 575px) {
//               font-size: 14px;
//             }
//           }

//           .card-type-edit-wrap {
//             font-size: 14px;
//             color: $color-Dark-20;
//             gap: 10px;

//             .card-type,
//             .edit-wrap {
//               font-size: 14px;
//             }

//             .edit-wrap {
//               cursor: pointer;
//               text-decoration: underline;
//             }
//           }
//         }

//         .card-radio {
//           padding: 0px;
//           color: $color-primary;
//         }

//         @media (max-width: 575px) {
//           gap: 10px;
//         }
//       }

//       .card-brand-wrap {
//         text-align: right;

//         .set-default-text {
//           font-size: 14px;
//           cursor: pointer;
//         }

//         .default-card-text {
//           border: 1px solid $color-primary;
//           background-color: $color-primary;
//           color: $color-secondary;
//           padding: 0px 5px;
//           border-radius: 5px;
//           font-size: 14px;
//           cursor: pointer;
//         }
//         .default-card {
//           fill: $color-primary;
//           width: 24px;
//           height: 24px;
//         }
//         .card-brand {
//           font-size: 14px;
//           font-weight: 500;
//         }
//       }

//       @media (max-width: 575px) {
//         padding: 24px 14px;
//       }
//     }
//   }

//   .add-new-btn-wrap {
//     .add-new-btn {
//       padding: 4px 10px !important;
//       font-size: 14px !important;
//       background-color: #ffffff;
//       color: $color-primary !important;

//       &:hover {
//         box-shadow: none !important;
//       }
//     }
//   }
// }
// .plan-payment-wrap {
//   .add-card-header {
//     .public-link-sec {
//       width: 100%;
//       gap: 8px;
//       .generate-password-btn {
//         svg {
//           width: 24px;
//           height: 24px;
//           margin-left: 16px;
//         }
//       }
//     }
//   }
// }
.payment-details-sec {
  margin-bottom: var(--spacing-base);
  .pay-tabs-wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ededed;
    background-color: var(--color-alice-blue);
    border-radius: var(--border-radius-md);

    .report-tabs {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // border-bottom: 1px solid red;
      overflow: auto;

      .tab-list-sec {
        min-height: 42px;
        .tab-name {
          text-transform: none;
          font-family: $PrimaryFont;
          font-size: 14px;
          line-height: 20px;
          font-weight: 600;
          color: #000000;
          opacity: 1;
          width: max-content;
          padding: var(--spacing-none) var(--spacing-sm);
          margin: var(--spacing-xs) var(--spacing-xs) var(--spacing-xs)
            var(--spacing-xsm);
          min-height: 28px;

          @media (max-width: 1200px) {
            width: max-content;
            // min-width: 50%;
          }
        }

        .MuiTabs-flexContainer {
          align-items: center;
          min-height: 42px;
          // justify-content: space-between;
        }

        .MuiTabs-indicator {
          display: none;
          background-color: var(--color-bright-blue);
          height: 3px;
        }

        .Mui-selected {
          // margin: var(--spacing-none) var(--spacing-xsm);
          background-color: var(--color-white);
          // margin: var(--spacing-xs) var(--spacing-sm);
          // margin: var(--spacing-xsm) var(--spacing-sm);
          color: var(--text-bright-blue);
          border-radius: var(--border-radius-md);
          box-shadow: var(--tab-box-shadow-xs);
        }

        .MuiTabs-scroller {
          display: block !important;
        }

        .MuiTabScrollButton-root {
          display: block !important;

          .MuiSvgIcon-root {
            margin: 14px;
          }
        }
      }
    }
  }
}
