@import '@/styles/variable.scss';

.payment-pro-wrap {
  .identifier-table-container {
    border: 1px solid $color-Dark-10;
    border-radius: 15px;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;

    .identifier-table {
      width: 100%;
      border-collapse: unset;
      min-width: 940px;

      .table-body-wrap {
        .MuiTableRow-root:last-child {
          .MuiTableCell-root {
            border-bottom: none;
          }
        }

        .MuiTableCell-root {
          color: $color-Dark-40;
        }

        .plan-name-wrap {
          color: $color-Dark-60;
        }

        .provider-name {
          color: $color-Dark-40;
        }
      }
      .action-icons {
        width: 50px;
        text-align: start;
        .icon {
          padding: 0;

          .edit-icon,
          .delete-icon {
            fill: $color-primary;
            height: 21px;
            width: 21px;
          }
        }
        .icon:first-child {
          margin-right: 8px;
        }
      }
    }

    .pagination-wrap {
      padding: 22px !important;
    }
  }
}
