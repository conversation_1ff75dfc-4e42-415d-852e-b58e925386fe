import { Box, Typography } from '@mui/material';
import CustomTextField from '@/components/UI/CustomTextField';

export default function BankDetails({
  touched,
  errors,
  values,
  handleChange,
  setFieldValue,
  handleBlur,
  ViewAccessOnly,
}) {
  const formatInput = (value) => {
    const numericValue = value.replace(/\D/g, '');
    const parts = [];
    for (let i = 0; i < numericValue.length; i += 2) {
      parts.push(numericValue.substring(i, i + 2));
    }

    return parts.join('-');
  };

  return (
    <Box>
      <Typography className="title-text pt16">
        In accordance with the condition of employement, I give below details of
        my personal Account so that my salary may be paid into the account by
        direct credit transfer.
      </Typography>

      <Box className="display-grid pt8">
        <Box>
          <CustomTextField
            fullWidth
            id="bi_acname"
            name="bi_acname"
            value={values?.bi_acname}
            label="Account Holder Name"
            placeholder="Enter Account Holder Name"
            required
            error={Boolean(touched.bi_acname && errors.bi_acname)}
            helperText={touched.bi_acname && errors.bi_acname}
            onBlur={handleBlur}
            onChange={handleChange}
            disabled={ViewAccessOnly}
          />
        </Box>
        <Box>
          <CustomTextField
            fullWidth
            id="bi_acnumber"
            name="bi_acnumber"
            value={values?.bi_acnumber}
            label="Account Number"
            placeholder="Enter Account Number"
            required
            error={Boolean(touched.bi_acnumber && errors.bi_acnumber)}
            helperText={touched.bi_acnumber && errors.bi_acnumber}
            onBlur={handleBlur}
            onChange={(e) => {
              if (e.target.value === '' || e.target.value?.length < 9) {
                handleChange(e);
              }
            }}
            onInput={(e) => {
              e.target.value = e.target.value.replace(/[^0-9]/g, ''); // Remove non-numeric characters
            }}
            disabled={ViewAccessOnly}
          />
        </Box>
        <Box>
          <CustomTextField
            fullWidth
            id="bi_sortcode"
            name="bi_sortcode"
            value={values?.bi_sortcode}
            label="Sort Code"
            placeholder="Enter Sort Code"
            required
            error={Boolean(touched.bi_sortcode && errors.bi_sortcode)}
            helperText={touched.bi_sortcode && errors.bi_sortcode}
            onBlur={handleBlur}
            disabled={ViewAccessOnly}
            onChange={(e) => {
              if (e.target.value === '' || e.target.value?.length < 7) {
                // handleChange(e);
                const rawValue = e.target.value;
                const formattedValue = formatInput(rawValue);
                setFieldValue('bi_sortcode', formattedValue);
              }
            }}
            onInput={(e) => {
              e.target.value = e.target.value.replace(/[^0-9]/g, ''); // Remove non-numeric characters
            }}
          />
        </Box>
        <Box>
          <CustomTextField
            fullWidth
            id="bi_bankdetails"
            name="bi_bankdetails"
            value={values?.bi_bankdetails}
            label="Bank/Building Society Name"
            placeholder="Enter Bank/Building Society Name "
            error={Boolean(touched.bi_bankdetails && errors.bi_bankdetails)}
            helperText={touched.bi_bankdetails && errors.bi_bankdetails}
            onBlur={handleBlur}
            onChange={handleChange}
            disabled={ViewAccessOnly}
          />
        </Box>
      </Box>
      <Box className="display-grid-50 pt8">
        <CustomTextField
          fullWidth
          id="bi_address"
          name="bi_address"
          value={values?.bi_address}
          label="Bank Address"
          placeholder="Enter Bank Address"
          error={Boolean(touched.bi_address && errors.bi_address)}
          helperText={touched.bi_address && errors.bi_address}
          onBlur={handleBlur}
          onChange={handleChange}
          disabled={ViewAccessOnly}
        />
      </Box>
    </Box>
  );
}
