'use client';

import React, { useEffect, useState, useContext } from 'react';
import { Box, Typography } from '@mui/material';
import CustomButton from '@/components/UI/CustomButton';
import AuthContext from '@/helper/authcontext';
import { useRouter } from 'next/navigation';
import { fetchFromStorage, saveToStorage } from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import {
  setApiMessage,
  TotalOfDSRArray,
  removeVAT,
  DateFormat,
} from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import CustomDatePicker from '@/components/UI/CustomDatePicker';
import DSRCategory from './dsrCategory';
import _ from 'lodash';
import moment from 'moment';
import dayjs from 'dayjs';

export default function EditDSR({ dsrId }) {
  const { authState, userdata, setUserdata } = useContext(AuthContext);
  const [DsrDetails, setDsrDetails] = useState();
  const [loader, setLoader] = useState(false);
  const [updateLoader, setUpdateLoader] = useState(false);
  const [random, setRandom] = useState();
  const [randomother, setRandomother] = useState();
  const [randomvat, setRandomvat] = useState();
  const [DsrDataIncome, setDsrDataIncome] = useState([]);
  const [DsrDataOther, setDsrDataOther] = useState([]);
  const [VatValue, setVATValue] = useState([0, 20]);
  const [VAT, setVAT] = useState({
    NoneVat: '',
    VAT1: '',
    VAT2: '',
    AmountVAT1: '',
    AmountVAT2: '',
    diff1: '',
    diff2: '',
    TotalIncome: '',
  });
  const [OldVAT, seOldtVAT] = useState({
    NoneVat: '',
    VAT1: '',
    VAT2: '',
    AmountVAT1: '',
    AmountVAT2: '',
    diff1: '',
    diff2: '',
    TotalIncome: '',
  });
  const [currency, setCurrency] = useState();
  const [isSubmit, setIsSubmit] = useState(false);
  const [sdate, setDate] = useState();
  // const [checkloader, setCheckLoader] = useState(false);
  // const [DsrExist, setISDsr] = useState(false);
  const [VatIsView, setVatIsView] = useState(true);
  const [IsRemarkView, setIsRemarkView] = useState(true);

  const router = useRouter();
  const ValueOfTotal = (value) => {
    const parsedValue = parseFloat(value);
    if (!Number.isNaN(parsedValue)) {
      return parsedValue;
    }
    // if (parseFloat(value) && parseFloat(value) !== NaN) {
    //   return parseFloat(value);
    // }
    return 0;
  };
  //Total without VAT
  const TotalWithoutVAT = () => {
    return ValueOfTotal(
      parseFloat(
        removeVAT(
          ValueOfTotal(parseFloat(VAT?.VAT2)),
          VatValue && VatValue?.[1] ? VatValue?.[1] : 0
        )?.netAmount +
          ValueOfTotal(parseFloat(VAT?.VAT1)) +
          ValueOfTotal(parseFloat(VAT?.NoneVat))
      )
    ).toFixed(2);
  };
  const UpdateDSR = async () => {
    setUpdateLoader(true);
    let dsrmerge = _.concat(DsrDataIncome, DsrDataOther);
    let vatCal = VAT;
    vatCal.TotalIncome = TotalOfDSRArray(DsrDataIncome)
      ? TotalOfDSRArray(DsrDataIncome)
      : 0;
    vatCal.AmountVAT1 =
      ValueOfTotal(parseFloat(VAT?.VAT2)) && VatValue && VatValue?.[1]
        ? removeVAT(ValueOfTotal(parseFloat(VAT?.VAT2)), VatValue?.[1])
            ?.vatAmount
        : 0;
    vatCal.AmountVAT2 =
      (ValueOfTotal(parseFloat(VAT?.VAT2)) ||
        ValueOfTotal(parseFloat(VAT?.VAT2)) === 0 ||
        ValueOfTotal(parseFloat(VAT?.VAT1)) ||
        ValueOfTotal(parseFloat(VAT?.VAT1)) === 0 ||
        ValueOfTotal(parseFloat(VAT?.NoneVat)) ||
        ValueOfTotal(parseFloat(VAT?.NoneVat)) === 0) &&
      VatValue &&
      VatValue?.[1]
        ? TotalWithoutVAT()
        : 0;
    vatCal.diff2 =
      VAT?.diff1 && VAT?.diff1 !== ''
        ? Math.abs(
            (
              removeVAT(ValueOfTotal(parseFloat(VAT?.VAT2)), VatValue?.[1])
                ?.vatAmount - ValueOfTotal(parseFloat(VAT?.diff1))
            ).toFixed(2)
          )
        : '';
    const sendData = {
      ...(authState?.web_user_active_role_id !== 7 &&
        authState?.web_user_active_role_id !== 14 && {
          dsr_date: dayjs(sdate).format('YYYY-MM-DD'),
        }),
      data: dsrmerge,
      dsr_amount_total: vatCal,
      old_dsr_amount_total: OldVAT,
      current_datetime: moment(Date()).format('YYYY-MM-DD HH:mm:ss'),
    };
    try {
      const { status, data } = await axiosInstance.put(
        URLS?.UPDATE_DSR + `${dsrId}`,
        sendData
      );

      if (status === 200) {
        if (data?.status) {
          setUpdateLoader(false);
          router?.push('/dsr');
          setApiMessage('success', data?.message);
        } else {
          setUpdateLoader(false);
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setUpdateLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // get DSR by id
  const getDSRData = async () => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_DSR_BY_ID + `${dsrId}`
      );
      if (status === 200) {
        setLoader(false);
        setDsrDetails(data?.data);
        setDate(data?.data?.dsr_date);
        const incomeData =
          data?.data &&
          data?.data?.dsrItems &&
          data?.data?.dsrItems?.length > 0 &&
          data?.data?.dsrItems?.filter(
            (f) => f?.payment_type_usage === 'income'
          );

        incomeData && incomeData?.length > 0
          ? setDsrDataIncome(incomeData)
          : setDsrDataIncome([]);
        const otherData =
          data?.data &&
          data?.data?.dsrItems &&
          data?.data?.dsrItems?.length > 0 &&
          data?.data?.dsrItems?.filter(
            (f) => f?.payment_type_usage === 'other'
          );

        otherData && otherData?.length > 0
          ? setDsrDataOther(otherData)
          : setDsrDataOther([]);
        if (data?.data?.dsr_amount_total) {
          let vats = JSON.parse(data?.data?.dsr_amount_total);
          let vatdata = JSON.parse(data?.data?.dsr_amount_total);
          vatdata.diff2 = Math.abs(
            (
              removeVAT(ValueOfTotal(parseFloat(vats?.VAT2)), VatValue?.[1])
                ?.vatAmount - ValueOfTotal(parseFloat(vats?.diff1))
            ).toFixed(2)
          );
          setVAT(vatdata);
          let vatss = JSON.parse(data?.data?.dsr_amount_total);
          let vatdatas = JSON.parse(data?.data?.dsr_amount_total);
          vatdata.diff2 = Math.abs(
            (
              removeVAT(ValueOfTotal(parseFloat(vatss?.VAT2)), VatValue?.[1])
                ?.vatAmount - ValueOfTotal(parseFloat(vatss?.diff1))
            ).toFixed(2)
          );
          seOldtVAT(vatdatas);
        }
        data?.vat_per_data &&
          data?.vat_per_data?.length > 0 &&
          setVATValue(data?.vat_per_data);
      }
    } catch (error) {
      setLoader(false);
      setDsrDetails();
      setDsrDataIncome([]);
      setDsrDataOther([]);
      setVAT();
      setVATValue();

      setApiMessage('error', error?.response?.data?.message);
    }
  };

  //Total of VAT amount
  const totalofALLVat = () => {
    const sum =
      ValueOfTotal(parseFloat(VAT?.NoneVat)) +
      ValueOfTotal(parseFloat(VAT?.VAT1)) +
      ValueOfTotal(parseFloat(VAT?.VAT2));
    return parseFloat(sum.toFixed(2));
  };
  //Total of Difference VAT amount
  // const totalofDiffVat = () => {
  //   const sum =
  //     ValueOfTotal(parseFloat(VAT?.diff1)) +
  //     ValueOfTotal(parseFloat(VAT?.diff2));
  //   return parseFloat(sum.toFixed(2));
  // };
  const DSRCheck = async (branch, date) => {
    // setCheckLoader(true);
    const sendData = {
      branch_id: branch,
      dsr_date: date
        ? dayjs(date).format('YYYY-MM-DD')
        : moment(Date()).format('YYYY-MM-DD'),
    };
    try {
      const { status, data } = await axiosInstance.post(
        URLS?.GET_DSR_CHECK,
        sendData
      );
      if (status === 200 || status === 202) {
        if (data?.status) {
          // setCheckLoader(false);
          // setISDsr(false);
        } else {
          // setCheckLoader(false);
          // setISDsr(true);
          setDate(DsrDetails?.dsr_date);
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      // setCheckLoader(false);
      // setISDsr(true);
      setDate(DsrDetails?.dsr_date);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  useEffect(() => {
    setDsrDataIncome(DsrDataIncome);
  }, [random]);
  useEffect(() => {
    setDsrDataOther(DsrDataOther);
  }, [randomother]);
  useEffect(() => {
    setVAT(VAT);
  }, [randomvat]);

  const oldDSR = fetchFromStorage(identifiers?.RedirectData)
    ? fetchFromStorage(identifiers?.RedirectData)
    : userdata;

  useEffect(() => {
    if (oldDSR?.IsFromUser === undefined && oldDSR?.page !== undefined) {
      setUserdata({ ...userdata, IsFromUser: true });
      saveToStorage(identifiers?.RedirectData, {
        ...oldDSR,
        IsFromUser: true,
      });
    }
  }, [oldDSR]);

  useEffect(() => {
    if (dsrId) {
      getDSRData();
    }
  }, [dsrId]);
  useEffect(() => {
    authState?.currency_details && setCurrency(authState?.currency_details);
  }, [authState]);

  return (
    <Box className="Add-dsr-section">
      <ArrowBackIosIcon
        className="mb8 cursor-pointer"
        onClick={() => {
          setTimeout(() => {
            router?.push('/dsr');
          }, 1000);
        }}
      />
      <Box>
        {(authState?.web_user_active_role_id === 7 ||
          authState?.web_user_active_role_id === 14) && (
          <>
            {DsrDetails?.dsr_date && (
              <Box className="d-flex align-center">
                <Typography className="title-text fw400">
                  <span className="fw600"> Date : </span>
                  <span>{DateFormat(DsrDetails?.dsr_date, 'dates')}</span>
                </Typography>
              </Box>
            )}
          </>
        )}

        {DsrDetails?.dsr_branch && DsrDetails?.dsr_branch?.branch_name && (
          <Box className="d-flex align-center pt4">
            <Typography className="title-text fw400">
              <span className="fw600"> Branch : </span>
              <span>{DsrDetails?.dsr_branch?.branch_name}</span>
            </Typography>
          </Box>
        )}

        {DsrDetails?.dsr_user && DsrDetails?.dsr_user?.user_full_name && (
          <Box className="d-flex align-center pt4">
            <Typography className="title-text fw400">
              <span className="fw600"> Submitted By : </span>
              <span>{DsrDetails?.dsr_user?.user_full_name}</span>
            </Typography>
          </Box>
        )}

        {DsrDetails?.dsr_detail_status && (
          <Box className="d-flex align-center pt4">
            <Typography className="title-text fw400">
              <span className="fw600"> Status : </span>
              <span>
                {
                  <span>
                    {DsrDetails?.dsr_detail_status === 'rejected' ? (
                      <span className="sub-title-text failed fw600 text-capital">
                        {' '}
                        {DsrDetails?.dsr_detail_status}{' '}
                      </span>
                    ) : DsrDetails?.dsr_detail_status === 'pending' ? (
                      <span className="sub-title-text draft fw600 text-capital">
                        {' '}
                        {DsrDetails?.dsr_detail_status}{' '}
                      </span>
                    ) : (
                      <span className="sub-title-text success fw600 text-capital">
                        {' '}
                        {DsrDetails?.dsr_detail_status}{' '}
                      </span>
                    )}
                  </span>
                }
              </span>
            </Typography>
          </Box>
        )}
      </Box>
      {authState?.web_user_active_role_id !== 7 &&
        authState?.web_user_active_role_id !== 14 &&
        authState?.UserPermission?.dsr === 2 && (
          <Box className="display-grid-branch edit-dsr pt8">
            <Box>
              <CustomDatePicker
                label={<span>Date(DD/MM/YYYY)</span>}
                name="date"
                value={dayjs(sdate)}
                disabled={loader}
                disableFuture={true}
                format="DD/MM/YYYY"
                onChange={(date) => {
                  setDate(date);
                  DsrDetails?.dsr_branch?.id &&
                    DSRCheck(DsrDetails?.dsr_branch?.id, date);
                }}
                inputVariant="outlined"
              />
            </Box>
            <Box></Box>
          </Box>
        )}
      <DSRCategory
        DsrDataIncome={DsrDataIncome}
        setVatIsView={setVatIsView}
        VatIsView={VatIsView}
        setIsRemarkView={setIsRemarkView}
        IsRemarkView={IsRemarkView}
        setIsSubmit={setIsSubmit}
        setDsrDataIncome={setDsrDataIncome}
        setRandom={setRandom}
        setDsrDataOther={setDsrDataOther}
        setRandomother={setRandomother}
        DsrDataOther={DsrDataOther}
        currency={currency}
        VAT={VAT}
        setVAT={setVAT}
        setRandomvat={setRandomvat}
        VatValue={VatValue}
        ValueOfTotal={ValueOfTotal}
        totalofALLVat={totalofALLVat}
        isSubmit={isSubmit}
        isEdit={true}
      />

      <Box className="mt24">
        {' '}
        <CustomButton
          variant="contained"
          onClick={() => {
            setIsSubmit(true);
            if (VatIsView && DsrDataIncome?.length > 0) {
              totalofALLVat() === TotalOfDSRArray(DsrDataIncome) &&
                (!isNaN(parseFloat(VAT?.VAT1)) ||
                  !isNaN(parseFloat(VAT?.VAT2)) ||
                  !isNaN(parseFloat(VAT?.NoneVat)) ||
                  TotalOfDSRArray(DsrDataIncome) === 0) &&
                UpdateDSR();
            } else {
              UpdateDSR();
            }
          }}
          disabled={updateLoader}
          title={`${updateLoader ? 'Updating...' : 'Update DSR'}`}
        />
      </Box>
    </Box>
  );
}
