import { IngredientIconSize } from '@/helper/common/commonFunctions';
import { RecipePlaceholder } from '@/helper/common/recipesImgPlaceholder';
import Image from 'next/image';
import { useState } from 'react';

const RecipesIcon = ({
  iconUrl,
  altText,
  imgWidth = IngredientIconSize,
  imgHeight = IngredientIconSize,
  objectFit = 'contain',
  ...props
}) => {
  const [imgError, setImgError] = useState(false);

  return (
    <>
      {!imgError ? (
        <Image
          src={iconUrl}
          alt={altText}
          width={imgWidth}
          height={imgHeight}
          {...props}
          style={{ objectFit: objectFit }}
          onError={() => setImgError(true)}
        />
      ) : (
        <div style={{ width: imgWidth, height: imgHeight }}>
          <RecipePlaceholder width={imgWidth} height={imgHeight} />
        </div>
      )}
    </>
  );
};

export default RecipesIcon;
