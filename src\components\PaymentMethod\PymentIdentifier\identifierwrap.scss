@import '@/styles/variable.scss';

.MuiDialog-container {
  .header-text {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 10px;
  }

  .form-wrap {
    .sub-plan-wraps {
      .MuiInputBase-root {
        min-height: 34px !important;

        .placeholder {
          margin-top: 3px;
          font-family: Inter, sans-serif;
          font-size: 16px;
          font-weight: 400 !important;
        }

        .MuiInputBase-input {
          padding: 5.8px 14px !important;
          margin-top: 0 !important;
          font-size: 14px !important;
        }

        fieldset {
          height: 38px !important;
          margin-top: 5px !important;
        }
      }

      .Mui-error {
        .MuiInputBase-input {
          &:focus-visible {
            outline: none !important;
          }
        }

        &:focus-visible {
          outline: 0 !important;
        }
      }
    }

    .selected-wrap {
      .MuiFormLabel-root {
        color: $color-Dark-80 !important;
      }

      .MuiInputBase-root {
        min-height: 33px !important;

        .placeholder {
          margin-top: 3px;
          font-family: Inter, sans-serif;
          font-size: 16px;
          font-weight: 400 !important;
        }

        .MuiInputBase-input {
          padding: 4px 14px !important;
          margin-top: 0 !important;
        }

        fieldset {
          height: 34px !important;
          margin-top: 6px !important;
        }

        .MuiSelect-select {
          font-size: 15px;
        }
      }
    }

    .error-border {
      border: 1px solid $error !important;
      border-radius: 8px !important;
    }
  }

  .create-plan-btn-wrap {
    .create-plan-btn {
      padding: 3px 20px 3px 12px !important;
      font-size: 14px !important;
      font-weight: 500 !important;

      &:hover {
        color: $color-secondary !important;
        box-shadow: none !important;
      }
    }
  }

  .error-message {
    color: $error !important;
    font-family: Inter, sans-serif !important;
    font-size: 12px !important;
    line-height: 18px !important;
    letter-spacing: -0.5px !important;
    font-weight: 600 !important;
    margin: 3px 14px 0px 14px;
  }

  .error-label {
    color: #d32f2f !important;
    font-family: Inter, sans-serif !important;
    font-size: 12px !important;
    line-height: 18px !important;
    letter-spacing: -0.5px !important;
    font-weight: 600 !important;
    color: $error;
  }
}
