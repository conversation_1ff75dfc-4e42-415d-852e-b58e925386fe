'use client';

import React, { useEffect, useState } from 'react';
import { Box, Divider, Tooltip, Typography } from '@mui/material';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import PreLoader from '@/components/UI/Loader';
import { URLS } from '@/helper/constants/urls';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import moment from 'moment';
import ViewIcon from '@/components/ActionIcons/ViewIcon';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import InfiniteScroll from 'react-infinite-scroll-component';
import CloseIcon from '@mui/icons-material/Close';
import HistoryIcon from '@mui/icons-material/History';
import DriveFileRenameOutlineIcon from '@mui/icons-material/DriveFileRenameOutline';
import '../contractview.scss';

export default function ContractView() {
  const router = useRouter();
  const { templateId } = useParams();
  const searchParams = useSearchParams();
  const [templateData, setTemplateData] = useState('');
  const [loader, setLoader] = useState(false);
  const [isViewHistory, setIsViewHistory] = useState(false);
  const [templateVersionData, setTemplateVersionData] = useState('');
  const isHistory = searchParams.get('history');

  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  // GET TEMPLATE DETAILS
  const getTemplateDetails = async (pageNumber = 1) => {
    templateData?.length === 0 && setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_TEMPLATE_VERSION +
          `?template_id=${templateId}&page=${pageNumber}&size=${10}`
      );

      if (status === 200) {
        const tempData = data?.data;
        setTemplateData((prevData) => [...prevData, ...tempData]);
        setHasMore(tempData?.length > 0);
        setLoader(false);
        // setTemplateData(tempData);
      }
    } catch (error) {
      setLoader(false);
      setTemplateData('');
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  useEffect(() => {
    getTemplateDetails(page);
  }, [page]);

  const handleViewVersion = (item) => {
    setTemplateVersionData(item);
    router.push(
      `/contract-view/${item?.emp_contract_template_id}?version=${item?.versionNumber}`
    );
  };

  const fetchMoreData = () => {
    setPage((prevPage) => prevPage + 1);
  };

  const latestVersion = templateVersionData
    ? templateVersionData
    : templateData?.[0];

  const handleContract = (type) => {
    if (type === 'edit') {
      router.push(`/edit-emp-contract/${templateId}`);
    } else if (type === 'history') {
      setIsViewHistory(!isViewHistory);
    }
  };

  useEffect(() => {
    if (isHistory) {
      setIsViewHistory(true);
    }
  }, [isHistory]);
  return (
    <>
      <Box className="main-page-container contract-view-main-container">
        <Box className={`h100 ${isViewHistory ? 'd-flex' : ''}`}>
          <Box className="contract-view-container">
            {loader ? (
              <PreLoader />
            ) : templateData === '' ? (
              <Box className="d-flex justify-center align-center h100">
                <Typography className="text-align h6">No data found</Typography>
              </Box>
            ) : (
              <Box>
                <Box className="d-flex justify-space-between align-center version-title-header">
                  <Box className="d-flex align-center">
                    <ArrowBackIosIcon
                      className="cursor-pointer mt4"
                      onClick={() => {
                        router.back();
                      }}
                    />
                    <Box>
                      <Typography className="title-sm fw600">
                        {latestVersion?.name}
                      </Typography>
                      <Typography className="title-text">
                        Current version as of{' '}
                        {latestVersion?.updatedAt
                          ? moment(latestVersion?.updatedAt)?.format(
                              'YYYY-MM-DD HH:mm'
                            )
                          : ''}
                      </Typography>
                    </Box>
                  </Box>
                  <Box className="d-flex justify-start align-center gap-sm contract-view-btn-list ">
                    <Box
                      className="d-flex contract-view-btn"
                      onClick={() => handleContract('edit')}
                    >
                      <Tooltip
                        title={<Typography>Edit</Typography>}
                        arrow
                        classes={{ tooltip: 'info-tooltip-container' }}
                      >
                        <DriveFileRenameOutlineIcon />
                      </Tooltip>
                    </Box>
                    <Divider orientation="vertical" flexItem />
                    <Box
                      className="d-flex contract-view-btn"
                      onClick={() => handleContract('history')}
                    >
                      <Tooltip
                        title={<Typography>Version History</Typography>}
                        arrow
                        classes={{ tooltip: 'info-tooltip-container' }}
                      >
                        <HistoryIcon />
                      </Tooltip>
                    </Box>
                  </Box>
                </Box>
                <Divider className="mb16" />
                <Box
                  className="contract-view-wrap"
                  dangerouslySetInnerHTML={{ __html: latestVersion?.content }}
                />
              </Box>
            )}
          </Box>
          {isViewHistory && (
            <Box className="right-sidebar-container">
              <Box className="d-flex justify-space-between align-center">
                <Typography className="title-sm fw600 contract-sidebar-header">
                  Version History
                </Typography>
                <Box
                  className="pr16 cursor-pointer"
                  onClick={() => setIsViewHistory(!isViewHistory)}
                >
                  <CloseIcon />
                </Box>
              </Box>
              <Box
                id="scrollableSidebar"
                style={{
                  height: 'calc(100vh - 150px - var(--banner-height))',
                  overflow: 'auto',
                }}
              >
                <InfiniteScroll
                  dataLength={templateData?.length}
                  next={fetchMoreData}
                  hasMore={hasMore}
                  loader={<Typography>Loading more versions...</Typography>}
                  scrollableTarget="scrollableSidebar"
                >
                  {templateData &&
                    templateData?.map((item, index) => {
                      return (
                        <Box
                          key={index}
                          className={`d-flex justify-space-between align-center gap-sm pb16 cursor-pointer history-log ${
                            item?.versionNumber === latestVersion?.versionNumber
                              ? 'active'
                              : ''
                          }`}
                          onClick={() => handleViewVersion(item)}
                        >
                          <Box>
                            <Typography className="title-text fw600">
                              {item?.updatedAt
                                ? moment(item?.updatedAt)?.format(
                                    'YYYY-MM-DD HH:mm'
                                  )
                                : null}
                            </Typography>
                            <Tooltip
                              title={<Typography>{item?.remark}</Typography>}
                              arrow
                              classes={{ tooltip: 'info-tooltip-container' }}
                            >
                              <Typography className="title-text history-log-remark">
                                {item?.remark}
                              </Typography>
                            </Tooltip>
                          </Box>
                          <Box
                            className="cursor-pointer"
                            onClick={() => handleViewVersion(item)}
                          >
                            <ViewIcon />
                          </Box>
                        </Box>
                      );
                    })}
                </InfiniteScroll>
              </Box>
            </Box>
          )}
        </Box>
      </Box>
    </>
  );
}
