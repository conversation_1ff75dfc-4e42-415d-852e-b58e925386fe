@import '@/styles/variable.scss';

.verify-mail-wrap {
  width: 100%;
  max-width: 550px;
  margin: 0 auto;
  border-radius: 8px;
  text-align: center;
  background-color: white;
  border-top-left-radius: 22px;
  border-top-right-radius: 22px;
  padding: 52px;
  box-shadow: 0px 0px 110px 0px #0260e133;
  min-height: 500px;
  overflow: hidden;

  .mail-icon-wrap {
    height: 105px;
    width: 115px;

    @media (max-width: 475px) {
      width: 80px;
      height: 80px;
    }
  }

  .trouble-text {
    color: var(--text-color-primary);
    text-decoration: underline;
  }
  .mail-text-wrap {
    .text-wrap {
      padding: 32px 0px 22px;
    }
  }

  .span-text-wrap {
    .span-folder-wrap {
      color: $color-Dark-80;
      font-weight: 300 !important;
      margin: 0px 5px;
      font-family: var(--font-family-poly-neutral);
    }
  }

  .email-link-wrap {
    color: var(--text-color-primary) !important;
  }

  .time-wrap {
    font-family: var(--font-family-poly-neutral);
    font-weight: 300;
    color: var(--text-color-primary) !important;
    margin-left: 10px;
  }

  .input-wrap {
    padding-bottom: 10px;

    .label-wrap {
      font-size: 14px;
      line-height: 20px;
      font-weight: 300;
      color: var(--text-slate-gray);
      text-align: left;
      font-family: var(--font-family-poly-slim) !important;
    }

    .MuiFormHelperText-root {
      margin-left: 0px;
    }
    .MuiInputBase-root {
      border-radius: 0px !important;
      padding-right: 0px !important;
      background-color: var(--color-white) !important;

      .Mui-error {
        .eye-wrap {
          svg {
            path {
              stroke: var(--color-danger);
            }
          }
        }
      }

      .MuiInputBase-input {
        font-size: 16px;
        line-height: 26px;
        font-weight: 400;
        margin: 5px 0px 10px;
        height: 100%;
        min-height: 26px;
        padding: 0px;
        color: #1d1e25 !important;
        font-family: var(--font-family-poly-slim) !important;

        .image-wrap {
          height: 24px;
          width: 24px;
        }

        &::placeholder {
          font-size: 16px !important;
          line-height: 26px !important;
          font-weight: 400 !important;
          margin: 5px 0px 10px !important;
          padding: 0px !important;
          font-family: var(--font-family-poly-slim) !important;
          text-transform: capitalize !important;
        }
      }

      &::before {
        border-bottom: 1px solid #e2e2ea;
      }
    }

    .Mui-error {
      font-family: var(--font-family-poly-slim) !important;

      .eye-wrap {
        path {
          stroke: var(--color-danger);
        }

        svg {
          fill: none;
        }
      }

      &::before {
        border-bottom: 1px solid var(--color-danger);
      }
    }
  }

  .email-check-section {
    svg {
      path {
        fill: var(--color-primary) !important;
      }
    }

    .checkbox-text {
      margin-left: 5px;
    }
  }

  .get-started-btn-wrap {
    margin: 20px 0px 22px 0px;

    .get-started-btn {
      font-size: 16px;
      line-height: 20px;
      font-weight: 300;
      padding: 16px !important;
      text-align: center;
      background-color: var(--color-primary) !important;
      color: #ffffff !important;
      border-radius: 12px !important;
      font-family: var(--font-family-poly-median) !important;
      border: none !important;
      box-shadow: 0px 2px 2px 0px #ffffff1a inset !important;
      letter-spacing: 0px;
      &:hover {
        font-family: var(--font-family-poly-median) !important;
        background-color: var(--color-primary) !important;
        color: #ffffff;
        box-shadow: none !important;
      }

      @media (max-width: 575px) {
        padding: 14px 0px !important;
      }
    }
    .register-btn {
      padding: 14px !important;
      letter-spacing: 0px !important;
    }
  }

  .contact-text {
    .contact-us-wrap {
      cursor: pointer;
      font-weight: 300;
      color: var(--text-color-primary) !important;
      font-family: var(--font-family-poly-neutral) !important;
    }
  }

  @media (max-width: 575px) {
    padding: 50px 14px;
    min-height: 100%;
  }

  // ::-webkit-scrollbar-thumb {
  //   display: none;
  // }
  // ::-webkit-scrollbar {
  //   display: none;
  // }
}
