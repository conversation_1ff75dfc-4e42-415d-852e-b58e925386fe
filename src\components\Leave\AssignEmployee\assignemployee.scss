@import '@/styles/variable.scss';

.assign-employee-section {
  height: calc(100% - 20px); //- 42px - 16px - 24px

  .filters-wrap {
    padding-bottom: 16px;
    gap: 10px;
    flex-wrap: wrap;
  }
  .assign-emp {
    height: calc(100% - 46px - 16px - 16px - 16px - 50px - 40px);
    overflow: scroll;
    max-height: 60vh;

    @media (max-width: 575px) {
      height: calc(100% - 260px) !important;
    }
    .Assign-emp-grid {
      display: grid;
      grid-template-columns: calc(50% - 4px) calc(50% - 4px);
      gap: 0px;
      height: 100%;
      .left-grid {
        border-right: 1px solid $color-Primary-10;
      }
      .right-grid {
      }
      @media (max-width: 768px) {
        grid-template-columns: calc(100%);
        gap: 16px;
        .left-grid {
          border-right: 0;
        }
        .right-grid {
        }
      }
    }
  }

  .assign-role-title {
    border-bottom: 1px solid $color-Primary-10;
    border-top: 1px solid $color-Primary-10;
    padding: 8px 0;
  }
  .assign-user-details {
    margin-top: 8px;
  }

  .disabled-user {
    opacity: 0.6;
    cursor: default !important;
    pointer-events: none !important;
  }
  .warning-icon {
    fill: $color-dark-yellow !important;
    margin-right: 8px;
    align-self: flex-start;
    width: 20px;
    height: 20px;
  }
  .user-warn {
    width: 16px;
    height: 16px;
    align-self: center;
  }
  .warning-details {
    padding: 4px 12px;
    display: flex;
    align-items: center;
    background-color: $color-Warning-Background;
  }
  .create-cancel-button {
    border-top: var(--normal-sec-border);
  }
}

.leave-assign-drawer,
.recipe-assign-drawer {
  .assign-emp {
    height: calc(100% - 46px - 16px - 16px - 16px - 50px);
    max-height: 66vh;
  }
}
