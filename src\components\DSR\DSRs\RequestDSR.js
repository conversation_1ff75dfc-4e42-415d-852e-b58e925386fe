'use client';

import React, { useEffect, useContext, useState } from 'react';
import {
  Box,
  Typography,
  Checkbox,
  FormControlLabel,
  FormGroup,
} from '@mui/material';
import CustomTextField from '@/components/UI/CustomTextField';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import CustomButton from '@/components/UI/CustomButton';
import { setApiMessage, DateFormat } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import AuthContext from '@/helper/authcontext';
import { useRouter } from 'next/navigation';
import { fetchFromStorage, saveToStorage } from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import DSRRequestByIdPage from './DSRRequestsId';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import DSRCategoryReq from './DsrCatRequest';

export default function RequestDSR({ dsrId }) {
  const { authState, userdata, setUserdata } = useContext(AuthContext);

  const [remark, setRemark] = useState('');
  const [isSubmit, setIsSubmit] = useState(false);
  const router = useRouter();
  const [DsrDetails, setDsrDetails] = useState();
  const [DsrDataIncome, setDsrDataIncome] = useState([]);
  const [DsrDataOther, setDsrDataOther] = useState([]);
  const [VatValue, setVATValue] = useState([0, 20]);
  const [VAT, setVAT] = useState({
    NoneVat: '',
    VAT1: '',
    VAT2: '',
    AmountVAT1: '',
    AmountVAT2: '',
    diff1: '',
    diff2: '',
    TotalIncome: '',
  });
  const [OldVAT, setOldVAT] = useState({
    NoneVat: '',
    VAT1: '',
    VAT2: '',
    AmountVAT1: '',
    AmountVAT2: '',
    diff1: '',
    diff2: '',
    TotalIncome: '',
  });
  const [loader, setLoader] = useState(false);
  const [isRequestTable, setIsRequestTable] = useState(false);
  const [approve, setApprove] = useState('');
  const [dsrReqData, setdsrReqData] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [requestId, setRequestId] = useState();
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [currency, setCurrency] = useState();
  const [VatIsView, setVatIsView] = useState(true);
  const [IsRemarkView, setIsRemarkView] = useState(true);
  const [page, setPage] = useState(1);
  // List of DSR request
  const getDsrRequestList = async (pageNo, Rpp) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_DSR_REQUEST_LIST +
          `?dsr_id=${dsrId}&page=${pageNo}&size=${Rpp ? Rpp : rowsPerPage}`
      );
      if (status === 200) {
        setLoader(false);
        let dsr =
          data?.data &&
          data?.data?.length > 0 &&
          data?.data?.map((d) => {
            return {
              ...d,
              branch: d?.dsr_branch?.branch_name,
              submitedby: d?.dsr_user?.user_full_name,
            };
          });
        dsr ? setdsrReqData(dsr) : setdsrReqData([]);

        setTotalCount(data?.count);
      }
    } catch (error) {
      setLoader(false);
      setTotalCount(0);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // List of DSR
  const getDsrRequest = async (id) => {
    setLoader(true);
    setRequestId(id);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_REQUEST_BY_ID + `${id}`
      );
      if (status === 200 || status === 201) {
        setLoader(false);
        setDsrDetails(data?.data);
        const incomeData =
          data?.data &&
          data?.data?.dsr_items &&
          data?.data?.dsr_items?.length > 0 &&
          data?.data?.dsr_items?.filter(
            (f) => f?.payment_type_usage === 'income'
          );
        incomeData && setDsrDataIncome(incomeData);
        const otherData =
          data?.data &&
          data?.data?.dsr_items &&
          data?.data?.dsr_items?.length > 0 &&
          data?.data?.dsr_items?.filter(
            (f) => f?.payment_type_usage === 'other'
          );
        otherData && setDsrDataOther(otherData);
        if (data?.data?.dsr_amount_total) {
          const vatdata = JSON.parse(data?.data?.dsr_amount_total);
          setVAT(vatdata);
        }
        if (data?.data?.old_dsr_amount_total) {
          const vatdatao = JSON.parse(data?.data?.old_dsr_amount_total);
          setOldVAT(vatdatao);
        }
        data?.vat_per_data &&
          data?.vat_per_data?.length > 0 &&
          setVATValue(data?.vat_per_data);
      }
    } catch (error) {
      setLoader(false);
      setDsrDetails();
      setDsrDataIncome();
      setDsrDataOther();
      setVAT();
      setVATValue();
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const handleDsrRequest = async (status) => {
    setLoader(true);
    let request = {
      dsr_request_id: requestId,
      request_status: status,
      request_remark: remark,
    };
    try {
      const { status, data } = await axiosInstance.post(
        URLS?.APPROVE_REJECT_REQUEST,
        request
      );
      if (status === 200) {
        setLoader(false);
        if (data?.status) {
          // getDsrRequest();
          setIsRequestTable(false);
          getDsrRequestList(1);
          setPage(1);
          setLoader(false);
          setApiMessage('success', data?.message);
        } else {
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const oldDSR = fetchFromStorage(identifiers?.RedirectData)
    ? fetchFromStorage(identifiers?.RedirectData)
    : userdata;
  const ValueOfTotal = (value) => {
    const parsedValue = parseFloat(value);
    if (!Number.isNaN(parsedValue)) {
      return parsedValue;
    }
    // if (parseFloat(value) && parseFloat(value) !== NaN) {
    //   return parseFloat(value);
    // }
    return 0;
  };
  useEffect(() => {
    if (oldDSR?.IsFromUser === undefined && oldDSR?.page !== undefined) {
      setUserdata({ ...userdata, IsFromUser: true });
      saveToStorage(identifiers?.RedirectData, {
        ...oldDSR,
        IsFromUser: true,
      });
    }
  }, [oldDSR]);
  useEffect(() => {
    if (dsrId) {
      getDsrRequestList(1);
    }
  }, [dsrId]);
  useEffect(() => {
    authState?.currency_details && setCurrency(authState?.currency_details);
  }, [authState]);
  return (
    <Box className="Add-dsr-section">
      <ArrowBackIosIcon
        className="mb8 cursor-pointer"
        onClick={() => {
          setTimeout(() => {
            router?.push('/dsr');
          }, 1000);
        }}
      />
      {isRequestTable ? (
        <>
          <Box className="">
            {DsrDetails?.dsr_date && (
              <Box className="d-flex align-center">
                <Typography className="title-text fw400">
                  <span className="fw600"> Date : </span>
                  <span>{DateFormat(DsrDetails?.dsr_date, 'dates')}</span>
                </Typography>
              </Box>
            )}
            {DsrDetails?.dsr_branch && (
              <Box className="d-flex align-center pt4">
                <Typography className="title-text fw400">
                  <span className="fw600"> Branch : </span>
                  <span>{DsrDetails?.dsr_branch}</span>
                </Typography>
              </Box>
            )}
            {DsrDetails?.submitted_user && (
              <Box className="d-flex align-center pt4">
                <Typography className="title-text fw400">
                  <span className="fw600"> Submitted By : </span>
                  <span>{DsrDetails?.submitted_user}</span>
                </Typography>
              </Box>
            )}
            {DsrDetails?.dsr_request_updated_by &&
              DsrDetails?.dsr_request_updated_by?.user_full_name && (
                <Box className="d-flex align-center pt4">
                  <Typography className="title-text fw400">
                    <span className="fw600"> Action By : </span>
                    <span>
                      {DsrDetails?.dsr_request_updated_by?.user_full_name}
                    </span>
                  </Typography>
                </Box>
              )}
            {DsrDetails?.dsr_request_status && (
              <Box className="d-flex align-center pt4">
                <Typography className="title-text fw400">
                  <span className="fw600"> Status : </span>
                  <span>
                    {DsrDetails?.dsr_request_status === 'rejected' ? (
                      <span className="sub-title-text failed fw600 text-capital">
                        {' '}
                        {DsrDetails?.dsr_request_status}{' '}
                      </span>
                    ) : DsrDetails?.dsr_request_status === 'pending' ? (
                      <span className="sub-title-text draft fw600 text-capital">
                        {' '}
                        {DsrDetails?.dsr_request_status}{' '}
                      </span>
                    ) : (
                      <span className="sub-title-text success fw600 text-capital">
                        {' '}
                        {DsrDetails?.dsr_request_status}{' '}
                      </span>
                    )}
                  </span>
                </Typography>
              </Box>
            )}
          </Box>

          {DsrDataIncome && DsrDataIncome?.length > 0 && (
            <>
              <FormGroup className="form-checkbox pt16 pt16">
                <FormControlLabel
                  control={
                    <Checkbox
                      className="check-box "
                      icon={
                        <CheckBoxOutlineBlankIcon className="uncheck-icon" />
                      }
                      checkedIcon={<CheckBoxIcon className="check-icon" />}
                      disableRipple
                    />
                  }
                  name="weekly"
                  className="check-box-form form-row max-content sub-title-text"
                  checked={VatIsView}
                  onChange={(e) => {
                    setVatIsView(e.target.checked);
                  }}
                  label="View VAT Details"
                />
              </FormGroup>
              <FormGroup className="form-checkbox">
                <FormControlLabel
                  control={
                    <Checkbox
                      className="check-box "
                      icon={
                        <CheckBoxOutlineBlankIcon className="uncheck-icon" />
                      }
                      checkedIcon={<CheckBoxIcon className="check-icon" />}
                      disableRipple
                    />
                  }
                  name="weekly"
                  className="check-box-form form-row max-content sub-title-text"
                  checked={IsRemarkView}
                  onChange={(e) => {
                    setIsRemarkView(e.target.checked);
                  }}
                  label="View Remark"
                />
              </FormGroup>
            </>
          )}
          <DSRCategoryReq
            DsrDataIncome={DsrDataIncome}
            setVatIsView={setVatIsView}
            VatIsView={VatIsView}
            setIsRemarkView={setIsRemarkView}
            IsRemarkView={IsRemarkView}
            setIsSubmit={setIsSubmit}
            setDsrDataIncome={setDsrDataIncome}
            setDsrDataOther={setDsrDataOther}
            DsrDataOther={DsrDataOther}
            currency={currency}
            VAT={VAT}
            setVAT={setVAT}
            VatValue={VatValue}
            ValueOfTotal={ValueOfTotal}
            isSubmit={isSubmit}
            OldVAT={OldVAT}
          />
          {DsrDetails?.dsr_request_status !== 'approved' &&
            DsrDetails?.dsr_request_status !== 'rejected' && (
              <>
                <Box className="pt64">
                  <CustomTextField
                    id="remark"
                    name="remark"
                    multiline
                    rows={2}
                    onChange={(e) => {
                      setIsSubmit(false);
                      setApprove();
                      setRemark(e.target.value);
                    }}
                    fullWidth
                    error={Boolean(!remark && isSubmit)}
                    helperText={isSubmit && !remark}
                    placeholder="Remark"
                    value={remark}
                    className="additional-textfeild"
                    label="Remark"
                    required
                  />
                  {!remark && isSubmit && (
                    <Typography
                      variant="body2"
                      color="error"
                      className="other-field-error-text"
                    >
                      This field is required
                    </Typography>
                  )}
                </Box>
                <Box className="create-cancel-button mt16">
                  <CustomButton
                    variant="contained"
                    disabled={loader}
                    className="red-button"
                    title={`${
                      loader && approve === 'rejected'
                        ? 'Rejecting...'
                        : 'Reject'
                    }`}
                    onClick={() => {
                      setIsSubmit(true);
                      if (remark) {
                        setApprove('rejected');
                        handleDsrRequest('rejected');
                      }
                    }}
                  />
                  <CustomButton
                    variant="contained"
                    disabled={loader} //|| !remark
                    className="green-button"
                    title={`${
                      loader && approve === 'approved'
                        ? 'Approving...'
                        : 'Approve'
                    }`}
                    onClick={() => {
                      setIsSubmit(true);
                      if (remark) {
                        setApprove('approved');
                        handleDsrRequest('approved');
                      }
                    }}
                  />
                </Box>
              </>
            )}
        </>
      ) : (
        <>
          <Box>
            {dsrReqData && dsrReqData?.length > 0 && (
              <>
                {dsrReqData?.[0]?.dsr_date && (
                  <Box className="d-flex align-center">
                    <Typography className="title-text fw400">
                      <span className="fw600"> Date : </span>
                      <span>
                        {DateFormat(dsrReqData?.[0]?.dsr_date, 'dates')}
                      </span>
                    </Typography>
                  </Box>
                )}
                {dsrReqData?.[0]?.dsr_branch &&
                  dsrReqData?.[0]?.dsr_branch?.branch_name && (
                    <Box className="d-flex align-center pt4">
                      <Typography className="title-text fw400">
                        <span className="fw600"> Branch : </span>
                        <span>{dsrReqData?.[0]?.dsr_branch?.branch_name}</span>
                      </Typography>
                    </Box>
                  )}
                {dsrReqData?.[0]?.dsr_user &&
                  dsrReqData?.[0]?.dsr_user?.user_full_name && (
                    <Box className="d-flex align-center pt4">
                      <Typography className="title-text fw400">
                        <span className="fw600"> Submitted By : </span>
                        <span>{dsrReqData?.[0]?.dsr_user?.user_full_name}</span>
                      </Typography>
                    </Box>
                  )}
                {dsrReqData?.[0]?.dsr_request_updated_by &&
                  dsrReqData?.[0]?.dsr_request_updated_by?.user_full_name && (
                    <Box className="d-flex align-center pt4">
                      <Typography className="title-text fw400">
                        <span className="fw600"> Submitted By : </span>
                        <span>
                          {
                            dsrReqData?.[0]?.dsr_request_updated_by
                              ?.user_full_name
                          }
                        </span>
                      </Typography>
                    </Box>
                  )}
                {dsrReqData?.[0]?.dsr_request_status && (
                  <Box className="d-flex align-center pt4">
                    <Typography className="title-text fw400">
                      <span className="fw600"> Status : </span>
                      <span>
                        <span>
                          {dsrReqData?.[0]?.dsr_request_status ===
                          'rejected' ? (
                            <span className="sub-title-text failed fw600">
                              {' '}
                              {dsrReqData?.[0]?.dsr_request_status}{' '}
                            </span>
                          ) : dsrReqData?.[0]?.dsr_request_status ===
                            'pending' ? (
                            <span className="sub-title-text draft fw600">
                              {' '}
                              {dsrReqData?.[0]?.dsr_request_status}{' '}
                            </span>
                          ) : (
                            <span className="sub-title-text success fw600">
                              {' '}
                              {dsrReqData?.[0]?.dsr_request_status}{' '}
                            </span>
                          )}
                        </span>
                      </span>
                    </Typography>
                  </Box>
                )}
              </>
            )}
          </Box>
          <DSRRequestByIdPage
            setIsRequestTable={setIsRequestTable}
            dsrReqData={dsrReqData}
            loader={loader}
            totalCount={totalCount}
            getDsrRequest={getDsrRequest}
            getDsrRequestList={getDsrRequestList}
            setRowsPerPage={setRowsPerPage}
            rowsPerPage={rowsPerPage}
            page={page}
            setPage={setPage}
          />
        </>
      )}
    </Box>
  );
}
