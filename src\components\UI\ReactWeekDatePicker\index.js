import React, { useEffect, useState } from 'react';
import { Box, InputLabel, InputAdornment, TextField } from '@mui/material';
import DatePicker from 'react-datepicker';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import 'react-datepicker/dist/react-datepicker.css';
import { enGB } from 'date-fns/locale';
import './datepicker.scss';

const CustomDateTimePicker = ({
  onChange,
  label,
  value,
  className,
  disablePast,
  disabled,
  minDate,
  maxDate,
  placeholder,
  years,
  months,
  days,
  format,
  isClearable,
  showWeekNumbers,
  showWeekPicker,
  formated,
}) => {
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);

  const handleWeekChange = (date) => {
    if (date) {
      const startOfWeek = new Date(date);

      const dayOfWeek = startOfWeek.getDay();
      const diffToMonday = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
      startOfWeek.setDate(startOfWeek.getDate() + diffToMonday);
      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(startOfWeek.getDate() + 6);
      setStartDate(startOfWeek);
      setEndDate(endOfWeek);

      onChange({ startOfWeek, endOfWeek });
    }
  };

  const formatWeekRange = (start, end) => {
    if (formated) {
      return `${start} - ${end}`;
    }
    if (!start || !end) return '';
    const options = { day: '2-digit', month: '2-digit', year: 'numeric' };
    const formattedStart = start?.toLocaleDateString('en-GB', options); // en-GB for DD/MM/YYYY format
    const formattedEnd = end?.toLocaleDateString('en-GB', options);

    return `${formattedStart} - ${formattedEnd}`;
  };
  useEffect(() => {
    value?.startDate && setStartDate(value?.startDate);
    value?.endDate && setEndDate(value?.endDate);
  }, [value]);
  return (
    <Box
      className={
        className
          ? `${className} react-datepicker-sec w100`
          : ' w100 react-datepicker-sec'
      }
    >
      <InputLabel shrink>{label}</InputLabel>
      <DatePicker
        selected={!formated && startDate ? startDate : null}
        onChange={handleWeekChange}
        minDate={disablePast ? new Date() : minDate}
        maxDate={maxDate}
        showTimeSelect={!showWeekPicker}
        dateFormat={
          format ||
          (years
            ? 'yyyy'
            : months
              ? 'MM/yyyy'
              : days
                ? 'dd/MM/yyyy'
                : showWeekPicker
                  ? 'I/yyyy'
                  : 'dd/MM/yyyy hh:mm aa')
        }
        placeholderText={placeholder || 'Select date and time'}
        disabled={disabled}
        customInput={
          <TextField
            className="input_field"
            variant="outlined"
            fullWidth
            InputLabelProps={{
              shrink: true,
            }}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <CalendarMonthIcon sx={{ cursor: 'pointer' }} />
                </InputAdornment>
              ),
              value: formatWeekRange(startDate, endDate),
            }}
          />
        }
        isClearable={isClearable}
        showWeekNumbers={showWeekNumbers}
        showWeekPicker={showWeekPicker}
        locale={enGB}
      />
    </Box>
  );
};

export default CustomDateTimePicker;
