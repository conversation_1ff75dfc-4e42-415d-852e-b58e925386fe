'use client';

import React, { useState } from 'react';
import { Box, Typography, Tooltip } from '@mui/material';
import ControlPointIcon from '@mui/icons-material/ControlPoint';
import ReorderIcon from '@mui/icons-material/Reorder';
import CustomCheckbox from '@/components/UI/CustomCheckbox';

export default function DSRCategoryMap({ paymentCateList, AddDSR, setAddDSR }) {
  const [deactive, setDeactive] = useState(true);
  const categoriesView = (catData, catName, isFirst, isSub, type, id) => {
    return (
      <ul className={isFirst && 'main-category-ul'}>
        {isSub && (
          <li
            className={
              !AddDSR?.reorder && AddDSR?.payment_type_title === catName
                ? 'list-view-selected'
                : ''
            }
          >
            <div
              className="list dsr-list add-dsr-category"
              onClick={() =>
                setAddDSR({
                  payment_type_usage: 'single',
                  payment_type_title: catName,
                  // payment_main_type: type,
                  id: id,
                })
              }
            >
              <ControlPointIcon className="add-icon" />
              <Typography className="title-text text-capitalize fw600">
                Add
              </Typography>
            </div>
          </li>
        )}

        {catData &&
          catData?.map((list, i) => (
            <>
              {list &&
                (list?.payment_type_usage === 'income' ||
                  list?.payment_type_usage === 'other' ||
                  list?.payment_type_usage === 'expense') &&
                i == 0 && (
                  <li
                    className={
                      AddDSR?.payment_type_title === catName
                        ? 'list-view-selected'
                        : ''
                    }
                  >
                    <div
                      className="list dsr-list add-dsr-category mt16"
                      onClick={() =>
                        setAddDSR({
                          payment_type_usage: list?.payment_type_usage,
                          payment_type_title: catName,
                          id: list?.id,
                          // CategoryList: paymentCateList
                        })
                      }
                    >
                      <ControlPointIcon className="add-icon" />
                      <Typography className="title-text text-capitalize fw600">
                        Add
                      </Typography>
                    </div>
                  </li>
                )}
              {list &&
                (list?.payment_type_category_pattern === 'single' ||
                  list?.payment_type_category_pattern === 'multiple') &&
                i == 0 && (
                  <li
                    className={
                      !AddDSR?.reorder && AddDSR?.payment_type_title === catName
                        ? 'list-view-selected'
                        : ''
                    }
                  >
                    <div
                      className="list dsr-list add-dsr-category"
                      onClick={() =>
                        setAddDSR({
                          payment_type_usage:
                            list?.payment_type_category_pattern,
                          payment_type_title: catName,
                          id: id,
                        })
                      }
                    >
                      <ControlPointIcon className="add-icon" />
                      <Typography className="title-text text-capitalize fw600">
                        Add
                      </Typography>
                    </div>
                  </li>
                )}
              {!deactive && list?.payment_type_status !== 'active' ? (
                <></>
              ) : (
                <>
                  <li className="list-view">
                    <div key={i} className="list dsr-list">
                      <Box className="add-dsr-category">
                        {list?.payment_type_status === 'active' ? (
                          <span className="active-badge"></span>
                        ) : list?.payment_type_status === 'inactive' ? (
                          <span className="inactive-badge"></span>
                        ) : (
                          <></>
                        )}

                        <span
                          className={`list-view-text title-text text-capitalize fw600 d-flex ${
                            AddDSR?.list?.payment_type_title ===
                            list?.payment_type_title
                              ? 'list-view-selected'
                              : ''
                          }`}
                          onClick={() =>
                            list?.payment_type_usage
                              ? setAddDSR({
                                  list: list,
                                  payment_type_usage: list?.payment_type_usage,
                                  id: list?.id,
                                  isUpdate: true,
                                })
                              : list?.payment_type_category_pattern
                                ? setAddDSR({
                                    list: list,
                                    payment_type_usage:
                                      list?.payment_type_category_pattern,
                                    id: list?.id,
                                    isUpdate: true,
                                  })
                                : setAddDSR({ list: list })
                          }
                        >
                          <span
                            // className="text-ellipsis"
                            title={list?.payment_type_title}
                          >
                            {list?.payment_type_title}
                          </span>
                          {(list?.payment_type_usage === 'income' ||
                            list?.payment_type_usage === 'expense' ||
                            list?.payment_type_usage === 'other') && (
                            <span className="category-status">
                              {list?.payment_type_usage === 'income' ? (
                                <span className="sub-title-text category-accepted  fw600 text-capital">
                                  {' '}
                                  {list?.payment_type_usage}{' '}
                                </span>
                              ) : list?.payment_type_usage === 'expense' ? (
                                <span className="sub-title-text category-draft fw600 text-capital">
                                  {' '}
                                  {list?.payment_type_usage}
                                </span>
                              ) : list?.payment_type_usage === 'other' ? (
                                <span className="sub-title-text category-ongoing fw600 text-capital">
                                  {' '}
                                  {list?.payment_type_usage}{' '}
                                </span>
                              ) : (
                                <></>
                              )}
                            </span>
                          )}
                        </span>
                        {list?.payment_type_category &&
                          list?.payment_type_category?.length > 0 &&
                          (list?.payment_type_usage === 'income' ||
                            list?.payment_type_usage === 'other' ||
                            list?.payment_type_usage === 'expense') && (
                            <Tooltip
                              arrow
                              classes={{
                                tooltip: 'info-tooltip-container',
                              }}
                              title={<Typography>Reorder Sub lists</Typography>}
                            >
                              <ReorderIcon
                                className={`reorder-icon ${
                                  AddDSR?.reorder &&
                                  AddDSR?.payment_type_title ===
                                    list?.payment_type_title &&
                                  'selected-reorder'
                                } `}
                                onClick={() =>
                                  setAddDSR({
                                    payment_type: list?.payment_type_usage,
                                    payment_type_title:
                                      list?.payment_type_title,
                                    CategoryList: list?.payment_type_category,
                                    payment_type_usage: 'single',
                                    reorder: true,
                                  })
                                }
                              />
                            </Tooltip>
                          )}
                      </Box>

                      <Box className="sign-activity sub-title-text">
                        {categoriesView(
                          list?.payment_type_category
                            ? list?.payment_type_category
                            : [],
                          list?.payment_type_title,
                          false,
                          list?.payment_type_usage
                            ? list?.payment_type_category?.length === 0
                            : false,
                          list?.payment_type_usage,
                          list?.id
                        )}
                      </Box>
                    </div>
                  </li>
                </>
              )}
            </>
          ))}
      </ul>
    );
  };

  return (
    <>
      <Box className="dsr-settings-payment-category">
        <Box className="d-flex align-center justify-space-between flex-wrap">
          <Box
            className={`add-dsr-category cursor-pointer ${
              AddDSR?.mainCatgory ? 'list-view-selected' : ''
            }`}
            onClick={() =>
              paymentCateList?.length !== 0 &&
              setAddDSR({
                mainCatgory: true,
                payment_type_usage: true,
                CategoryList: paymentCateList,
              })
            }
          >
            <Typography className="title-sm fw600">DSR Setting</Typography>
            <Tooltip
              arrow
              classes={{
                tooltip: 'info-tooltip-container',
              }}
              title={<Typography>Reorder lists</Typography>}
            >
              <ReorderIcon className="reorder-icon reorder-main" />
            </Tooltip>
          </Box>
          <Box className="deactive-section">
            <CustomCheckbox
              checked={deactive}
              onChange={(e) => {
                setDeactive(e?.target?.checked);
              }}
              disableRipple
              label={
                <Typography className="sub-content-text">
                  Show In-Active Categories
                </Typography>
              }
            />
          </Box>
        </Box>
        <ul className="dsr-setting-tree">
          {paymentCateList?.length === 0 && (
            <ul className="main-category-ul">
              <li className="">
                <div
                  className="list dsr-list add-dsr-category"
                  onClick={() =>
                    setAddDSR({
                      payment_type_usage: true,
                      payment_type_title: null,
                    })
                  }
                >
                  <ControlPointIcon className="add-icon" />
                  <Typography className="title-text text-capitalize fw600">
                    Add
                  </Typography>
                </div>
              </li>
            </ul>
          )}
          {categoriesView(paymentCateList, null, true)}
        </ul>
      </Box>
    </>
  );
}
