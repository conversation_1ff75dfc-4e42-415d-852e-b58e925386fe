import React, { useEffect, useState, useContext } from 'react';
import {
  Box,
  ListItemIcon,
  ListItemText,
  MenuList,
  MenuItem,
  Tooltip,
  Typography,
} from '@mui/material';
import AuthContext from '@/helper/authcontext';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import { checkOrganizationRole } from '@/helper/common/commonFunctions';
import { leftSideMenu } from '@/helper/common/commonMenus';
import {
  removeFromStorage,
  saveToStorage,
  // fetchFromStorage,
} from '@/helper/context';
import { identifiers } from '@/helper/constants/identifier';
import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew';
import Image from 'next/image';
import hierarchyImage from '../../public/finalflow.jpg';
import InfoIcon from '@mui/icons-material/Info';
import AppBrandLogo from './UI/AppBrandLogo';
import './sidebarmenulist.scss';

const SideBarMenuList = ({ setMobileOpen }) => {
  const {
    // userdata,
    setUserdata,
    authState,
    // setAuthState,
    reFetch,
    setReFetch,
    isActive,
    setIsActive,
  } = useContext(AuthContext);
  const router = useRouter();
  const pathname = usePathname();
  const [openMenu, setOpenMenu] = useState({});
  const searchParams = useSearchParams();
  // const [permissionLink, setPermissionLink] = useState();
  // const AuthData = fetchFromStorage(identifiers?.AUTH_DATA);

  const IsADMIN = searchParams.get('IsAdmin');
  const IsInvite = searchParams.get('IsInvite');
  const handleClick = (item) => {
    setUserdata();
    removeFromStorage(identifiers?.RedirectData);
    setOpenMenu((prevState) => ({
      ...openMenu,
      [item?.name]: !prevState[item?.name],
    }));
    setReFetch(!reFetch);
    saveToStorage('activeMenuItem', item?.id);
    setIsActive(item?.id);
    if (item?.link) {
      router.push(item?.link);
    }
  };
  const SideBarMenuList = (menu) => {
    return (
      <>
        {menu
          ?.filter((item) => item?.id !== 5)
          ?.map((item, index) => {
            if (
              authState &&
              authState?.UserPermission &&
              item?.permission &&
              (authState?.UserPermission?.[item?.permission] === 2 ||
                authState?.UserPermission?.[item?.permission] === 1 ||
                item?.permission === 'leavecenter' ||
                (item?.permission === 'dashboard' &&
                  (authState?.UserPermission?.['forecast'] === 2 ||
                    authState?.UserPermission?.['forecast'] === 1)) ||
                (item?.permission === 'notificationcenter' && item?.id === 5) ||
                (item?.permission === 'notificationcenter' &&
                  item?.id === 2 &&
                  (checkOrganizationRole('org_master') ||
                    checkOrganizationRole('super_admin') ||
                    authState?.web_user_active_role_id !== 1)) ||
                (item?.permission === 'resignationcenter' && item?.id === 9) ||
                (item?.permission === 'staffpermission' &&
                  CheckMedia('staff')) ||
                (item?.permission === 'resignationcenter' &&
                  item?.id === 2 &&
                  authState?.web_user_active_role_id !== 1) ||
                item?.permission === 'change_request' ||
                item?.permission === 'owncategory' ||
                (item?.permission === 'category' && item?.id === 15))
            ) {
              return (
                <Tooltip
                  title={item?.name}
                  placement="right"
                  classes={{
                    tooltip: open ? 'd-none' : 'sidebar-list-tooltip',
                  }}
                  arrow
                  key={index}
                >
                  <MenuItem
                    className={isActive === item?.id ? 'active' : ''}
                    onClick={() => {
                      setIsActive(item?.id);
                      item?.link && router?.push(item?.link);
                      handleClick(item);
                    }}
                    role="complementary"
                  >
                    <ListItemText>
                      {item?.icon && (
                        <Box className="d-flex align-center">
                          <ListItemIcon className="menu-icon">
                            <Box className="icon">{item?.icon}</Box>
                          </ListItemIcon>
                        </Box>
                      )}
                      <Box className="d-flex align-center justify-space-between w100">
                        <Typography className="title-text menu-name">
                          {item?.name}
                        </Typography>
                      </Box>
                    </ListItemText>
                  </MenuItem>
                </Tooltip>
              );
            } else if (
              checkOrganizationRole('org_master') ||
              checkOrganizationRole('super_admin')
            ) {
              return (
                <Tooltip
                  title={item?.name}
                  placement="right"
                  classes={{
                    tooltip: open ? 'd-none' : 'sidebar-list-tooltip',
                  }}
                  arrow
                  key={index}
                >
                  <MenuItem className={isActive === item?.id ? 'active' : ''}>
                    <ListItemText>
                      {item?.icon && (
                        <Box className="d-flex align-center">
                          <ListItemIcon className="menu-icon">
                            <Box className="icon">{item?.icon}</Box>
                          </ListItemIcon>
                        </Box>
                      )}
                      <Box className="d-flex align-center justify-space-between w100">
                        <Typography className="title-text menu-name">
                          {item?.name}
                        </Typography>
                        {item?.name === 'Department' && open && (
                          <Tooltip
                            arrow
                            title={
                              <Box>
                                <Image
                                  src={hierarchyImage}
                                  className="hierarchy-image-wrap"
                                  alt="Hierarchy"
                                  height={100}
                                  width={100}
                                />
                              </Box>
                            }
                            classes={{
                              tooltip: 'hierarchy-wrap',
                            }}
                          >
                            <Box className="info-icon-wrap">
                              <InfoIcon />
                            </Box>
                          </Tooltip>
                        )}
                      </Box>
                    </ListItemText>
                  </MenuItem>
                </Tooltip>
              );
            } else {
              return <></>;
            }
          })}
      </>
    );
  };

  useEffect(() => {
    const activetab = leftSideMenu.find(
      (f) =>
        window.location.pathname.startsWith(f?.link) &&
        window.location.pathname.endsWith(f?.link)
    );
    activetab?.id && setIsActive(activetab?.id);
    const pathsToCheck = [
      '/own-leave',
      '/leave-remark',
      '/staff-notification',
      '/own-notification',
      '/resignation-remark',
      '/resignation',
      '/dsr',
      '/wsr',
      '/payroll',
      '/logbook-reports',
      '/staff',
      '/invited-staff',
      '/empcontracts',
      '/contract-renewal',
      '/leave-policy',
      '/leave-policy-type',
      '/contract-type',
      '/budget-forecast',
      '/chart-dashboard',
      '/leave-reports',
      '/org/setup',
    ];
    const isPathIncluded = pathsToCheck.some((path) =>
      window.location.pathname.includes(path)
    );
    if (window.location.pathname.includes('/user/') && IsADMIN) {
      setIsActive(2);
      setOpenMenu({ ...openMenu, 'Admin User': true });
    } else if (
      window.location.pathname.includes('/create-emp-contract') ||
      window.location.pathname.includes('/edit-emp-contract')
    ) {
      setIsActive(14);
      setOpenMenu({
        ...openMenu,
        'Contract Center': true,
      });
    } else if (window.location.pathname.includes('/org/setup')) {
      setIsActive(17);
      setOpenMenu({
        ...openMenu,
        Setup: true,
      });
    } else if (window.location.pathname.includes('/user/') && IsInvite) {
      setIsActive(6);
      setOpenMenu({ ...openMenu, Staff: true, 'Staff Invitation': true });
    } else if (window.location.pathname.includes('/user/')) {
      setIsActive(6);
      setOpenMenu({ ...openMenu, Staff: true, 'All staff': true });
    } else if (
      window.location.pathname.includes('/change-req') ||
      window.location.pathname.includes('/change-request')
    ) {
      setIsActive(11);
      setOpenMenu({ 'Change Request': true });
    } else if (
      window.location.pathname.includes('/document-staff/') ||
      window.location.pathname.includes('/create-category') ||
      window.location.pathname.includes('/create-content')
    ) {
      setIsActive(15);
      setOpenMenu({ 'Document Center': true, 'Staff view': true });
    } else if (window.location.pathname.includes('/document-own/')) {
      setIsActive(15);
      setOpenMenu({ 'Document Center': true, 'Personal view': true });
    } else if (window.location.pathname.includes('/branch/')) {
      setIsActive(3);
      setOpenMenu({ Branch: true });
    } else if (
      window.location.pathname.includes('/chart-dashboard/') ||
      window.location.pathname.includes('/chart-dashboard/template') ||
      window.location.pathname.includes('/budget-forecast/')
    ) {
      setIsActive(1);
      setOpenMenu({ Dashboard: true });
    }
    if (isPathIncluded) {
      const seltab = leftSideMenu.find((f) => {
        return (
          f?.submenu &&
          f?.submenu.length > 0 &&
          f?.submenu.find((s) => window.location.pathname === s?.link)
        );
      });
      const selSub = seltab?.submenu?.find(
        (s) => window.location.pathname === s?.link
      );
      if (seltab) {
        setIsActive(seltab?.id);
        setOpenMenu({ [selSub?.name]: true, [seltab?.name]: true });
      } else if (window.location.pathname.includes('/play-list/')) {
        setIsActive(10);
        setOpenMenu({ 'Document Center': true, Folders: true });
      } else if (
        window.location.pathname.includes('/dsr/') ||
        window.location.pathname.includes('/wsr/') ||
        window.location.pathname.includes('/payroll/')
      ) {
        setIsActive(10);
        setOpenMenu({ 'Logs Book': true });
      }
    }
  }, [pathname]);
  useEffect(() => {
    if (
      authState?.UserPermission &&
      authState?.UserPermission?.category === 0 &&
      pathname.startsWith('/document-staff/all')
    ) {
      router?.push('/document-own/all');
      // setPermissionLink('/document-own/all');
    }
    if (
      pathname === '/myprofile' ||
      pathname === '/org/organization' ||
      pathname === '/sorg/organization'
    ) {
      removeFromStorage('activeMenuItem');
      setOpenMenu({});
      setIsActive();
    }
  }, [pathname]);
  useEffect(() => {
    if (
      authState?.UserPermission?.dashboard === 2 ||
      authState?.UserPermission?.dashboard === 1 ||
      authState?.UserPermission?.forecast === 2 ||
      authState?.UserPermission?.forecast === 1
    ) {
      if (
        window.location.pathname.startsWith('/budget-forecast') &&
        authState?.UserPermission &&
        authState?.UserPermission?.forecast === 0 &&
        (authState?.UserPermission?.dashboard === 2 ||
          authState?.UserPermission?.dashboard === 1)
      ) {
        router?.push('/chart-dashboard');
        // setPermissionLink('/chart-dashboard');
      } else if (
        window.location.pathname.startsWith('/chart-dashboard') &&
        authState?.UserPermission &&
        authState?.UserPermission?.dashboard === 0 &&
        (authState?.UserPermission?.forecast === 2 ||
          authState?.UserPermission?.forecast === 1)
      ) {
        router?.push('/budget-forecast');
        // setPermissionLink('/budget-forecast');
      }
    }
  }, [pathname]);

  useEffect(() => {
    if (authState?.UserPermission) {
      const activetab = leftSideMenu.find(
        (f) =>
          window.location.pathname.startsWith(f?.link) &&
          window.location.pathname.endsWith(f?.link)
      );
      const permissionAccess = leftSideMenu?.find(
        (f) =>
          authState?.UserPermission?.[f?.permission] !== 0 &&
          authState?.UserPermission?.[f?.permission] !== '' &&
          authState?.UserPermission?.[f?.permission] !== undefined &&
          f?.link !== undefined &&
          f?.permission !== ''
      );
      var permissionSubActive = null;
      leftSideMenu?.find((f) => {
        if (
          authState?.UserPermission?.[f?.permission] !== 0 &&
          authState?.UserPermission?.[f?.permission] !== '' &&
          authState?.UserPermission?.[f?.permission] !== undefined &&
          f?.link !== undefined &&
          f?.permission !== ''
        ) {
          return f;
        } else {
          const subpermission = f?.submenu?.find(
            (fs) =>
              authState?.UserPermission?.[fs?.permission] !== 0 &&
              authState?.UserPermission?.[fs?.permission] !== '' &&
              authState?.UserPermission?.[fs?.permission] !== undefined &&
              fs?.link !== undefined &&
              fs?.permission !== ''
          );
          permissionSubActive = subpermission;
          if (subpermission) {
            return subpermission;
          }
        }
      });

      let actvivesubmenu = null;
      leftSideMenu.find((f) =>
        f?.submenu?.find((s) => {
          if (
            window.location.pathname.startsWith(s?.link) &&
            window.location.pathname.endsWith(s?.link)
          ) {
            actvivesubmenu = s;
          }
        })
      );
      if (
        window.location.pathname.includes('/change-req') ||
        window.location.pathname.includes('/change-request')
      ) {
        //
      } else if (
        actvivesubmenu?.permission === 'notificationcenter' ||
        actvivesubmenu?.permission === 'resignationcenter' ||
        actvivesubmenu?.permission === 'leavecenter' ||
        actvivesubmenu?.permission === 'owncategory'
      ) {
        //
      } else if (
        window.location.pathname.startsWith('/budget-forecast') &&
        authState?.UserPermission &&
        authState?.UserPermission?.forecast === 0 &&
        (authState?.UserPermission?.dashboard === 2 ||
          authState?.UserPermission?.dashboard === 1)
      ) {
        router?.push('/chart-dashboard');
        // setPermissionLink('/chart-dashboard');
      } else if (
        window.location.pathname.startsWith('/chart-dashboard') &&
        authState?.UserPermission &&
        authState?.UserPermission?.dashboard === 0 &&
        (authState?.UserPermission?.forecast === 2 ||
          authState?.UserPermission?.forecast === 1)
      ) {
        router?.push('/budget-forecast');
        // setPermissionLink('/budget-forecast');
      } else if (
        activetab &&
        (permissionAccess?.link || permissionSubActive?.link) &&
        (authState?.UserPermission?.[activetab?.permission] === 0 ||
          authState?.UserPermission?.[activetab?.permission] === undefined)
      ) {
        permissionAccess?.link
          ? router?.push(`${permissionAccess?.link}`)
          : router?.push(`${permissionSubActive?.link}`);
        // permissionAccess?.link
        //   ? setPermissionLink(permissionAccess?.link)
        //   : setPermissionLink(permissionSubActive?.link);
      } else if (
        actvivesubmenu &&
        (permissionAccess?.link || permissionSubActive?.link) &&
        (authState?.UserPermission?.[actvivesubmenu?.permission] === 0 ||
          authState?.UserPermission?.[actvivesubmenu?.permission] === undefined)
      ) {
        permissionAccess?.link
          ? router?.push(`${permissionAccess?.link}`)
          : router?.push(`${permissionSubActive?.link}`);
        // permissionAccess?.link
        //   ? setPermissionLink(permissionAccess?.link)
        //   : setPermissionLink(permissionSubActive?.link);
      } else if (
        window.location.pathname.startsWith('/branch/') &&
        (permissionAccess?.link || permissionSubActive?.link) &&
        (authState?.UserPermission?.branch === 0 ||
          authState?.UserPermission?.branch === 1)
      ) {
        permissionAccess?.link
          ? router?.push(`${permissionAccess?.link}`)
          : router?.push(`${permissionSubActive?.link}`);
        // permissionAccess?.link
        //   ? setPermissionLink(permissionAccess?.link)
        //   : setPermissionLink(permissionSubActive?.link);
      }
    }
  }, [authState?.UserPermission]);

  return (
    <>
      <Box>
        <nav
          className={open ? 'sub-menu-list sub-menu-list-open' : 'menu-list'}
        >
          <Box>
            <MenuList
              className={open ? 'menu-ul-list' : 'menu-ul-list pt0'}
              role="sidebar"
            >
              <Box className="header-close-section d-flex justify-space-between">
                <Box className="d-flex align-center justify-center sidebar-logo-wrap">
                  <AppBrandLogo
                    className="text-center side-menu-logo-wrap"
                    logoClassName="side-menu-logo"
                    onClick={() => {
                      router.push(
                        authState?.UserPermission?.dashboard
                          ? '/chart-dashboard'
                          : '/own-notification'
                      );
                    }}
                  />
                </Box>
                {/* <Image
                  src={TTHLogo}
                  className="header-logo cursor-pointer"
                  alt="Logo"
                  onClick={() => {
                    router.push(
                      authState?.UserPermission?.dashboard
                        ? '/chart-dashboard'
                        : '/own-notification'
                    );
                  }}
                /> */}
                <Box
                  className="close-icon"
                  onClick={() => setMobileOpen(false)}
                >
                  <ArrowBackIosNewIcon />
                </Box>
              </Box>
              {SideBarMenuList(leftSideMenu)}
            </MenuList>
          </Box>
        </nav>
      </Box>
    </>
  );
};

export default SideBarMenuList;
