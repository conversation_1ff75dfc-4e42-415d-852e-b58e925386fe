import { Box } from '@mui/material';
import PublicRecipe from '@/components/PublicRecipe';
import { generateMetadata } from '@/helper/common/commonFunctions';

export const metadata = generateMetadata({
  pageTitle: 'Public Recipe',
});

export default function PublicRecipePage({ params }) {
  return (
    <Box className="main-page-container">
      <PublicRecipe orgName={params.org_name} />
    </Box>
  );
}
