import { Box } from '@mui/material';
import React from 'react';

export default function RemarkListView() {
  return (
    <Box>
      {' '}
      {viewMode === 'list' && (
        <Box className="list-sec-wrap">
          {loader ? (
            <Box className="content-loader">
              <CircularProgress className="loader" color="inherit" />
            </Box>
          ) : (
            <>
              {leaveRemarkList && leaveRemarkList?.length === 0 ? (
                <Box className="">
                  <Typography className="text-align h6">
                    No data found
                  </Typography>
                </Box>
              ) : (
                <>
                  <Box>
                    <Box className="aply-leave-wrap">
                      <Typography variant="h6">4 Leaves Applied</Typography>

                      <Box className="leave-filters-wrap">
                        <Box className="filter-wrap">
                          <CustomTextField
                            InputLabelProps={{
                              shrink: true,
                            }}
                            fullWidth
                            id="categoryname"
                            name="categoryname"
                            variant="filled"
                            placeholder="Search"
                            InputProps={{
                              startAdornment: <CalendarMonthIcon />,
                            }}
                          />

                          <Box>
                            {/* Year Selector */}
                            <CustomSelect
                              placeholder="Select Year"
                              options={yearData}
                              // value={values?.year}
                              // name='year'
                              // error={touched.year && errors.year}
                              // className={
                              //   touched.year && errors.year
                              //     ? 'textfeild-error'
                              //     : ''
                              // }
                              // onChange={handleChange}
                              label="Year"
                            />

                            {/* Status Selector */}
                            <CustomSelect
                              placeholder="Select Status"
                              options={statusData}
                              // value={values?.status}
                              // name='status'
                              // error={touched.status && errors.status}
                              // className={
                              //   touched.status && errors.status
                              //     ? 'textfeild-error'
                              //     : ''
                              // }
                              // onChange={handleChange}
                              // label={
                              //   <span>
                              //     Status
                              //     <span className='primary-color'> *</span>
                              //   </span>
                              // }
                            />

                            {/* Leave Type Selector */}
                            <CustomSelect
                              placeholder="Select Leave Type"
                              options={leaveTypeData}
                              // value={values?.leaveType}
                              // name='leaveType'
                              // error={touched.leaveType && errors.leaveType}
                              // className={
                              //   touched.leaveType && errors.leaveType
                              //     ? 'textfeild-error'
                              //     : ''
                              // }
                              // onChange={handleChange}
                              // label='Leave Type'
                            />
                          </Box>
                        </Box>
                        <CustomButton />
                        <CustomButton
                          className="p12 secondary-button"
                          type="submit"
                          fontWeight="600"
                          variant="contained"
                          background="#FFFFFF"
                          backgroundhover="#39596e"
                          colorhover="#FFFFFF"
                          startIcon={<AddIcon />}
                        />
                      </Box>

                      {/* Table */}
                      <TableContainer>
                        <Table>
                          <TableHead>
                            <TableRow>
                              {[
                                { label: 'Leave Type', key: 'leaveType' },
                                { label: 'From', key: 'from' },
                                { label: 'To', key: 'to' },
                                {
                                  label: 'No. of Days/Hours',
                                  key: 'daysHours',
                                },
                                { label: 'Added Date', key: 'addedDate' },
                                { label: 'Reason', key: 'reason' },
                                { label: 'Action By', key: 'actionBy' },
                                { label: 'Status', key: 'status' },
                              ].map((column) => (
                                <TableCell key={column.key}>
                                  <TableSortLabel
                                    active={sortConfig.key === column.key}
                                    direction={
                                      sortConfig.key === column.key
                                        ? sortConfig.direction
                                        : 'asc'
                                    }
                                    onClick={() => handleSort(column.key)}
                                  >
                                    {column.label}
                                  </TableSortLabel>
                                </TableCell>
                              ))}
                              <TableCell align="center">Actions</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {sortedData.map((leave, index) => (
                              <TableRow key={index}>
                                <TableCell>{leave.leaveType}</TableCell>
                                <TableCell>{leave.from}</TableCell>
                                <TableCell>{leave.to}</TableCell>
                                <TableCell>{leave.daysHours}</TableCell>
                                <TableCell>{leave.addedDate}</TableCell>
                                <TableCell>
                                  <Typography noWrap>{leave.reason}</Typography>
                                </TableCell>
                                <TableCell>
                                  {leave.actionBy} <br />
                                  <Typography
                                    variant="caption"
                                    color="text.secondary"
                                  >
                                    {leave.actionTime}
                                  </Typography>
                                </TableCell>
                                <TableCell>
                                  <Button
                                    variant="outlined"
                                    size="small"
                                    sx={{
                                      color:
                                        leave.status === 'Approved'
                                          ? 'green'
                                          : leave.status === 'Pending'
                                            ? 'orange'
                                            : 'red',
                                      borderColor:
                                        leave.status === 'Approved'
                                          ? 'green'
                                          : leave.status === 'Pending'
                                            ? 'orange'
                                            : 'red',
                                    }}
                                  >
                                    {leave.status}
                                  </Button>
                                </TableCell>
                                <TableCell align="center">
                                  <IconButton>
                                    <MoreVertIcon />
                                  </IconButton>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </Box>
                  </Box>
                </>
              )}
            </>
          )}
        </Box>
      )}
    </Box>
  );
}
