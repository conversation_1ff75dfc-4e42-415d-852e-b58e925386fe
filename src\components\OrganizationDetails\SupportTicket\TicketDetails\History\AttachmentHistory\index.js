import React from 'react';
import { Box, Typography, Chip } from '@mui/material';
import NoteIcon from '@mui/icons-material/Note';
import EditIcon from '@mui/icons-material/Edit';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import InfoIcon from '@mui/icons-material/Info';
import UserAvatar from '@/components/UI/Avatar/UserAvatar';
import { DateFormat } from '@/helper/common/commonFunctions';
import './attachmenthistory.scss';

const AttachmentHistory = ({ attachmentData }) => {
  // Helper function to safely get user name
  const getUserName = (entry) => {
    const name = entry?.user_name || entry?.created_by || 'System';
    return typeof name === 'string' && name.trim() ? name.trim() : 'System';
  };

  // Helper function to format action type text
  const formatActionType = (actionType) => {
    if (!actionType) return '';
    return actionType
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  // Helper function to get action icon
  const getActionIcon = (actionType) => {
    switch (actionType?.toLowerCase()) {
      case 'status_changed':
      case 'status_update':
        return <CheckCircleIcon className="action-icon status-icon" />;
      case 'note_added':
      case 'comment_added':
        return <NoteIcon className="action-icon note-icon" />;
      case 'updated':
      case 'modified':
        return <EditIcon className="action-icon edit-icon" />;
      default:
        return <InfoIcon className="action-icon default-icon" />;
    }
  };

  // Helper function to get status chip color
  const getStatusChipColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'open':
        return 'primary';
      case 'in_progress':
      case 'in progress':
        return 'warning';
      case 'resolved':
      case 'completed':
        return 'success';
      case 'closed':
        return 'default';
      case 'on_hold':
      case 'on hold':
        return 'secondary';
      default:
        return 'default';
    }
  };
  // Safety check for attachmentData
  if (
    !attachmentData ||
    !Array.isArray(attachmentData) ||
    attachmentData.length === 0
  ) {
    return (
      <Box className="attachment-wrap">
        <Box className="no-history-message">
          <Typography className="sub-title-text">
            No history available
          </Typography>
        </Box>
      </Box>
    );
  }

  return (
    <Box className="attachment-wrap">
      <Box className="attachment-history-wrap">
        {attachmentData.map((entry, index) => (
          <Box key={index} className="history-entry-card">
            {/* Timeline connector */}
            <Box className="timeline-connector">
              <Box className="timeline-dot">
                {getActionIcon(entry?.action_type)}
              </Box>
              {index < attachmentData.length - 1 && (
                <Box className="timeline-line" />
              )}
            </Box>

            {/* Content card */}
            <Box className="entry-content-card">
              {/* Header with user info and timestamp */}
              <Box className="entry-header">
                <Box className="user-info">
                  <UserAvatar
                    name={getUserName(entry)}
                    src={entry?.user_avatar}
                    classname="history-user-avatar"
                  />
                  <Box className="user-details">
                    <Typography className="user-name body-sm fw600">
                      {getUserName(entry)}
                    </Typography>
                    <Typography className="action-type sub-title-text">
                      {formatActionType(entry?.action_type)}
                    </Typography>
                  </Box>
                </Box>
                <Box className="timestamp-info">
                  <Typography className="date-time sub-title-text">
                    {DateFormat(entry?.created_at, 'datesWithhour')}
                  </Typography>
                  <Typography className="time-only sub-title-text">
                    {DateFormat(entry?.created_at, 'hoursUTC')}
                  </Typography>
                </Box>
              </Box>

              {/* Content body */}
              <Box className="entry-body">
                {entry?.change_note && (
                  <Box className="note-section">
                    <Box className="d-flex align-center gap-xs">
                      <NoteIcon className="note-icon" />
                      <Typography className="note-label sub-title-text fw600">
                        Note:
                      </Typography>
                    </Box>
                    <Typography className="note-content body-text">
                      {entry?.change_note}
                    </Typography>
                  </Box>
                )}

                {entry?.new_status && (
                  <Box className="status-section">
                    <Box className="d-flex align-center gap-xs">
                      <CheckCircleIcon className="status-icon" />
                      <Typography className="status-label sub-title-text fw600">
                        Status changed to:
                      </Typography>
                    </Box>
                    <Chip
                      label={entry?.new_status}
                      color={getStatusChipColor(entry?.new_status)}
                      size="small"
                      className="status-chip"
                    />
                  </Box>
                )}
              </Box>
            </Box>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default AttachmentHistory;
