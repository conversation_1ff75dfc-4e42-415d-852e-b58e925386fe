import React from 'react';
import { Box, Typography } from '@mui/material';
import NoteIcon from '@mui/icons-material/Note';
import EditIcon from '@mui/icons-material/Edit';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import InfoIcon from '@mui/icons-material/Info';
import CircleIcon from '@mui/icons-material/Circle';
import { DateFormat } from '@/helper/common/commonFunctions';
import './attachmenthistory.scss';

const AttachmentHistory = ({ attachmentData }) => {
  // Helper function to format action type text
  const formatActionType = (actionType) => {
    if (!actionType) return '';
    return actionType
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  // Helper function to get action icon with color
  const getActionIcon = (actionType) => {
    switch (actionType?.toLowerCase()) {
      case 'status_changed':
      case 'status_update':
        return (
          <CheckCircleIcon
            className="calender-icon"
            style={{ fill: 'var(--color-success)' }}
          />
        );
      case 'note_added':
      case 'comment_added':
        return (
          <NoteIcon
            className="calender-icon"
            style={{ fill: 'var(--color-info)' }}
          />
        );
      case 'updated':
      case 'modified':
        return (
          <EditIcon
            className="calender-icon"
            style={{ fill: 'var(--color-warning)' }}
          />
        );
      default:
        return (
          <InfoIcon
            className="calender-icon"
            style={{ fill: 'var(--color-primary)' }}
          />
        );
    }
  };

  // Helper function to get dot color based on action type
  const getDotColor = (actionType) => {
    switch (actionType?.toLowerCase()) {
      case 'status_changed':
      case 'status_update':
        return 'var(--color-success)';
      case 'note_added':
      case 'comment_added':
        return 'var(--color-info)';
      case 'updated':
      case 'modified':
        return 'var(--color-warning)';
      default:
        return 'var(--color-primary)';
    }
  };
  // Safety check for attachmentData
  if (
    !attachmentData ||
    !Array.isArray(attachmentData) ||
    attachmentData.length === 0
  ) {
    return (
      <Box className="attachment-wrap">
        <Box className="no-history-message">
          <Typography className="sub-title-text">
            No history available
          </Typography>
        </Box>
      </Box>
    );
  }

  return (
    <Box className="attachment-wrap">
      <Box className="devider-wrap">
        <Box orientation="vertical" className="vertical-divider" />
      </Box>
      <Box className="attachment-history-wrap">
        {attachmentData.map((entry, index) => (
          <Box key={index} className="d-flex attachment-history">
            <Box className="devider-wrap">
              <Box orientation="vertical" className="vertical-divider" />
            </Box>
            <Box className="attachment-item">
              <Box className="d-flex align-center header-date-wrap">
                {getActionIcon(entry?.action_type)}
                <Typography component="p" className="title-text">
                  {DateFormat(entry?.created_at, 'datesWithhour')}
                </Typography>
              </Box>
              <Typography component="p" className="title-text">
                {DateFormat(entry?.created_at, 'hoursUTC')}
              </Typography>
              <Box className="d-flex align-center name-text-wrap">
                <CircleIcon
                  className="circle-wrap"
                  style={{ fill: getDotColor(entry?.action_type) }}
                />
                <Typography className="title-text">
                  {formatActionType(entry?.action_type)}
                </Typography>
              </Box>
              {entry?.change_note && (
                <Typography className="file-name body-text">
                  <span className="content-text">Note </span>
                  {entry?.change_note}
                </Typography>
              )}
              {entry?.new_status && (
                <Typography className="attachment-type-wrap body-text">
                  <span className="attachment-type-text content-text">
                    Status{' '}
                  </span>
                  {entry?.new_status}
                </Typography>
              )}
            </Box>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default AttachmentHistory;
