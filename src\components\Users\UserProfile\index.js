'use client';

import React, { useEffect, useContext, useState, useRef } from 'react';
import {
  Box,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
} from '@mui/material';
import AuthContext from '@/helper/authcontext';
import { useRouter, useSearchParams } from 'next/navigation';
import PreLoader from '@/components/UI/Loader';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import { fetchFromStorage, saveToStorage } from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import Onboarding from '../Onboarding';
import {
  setApiMessage,
  checkOrganizationRole,
} from '@/helper/common/commonFunctions';
import { leftSideMenu } from '@/helper/common/commonMenus';
import ProfileDetails from '../ProfileDetails';
import useRoleList from '@/hooks/useRoleList';
import useLocationData from '@/hooks/useLocationData';
import UserActivity from '@/components/Users/<USER>';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import './user.scss';
import {
  getUserDetails,
  getGeneralTemplateDetails,
  getDepartmentTemplateDetailsByID,
  getDepartmentTemplateDetails,
  getContractTypeDetails,
  handleCreateOptionContractPolicy,
  handleContractAction,
} from '@/services/userProfileService';

const UserPage = ({ params, isAdminProfile = false }) => {
  const router = useRouter();
  const formikRef = useRef(null);
  const formikRefcontract = useRef(null);
  const searchParams = useSearchParams();
  const IsADMIN = isAdminProfile
    ? searchParams.get('is_edit')
    : searchParams.get('IsAdmin');
  const isAdminId = searchParams.get('admin_id');
  const UserId = isAdminProfile ? isAdminId : params?.id;
  const { authState, userdata, setUserdata } = useContext(AuthContext);
  const {
    countries,
    counties,
    cities,
    setCities,
    setCounties,
    setSelectedCountry,
    setSelectedCounty,
  } = useLocationData();
  const { roleList, fetchRoleList } = useRoleList();
  const [UserDetails, setUserDetails] = useState('');
  const [loader, setLoader] = useState(false);
  const [getCheckList, setGetCheckList] = useState([]);
  const [formRightToWork, setFormRightToWork] = useState();
  const [formNewStarterHMRC, setFormNewStarterHMRC] = useState();
  const [formEmpContract, setFormEmpContract] = useState();
  const [formHealthSafety, setFormHealthSafety] = useState();
  const [profileImage, setProfileImage] = useState();
  const [expanded, setExpanded] = useState(false);
  const [isLoader, setIsLoader] = useState(false);
  const [isLoaderEmp, setIsLoaderEmp] = useState(false);

  const [generalTemplateData, setGeneralTemplateData] = useState([]);
  const [departmentTemplateData, setDepartmentTemplateData] = useState([]);
  const [departmentTemplateDataByID, setDepartmentTemplateDataByID] = useState(
    []
  );
  const [ContractList, setContractList] = useState([]);
  const [departmentTemByID, setDepartmentTempByID] = useState([]);
  const [contractTypeData, setContractTypeData] = useState([]);
  const [leaveTypeList, setLeaveTypeList] = useState([]);
  const [leaveTypeListall, setLeaveTypeListAll] = useState([]);
  const ViewAccessOnly = isAdminProfile
    ? authState?.UserPermission?.user === 1 ||
      UserDetails?.user_status === 'deleted'
    : authState?.UserPermission?.staff === 1 ||
      UserDetails?.user_status === 'deleted';
  const onDragEnd = (result) => {
    const { source, destination } = result;
    const newData = ContractList;
    const [movedRow] = newData.splice(source?.index, 1);
    newData.splice(destination?.index, 0, movedRow);
    setContractList(newData);
  };

  // Get user details by user id
  const getUserDetailsData = async (id) => {
    setLoader(true);
    try {
      const response = await getUserDetails(id);
      if (response.success) {
        setUserDetails(response.data);
        setProfileImage({ url: response.data?.user_avatar, IsFromAPi: true });
        const isSingleRelevantRole =
          response.data?.user_roles &&
          response.data?.user_roles?.length === 1 &&
          (response.data?.web_user_active_role_id === 2 ||
            response.data?.web_user_active_role_id === 1);
        if (!isSingleRelevantRole) {
          getOnboardingChecklist(UserId, true, response.data);
        }
        const firstId = response.data?.user_meta?.department_template
          ? response.data?.user_meta?.department_template
          : null;
        const getAlldepart =
          response.data?.user_meta?.additional_template &&
          response.data?.user_meta?.additional_template.split(',').map(Number);
        getDepartmentTemplateDetailsData(
          null,
          firstId,
          getAlldepart ? getAlldepart : []
        );
        getDepartmentTemplateDetailsByIDData(response.data?.department?.id);
        setLoader(false);
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // GET GENERAL TEMPLATE DETAILS
  const getGeneralTemplateDetailsData = async () => {
    setLoader(true);
    try {
      const response = await getGeneralTemplateDetails();
      if (response.success) {
        setLoader(false);
        setGeneralTemplateData(response.data);
      }
    } catch (error) {
      setLoader(false);
      setGeneralTemplateData([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // GET DEPARTMENT TEMPLATE DETAILS
  const getDepartmentTemplateDetailsByIDData = async (id) => {
    try {
      const response = await getDepartmentTemplateDetailsByID(id);
      if (response.success) {
        setLoader(false);
        setDepartmentTemplateDataByID(response.data);
      }
    } catch (error) {
      setLoader(false);
      setDepartmentTemplateDataByID([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // GET DEPARTMENT TEMPLATE DETAILS
  const getDepartmentTemplateDetailsData = async (is, id, allID) => {
    try {
      const response = await getDepartmentTemplateDetails(is, id, allID);
      if (response.success) {
        setLoader(false);
        const { departmentTemplateData, contractList, departmentTempByID } =
          response.data;
        setDepartmentTemplateData(departmentTemplateData);
        setContractList(contractList);
        setDepartmentTempByID(departmentTempByID);
      }
    } catch (error) {
      setLoader(false);
      setDepartmentTemplateData([]);
      setDepartmentTempByID([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // Get Form details by ID
  const getFormDetailsById = async (id, checkId, setValue) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_ONBOARDING_FORM_DETAILS +
          `?checklist_id=${checkId}&from_user_id=${id}`
      );

      if (status === 200) {
        setLoader(false);
        setValue(data?.formDetail);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);

      setLoader(false);
    }
  };
  // List of onboarding check list
  const getOnboardingChecklist = async (id, IsUpdate, users) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_ONBOARDING_CHECKLIST + `?user_id=${id}`
      );

      if (status === 200) {
        setGetCheckList(data?.checklist);
        getFormDetailsById(id, 1, setFormRightToWork);
        getFormDetailsById(id, 2, setFormNewStarterHMRC);
        getFormDetailsById(id, 3, setFormHealthSafety);
        if (
          users?.user_status !== 'active' &&
          users?.user_status !== 'pending'
        ) {
          getFormDetailsById(id, 4, setFormEmpContract);
        }
        if (IsUpdate !== true) {
          getUserDetailsData(id);
        }
        setLoader(false);
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const handleAccordianChange = (panel) => {
    expanded === panel ? setExpanded() : setExpanded(panel);
  };

  const ResendInvitation = async (id) => {
    setLoader(true);
    let sendData = { user_ids: [id] };
    const isOrgLogin =
      checkOrganizationRole('org_master') ||
      checkOrganizationRole('super_admin') ||
      checkOrganizationRole('staff');
    try {
      const { status, data } = await axiosInstance.post(
        isOrgLogin ? URLS?.ORG_SEND_INVITATION : URLS?.SEND_INVITATION,
        sendData
      );

      if (status === 200) {
        setLoader(false);
        setApiMessage('success', data?.message);
        getUserDetailsData(UserId);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setLoader(false);
    }
  };

  const regenerateEmp = async () => {
    setIsLoaderEmp(true);
    setIsLoader(true);
    try {
      const response = await handleContractAction(
        UserDetails?.id ? UserDetails?.id : UserId,
        'notify'
      );
      if (response.success) {
        setIsLoaderEmp(false);
        setIsLoader(false);
        getUserDetailsData(UserDetails?.id ? UserDetails?.id : UserId, true);
        getFormDetailsById(
          UserDetails?.id ? UserDetails?.id : UserId,
          4,
          setFormEmpContract
        );
        getOnboardingChecklist(UserId);
      }
    } catch {
      setIsLoaderEmp(false);
      setIsLoader(false);
    }
  };

  const handleCreateOptionContractPolicyData = async (inputValue) => {
    try {
      const response = await handleCreateOptionContractPolicy(inputValue);
      if (response.success) {
        setLoader(false);
        getContractTypeDetailsData(inputValue);
      }
    } catch {
      setLoader(false);
    }
  };

  // GET CONTRACT TYPE DETAILS
  const getContractTypeDetailsData = async (name) => {
    try {
      const response = await getContractTypeDetails();
      if (response.success) {
        setLoader(false);
        setContractTypeData(response.data);
        if (name) {
          const findname = response.data?.find((f) => f?.label === name);
          formikRefcontract.current.setFieldValue(
            'contractName',
            findname?.value
          );
        }
      }
    } catch {
      setLoader(false);
      setContractTypeData([]);
    }
  };

  const getLeaveTypeList = async () => {
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_LEAVE_TYPE + `?search=`
      );
      if (status === 200) {
        const tempData = data?.data?.map((item) => {
          const options = item?.leave_accural_policy?.map((sub) => {
            return {
              ...sub,
              label: sub?.leave_policy_name,
              value: sub?.id,
              parent_id: item?.id,
            };
          });
          return {
            ...item,
            label: item?.name,
            options: options,
          };
        });
        if (tempData && tempData?.length > 0) {
          setLeaveTypeList(tempData);
          setLeaveTypeListAll(tempData);
        }
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  useEffect(() => {
    const hasUserOrStaffPermission =
      [1, 2].includes(authState?.UserPermission?.user) ||
      [1, 2].includes(authState?.UserPermission?.staff);

    if (hasUserOrStaffPermission && UserId) {
      getUserDetailsData(UserId);
      fetchRoleList();
      getGeneralTemplateDetailsData();
      getContractTypeDetailsData();
      getLeaveTypeList();
    }
  }, [
    UserId,
    authState?.UserPermission?.user,
    authState?.UserPermission?.staff,
  ]);
  useEffect(() => {
    const { user, staff, ...permissions } = authState?.UserPermission || {};

    const isAdmin = IsADMIN === 'true';
    const hasNoAccess = (isAdmin && user === 0) || (!isAdmin && staff === 0);

    if (hasNoAccess) {
      const permissionAccess = leftSideMenu?.find((item) => {
        const permValue = permissions?.[item?.permission];
        return (
          permValue !== 0 && permValue !== '' && item?.link && item?.permission
        );
      });

      if (permissionAccess?.link) {
        router?.push(permissionAccess.link);
      }
    }
  }, [
    authState?.UserPermission?.staff,
    authState?.UserPermission?.user,
    IsADMIN,
  ]);
  const oldStaff = fetchFromStorage(identifiers?.RedirectData)
    ? fetchFromStorage(identifiers?.RedirectData)
    : userdata;

  useEffect(() => {
    if (oldStaff?.IsFromUser === undefined && oldStaff?.page !== undefined) {
      setUserdata({ ...userdata, IsFromUser: true });
      saveToStorage(identifiers?.RedirectData, {
        ...oldStaff,
        IsFromUser: true,
      });
    }
  }, [oldStaff]);

  useEffect(() => {
    if (
      UserDetails?.user_meta?.contract_type &&
      contractTypeData &&
      contractTypeData?.length > 0
    ) {
      const sel = contractTypeData?.find(
        (f) => f?.id === UserDetails?.user_meta?.contract_name_id
      );
      formikRefcontract.current.setFieldValue('contractName', sel);
    }
  }, [contractTypeData, UserDetails?.user_meta?.contract_type]);
  useEffect(() => {
    if (
      UserDetails?.leave_policy_list &&
      UserDetails?.leave_policy_list?.length > 0 &&
      leaveTypeListall &&
      leaveTypeListall?.length > 0
    ) {
      const sel = UserDetails?.leave_policy_list?.map((f) => {
        return {
          ...f?.leave_accural_policy,
          label: f?.leave_accural_policy?.leave_policy_name,
          value: f?.leave_accural_policy?.id,
          parent_id: f?.id,
        };
      });
      if (sel && sel?.length > 0) {
        const parentIds = sel?.map((policy) => policy?.parent_id);
        const filteredLeaveTypes = leaveTypeListall?.filter(
          (type) => !parentIds.includes(type?.id)
        );
        setLeaveTypeList(filteredLeaveTypes);
      } else {
        setLeaveTypeList(leaveTypeListall);
      }
      if (formikRefcontract?.current) {
        formikRefcontract.current.setFieldValue('policyholiday', sel);
      }
    }
  }, [leaveTypeListall, UserDetails?.leave_policy_list]);

  return (
    <>
      <Box className="user-container">
        {loader ? (
          <PreLoader />
        ) : (
          <Box className="User-profile-page">
            <Box className="user-section">
              <ProfileDetails
                profileImage={profileImage}
                oldStaff={oldStaff}
                ViewAccessOnly={ViewAccessOnly}
                setProfileImage={setProfileImage}
                UserDetails={UserDetails}
                UserId={UserId}
                setLoader={setLoader}
                formikRef={formikRef}
                formikRefcontract={formikRefcontract}
                roleList={roleList}
                isLoader={isLoader}
                contractTypeData={contractTypeData}
                leaveTypeList={leaveTypeList}
                departmentTemplateDataByID={departmentTemplateDataByID}
                departmentTemplateData={departmentTemplateData}
                loader={loader}
                onDragEnd={onDragEnd}
                isLoaderEmp={isLoaderEmp}
                ContractList={ContractList}
                setContractList={setContractList}
                regenerateEmp={regenerateEmp}
                getUserDetails={getUserDetailsData}
                setIsLoader={setIsLoader}
                departmentTemByID={departmentTemByID}
                handleCreateOptionContractPolicy={
                  handleCreateOptionContractPolicyData
                }
                generalTemplateData={generalTemplateData}
                setDepartmentTemplateData={setDepartmentTemplateData}
                leaveTypeListall={leaveTypeListall}
                setLeaveTypeList={setLeaveTypeList}
                ResendInvitation={ResendInvitation}
                countries={countries}
                counties={counties}
                cities={cities}
                setCities={setCities}
                setCounties={setCounties}
                setSelectedCountry={setSelectedCountry}
                setSelectedCounty={setSelectedCounty}
              />
              {(UserDetails?.web_user_active_role_id === 2 ||
                UserDetails?.web_user_active_role_id === 1) &&
                UserDetails?.user_roles &&
                UserDetails?.user_roles.length === 1 && (
                  <>
                    <Accordion
                      elevation={0}
                      className="onboarding-accordion accordion-content"
                      expanded={expanded === 1}
                      onChange={() => handleAccordianChange(1)}
                    >
                      <AccordionSummary
                        expandIcon={<KeyboardArrowDownIcon />}
                        className=""
                      >
                        <Typography className=" p20  fw600">
                          Activity
                        </Typography>
                      </AccordionSummary>
                      <AccordionDetails className="">
                        <UserActivity userId={UserId} />
                      </AccordionDetails>
                    </Accordion>
                  </>
                )}
              {((UserDetails?.web_user_active_role_id !== 2 &&
                UserDetails?.web_user_active_role_id !== 1) ||
                (UserDetails?.user_roles &&
                  UserDetails?.user_roles.length > 1)) && (
                <>
                  <Divider className="mb16 mt16" />
                  <Onboarding
                    UserId={UserId}
                    getCheckList={getCheckList}
                    formRightToWork={formRightToWork}
                    formNewStarterHMRC={formNewStarterHMRC}
                    formHealthSafety={formHealthSafety}
                    UserDetails={UserDetails}
                    formEmpContract={formEmpContract}
                    getOnboardingChecklist={getOnboardingChecklist}
                    setLoaderMain={setLoader}
                    countryList={countries}
                    IsMainLoader={true}
                    ViewAccessOnly={
                      ViewAccessOnly || UserDetails?.user_status === 'pending'
                    }
                  />
                </>
              )}
            </Box>
          </Box>
        )}
      </Box>
    </>
  );
};

export default UserPage;
