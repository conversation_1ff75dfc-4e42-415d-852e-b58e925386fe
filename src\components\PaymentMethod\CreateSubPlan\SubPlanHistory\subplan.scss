@import '@/styles/variable.scss';

.sub-plan-wrap {
  .subplan-table-container {
    border: 1px solid $color-Dark-10;
    border-radius: 15px;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;

    .sub-plan-table {
      width: 100%;
      border-collapse: unset;
      min-width: 900px;

      .table-body-wrap {
        .MuiTableRow-root:last-child {
          .MuiTableCell-root {
            border-bottom: none;
          }
        }

        .MuiTableCell-root {
          color: $color-Dark-40;
        }
        .plan-badge {
          display: inline-block;
          width: 10px;
          height: 10px;
          border-radius: 50%;
          margin-right: 5px;
        }
        .free-plan {
          background-color: $color-orange;
        }
        .paid-plan {
          background-color: $color-green;
        }

        .plan-name-wrap {
          color: $color-Dark-60;
        }

        .plan-discount {
          color: $color-green;
          border-radius: 8px;
        }

        .no-discount {
          color: $error;
          font-weight: bold;
          font-size: 20px;
        }
      }
      .public-text {
        color: $color-green;
      }
      .private-text {
        color: $color-Primary-90;
      }
      .action-icons {
        width: 50px;
        text-align: start;
        .icon {
          padding: 0;

          .edit-icon,
          .delete-icon {
            fill: $color-primary;
            height: 21px;
            width: 21px;
          }
        }
        .icon:first-child {
          margin-right: 8px;
        }
      }
    }

    .pagination-wrap {
      padding: 22px !important;
    }
  }
}
