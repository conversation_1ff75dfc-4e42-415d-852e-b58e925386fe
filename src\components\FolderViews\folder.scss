@import '@/styles/variable.scss';
.folder-conatiner {
  box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
  border-radius: 8px;
  padding: 12px 16px;
  &:hover {
    box-shadow: rgba(0, 0, 0, 0.16) 0px 5px 10px;
  }
  .folder-details {
    .folder-checkbox {
      margin-right: 0 !important;
      .MuiButtonBase-root {
        padding: 3px 0 3px 9px;
      }
    }
    .menu-icons {
      svg {
        height: 20px;
        width: 20px;
      }
    }
  }
  .table-checkbox {
    color: var(--color-primary);
  }
  .file-preview-img {
    width: 100%;
    height: 200px;
    margin-top: 20px;
    img {
      width: 100%;
      height: 100%;
      object-fit: fill;
      border-radius: 4px;
    }
  }
  .image-icon {
    width: 100px;
    height: 100px;
    fill: var(--color-primary) !important;
  }
  .youtube-icon {
    width: 100px;
    height: 100px;
    color: var(--color-danger) !important;
  }
  .folder-size-time-icon {
    width: var(--icon-size-xs);
    height: var(--icon-size-xs);
  }
}
.folder-conatiner {
  .inactive-foldar {
    opacity: 0.3;
  }
}
.assign-icon {
  width: 22px !important;
  height: 22px !important;
}
.link-text-name-active {
  cursor: pointer !important;
  color: var(--text-color-primary) !important;
  font-weight: 600 !important;
}
.folder-list-view {
  .MuiTableContainer-root {
    padding: 10px 10px;
    .MuiTableCell-head,
    .MuiTableCell-body {
      padding: 8px;
      border-bottom: none;
    }
  }
  .table-checkbox {
    padding: 0px;
    color: var(--color-primary);
  }
  .details-table-row {
    box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
    border-radius: 4px;
  }
  .blank-table-row {
    height: 10px;
  }
  .list-folder-name-view {
    max-width: 400px;
    width: 100%;
    .list-view-folder {
      width: 35px;
      height: 35px;
      @media (max-width: 768px) {
        width: 30px;
        height: 30px;
      }
    }
  }
  .image-icon {
    width: 40px;
    height: 40px;
    fill: var(--color-primary) !important;
  }
  .youtube-icon {
    width: 40px;
    height: 40px;
    color: var(--color-danger) !important;
  }
  .svg-icon {
    width: 17px;
    height: 17px;
    margin-right: 5px;
  }
}
.opened-layout {
  .folder-sidebar-container {
    // margin-left: 257px;
    // margin-left: 289px; //some space for sidebar
  }
}
.folder-sidebar-container {
  .mobile-view-menu {
    display: none;
    @media (max-width: 768px) {
      display: flex;
      padding: 10px 20px;
      .MuiTypography-root {
        margin-left: 20px;
      }
    }
  }
  .folder-side-menu {
    @media (max-width: 768px) {
      display: none;
    }
  }
}

.sidebar-folder-container {
  height: 100%;
  .mobile-view-menu {
    display: none;
    @media (max-width: 768px) {
      display: flex;
      padding: 10px 20px;
      .MuiTypography-root {
        margin-left: 20px;
      }
    }
  }
  .folder-side-menu {
    height: 100%;
    @media (max-width: 768px) {
      display: none;
    }
  }
}

.document-sidebar-container {
  .mobile-view-menu {
    display: none;
    @media (max-width: 768px) {
      display: flex;
      padding: 10px 20px;
      .MuiTypography-root {
        margin-left: 20px;
      }
    }
  }
  .folder-side-menu,
  .desktop-sidebar {
    @media (max-width: 768px) {
      display: none;
    }
  }
  .folder-doc-side-menu {
    height: auto !important;
    min-height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .memory-usage-for-sidemenu {
      margin-left: 8px;
      margin-right: 8px;
      margin-bottom: 8px;
      padding: 5px 7px;
      border-radius: 10px;
      width: calc(240px - 16px);
      background-color: var(--color-light-blue);
      .storage {
        display: flex;
        align-items: center;
        color: var(--text-color-primary) !important;
        p {
          color: var(--text-color-primary) !important;
        }
        svg {
          margin-right: 5px;
          // fill: #5294e2;
          fill: var(--color-primary);
        }
      }
      .progress-bar {
        border-radius: 21px;
        margin: 3px 0;
        background-color: var(--color-dark-lavender);
        .MuiLinearProgress-barColorPrimary {
          background-color: var(--color-primary);
        }
      }
    }
  }
}
.folder-side-menu {
  @media (max-width: 768px) {
    width: 240px;
  }
}
.folder-doc-side-menu {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .memory-usage-for-sidemenu {
    margin-left: 8px;
    margin-right: 8px;
    margin-bottom: 8px;
    margin-top: 20px;
    padding: 5px 7px;
    border-radius: 10px;
    width: calc(240px - 16px);
    background-color: var(--color-soft-lavender);
    .storage {
      display: flex;
      align-items: center;
      color: var(--color-dark-blue);
      svg {
        margin-right: 5px;
        // fill: #5294e2;
        fill: $color-Primary-90;
        // fill: $color-Dark-70;
      }
    }
    .progress-bar {
      border-radius: 21px;
      margin: 3px 0;
      background-color: $color-Primary-30;
      .MuiLinearProgress-barColorPrimary {
        background-color: var(--color-soft-lavender) 0;
      }
    }
  }
}
.folder-create-btn {
  padding: var(--spacing-md);
  .custom-button {
    padding: 10px 10px !important;
    svg {
      path {
        stroke: var(--color-white);
      }
    }
    &:hover {
      svg {
        path {
          stroke: $color-Dark-100;
        }
      }
    }
  }
}
.folder-name-list-container,
.folder-view-container {
  height: 100%;
  .folder-sidebar-header {
    padding: 10px 20px;
  }
  color: $color-Dark-40;
  .folder-list-wrap {
    height: calc(100% - 110px); //remove button container height and title
    overflow: auto;
    margin-bottom: 20px;
    .folder-list-item {
      padding: 2px 0px;
      align-items: flex-start;

      .folder-list-btn {
        &:hover {
          background-color: var(--color-primary-opacity);
        }
        .sidebar-list-icon {
          width: var(--icon-size-sm);
          height: var(--icon-size-sm);
        }
        .sidebar-list-menu-name {
          margin-left: var(--spacing-sm);
        }
      }
      .folder-list-btn.Mui-selected {
        background-color: var(--color-primary-opacity);
        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          width: 4px;
          border-radius: 0 5px 5px 0;
          background: var(--color-primary);
        }
      }
      .document-folder-btn {
        max-width: 18px;
        min-width: 18px;
        padding: 0;
        cursor: default;
        align-self: flex-start;
        margin-right: 4px;
      }
      .sidebar-list-menu-name {
        margin: 0;
      }
      .MuiCheckbox-root {
        padding: 0;
      }
      .sidebar-menu {
        // max-width: calc(100% - 24px - 24px);
        .MuiTypography-root {
          font-family: var(--font-family-primary);
          font-size: var(--font-size-sm);
          font-weight: var(--font-weight-regular);
          color: var(--text-color-black);
          line-height: var(--line-height-base);
        }
      }
      .redirect-icon {
        fill: var(--color-primary) !important;
        width: 15px;
        height: 15px;
        margin-left: 10px;
        cursor: pointer;
      }
    }
    .popover-list-item {
      padding: 3px 0px;
    }
    .folder-menu {
      .document-folder-btn {
        cursor: pointer !important;
      }
    }
  }
}
.folder-popover {
  // width: inherit;
  .MuiPaper-root {
    width: inherit;
  }
  .folder-list-item {
    padding: 0px 14px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    cursor: pointer;
    .document-folder-btn {
      padding: 0;
      display: flex;
      flex-grow: 0;
      margin-right: 4px;
      .sidebar-list-icon {
        width: 24px;
        height: 24px;
        fill: var(--color-primary) !important;
      }
      .folder {
        fill: #ffca28 !important;
      }
    }
    .menu-name {
      margin: 0;
      .MuiTypography-root {
        font-family: var(--font-family-primary);
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-regular);
        color: var(--text-color-black);
        line-height: var(--line-height-base);
      }
    }
  }
}
.User-track-section {
  .User-track-folders {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 16px;
    .folder-content {
      display: flex;
      align-items: center;
      border-radius: 8px;
      padding: 4px 16px 4px 8px;
      background-color: var(--color-light-blue);
      color: var(--text-color-black);
      max-width: max-content;

      .list-view-folder {
        width: 18px;
        height: 18px;
        margin-right: 5px;
        margin-top: -5px;
      }
      .link-text-name {
        font-weight: 500;
      }
    }
    .selected-folder-content {
      background-color: var(--color-dark-lavender);

      .link-text-name {
        font-weight: 600 !important;
        color: var(--color-dark-blue) !important;
      }
    }
    .list-view-file {
      width: 20px;
      height: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 5px;
      .list-view-file-image {
        width: 18px;
        height: 18px;
        object-fit: cover;
        border-radius: 5px;
      }
    }
  }
  .reset-icon {
    svg {
      fill: var(--action-icon-bg-color) !important;
    }
  }
}
.document-center-media-section {
  .document-center-media-view {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: calc(
      100vh - 76px - 70px - 36px - 24px - 15px - var(--banner-height)
    );
    //  height: calc(100vh - 76px - 70px - 36px - 24px - 15px);
    // overflow-y: scroll;
    .document-media-slider {
      .slick-dots li button:before {
        font-size: 10px;
      }
      .slick-arrow {
        display: none !important;
      }
    }
    .document-media-view {
      .slick-dots li button:before {
        font-size: 10px;
      }
      .slick-list {
        margin: 0 24px;
      }
      .slick-prev {
        left: 0 !important;
        z-index: 10;
      }
      .slick-prev::before {
        color: $color-primary !important;
      }
      .slick-next {
        right: 0 !important;
      }
      .slick-next::before {
        color: var(--color-primary) !important;
      }

      .image-container {
        width: 64px;
        height: 64px;
        .pdf-svg,
        img,
        video,
        iframe {
          width: 64px;
          height: 64px;
          object-fit: cover;
          border-radius: 5px;
          cursor: pointer;
        }
        .pdf-svg {
          fill: var(--color-primary) !important;
        }
        .video-section {
          position: relative;
          cursor: pointer;
          .play-icon {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10;
            svg {
              width: 21px;
              height: 21px;
              fill: var(--color-white) !important;
            }
          }
        }
        .youtube-slide {
          width: 64px;
          height: 64px;
          background-color: var(--color-black);
          .youtube-icon svg {
            width: 44px;
            height: 44px;
          }
          .youtube-icon-back {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 8;
            .back {
              height: 16px;
              width: 16px;
              background: white;
            }
          }
        }
      }
    }
    .image-container {
      width: 100%;
      height: 300px;
      max-height: 300px;
      display: flex !important;
      justify-content: center;
      align-items: center;
      padding: 18px;
      .pdf-svg {
        width: 100px;
        height: 100px;
        cursor: pointer;
      }
      img,
      video {
        // border-radius: 5px;
        width: auto;
        max-width: 100%;
        height: auto;
        margin: auto;
        display: block;
        // aspect-ratio: 16 / 9;
        object-fit: cover;
        max-height: 260px;
      }
      video,
      iframe {
        max-height: 260px;
        width: 100%;
      }
    }
    .preview-image {
      // width: 100%;
      // height: 100%;

      .media {
        // width: 100%;
        // height: auto;
        // border-radius: 5px;
        width: auto;
        max-width: 100%;
        height: auto;
        margin: auto;
        display: block;
        // aspect-ratio: 16/9;
        object-fit: cover;
      }
      .react-player-audio {
        width: 290px !important;
        height: 60px !important;
        margin: 0 auto !important;
      }
    }
    .documents-buttons {
      display: flex;
      justify-content: space-between;
      align-items: center;
      // position: absolute;
      // width: 100%;
      // bottom: 0;
    }
  }
  ul,
  ol {
    padding-left: 20px;
  }
  .document-center-desc {
    table {
      border-collapse: collapse;

      td {
        padding: 4px;
        border: var(--table-border);
      }
    }
  }
  .mb26 {
    margin-bottom: 54px;
  }
  .document-center-desc {
    table {
      border-collapse: collapse;

      td {
        padding: 4px;
        border: var(--table-border);
      }
    }
  }
}
.Action-menu-popover {
  .MuiListItemIcon-root {
    min-width: 30px !important;
  }
  .Delete {
    svg {
      margin-left: -3px;
    }
  }
  .Copy,
  .Move,
  .Update {
    svg {
      width: 21px;
      height: 21px;
    }
  }
}
.inactive-folder {
  opacity: 0.3;
}
.move-copy-document {
  .sidebar-list-menu-name {
    margin-right: 10px;
    .MuiTypography-root {
      overflow: hidden;
    }
  }
  .MuiCheckbox-root.Mui-checked {
    color: var(--color-primary);
  }
}
.Onboarding-media-view {
  .onboarding-media {
    min-height: 300px;
  }
}
.actions-staff {
  width: 72px;
  gap: 10px;
}
.User-track-logs {
  margin-top: 24px;

  .folder-desc-divider {
    border-bottom: var(--normal-sec-border);
    margin: 16px 0;
  }
  .tree {
    padding: 0;
    padding-left: 9px;
    list-style: none;
    color: #369;
    position: relative;

    li {
      margin: 0;
      line-height: 2em;
      font-weight: bold;
      position: relative;
      list-style: none;

      &:last-child {
        &:before {
          background: var(--color-white);
          height: auto;
          top: 1em;
          bottom: 0;
        }
      }
    }

    ul {
      margin: 0 0 0 1em;
      padding: 0;
      list-style: none;
      color: #000;
      position: relative;
      margin-left: 0.5em;

      li {
        margin: 0;
        padding: 0px 5px 0px 30px;
        position: relative;
        list-style: none;

        &:before {
          content: '';
          display: block;
          width: 40px;
          border-top: var(--table-border);
          margin-top: -5px;
          position: absolute;
          left: -3px;
          color: #393939;
          top: 10px;
          width: 7px;
          height: 7px;
          border-radius: 50%;
          background-color: #393939;
        }

        &:last-child {
          &:before {
            background: white;

            bottom: 0;

            left: -3px;
            // height: 100%;
            top: 10px;
            width: 7px;
            height: 7px;
            border-radius: 50%;
            background-color: #393939;
          }
        }
      }

      &:before {
        content: '';
        display: block;
        width: 0;
        position: absolute;
        top: 6px;
        bottom: 0;
        left: 0;

        // border-left: 1px solid var( --color-soft-lavender)0;
        border-left: 2px solid $color-Dark-20;
        color: #393939;
      }
      .event-type-tree {
        padding-top: 14px;
        &:before {
          color: #606060;
        }
        li {
          &:before {
            color: #606060;
          }
          span {
            color: #606060;
            span {
              color: #000000;
            }
          }
          .list {
            margin-bottom: 8px !important;
            display: flex;
            align-items: baseline;
          }
        }
      }
    }
  }
  .signature-list {
    margin-top: 22px;
    max-width: max-content;
  }

  .list-view {
    position: relative;
  }
  .Image-profile {
    max-width: max-content;
    position: absolute;
    left: -16px;
    top: 0;
    .profile-icon {
      width: 36px;
      height: 36px;
    }
    .profile-image {
      .profile {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        object-fit: cover;
      }
    }
  }
  .sign-activity {
    span {
      font-family: var(--font-family-primary);
      font-size: var(--font-size-xs);
      font-weight: var(--font-weight-regular);
      color: var(--text-color-black);
      line-height: var(--line-height-base);
    }
    margin-top: -2px;
  }
}
.form-checkbox {
  display: flex;

  .check-box-form {
    flex-direction: row-reverse;
    margin-left: 0;

    .MuiFormControlLabel-label {
      font-family: var(--font-family-primary);
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-regular);
      color: var(--text-color-black);
      line-height: var(--line-height-base);
    }
    .MuiCheckbox-root.Mui-checked {
      color: var(--color-primary);
    }
  }
  .form-row {
    flex-direction: row !important;
    .MuiButtonBase-root {
      padding-left: 0 !important;
    }
  }
  .approved-checkbox {
    color: var(--text-green);

    .MuiFormControlLabel-label {
      font-weight: 600;
    }

    .Mui-disabled.MuiTypography-root {
      color: var(--text-green) !important;
    }

    .MuiTypography-root {
      color: var(--text-green) !important;
    }

    svg {
      fill: var(--text-green) !important;
    }
  }

  .reject-checkbox {
    color: var(--color-danger);

    .MuiFormControlLabel-label {
      font-weight: 600;
    }

    .Mui-disabled.MuiTypography-root {
      color: var(--color-danger) !important;
    }

    .MuiTypography-root {
      color: var(--color-danger) !important;
    }

    svg {
      fill: var(--color-danger) !important;
    }
  }
  .Mui-disabled {
    opacity: 1;
    cursor: not-allowed !important;
    .MuiCheckbox-root.Mui-checked {
      color: var(--color-primary);
    }
    .MuiTypography-root {
      color: var(--color-black) !important;
    }
  }
}
