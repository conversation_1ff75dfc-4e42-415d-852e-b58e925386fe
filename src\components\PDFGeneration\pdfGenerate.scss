@import '@/styles/variable.scss';
.pdf-generate-main-container {
  width: 100%;
  margin: 0 auto;
  padding: 20px;
  .pdf-watermark {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-45deg);
    opacity: 0.2;
    font-size: 100px;
    color: #ccc;
    // z-index: 9999;
  }
  // .MuiTypography-root {
  //   font-family: Arial, Helvetica, sans-serif !important;
  // }
  .m0 {
    margin: 0px !important;
  }
  .mt20 {
    margin-top: 20px !important;
  }
  .ml20 {
    margin-left: 20px !important;
  }
  .ml32 {
    margin-left: 32px !important;
  }
  .mt40 {
    margin-top: 40px !important;
  }
  .ml40 {
    margin-left: 40px !important;
  }
  .mt8 {
    margin-top: 8px !important;
  }
  .mb8 {
    margin-bottom: 8px !important;
  }
  .w90 {
    width: 90%;
  }
  .p18 {
    font-family: $PrimaryFont !important;
    font-size: 18px !important;
    line-height: 20px !important;
    letter-spacing: -0.5px !important;
  }
  .p11 {
    font-family: $PrimaryFont !important;
    font-size: 11px !important;
    line-height: 1.5 !important;
    letter-spacing: 0px !important;
  }
  .text-start {
    text-align: start;
  }
  .flex-column {
    flex-direction: column;
  }
  .align-items-baseline {
    align-items: baseline;
  }
  .pdf-generate-title {
    text-align: center;
  }
  .text-underline {
    text-underline-offset: 3px;
  }
  .pdf-generate-checkbox-sub-text {
    font-size: 16px;
    font-weight: 400;
    &::before {
      content: '';
      margin-right: 10px;
      border: 1px solid black;
      display: inline-block;
      height: 15px;
      width: 12px;
    }
  }
  .name-wrapper {
    border: 1px solid #000000;
    display: grid;
    grid-template-columns: 3fr 2fr 3fr;
    height: 60px;
  }
  .checkbox {
    margin-right: 10px;
    margin-left: 5px;
    height: 18px;
    width: 18px;
    -webkit-appearance: none;
    appearance: none;
    border: 1px solid #000000;
    outline: none;
    cursor: pointer;
    position: relative;
  }
  .checkbox:checked {
    background-color: #ffffff;
  }
  .checkbox:checked::after {
    content: '';
    position: absolute;
    left: 5px;
    top: 2px;
    width: 5px;
    height: 10px;
    border: 1px solid #000000;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
  }
  .radiobox {
    margin-right: 10px;
    margin-left: 5px;
    height: 18px;
    width: 18px;
  }
  .input-field {
    height: 20px;
  }
  .bank-information {
    margin-top: 15px;
    .bank-details-table {
      .bank-address {
        height: 80px;
      }
    }
  }
  .grid-2container {
    display: grid;
    grid-template-columns: 2fr 2fr;
  }
  table {
    border-collapse: collapse;
    width: 100%;
  }
  td {
    border: 1px solid black;
    padding: 4px;
    text-align: left;
    vertical-align: top;
    width: 50%;
  }
  .next-kin-information {
    margin-top: 15px;
  }
  .additional-personal-information {
    .additional-other-details {
      position: relative;
      border: 1px solid black;
      height: 80px;
    }
  }
  .professional-information {
    margin-top: 15px;
    .professional-details-table {
      .table-row {
        height: 100px;
      }
    }
  }
  .signature-wrapper {
    .signature-content {
      display: grid;
      grid-template-columns: 3fr 2fr;
      align-items: flex-end;
      .signature {
        // max-width: 100px;
        // height: 20px;
        // object-fit: cover;
        width: 180px;
        aspect-ratio: auto 180 / 60;
        height: 60px;
      }
    }
  }
  // Health And Safety
  .health-safety-information {
    .health-safety-table {
      td {
        padding: 0px;
      }
      .w70 {
        width: 70%;
      }
      .w30 {
        width: 30%;
      }
    }
  }
  .bold-divider {
    border: 1px solid #000000;
    width: 100%;
    // margin: 10px 0px;
  }
  .square-number {
    border: 1px solid #000000;
    padding: 0px 5px 0px 20px;
    margin-right: 5px;
  }
  .custom-input-field {
    padding: 10px 10px;
    border-style: solid;
    border-color: #000000;
    border-width: 1px 1px 0px 1px;
    position: relative;
  }
  .custom-field-value {
    position: absolute;
    top: -0px;
    left: 5px;
    line-height: 20px !important;
  }
  .one-line-ellipsis {
    word-break: break-word;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
  .two-line-ellipsis {
    word-break: break-word;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  .three-line-ellipsis {
    word-break: break-word;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  .field-text-placeholder {
    position: absolute;
    color: gray;
    position: absolute;
    top: 2px;
    left: 5px;
  }
  .border-b {
    border-bottom: 1px solid #000000;
  }
  .hmrc-header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .header-title {
      font-size: 28px !important;
    }
  }
  .hmrc-header-logo {
    width: 130px;
    height: 100%;
    img {
      width: 100%;
      height: 100%;
      // object-fit: contain;
    }
  }
  .hmrc-information-wrapper {
    // .custom-input-field {
    //   max-width: 80%;
    // }
    .number-field-container {
      .checkbox {
        margin-right: 0px;
      }
    }
    .statement-information {
      .statement-details-table {
        td {
          width: 33.33%;
        }
      }
    }
  }
  .signature-field {
    .signature {
      max-width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .footer-wapper {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    text-align: center;
    align-items: center;
  }
  .hmrc-contract-footer {
    display: grid;
    grid-template-columns: 1fr;
    text-align: center;
    align-items: center;
  }
}
// Employee contract
.employee-contract-wrapper {
  padding: 150px 80px 20px 80px;
  .nv-header-logo {
    width: 100%;
    img {
      width: 100px;
      height: auto;
    }
  }
  .arial-font {
    font-family: Arial !important;
  }
  .MuiTypography-root {
    font-family: Arial !important;
  }
  ul {
    li {
      &::marker {
        font-size: 14px;
      }
    }
  }
  .ml35 {
    margin-left: 35px;
  }
  .emp-contents {
    .nestedList {
      list-style-type: none;
      padding-left: 1rem;
    }
  }
  .emp-list-data {
    .nestedList {
      list-style-type: none;
      padding-left: 1rem;
      padding-top: 1rem;
      li {
        padding-top: 1rem;
        :first-child {
          padding-top: 0px;
        }
      }
      .description-list {
        li {
          list-style-type: disc;
          margin-left: 50px;
          padding-top: 0px;
        }
      }
    }
  }
  .emp-signature-wrapper {
    .emp-signature-content {
      display: grid;
      grid-template-columns: 2fr 2fr 2fr;
      .text-sign-border {
        border-bottom: 1px solid black;
        width: 50%;
        margin-top: 50px;
        margin-bottom: 40px;
      }
    }
  }
  .emp-contract-footer {
    display: grid;
    grid-template-columns: 1fr;
    text-align: center;
    align-items: center;
  }
}

.pdf-download-btn {
  justify-content: left !important;
  padding: 0 !important;
  align-items: center !important;
  text-transform: capitalize !important;
  color: #000000 !important;
  font-weight: 400 !important;
  svg {
    height: 20px;
    width: 20px;
    path {
      fill: #a6a8b1;
    }
  }
  .text-underline {
    color: #a6a8b1 !important;
  }
}
.pb12 {
  padding-bottom: 12px;
}
