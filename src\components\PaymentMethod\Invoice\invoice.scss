@import '@/styles/variable.scss';

.invoice-container {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  box-shadow: 0px 0px 2px $color-Dark-30;
  border-radius: 8px;

  .invoice-wrap {
    .invoice-header-wrap {
      .invoice-header {
        padding: 20px;
      }

      .paid-on-wrap {
        padding: 20px;
        gap: 8px;
        background-color: #f0fdf4;
        border-bottom: 2px solid #16a34a;
        border-top: 1px solid $color-Dark-10;

        .check-icon {
          fill: #16a34a;
        }

        @media (max-width: 424px) {
          padding: 15px 10px;
          font-size: 14px;
        }
      }
    }

    .invoice-middle-sec-wrap {
      padding: 35px 20px 0px 20px;
      min-height: calc(100vh - 330px - var(--banner-height));

      .date-wrap {
        margin-bottom: 25px;

        .date {
          width: 50%;

          .issue-date,
          .due-date {
            font-size: 16px;
          }

          @media (max-width: 575px) {
            width: 100%;
          }
        }

        @media (max-width: 575px) {
          flex-direction: column;
          row-gap: 20px;
        }
      }

      .from-to-wrap {
        margin-bottom: 25px;

        .from-wrap {
          width: 50%;

          .from-name,
          .from-two-name {
            font-size: 16px;
            padding-bottom: 5px;
          }

          .from-adress,
          .from-two-adress {
            max-width: 230px;
            font-size: 15px;
            color: $color-Dark-40;
          }
        }

        @media (max-width: 575px) {
          flex-direction: column;
          row-gap: 20px;

          .from-wrap {
            width: 100%;
          }
        }
      }

      .plan-name-wrap {
        font-size: 16px;
        margin-bottom: 25px;
      }

      .table-wrap {
        padding: 25px 0px;

        .invoice-table-wrap {
          border: 1px solid $color-Dark-10;
          margin-top: 15px;
          border-radius: 8px;
          overflow: auto;

          .invoice-table {
            .total-amount {
              @media (max-width: 575px) {
                text-align: center !important;
              }
            }

            .MuiTableCell-root {
              border: none;
            }

            .MuiTableBody-root {
              .MuiTableRow-root:last-child {
                .MuiTableCell-root {
                  border-top: 1px solid $color-Dark-10;
                }
              }
            }
          }
        }
      }

      @media (max-width: 575px) {
        padding: 35px 14px 0px 14px;
      }
    }
  }

  .invoice-btn-wrap {
    padding: 10px 20px;
    gap: 10px;

    .download-rec-btn,
    .mark-btn {
      padding: 7px 24px !important;
      background-color: white !important;
      color: $color-primary !important;
      box-shadow: 0px 0px 2px $color-primary !important;
      border: none;
      font-size: 16px !important;
      font-weight: 500 !important;

      svg {
        fill: $color-primary !important;
      }

      &:hover {
        box-shadow: 0px 0px 2px $color-primary !important;
      }

      @media (max-width: 767px) {
        padding: 5px 9px !important;
        font-size: 14px !important;
      }
    }

    @media (max-width: 575px) {
      flex-direction: column;
    }
  }

  .text-wrap {
    font-size: 14px !important;
    color: $color-Dark-20;
    padding-bottom: 5px;
  }
}
