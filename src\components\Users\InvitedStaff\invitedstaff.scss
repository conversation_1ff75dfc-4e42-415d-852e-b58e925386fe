// .search-filter-section {
//   display: flex;
//   column-gap: 25px;
//   align-items: center;
//   .staff-search {
//     max-width: calc(100% - 75px - 25px - 165px - 124px) !important;
//   }
//   .filter-icon {
//     border: 1px solid $color-Black;
//     border-radius: 3px;
//     width: 25px;
//     height: 25px;
//     cursor: pointer;
//   }
//   @media (max-width: 799px) {
//     column-gap: 15px;
//     .staff-search {
//       max-width: calc(100% - 45px - 25px - 165px - 124px) !important;
//     }
//   }
//   @media (max-width: 599px) {
//     display: flex;
//     flex-wrap: wrap;
//     row-gap: 25px;
//     .staff-search {
//       width: 85% !important;
//       max-width: 85% !important;
//     }
//     .filter-icon {
//       max-width: 15%;
//     }
//     button {
//       width: 45%;
//     }
//   }
//   @media (max-width: 599px) {
//     row-gap: 15px;
//     button {
//       width: 100%;
//     }
//   }
// }
// .search-filter-section.staff-permission {
//   .staff-search {
//     max-width: calc(100% - 50px - 25px - 124px) !important;
//   }
//   @media (max-width: 799px) {
//     column-gap: 15px;
//     .staff-search {
//       max-width: calc(100% - 30px - 25px - 124px) !important;
//     }
//   }
//   @media (max-width: 599px) {
//     display: flex;
//     flex-wrap: wrap;
//     row-gap: 25px;
//     .staff-search {
//       width: 85% !important;
//       max-width: 85% !important;
//     }
//     .filter-icon {
//       max-width: 15%;
//     }
//     button {
//       width: 45%;
//     }
//   }
//   @media (max-width: 599px) {
//     row-gap: 15px;
//     button {
//       width: 100%;
//     }
//   }
// }
// .staff-filter {
//   .staff-filter-button {
//     display: flex;
//     column-gap: 10px;
//     margin-top: 24px;
//   }
// }
.invited-staff-section {
  background-color: var(--color-white);
  padding: var(--spacing-xxl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-xs);

  .User-list {
    display: grid;
    grid-template-columns: 32% 32% 32%;
    margin-top: 16px;
    row-gap: 20px;
    column-gap: 20px;
    justify-content: space-between;
    @media (max-width: 1400px) {
      display: grid;
      grid-template-columns: 49% 49%;
      row-gap: 20px;
      column-gap: 20px;
    }
    @media (max-width: 1040px) {
      display: grid;
      grid-template-columns: 100%;
      row-gap: 20px;
      column-gap: 20px;
    }
    @media (max-width: 899px) {
      display: grid;
      grid-template-columns: 48% 48%;
      row-gap: 20px;
      column-gap: 20px;
    }
    @media (max-width: 768px) {
      display: grid;
      grid-template-columns: 100%;
      row-gap: 20px;
      column-gap: 20px;
      .user-details-section {
        margin-top: 0 !important;
      }
    }
  }
  .user-details-section {
    // margin-top: 16px;
    border-radius: 8px;
    border-bottom-left-radius: 8px !important;
    border-bottom-right-radius: 8px !important;
    box-shadow: var(--box-shadow-xs);
    padding: 12px 8px 8px;
    position: relative;
    // cursor: pointer;
  }
  .users-section {
    display: flex;

    .profile-icon {
      width: 50px;
      height: 50px;
    }
    .profile-image {
      margin-right: 7px;
      .profile {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        object-fit: cover;
      }
    }
  }
  .user-right-section {
    display: flex;
    justify-content: space-between;
    width: 100%;
  }
  .user-details-right-section {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
  }
  //   .invited-status {
  //     position: absolute;
  //     right: 10px;
  //     top: 11px;
  //     span {
  //       //   border-radius: 0 !important;
  //       //   border-radius: 0 0px 5px 5px !important;
  //     }
  //   }
  .invited-status {
    min-width: 75px;
    .invited-draft {
      border: 0;
      padding: 1.5px 8px;
      border-radius: 3px;
      background-color: var(--color-light-success);
      color: var(--text-color-white);
    }
    .invited-ongoing {
      border: 0;
      padding: 1.5px 8px;
      border-radius: 3px;
      background-color: var(--color-primary);
      color: var(--text-color-white);
    }
    .invited-accepted {
      border: 0;
      padding: 1.5px 8px;
      border-radius: 3px;
      background-color: var(--color-green);
      color: var(--text-color-white);
    }
  }

  .check-icon-box {
    position: absolute;
    bottom: 7px;
    right: 7px;
    cursor: pointer;
  }
  .check-icon {
    fill: var(--color-green) !important;
  }
  .user-name {
    display: flex;
    width: 100%;
    justify-content: space-between;
    .username {
      width: calc(100% - 85px);
    }
    .text-ellipses {
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      overflow: hidden;
      word-break: break-all;
    }
  }
  .user-emails {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: flex-end;
    .email {
      width: calc(100% - 78px);
      // margin-top: 4px;
    }
    .text-ellipses {
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      overflow: hidden;
      word-break: break-all;
    }
    .user-date {
      width: 75px;
    }
  }
  .text-ellipses {
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-all;
  }
  // .secondary-button.resend-invite-button {
  //   padding: 8px 16px !important;
  //   color: $color-primary !important;
  //   svg {
  //     fill: none !important;
  //     margin-right: 5px;
  //     stroke: $color-primary;
  //   }
  // }

  // .resend-invite-button {
  //   padding: 8px 16px !important;
  //   svg {
  //     fill: none !important;
  //     margin-right: 5px;
  //     stroke: var(--color-white);
  //   }
  // }

  // .resend-invite-button:hover {
  //   svg {
  //     stroke: $color-primary;
  //   }
  // }
  // .resend-invite-button.Mui-disabled {
  //   opacity: 0.7;
  // }
  .resend-invite-button {
    .MuiButton-startIcon {
      svg {
        stroke: var(--color-white);
      }
    }
  }
  .select-all-text {
    cursor: pointer;
    color: var(--text-color-primary);
    font-weight: 600;
    padding-top: 12px;
    max-width: max-content;
  }
  .select-all-text-sec {
    width: 100px;
    max-width: 100%;
  }
  .select-all-text-remove {
    opacity: 0.7;
  }
  .disabled {
    cursor: default !important;
  }
  .link-text-name {
    cursor: pointer !important;
    color: var(--text-color-primary);
  }
}
