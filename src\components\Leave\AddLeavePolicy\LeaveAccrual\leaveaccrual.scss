.leave-accrual-container {
  .leave-accrual-fields {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    @media (max-width: 767px) {
      grid-template-columns: repeat(2, 1fr);
    }
    @media (max-width: 575px) {
      grid-template-columns: repeat(1, 1fr);
    }
  }
  .small-text-field {
    width: 55px;
  }
  .text-field-right-border-none {
    .MuiInputBase-root,
    .select__control {
      border-top-right-radius: 0px;
      border-bottom-right-radius: 0px;
    }
  }
  .text-field-left-border-none {
    .MuiInputBase-root,
    .select__control {
      border-top-left-radius: 0px;
      border-bottom-left-radius: 0px;
    }
  }
  .leave-select-wrap {
    min-width: 200px;
  }
  .delete-icon {
    fill: var(--icon-bold-red-color);
  }
  .on-the-text {
    margin-right: var(--spacing-xsm);
  }
}
