import React, { useRef } from 'react';
import { Box, Button, Typography } from '@mui/material';
import { useReactToPrint } from 'react-to-print';
import InsertDriveFileOutlinedIcon from '@mui/icons-material/InsertDriveFileOutlined';
import './pdfGenerate.scss';
import { DateFormat } from '@/helper/common/commonFunctions';

const NewStarterFormPDF = ({ userDetails, formDetails }) => {
  const componentRef = useRef();

  const handleDownlodPDF = useReactToPrint({
    content: () => componentRef.current,
    documentTitle: 'New Starter Form',
  });

  const namePrifixOptions = [
    { id: 1, name: 'Mr' },
    { id: 2, name: 'Mrs' },
    { id: 3, name: 'Miss' },
    { id: 4, name: 'Ms' },
    { id: 5, name: 'Other' },
  ];

  const getUserPrefix = (gender, maritalStatus) => {
    if (gender === 'male') {
      return 'Mr';
    } else if (gender === 'female') {
      if (maritalStatus === 'married') {
        return 'Mrs';
      } else if (maritalStatus === 'single') {
        return 'Miss';
      } else {
        return 'Ms';
      }
    }
    return '';
  };

  const newStarterPDFForm = () => {
    return (
      <Box ref={componentRef} className="pdf-generate-main-container pb12">
        <Typography variant="h6" className="pdf-watermark">
          CONFIDENTIAL
        </Typography>
        <Typography
          variant="h5"
          className="pdf-generate-title text-underline pb32"
        >
          New Starter Form
        </Typography>
        <Box className="ml32">
          <Typography variant="h6" className="pdf-generate-checkbox-sub-text">
            Please fill the form in{' '}
            <span className="text-underline fw600">BLOCK LETTER</span>
          </Typography>
          <Typography variant="h6" className="pdf-generate-checkbox-sub-text">
            If you have any question please ask one of our staff member for help
          </Typography>
          <Typography variant="h6" className="pdf-generate-checkbox-sub-text">
            Please tick the necessary box/es.
          </Typography>
        </Box>
        {/* Personal info */}
        <Box className="personal-information">
          <Typography variant="h6" className="p20 text-underline fw600 pt16">
            PERSONAL INFORMATION:
          </Typography>
          <Box className="pt16 pb16 d-flex">
            <Typography variant="h6" className="body-text">
              TITLE:
            </Typography>
            <Box className="d-flex pl40">
              {namePrifixOptions &&
                namePrifixOptions.length > 0 &&
                namePrifixOptions?.map((item, i) => {
                  return (
                    <div key={i} className="d-flex align-center">
                      <label htmlFor={item?.name} className="body-text fw500">
                        {item?.name}
                      </label>
                      <input
                        type="checkbox"
                        id={item?.name}
                        className="checkbox"
                        checked={
                          item?.name ===
                          getUserPrefix(
                            userDetails?.user_gender,
                            userDetails?.marital_status
                          )
                        }
                      />
                    </div>
                  );
                })}
              <div className="d-flex align-center">
                <label htmlFor="other_prifix" className="body-text pr8 fw500">
                  Please Specify
                </label>
                <input
                  type="text"
                  id="other_prifix"
                  className="input-field"
                  name="other_prifix"
                />
              </div>
            </Box>
          </Box>
          <Box className="name-wrapper">
            <Typography variant="h6" className="body-text pl4">
              FIRST NAME:
              <Typography variant="h6" className="title-text one-line-ellipsis">
                {userDetails?.user_first_name}
              </Typography>
            </Typography>
            <Typography variant="h6" className="body-text">
              MIDDLE NAME:
              <Typography variant="h6" className="title-text">
                {userDetails?.user_middle_name}
              </Typography>
            </Typography>
            <Typography variant="h6" className="body-text">
              SURNAME:
              <Typography variant="h6" className="title-text">
                {userDetails?.user_last_name}
              </Typography>
            </Typography>
          </Box>
          <Box className="personal-other-info pt16">
            <Typography variant="h6" className="pl4 body-text">
              MOBILE NO:
              <span className="title-text pl8">
                {userDetails?.user_phone_number}
              </span>
            </Typography>
            <Typography variant="h6" className="pl4 body-text pt8">
              HOME PHONE NUMBER:
              <span className="title-text pl8">
                {userDetails?.emergency_contact}
              </span>
            </Typography>
            <Typography variant="h6" className="pl4 body-text pt8">
              Email ID:
              <span className="title-text pl8">{userDetails?.user_email}</span>
            </Typography>
            <Typography variant="h6" className="pl4 body-text pt8">
              Nationality:
              <span className="title-text pl8">{userDetails?.country}</span>
            </Typography>
            <Box className="grid-2container pt8">
              <Typography variant="h6" className="pl4 body-text">
                PASSPORT NO /ID DOC NO.:
                <span className="title-text pl8">
                  {formDetails?.passport_no}
                </span>
              </Typography>
              <Typography variant="h6" className="pl4 body-text">
                WORK PERMIT TYPE:
                <span className="title-text pl8">
                  {formDetails?.permit_type === 'Other'
                    ? formDetails?.permit_type_other
                    : formDetails?.permit_type}
                </span>
              </Typography>
            </Box>
            <Box className="grid-2container pt8">
              <Typography variant="h6" className="pl4 body-text">
                {' '}
                VALIDITY:
                <span className="title-text pl8">
                  {DateFormat(formDetails?.validity, 'dates')}
                </span>
              </Typography>
              <Typography variant="h6" className="pl4 body-text">
                ISSUING DATE:
                <span className="title-text pl8">
                  {DateFormat(formDetails?.issued_date, 'dates')}
                </span>
              </Typography>
            </Box>
          </Box>
        </Box>
        {/* Bank Details */}
        <Box className="bank-information">
          <Typography variant="h6" className="p20 text-underline fw600 pt20">
            BANK DETAILS:
          </Typography>
          <Typography variant="h6" className="body-text">
            In accordance with the condition of employment, I give below details
            of my personal account so that my salary may be paid into the
            account by direct credit transfer.
          </Typography>

          <Box className="bank-details-table pt8">
            <table>
              <tbody>
                <tr>
                  <td colspan="2">
                    <Typography variant="h6" className="pl4 body-text">
                      ACCOUNT HOLDERS NAME:
                      <span className="title-text  pl8">
                        {formDetails?.bank_account_name}
                      </span>
                    </Typography>
                  </td>
                </tr>
                <tr>
                  <td>
                    <Typography variant="h6" className="pl4 body-text">
                      ACCOUNT NUMBER:
                      <span className="title-text pl8">
                        {formDetails?.bank_account_number}
                      </span>
                    </Typography>
                  </td>
                  <td>
                    <Typography variant="h6" className="pl4 body-text">
                      SORT CODE:
                      <span className="title-text pl8">
                        {formDetails?.bank_sort_code}
                      </span>
                    </Typography>
                  </td>
                </tr>
                <tr>
                  <td colspan="2" className="bank-address">
                    <Typography variant="h6" className="pl4 body-text">
                      BANK/BUILDING SOCIETY NAME AND ADDRESS:
                    </Typography>
                    <Typography
                      variant="h6"
                      className="title-text pl4 two-line-ellipsis"
                    >
                      {formDetails?.bank_society_name}
                      {formDetails?.bank_address
                        ? `, ${formDetails?.bank_address}`
                        : ''}
                    </Typography>
                  </td>
                </tr>
              </tbody>
            </table>
          </Box>
        </Box>
        {/* Next of kin details */}
        <Box className="next-kin-information">
          <Typography variant="h6" className="p20 text-underline fw600 pt20">
            NEXT OF KIN DETAILS:
          </Typography>

          <Box className="bank-details-table pt16">
            <table>
              <tbody>
                <tr>
                  <td>
                    <Typography variant="h6" className="pl4 body-text">
                      NAME:
                      <span className="title-text pl8">
                        {formDetails?.kin1_name}
                      </span>
                    </Typography>
                  </td>
                  <td>
                    <Typography variant="h6" className="pl4 body-text">
                      RELATIONSHIP:
                      <span className="title-text pl8">
                        {formDetails?.kin1_relation}
                      </span>
                    </Typography>
                  </td>
                </tr>
                <tr>
                  <td colspan="2">
                    <Typography
                      variant="h6"
                      className="pl4 pb4 body-text one-line-ellipsis"
                    >
                      ADDRESS:
                      <span className="title-text pl8">
                        {formDetails?.kin1_address}
                      </span>
                    </Typography>
                    <Typography variant="h6" className="pl4 body-text">
                      PHONE NO.:
                      <span className="title-text pl8">
                        {formDetails?.kin1_mobile_number}
                      </span>
                    </Typography>
                  </td>
                </tr>

                <tr>
                  <td>
                    <Typography variant="h6" className="pl4 body-text">
                      NAME:
                      <span className="title-text pl8">
                        {formDetails?.kin2_name}
                      </span>
                    </Typography>
                  </td>
                  <td>
                    <Typography variant="h6" className="pl4 body-text">
                      RELATIONSHIP:
                      <span className="title-text pl8">
                        {formDetails?.kin2_relation}
                      </span>
                    </Typography>
                  </td>
                </tr>
                <tr>
                  <td colspan="2">
                    <Typography
                      variant="h6"
                      className="pl4 pb4 body-text one-line-ellipsis"
                    >
                      ADDRESS:
                      <span className="title-text pl8">
                        {formDetails?.kin2_address}
                      </span>
                    </Typography>
                    <Typography variant="h6" className="pl4 body-text">
                      PHONE NO.:
                      <span className="title-text pl8">
                        {formDetails?.kin2_mobile_number}
                      </span>
                    </Typography>
                  </td>
                </tr>
              </tbody>
            </table>
          </Box>
        </Box>
        {/* Additional personal & health details */}
        <Box
          style={{ pageBreakBefore: 'always' }}
          className="additional-personal-information pt16"
        >
          <Typography variant="h6" className="p20 text-underline fw600 pt20">
            ADDITIONAL PERSONAL & HEALTH DETAILS:
          </Typography>

          <Box className="pt16 pb16 d-flex">
            <Typography variant="h6" className="body-text">
              GENDER:
            </Typography>
            <Box className="d-flex pl16">
              <div className="d-flex align-center">
                <label className="body-text fw500" htmlFor="male">
                  Male
                </label>
                <input
                  type="checkbox"
                  id="male"
                  name="male"
                  className="checkbox"
                  checked={userDetails?.user_gender === 'male'}
                />
                <label className="body-text fw500" htmlFor="female">
                  Female
                </label>
                <input
                  type="checkbox"
                  id="female"
                  name="female"
                  className="checkbox"
                  checked={userDetails?.user_gender === 'female'}
                />
                <label className="body-text fw500" htmlFor="other_gender">
                  Others (Please specify)
                </label>
                <input
                  type="text"
                  id="other_gender"
                  className="input-field"
                  name="other_gender"
                  style={{ width: '100px' }}
                  value={userDetails?.user_gender_other || ''}
                />
                <label className="body-text fw500 pl8" htmlFor="disclose">
                  I do not wish to disclose
                </label>
                <input
                  id="disclose"
                  type="checkbox"
                  name="disclose"
                  className="checkbox"
                />
              </div>
            </Box>
          </Box>
          <Box className="pb16 d-flex">
            <Box className="d-flex">
              <div className="d-flex align-center">
                <label className="body-text fw500" htmlFor="married">
                  Married
                </label>
                <input
                  type="checkbox"
                  id="married"
                  name="married"
                  className="checkbox"
                  checked={userDetails?.marital_status === 'married'}
                />
                <label className="body-text fw500" htmlFor="single">
                  Single
                </label>
                <input
                  type="checkbox"
                  id="single"
                  name="single"
                  className="checkbox"
                  checked={userDetails?.marital_status === 'single'}
                />
                <label
                  className="body-text fw500 pr4"
                  htmlFor="other_married_status"
                >
                  Others (Please specify)
                </label>
                <input
                  type="text"
                  id="other_married_status"
                  className="input-field"
                  name="other_married_status"
                  value={userDetails?.marital_status_other || ''}
                />
              </div>
            </Box>
          </Box>
          <Typography variant="h6" className="body-text">
            Do you consider yourself to have a disability or any long term
            health conditions?
          </Typography>
          <Box className="pt4 pb16 d-flex">
            <Box className="d-flex">
              <div className="d-flex align-center">
                <label className="body-text fw500" htmlFor="yes">
                  Yes
                </label>
                <input
                  type="checkbox"
                  id="yes"
                  name="yes"
                  className="checkbox"
                  checked={formDetails?.medical_disability === true}
                />
                <label className="body-text fw500 pl32" htmlFor="no">
                  No
                </label>
                <input
                  type="checkbox"
                  id="no"
                  name="no"
                  className="checkbox"
                  checked={formDetails?.medical_disability === false}
                />
              </div>
            </Box>
          </Box>
          <Typography variant="h6" className="body-text pb16">
            If yes, please provide the details: (and also if you undergoing any
            treatments or taking any medications and its effects){' '}
            <span className="text-underline">
              PLEASE NOTE: THIS INFORMATION IS ONLY FOR THE UNDERSTANDING OF THE
              CAPABILITY TO WORK AND IF INCASE YOU NEED FURTHER SUPPORT FROM US
            </span>
          </Typography>
          <Box className="additional-other-details">
            <Typography
              variant="h6"
              className="title-text custom-field-value three-line-ellipsis"
            >
              {formDetails?.medical_disability_detail}
            </Typography>
          </Box>
        </Box>
        {/* Professional Details */}
        <Box className="professional-information">
          <Typography variant="h6" className="p20 text-underline fw600 pt20">
            PROFESSIONAL DETAILS:
          </Typography>
          <Typography variant="h6" className="body-text pt8">
            PLEASE PROVIDE MOST RECENT TWO EMPLOYER DETAILS: (If you have not
            worked in recent past you may provide two references of personal or
            professional contacts)
          </Typography>

          <Box className="professional-details-table pt8">
            <table>
              <tbody>
                <tr className="table-row">
                  <td style={{ width: '35%' }}>
                    <Typography variant="h6" className="pl4 title-text fw600">
                      NAME AND CONTACT DETAILS:
                    </Typography>
                    <Typography
                      variant="h6"
                      className="title-text pl4 two-line-ellipsis"
                    >
                      {formDetails?.professional1_name_contact}
                    </Typography>
                  </td>
                  <td style={{ width: '35%' }}>
                    <Typography variant="h6" className="pl4 title-text fw600">
                      YOUR ROLE DESCRIPION AND DURATION YOU WORKED:
                    </Typography>
                    <Typography
                      variant="h6"
                      className="title-text pl4 two-line-ellipsis"
                    >
                      {formDetails?.professional1_role_description}
                    </Typography>
                  </td>
                  <td style={{ width: '15%' }}>
                    <Typography variant="h6" className="pl4 title-text fw600">
                      Start Date:
                    </Typography>
                    <Typography
                      variant="h6"
                      className="title-text pl4 two-line-ellipsis"
                    >
                      {formDetails?.professional1_start_date
                        ? DateFormat(
                            formDetails?.professional1_start_date,
                            'dates'
                          )
                        : '-'}
                    </Typography>
                  </td>
                  <td style={{ width: '15%' }}>
                    <Typography variant="h6" className="pl4 title-text fw600">
                      End Date:
                    </Typography>
                    <Typography
                      variant="h6"
                      className="title-text pl4 two-line-ellipsis"
                    >
                      {formDetails?.professional1_end_date
                        ? DateFormat(
                            formDetails?.professional1_end_date,
                            'dates'
                          )
                        : '-'}
                    </Typography>
                  </td>
                </tr>
                <tr className="table-row">
                  <td style={{ width: '35%' }}>
                    <Typography variant="h6" className="pl4 title-text fw600">
                      NAME AND CONTACT DETAILS:
                    </Typography>
                    <Typography
                      variant="h6"
                      className="title-text pl4 two-line-ellipsis"
                    >
                      {formDetails?.professional2_name_contact}
                    </Typography>
                  </td>
                  <td style={{ width: '35%' }}>
                    <Typography variant="h6" className="pl4 title-text fw600">
                      YOUR ROLE DESCRIPION AND DURATION YOU WORKED:
                    </Typography>
                    <Typography
                      variant="h6"
                      className="title-text pl4 two-line-ellipsis"
                    >
                      {formDetails?.professional2_role_description}
                    </Typography>
                  </td>
                  <td style={{ width: '15%' }}>
                    <Typography variant="h6" className="pl4 title-text fw600">
                      Start Date:
                    </Typography>
                    <Typography
                      variant="h6"
                      className="title-text pl4 two-line-ellipsis"
                    >
                      {formDetails?.professional2_start_date
                        ? DateFormat(
                            formDetails?.professional2_start_date,
                            'dates'
                          )
                        : '-'}
                    </Typography>
                  </td>
                  <td style={{ width: '15%' }}>
                    <Typography variant="h6" className="pl4 title-text fw600">
                      End Date:
                    </Typography>
                    <Typography
                      variant="h6"
                      className="title-text pl4 two-line-ellipsis"
                    >
                      {formDetails?.professional2_end_date
                        ? DateFormat(
                            formDetails?.professional2_end_date,
                            'dates'
                          )
                        : '-'}
                    </Typography>
                  </td>
                </tr>
              </tbody>
            </table>
          </Box>
        </Box>

        {/* Details */}
        <Box className="professional-information">
          <Typography variant="h6" className="body-text">
            I hereby declare that the information provided is true and correct
            to the best of my knowledge. I also understand that any wilful
            dishonesty may render for refusal of this application or immediate
            termination of employment. And in case of any changes I agree to
            update and correct the above information provided.
          </Typography>
          <Typography variant="h6" className="body-text pt8">
            I authorize to keep this information in my personal file otherwise
            it can be saved and used for future reference or recruitment.
          </Typography>
          <Typography variant="h6" className="body-text pt8">
            I understand all the information supplied in this application will
            be treated in a confidential manner and will have access to the
            parties namely the Human Resources staff, concerning department head
            or above for and assessment.
          </Typography>
        </Box>

        <Box className="signature-wrapper mt8">
          <Typography variant="h6" className="body-text one-line-ellipsis">
            <span className="text-underline">Full Name:</span>
            <span className="title-text pl8">
              {userDetails?.user_first_name +
                ' ' +
                userDetails?.user_middle_name +
                ' ' +
                userDetails?.user_last_name}
            </span>
          </Typography>
          <Box className="signature-content">
            <Typography
              variant="h6"
              className="body-text text-underline d-flex align-end"
            >
              Signature:
              {userDetails?.user_signature && (
                <span className="title-text pl8">
                  <img
                    src={userDetails?.user_signature}
                    alt="not found"
                    className="signature"
                    // style={{ height: '30px' }}
                  />
                </span>
              )}
            </Typography>
            <Typography variant="h6" className="body-text">
              <span className=" text-underline">Date:</span>
              <span className="pl8 title-text">
                {DateFormat(
                  userDetails?.updatedAt
                    ? userDetails?.updatedAt
                    : userDetails?.createdAt,
                  'dates'
                )}
              </span>
            </Typography>
          </Box>
        </Box>
      </Box>
    );
  };

  // function ViewPDFIcon(props) {
  //   return (
  //     <svg
  //       {...props}
  //       xmlns="http://www.w3.org/2000/svg"
  //       width="24"
  //       height="24"
  //       viewBox="0 0 24 24"
  //       fill="none"
  //       stroke="currentColor"
  //       strokeWidth="2"
  //       strokeLinecap="round"
  //       strokeLinejoin="round"
  //       class="view-pdf-icon"
  //       color="#d93434"
  //     >
  //       <path d="M12 2h6a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6l8-4z" />
  //       <path d="M14 2v6h6" />
  //       <text
  //         x="8"
  //         y="17"
  //         font-family="Arial, Helvetica, sans-serif"
  //         font-size="6"
  //         fill="currentColor"
  //       >
  //         PDF
  //       </text>
  //     </svg>
  //   );
  // }

  return (
    <div>
      <div style={{ display: 'none' }}>{newStarterPDFForm()}</div>
      <Button
        onClick={() => handleDownlodPDF()}
        className="pdf-download-btn"
        disableRipple
      >
        <span className="title-text onboarding-form-name">
          NEW STARTER FORM :{' '}
        </span>
        <InsertDriveFileOutlinedIcon />
        <span className="title-text pl4 text-underline">View PDF</span>
      </Button>
    </div>
  );
};

export default NewStarterFormPDF;
