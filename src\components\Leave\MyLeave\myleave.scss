.my-leave-card-wrap {
  .leave-balance-card {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
  }
  .my-leave-card {
    background-color: var(--color-white);
    padding: 20px 0px 0px;
    margin-top: 0px;
    border-radius: 12px;
    .slick-slide {
      > div {
        margin: 0px 10px 0px;
        @media (max-width: 575px) {
          margin: 0px;
        }
      }
    }
    .card-wrap {
      width: 100% !important;
      max-width: 342px;
      box-shadow: none;
      border: var(--normal-sec-border);
      .card-details-wrap {
        padding-bottom: 20px !important;
      }
      .card-content-wrap {
        padding: 15px;
        .leave-wrap {
          margin-bottom: 20px;
          .leave-icon-wrap {
            line-height: 0px;
            padding: 9px;
            border: var(--normal-sec-border);
            border-radius: 5px;
          }
          .leave-type-wrap {
            line-height: 0px;
            margin-left: 10px;
          }
        }
        .apply-leave {
          margin-top: 15px;
          color: var(--color-primary);
        }
        .balance-wrap {
          width: 50%;
          border-right: var(--normal-sec-border);
          .unlimited-text-wrap {
            color: var(--color-danger);
          }
        }
        .text-wrap {
          line-height: 18px !important;
        }
        .info-icon-wrap {
          height: 16px;
          width: 16px;
          &:hover {
            fill: var(--color-primary);
          }
        }
        .booked-wrap {
          width: 50%;
        }
      }
    }
    .leave-card {
      max-width: 280px !important;
    }
    .slick-slider {
      .slick-disabled {
        &::before {
          opacity: 0;
          display: none;
        }
      }
      .slick-prev,
      .slick-next {
        &:before {
          color: black;
          font-size: 25px;
        }
      }
    }
  }
  .slick-slider {
    .slick-arrow {
      z-index: 999;
      width: 72px;
      height: 72px;
      @media (max-width: 575px) {
        width: 50px;
      }
    }
    .slick-list {
      .slick-track {
        margin-left: 0px;
      }
    }
  }
  // .no-data-show-wrap {
  //   min-height: calc(100vh - 500px);
  // }
}

.leave-balance-popover {
  .settings-icon-wrap {
    padding: 7.5px 15px;
    .settings-icon {
      height: 18px;
      width: 18px;
      fill: var(--color-warning);
    }
    .text-wrap {
      font-weight: 500;
    }
    &:hover {
      color: var(--text-color-primary);
      .settings-icon {
        fill: var(--color-warning);
      }
    }
  }
}
