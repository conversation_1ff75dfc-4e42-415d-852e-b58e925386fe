'use client';

import React, { useContext, useEffect, useState, useRef } from 'react';
import { Box, Typography, CircularProgress, Tooltip } from '@mui/material';
import { useRouter, useSearchParams } from 'next/navigation';
import AuthContext from '@/helper/authcontext';
import Searchbar from '@/components/UI/CustomSearch';
import {
  GridIcon,
  MenuIcon,
  FolderDocHomeGridIcon,
} from '@/helper/common/images';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import CustomSelect from '@/components/UI/CustomSelect';
import DialogBox from '@/components/UI/Modalbox';
import FolderGridViews from '@/components/FolderViews/FolderGridViewsD';
import FolderListViews from '@/components/FolderViews/FolderListViewsD';
import FolderSideBar from '@/components/FolderViews/FolderSidebarD';
import FileDetails from '@/components/FolderViews/Filedetails';
import CustomButton from '@/components/UI/CustomButton';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { staticOptions } from '@/helper/common/staticOptions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import NoDataView from '@/components/UI/NoDataView';
import '@/app/(auth)/(users)/(EmpContract)/empcontracts/empcontracts.scss';
import '../documentcenter.scss';

export default function DocumentCenterOwn({ params }) {
  const router = useRouter();
  const Id = params?.id;
  const searchParams = useSearchParams();
  const isDetails = searchParams.get('details');
  const CatId = searchParams.get('subId');
  const { folderdata, setfolderdata, authState } = useContext(AuthContext);
  const [DocumentList, setDocumentList] = useState([]);
  const [DocumentListFile, setDocumentListFile] = useState([]);
  const [loader, setLoader] = useState(false);
  const [documentMemory, setDocumentMemory] = useState();
  const [isViewType, setViewType] = useState('grid');
  const [selectedSideBarData, setSelectedSideBarData] = useState('');
  const [searchValue, setSearchValue] = useState('');
  const [currentHSIndex, setCurrentHSIndex] = useState(0);
  const divRef = useRef(null);
  const [width, setWidth] = useState(0);
  const [breadcrumb, setBreadcrumb] = useState([]);
  const [openMenu, setOpenMenu] = useState({});
  const [branchList, setBranchList] = useState([]);
  const [departmentList, setDepartmentList] = useState([]);
  const [filter, setFilter] = useState(false);
  const [CategoryDetails, setCategoryDetails] = useState('');
  const [filterData, setFilterData] = useState({
    branch: '',
    categorytype: '',
    department: '',
    status: '',
  });
  const [filterDataApplied, setFilterDataApplied] = useState({
    branch: '',
    categorytype: '',
    department: '',
    status: '',
  });
  const [DocumentFolderList, setDocumentFolderList] = useState([]);
  const [filesId, setFilesId] = useState([]);

  const handleViewType = (type) => {
    setViewType(type);
  };
  // const handleCloseDialog = () => {
  //   setCreateModal(false);
  // };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      getDocumentList(searchValue, filterDataApplied);
    }
  };
  // List of branches
  const getBranchList = async () => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_BRANCH_LIST +
          `?search=${''}&page=${1}&size=${''}&branchStatus=active`
      );
      if (status === 200) {
        setLoader(false);
        let filterUserList = data?.data?.map((user) => ({
          label: user?.branch_name,
          value: user?.id,
          color: user?.branch_color,
        }));
        setBranchList(filterUserList);
      }
    } catch (error) {
      setLoader(false);
      setBranchList([]);

      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // List of department
  const getDepartmentList = async () => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_DEPARTMENT_LIST +
          `?search=${''}&page=${1}&size=${''}&departmentStatus=active`
      );

      if (status === 200) {
        setLoader(false);
        let filterUserList = data?.data?.map((user) => ({
          label: user?.department_name,
          value: user?.id,
        }));
        setDepartmentList(filterUserList);
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // List of Documents
  const getDocumentList = async (search) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_DOCUMENT_OWN_LIST +
          `?search=${search}&page=${1}&size=${''}&parent_id=${
            Id && Id !== 'all' ? Id : ''
          }` //&branches=${
        //   filter?.branch
        // }&departments=${filter?.department}&category_status=${
        //   filter?.status
        // }&category_use=${filter?.categorytype}
      );
      if (status === 200) {
        setLoader(false);
        setDocumentList(data?.data);
        // setDocumentMemory(data?.memory_calculation);
        const filesDoc =
          data?.data &&
          data?.data?.length > 0 &&
          data?.data?.filter((f) => f?.category_type !== 'folder');
        filesDoc && setDocumentListFile(filesDoc);
      }
    } catch (error) {
      setLoader(false);
      setDocumentList([]);

      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // Memory of Documents
  const getDocumentListHome = async () => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_DOCUMENT_OWN_LIST +
          `?search=${''}&page=${1}&size=${''}&parent_id=${''}`
      );
      if (status === 200) {
        setLoader(false);
        setDocumentMemory(data?.memory_calculation);
        setfolderdata({ ...folderdata, Ownmemory: data?.memory_calculation });
      }
    } catch (error) {
      setLoader(false);
      setDocumentMemory('');
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // List of Documents Folder
  const getDocumentFolderList = async () => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_DOCUMENT_FOLDER_OWN_LIST +
          `?search=${''}&page=${1}&size=${''}`
      );
      if (status === 200) {
        setLoader(false);
        setDocumentFolderList(data?.data);
        setfolderdata({ ...folderdata, ownFolder: data?.data });
      }
    } catch (error) {
      setLoader(false);
      setDocumentFolderList([]);
      setfolderdata({ ...folderdata, ownFolder: [] });
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // List of Category details
  const getDocumentCategory = async (ID) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_ONE_CATEGORY + `${ID}`
      );
      if (status === 200) {
        setLoader(false);
        const branchList = data?.data?.branches?.map((i) => ({
          value: i?.id,
          label: i?.branch_name,
        }));
        const depList = data?.data?.departments?.map((i) => ({
          value: i?.id,
          label: i?.department_name,
        }));
        setCategoryDetails({
          ...data?.data,
          branches: branchList,
          departments: depList,
        });
      }
    } catch (error) {
      setLoader(false);
      setCategoryDetails();
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // User track by category
  const userMediaTrack = async (id, isLast) => {
    const requestData = {
      category_id: id,
    };
    try {
      const { status } = await axiosInstance.post(
        URLS?.USER_MEDIA_TRACK,
        requestData
      );
      if (status === 200) {
        isLast && router.push(`/document-own/${Id}`);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  useEffect(() => {
    getDepartmentList();
    getBranchList();
    getDocumentList(searchValue, filterDataApplied);

    if (folderdata && folderdata?.Ownmemory) {
      setDocumentMemory(folderdata?.Ownmemory);
    } else {
      getDocumentListHome();
    }
    if (
      folderdata &&
      folderdata?.ownFolder &&
      folderdata?.ownFolder?.length > 0
    ) {
      setDocumentFolderList(folderdata?.ownFolder);
    } else {
      getDocumentFolderList();
    }
  }, []);
  useEffect(() => {
    if (divRef.current) {
      setWidth(divRef.current.getBoundingClientRect().width);
    }
  }, []);

  useEffect(() => {
    if (isDetails && CatId) {
      getDocumentCategory(CatId);
    }
  }, [CatId]);
  useEffect(() => {
    if (DocumentList && DocumentList?.length > 0 && CatId) {
      const files = DocumentList?.filter((f) => f?.category_type !== 'folder');
      const fid = files && files?.map((f) => f?.id);
      fid && fid?.length > 0 ? setFilesId(fid) : setFilesId([]);
      CatId && setCurrentHSIndex(fid?.indexOf(Number(CatId)));
    }
  }, [CatId, DocumentList]);

  const findBreadcrumb = (arr, selectedId, res) => {
    for (const el of arr) {
      if (el?.id === Number(selectedId)) {
        res.unshift(el);
        return true;
      } else {
        let is_parent =
          el?.children && findBreadcrumb(el?.children, selectedId, res);
        if (is_parent) {
          res.unshift(el);
          return true;
        }
      }
    }
  };

  function findMenuById(menu, selectedId, parents = []) {
    for (const item of menu) {
      if (item?.id === Number(selectedId)) {
        const result = {};
        [...parents, item.category_name].forEach(
          (category_name) => (result[category_name] = true)
        );
        return result;
      }
      if (item.children) {
        const result = findMenuById(item.children, selectedId, [
          ...parents,
          item?.category_name,
        ]);
        if (result) return result;
      }
    }
    return null;
  }

  useEffect(() => {
    let res = [];
    DocumentFolderList &&
      DocumentFolderList?.length > 0 &&
      Id &&
      findBreadcrumb(DocumentFolderList, Id, res);
    res && res?.length > 0 && setBreadcrumb(res);
    const result =
      DocumentFolderList &&
      DocumentFolderList?.length > 0 &&
      findMenuById(DocumentFolderList, Id);
    result && setOpenMenu(result);
  }, [Id, DocumentFolderList]);

  return (
    <Box className="document-section-wrapper">
      <FolderSideBar
        DocumentFolderList={DocumentFolderList}
        Id={Id}
        setSelectedSideBarData={setSelectedSideBarData}
        selectedSideBarData={selectedSideBarData}
        setOpenMenu={setOpenMenu}
        openMenu={openMenu}
        isOwn={true}
        documentMemory={documentMemory}
      />

      <Box className="folder-section-right document-center-section">
        <Box className="document-center">
          {loader ? (
            <Box className="content-loader">
              <CircularProgress className="loader" color="inherit" />
            </Box>
          ) : (
            <>
              <Box className="custom-breadcrumbs mb24">
                {Id && Id !== 'all' && (
                  <Box className="breadcrumbs-menu cursor-pointer">
                    <Box
                      onClick={() => router.push('/document-own/all')}
                      className="grid-icon home-grid-icon"
                    >
                      {FolderDocHomeGridIcon()}
                    </Box>
                  </Box>
                )}

                {Id &&
                  breadcrumb &&
                  breadcrumb?.length > 0 &&
                  breadcrumb?.map((item, i) => {
                    return (
                      <Box className="breadcrumbs-menu pointer" key={i}>
                        {i !== 0 && (
                          <ArrowForwardIosIcon className="separator-icon" />
                        )}

                        <Typography
                          className={
                            !isDetails && item?.id === Number(Id)
                              ? 'last-breadcrumb title-text'
                              : 'active-breadcrumb title-text'
                          }
                          id={`breadcrumb-${i}`}
                          onClick={() => {
                            if (item?.id !== Number(Id) || isDetails) {
                              router.push(`/document-own/${item?.id}`);
                            }
                          }}
                        >
                          {item?.category_name}
                        </Typography>
                      </Box>
                    );
                  })}
                {isDetails && CategoryDetails && (
                  <Box className="breadcrumbs-menu pointer">
                    <ArrowForwardIosIcon className="separator-icon" />
                    <Typography
                      className={'last-breadcrumb title-text'}
                      id={`breadcrumb`}
                      // onClick={() =>
                      //   item?.id !== Number(Id) &&
                      //   router.push(`/document-own/${item?.id}`)
                      // }
                    >
                      {CategoryDetails?.category_name}
                    </Typography>
                  </Box>
                )}
              </Box>
              {isDetails ? (
                <>
                  <FileDetails
                    CategoryDetails={CategoryDetails}
                    DocumentList={DocumentListFile}
                    getDocumentCategory={getDocumentCategory}
                    CatId={CatId}
                    setCurrentHSIndex={setCurrentHSIndex}
                    currentHSIndex={currentHSIndex}
                    userMediaTrack={userMediaTrack}
                    filesId={filesId}
                    Id={Id}
                  />
                </>
              ) : (
                <>
                  <Box className="document-filter-view mb16">
                    <Box className="document-filter">
                      <Searchbar
                        setSearchValue={setSearchValue}
                        searchValue={searchValue}
                        onKeyPress={handleKeyPress}
                      />
                      <CustomButton
                        variant="contained"
                        title="Search"
                        fullWidth={false}
                        onClick={() => {
                          getDocumentList(searchValue, filterDataApplied);
                        }}
                      />
                    </Box>

                    <Box className="d-flex justify-start align-center gap-sm grid-list-view-btn">
                      <CustomButton
                        variant={
                          isViewType === 'grid' ? 'contained' : 'outlined'
                        }
                        isIconOnly
                        startIcon={
                          <Tooltip
                            title={
                              <Typography className="sub-title-text">
                                Grid View
                              </Typography>
                            }
                            classes={{
                              tooltip: 'info-tooltip-container',
                            }}
                            arrow
                          >
                            <GridIcon />
                          </Tooltip>
                        }
                        onClick={() => {
                          handleViewType('grid');
                        }}
                      />

                      <CustomButton
                        variant={
                          isViewType === 'list' ? 'contained' : 'outlined'
                        }
                        isIconOnly
                        startIcon={
                          <Tooltip
                            title={
                              <Typography className="sub-title-text">
                                List View
                              </Typography>
                            }
                            classes={{
                              tooltip: 'info-tooltip-container',
                            }}
                            arrow
                          >
                            <MenuIcon />
                          </Tooltip>
                        }
                        onClick={() => {
                          handleViewType('list');
                        }}
                      />
                    </Box>
                  </Box>
                  {DocumentList && DocumentList?.length === 0 ? (
                    <Box className="">
                      <Box className="no-data d-flex align-center justify-center">
                        <NoDataView
                          title="No Document Data Found"
                          description="There is no document data available at the moment."
                        />
                      </Box>
                    </Box>
                  ) : isViewType === 'list' ? (
                    <FolderListViews
                      folderData={DocumentList}
                      isOwn={true}
                      Id={Id}
                      filesId={filesId}
                    />
                  ) : (
                    <Box>
                      <Box className={'emp-contract-folder-sec'}>
                        {DocumentList &&
                          DocumentList?.map((item) => (
                            <Box
                              key={item?.id}
                              className="emp-contract-item"
                              ref={divRef}
                              style={{ minWidth: `${width}px` }}
                            >
                              <FolderGridViews
                                folderData={item}
                                Id={Id}
                                isOwn={true}
                              />
                            </Box>
                          ))}
                      </Box>
                    </Box>
                  )}
                </>
              )}
            </>
          )}
        </Box>
      </Box>
      {/* <DialogBox
        open={createModal}
        handleClose={() => {
          handleCloseDialog();
        }}
        title={createModalTitle}
        content={
          <>
            <MoveItems
              sideBarMenu={sideBarMenu}
              isMove={createModalTitle === 'Move Items' ? true : false}
              handleCloseDialog={handleCloseDialog}
            />
          </>
        }
      /> */}
      <DialogBox
        open={filter}
        handleClose={() => {
          setFilter(!filter);
        }}
        title={'Document filter'}
        content={
          <>
            <Box className="staff-filter">
              <Box className="pt32">
                {(authState?.UserPermission?.branch === 2 ||
                  authState?.UserPermission?.branch === 1) && (
                  <Box>
                    <CustomSelect
                      placeholder="Branch name"
                      options={branchList}
                      value={
                        branchList?.find((opt) => {
                          return opt?.value === filterData?.branch;
                        }) || ''
                      }
                      onChange={(e) => {
                        setFilterData({
                          ...filterData,
                          branch: e?.value || '',
                        });
                      }}
                      label={<span>Branch name</span>}
                      menuPortalTarget={document.body}
                      styles={{
                        menuPortal: (base) => ({
                          ...base,
                          zIndex: 9999,
                        }),
                      }}
                    />
                  </Box>
                )}
                {(authState?.UserPermission?.department === 2 ||
                  authState?.UserPermission?.department === 1) && (
                  <Box className="pt8">
                    <CustomSelect
                      placeholder="Department name"
                      options={departmentList}
                      value={
                        departmentList?.find((opt) => {
                          return opt?.value === filterData?.department;
                        }) || ''
                      }
                      onChange={(e) => {
                        setFilterData({
                          ...filterData,
                          department: e?.value || '',
                        });
                      }}
                      label={<span>Department name</span>}
                      menuPortalTarget={document.body}
                      styles={{
                        menuPortal: (base) => ({
                          ...base,
                          zIndex: 9999,
                        }),
                      }}
                    />
                  </Box>
                )}
                <Box className="pt8">
                  <CustomSelect
                    placeholder="Category type"
                    options={staticOptions?.CONTENT_TYPE}
                    value={
                      staticOptions?.CONTENT_TYPE?.find((opt) => {
                        return opt?.value === filterData?.categorytype;
                      }) || ''
                    }
                    onChange={(e) => {
                      setFilterData({
                        ...filterData,
                        categorytype: e?.value || '',
                      });
                    }}
                    label={<span>Category type</span>}
                    menuPortalTarget={document.body}
                    styles={{
                      menuPortal: (base) => ({
                        ...base,
                        zIndex: 9999,
                      }),
                    }}
                  />
                </Box>

                <Box className="pt8">
                  <CustomSelect
                    placeholder="Status"
                    options={staticOptions?.STATUS}
                    value={
                      staticOptions?.STATUS?.find((opt) => {
                        return opt?.value === filterData?.status;
                      }) || ''
                    }
                    onChange={(e) => {
                      setFilterData({ ...filterData, status: e.target.value });
                    }}
                    label={<span>Status</span>}
                    menuPortalTarget={document.body}
                    styles={{
                      menuPortal: (base) => ({
                        ...base,
                        zIndex: 9999,
                      }),
                    }}
                  />
                </Box>
                <Box className="form-actions-btn">
                  <CustomButton
                    fullWidth
                    variant="outlined"
                    title="Clear"
                    onClick={() => {
                      setFilter(false);
                      setPage(1);
                      setFilterData({
                        branch: '',
                        categorytype: '',
                        department: '',
                        status: '',
                      });
                      setFilterDataApplied({
                        branch: '',
                        categorytype: '',
                        department: '',
                        status: '',
                      });
                      getUserList(searchValue, 1, '', '', '', '');
                    }}
                  />
                  <CustomButton
                    fullWidth
                    variant="contained"
                    title="Apply"
                    onClick={() => {
                      setFilter(false);
                      setPage(1);
                      setFilterDataApplied({
                        branch: filterData?.branch,
                        categorytype: filterData?.categorytype,
                        department: filterData?.department,
                        status: filterData?.status,
                      });
                      getUserList(
                        searchValue,
                        1,
                        filterData?.branch,
                        filterData?.categorytype,
                        filterData?.department,
                        filterData?.status
                      );
                    }}
                  />
                </Box>
              </Box>
            </Box>
          </>
        }
      />
    </Box>
  );
}
