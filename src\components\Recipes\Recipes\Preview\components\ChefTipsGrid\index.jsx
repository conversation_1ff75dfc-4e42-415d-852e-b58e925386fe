import React from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import NoDataView from '@/components/UI/NoDataView';
import './ChefTipsGrid.scss';

const ChefTipsGrid = ({ recipeData, highlightData = null }) => {
  // Check if tips are highlighted
  const isTipsHighlighted =
    highlightData && Object.keys(highlightData).length > 0;

  // Helper function to render tip text with highlighting
  const renderTipTextWithHighlight = (tipText, tipType) => {
    if (isTipsHighlighted && highlightData?.[tipType]) {
      const highlightedTip = highlightData[tipType];
      if (highlightedTip !== tipText) {
        return (
          <div className="highlight-container">
            <p className="chef-tips-grid__text highlight-no-margin-bottom">
              {tipText}
            </p>
            <p className="highlight-original-text">{highlightedTip}</p>
          </div>
        );
      }
    }

    // Normal display
    return <p className="chef-tips-grid__text">{tipText}</p>;
  };

  return (
    <div className="chef-tips-grid">
      {/* Head Chef Tips */}
      <div className="chef-tips-grid__card chef-tips-grid__card-wrap">
        <div className="chef-tips-grid__header">
          <p
            className={`chef-tips-grid__title chef-tips-grid__head-chef-title-text ${isTipsHighlighted ? 'highlight-no-margin-bottom' : ''}`}
          >
            <Icon name="ChefHat" size={18} color="currentColor" />
            <span>Head Chef Tips</span>
          </p>
        </div>
        <div className="chef-tips-grid__content">
          {recipeData?.recipe_head_chef_tips ? (
            renderTipTextWithHighlight(
              recipeData?.recipe_head_chef_tips,
              'recipe_head_chef_tips'
            )
          ) : (
            <NoDataView
              image={<Icon name="ChefHat" size={48} />}
              title="No Head Chef Tips"
              description="There are no Head Chef tips available for this recipe."
              className="no-data-auto-height-conainer"
            />
          )}
        </div>
      </div>

      {/* FOH Tips */}
      <div className="chef-tips-grid__card chef-tips-grid__foh-wrap">
        <div className="chef-tips-grid__header">
          <p
            className={`chef-tips-grid__title chef-tips-grid__foh-title-wrap ${isTipsHighlighted ? 'highlight-no-margin-bottom' : ''}`}
          >
            <Icon name="Users" size={18} color="currentColor" />
            <span>Front of House</span>
          </p>
        </div>
        <div className="chef-tips-grid__content">
          {recipeData?.recipe_foh_tips ? (
            renderTipTextWithHighlight(
              recipeData?.recipe_foh_tips,
              'recipe_foh_tips'
            )
          ) : (
            <NoDataView
              image={<Icon name="ChefHat" size={48} />}
              title="No Front of House Tips"
              description="There are no Front of House tips available for this recipe."
              className="no-data-auto-height-conainer"
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default ChefTipsGrid;
