'use client';

import React, { useEffect, useContext, useState } from 'react';
import { Box, Typography } from '@mui/material';
import CustomTextField from '@/components/UI/CustomTextField';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import CustomButton from '@/components/UI/CustomButton';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import AuthContext from '@/helper/authcontext';
import { useRouter } from 'next/navigation';
import { fetchFromStorage, saveToStorage } from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import ExpenseCategory from './ExpenseCatReq';

export default function RequestPayrollTab({ dsrId }) {
  const { authState, userdata, setUserdata } = useContext(AuthContext);

  const [remark, setRemark] = useState('');
  const [isSubmit, setIsSubmit] = useState(false);
  const router = useRouter();
  const [DsrDetails, setDsrDetails] = useState();
  const [expenseData, setExpenseData] = useState([]);

  const [loader, setLoader] = useState(false);
  const [approve, setApprove] = useState('');
  const [currency, setCurrency] = useState();
  const [IsRemarkView, setIsRemarkView] = useState(true);
  // Expense request by id
  const getExpenseRequest = async () => {
    setLoader(true);

    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_REQUEST_EXPENSE_ID + `${dsrId}`
      );
      if (status === 200 || status === 201) {
        setLoader(false);
        setDsrDetails(data?.data);
        const incomeData =
          data?.data &&
          data?.data?.expense_items &&
          data?.data?.expense_items?.length > 0 &&
          data?.data?.expense_items?.filter(
            (f) => f?.payment_type_usage === 'expense'
          );
        incomeData && setExpenseData(incomeData);
      }
    } catch (error) {
      setLoader(false);
      setDsrDetails();
      setExpenseData();
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const handleExpenseRequest = async (status) => {
    setLoader(true);
    let request = {
      expense_request_id: dsrId,
      request_status: status,
      request_remark: remark,
    };
    try {
      const { status, data } = await axiosInstance.post(
        URLS?.APPROVE_REJECT_REQUEST_EXPENSE,
        request
      );
      if (status === 200) {
        setLoader(false);
        if (data.status) {
          // getExpenseRequest();
          router?.push('/payroll-request');
          setLoader(false);
          setApiMessage('success', data?.message);
        } else {
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const oldDSR = fetchFromStorage(identifiers?.RedirectData)
    ? fetchFromStorage(identifiers?.RedirectData)
    : userdata;

  useEffect(() => {
    if (oldDSR?.IsFromUser === undefined && oldDSR?.page !== undefined) {
      setUserdata({ ...userdata, IsFromUser: true });
      saveToStorage(identifiers?.RedirectData, {
        ...oldDSR,
        IsFromUser: true,
      });
    }
  }, [oldDSR]);
  useEffect(() => {
    if (dsrId) {
      getExpenseRequest();
    }
  }, [dsrId]);
  useEffect(() => {
    authState?.currency_details && setCurrency(authState?.currency_details);
  }, [authState]);
  return (
    <Box className="Add-dsr-section">
      <ArrowBackIosIcon
        className="mb8 cursor-pointer"
        onClick={() => {
          setTimeout(() => {
            router?.push('/payroll-request');
          }, 1000);
        }}
      />
      <Box>
        {DsrDetails?.expense_month && DsrDetails?.expense_year && (
          <Box className="d-flex align-center">
            <Typography className="title-text fw400">
              <span className="fw600"> Month : </span>
              <span>
                {DsrDetails?.expense_month + '-' + DsrDetails?.expense_year}
              </span>
            </Typography>
          </Box>
        )}
        {DsrDetails?.expense_branch && (
          <Box className="d-flex align-center pt4">
            <Typography className="title-text fw400">
              <span className="fw600"> Branch : </span>
              <span>{DsrDetails?.expense_branch}</span>
            </Typography>
          </Box>
        )}
        {DsrDetails?.submitted_user && (
          <Box className="d-flex align-center pt4">
            <Typography className="title-text fw400">
              <span className="fw600"> Submitted By : </span>
              <span>{DsrDetails?.submitted_user}</span>
            </Typography>
          </Box>
        )}
        {DsrDetails?.expense_request_updated_by &&
          DsrDetails?.expense_request_updated_by?.user_full_name && (
            <Box className="d-flex align-center pt4">
              <Typography className="title-text fw400">
                <span className="fw600"> Action By : </span>
                <span>
                  {DsrDetails?.expense_request_updated_by?.user_full_name}
                </span>
              </Typography>
            </Box>
          )}
        {DsrDetails?.expense_request_status && (
          <Box className="d-flex align-center pt4">
            <Typography className="title-text fw400">
              <span className="fw600"> Status : </span>
              <span>
                {DsrDetails?.expense_request_status === 'rejected' ? (
                  <span className="sub-title-text failed fw600 text-capital">
                    {' '}
                    {DsrDetails?.expense_request_status}{' '}
                  </span>
                ) : DsrDetails?.expense_request_status === 'pending' ? (
                  <span className="sub-title-text draft fw600 text-capital">
                    {' '}
                    {DsrDetails?.expense_request_status}{' '}
                  </span>
                ) : (
                  <span className="sub-title-text success fw600 text-capital">
                    {' '}
                    {DsrDetails?.expense_request_status}{' '}
                  </span>
                )}
              </span>
            </Typography>
          </Box>
        )}
      </Box>
      <ExpenseCategory
        expenseData={expenseData}
        isEdit={true}
        setIsRemarkView={setIsRemarkView}
        IsRemarkView={IsRemarkView}
        setExpenseData={setExpenseData}
        currency={currency}
      />
      {/* {expenseData && expenseData?.length > 0 && (
        <>
          <FormGroup className="pt16">
            <FormControlLabel
              control={
                <Checkbox
                  className="check-box "
                  icon={<CheckBoxOutlineBlankIcon className="uncheck-icon" />}
                  checkedIcon={<CheckBoxIcon className="check-icon" />}
                  disableRipple
                />
              }
              name="weekly"
              className="weekly-check-box sub-title-text"
              checked={IsRemarkView}
              onChange={(e) => {
                setIsRemarkView(e.target.checked);
              }}
              label="View Remark"
            />
          </FormGroup>
          <Box className="dsr-add-view pt16">
            {expenseData &&
              expenseData?.length > 0 &&
              expenseData?.map((item, dindex) => {
                return (
                  <Box className="dsr-main-category income">
                    <Typography className="p16 fw600 text-capital main-category-text income-text">
                      {item?.payment_type_title}
                    </Typography>
                    <span className="category-status income">
                      <span className="sub-title-text category-draft fw600 text-capital">
                        {' '}
                        Expense
                      </span>
                    </span>
                    {item?.payment_type_category?.length > 0 &&
                      item?.payment_type_category?.map((citem, cindex) => {
                        return (
                          <Box className="">
                            {' '}
                            {citem?.payment_type_category_pattern ===
                              'multiple' && (
                              <Typography className="title-text fw600 text-capital pt16">
                                {citem?.payment_type_category_title}
                              </Typography>
                            )}
                            {citem?.payment_type_category_pattern ===
                            'multiple' ? (
                              <>
                                {citem?.categoryBranchValue &&
                                  citem?.categoryBranchValue?.length > 0 &&
                                  citem?.categoryBranchValue?.map(
                                    (csitem, subIndex) => {
                                      return (
                                        <>
                                          <Box className="card-details-section pt16">
                                            <Box>
                                              <Typography className="title-text">
                                                {csitem?.first_field_value}
                                              </Typography>
                                            </Box>
                                            <Box className="amount-field">
                                              <CustomTextField
                                                InputLabelProps={{
                                                  shrink: true
                                                }}
                                                value={csitem?.expense_amount}
                                                disabled={true}
                                                name={`amount ${subIndex}`}
                                                variant="filled"
                                                placeholder={
                                                  item?.has_field_currency
                                                    ? 'Amount'
                                                    : 'Enter value'
                                                }
                                                className={'amount-textfield'}
                                                InputProps={{
                                                  ...(item?.has_field_currency && {
                                                    startAdornment: (
                                                      <InputAdornment position="start">
                                                        <Typography className="title-text currency">
                                                          {currency?.symbol
                                                            ? currency?.symbol
                                                            : '£'}
                                                        </Typography>{' '}
                                                      </InputAdornment>
                                                    )
                                                  })
                                                }}
                                              />
                                            </Box>
                                          </Box>
                                        </>
                                      );
                                    }
                                  )}
                              </>
                            ) : (
                              <>
                                <Box className="card-details-section pt16">
                                  <Box>
                                    <Typography className="title-text fw600 text-capital">
                                      {citem?.payment_type_category_title}
                                    </Typography>
                                  </Box>
                                  <Box className="amount-field">
                                    <CustomTextField
                                      InputLabelProps={{
                                        shrink: true
                                      }}
                                      value={citem?.expense_amount}
                                      disabled={true}
                                      name={`amount ${cindex}`}
                                      variant="filled"
                                      placeholder={
                                        item?.has_field_currency
                                          ? 'Amount'
                                          : 'Enter value'
                                      }
                                      className={'amount-textfield'}
                                      InputProps={{
                                        ...(item?.has_field_currency && {
                                          startAdornment: (
                                            <InputAdornment position="start">
                                              <Typography className="title-text currency">
                                                {currency?.symbol
                                                  ? currency?.symbol
                                                  : '£'}
                                              </Typography>{' '}
                                            </InputAdornment>
                                          )
                                        })
                                      }}
                                    />
                                  </Box>
                                </Box>
                              </>
                            )}
                          </Box>
                        );
                      })}
                    <>
                      <Box className="card-details-section pt16 totol-dsr">
                        <Box>
                          <Typography className="title-text fw600">Total</Typography>
                        </Box>
                        <Box className="amount-field">
                          <CustomTextField
                            InputLabelProps={{
                              shrink: true
                            }}
                            value={TotalOfExpenseObj(expenseData, dindex)}
                            name={`amount `}
                            variant="filled"
                            disabled={true}
                            placeholder={
                              item?.has_field_currency
                                ? 'Amount'
                                : 'Enter value'
                            }
                            className={'amount-textfield'}
                            InputProps={{
                              ...(item?.has_field_currency && {
                                startAdornment: (
                                  <InputAdornment position="start">
                                    <Typography className="title-text currency">
                                      {currency?.symbol
                                        ? currency?.symbol
                                        : '£'}
                                    </Typography>{' '}
                                  </InputAdornment>
                                )
                              })
                            }}
                          />
                        </Box>
                      </Box>
                      {IsRemarkView && (
                        <Box className="card-details-section  card-details-remark-section pt16">
                          <Box>
                            <Typography className="title-text fw600">
                              Remark
                            </Typography>
                          </Box>
                          <Box className="amount-field">
                            <CustomTextField
                              InputLabelProps={{
                                shrink: true
                              }}
                              value={item?.payment_type_remark}
                              onChange={(e) => {
                                if (
                                  e.target.value === '' ||
                                  e.target.value?.length < 161
                                ) {
                                  const income = expenseData;
                                  income[dindex].payment_type_remark =
                                    e.target.value;
                                  setExpenseData(income);
                                  setRandom(Math.random());
                                }
                              }}
                              onPaste={(e) => {
                                if (
                                  e.target.value === '' ||
                                  e.target.value?.length < 161
                                ) {
                                  setIsSubmit(false);
                                  const income = DsrDataIncome;
                                  income[dindex].payment_type_remark =
                                    e.target.value;
                                  setDsrDataIncome(income);
                                  setRandom(Math.random());
                                }
                              }}
                              multiline
                              rows={2}
                              name={`amount ${dindex}`}
                              variant="filled"
                              disabled={true}
                              placeholder={'Enter value'}
                              className={
                                'amount-textfield additional-textfeild'
                              }
                            />
                          </Box>
                          <Typography className="sub-title-text text-align-end">
                            {(item?.payment_type_remark?.length
                              ? item?.payment_type_remark?.length
                              : 0) + ' / 160'}
                          </Typography>
                        </Box>
                      )}
                    </>
                  </Box>
                );
              })}
          </Box>
        </>
      )} */}

      {DsrDetails &&
        DsrDetails?.expense_request_status &&
        DsrDetails?.expense_request_status !== 'approved' &&
        DsrDetails?.expense_request_status !== 'rejected' && (
          <>
            <Box className="pt32">
              <CustomTextField
                id="remark"
                name="remark"
                multiline
                rows={2}
                onChange={(e) => {
                  setIsSubmit(false);
                  setApprove();
                  setRemark(e.target.value);
                }}
                fullWidth
                error={Boolean(!remark && isSubmit)}
                helperText={!remark && isSubmit}
                placeholder="Remark"
                value={remark}
                className="additional-textfeild"
                label="Remark"
                required
              />
              {!remark && isSubmit && (
                <Typography
                  variant="body2"
                  color="error"
                  className="other-field-error-text"
                >
                  This field is required
                </Typography>
              )}
            </Box>
            <Box className="create-cancel-button mt16">
              <CustomButton
                variant="contained"
                className="red-button"
                disabled={loader}
                title={`${
                  loader && approve === 'rejected' ? 'Rejecting...' : 'Reject'
                }`}
                onClick={() => {
                  setIsSubmit(true);
                  if (remark) {
                    setApprove('rejected');
                    handleExpenseRequest('rejected');
                  }
                }}
              />
              <CustomButton
                className="green-button"
                variant="contained"
                disabled={loader}
                title={`${
                  loader && approve === 'approved' ? 'Approving...' : 'Approve'
                }`}
                onClick={() => {
                  setIsSubmit(true);
                  if (remark) {
                    setApprove('approved');
                    handleExpenseRequest('approved');
                  }
                }}
              />
            </Box>
          </>
        )}
    </Box>
  );
}
