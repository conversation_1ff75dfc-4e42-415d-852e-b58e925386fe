'use client';
import React, { useState } from 'react';
import { Box, Divider, Typography } from '@mui/material';
import PricingCards from './PlanCards';
import AddCard from './AddCards';
import DiscountCode from './DiscountCode';
import Invoice from './Invoice';
import './paymentmethod.scss';
const subscriptions = [
  {
    planName: 'Free',
    employeeRange: { min: 0, max: 10 },
    amount: 0,
    duration: { type: 'monthly', days: 30 },
    discount: 0,
    offer: 'No charges for small teams',
    details: [
      'Min 0 to 10 employees',
      'Basic support',
      'Limited access to features',
    ],
  },
  {
    planName: 'Basic',
    employeeRange: { min: 11, max: 50 },
    amount: 100,
    duration: { type: 'monthly', days: 30 },
    discount: 10,
    offer: '10% discount on yearly subscription',
    details: [
      'Min 11 to 50 employees',
      'Access to all products or bundles',
      'Priority support',
    ],
  },
  {
    planName: 'Pro',
    employeeRange: { min: 51, max: 200 },
    amount: 500,
    duration: { type: 'yearly', days: 365 },
    discount: 20,
    offer: '20% discount for early bird signup',
    details: [
      'Min 51 to 200 employees',
      'Dedicated account manager',
      'Early access to new/beta features',
    ],
  },
];
export default function PaymentMethod() {
  const [selectedPlan, setSelectedPlan] = useState(null);

  return (
    <Box className="payment-method-wrap">
      <Box className="payment-header-wrap">
        <Typography variant="h4" className="payment-text-wrap">
          Subscription plan
        </Typography>
        <Typography component="p" className="payment-subtext-wrap">
          Unlock instant access to all existing products and daily new releases.
        </Typography>
      </Box>
      <Divider />
      <Box className="d-flex plans-wrap">
        <Box className="choose-plan-wrap">
          <PricingCards
            subscriptions={subscriptions}
            selectedPlan={selectedPlan}
            setSelectedPlan={setSelectedPlan}
          />
        </Box>
        <Box className="plan-payment-wrap">
          <AddCard />
          <DiscountCode planDiscount={selectedPlan} />
        </Box>
      </Box>
      <Invoice />
    </Box>
  );
}
