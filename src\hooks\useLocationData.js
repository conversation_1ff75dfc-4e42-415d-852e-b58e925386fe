import { useState, useEffect } from 'react';
// import axios from 'axios';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { URLS } from '@/helper/constants/urls';
import axiosInstance from '@/helper/axios/axiosInstance';
// import axiosInstance from '@/helper/axios/axiosInstanceOrg';

const useLocationData = () => {
  const [countries, setCountries] = useState([]);
  const [counties, setCounties] = useState([]);
  const [cities, setCities] = useState([]);
  const [selectedCountry, setSelectedCountry] = useState(null);
  const [selectedCounty, setSelectedCounty] = useState(null);

  // Fetch country list on mount
  useEffect(() => {
    const fetchCountries = async () => {
      try {
        const { status, data } = await axiosInstance.get(
          URLS?.GET_COUNTRY_LIST + `?parent_place=&geo_type=1`
        );

        if (status === 200 || status === 201) {
          if (data?.status) {
            const transformedOptions = data?.data?.map((country) => {
              return {
                label: country?.place_name,
                value: country?.place_code,
              };
            });

            setCountries(transformedOptions);
          } else {
            setApiMessage('error', data?.message);
          }
        }
      } catch (error) {
        setApiMessage('error', error?.response?.data?.message);
      }
    };

    fetchCountries();
  }, []);

  // Fetch county list when country changes
  useEffect(() => {
    if (!selectedCountry) return;
    const fetchCounties = async () => {
      // setLoader(true);
      try {
        const { status, data } = await axiosInstance.get(
          URLS?.GET_COUNTRY_LIST + `?parent_place=${selectedCountry}&geo_type=2`
        );
        if (status === 200) {
          // setLoader(false);
          let filterUserList = data?.data?.map((user) => ({
            label: user?.place_name,
            value: user?.place_code,
          }));
          const sortbyname = _.orderBy(filterUserList, 'label', ['asc']);
          setCounties(sortbyname);
          // setCities([]); // Reset cities when changing country
        }
      } catch (error) {
        setApiMessage('error', error?.response?.data?.message);
        // setLoader(false);
        setCounties([]);
      }
    };

    fetchCounties();
  }, [selectedCountry]);

  // Fetch city list when county changes
  useEffect(() => {
    if (!selectedCounty) return;

    const fetchCities = async () => {
      try {
        const { status, data } = await axiosInstance.get(
          URLS?.GET_COUNTRY_LIST + `?parent_place=${selectedCounty}&geo_type=3`
        );
        if (status === 200) {
          let filterUserList = data?.data?.map((user) => ({
            label: user?.place_name,
            value: user?.place_code,
          }));
          const sortbyname = _.orderBy(filterUserList, 'label', ['asc']);
          setCities(sortbyname);
        }
      } catch (error) {
        setApiMessage('error', error?.response?.data?.message);
        // setCities([]);
      }
    };

    fetchCities();
  }, [selectedCounty]);

  return {
    countries,
    counties,
    cities,
    setCities,
    setCounties,
    setSelectedCountry,
    setSelectedCounty,
  };
};

export default useLocationData;
