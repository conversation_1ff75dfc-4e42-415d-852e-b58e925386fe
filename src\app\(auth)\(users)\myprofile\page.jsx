import React from 'react';
import { Box } from '@mui/material';
import { generateMetadata } from '@/helper/common/commonFunctions';
import UserProfile from '@/components/Users/<USER>/index';

export const metadata = generateMetadata({
  pageTitle: 'My Profile',
});

const UserPage = ({ params }) => {
  return (
    <Box className="main-page-container">
      <UserProfile params={params} />
    </Box>
  );
};

export default UserPage;
