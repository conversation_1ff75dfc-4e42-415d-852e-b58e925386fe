import { Box, FormLabel, Typography } from '@mui/material';
import { CommonTextField } from './textField';
import './textfield.scss';

const CustomTextField = ({
  label,
  required,
  helperText,
  error,
  labelIcon,
  ...props
}) => {
  return (
    <Box className={`custom-textfield-wrapper ${error ? 'Mui-error' : ''}`}>
      {label && (
        <FormLabel
          htmlFor={props?.name}
          className={`field-label ${error ? 'error-label' : ''}`}
        >
          <span>
            {label}
            {required && <span className="required">*</span>}
          </span>
          {labelIcon && labelIcon}
        </FormLabel>
      )}
      <CommonTextField {...props} />
      {error && helperText && (
        <Typography className="field-error-text">{helperText}</Typography>
      )}
    </Box>
  );
};

export default CustomTextField;
