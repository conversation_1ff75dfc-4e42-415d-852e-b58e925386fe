'use client';

import React, { useEffect, useState } from 'react';
import { Box, Typography, Tooltip, InputAdornment } from '@mui/material';
import {
  Dragdropicon,
  Numver123Icon,
  StringABCIcon,
} from '@/helper/common/images';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import CancelIcon from '@mui/icons-material/Cancel';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomButton from '@/components/UI/CustomButton';
import DialogBox from '@/components/UI/Modalbox';
import DeleteModal from '@/components/UI/DeleteModal';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';

export default function UpdateCategory({
  AddDSR,
  AddSubCategory,
  ReorderBranchPayment,
  getCategoriesPaymentList,
}) {
  const [updateCatData, setUpdateCatData] = useState();
  const [categoryList, setCatList] = useState([]);
  const [fieldList, setFieldList] = useState([]);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteId, setDeleteId] = useState(null);

  const handleOpenDeleteDialog = (id) => {
    setDeleteId(id);
    setDeleteDialogOpen(true);
  };
  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setDeleteId(null);
  };

  const handleConfirmDelete = () => {
    deleteBranchCategory(deleteId);
    setDeleteDialogOpen(false);
    setDeleteId(null);
  };

  // Delete branchs Items
  const deleteBranchCategory = async (id) => {
    try {
      const { status, data } = await axiosInstance.delete(
        URLS?.DELETE_SUB_PAYMENT + id
      );
      if (status === 200) {
        if (data.status) {
          getCategoriesPaymentList(true);
          const categories = categoryList?.filter((f) => f?.id !== item?.id);
          setCatList(categories);
          setDeleteDialogOpen(false);
          setDeleteId(null);
          setApiMessage('success', data?.message);
        } else {
          setApiMessage('error', data?.message);
        }
      } else {
        setApiMessage('error', data?.message);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // Reorder #3 level categories
  const onDragEnd = (result) => {
    const { source, destination } = result;
    if (destination?.index) {
      const newData = categoryList;
      const [movedRow] = newData.splice(source?.index, 1);
      newData.splice(destination?.index, 0, movedRow);
      setCatList(newData);
      const CuurectCat = categoryList[destination?.index];
      ReorderBranchPayment(destination?.index, CuurectCat?.id, AddDSR?.id);
    }
  };

  // Reorder #3 level lists
  useEffect(() => {
    AddDSR &&
      AddDSR?.CategoryList &&
      AddDSR?.Reorder &&
      AddDSR?.CategoryList?.length > 0 &&
      setCatList(AddDSR?.CategoryList);
  }, [AddDSR?.CategoryList, AddDSR?.Reorder]);

  // Store add data
  useEffect(() => {
    if (
      AddDSR?.categoryDetails &&
      AddDSR?.categoryDetails?.payment_type_category_field &&
      AddDSR?.Add
    ) {
      AddDSR?.categoryDetails?.payment_type_category_field?.length > 0
        ? setFieldList(AddDSR?.categoryDetails?.payment_type_category_field)
        : setFieldList([]);
    }
    if (AddDSR?.categoryDetails && AddDSR?.Add) {
      setUpdateCatData(AddDSR?.categoryDetails);
    }
  }, [AddDSR?.categoryDetails?.id, AddDSR?.Add]);

  // Store update data
  useEffect(() => {
    if (AddDSR?.paymentDetails && AddDSR?.IsUpdate) {
      AddDSR?.paymentDetails?.length > 0
        ? setFieldList(AddDSR?.paymentDetails)
        : setFieldList([]);
    }
    if (AddDSR?.paymentDetails && AddDSR?.IsUpdate) {
      setUpdateCatData({
        payment_type_category_field: AddDSR?.paymentDetails,
      });
    }
  }, [AddDSR?.paymentDetails, AddDSR?.IsUpdate]);

  // Create validation schema dynamically based on fieldList
  const getValidationSchema = () => {
    const validationObject = {};
    fieldList?.forEach((field) => {
      validationObject[field.field_name] = Yup.string().required(
        'This field is required'
      );
    });
    return Yup.object().shape(validationObject);
  };

  // Create initial values dynamically based on fieldList
  const getInitialValues = () => {
    const initialValues = {};
    fieldList?.forEach((field) => {
      initialValues[field.field_name] = field.field_value || '';
    });
    return initialValues;
  };

  return (
    <Box className="category-level-1-section">
      {(AddDSR?.Add || AddDSR?.IsUpdate) && (
        <Box className="">
          <Typography className="body-text fw600 text-underline color-dark-blue">
            {AddDSR?.IsUpdate && AddDSR?.title
              ? `Update ${AddDSR?.title}`
              : `Add item into ${AddDSR?.payment_type_title}`}
          </Typography>
          <Formik
            initialValues={getInitialValues()}
            validationSchema={getValidationSchema()}
            enableReinitialize
            onSubmit={(values) => {
              const updatedFieldList = fieldList.map((field) => ({
                ...field,
                field_value: values[field.field_name],
              }));
              const updatedData = {
                ...updateCatData,
                payment_type_category_field: updatedFieldList,
              };
              AddSubCategory(updatedData, AddDSR?.IsUpdate ? true : false);
            }}
          >
            {({
              values,
              errors,
              touched,
              handleChange,
              handleBlur,
              dirty,
              isValid,
            }) => (
              <Form>
                {fieldList &&
                  fieldList?.length > 0 &&
                  fieldList?.map((item, index) => {
                    return (
                      <Box
                        key={index}
                        className={index === 0 ? 'pt16 pb16' : 'pb316'}
                      >
                        {item?.field_type === 'integer' ? (
                          <Box className="">
                            <CustomTextField
                              fullWidth
                              id={item.field_name}
                              name={item.field_name}
                              value={values[item.field_name] || ''}
                              label={item?.field_name}
                              placeholder={`Enter ${item?.field_name}`}
                              error={Boolean(
                                touched[item.field_name] &&
                                  errors[item.field_name]
                              )}
                              helperText={
                                touched[item.field_name] &&
                                errors[item.field_name]
                              }
                              onChange={(e) => {
                                if (
                                  e.target.value === '' ||
                                  !item?.field_limit ||
                                  e.target.value?.length < item?.field_limit + 1
                                ) {
                                  handleChange(e);
                                }
                              }}
                              onBlur={handleBlur}
                              required
                              onInput={(e) => {
                                e.target.value = e.target.value.replace(
                                  /[^0-9]/g,
                                  ''
                                );
                              }}
                              InputProps={{
                                startAdornment: (
                                  <InputAdornment position="start">
                                    <Tooltip
                                      arrow
                                      classes={{
                                        tooltip: 'info-tooltip-container',
                                      }}
                                      title={<Typography>Number</Typography>}
                                    >
                                      <Box className="d-flex">
                                        <Numver123Icon />
                                      </Box>
                                    </Tooltip>
                                  </InputAdornment>
                                ),
                              }}
                            />
                          </Box>
                        ) : (
                          <Box className="">
                            <CustomTextField
                              fullWidth
                              id={item.field_name}
                              name={item.field_name}
                              value={values[item.field_name] || ''}
                              label={item?.field_name}
                              placeholder={`Enter ${item?.field_name}`}
                              error={Boolean(
                                touched[item.field_name] &&
                                  errors[item.field_name]
                              )}
                              helperText={
                                touched[item.field_name] &&
                                errors[item.field_name]
                              }
                              onChange={(e) => {
                                if (
                                  e.target.value === '' ||
                                  !item?.field_limit ||
                                  e.target.value?.length < item?.field_limit + 1
                                ) {
                                  handleChange(e);
                                }
                              }}
                              onBlur={handleBlur}
                              required
                              InputProps={{
                                startAdornment: (
                                  <InputAdornment position="start">
                                    <Tooltip
                                      arrow
                                      classes={{
                                        tooltip: 'info-tooltip-container',
                                      }}
                                      title={<Typography>String</Typography>}
                                    >
                                      <Box className="d-flex">
                                        <StringABCIcon />
                                      </Box>
                                    </Tooltip>
                                  </InputAdornment>
                                ),
                              }}
                            />
                          </Box>
                        )}
                      </Box>
                    );
                  })}

                <CustomButton
                  fullWidth={false}
                  className="mt16"
                  title={AddDSR?.IsUpdate ? 'Update' : 'Add'}
                  type="submit"
                  disabled={!(dirty && isValid)}
                />
              </Form>
            )}
          </Formik>
        </Box>
      )}
      {AddDSR?.Reorder && (
        <Box>
          <Typography className="body-text fw600 text-underline color-dark-blue">
            {`Reorder ${
              AddDSR?.payment_type_title ? AddDSR?.payment_type_title : ''
            }'s item`}
          </Typography>
          <Box className="category-list-details">
            <DragDropContext onDragEnd={onDragEnd}>
              <Droppable droppableId="droppable">
                {(provided) => (
                  <Box {...provided.droppableProps} ref={provided.innerRef}>
                    {categoryList?.map((item, index) => (
                      <Draggable
                        key={item?.payment_type_category_branch_id}
                        draggableId={item?.payment_type_category_branch_id.toString()}
                        index={index}
                      >
                        {(provided) => (
                          <Box
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            hover
                            className="main-level-cards-screen"
                            tabIndex={-1}
                            key={item?.id}
                            {...provided.dragHandleProps}
                          >
                            <Box
                              className="card-details cursor-pointer"
                              onClick={() => handleEdit(item)}
                            >
                              <Box className="card-left">
                                <Box>
                                  <Box className="d-flex align-center">
                                    <Typography className="title-sm fw400 user-date">
                                      <span className="text-capital">
                                        {item?.first_field_value}
                                      </span>
                                    </Typography>
                                  </Box>
                                  <Box className="d-flex align-center">
                                    <Typography className="title-text fw400 user-date">
                                      <span className="text-capital"></span>
                                    </Typography>
                                  </Box>
                                </Box>
                              </Box>
                            </Box>
                            <Box>
                              <Box draggable={true} className="drag-icon">
                                {Dragdropicon()}
                              </Box>
                            </Box>
                            <CancelIcon
                              className="close-icon"
                              onClick={() => {
                                handleOpenDeleteDialog(item?.id);
                              }}
                            />
                          </Box>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </Box>
                )}
              </Droppable>
            </DragDropContext>
          </Box>
        </Box>
      )}
      <DialogBox
        open={deleteDialogOpen}
        handleClose={handleCloseDeleteDialog}
        className="delete-modal"
        dividerClass="delete-modal-divider"
        title="Confirmation"
        content={
          <DeleteModal
            handleCancel={handleCloseDeleteDialog}
            handleConfirm={handleConfirmDelete}
            text="Are you sure you want to delete?"
          />
        }
      />
    </Box>
  );
}
