import React, { useRef } from 'react';
import { Box, Button, Typography } from '@mui/material';
import { useReactToPrint } from 'react-to-print';
import HMRCLogo from '../../../public/images/logoNV.png';
import './pdfGenerate.scss';
import Image from 'next/image';
import moment from 'moment';
import InsertDriveFileOutlinedIcon from '@mui/icons-material/InsertDriveFileOutlined';

const EmployeeContractPDF = ({ userDetails }) => {
  const componentRef = useRef();

  const handleDownlodPDF = useReactToPrint({
    content: () => componentRef.current,
    documentTitle: 'Emaployee Contract',
  });

  const NestedOrderedList = ({ data }) => {
    return (
      <ul className="nestedList">
        {data?.map((item, index) => (
          <li key={index}>
            <Typography className="p12 fw600">
              {item?.id}
              <span> {item?.text}</span>
            </Typography>
            {item?.children && <NestedOrderedList data={item.children} />}
          </li>
        ))}
      </ul>
    );
  };

  const contentListData1 = [
    {
      id: '1.0',
      text: 'Staff Guide',
      children: [
        { id: '1.1', text: 'Introduction' },
        { id: '1.2', text: 'Mobile phones' },
        {
          id: '1.3',
          text: 'Keys',
          children: [
            { id: '1.3.1', text: 'On-site keys' },
            { id: '1.3.2', text: 'Staff issued keys' },
          ],
        },
        { id: '1.4', text: 'Card transactions' },
        { id: '1.5', text: 'Teamwork & Flexibility' },
        { id: '1.6', text: 'Preparation and cleaning' },
        { id: '1.7', text: 'Personal details' },
        { id: '1.8', text: 'Training' },
        { id: '1.9', text: 'Observation days' },
        { id: '1.10', text: 'Communication' },
        { id: '1.11', text: 'Equal opportunities' },
      ],
    },
    { id: '2.0', text: 'Uniform' },
    {
      id: '3.0',
      text: 'Breaks',
      children: [{ id: '3.1', text: 'Presence at work' }],
    },
    { id: '4.0', text: 'Resignation/Termination' },
    {
      id: '5.0',
      text: 'Pay',
      children: [
        { id: '5.1', text: 'Clock in/ou' },
        { id: '5.2', text: 'Pay periods' },
        { id: '5.3', text: 'National insurance & tax' },
        { id: '5.4', text: 'Wage deductions' },
        { id: '5.5', text: 'Final pay' },
        { id: '5.6', text: 'Pensions' },
        { id: '5.7', text: 'Tips' },
      ],
    },
    {
      id: '6.0',
      text: 'Time off',
      children: [
        { id: '6.1', text: 'Holiday' },
        { id: '6.2', text: 'Day(s) off' },
        { id: '6.3', text: 'Bank holidays' },
        { id: '6.4', text: 'Pay' },
        { id: '6.5', text: 'SMP, SPP & SSP' },
      ],
    },
    {
      id: '7.0',
      text: 'Staff discount/Purchase',
    },
  ];
  const contentListData2 = [
    {
      id: '8.0',
      text: 'Safe practice',
      children: [
        {
          id: '8.1',
          text: 'Posters',
        },
        {
          id: '8.2',
          text: 'Personal hygiene',
        },
        {
          id: '8.3',
          text: ' Gross misconduct through health & safety',
        },
        {
          id: '8.4',
          text: 'Reporting Sickness',
        },
        {
          id: '8.5',
          text: 'Accidents',
        },
        {
          id: '8.6',
          text: ' Health & safety',
        },
        {
          id: '8.7',
          text: 'Drinking at work',
        },
        {
          id: '8.8',
          text: 'Gross misconduct, restricted sales',
        },
        {
          id: '8.9',
          text: 'Medical examinations',
        },
      ],
    },
    {
      id: '9.0',
      text: 'Management sheets',
      children: [
        {
          id: '9.1',
          text: 'Daily sheets',
        },
        {
          id: '9.2',
          text: 'Responsibility',
        },
      ],
    },
    {
      id: '10.0',
      text: 'Accessibility',
      children: [
        {
          id: '10.1',
          text: 'Documents',
        },
      ],
    },
    {
      id: '11.0',
      text: 'Grievances',
      children: [
        {
          id: '11.1',
          text: 'Purpose',
        },
        {
          id: '11.2',
          text: 'Fairness & transparency',
        },
        {
          id: '11.3',
          text: 'Formal action',
        },
        {
          id: '11.4',
          text: 'Procedures',
        },
        {
          id: '11.5',
          text: 'CODE OF PRACTICE 1',
        },
      ],
    },
    {
      id: '12.0',
      text: 'Terms',
      children: [
        {
          id: '12.1',
          text: 'Termination of employment contract',
        },
        {
          id: '12.2',
          text: 'Severability',
        },
        {
          id: '12.3',
          text: 'Other important notes',
        },
      ],
    },
  ];

  const NestedOrderedListData = ({ data }) => {
    return (
      <ul className="nestedList">
        {data?.map((item, index) => (
          <li key={index}>
            <Typography className="p12 fw600">
              {item?.id}
              <span> {item?.text}</span>
            </Typography>
            {item?.otherText && (
              <Typography
                dangerouslySetInnerHTML={{ __html: item?.otherText }}
              />
            )}
            {item?.desc && (
              <Typography
                className="p12 pt8"
                dangerouslySetInnerHTML={{ __html: item?.desc }}
              />
            )}
            {item?.children && <NestedOrderedListData data={item.children} />}
          </li>
        ))}
      </ul>
    );
  };
  const staffGuidePage3 = [
    {
      id: '1.0',
      text: 'Staff Guide',
      children: [
        {
          id: '1.1',
          text: 'Introduction',
          desc: 'This guide includes the terms of your employment contract. Other important terms from www.direct.gov will supplement this document.  Every effort has been taken to make this document as concise as possible for ease of understanding.  ',
        },
        {
          id: '1.2',
          text: 'Mobile phones & possessions',
          desc: 'Please be aware that any use of mobile phones is prohibited whilst working.  we ask that they are left with your bags and coats in the lockers in the staff room or designated location at the premises. No responsibility will be taken by the companies for any losses of personal possessions. Please be careful when putting away your own items and be mindful of other members of staff/s possessions. Only management team is allowed to use their personal phone/s for business purpose only, with approval from the business director. And for data protection, you are not allowed to take any photographs of the event or the guests from your personal mobile phone, camera or any such equipment.',
        },
        {
          id: '1.3',
          text: 'Keys',
          children: [
            {
              id: '1.3.1',
              text: 'On-site keys',
              desc: 'All on-site keys (which are for common use) must be placed at their designated location after each use. There is a very big security risk when keys are lost.  Replacements are costly and (approximately £100 per lock, £8 per key) will become chargeable against employee salary when an individual or individuals are identified as the culprits. If a whole set of keys and lock is required for change for security because of loss of keys, the whole amount will be charged against the employees.',
            },
            {
              id: '1.3.2',
              text: 'Staff issued keys',
              desc: 'We ask that where staff keys have been issued to you, please be aware that we have placed a great deal of trust in you.  We do advise that if you do leave any items at the premises and need to get access afterhours please advise us (text or phone) before hand.  Again if it is deemed that it is necessary that a full set of keys be replaced and reissued because of loss or non-return after resignation or dismissal a charge will be made against that person’s remuneration to recoup any loss incurred.  The Keys must be return to the management after your last shift. A complete lock change of the premises would be needed if when you leave keys are not returned or left with another employee at the time of final working hours.  Changing of the locks is a considerable sum of money and will include the locksmiths call out, replacement of the lock barrel and replacement keys for all current keys issued.',
            },
          ],
        },
        {
          id: '1.4',
          text: 'Card transactions',
          desc: 'Any details of customers’ card transactions must be treated with utmost care and privacy. Failure to do so will result in immediate termination of the contract. ',
        },
        {
          id: '1.5',
          text: 'Teamwork & Flexibility',
          desc: "On some occasions you will be working in small space with other colleagues. It is very important that staffs are not territorial over space. <p class='pt16' />There is a need for the employee to be flexible and these hours may be changed as required according to the employer’s needs. Under these circumstances, where hours need to be changed or additional hours worked, the employer will let you know.<p class='pt16' />The main place to work is mentioned above but it may change as per the need of the business and we will let you know with as much notice as possible and in some cases it could be instant decision as well.",
        },
        {
          id: '1.6',
          text: 'Preparation and cleaning',
          desc: 'It is important that preparation and cleaning for service is performed even where the restaurant is not busy. Staff should plan micro-cleaning, deep cleaning; stock arrangements, premises check and other similar duties and must perform during less busy time.',
        },
      ],
    },
  ];
  const staffGuidePage4 = [
    {
      id: '',
      text: '',
      children: [
        {
          id: '1.7',
          text: 'Personal details',
          desc: 'You are required to keep all of your personal details up to date with your employer using change of details forms. Change of details needs to be updated to us immediately but no later than 7 days. It is the responsibility of the employee and failure to do so may incur cost to rectify the issue. Please be especially careful when providing bank details and national insurance details. And also most importantly, your passport / work-permit dues/re-news, failing to keep the track of this will cause termination of the employment immediately.',
        },
        {
          id: '1.8',
          text: 'Training',
          desc: 'Staff training sheets (STS) for trainings are signed by trainee and mentor. It shows that both parties are happy to carry our detailed job with required standard.  It is your responsibility to maintain these standards and if upon a number of warnings whether verbal or written it is felt that you are not meeting expected standards your pay may be dropped back to that of the training wage or initial starting pay until it is felt that an improvement and understanding of the jobs have been met. If for any reason you are unable to upkeep these standards please speak to us before it becomes necessary to retrain. After the completion of the training, STS must be completed within 2 weeks',
        },
        {
          id: '1.9',
          text: 'Observation days',
          desc: 'Observation days are crucial because they provide both employee and employer with an opportunity to see how things work.  Observation days are your starting point and they are not paid. During observation days, a member of staff will be showing you the various duties which are carried out by the employee in that job role. You are expected to come in professional appearance and on time for that job role during observation days.',
        },
        {
          id: '1.10',
          text: 'Communication',
          desc: 'English must be used as common language for communication within the team especially when there is a person who does not understand a language other than English. All employees are requested to keep distance from swearing and abusive language. Swearing and abusive language will not be tolerated in any form.',
        },
        {
          id: '1.11',
          text: 'Equal opportunities',
          desc: 'It is company’s policy to provide employment, compensation, training , promotions and other conditions of employment without regard to race, colour, ethnic origin, nationality , national origin, religion or belief, sex , sexual orientation, marital status and/or disability unrelated to an individual’s ability to perform essential job functions. It is also the company’s policy to conform to all employment standards required by law.',
        },
      ],
    },
    {
      id: '2.0',
      text: 'Uniform',
      desc: 'All staff members are required to wear appropriate uniforms. Individuals are required to pay for their uniform.  Make sure you always comply with food safety and sanitation regulation and maintain your personal hygiene while at work. Please note that you may not be allowed to work if these requirements are not fulfilled.',
    },
    {
      id: '3.0',
      text: 'Breaks',
      desc: 'All staff is entitled to a break when working over six consecutive hours. Breaks are unpaid. Every time you go for break please make sure to clock-out/in on the clocking in machines.  We ask that staff sort out amongst themselves the best time to take these breaks bearing in mind that, who is already on their break and how many customers there are in the restaurant.  Also if in a break where the restaurant becomes busy you may be asked to stop your break and commence it again after.',
      children: [
        {
          id: '3.1',
          text: 'Presence at work',
          desc: 'We ask that employees remain on site for the full period of their shift.  If required to leave site this must be cleared with a supervisory party with a set period for return.',
        },
      ],
    },
  ];
  const staffGuidePage5 = [
    {
      id: '4.0',
      text: 'Resignation/Termination',
      desc: "Your employment is not for a fixed term and there is no anticipated duration for your employment but it may be terminated by notice. The company has 3 months probationary period. During the probationary period, your employment may be ended by you giving the company four week’s written notice or by the company giving you one week’s written notice.<p class='pt16' />After the successful completion of any probationary period, your employment may be ended by you giving the company one month’s written notice. The company will also give you one month’s written notice and after 4 years service a further one weeks’ notice or each additional complete year of service up to maximum of 12 weeks’ notice.<p class='pt16'/>If you do not provide us with the required amount of notice, the company may deduct the number of days pay equivalent to the number of days not worked in the required notice period.<p class='pt16'/>The company will not be obliged to provide you with work at any time after notice of termination shall have been given by either party and the company may, in its absolute discretion, pay your salary entitlement in lieu of all or any part of the unexpired period of notice (subject to deduction at source of income tax and applicable national insurance contributions).",
    },
    {
      id: '5.0',
      text: 'Pay',
      children: [
        {
          id: '5.1',
          text: 'Clock in/out',
          desc: "You must clock in when you start the shift and clock out upon finishing. This applies after you arrive and have put away any belongings, when you go on a break, when you come back off of a break and when you leave.  If you think there has been a mistake or over payment please contact HR team.  We reserve the right where there is an overpayment to adjust the next payment to be sent out.Clock-in/Out in the system on behalf of other employee is a serious offence. You may be immediately terminated from the job as soon it comes to our notice.<p class='pt16' />You must complete the job within the allocated time periods. However, if you are required to stay for longer due to exceptional busy time period or to complete a specific job, over time must be authorized beforehand. Also, an overtime approval form must be submitted with your line manager. Over times are paid at normal rates.<p class='pt16' />If you are going to be more than 10 minutes late you are required to contact the employer as soon as possible but at the very latest 30 minutes before you are due to start work.",
        },
        {
          id: '5.2',
          text: 'Pay periods',
          desc: 'Wages are paid monthly and on 10th day of every month. This means that for work performed in a month will be processed in the following month and paid out via bank transfer.  Should a person be dismissed or resign these pay periods will still apply.  Your wages will be deducted as per section 5.4 if necessary.',
        },
        {
          id: '5.3',
          text: 'National insurance & tax',
          desc: 'If you become liable to tax and national insurance this will be deducted from your pay and the amounts shown on your wage break down. Your payslip will be set to you via email.',
        },
        {
          id: '5.4',
          text: 'Wage deductions',
          desc: "We reserve the right to deduct amount from staff wages for any of below reasons and where the company has to suffer financial loss due to negligence of staff members: Damage to stock, stock out of date or going out of date, theft of stock where a staff member is negligent as to their duties, shortages of money in the till, damage to the business properties etc<p class='pt16' />We also reserve this right where Holiday has been overpaid and that staff member maybe leaving; their contract has been terminated or will be taking a period of leave.",
        },
      ],
    },
  ];
  const staffGuidePage6 = [
    {
      id: '',
      text: '',
      children: [
        {
          id: '',
          text: '',
          desc: "We reserve the right to make deductions on behalf of third parties such as government institutions or alike where your employer has been requested to do so or withhold pay where any of the above situations are believed to have occurred pending confirmation.<p class='pt16' />Any overpayment of pay or holiday pay will also be recouped.",
        },
        {
          id: '5.5',
          text: 'Final pay',
          desc: 'Final pay, following resignation or dismissal will be paid by cheque in order to evidence collection of any final documentation/properties. This may be collected from the office with prior arrangement.',
        },
        {
          id: '5.6',
          text: 'Pensions',
          desc: 'The Company does operate a pension scheme. Once you reach qualifying status you will be automatically enrolled into the Government pension (NEST). This is optional for the employee but must be terminated with the pension provider directly by the employee. Deductions for this pension will be taken from your wage payments. ',
        },
        {
          id: '5.7',
          text: 'Tips',
          otherText:
            "<span style='color:red;font-size:28px'>3 months no tips</span>",
          desc: 'We are a small business and calculation of the same on daily basis is complicated process. Hence, Tips are paid (with mutual agreement) to members of staff in the form of higher wages than national minimum wages, periodic rewards, staff benefits and team out events (hosted every month). ',
        },
      ],
    },
    {
      id: '6.0',
      text: 'Time off',
      children: [
        {
          id: '6.1',
          text: 'Holiday',
          desc: 'The company’s holiday year runs  April to March.  Holidays must be booked by staff using our holiday form. The limitations to this no more than one week are booked off without prior consent.  The cut off point for making bookings on the calendar is two weeks prior to the first booked day as rotas are written a week in advance. If you insist on taking time off for holiday which leaves the businesses short staffed you may find your hours greatly reduced if another person is brought in to replace the hours needed.  Please be aware that our busiest months are July, August and December this is booked out as unavailable. Less busy months are January and February and can be considered for holiday. Holiday will not be carried over to the next holiday year.',
        },
        {
          id: '6.2',
          text: 'Day(s) off',
          desc: 'We understand that it may become necessary to have days off during the working week.  Bookings for these days can be made with an advance notice but again are subject to the terms of 6.1. We are sometimes able to swap staff over during certain days where they are of the same skill level.  If you do make these arrangements between each other please bear in mind the person who takes on the shift will need to be of the same skill set and management must be made aware of the arrangements.  Please also be aware that should the person swapping into those hours be unable to fulfil them due to illness or inability the original person who was issued the hours will be required to fill in.  It is the responsibility of the person who is swapping there hours to make sure that the person covering understands the hours; location and they must perform effectively during those hours.',
        },
        {
          id: '6.3',
          text: 'Bank holidays',
          desc: 'The Restaurant may be open for bank holidays and forms part of our normal working week.  It is possible to book these off in advance but please be advised that they do fill up quickly. These will form part of holiday periods.',
        },
        {
          id: '6.4',
          text: 'Holiday Pay',
        },
      ],
    },
  ];
  const staffGuidePage7 = [
    {
      id: '',
      text: '',
      children: [
        {
          id: '',
          text: '',
          desc: 'Each employee is entitled to get 20 days holidays pro-rata basis excluding bank holidays, per calendar year (1st April-31st March). Holiday pay is not provided for exempt age groups under government legislation.  All employees over school leaving age are entitled to holiday pay which is calculated from current government requirements.  If an overpayment of holiday pay has been made, the employee will be required to pay it back.',
        },
        {
          id: '6.5',
          text: 'SMP, SPP & SSP',
          desc: 'You are entitled to statutory sick pay (“SSP”), (“SMP”) and (“SPP”) upon satisfy all of the governments qualifying conditions.',
        },
      ],
    },
    {
      id: '7.0',
      text: 'Staff discount/Purchase',
      desc: "For all staff we offer 50% discount on all products we make in house from our group companies. There may be items which are not eligible for staff discount. While dining- in, A staff member accompanied with family / friends up to 4people can only get 50% discount as per above. Other people with staff member/s will get discounts up to 30% for dine in and take away (excluding alcohol).<p class='pt16' />All purchases by staff must be put through the till by an alternate member of staff.",
    },
    {
      id: '8.0',
      text: 'Safe practice (also see HACCP document)',
      children: [
        {
          id: '8.1',
          text: 'Posters',
          desc: 'Some of the safe practice methods are too complex to remember and require reminders. You can find posters in the premises reminding you of practices as well as in Staff training File (STF) & Management sheet for explaining some terms/procedure.',
        },
        {
          id: '8.2',
          text: 'Personal hygiene',
          desc: 'Please be aware that you are expected to turn up to work in a presentable manner (Uniform 2.0).  Please also use the hand washing stations prior to working and hand wash facilities within the toilet.',
        },
        {
          id: '8.3',
          text: ' Gross misconduct through health & safety',
          desc: 'Please be aware that serious breaches of health and safety can be considered as gross misconduct and can result in immediate termination. This is because we are legally obliged to dismiss anyone who puts other staff members in danger of their safety.',
        },
        {
          id: '8.4',
          text: 'Reporting Sickness',
          desc: "On the first day of any sickness absence you must ensure that we are informed by telephone of your sickness at the earliest possible opportunity and if practicable one hour prior to starting in case of emergency. You should also give the details of the nature of your illness and the day on which you are expecting to return to work. You must inform the company as soon as possible of any change in the date of your anticipated return to work.<p class='pt16' />Sickness absence of up to and including seven consecutive day must be fully supported by a self certificate and thereafter by one or more doctors certificates provided to the company at intervals of no more than seven day during the period of sickness absence.",
        },
        {
          id: '8.5',
          text: 'Accidents',
          desc: 'Please record all accidents in the accident report book.  It is very important that these notes are made so that we can keep a track of accidents and any repercussions that may occur. The premises  have designated locations for the first aid kit. i.e office. It is the responsibility of staff and mainly that staff member using the items to put anything needed back on the stocking up list.',
        },
        {
          id: '8.6',
          text: ' Health & safety',
          desc: 'Serious breaches of health and safety constitute gross misconduct and may result in immediate termination in order to protect the safety of others.  Please refer to health and safety document in staff training file (STF) for more information on all aspects of this or contact us about anything you are unsure of. ',
        },
      ],
    },
  ];
  const staffGuidePage8 = [
    {
      id: '',
      text: '',
      children: [
        {
          id: '8.7',
          text: 'Drinking at work',
          desc: 'The consumption of alcohol whilst at work is strictly forbidden and is deemed a health and safety risk.',
        },
        {
          id: '8.8',
          text: 'Gross misconduct, restricted sales',
          desc: 'A number of items that are either retailed in our stores or served and sold in are restaurants may have restrictions on them. Training is provided at the time of collecting employee information and on the staff training sheet which is signed off by the employee.  If you are under 18 you will not be able to authorise the sale of these items and you will require having these sales approved by a member of staff over 18.  Making a sale to an under 18 on these restricted items is against the law and having received training may jeopardise not only company reputation but may leave the employee and/or employer with a custodial sentence or fine.  Please be aware that as we provide all of the information and training to the employee to avoid the occurrence of such instances. We consider any such circumstances or near misses as a gross misconduct and may result in immediate termination.',
        },
        {
          id: '8.9',
          text: 'Medical examinations',
          desc: 'The company may require you to undergo a medical examination by a medical practioner nominated by it at any stage of your employment. The cost of any such examination or examinations will be met by the company and you will co-operate in the disclosure of all results and reports to the company. The company will only request such an examination where reasonable to do so.',
        },
      ],
    },
    {
      id: '9.0',
      text: 'Management sheets',
      children: [
        {
          id: '9.1',
          text: 'Daily sheets',
          desc: 'Each day will require a new sheet and reading. There are a new set of tasks each day to be undertaken and it is very important that these are done on time.  Please be aware that the sheets do change from time to time and so require attention.',
        },
        {
          id: '9.2',
          text: 'Responsibility',
          desc: 'It is the responsibility of all staff members to carry out all tasks and records on the management sheet .Initialling a task means that you assume responsibility for its completion. You may become answerable for anything that is not correct if it has been signed off by you. Omissions of not writing about complaints for example, will be taken very seriously especially if this is for the purpose of covering up acts or omissions.  Breakages are another example of where we wish to see staff taking responsibility and we reserve the right to make charges against remuneration payments.',
        },
      ],
    },
    {
      id: '10.0',
      text: 'Accessibility',
      children: [
        {
          id: '10.1',
          text: 'Documents',
          desc: 'Copies of the several staff related documents (such as risk assessment, Health & safety leaflet) can be found in STF. We recommend you to use this folder regularly.',
        },
      ],
    },
    {
      id: '11.0',
      text: 'Grievances',
      children: [
        {
          id: '11.1',
          text: 'Purpose',
          desc: "<ul class='description-list'><li>This Code is designed to help employers, employees and their representatives deal with disciplinary and grievance situations in the workplace.</li><li>Disciplinary situations include misconduct and/or poor performance</li><li>If employers have a separate capability procedure they may prefer to address performance issues under this procedure. If so, however, the basic principles of fairness set out in this Code should still be followed, albeit that they may need to be adapted</li></ul>",
        },
      ],
    },
  ];
  const staffGuidePage9 = [
    {
      id: '',
      text: '',
      children: [
        {
          id: '',
          text: '',
          desc: "<ul class='description-list'><li>Grievances are concerns, problems or complaints that employees raise with their employers.</li><li>The Code does not apply to redundancy dismissals or the non renewal of fixed term contracts on their expiry.</li></ul>",
        },
        {
          id: '11.2',
          text: 'Fairness & transparency',
          desc: 'Fairness and transparency are promoted by developing and using rules and procedures for handling disciplinary and grievance situations.  These should be set down in writing, be specific and clear. Employees and, where appropriate, their representatives should be involved in the development of rules and procedures. It is also important to help employees and managers understand what the rules and procedures are, where they can be found and how they are to be used.',
        },
        {
          id: '11.3',
          text: 'Formal action',
          desc: 'Where some form of formal action is needed, what action is reasonable or justified will depend on all the circumstances of the particular case. Employment tribunals will take the size and resources of an employer into account when deciding on relevant cases and it may sometimes not be practicable for all employers to take all of the steps set out in this Code.',
        },
        {
          id: '11.4',
          text: 'Procedures',
          desc: "That said, whenever a disciplinary or grievance process is being followed it is important to deal with issues fairly. There are a number of elements to this.<p class='pt16' />Employers and employees should raise and deal with issues promptly and should not unreasonably delay meetings, decisions or confirmation of those decisions.",
        },
        {
          id: '11.5',
          text: 'CODE OF PRACTICE 1',
          desc: "<ul class='description-list'><li>Employers and employees should act consistently.</li><li>Employers should carry out any necessary investigations, to establish the facts of the case.</li><li>Employers should inform employees of the basis of the problem and give them an opportunity to put their case in response before any decisions are made.</li><li>Employers should allow employees to be accompanied at any formal disciplinary or grievance meeting.</li><li>Employers should allow an employee to appeal against any formal decision made.</li></ul>",
        },
      ],
    },
    {
      id: '12.0',
      text: 'Terms',
      desc: 'The terms of this document are a good guide to the operation of the restaurant and the undertakings of the work. Deliberate or continued Infringement of the terms may result in a warning letter and/or dismissal or retraining at a reduced hourly pay.  The continued co-operation of everyone within the work place is expected.',
      children: [
        {
          id: '12.1',
          text: 'Termination of employment contract',
          desc: 'Where practicable dismissals/termination of contracts will be performed face to face.  Please be aware that these conversations may be recorded for purpose of witness. Please also refer to final pay following dismissal.',
        },
        {
          id: '12.2',
          text: 'Severability',
          desc: 'If any provision or part-provision of this agreement is or becomes invalid, illegal or unenforceable, it shall be deemed modified to the minimum extent necessary to make it valid, legal and enforceable. If such modification is not possible, the relevant provision or part-provision shall be deemed deleted. Any modification to or deletion of a provision or part-provision under this clause shall not affect the validity and enforceability of the rest of this agreement.',
        },
        {
          id: '12.3',
          text: 'Other important notes',
        },
      ],
    },
  ];
  const staffGuidePage10 = [
    {
      id: '',
      text: '',
      desc: '',
      children: [
        {
          id: '',
          text: '',
          desc: "<ul class='description-list' ><li>Employees are not allowed to carry money on them while working. Money found with employee while working will be assumed to belong to the business and will be taken off by the employers. Employees are provided with personal locker where you can keep your personal belonging such as money, phones before starting the shift. You are allowed to carry the key to this locker and the safety of the locker is at your own risk.</li><li>Employers have full right to check your pockets and belongings at any time without giving any notice but in your presence. Your full cooperation will be expected.</li><li>Employees cannot do any publication on social media about business and its property or anything which relates to business without permission from business. We take business indemnity very seriously.</li><li>All employees need to obtain food hygiene level-2 training certificate within 3 months of joining after initial food hygiene training given during first week of starting the job.</li><li>You are not allowed to share any recipes and business secrets outside the business. If you are starting a similar food business after leaving the job with the company, you are not allowed to start in 10 miles radius without prior consent. Otherwise, it may result in legal action.</li><li>No grazing during duty hours and one should remain dedicated to work during duty hours. You are not allowed to leave the work place and work station during duty hours.</li><li>You will not at any time either during your employment or afterwards, to  the detriment or prejudice of the company or the company’s customers, use or divulge to any person, firm or company, except in the proper course of  your duties during your employment by the company, any confidential information identify relating to the company, details of which are not in the public domains, or such confidential information or trade secrets relating to the business of any customer of the company which have come to your knowledge during your employment.</li><li>Staff members must attend all the team meetings and put a positive contribution towards the growth of the organization. Team meets are organized periodically and you will be informed about it as soon as possible.</li><li>We recommend staff members to take part in team outing events and team meals. It is a great way to build positive relationship with the team members and they are very joyful gatherings.</li></ul>",
        },
      ],
    },
  ];

  const employeeContract = () => {
    return (
      <Box
        ref={componentRef}
        className="pdf-generate-main-container employee-contract-wrapper"
      >
        <Box className="pb32 text-align">
          <Box className="nv-header-logo">
            <Image src={HMRCLogo} alt="" />
          </Box>
          <Box className="">
            <Typography variant="h5" className="p14 fw600">
              Staff Contract and Guide <br />
              Namaste Village Norwich Ltd.
              <br />
              131-139, Queens Road, Norwich, NR1 3PN
            </Typography>
          </Box>
        </Box>
        <Box className="emp-information-wrapper">
          {/* PAGE 1 */}
          <Box className="">
            <Typography variant="h6" className="p14 fw600">
              Employer’s name:
              <span variant="h6" className="p12 pl8">
                {userDetails?.user_first_name}
              </span>
            </Typography>
            <Typography variant="h6" className="p14 pt8 fw600">
              Main place of work:
              <span variant="h6" className="p12 pl8">
                {userDetails?.branch?.branch_name}
              </span>
            </Typography>
            <Typography variant="h6" className="p14 pt8 fw600">
              Employee Name:
              <span variant="h6" className="p12 pl8">
                {userDetails?.employee_name}
              </span>
            </Typography>
            <Typography variant="h6" className="p14 pt8 fw600">
              Employee Address:
              <span variant="h6" className="p12 pl8">
                {userDetails?.branch?.employee_address}
              </span>
            </Typography>
            <Typography variant="h6" className="p14 pt8 fw600">
              National Insurance number:
              <span variant="h6" className="p12 pl8">
                {userDetails?.insurance_number}
              </span>
            </Typography>
            <Typography variant="h6" className="p14 pt8 fw600">
              Job Title:
              <span variant="h6" className="p12 pl8">
                {userDetails?.user_roles?.[0].label}
              </span>
            </Typography>
            <Typography variant="h6" className="p14 pt8 fw600">
              Date of commencement of employment:
              <span variant="h6" className="p12 pl8">
                {userDetails?.user_joining_date
                  ? moment(userDetails?.user_joining_date)?.format('DD/MM/YYYY')
                  : ''}
              </span>
            </Typography>
            <Typography variant="h6" className="p14 pt8 fw600">
              Duties and responsibilities:
              <span variant="h6" className="p12 pl8">
                {userDetails?.user_roles
                  ?.map((item) => {
                    return item?.label;
                  })
                  ?.join(', ')}
              </span>
            </Typography>
            <Box className="duties-responsibilities-container">
              <ul className="ml35 pt16">
                <li>
                  <Typography variant="h6" className="p12">
                    Serve all customers in line with service flow set up by the
                    management
                  </Typography>
                </li>
                <li>
                  <Typography variant="h6" className="p12">
                    The set up and maintenance of all restaurant service areas.
                  </Typography>
                </li>
                <li>
                  <Typography variant="h6" className="p12">
                    Welcoming of members and guests to the restaurants.
                  </Typography>
                </li>
                <li>
                  <Typography variant="h6" className="p12">
                    All enquires made in person or over the phone must be
                    handled to the standards of the restaurants.
                  </Typography>
                </li>
                <li>
                  <Typography variant="h6" className="p12">
                    Serving of meals/drinks to members and guests.
                  </Typography>
                </li>
                <li>
                  <Typography variant="h6" className="p12">
                    To notify the Manager or his/her deputy where
                    stocks/supplies are low.
                  </Typography>
                </li>
                <li>
                  <Typography variant="h6" className="p12">
                    To ensure that all duties are carried out in accordance with
                    statutory and/or Group Health and Safety requirements,
                    including food hygiene regulations.
                  </Typography>
                </li>
                <li>
                  <Typography variant="h6" className="p12">
                    To adhere to all Restaurant standards in every interaction
                    with members and guests, colleagues, other members of the
                    team and external suppliers and contacts.
                  </Typography>
                </li>
                <li>
                  <Typography variant="h6" className="p12">
                    To manage all payment transactions in accordance with the
                    Restaurant financial policies and procedures and to handle
                    all transactions with diligence, honesty and integrity.
                  </Typography>
                </li>
                <li>
                  <Typography variant="h6" className="p12">
                    To support colleagues at peak times and to undertake any
                    operational duty which might be reasonably required, to
                    ensure customer expectations are met.
                  </Typography>
                </li>
                <li>
                  <Typography variant="h6" className="p12">
                    To participate, constructively, in performance reviews with
                    the Manager and to work towards objectives/goals set by the
                    Manager, and to improve any areas of performance felt
                    necessary by the Manager, in order to maintain an excellent
                    level of service to customers.
                  </Typography>
                </li>
                <li>
                  <Typography variant="h6" className="p12">
                    To assist in the receipt of deliveries when required.
                  </Typography>
                </li>
                <li>
                  <Typography variant="h6" className="p12">
                    Attend all team meetings & provide valuable inputs{' '}
                  </Typography>
                </li>
              </ul>
              <Typography variant="h6" className="p12 pt8">
                *Also, the employer may require you to carry out other
                reasonable duties as required.
              </Typography>
              <Typography variant="h6" className="p12 pt8 fw600">
                Hours of work: Flexible Hours / Maximum 20 hours per week /Full
                Time/ Zero Hour/ Part Time
              </Typography>
              <Typography variant="h6" className="p12 pt8 fw600">
                Salary:<span className="fw400"> £10.42 /hour</span>
              </Typography>
            </Box>
            <Box className="emp-contract-footer" style={{ marginTop: '30px' }}>
              <Typography variant="h6" className="p12 pt4 arial-font">
                1
              </Typography>
            </Box>
          </Box>
          {/* PAGE 2 */}
          <Box className="mt40" style={{ pageBreakBefore: 'always' }}>
            <Typography className="p14 fw600">
              <span className="text-underline">NOTE:</span> Please read and
              understand this attached document carefully. Should you have any
              queries or should you not understand anything regarding this
              contract please contact us for clarification. Not understanding
              these terms and that of counterpart documents through ignorance or
              language will not be an acceptable form of argument.
            </Typography>
            <Typography className="p14 fw600 pt16">Contents</Typography>
            <Box className="emp-contents pt16 grid-2container">
              <NestedOrderedList data={contentListData1} />
              <NestedOrderedList data={contentListData2} />
            </Box>
            <Box className="emp-contract-footer" style={{ marginTop: '200px' }}>
              <Typography variant="h6" className="p12 pt4 arial-font">
                2
              </Typography>
            </Box>
          </Box>
          {/* PAGE 3 */}
          <Box className="mt40 emp-information-content">
            <Box className="emp-contents emp-list-data pt16">
              <NestedOrderedListData data={staffGuidePage3} />
            </Box>
            <Box className="emp-contract-footer" style={{ marginTop: '20px' }}>
              <Typography variant="h6" className="p12 pt4 arial-font">
                3
              </Typography>
            </Box>
          </Box>
          {/* PAGE 4 */}
          <Box className="mt40 emp-information-content">
            <Box className="emp-contents emp-list-data pt16">
              <NestedOrderedListData data={staffGuidePage4} />
            </Box>
            <Box className="emp-contract-footer" style={{ marginTop: '50px' }}>
              <Typography variant="h6" className="p12 pt4 arial-font">
                4
              </Typography>
            </Box>
          </Box>
          {/* PAGE 5 */}
          <Box className="mt40 emp-information-content">
            <Box className="emp-contents emp-list-data pt16">
              <NestedOrderedListData data={staffGuidePage5} />
            </Box>
            <Box className="emp-contract-footer" style={{ marginTop: '50px' }}>
              <Typography variant="h6" className="p12 pt4 arial-font">
                5
              </Typography>
            </Box>
          </Box>
          {/* PAGE 6 */}
          <Box className="mt40 emp-information-content">
            <Box className="emp-contents emp-list-data pt16">
              <NestedOrderedListData data={staffGuidePage6} />
            </Box>
            <Box className="emp-contract-footer" style={{ marginTop: '30px' }}>
              <Typography variant="h6" className="p12 pt4 arial-font">
                6
              </Typography>
            </Box>
          </Box>
          {/* PAGE 7 */}
          <Box className="mt40 emp-information-content">
            <Box className="emp-contents emp-list-data pt16">
              <NestedOrderedListData data={staffGuidePage7} />
            </Box>
            <Box className="emp-contract-footer" style={{ marginTop: '20px' }}>
              <Typography variant="h6" className="p12 pt4 arial-font">
                7
              </Typography>
            </Box>
          </Box>
          {/* PAGE 8 */}
          <Box className="mt40 emp-information-content">
            <Box className="emp-contents emp-list-data pt16">
              <NestedOrderedListData data={staffGuidePage8} />
            </Box>
            <Box className="emp-contract-footer" style={{ marginTop: '30px' }}>
              <Typography variant="h6" className="p12 pt4 arial-font">
                8
              </Typography>
            </Box>
          </Box>
          {/* PAGE 9 */}
          <Box className="mt40 emp-information-content">
            <Box className="emp-contents emp-list-data pt16">
              <NestedOrderedListData data={staffGuidePage9} />
            </Box>
            <Box className="emp-contract-footer" style={{ marginTop: '50px' }}>
              <Typography variant="h6" className="p12 pt4 arial-font">
                9
              </Typography>
            </Box>
          </Box>
          {/* PAGE 10 */}
          <Box className="mt40 emp-information-content">
            <Box className="emp-contents emp-list-data pt16">
              <NestedOrderedListData data={staffGuidePage10} />
            </Box>
            {/* Signature Details */}
            <Box className="emp-signature-wrapper pt32">
              <Box className="emp-signature-content">
                <Typography variant="h6" className="p16">
                  Employee Print Name:
                </Typography>
                <Typography variant="h6" className="p16">
                  Employee Sign:
                </Typography>
                <Typography variant="h6" className="p16">
                  Date:
                </Typography>
              </Box>
              <Box className="emp-signature-content">
                <span className="text-sign-border"></span>
                <span className="text-sign-border"></span>
                <span className="text-sign-border"></span>
              </Box>
              <Box className="emp-signature-content">
                <Typography variant="h6" className="p16">
                  Employee Print Name:
                </Typography>
                <Typography variant="h6" className="p16">
                  Employee Sign:
                </Typography>
                <Typography variant="h6" className="p16">
                  Date:
                </Typography>
              </Box>
              <Box className="emp-signature-content">
                <span className="text-sign-border"></span>
                <span className="text-sign-border"></span>
                <span className="text-sign-border"></span>
              </Box>
            </Box>
            <Box className="emp-contract-footer" style={{ marginTop: '200px' }}>
              <Typography variant="h6" className="p12 pt4 arial-font">
                10
              </Typography>
            </Box>
          </Box>
        </Box>
      </Box>
    );
  };

  return (
    <div>
      <div style={{ display: 'none' }}>{employeeContract()}</div>
      <Button
        onClick={() => handleDownlodPDF()}
        className="pdf-download-btn"
        disableRipple
      >
        <InsertDriveFileOutlinedIcon />
        <span className="p14 pl4 text-underline">View PDF</span>
      </Button>
    </div>
  );
};

export default EmployeeContractPDF;
