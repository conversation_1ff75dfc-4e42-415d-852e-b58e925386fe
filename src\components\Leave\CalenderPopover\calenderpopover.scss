@import '@/styles/variable.scss';

.calender-leave-popover {
  width: 300px !important; // Set popover width explicitly
  box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.1) !important;
  height: 100%;
  max-height: max-content;
  min-height: 210px;
  background-color: $color-White !important;
  margin: 8px 0px 0px !important;
  padding: 16px !important;
  .user-leave-details-wrap {
    .user-details-wrap {
      gap: 10px;
    }
    .user-role {
      color: $color-Dark-50;
    }
    .leave-type-wrap {
      .leave-type {
        width: 50%;
      }
    }
    .leave-date-wrap {
      width: 50%;
    }
    .leave-type-text {
      text-transform: capitalize;
    }
    .leave-text-wrap {
      color: $color-Dark-50;
    }
    .title-text-wrap {
      color: $color-Dark-80;
    }
    .total-days-wrap {
      .total-days-text-wrap,
      .total-days {
        width: 50%;
      }
    }
    .approved-leave {
      text-transform: capitalize;
      color: $color-green;
    }
    .pending-leave {
      text-transform: capitalize;
      color: $color-Warning;
    }
    .rejected-leave {
      text-transform: capitalize;
      color: $color-dark-red;
    }
    .canceled-leave {
      text-transform: capitalize;
      color: $color-Black;
    }
  }
  .right-arrow-wrap {
    line-height: 0px;
    background-color: $color-primary-bg-color;
    padding: 5px;
    border-radius: 3px;
    .right-arrow-icon {
      cursor: pointer;
      fill: $color-primary;
      height: 21px;
      width: 21px;
    }
  }
}
