.tickets-list-container {
  // Remove bottom margin from last ticket to eliminate extra space
  .ticket-wrap:last-child {
    margin-bottom: 0 !important;
  }
}

.ticket-list-container-wrap {
  padding-bottom: var(--spacing-none);
}

.ticket-wrap {
  width: 100%;
  border: 1px solid var(--border-color-light-gray);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--border-radius-md);
  position: relative;
  background-color: var(--color-white);
  margin-bottom: var(--spacing-md);

  &.selected {
    border: 2px solid var(--color-primary);
  }
  .status-chip-wrap {
    width: 100%;
    max-width: max-content;
  }

  // New styling for status and delete icon container
  .status-delete-wrap {
    .delete-icon {
      font-size: var(--icon-size-sm);
      fill: var(--icon-charcoal-gray);
    }
  }
  .id-name-wrap {
    gap: var(--spacing-lg);
    cursor: pointer;
    padding: var(--spacing-md) 0px;

    .id-wrap {
      color: var(--color-dark-50);
      border-bottom: 1px dashed var(--color-light-grayish-blue);
      line-height: var(--line-height-base);
    }
  }

  .heading-text-wrap {
    font-weight: var(--font-weight-medium);
  }

  .description-wrap {
    padding: var(--spacing-xs) 0;

    .description-text {
      color: var(--color-dark-50);
      font-size: var(--font-size-sm);
      line-height: var(--line-height-relaxed);
    }
  }

  .name-wrap {
    color: var(--color-dark-50);
    max-width: 155px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .name-wrap {
    color: var(--color-dark-50);
    max-width: 155px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .date-wrap {
    padding: var(--spacing-xs) 0;

    .date-text {
      color: var(--color-dark-50);
      font-size: var(--font-size-sm);
    }
  }

  .category-wrap {
    padding: var(--spacing-xs) 0;

    .category-text {
      color: var(--color-dark-50);
      font-size: var(--font-size-sm);
    }
  }

  .status-badge-wrap {
    padding: var(--spacing-xs) 0;
  }

  .name-time-wrap {
    .user-icon {
      font-size: var(--font-size-md);
      color: var(--color-dark-50);
    }

    .time-icon {
      font-size: var(--font-size-md);
      color: var(--color-dark-50);
    }

    .name-text {
      color: var(--color-dark);
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
    }

    .time-text {
      color: var(--color-dark-50);
      font-size: var(--font-size-sm);
    }
  }

  .assigned-wrap {
    padding: var(--spacing-xs) 0;

    .assigned-text {
      color: var(--color-dark-50);
      font-size: var(--font-size-sm);

      .assigned-label {
        font-weight: var(--font-weight-medium);
      }
    }
  }

  .time-wrap {
    color: var(--color-dark-50);

    .timer-icon {
      height: var(--font-size-sm);
      width: var(--font-size-sm);
      fill: var(--color-primary);
    }
  }

  .profile-wrap {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-lg);
    line-height: 0px;

    .profile-image {
      height: 35px;
      width: 35px;
      border-radius: var(--border-radius-full);
    }
  }

  .ticket-status-select {
    padding-top: var(--spacing-md);

    .MuiFormControl-root {
      width: auto;

      .MuiSvgIcon-root {
        height: var(--icon-size-sm);
        width: var(--icon-size-sm);
        margin-top: var(--spacing-xs);
      }
    }

    .MuiInputBase-root {
      font-family: var(--font-family-primary) !important;
    }

    .MuiSelect-select {
      padding: var(--spacing-tiny) 0px 0px var(--spacing-md);
      font-size: var(--font-size-sm) !important;
    }

    fieldset {
      height: 25px !important;
      border-radius: var(--border-radius-xs);
    }
  }
}
