// DownloadField Component Styles
.download-field-container {
  // background-color: var(--color-white);
  // padding: var(--spacing-xxl);
  // border-radius: var(--border-radius-lg);
  // box-shadow: var(--box-shadow-xs);
  // Page Header Section
  .page-header {
    margin-bottom: 2rem;

    .header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 1rem;
      flex-wrap: wrap;

      .header-actions {
        display: flex;
        align-items: center;
        gap: 0.75rem;

        .field-counter {
          color: var(--text-light-dark);

          .counter-number {
            font-weight: var(--font-weight-medium);
          }
        }

        .back-button {
          display: flex;
          align-items: center;
          padding: var(--spacing-lg) var(--spacing-lg);
          color: var(--text-light-dark);
          background: transparent;
          border: none;
          cursor: pointer;
          transition: color 0.15s ease;

          &:hover {
            color: var(--text-color-black);
          }

          .back-icon {
            margin-right: var(--spacing-sm);
            width: var(--icon-size-xs);
            height: var(--icon-size-xs);
            stroke-width: 2;
          }
        }
      }
    }
  }

  // Search Section
  .search-section {
    margin-bottom: var(--spacing-xxl);
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
    // margin-bottom: var(--spacing-2xl);

    @media (min-width: 1024px) {
      grid-template-columns: 1fr 1fr;
    }

    .search-container {
      position: relative;
      max-width: 28rem;
    }
  }

  // Main Content Grid
  .main-contents {
    // display: grid;
    // grid-template-columns: 1fr;
    // gap: var(--spacing-2xl);
    // margin-bottom: var(--spacing-2xl);

    // @media (min-width: 1024px) {
    //   grid-template-columns: 1fr 1fr;
    // }

    // Available Fields Section
    .available-fields {
      .section-title {
        margin-bottom: var(--spacing-xxl);
        font-weight: var(--font-weight-semibold);
      }
      .field-count {
        color: var(--text-light-dark);
        font-weight: var(--font-weight-regular);
      }
      .categories-container {
        // display: flex;
        // flex-direction: column;
        // gap: var(--spacing-lg);
        display: grid;
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
        margin-bottom: var(--spacing-2xl);

        @media (min-width: 1024px) {
          grid-template-columns: 1fr 1fr;
        }
      }
    }
  }

  // Export Preview Section
  .export-preview-section {
    margin-bottom: var(--spacing-2xl);
  }
  .preview-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);

    .preview-button,
    .save-button {
      display: flex;
      align-items: center;
      padding: var(--spacing-sm) var(--spacing-lg);
      background: transparent;
      border: none;
      cursor: pointer;
      transition: color 0.15s ease;
      font-size: var(--font-size-sm);

      .button-icon {
        margin-right: var(--spacing-sm);
        width: var(--icon-size-xs);
        height: var(--icon-size-xs);
        stroke-width: 2;
      }
    }

    .preview-button {
      color: var(--color-primary);

      &:hover {
        color: var(--color-green);
      }
    }

    .save-button {
      color: var(--text-light-dark);

      &:hover {
        color: var(--text-color-black);
      }
    }
  }
  // Action Buttons Section
  .action-buttons {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-lg);
    padding: var(--spacing-xxl);
    background: var(--color-off-white);
    border-radius: var(--border-radius-md);
    border: var(--normal-sec-border);

    @media (min-width: 640px) {
      flex-direction: row;
    }

    .main-actions {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);

      .cancel-button {
        padding: var(--spacing-sm) var(--spacing-xxl);
        border: var(--normal-sec-border);
        color: var(--text-light-dark);
        background: var(--color-white);
        border-radius: var(--border-radius-md);
        cursor: pointer;
        transition: all 0.15s ease;
        font-size: var(--font-size-sm);

        &:hover {
          background: var(--color-secondary);
        }

        &:focus {
          outline: none;
          box-shadow:
            0 0 0 2px var(--color-primary),
            0 0 0 4px rgba(19, 94, 150, 0.1);
        }
      }

      .continue-button {
        padding: var(--spacing-sm) var(--spacing-xxl);
        background: var(--color-primary);
        color: var(--color-white);
        border: none;
        border-radius: var(--border-radius-md);
        cursor: pointer;
        transition: all 0.15s ease;
        font-size: var(--font-size-sm);

        &:hover {
          background: var(--color-green);
        }

        &:disabled {
          opacity: var(--opacity-5);
          cursor: not-allowed;
        }

        &:focus {
          outline: none;
          box-shadow:
            0 0 0 2px var(--color-primary),
            0 0 0 4px rgba(19, 94, 150, 0.1);
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .download-field-container {
    .page-header {
      .header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;

        .header-actions {
          width: 100%;
          justify-content: space-between;
        }
      }
    }

    .main-contents {
      // grid-template-columns: 1fr;
    }

    .action-buttons {
      flex-direction: column;
      align-items: stretch;

      .preview-actions {
        justify-content: center;
      }

      .main-actions {
        justify-content: stretch;

        .cancel-button,
        .continue-button {
          flex: 1;
        }
      }
    }
  }
}
