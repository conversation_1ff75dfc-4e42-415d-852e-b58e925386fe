body {
  .public-footer-section {
    width: 100%;
    max-width: 1284px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
    .footer-content-wrap {
      background-color: var(--color-white);
      text-align: center;
      padding: var(--spacing-4xl) var(--spacing-3xl);
      border: var(--normal-sec-border);
      margin-top: var(--spacing-4xl);

      .help-text-wrap {
        .help-text {
          margin-bottom: var(--spacing-xl);
        }
      }
      @media (max-width: 767px) {
        margin: 0px;
      }
    }
    .footer-links {
      padding-top: 80px;
      .footer-link-text {
        padding: 0 var(--spacing-xxl);
        &:hover {
          color: var(--color-charcoal-gray);
          opacity: var(--opacity-6);
        }
        @media (max-width: 767px) {
          padding-bottom: var(--spacing-xl);
        }
      }
      @media (max-width: 767px) {
        flex-direction: column;
        align-items: center;
      }
    }
    .powered-by-wrap {
      margin-top: 60px;
      padding-bottom: 90px;
    }
    @media (max-width: 767px) {
      max-width: 460px;
      // padding: var(--spacing-none) var(--spacing-2xl);
    }
  }
}
