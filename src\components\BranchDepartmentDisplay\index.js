import React, { useContext } from 'react';
import { Box, Typography } from '@mui/material';
import AuthContext from '@/helper/authcontext';
import './branchdep.scss';

const BranchDepartmentDisplay = ({ row, isBranchOnly }) => {
  const { AllListsData } = useContext(AuthContext);
  const defaultTextColor = '#ffffff';
  const defaultBackgroundColor = '#006bff';
  const branchName =
    !row?.branch?.branch_color &&
    !row?.branch?.text_color &&
    AllListsData?.ActiveBranchList?.find((f) => f?.value === row?.branch?.id);
  return (
    <Box className="d-flex align-center justify-start h100 gap-sm flex-wrap">
      {row?.branch?.branch_name || branchName?.label ? (
        <Typography
          className="title-text branch-name-wrap text-ellipsis"
          style={{
            backgroundColor: row?.branch?.branch_color
              ? row?.branch?.branch_color
              : branchName?.color
                ? branchName?.color
                : defaultBackgroundColor,
            color: row?.branch?.text_color
              ? row?.branch?.text_color
              : branchName?.textcolor
                ? branchName?.textcolor
                : defaultTextColor,
          }}
        >
          <span>{row?.branch?.branch_name || branchName?.label}</span>
        </Typography>
      ) : (
        <Typography className="text-align w100">-</Typography>
      )}

      {!isBranchOnly && row?.department?.department_name && (
        <Box className="title-text bg-dept-transparent text-ellipsis">
          <Typography>
            <span>{row?.department?.department_name}</span>
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default BranchDepartmentDisplay;
