'use client';
import React, {
  // useContext,
  useEffect,
  useState,
} from 'react';
import { Box } from '@mui/material';
// import AuthContext from '@/helper/authcontext';
// import Notification from './@notification';
import axiosInstance from '@/helper/axios/axiosInstance';
// import axiosInstanceOrg from '@/helper/axios/axiosInstanceOrg';
import {
  // ORG_URLS,
  URLS,
} from '@/helper/constants/urls';
import { setApiMessage } from '@/helper/common/commonFunctions';
import CustomOrgPagination from '@/components/UI/customPagination';
import NotificationsNoneIcon from '@mui/icons-material/NotificationsNone';
import PermIdentityOutlinedIcon from '@mui/icons-material/PermIdentityOutlined';
import HomeOutlinedIcon from '@mui/icons-material/HomeOutlined';
import ChangeCircleOutlinedIcon from '@mui/icons-material/ChangeCircleOutlined';
import NotStartedOutlinedIcon from '@mui/icons-material/NotStartedOutlined';
import SchoolOutlinedIcon from '@mui/icons-material/SchoolOutlined';
import { DSRIcon, ResignationIcon } from '@/helper/common/images';
import QueryBuilderOutlinedIcon from '@mui/icons-material/QueryBuilderOutlined';
import Notification from './Notification';
import CustomTabs from '@/components/UI/CustomTabs';
import NoDataView from '@/components/UI/NoDataView';
import ContentLoader from '@/components/UI/ContentLoader';

export default function OwnNotification() {
  // const { authState, setAuthState } = useContext(AuthContext);
  const [activeTab, setActiveTab] = useState(1);
  const [loader, setLoader] = useState(true);
  const [notificationList, setNotificationList] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [currentPageper, setCurrentPagePer] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [totalCountPer, setTotalCountPer] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [rowsPerPPage, setRowsPerPPage] = useState(10);
  // const userData = fetchFromStorage(identifiers?.USER_DATA);

  // const userID = userData?.user?.id;

  const handleTabChange = (val) => {
    setActiveTab(val);
    val === 1
      ? getOwnNotification(currentPage, 1, '', false)
      : getOwnNotification(currentPageper, 2, '', false);
  };

  const getOwnNotification = async (page, tabVal, Rpp, isUpdated) => {
    !isUpdated && setLoader(true);
    const rowPer =
      Number(tabVal) === 1 ? (Rpp ?? rowsPerPage) : (Rpp ?? rowsPerPPage);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_OWN_NOTIFICATON +
          `?tab=${Number(tabVal)}&size=${rowPer}&page=${page}`
      );
      if (status === 200) {
        setLoader(false);
        const notificationList = data?.notificationList;
        Number(tabVal) === 1
          ? setTotalCount(data?.count)
          : setTotalCountPer(data?.count);
        setNotificationList(
          notificationList?.length > 0 ? notificationList : []
        );
      }
    } catch (error) {
      setLoader(false);
      setNotificationList([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // const getOwnNotification = async (page, tabVal, Rpp) => {
  //   setLoader(true);
  //   const rowPer =
  //     Number(tabVal) === 1 ? (Rpp ?? rowsPerPage) : (Rpp ?? rowsPerPPage);

  //   try {
  //     const orgRequest = axiosInstanceOrg.get(
  //       ORG_URLS?.GET_NOTIFICATION_LIST +
  //         `/${userID}?size=${rowPer}&page=${page}`
  //     );

  //     const ownRequest = axiosInstance.get(
  //       URLS?.GET_OWN_NOTIFICATON +
  //         `?tab=${Number(tabVal)}&size=${rowPer}&page=${page}`
  //     );

  //     const [orgRes, ownRes] = await Promise.all([orgRequest, ownRequest]);

  //     const orgList = orgRes?.data?.notificationList || [];
  //     const ownList = ownRes?.data?.notificationList || [];

  //     const totalCount = orgRes?.data?.count || 0;
  //     const totalCountPer = ownRes?.data?.count || 0;

  //     Number(tabVal) === 1
  //       ? setTotalCount(totalCount + totalCountPer)
  //       : setTotalCountPer(totalCount + totalCountPer);

  //     const merged = [...orgList, ...ownList].sort(
  //       (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
  //     );

  //     setNotificationList(merged);
  //     setLoader(false);
  //   } catch (error) {
  //     setLoader(false);
  //     setNotificationList([]);
  //     setApiMessage('error', error?.response?.data?.message);
  //   }
  // };

  const handleNotificationIcon = (type) => {
    if (type === 'profile') {
      return (
        <Box className="notification-icon">
          <PermIdentityOutlinedIcon />
        </Box>
      );
    } else if (type === 'resignation') {
      return (
        <Box className="notification-icon resign-icon">{ResignationIcon()}</Box>
      );
    } else if (type === 'dsr_request') {
      return (
        <Box className="notification-icon">
          <QueryBuilderOutlinedIcon />
        </Box>
      );
    } else if (type === 'dsr') {
      return <Box className="notification-icon noti-dsr-icon">{DSRIcon()}</Box>;
    } else if (type === 'media') {
      return (
        <Box className="notification-icon">
          <NotStartedOutlinedIcon />
        </Box>
      );
    } else if (type === 'change_request') {
      return (
        <Box className="notification-icon">
          <ChangeCircleOutlinedIcon />
        </Box>
      );
    } else if (type === 'onboarding') {
      //onboarding
      return (
        <Box className="notification-icon">
          <SchoolOutlinedIcon />
        </Box>
      );
    } else if (type === 'leave') {
      return (
        <Box className="notification-icon">
          <HomeOutlinedIcon />
        </Box>
      );
    } else {
      return (
        <Box className="notification-icon">
          <NotificationsNoneIcon />
        </Box>
      );
    }
  };

  useEffect(() => {
    getOwnNotification(1, 1, '', false);
  }, []);

  const onPageChange = (newPage) => {
    activeTab === 1 ? setCurrentPage(newPage) : setCurrentPagePer(newPage);
    getOwnNotification(newPage, activeTab, '', true);
  };
  const OnRowPerPage = (newPage) => {
    activeTab === 1 ? setRowsPerPage(newPage) : setRowsPerPPage(newPage);
    activeTab === 1 ? setCurrentPage(1) : setCurrentPagePer(1);
    getOwnNotification(1, activeTab, newPage, '', true);
  };
  const handleMarkAsRead = async (noti_data) => {
    setLoader(true);
    try {
      const { status } = await axiosInstance.get(
        URLS?.MARK_AS_READ_NOTIFICATION + `/${noti_data?.id}`
      );
      if (status === 200) {
        setLoader(false);
        getOwnNotification(
          activeTab === 1 ? currentPage : currentPageper,
          activeTab,
          '',
          true
        );
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const notification_tabs = [
    {
      id: 1,
      label: 'General',
      component: (
        <Notification
          notificationList={notificationList}
          handleMarkAsRead={handleMarkAsRead}
          handleNotificationIcon={handleNotificationIcon}
        />
      ),
    },
    {
      id: 2,
      label: 'Personal',
      component: (
        <Notification
          notificationList={notificationList}
          handleMarkAsRead={handleMarkAsRead}
          handleNotificationIcon={handleNotificationIcon}
        />
      ),
    },
  ];

  const getCurrentContent = () => {
    const currentItem = notification_tabs?.find(
      (item) => item?.id === activeTab
    );

    return currentItem?.component || notification_tabs?.[0]?.component;
  };

  return (
    <>
      <Box className="own-notification-container">
        <CustomTabs
          tabs={notification_tabs}
          initialTab={activeTab}
          onTabChange={handleTabChange}
        />

        {loader ? (
          <ContentLoader />
        ) : (
          <>
            {notificationList && notificationList?.length === 0 ? (
              <Box className="">
                <NoDataView
                  title="No Notification Found"
                  description="There is no Notification available at the moment."
                />
              </Box>
            ) : (
              <>
                <Box className="">{getCurrentContent()}</Box>
              </>
            )}
          </>
        )}

        {!loader &&
        notificationList &&
        notificationList?.length > 0 &&
        activeTab === 1 ? (
          <CustomOrgPagination
            currentPage={currentPage}
            totalCount={totalCount}
            rowsPerPage={rowsPerPage}
            onPageChange={onPageChange}
            OnRowPerPage={OnRowPerPage}
          />
        ) : !loader &&
          notificationList &&
          notificationList?.length > 0 &&
          activeTab === 2 ? (
          <CustomOrgPagination
            currentPage={currentPageper}
            totalCount={totalCountPer}
            rowsPerPage={rowsPerPPage}
            onPageChange={onPageChange}
            OnRowPerPage={OnRowPerPage}
          />
        ) : (
          <></>
        )}
      </Box>
    </>
  );
}
