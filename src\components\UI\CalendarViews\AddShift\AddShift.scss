.add-shift-container {
  min-width: 350px;
  .add-shift-header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
    padding: 10px 15px;
    .add-shift-header-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 10px;
      .update-shift-btn {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 10px;
      }
      .delete-shift-btn {
        cursor: pointer;
        height: var(--icon-size-md);
        width: var(--icon-size-md);
        color: var(--icon-bold-red-color);
        svg {
          height: var(--icon-size-md);
          width: var(--icon-size-md);
        }
      }
      .close-shift-tooltip {
        height: var(--icon-size-md);
        width: var(--icon-size-md);
        cursor: pointer;
        color: var(--icon-color-slate-gray);
      }
    }
  }
  .dropdown-wrapper {
    position: relative;
    .dropdown-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;
      padding: var(--field-padding);
      border-radius: var(--field-radius);
      font-size: var(--font-size-base);
      line-height: var(--line-height-xs);
      font-weight: var(--font-weight-medium);
      &:hover {
        background: var(--color-secondary);
        color: var(--text-color-black);
      }
      .arrow {
        height: var(--field-icon-size);
        width: var(--field-icon-size);
        color: var(--icon-color-slate-gray);
        margin-left: var(--spacing-xsm);
        svg {
          height: var(--field-icon-size);
          width: var(--field-icon-size);
        }
      }
    }
    .dropdown-content {
      position: absolute;
      width: 100%;
      min-width: 230px;
      max-width: 230px;
      background: var(--color-white);
      border: var(--normal-sec-border);
      border-radius: var(--border-radius-xs);
      box-shadow: var(--box-shadow-xs);
      z-index: 100;
      margin-top: var(--spacing-xsm);
      .search-input-wrapper {
        display: flex;
        align-items: center;
        background: var(--color-white);
        padding: var(--field-padding);
        border-bottom: var(--normal-sec-border);
        border-radius: 5px 5px 0px 0px;
        overflow: hidden;
        gap: 5px;
        .search-icon {
          color: var(--icon-color-slate-gray);
          height: var(--field-icon-size);
          width: var(--field-icon-size);
        }
        .search-input {
          width: 100%;
          border: none;
          background: none;
          outline: none;
          font-size: var(--font-size-sm);
          line-height: var(--line-height-xs);
          font-family: var(--font-family-primary);
        }
      }
      ul {
        list-style: none;
        padding: var(--spacing-xs);
        margin: var(--spacing-none);
        max-height: 150px;
        overflow-y: auto;
        li {
          cursor: pointer;
          padding: var(--spacing-xsm);
          font-size: var(--font-size-sm);
          line-height: var(--line-height-xs);
          border-radius: var(--border-radius-xs);
          margin-bottom: var(--spacing-xs);
          &:hover {
            background: var(--color-secondary);
            color: var(--text-color-black);
          }
          &.selected {
            background: var(--color-primary);
            color: var(--text-color-white);
          }
        }
      }
    }
  }
  .add-shift-details-container {
    padding: 10px 15px;
    .d-center-start {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: 10px;
    }
    // Shift Date
    .shift-date-row {
      margin-bottom: 10px;
      .shift-date-input-text {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        gap: 10px;
        padding: var(--field-padding);
        border-radius: var(--field-radius);
        font-size: var(--font-size-sm);
        line-height: var(--line-height-xs);
        &:hover {
          background: var(--color-secondary);
          color: var(--text-color-black);
        }
        :focus-within {
          outline: var(--field-border);
        }
      }
    }
    // Shift Time
    .shift-input-row {
      margin-bottom: 10px;
      .shift-input {
        border: none;
        background: none;
        outline: none;
        padding: var(--field-padding);
        border-radius: var(--field-radius);
        font-size: var(--font-size-sm);
        line-height: var(--line-height-xs);
        font-family: var(--font-family-primary);
        color: var(--text-color-black);
        &:hover {
          background: var(--color-secondary);
          color: var(--text-color-black);
        }
        &:focus {
          outline: var(--field-border);
        }
      }
      .shift-break-input-wrapper {
        .shift-input-break {
          outline: none;
          padding: var(--field-padding);
          border-radius: var(--field-radius);
          font-size: var(--font-size-sm);
          line-height: var(--line-height-xs);
          &:hover {
            background: var(--color-secondary);
            color: var(--text-color-black);
          }
          &:focus-within {
            outline: var(--field-border);
          }
          span {
            color: var(--text-color-slate-gray);
          }
          .break-input {
            width: 40px;
            border: none;
            background: none;
            outline: none;
            margin: 0px 5px;
            padding: 0px 5px;
            font-size: var(--font-size-sm);
            line-height: var(--line-height-xs);
            font-family: var(--font-family-primary);
            color: var(--text-color-black);
            // Chrome, Safari, Edge, Opera
            &::-webkit-outer-spin-button,
            &::-webkit-inner-spin-button {
              -webkit-appearance: none;
              margin: 0;
            }

            // Firefox
            &[type='number'] {
              -moz-appearance: textfield;
            }
          }
        }
      }
    }
    // Shift Role / Location / Department
    .shift-role-input-row {
      margin: 5px 0px;
      .dropdown-wrapper {
        width: 100%;
        font-size: var(--font-size-sm);
        line-height: var(--line-height-xs);
        border-radius: var(--field-radius);
        &:hover {
          background: var(--color-secondary);
          color: var(--text-color-black);
        }
        .dropdown-header {
          font-size: var(--font-size-sm);
          line-height: var(--line-height-xs);
          border-radius: var(--field-radius);
          font-weight: var(--font-weight-regular);
          justify-content: flex-start;
        }
        // .role-left-border {
        //   border-left: 3px solid var(--border-color-primary);
        //   margin-right: 5px;
        // }
      }
    }

    // Shift Toggle
    .shift-toggle-row {
      padding: 5px 0px;
      .publish-toggle-container {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        gap: 10px;
        padding: var(--field-padding);
        border-radius: var(--field-radius);
        font-size: var(--font-size-sm);
        line-height: var(--line-height-xs);
        &:hover {
          background: var(--color-secondary);
          color: var(--text-color-black);
        }
        .MuiFormControlLabel-root {
          margin-left: 0px;
          margin-right: 0px;
        }
        .publish-toggle-title {
          font-size: var(--font-size-sm);
          font-weight: var(--font-weight-regular);
        }
      }
    }
    // Shift Note
    .shift-note-row {
      padding: 5px 0px;
      .shift-note-input-wrapper {
        width: 100%;
        .shift-note-input {
          width: 100%;
          border: none;
          background: none;
          outline: none;
          padding: var(--field-padding);
          border-radius: var(--field-radius);
          font-size: var(--font-size-sm);
          line-height: var(--line-height-xs);
          font-family: var(--font-family-primary);
          color: var(--text-color-black);
          resize: none;
          &:hover {
            background: var(--color-secondary);
            color: var(--text-color-black);
          }
          &:focus {
            outline: var(--field-border);
          }
        }
      }
    }
    // Shift Note Char Count
    .shift-note-char-count {
      padding: 5px 0px;
      .shift-note-visibility-container {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0px 10px;
        .shift-note-visibility-text {
          font-size: var(--font-size-xs);
          color: var(--text-color-slate-gray);
          font-weight: var(--font-weight-regular);
        }
        .char-count {
          font-size: var(--font-size-xs);
          color: var(--text-color-slate-gray);
          font-weight: var(--font-weight-regular);
        }
      }
    }
  }
  .icon {
    height: var(--icon-size-sm);
    width: var(--icon-size-sm);
    color: var(--icon-color-slate-gray);
    svg {
      height: var(--icon-size-sm);
      width: var(--icon-size-sm);
    }
  }
  .error-message {
    color: var(--text-error);
    font-size: 12px;
    margin-top: 4px;
    padding: 0px 10px;
  }
  .error-message-row {
    display: flex;
    align-items: flex-start;
    margin: 5px 0px;
  }
}
