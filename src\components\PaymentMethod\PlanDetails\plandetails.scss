@import '@/styles/variable.scss';

.plan-details-container {
  display: flex;
  flex-wrap: wrap;
  padding: var(--spacing-xxl);
  gap: var(--spacing-xxl);
  @media (max-width: 768px) {
    padding: var(--spacing-none);
  }
  .plan-card {
    position: relative;
    border: var(--border-width-sm) var(--border-style-solid)
      var(--border-color-primary);
    border-radius: var(--border-radius-lg);
    max-width: 500px;
    width: 100%;
    padding: var(--spacing-xxl);
    box-shadow: var(--box-shadow-xs);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
    .active-plan {
      position: absolute;
      font-family: var(--font-family-primary);
      top: var(--spacing-md);
      right: var(--spacing-md);
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-semibold);
      padding: var(--spacing-xs) var(--spacing-md);
      border-radius: var(--border-radius-lg);
      text-transform: capitalize;
      &.active {
        background-color: var(--color-success-opacity);
        color: var(--text-green);
      }
      &.inactive {
        background-color: var(--color-danger-opacity);
        color: var(--text-color-danger);
      }
      &.queued {
        background-color: var(--color-muted-mustard-opacity);
        color: var(--text-muted-mustard);
      }
    }
    .plan-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      margin-top: var(--spacing-md);
      @media (max-width: 600px) {
        margin-top: var(--spacing-xl);
      }
      .plan-name {
        font-family: var(--font-family-primary);
        font-size: var(--font-size-xl);
        line-height: var(--line-height-xl);
        font-weight: var(--font-weight-semibold);
        color: var(--text-color-black);
      }
      .plan-type {
        font-family: var(--font-family-primary);
        font-size: var(--font-size-sm);
        line-height: var(--line-height-sm);
        font-weight: var(--font-weight-medium);
        color: var(--text-color-primary);
        text-transform: uppercase;
        margin-bottom: var(--spacing-sm);
        .plan-sub-type {
          font-size: var(--font-size-xs);
          line-height: var(--line-height-xs);
          font-weight: var(--font-weight-regular);
          text-transform: capitalize;
        }
      }
      .plan-description {
        font-family: var(--font-family-primary);
        font-size: var(--font-size-sm);
        line-height: var(--line-height-sm);
        color: var(--text-color-slate-gray);
      }
    }
    .plan-price {
      display: flex;
      justify-content: center;
      .price {
        font-family: var(--font-family-primary);
        font-size: var(--font-size-2xl);
        font-weight: var(--font-weight-semibold);
        color: var(--text-color-black);
        text-align: center;
        .duration {
          font-size: var(--font-size-sm);
          line-height: var(--line-height-sm);
          font-weight: var(--font-weight-medium);
          color: var(--text-dark);
        }
      }
    }
    .renew-payment-text {
      color: var(--text-color-danger);
    }
    .next-payment-text {
      color: var(--text-color-black);
    }
    .bold-date-text {
      font-weight: var(--font-weight-semibold);
    }
    .employees-range {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      margin-bottom: var(--spacing-md);
      .employees-icon {
        width: var(--icon-size-md);
        height: var(--icon-size-md);
        fill: var(--icon-color-primary);
      }
      .employees {
        display: flex;
        align-items: center;
        font-family: var(--font-family-primary);
        font-size: var(--font-size-sm);
        line-height: var(--line-height-sm);
        color: var(--text-color-black);
        &.storage {
          text-transform: uppercase;
        }
      }
    }
    .plan-remaining-data {
      font-family: var(--font-family-primary);
      font-size: var(--font-size-sm);
      line-height: var(--line-height-sm);
      color: var(--text-color-black);
      font-weight: var(--font-weight-semibold);
      .remaining {
        font-weight: var(--font-weight-regular);
      }
    }
    .plan-benefits {
      padding: var(--spacing-lg) var(--spacing-none);
      border-top: var(--field-border);
      .plan-emp-benefites {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        gap: var(--spacing-xxl);
      }

      // border-bottom: var(--field-border);
      .benefits-title {
        font-family: var(--font-family-primary);
        font-size: var(--font-size-md);
        font-weight: var(--font-weight-semibold);
        color: var(--text-color-black);
        margin-bottom: var(--spacing-lg);
      }
      .benefit-item {
        font-family: var(--font-family-primary);
        p {
          margin-bottom: var(--spacing-sm);
        }
        // display: flex;
        // align-items: center;
        // gap: var(--spacing-sm);
        // margin-bottom: var(--spacing-md);
        // .check-icon {
        //   fill: var(--color-success);
        //   width: var(--icon-size-sm);
        //   height: var(--icon-size-sm);
        // }
        // .benefit-text {
        //   font-family: var(--font-family-primary);
        //   font-size: var(--font-size-sm);
        //   color: var(--text-color-slate-gray);
        // }
      }
    }
    .cancel-button {
      color: var(--text-color-danger);
    }
    @media (max-width: 768px) {
      .plan-card {
        max-width: 100%;
      }
    }
  }

  @media (max-width: 600px) {
    // padding: var(--spacing-lg);
    .plan-card {
      max-width: 100%;
      padding: var(--spacing-lg);
    }
  }
}

.plan-storage-wrap {
  .plan-remaining-data {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-sm);
    color: var(--text-color-black);
    font-weight: var(--font-weight-semibold);
  }
  .storage {
    text-transform: uppercase;
  }
  .plan-remaining-data.remaining {
    font-weight: var(--font-weight-regular) !important;
  }
}
.plan-storage-wrap.plan-card {
  border: 0 !important;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.16);
}
