'use client';
import React, { useState, useMemo, useEffect } from 'react';
// import { Text } from 'intergalactic/typography';
import Select from 'intergalactic/select';
import { Box } from 'intergalactic/flex-box';
import { FilterTrigger } from 'intergalactic/base-trigger';
import Ellipsis from 'intergalactic/ellipsis';
// import ExpandMoreOutlinedIcon from '@mui/icons-material/ExpandMoreOutlined';
import { DropdownIcon } from '@/helper/common/images';
import './timeperiods.scss';

export default function TimePeriodFilter({
  selectedOption,
  setSelectedOption,
  TimePeriodList,
}) {
  const [visible, setVisible] = useState(false);
  // const [filter, setFilter] = useState('');
  const filter = '';
  // const [loading, setLoading] = useState(false);

  useEffect(() => {
    setSelectedOption('none');
  }, []);

  // Filters the time period options based on the user's input and returns the filtered list
  const filteredOptions = useMemo(() => {
    if (filter) {
      return TimePeriodList?.filter((option) =>
        option?.label.toLowerCase().includes(filter.toLowerCase())
      );
    }
    return TimePeriodList;
  }, [TimePeriodList]);

  // Handles visibility changes of the Select dropdown and simulates a loading state
  const handleChangeVisible = (visible) => {
    setVisible(visible);
    // if (visible) {
    //   setLoading(true);
    //   setTimeout(() => setLoading(false), 1000);
    // }
  };

  // Updates the selected time period option when a user selects a time period
  const handleSelectTimePeriod = (periodId) => {
    setSelectedOption(periodId);
  };

  return (
    <Select
      placeholder="Time Period"
      value={selectedOption || ''}
      onVisibleChange={handleChangeVisible}
      visible={visible}
      onChange={setSelectedOption}
    >
      <Select.Trigger className="intergalactic-multiselect" tag={FilterTrigger}>
        <Box className="d-flex justify-space-between w100">
          <Box className="d-flex align-center gap-5">
            <span className="branches-wrap">Time Period :</span>
            <span>
              {
                TimePeriodList?.find(
                  (period) => period?.value === selectedOption
                )?.label
              }
            </span>
          </Box>
          <Box className="d-flex align-center gap-5">
            <DropdownIcon
              className="down-arrow-wrap"
              // fontSize="small"
            />
          </Box>
        </Box>
      </Select.Trigger>
      <Select.Popper
        className="select-input-wrap"
        aria-label="Options with search"
      >
        {/* {loading && (
          <Text tag="div" m="10px 8px" size={200} use="secondary">
            Loading...
          </Text>
        )} */}
        {/* {!loading && ( */}
        <>
          <Select.List id="search-list">
            {filteredOptions.map((option, index) => (
              <Select.Option
                key={option?.value}
                value={option?.value}
                id={`option-${index}`}
                onClick={() => handleSelectTimePeriod(option?.value)}
                aria-selected={selectedOption === option?.value}
              >
                <Ellipsis placement="right">
                  <Ellipsis.Content>
                    <span>{option?.label}</span>
                  </Ellipsis.Content>
                </Ellipsis>
              </Select.Option>
            ))}
            {!filteredOptions?.length && (
              <Select.OptionHint>No time periods found</Select.OptionHint>
            )}
          </Select.List>
        </>
        {/* )} */}
      </Select.Popper>
    </Select>
  );
}
