import React from 'react';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker'; // Use DateTimePicker instead
import { Box, InputLabel } from '@mui/material';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import '../datepicker/datepicker.scss';

const CustomDateTimePicker = ({
  onChange,
  label,
  value,
  error,
  helperText,
  format,
  years,
  months,
  days,
  className,
  yearMonth,
  disablePast,
  disabled,
  OpenDialog,
  CloseDialog,
  disableFuture,
  minDate,
  maxDate,
  onBlur,
}) => {
  return (
    <>
      <Box className={className ? `${className} date-wrap` : 'date-wrap'}>
        <LocalizationProvider dateAdapter={AdapterDayjs}>
          <InputLabel className="input-label input-label-date">
            {label}
          </InputLabel>
          <DateTimePicker
            InputLabelProps={{
              shrink: true,
            }}
            disablePast={disablePast}
            disableFuture={disableFuture}
            format={format || 'DD/M/YYYY HH:mm A'} // Include time format here
            onOpen={OpenDialog}
            onClose={CloseDialog}
            value={value || null}
            onChange={onChange}
            onBlur={onBlur}
            views={
              (yearMonth && ['month', 'year']) ||
              (years && ['year']) ||
              (months && ['month']) ||
              (days && ['day'])
            }
            minDate={minDate}
            maxDate={maxDate}
            slots={{ openPickerIcon: CalendarMonthIcon }}
            slotProps={{
              textField: {
                placeholder: 'Select date and time',
              },
            }}
            disabled={disabled}
          />
        </LocalizationProvider>
      </Box>
    </>
  );
};

export default CustomDateTimePicker;
