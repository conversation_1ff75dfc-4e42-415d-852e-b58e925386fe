'use client';

import React, { useEffect, useState } from 'react';
import { Box, CircularProgress, Tooltip, Typography } from '@mui/material';
import CustomButton from '@/components/UI/button';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import TuneIcon from '@mui/icons-material/Tune';
import moment from 'moment';
import SearchBar from '@/components/UI/SearchBar';
import DialogBox from '@/components/UI/Modalbox';
import CustomSelect from '@/components/UI/selectbox';
import { identifiers } from '@/helper/constants/identifier';
import CustomPagination from '@/components/UI/pagination';
import EditIcon from '@/components/ActionIcons/EditIcon';
import DeleteIcon from '@/components/ActionIcons/DeleteIcon';
import InfoIcon from '@mui/icons-material/Info';
import { useRouter } from 'next/navigation';
import './leavepolicy.scss';

const LeavePolicy = () => {
  const router = useRouter();
  const [loader, setLoader] = useState(false);
  const [filter, setFilter] = useState(false);
  const [leavePolicyData, setLeavePolicyData] = useState([]);
  const [searchValue, setSearchValue] = useState('');
  const [filterData, setFilterData] = useState({ status: '' });
  const [filterDataApplied, setFilterDataApplied] = useState({ status: '' });
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  // GET LEAVE POLICY LIST
  const getLeavePloicyList = async (page, searchValue, filter, Rpp) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_LEAVE_POLICY +
          `?search=${searchValue}&size=${
            Rpp ? Rpp : rowsPerPage
          }&page=${page}&status=${filter?.status}`
      );

      if (status === 200) {
        const leavePolicyList = data?.data;
        setLeavePolicyData(leavePolicyList);
        setTotalCount(data?.total);
        setLoader(false);
      }
    } catch (error) {
      setLoader(false);
      setLeavePolicyData([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const onPageChange = (newPage) => {
    setCurrentPage(newPage);
    getLeavePloicyList(newPage, searchValue, filterDataApplied);
  };
  const OnRowPerPage = (newPage) => {
    setRowsPerPage(newPage);
    setCurrentPage(1);
    getLeavePloicyList(1, searchValue, filterDataApplied, newPage);
  };
  const handleFilterData = (type) => {
    setFilter(false);
    if (type === 'apply') {
      getLeavePloicyList(1, searchValue, filterData);
      setFilterDataApplied(filterData);
    } else {
      const clearFilter = {
        status: '',
      };
      setFilterData(clearFilter);
      setFilterDataApplied(clearFilter);
      getLeavePloicyList(1, searchValue, clearFilter);
    }
  };
  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      handleFilterData('apply');
    }
  };
  useEffect(() => {
    getLeavePloicyList(1, searchValue, filterDataApplied);
  }, []);

  const handleDelete = async (e, item) => {
    e.stopPropagation();

    try {
      setLoader(true);
      const { status, data } = await axiosInstance.delete(
        URLS?.LEAVE_POLICY + `/${item?.id}`
      );

      if (status === 200) {
        if (data.status) {
          setApiMessage('success', data?.message);
          getLeavePloicyList(currentPage, searchValue, filterDataApplied);
        } else {
          setApiMessage('error', data?.message);
          getLeavePloicyList(currentPage, searchValue, filterDataApplied);
        }
      } else {
        setApiMessage('error', data?.message);
      }
      setLoader(false);
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  return (
    <>
      <Box className="page-container leave-policy-container">
        <Box className="search-filter-section">
          <SearchBar
            searchclass="staff-leave-search"
            setSearchValue={setSearchValue}
            onKeyPress={handleKeyPress}
          />
          <TuneIcon
            onClick={() => {
              setFilter(!filter);
            }}
            className="filter-icon"
          />
          <CustomButton
            variant="contained"
            background="#39596e"
            backgroundhover="#FFFFFF"
            colorhover="#000000"
            className="p14"
            fontWeight="600"
            title="Apply filter"
            fullWidth={false}
            onClick={() => handleFilterData('apply')}
          />
          <CustomButton
            variant="contained"
            background="#39596e"
            backgroundhover="#FFFFFF"
            colorhover="#000000"
            className="p14"
            fontWeight="600"
            title="Add Policy"
            fullWidth={false}
            onClick={() => {
              router.push('/create-policy');
            }}
          />
        </Box>
        <Box className="table-container">
          {loader ? (
            <Box className="content-loader pt16">
              <CircularProgress className="loader" color="inherit" />
            </Box>
          ) : leavePolicyData && leavePolicyData?.length === 0 ? (
            <Box className="pb16">
              <Typography className="text-align h6">No data found</Typography>
            </Box>
          ) : (
            <Box className="leave-policy-wrapper">
              {leavePolicyData?.map((item, i) => {
                const totalDays = item?.leave_types?.reduce(
                  (acc, leaveType) => acc + leaveType?.days,
                  0
                );
                const isUnlimited = item?.leave_types?.find(
                  (f) => f?.has_unlimited
                );

                return (
                  <Box
                    className="leave-policy-wrap h100 cursor-pointer"
                    key={i}
                    onClick={() => router.push(`/edit-policy/${item?.id}`)}
                  >
                    <Box className="title-section">
                      <Box className="d-flex align-center justify-space-between text-capital">
                        {item?.status === 'inactive' ? (
                          <Typography className="p12  ongoing fw600">
                            {item?.status}
                          </Typography>
                        ) : item?.status === 'active' ? (
                          <Typography className="p12 success fw600">
                            {item?.status}
                          </Typography>
                        ) : (
                          '-'
                        )}
                        <Box className="d-flex align-center justify-start gap-sm">
                          <Tooltip
                            title="Edit"
                            arrow
                            className="action-tooltip cursor-pointer"
                          >
                            <Box>
                              <EditIcon />
                            </Box>
                          </Tooltip>
                          <Tooltip
                            title="Delete"
                            arrow
                            className="action-tooltip cursor-pointer"
                          >
                            <Box onClick={(e) => handleDelete(e, item)}>
                              <DeleteIcon />
                            </Box>
                          </Tooltip>
                        </Box>
                      </Box>
                      <Box className="d-flex align-center pt8">
                        <Typography className="p14 fw400">
                          <span className="fw600">Name : </span>
                          <span>{item?.name}</span>
                        </Typography>
                      </Box>
                      <Box className="d-flex align-center pt8">
                        <Typography className="p14 fw400 d-flex align-center justify-start">
                          <span className="fw600">No of leave : </span>
                          <span className="pl4  d-flex align-center">
                            {item?.leave_types?.length === 1 && isUnlimited ? (
                              <span className="pl4 p20 d-flex align-center">
                                &infin;
                              </span>
                            ) : isUnlimited ? (
                              <>
                                {totalDays}
                                <span className="pl4 p14 d-flex align-center">
                                  {'+ '}
                                  <span className="pl4 p20 d-flex align-center">
                                    &infin;
                                  </span>
                                </span>
                              </>
                            ) : (
                              <>{totalDays}</>
                            )}
                          </span>
                          <Tooltip
                            classes={{
                              tooltip: 'sidebar-list-tooltip',
                            }}
                            title={
                              <Box>
                                {item?.leave_types?.map((item) => {
                                  return (
                                    <p className="d-flex align-center">
                                      {item?.has_unlimited ? (
                                        <>
                                          {' '}
                                          {item?.name} ({' '}
                                          <span className="p16 d-flex align-center">
                                            &infin;
                                          </span>{' '}
                                          )
                                        </>
                                      ) : (
                                        <>
                                          {' '}
                                          {item?.name} ({item?.days})
                                        </>
                                      )}
                                    </p>
                                  );
                                })}
                              </Box>
                            }
                          >
                            <InfoIcon className="info-icon" />
                          </Tooltip>
                        </Typography>
                      </Box>
                      <Box className="d-flex align-center pt8">
                        <Typography className="p14 fw400">
                          <span className="fw600">Remark : </span>
                          <span>{item?.remark}</span>
                        </Typography>
                      </Box>
                      <Box className="d-flex align-center pt8">
                        <Typography className="p14 fw400">
                          <span className="fw600">Update By : </span>
                          <span>{item?.created_by?.user_full_name}</span>
                        </Typography>
                      </Box>
                      <Box className="d-flex align-center pt8">
                        <Typography className="p14 fw400">
                          <span className="fw600">Created Date : </span>
                          <span>
                            {moment
                              .utc(item?.createdAt)
                              .local()
                              .format('DD/MM/YYYY hh:mm A')}
                          </span>
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                );
              })}
            </Box>
          )}
          <CustomPagination
            currentPage={currentPage}
            totalCount={totalCount}
            rowsPerPage={rowsPerPage}
            onPageChange={onPageChange}
            OnRowPerPage={OnRowPerPage}
          />
        </Box>
      </Box>
      <DialogBox
        open={filter}
        handleClose={() => {
          setFilter(!filter);
        }}
        title="Leave policy filter"
        content={
          <>
            <Box className="staff-filter">
              <Box className="pt32">
                <Box className="select-box pb32">
                  <CustomSelect
                    placeholder="Status"
                    options={identifiers?.CARD_STATUS}
                    value={filterData?.status}
                    onChange={(e) => {
                      setFilterData({
                        ...filterData,
                        status: e.target.value,
                      });
                    }}
                    label={<span>Status</span>}
                  />
                </Box>
                <Box className="create-cancel-button">
                  <CustomButton
                    fullWidth
                    className="p12 secondary-button"
                    type="submit"
                    fontWeight="600"
                    variant="contained"
                    background="#FFFFFF"
                    backgroundhover="#39596e"
                    colorhover="#FFFFFF"
                    title="Cancel"
                    onClick={() => {
                      handleFilterData('cancel');
                    }}
                  />
                  <CustomButton
                    fullWidth
                    className="p12"
                    type="submit"
                    fontWeight="600"
                    variant="contained"
                    background="#39596e"
                    backgroundhover="#FFFFFF"
                    colorhover="#000000"
                    title="Apply"
                    onClick={() => {
                      handleFilterData('apply');
                    }}
                  />
                </Box>
              </Box>
            </Box>
          </>
        }
      />
    </>
  );
};

export default LeavePolicy;
