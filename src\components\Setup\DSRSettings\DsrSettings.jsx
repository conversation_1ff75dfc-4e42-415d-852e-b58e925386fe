'use client';
import React, { useEffect, useState } from 'react';
import { URLS } from '@/helper/constants/urls';
import axiosInstance from '@/helper/axios/axiosInstance';
import { setApiMessage } from '@/helper/common/commonFunctions';
import DSRCategories from './DSRCategories';

export default function DsrSettingsPage() {
  const [paymentCateList, setPaymentList] = useState([]);
  const [addDSR, setAddDSR] = useState('');
  const [loader, setLoader] = useState(true);

  const getCategoriesPaymentList = async (isUpdate) => {
    !isUpdate && setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_PAYMENT_ALL_LIST + `?search=${''}&page=${1}&size=${''}`
      );
      if (status === 200 || status === 304) {
        setLoader(false);
        setPaymentList(data?.data);
      }
    } catch (error) {
      setLoader(false);
      setPaymentList([]);

      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // delete main category item
  const deleteMainCategory = async (id) => {
    try {
      // setLoader(true);
      const { status, data } = await axiosInstance.delete(
        URLS?.DELETE_DSR_PAYMENT + id
      );
      if (status === 200) {
        if (data.status) {
          getCategoriesPaymentList(true);
          setAddDSR('');
          setApiMessage('success', data?.message);
        } else {
          setApiMessage('error', data?.message);
        }
      } else {
        setApiMessage('error', data?.message);
      }
      setLoader(false);
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // delete sub category item
  const deletesubCategory = async (id) => {
    try {
      // setLoader(true);
      const { status, data } = await axiosInstance.delete(
        URLS?.DELETE_DSR_SUB_PAYMENT + id
      );
      if (status === 200) {
        if (data.status) {
          getCategoriesPaymentList(true);
          setApiMessage('success', data?.message);
        } else {
          setApiMessage('error', data?.message);
        }
      } else {
        setApiMessage('error', data?.message);
      }
      setLoader(false);
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const handleAddPayment = async (categories, isUpdate, updateId) => {
    const requestData = {
      payment_type_title: categories?.title,
      payment_type_status: categories?.status,
      payment_type_usage: categories?.type,
      has_weekly_use: categories?.isWeekly,
      has_include_amount: categories?.has_include_amount
        ? categories?.has_include_amount
        : false,
      has_field_currency: categories?.has_field_currency ? true : false,
    };
    const ApiUrl = isUpdate
      ? URLS.UPDATE_DSR_PAYMENT + updateId
      : URLS.CREATE_DSR_PAYMENT;
    const method = isUpdate ? 'put' : 'post';
    try {
      const { status, data } = await axiosInstance[method](ApiUrl, requestData);
      if (status === 200) {
        if (data.status) {
          getCategoriesPaymentList(true);
          setAddDSR('');
          setApiMessage('success', data?.message);
        } else {
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const handleAddSubPayment = async (categories, isUpdate, updateId) => {
    const MultipleList =
      categories?.fieldArray?.length > 0 &&
      categories?.fieldArray?.map((item) => {
        return {
          ...(item?.id && { field_id: item?.id }),
          field_name: item?.name,
          field_type: item?.type === 'number' ? 'integer' : item?.type,
          field_limit: item?.limit ? parseFloat(item?.limit) : null,
        };
      });
    const requestData = {
      payment_type_category_title: categories?.fieldLabel,
      payment_type_category_status: categories?.fieldstatus,
      payment_type_category_pattern: categories?.fieldtype,
      payment_type_category_remarks: categories?.fieldRemark,
      ...(categories?.fieldtype === 'single' && {
        payment_type_category_data_type: 'integer',
      }),
      ...(categories?.fieldtype === 'multiple' &&
        MultipleList &&
        MultipleList?.length > 0 && {
          payment_type_category_fields: MultipleList,
        }),
    };
    const ApiUrl = isUpdate
      ? URLS.UPDATE_DSR_SUB_PAYMENT + updateId
      : URLS.CREATE_DSR_SUB_PAYMENT + updateId;
    const method = isUpdate ? 'put' : 'post';
    try {
      const { status, data } = await axiosInstance[method](ApiUrl, requestData);
      if (status === 200) {
        if (data.status) {
          getCategoriesPaymentList(true);
          setAddDSR('');
          setApiMessage('success', data?.message);
        } else {
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // Reorder main category
  const handleReorderPayment = async (index, id) => {
    const requestData = {
      payment_type_id: id,
      order: Number(index) + 1,
    };
    try {
      const { status } = await axiosInstance.put(
        URLS?.PAYMENT_LIST_REORDER,
        requestData
      );
      if (status === 200) {
        getCategoriesPaymentList(true);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // Reorder sub category list
  const handleReorderSubPayment = async (index, id) => {
    const requestData = {
      payment_type_category_id: id,
      order: Number(index) + 1,
    };
    try {
      const { status } = await axiosInstance.put(
        URLS?.PAYMENT_SUB_LIST_REORDER,
        requestData
      );
      if (status === 200) {
        getCategoriesPaymentList(true);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  useEffect(() => {
    getCategoriesPaymentList(false);
  }, []);

  return (
    <>
      <DSRCategories
        loader={loader}
        paymentCateList={paymentCateList}
        handleAddPayment={handleAddPayment}
        handleAddSubPayment={handleAddSubPayment}
        handleReorderPayment={handleReorderPayment}
        deleteMainCategory={deleteMainCategory}
        handleReorderSubPayment={handleReorderSubPayment}
        deletesubCategory={deletesubCategory}
        addDSR={addDSR}
        setAddDSR={setAddDSR}
      />
    </>
  );
}
