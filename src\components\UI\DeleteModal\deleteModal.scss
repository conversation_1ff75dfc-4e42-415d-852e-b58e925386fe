.delete-modal {
  .MuiPaper-root {
    padding: var(--spacing-xl) !important;
    .dialog-title-wrap {
      justify-content: center;
      .MuiSvgIcon-root {
        position: absolute;
        right: 15px;
        top: 15px;
        width: var(--icon-size-md);
        height: var(--icon-size-md);
      }
    }
    .delete-modal-divider {
      display: none;
    }
    .delete-modal-wrap {
      .delete-text {
        font-family: var(--font-family-primary);
        font-size: var(--font-size-base);
        line-height: var(--line-height-base);
        margin-bottom: var(--spacing-2xl);
        margin-top: var(--spacing-2xl);
        color: var(--text-color-slate-gray);
        text-align: center;
      }
      .delete-action {
        display: flex;
        justify-content: center;
        gap: var(--spacing-md);
        .cancel-button {
          background: var(--btn-color-dark);
          color: var(--btn-text-color-white);
        }
        .delete-button {
          background: var(--btn-color-danger);
          color: var(--btn-text-color-white);
          border-color: var(--btn-border-color-danger);
        }
      }
    }
  }
}
