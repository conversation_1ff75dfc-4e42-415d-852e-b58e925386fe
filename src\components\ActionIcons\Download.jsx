import React from 'react';

const DownloadIcon = ({ onClick }) => {
  return (
    <div className="action-icon d-flex" onClick={onClick}>
      <Download />
    </div>
  );
};

export default DownloadIcon;

const Download = (props) => {
  return (
    <svg
      {...props}
      height="21"
      viewBox="0 0 21 21"
      width="21"
      xmlns="http://www.w3.org/2000/svg"
      className="lock-icon"
    >
      <g
        fill="none"
        fill-rule="evenodd"
        stroke="currentColor"
        stroke-linecap="round"
        stroke-linejoin="round"
        transform="translate(4 3)"
      >
        <path d="m2.5 7.5 4 4.232 4-4.191" />
        <path d="m6.5.5v11" />
        <path d="m.5 14.5h12" />
      </g>
    </svg>
  );
};
