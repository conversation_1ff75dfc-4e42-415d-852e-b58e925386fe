'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { fetchFromStorage } from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import { checkOrganizationRole } from '@/helper/common/commonFunctions';
import PreLoader from '@/components/UI/Loader';

export default function Home() {
  const router = useRouter();

  useEffect(() => {
    if (checkOrganizationRole('org_master')) {
      router?.push('/org/organization');
    } else if (checkOrganizationRole('super_admin')) {
      router?.push('/sorg/organization');
    } else if (checkOrganizationRole('staff')) {
      router?.push('/myprofile');
    } else if (fetchFromStorage(identifiers?.AUTH_DATA)) {
      router.push('/chart-dashboard');
    } else {
      router.push('/login');
    }
  }, []);

  return (
    <>
      <PreLoader />
    </>
  );
}
