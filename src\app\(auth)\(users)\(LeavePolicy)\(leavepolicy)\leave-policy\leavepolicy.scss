@import '@/styles/variable.scss';

.leave-policy-container {
  .search-filter-section {
    display: flex;
    column-gap: 25px;
    align-items: center;
    .staff-leave-search {
      max-width: calc(100% - 350px) !important;
    }
    .filter-icon {
      border: 1px solid $color-Black;
      border-radius: 3px;
      width: 25px;
      height: 25px;
      cursor: pointer;
    }
    @media (max-width: 799px) {
      column-gap: 15px;
      .staff-leave-search {
        max-width: calc(100% - 320px) !important;
      }
    }
    @media (max-width: 599px) {
      display: flex;
      flex-wrap: wrap;
      row-gap: 25px;
      .staff-leave-search {
        width: 85% !important;
        max-width: 85% !important;
      }
      .filter-icon {
        max-width: 15%;
      }
      button {
        width: 45%;
      }
    }
    @media (max-width: 599px) {
      row-gap: 15px;
      button {
        width: 100%;
      }
    }
  }
  .leave-policy-wrapper {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 22px;
    margin-bottom: 20px;
    @media (max-width: 425px) {
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
    .leave-policy-wrap {
      background: $color-White;
      display: flex;
      flex-direction: column;
      box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
      padding: 12px 12px;
      border-radius: 8px;
      .title-section {
        .title {
          text-align: start;
          width: calc(100% - 100px);
        }
        .info-icon {
          height: 15px;
          width: 15px;
          margin-left: 5px;
          fill: gray !important;
        }
      }
    }
  }
}
.create-policy {
  background-color: $color-White;
  padding: 26px 15px;
  border-radius: 12px;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.05);
  .display-grid {
    display: grid;
    grid-template-columns: 32% 32% 32%;
    column-gap: 15px;
    row-gap: 15px;
    @media (max-width: 899px) {
      display: block;
    }
  }
  .select-wrap {
    .MuiInputBase-root {
      min-height: 48px;
      margin-top: 2px;
      .MuiSelect-select {
        margin-top: 0px;
      }
    }
    fieldset {
      margin-top: 0px;
    }
    .MuiOutlinedInput-notchedOutline {
      top: 0;
    }
  }
  .button-unlimited {
    svg {
      fill: $color-green;
    }
  }
}
