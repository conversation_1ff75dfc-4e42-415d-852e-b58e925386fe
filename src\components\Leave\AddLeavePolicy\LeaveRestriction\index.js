import React, { useEffect, useContext } from 'react';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { Box, Tooltip, Typography } from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import AuthContext from '@/helper/authcontext';
import CustomSelect from '@/components/UI/CustomSelect';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomCheckbox from '@/components/UI/CustomCheckbox';
import './leaveresriction.scss';

export default function LeaveRestriction({
  setLeavePolicyDetails,
  formikRefRestriction,
  leavePolicyDetails,
  setLeavePolicySubmit,
  leavePolicySubmit,
  LeaveStep3Payload,
  leavePolicyBack,
  setLeavePolicyBack,
}) {
  const timePeriodOptions = [
    { label: 'Days', value: 'days' },
    { label: 'Weekly', value: 'weekly' },
    { label: 'Monthly', value: 'monthly' },
    { label: 'Quarterly', value: 'quarterly' },
    { label: 'Yearly', value: 'yearly' },
  ];
  const { authState } = useContext(AuthContext);
  const isHour =
    authState?.generalSeetings?.leave_period_type &&
    authState?.generalSeetings?.leave_period_type === 'hour'
      ? true
      : false;

  const { leaveRestriction } = leavePolicyDetails || {};
  const {
    has_maximum_consecutive,
    maximum_consecutive_count,
    has_allow_gap_between_leave,
    gap_between_leave_application,
    emp_can_apply_in_notice_period,
    extend_notice_period,
    maximum_leave_allowed_for_period_count,
    maximum_leave_allowed_for_period_type,
    allow_view_for_same_leave_type,
  } = leaveRestriction || {};

  const validationSchema = Yup.object().shape({
    maxConsecutiveNoDays: Yup.lazy((value, context) => {
      const past = context.parent.maxConsecutiveDays;
      return past === true
        ? Yup.string()
            .required('This field is required')
            .test(
              'not-zero',
              'Consecutive days cannot be 0',
              (val) => !/^[0]+$/.test(val)
            )
        : Yup.string().nullable().notRequired();
    }),
    minGapBetweenApplications: Yup.lazy((value, context) => {
      const next = context.parent.minGapInDays;
      return next === true
        ? Yup.string()
            .required('This field is required')
            .test(
              'not-zero',
              'Gap between applications cannot be 0',
              (val) => !/^[0]+$/.test(val)
            )
        : Yup.string().nullable().notRequired();
    }),
    maxApplications: Yup.lazy((value, context) => {
      const toBeApplied = context.parent.allowMaxApplications;
      return toBeApplied === true
        ? Yup.string()
            .required('This field is required')
            .test(
              'not-zero',
              'Applications cannot be 0',
              (val) => !/^[0]+$/.test(val)
            )
        : Yup.string().nullable().notRequired();
    }),
  });

  useEffect(() => {
    setLeavePolicySubmit({
      ...leavePolicySubmit,
      leaveRestriction: false,
    });
    setLeavePolicyBack({
      ...leavePolicyBack,
      leaveRestriction: false,
    });
  }, []);

  const Inputype = (e) => {
    let value = e.target.value;
    if (isHour) {
      {
        // Remove non-numeric characters
        if (value === '' || /^\d*\.?\d{0,2}$/.test(value)) {
          e.target.value = value;
        } else {
          e.target.value = value.slice(0, -1);
        }
      }
    } else {
      {
        e.target.value = e.target.value.replace(/[^0-9]/g, ''); // Remove non-numeric characters
      }
    }
  };

  return (
    <Formik
      innerRef={formikRefRestriction}
      enableReinitialize
      initialValues={{
        maxConsecutiveDays: has_maximum_consecutive || false,
        maxConsecutiveNoDays: maximum_consecutive_count || 0,
        minGapInDays: has_allow_gap_between_leave || false,
        minGapBetweenApplications: gap_between_leave_application || 0,
        employeeCanConsume: emp_can_apply_in_notice_period || false,
        employeeCanExtend: extend_notice_period || false,
        maxApplications: maximum_leave_allowed_for_period_count || 0,
        allowMaxApplications:
          maximum_leave_allowed_for_period_count !== undefined &&
          maximum_leave_allowed_for_period_count !== 0
            ? true
            : false,
        employeeTimePeriod: maximum_leave_allowed_for_period_type || 'weekly',
        effectiveFrom: '',
        allowEmpSameLeave:
          allow_view_for_same_leave_type !== undefined
            ? allow_view_for_same_leave_type
            : true,
      }}
      validationSchema={validationSchema}
      onSubmit={(values) => {
        setLeavePolicyDetails((prevState) => ({
          ...prevState,
          leaveRestriction: LeaveStep3Payload(values),
        }));
      }}
    >
      {({
        values,
        errors,
        touched,
        handleChange,
        setFieldValue,
        handleBlur,
      }) => (
        <Form>
          <Box className="leave-restriction-wrap">
            <Box className="d-flex align-center gap-sm mt16 flex-wrap">
              <Box>
                <CustomCheckbox
                  checked={values?.maxConsecutiveDays}
                  onChange={(e) => {
                    setFieldValue('maxConsecutiveDays', e?.target?.checked);
                    setFieldValue('maxConsecutiveNoDays', 0);
                    setLeavePolicySubmit({
                      ...leavePolicySubmit,
                      leaveRestriction: false,
                    });
                    setLeavePolicyBack({
                      ...leavePolicyBack,
                      leaveRestriction: false,
                    });
                  }}
                  disableRipple
                  label={
                    <Typography className="sub-content-text">
                      Maximum number of consecutive days of leave allowed
                    </Typography>
                  }
                />
              </Box>

              <Box>
                <CustomTextField
                  fullWidth
                  id="maxConsecutiveNoDays"
                  name="maxConsecutiveNoDays"
                  value={values?.maxConsecutiveNoDays}
                  placeholder="Days"
                  error={Boolean(
                    touched?.maxConsecutiveNoDays &&
                      errors?.maxConsecutiveNoDays
                  )}
                  // helperText={
                  //   touched?.maxConsecutiveNoDays &&
                  //   errors?.maxConsecutiveNoDays
                  // }
                  onBlur={handleBlur}
                  onChange={(e) => {
                    handleChange(e);
                    setLeavePolicySubmit({
                      ...leavePolicySubmit,
                      leaveRestriction: false,
                    });
                    setLeavePolicyBack({
                      ...leavePolicyBack,
                      leaveRestriction: false,
                    });
                  }}
                  onInput={Inputype}
                  disabled={!values?.maxConsecutiveDays}
                />
              </Box>
              <Tooltip
                arrow
                title={
                  <Typography>
                    This option is useful when you want to restrict an employee
                    from generating a leave application for certain consecutive
                    days
                  </Typography>
                }
                classes={{ tooltip: 'info-tooltip-container' }}
              >
                <InfoIcon className="info-icon cursor-pointer" />
              </Tooltip>
              <Box>
                {touched.maxConsecutiveNoDays &&
                  errors.maxConsecutiveNoDays && (
                    <Typography className="other-field-error-text">
                      {errors.maxConsecutiveNoDays}
                    </Typography>
                  )}
              </Box>
            </Box>
            <Box className="d-flex align-center gap-sm mt16 flex-wrap">
              <Box>
                <CustomCheckbox
                  checked={values.minGapInDays}
                  onChange={(e) => {
                    setFieldValue('minGapInDays', e?.target?.checked);
                    setFieldValue('minGapBetweenApplications', 0);
                    setLeavePolicySubmit({
                      ...leavePolicySubmit,
                      leaveRestriction: false,
                    });
                    setLeavePolicyBack({
                      ...leavePolicyBack,
                      leaveRestriction: false,
                    });
                  }}
                  disableRipple
                  label={
                    <Typography className="sub-content-text">
                      Minimum gap (In days) between two applications
                    </Typography>
                  }
                />
              </Box>

              <Box>
                <CustomTextField
                  fullWidth
                  id="minGapBetweenApplications"
                  name="minGapBetweenApplications"
                  value={values?.minGapBetweenApplications}
                  placeholder="Days"
                  error={Boolean(
                    touched?.minGapBetweenApplications &&
                      errors?.minGapBetweenApplications
                  )}
                  // helperText={
                  //   touched?.minGapBetweenApplications &&
                  //   errors?.minGapBetweenApplications
                  // }
                  onBlur={handleBlur}
                  onChange={(e) => {
                    handleChange(e);
                    setLeavePolicySubmit({
                      ...leavePolicySubmit,
                      leaveRestriction: false,
                    });
                    setLeavePolicyBack({
                      ...leavePolicyBack,
                      leaveRestriction: false,
                    });
                  }}
                  onInput={Inputype}
                  disabled={!values?.minGapInDays}
                />
              </Box>
              <Tooltip
                arrow
                title={
                  <Typography>
                    With the leave application date setting restricted to 0
                    days, the user attempted to apply for leave application for
                    just once time in day then user can't apply leave for same
                    day his can able to apply for next day.
                  </Typography>
                }
                classes={{ tooltip: 'info-tooltip-container' }}
              >
                <InfoIcon className="info-icon cursor-pointer" />
              </Tooltip>
              <Box>
                {touched.minGapBetweenApplications &&
                  errors.minGapBetweenApplications && (
                    <Typography className="other-field-error-text">
                      {errors.minGapBetweenApplications}
                    </Typography>
                  )}
              </Box>
            </Box>

            <Box className="mt16">
              <CustomCheckbox
                checked={values?.employeeCanConsume}
                onChange={(e) => {
                  setFieldValue('employeeCanConsume', e?.target?.checked);
                  setFieldValue('employeeCanExtend', false);
                  setLeavePolicySubmit({
                    ...leavePolicySubmit,
                    leaveRestriction: false,
                  });
                  setLeavePolicyBack({
                    ...leavePolicyBack,
                    leaveRestriction: false,
                  });
                }}
                disableRipple
                label={
                  <Typography className="sub-content-text">
                    Employee can consume/apply for available leave in notice
                    period.
                  </Typography>
                }
              />
            </Box>

            {values?.employeeCanConsume ? (
              <Box className="d-flex align-center gap-sm mt8 ml16">
                <Box>
                  <CustomCheckbox
                    checked={values?.employeeCanExtend}
                    onChange={(e) => {
                      setFieldValue('employeeCanExtend', e?.target?.checked);
                      setLeavePolicySubmit({
                        ...leavePolicySubmit,
                        leaveRestriction: false,
                      });
                      setLeavePolicyBack({
                        ...leavePolicyBack,
                        leaveRestriction: false,
                      });
                    }}
                    disableRipple
                    label={
                      <Typography className="sub-content-text">
                        Extend notice period if leave is taken ?
                      </Typography>
                    }
                  />
                </Box>
                <Tooltip
                  arrow
                  title={
                    <Typography>
                      If an employee takes leave during their resignation notice
                      period, this option will extend the notice period by the
                      number of leave days taken.
                      <br /> Example : If an employee has a 30-day notice period
                      and takes 3 days of leave during this period, their notice
                      period will be extended to 33 days.
                    </Typography>
                  }
                  classes={{ tooltip: 'info-tooltip-container' }}
                >
                  <InfoIcon className="info-icon cursor-pointer" />
                </Tooltip>
              </Box>
            ) : (
              <></>
            )}
            <Box className="mt16">
              <CustomCheckbox
                checked={values?.allowEmpSameLeave}
                onChange={(e) => {
                  setFieldValue('allowEmpSameLeave', e?.target?.checked);
                  setLeavePolicySubmit({
                    ...leavePolicySubmit,
                    leaveRestriction: false,
                  });
                  setLeavePolicyBack({
                    ...leavePolicyBack,
                    leaveRestriction: false,
                  });
                }}
                disableRipple
                label={
                  <Typography className="sub-content-text">
                    Allow employees to view others who are already on leave or
                    have applied for leave on the same date with the same leave
                    type when applying.
                  </Typography>
                }
              />
            </Box>
            <Box className="d-flex align-center gap-sm mt16 flex-wrap">
              <CustomCheckbox
                checked={values?.allowMaxApplications}
                onChange={(e) => {
                  setFieldValue('allowMaxApplications', e?.target?.checked);
                  setFieldValue('maxApplications', 0);
                  setLeavePolicySubmit({
                    ...leavePolicySubmit,
                    leaveRestriction: false,
                  });
                  setLeavePolicyBack({
                    ...leavePolicyBack,
                    leaveRestriction: false,
                  });
                }}
                disableRipple
                label={
                  <Typography className="sub-content-text">
                    Maximum number of applications allowed within the specified
                    period
                  </Typography>
                }
              />
              <Box>
                <CustomTextField
                  fullWidth
                  id="maxApplications"
                  name="maxApplications"
                  value={values?.maxApplications}
                  placeholder="Days"
                  error={Boolean(
                    values?.allowMaxApplications &&
                      touched?.maxApplications &&
                      errors?.maxApplications
                  )}
                  // helperText={
                  //   touched?.maxApplications && errors?.maxApplications
                  // }
                  onBlur={handleBlur}
                  onChange={(e) => {
                    handleChange(e);
                    setLeavePolicySubmit({
                      ...leavePolicySubmit,
                      leaveRestriction: false,
                    });
                    setLeavePolicyBack({
                      ...leavePolicyBack,
                      leaveRestriction: false,
                    });
                  }}
                  onInput={Inputype}
                  disabled={!values?.allowMaxApplications}
                />
              </Box>
              <span> /</span>
              <Box>
                <CustomSelect
                  className="leave-select-wrap"
                  placeholder="Select"
                  name="employeeTimePeriod"
                  options={timePeriodOptions}
                  value={
                    timePeriodOptions?.find((opt) => {
                      return opt?.value === values.employeeTimePeriod;
                    }) || ''
                  }
                  onChange={(e) => {
                    setFieldValue('employeeTimePeriod', e?.value);
                    setLeavePolicySubmit({
                      ...leavePolicySubmit,
                      leaveRestriction: false,
                    });
                    setLeavePolicyBack({
                      ...leavePolicyBack,
                      leaveRestriction: false,
                    });
                  }}
                  error={Boolean(
                    touched?.employeeTimePeriod && errors?.employeeTimePeriod
                  )}
                  helperText={
                    touched?.employeeTimePeriod && errors?.employeeTimePeriod
                  }
                  isClearable={false}
                />
              </Box>
              <Tooltip
                arrow
                title={
                  <Typography>
                    Maximum number of leave requests per specified period. The
                    period may be a week, month, year, accrual period, or job
                    tenure. As an example, we can set a maximum of two maternity
                    leaves during an employee's tenure.
                  </Typography>
                }
                classes={{ tooltip: 'info-tooltip-container' }}
              >
                <InfoIcon className="info-icon cursor-pointer" />
              </Tooltip>
              <Box>
                {values?.allowMaxApplications &&
                  touched.maxApplications &&
                  errors.maxApplications && (
                    <Typography className="other-field-error-text">
                      {errors.maxApplications}
                    </Typography>
                  )}
              </Box>
            </Box>
          </Box>
        </Form>
      )}
    </Formik>
  );
}
