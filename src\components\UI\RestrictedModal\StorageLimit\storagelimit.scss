.storage-limit-container {
  // Dialog Overlay
  .dialog-overlay {
    .dialog-container {
      display: flex;
      align-items: center;
      justify-content: center;
      // padding: 1rem;

      .dialog-content {
        position: relative;
        width: 100%;
        max-width: 28rem;
        background-color: var(--color-white);
        border-radius: var(--border-radius-md);
        // box-shadow: var(--box-shadow-xs);
        transition: all 0.5s;
        transform: scale(0.95);
        opacity: 0;

        &.visible {
          transform: scale(1);
          opacity: 1;
        }

        .dialog-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 1.5rem;
          border-bottom: var(--normal-sec-border);

          .dialog-title {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--text-color-primary);
          }

          .close-button {
            padding: 0.5rem;
            color: var(--text-color-slate-gray);
            transition: color 0.2s;

            &:hover {
              color: var(--text-color-primary);
            }
          }
        }

        .dialog-body {
          // padding: 1.5rem;
          padding-bottom: var(--spacing-xl);

          .dialog-image-container {
            text-align: center;
            margin-bottom: 1.5rem;

            .dialog-image-wrapper {
              width: 100%;
              max-width: 20rem;
              margin: 0 auto;

              .dialog-image {
                width: 100%;
                height: 200px;
                object-fit: contain;
                // border-radius: var(--border-radius-md);
                // box-shadow: var(--box-shadow-xs);
              }
            }
          }

          .dialog-text {
            text-align: center;
            margin-bottom: 1.5rem;

            .dialog-message {
              color: var(--text-color-primary);
              margin-bottom: 0.75rem;
            }

            .dialog-description {
              color: var(--text-color-slate-gray);
              margin-bottom: 0.75rem;

              &:last-child {
                margin-bottom: 0;
              }
            }
          }

          .storage-status {
            background-color: var(--color-danger-opacity);
            border: 1px solid var(--color-danger);
            border-radius: var(--border-radius-md);
            padding: 1rem;
            margin-bottom: 1.5rem;

            .storage-status-header {
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin-bottom: 0.5rem;

              .storage-status-label {
                color: var(--text-color-danger);
              }

              .storage-status-value {
                color: var(--text-color-danger);
              }
            }

            .storage-status-progress-container {
              width: 100%;
              background-color: var(--color-danger-opacity);
              border-radius: var(--border-radius-full);
              height: 0.5rem;

              .storage-status-progress-bar {
                background-color: var(--color-danger);
                height: 0.5rem;
                border-radius: var(--border-radius-full);
              }
            }
          }

          .dialog-actions {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            .custom-button-wrapper {
              width: 240px;
            }
          }
          .profile-incomplete-actions {
            align-items: center;
            .custom-button-wrapper {
              width: 240px;
            }
          }
        }
      }
    }
  }
}
