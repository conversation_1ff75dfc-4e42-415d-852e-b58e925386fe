import { Box, Divider, Typography } from '@mui/material';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import CircleIcon from '@mui/icons-material/Circle';
import { DateFormat } from '@/helper/common/commonFunctions';
import './timehistory.scss';

const TimeHistory = ({ timeHistory }) => {
  // Format time spent as "3 minutes 38 seconds"
  const formatTimeSpent = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes} minutes ${remainingSeconds} seconds`;
  };

  return (
    <Box className="container">
      {timeHistory?.map((entry, index) => (
        <Box key={index} className="d-flex time-history-wrap">
          <Box className="devider-wrap">
            <Divider orientation="vertical" className="vertical-divider" />
          </Box>
          <Box className="history-item">
            <Box className="d-flex align-center header-date-wrap">
              <CalendarMonthIcon className="calender-icon" />
              <Typography component="p" className="header-date">
                {DateFormat(entry.date, 'dates')}
              </Typography>
            </Box>
            <Typography component="p" className="time-wrap">
              {DateFormat(entry.date, 'hoursUTC')}
            </Typography>
            <Box className="d-flex align-center name-text-wrap">
              <CircleIcon className="circle-wrap" />
              <Typography className="name-wrap">{entry.name}</Typography>
            </Box>
            <Typography className="time-spent-wrap">
              <span className="time-spent">Time spent</span>{' '}
              {formatTimeSpent(entry.timeSpent)}
            </Typography>
            <Typography className="time-spent-wrap">
              <span className="exicuted-on">Executed on</span>{' '}
              {DateFormat(entry.date, 'datesWithhour')}
            </Typography>
          </Box>
        </Box>
      ))}
    </Box>
  );
};

export default TimeHistory;
