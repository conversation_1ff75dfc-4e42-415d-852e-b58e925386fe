.side-menu-list {
  @media (max-width: 1024px) {
    width: 100%;
    overflow: auto;
  }
  .MuiList-root {
    @media (max-width: 1024px) {
      display: flex;
      gap: var(--spacing-xs);
      width: max-content;
    }
    .side-menu-item {
      padding: var(--spacing-sm);
      border-radius: var(--border-radius-xl);
      gap: var(--spacing-sm);
      margin-bottom: var(--spacing-sm);
      @media (max-width: 1024px) {
        margin-bottom: var(--spacing-none);
        white-space: nowrap;
      }
      .side-menu-icon {
        min-width: auto;
        svg {
          fill: var(--icon-color-primary);
          height: var(--icon-size-xsm);
          width: var(--icon-size-xsm);
        }
      }
      .side-menu-name {
        margin: var(--spacing-none);
        span {
          font-family: var(--font-family-primary);
          font-size: var(--font-size-sm);
          line-height: var(--line-height-sm);
          color: var(--text-color-black);
        }
      }
      &.active {
        background-color: var(--color-primary);
        .side-menu-icon {
          svg {
            fill: var(--icon-color-white);
          }
        }
        .side-menu-name {
          span {
            color: var(--icon-color-white);
          }
        }
      }
    }
  }
}
