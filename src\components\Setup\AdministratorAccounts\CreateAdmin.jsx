'use client';

import React, { useState } from 'react';
import { Box } from '@mui/material';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
// import { CustomTextField } from '@/components/UI/CommonField/index';
// import CustomButton from '@/components/UI/button';
// import CustomSelect from '@/components/UI/selectbox';
import Gender from '@/components/UI/FormGroupGender';
import MaritalStatus from '@/components/UI/FormGroupMarital';
// import CustomDatePicker from '@/components/UI/datepicker';
import axiosInstance from '@/helper/axios/axiosInstance';
import { setApiMessage } from '@/helper/common/commonFunctions';
// import SingleSelect from '@/components/UI/SelectWithSearch';
import { URLS } from '@/helper/constants/urls';
import dayjs from 'dayjs';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomButton from '@/components/UI/CustomButton';
import CustomSelect from '@/components/UI/CustomSelect';
import CustomDatePicker from '@/components/UI/CustomDatePicker';
import { useRouter } from 'next/navigation';

export default function CreateAdmin({
  isUpdate,
  updateItem,
  getAdminUserList,
  searchValue,
  page,
  countries,
  counties,
  cities,
  setCounties,
  setCities,
  setSelectedCountry,
  setSelectedCounty,
  roleList,
}) {
  const router = useRouter();
  const [loader, setLoader] = useState(false);

  return (
    <Box className="add-edit-admin-section">
      <Formik
        initialValues={{
          firstname: updateItem?.user_first_name || '',
          lastname: updateItem?.user_last_name || '',
          mname: updateItem?.user_middle_name || '',
          email: updateItem?.user_email || '',
          nationality: updateItem?.country || '',
          country: updateItem?.country || '',
          county: updateItem?.geo_country?.place_code || '',
          city: updateItem?.geo_city?.place_code || '',
          pincode: updateItem?.pin_code || '',
          phoneNo: updateItem?.user_phone_number || '',
          birthdate:
            isUpdate && updateItem?.date_of_birth
              ? dayjs(updateItem?.date_of_birth).format('YYYY-MM-DD')
              : null,
          designation: 2,
          joiningdate:
            isUpdate && updateItem?.user_joining_date
              ? dayjs(updateItem?.user_joining_date).format('YYYY-MM-DD')
              : null,
          homeaddress1: updateItem?.address_line1 || '',
          homeaddress2: updateItem?.address_line2 || '',
          gender:
            isUpdate && updateItem?.user_gender
              ? updateItem?.user_gender
              : isUpdate && updateItem?.user_gender_other
                ? updateItem?.user_gender_other
                : '',
          marital:
            isUpdate && updateItem?.marital_status
              ? updateItem?.marital_status
              : isUpdate && updateItem?.marital_status_other
                ? updateItem?.marital_status_other
                : '',
        }}
        enableReinitialize={true}
        validationSchema={Yup.object().shape({
          firstname: Yup.string().trim().required('This field is required'),
          lastname: Yup.string().trim().required('This field is required'),
          email: Yup.string()
            .required('This field is required')
            .matches(
              /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i,
              'Please enter valid email'
            ),
          joiningdate: Yup.string().trim().required('This field is required'),
          phoneNo: Yup.string()
            .trim()
            .matches(/^[0-9]{10,11}$/, 'Invalid phone number'),
        })}
        onSubmit={async (requestData) => {
          setLoader(true);
          let sendData = {
            user_first_name: requestData?.firstname,
            user_last_name: requestData?.lastname,
            ...(requestData?.mname && { user_middle_name: requestData?.mname }),
            user_email: requestData?.email,
            ...(requestData?.homeaddress1 && {
              address_line1: requestData?.homeaddress1,
            }),
            ...(requestData?.homeaddress2 && {
              address_line2: requestData?.homeaddress2,
            }),
            ...(requestData?.country && {
              geo_country: requestData?.country,
            }),
            ...(requestData?.nationality && {
              country: requestData?.nationality,
            }),
            ...(requestData?.county && {
              geo_state: requestData?.county,
            }),
            ...(requestData?.city && {
              geo_city: requestData?.city,
            }),
            ...(requestData?.pincode && {
              pin_code: requestData?.pincode,
            }),
            ...(requestData?.phoneNo && {
              user_phone_number: requestData?.phoneNo.toString(),
            }),
            ...(requestData?.birthdate && {
              date_of_birth: dayjs(requestData?.birthdate).format('YYYY-MM-DD'),
            }),
            ...(requestData?.gender &&
              (requestData?.gender === 'male' ||
                requestData?.gender == 'female') && {
                user_gender: requestData?.gender,
              }),
            ...(requestData?.gender &&
              requestData?.gender !== 'male' &&
              requestData?.gender !== 'female' && {
                user_gender_other: requestData?.gender,
              }),
            ...(requestData?.marital &&
              (requestData?.marital === 'single' ||
                requestData?.marital === 'married') && {
                marital_status: requestData?.marital,
              }),
            ...(requestData?.marital &&
              requestData?.marital !== 'single' &&
              requestData?.marital !== 'married' && {
                marital_status_other: requestData?.marital,
              }),
            ...(requestData?.joiningdate && {
              joining_date: dayjs(requestData?.joiningdate).format(
                'YYYY-MM-DD'
              ),
            }),
            role_ids: [2],
          };
          const ApiUrl = isUpdate
            ? URLS.UPDATE_USER + updateItem?.id
            : URLS.CREATE_USER;
          const method = isUpdate ? 'put' : 'post';
          try {
            const { status, data } = await axiosInstance[method](
              ApiUrl,
              sendData
            );

            if (status === 200) {
              if (data?.status) {
                setApiMessage('success', data?.message);
                getAdminUserList(searchValue, page);
                router.back();
              } else {
                setApiMessage('error', data?.message);
              }
              setLoader(false);
            }
          } catch (error) {
            setLoader(false);
            setApiMessage('error', error?.response?.data?.message);
          }
        }}
      >
        {({
          errors,
          touched,
          handleBlur,
          values,
          handleSubmit,
          handleChange,
          setFieldValue,
        }) => (
          <Form onSubmit={handleSubmit}>
            <Box className="display-grid">
              <Box className="">
                <CustomTextField
                  fullWidth
                  id="firstname"
                  name="firstname"
                  label="First name"
                  placeholder="Enter firstname"
                  value={values?.firstname}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={Boolean(touched.firstname && errors.firstname)}
                  helperText={touched.firstname && errors.firstname}
                  required
                />
              </Box>
              <Box className="">
                <CustomTextField
                  fullWidth
                  id="mname"
                  name="mname"
                  label="Middle name"
                  placeholder="Enter middle name"
                  value={values?.mname}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={Boolean(touched.mname && errors.mname)}
                  helperText={touched.mname && errors.mname}
                />
              </Box>
              <Box className="">
                <CustomTextField
                  fullWidth
                  id="lastname"
                  name="lastname"
                  label="Last name"
                  placeholder="Enter lastname"
                  value={values?.lastname}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={Boolean(touched.lastname && errors.lastname)}
                  helperText={touched.lastname && errors.lastname}
                  required
                />
              </Box>
              <Box className="">
                <CustomTextField
                  fullWidth
                  id="email"
                  name="email"
                  label="Email address"
                  placeholder="Enter Email address"
                  value={values?.email}
                  onBlur={handleBlur}
                  onChange={handleChange}
                  error={Boolean(touched.email && errors.email)}
                  helperText={touched.email && errors.email}
                  required
                />
              </Box>
              <Box className="">
                <CustomTextField
                  fullWidth
                  id="phoneNo"
                  name="phoneNo"
                  label="Phone number"
                  placeholder="Enter Phone number"
                  value={values?.phoneNo}
                  onChange={(e) => {
                    if (e.target.value === '' || e.target.value?.length < 12) {
                      handleChange(e);
                    }
                  }}
                  onBlur={handleBlur}
                  error={Boolean(touched.phoneNo && errors.phoneNo)}
                  helperText={touched.phoneNo && errors.phoneNo}
                  onInput={(e) => {
                    e.target.value = e.target.value.replace(/[^0-9]/g, ''); // Remove non-numeric characters
                  }}
                />
              </Box>
              <Box className="">
                <CustomDatePicker
                  name="birthdate"
                  label="Date of birth"
                  placeholder="Date of birth"
                  error={Boolean(touched.birthdate && errors.birthdate)}
                  helperText={touched.birthdate && errors.birthdate}
                  value={dayjs(values?.birthdate)}
                  onBlur={handleBlur}
                  onChange={(date) => {
                    setFieldValue('birthdate', date);
                  }}
                  disableFuture={true}
                  maxDate={dayjs().subtract(18, 'year')}
                />
              </Box>
              <Box className="">
                <CustomSelect
                  name="nationality"
                  label="Nationality"
                  placeholder="Nationality"
                  options={countries}
                  value={
                    countries?.find((opt) => {
                      return opt?.value === values?.nationality;
                    }) || ''
                  }
                  onChange={(e) => {
                    setFieldValue('nationality', e?.value);
                  }}
                />
              </Box>
              <Box className="">
                <CustomSelect
                  name="country"
                  label="Country"
                  placeholder="Country"
                  options={countries}
                  value={
                    countries?.find((opt) => {
                      return opt?.value === values?.country;
                    }) || ''
                  }
                  onChange={(e) => {
                    setFieldValue('country', e?.value);
                    setFieldValue('county', '');
                    setFieldValue('city', '');
                    setCities([]);
                    setCounties([]);
                    setSelectedCountry(e?.value);
                  }}
                />
              </Box>
              <Box className="">
                <CustomSelect
                  name="county"
                  label="County"
                  placeholder="County"
                  options={counties}
                  value={
                    counties?.find((opt) => {
                      return opt?.value === values?.county;
                    }) || ''
                  }
                  onChange={(e) => {
                    setFieldValue('county', e?.value);
                    setFieldValue('city', '');
                    setSelectedCounty(e?.value);
                  }}
                />
              </Box>
              <Box className="">
                <CustomSelect
                  name="city"
                  label="City"
                  placeholder="City"
                  options={cities}
                  value={
                    cities?.find((opt) => {
                      return opt?.value === values?.city;
                    }) || ''
                  }
                  onChange={(e) => {
                    setFieldValue('city', e?.value);
                  }}
                />
              </Box>
              <Box className="">
                <CustomTextField
                  fullWidth
                  id="pincode"
                  name="pincode"
                  label="Post Code"
                  placeholder="Enter Post code"
                  value={values?.pincode}
                  onChange={(e) => {
                    if (e.target.value && e.target.value?.length < 9) {
                      handleChange(e);
                    }
                  }}
                  onBlur={handleBlur}
                  error={Boolean(touched.pincode && errors.pincode)}
                  helperText={touched.pincode && errors.pincode}
                />
              </Box>
            </Box>
            <Box className="pt16 display-grid">
              <Box className="">
                <CustomTextField
                  fullWidth
                  id="homeaddress1"
                  name="homeaddress1"
                  label="Address line 1"
                  placeholder="Enter Address"
                  value={values?.homeaddress1}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={Boolean(touched.homeaddress1 && errors.homeaddress1)}
                  helperText={touched.homeaddress1 && errors.homeaddress1}
                />
              </Box>
              <Box className="">
                <CustomTextField
                  fullWidth
                  id="homeaddress2"
                  name="homeaddress2"
                  label="Address line 2"
                  placeholder="Enter Address"
                  value={values?.homeaddress2}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={Boolean(touched.homeaddress2 && errors.homeaddress2)}
                  helperText={touched.homeaddress2 && errors.homeaddress2}
                />
              </Box>
            </Box>
            <Box className="pt16">
              <Gender
                keyName="gender"
                setFieldValue={setFieldValue}
                isRequire={false}
                keyValue={values?.gender}
              />
            </Box>
            <Box className="pt16">
              <MaritalStatus
                keyName="marital"
                setFieldValue={setFieldValue}
                isRequire={false}
                keyValue={values?.marital}
              />
            </Box>
            <Box className="pt16 display-grid">
              <Box className="">
                <CustomSelect
                  name="designation"
                  label="System Access"
                  placeholder="System Access"
                  options={roleList}
                  value={
                    roleList?.find((opt) => {
                      return opt?.value === values?.designation;
                    }) || ''
                  }
                  onChange={(e) => {
                    setFieldValue('designation', e?.value);
                  }}
                  //   onChange={handleChange}
                  isDisabled
                />
              </Box>
              <Box className="">
                <CustomDatePicker
                  name="joiningdate"
                  label="Joining date"
                  placeholder="Joining date"
                  value={dayjs(values?.joiningdate)}
                  onBlur={handleBlur}
                  onChange={(date) => {
                    setFieldValue('joiningdate', date);
                  }}
                  error={Boolean(touched.joiningdate && errors.joiningdate)}
                  helperText={touched.joiningdate && errors.joiningdate}
                  required
                />
              </Box>
            </Box>

            <Box className="form-actions-btn">
              <CustomButton
                type="submit"
                disabled={loader}
                title={`${loader ? 'Saving...' : 'Save'}`}
              />
            </Box>
          </Form>
        )}
      </Formik>
    </Box>
  );
}
