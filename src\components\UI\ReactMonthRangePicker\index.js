import React, { useEffect, useState } from 'react';
import { Box, InputLabel, InputAdornment, TextField } from '@mui/material';
import DatePicker from 'react-datepicker';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import 'react-datepicker/dist/react-datepicker.css';
import './datepicker.scss';

const CustomMonthRangePicker = ({
  onChange,
  label,
  value,
  className,
  disablePast,
  disabled,
  minDate,
  maxDate,
  placeholder,
  isClearable,
}) => {
  const [startMonth, setStartMonth] = useState(new Date());
  const [endMonth, setEndMonth] = useState(new Date());

  const handleStartChange = (date) => {
    setStartMonth(date);
    onChange?.({ startMonth: date, endMonth });
  };

  const handleEndChange = (date) => {
    setEndMonth(date);
    onChange?.({ startMonth, endMonth: date });
  };

  useEffect(() => {
    value?.startMonth
      ? setStartMonth(new Date(value?.startMonth))
      : setStartMonth();
    value?.endMonth ? setEndMonth(new Date(value?.endMonth)) : setEndMonth();
  }, [value]);

  return (
    <Box
      className={
        className
          ? `${className} date-wrap react-datepicker-sec w100 react-Monthpicker-sec `
          : 'date-wrap w100 react-datepicker-sec react-Monthpicker-sec'
      }
    >
      <InputLabel shrink>{label}</InputLabel>
      <Box className="date-picker-container">
        <DatePicker
          selected={startMonth}
          onChange={handleStartChange}
          selectsStart
          startDate={startMonth}
          endDate={endMonth}
          minDate={disablePast ? new Date() : minDate}
          maxDate={maxDate}
          showMonthYearPicker
          dateFormat="MM/yyyy"
          placeholderText={placeholder || 'Start Month'}
          disabled={disabled}
          isClearable={isClearable}
          customInput={
            <TextField
              className="input_field"
              variant="outlined"
              fullWidth
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <CalendarMonthIcon sx={{ cursor: 'pointer' }} />
                  </InputAdornment>
                ),
                value: startMonth
                  ? startMonth.toLocaleDateString('en-GB', {
                      month: 'short',
                      year: 'numeric',
                    })
                  : '',
              }}
            />
          }
        />
        <DatePicker
          selected={endMonth}
          onChange={handleEndChange}
          selectsEnd
          startDate={startMonth}
          endDate={endMonth}
          minDate={startMonth}
          maxDate={maxDate}
          showMonthYearPicker
          dateFormat="MM/yyyy"
          placeholderText={placeholder || 'End Month'}
          disabled={disabled}
          isClearable={isClearable}
          customInput={
            <TextField
              className="input_field"
              variant="outlined"
              fullWidth
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <CalendarMonthIcon sx={{ cursor: 'pointer' }} />
                  </InputAdornment>
                ),
                value: endMonth
                  ? endMonth.toLocaleDateString('en-GB', {
                      month: 'short',
                      year: 'numeric',
                    })
                  : '',
              }}
            />
          }
        />
      </Box>
    </Box>
  );
};

export default CustomMonthRangePicker;
