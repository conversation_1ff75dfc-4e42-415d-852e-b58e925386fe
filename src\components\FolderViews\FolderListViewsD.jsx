import React, { useEffect, useState } from 'react';
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Checkbox,
  ListItemIcon,
  Menu,
  MenuItem,
  Typography,
  TableSortLabel,
} from '@mui/material';
import { useRouter } from 'next/navigation';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import {
  FolderDocListIcon,
  FolderDocListTraingIcon,
} from '../../helper/common/images';
import HeaderImage from '@/components/UI/ImageSecurity';
import { DateFormat } from '@/helper/common/commonFunctions';
import DeleteModal from '@/components/UI/DeleteModal';
import DialogBox from '@/components/UI/Modalbox';
import './folder.scss';
import moment from 'moment';

const FolderListViews = ({
  folderData,
  menuList,
  setCreateModalTitle,
  setCreateModal,
  isOwn,
  setEmpContractList,
  selectedCat,
  setSelectedCat,
  Id,
  deleteDocument,
  CategoryDetails,
  ReorderDoctMedia,
  isFilter,
}) => {
  // const [selected, setSelected] = useState([]);
  const [sortedData, setSortedData] = useState([]);
  const [listItem, setListItem] = useState();
  const [anchorEl, setAnchorEl] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteId, setDeleteId] = useState([]);
  const [order, setOrder] = useState('asc');
  const [orderBy, setOrderBy] = useState('name');
  const open = Boolean(anchorEl);
  const router = useRouter();
  const handleClick = (event) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
    setListItem();
  };
  const handleOpenDeleteDialog = (id) => {
    setDeleteId(id);
    setDeleteDialogOpen(true);
  };
  const handleConfirmDelete = () => {
    deleteDocument(deleteId);
    setDeleteDialogOpen(false);
    setDeleteId(null);
  };
  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setDeleteId([]);
  };
  const onclickAction = (option) => {
    setAnchorEl(null);
    if (option?.id === 1) {
      let deleteItem = [];
      deleteItem.push(listItem?.id);
      // deleteDocument(deleteItem);
      handleOpenDeleteDialog(deleteItem);
    } else if (option?.id === 2 || option?.id === 3) {
      setCreateModal(true);
      option?.id === 2
        ? setCreateModalTitle({ title: 'Copy Items', id: listItem?.id })
        : option?.id === 3
          ? setCreateModalTitle({ title: 'Move Items', id: listItem?.id })
          : setCreateModalTitle('');
    } else if (option?.id === 4 && listItem) {
      listItem?.category_type === 'folder'
        ? router.push(
            `/create-category?id=${Id}&UpdateId=${listItem?.id}&type=${
              listItem?.category_use
                ? listItem?.category_use
                : CategoryDetails?.category_use
            }`
          )
        : router.push(
            `/create-content?id=${Id}&UpdateId=${listItem?.id}&type=${
              listItem?.category_use
                ? listItem?.category_use
                : CategoryDetails?.category_use
            }`
          );
    }
  };
  const handleSelectAllClick = (event) => {
    if (event.target.checked) {
      const newSelecteds = folderData?.map((n) => n?.id);
      setSelectedCat(newSelecteds);
      return;
    }
    setSelectedCat([]);
  };

  const handleCheckBoxClick = (event, id) => {
    const selectedIndex = selectedCat?.indexOf(id);
    let newSelected = [];

    if (selectedIndex === -1) {
      newSelected = newSelected?.concat(selectedCat, id);
    } else if (selectedIndex === 0) {
      newSelected = newSelected?.concat(selectedCat?.slice(1));
    } else if (selectedIndex === selectedCat?.length - 1) {
      newSelected = newSelected?.concat(selectedCat?.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelected = newSelected?.concat(
        selected?.slice(0, selectedIndex),
        selected?.slice(selectedIndex + 1)
      );
    }

    setSelectedCat(newSelected);
  };

  const handleRequestSort = (property) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
    const orderrequest = isAsc ? 'desc' : 'asc';
    const sData = folderData?.slice().sort((a, b) => {
      if (property === 'updateDate') {
        return orderrequest === 'asc'
          ? moment(a[property]).unix() - moment(b[property]).unix()
          : moment(b[property]).unix() - moment(a[property]).unix();
      }
      if (a[property] < b[property]) {
        return orderrequest === 'asc' ? -1 : 1;
      }
      if (a[property] > b[property]) {
        return orderrequest === 'asc' ? 1 : -1;
      }
      return 0;
    });
    setSortedData(sData);
    setEmpContractList(sData);
  };

  const onDragEnd = (result) => {
    const { source, destination } = result;
    const newData = sortedData;
    const [movedRow] = newData.splice(source?.index, 1);
    newData.splice(destination?.index, 0, movedRow);
    setSortedData(newData);
    setEmpContractList(newData);
    const CuurectCat = sortedData[destination?.index];
    ReorderDoctMedia(destination?.index, CuurectCat?.id);

    // const mediaIds = newData?.map((m) => m?.id);
    // ReorderPlaylistMedia(PlayId, mediaIds);
  };

  useEffect(() => {
    if (folderData && folderData?.length > 0) {
      setSortedData(folderData);
    }
  }, [folderData]);

  return (
    <>
      <Box className="folder-list-view">
        <TableContainer>
          <Table>
            {!isOwn ? (
              <>
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ width: '50px' }}>
                      <Checkbox
                        indeterminate={
                          selectedCat?.length > 0 &&
                          selectedCat?.length < folderData?.length
                        }
                        checked={
                          folderData?.length > 0 &&
                          selectedCat?.length === folderData?.length
                        }
                        onChange={handleSelectAllClick}
                        className="table-checkbox"
                      />
                    </TableCell>
                    <TableCell sx={{ width: '400px' }}>
                      <Box className="d-flex">
                        <Box style={{ width: '24px', height: '24px' }}></Box>
                        <TableSortLabel
                          active={orderBy === 'category_name'}
                          direction={
                            orderBy === 'category_name' ? order : 'asc'
                          }
                          onClick={() => handleRequestSort('category_name')}
                        >
                          <Typography className="title-text fw600">
                            Name
                          </Typography>
                        </TableSortLabel>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <TableSortLabel
                        active={orderBy === 'size'}
                        direction={orderBy === 'size' ? order : 'asc'}
                        onClick={() => handleRequestSort('size')}
                      >
                        <Typography className="title-text fw600">
                          Size
                        </Typography>
                      </TableSortLabel>
                    </TableCell>
                    <TableCell>
                      <TableSortLabel
                        active={orderBy === 'updatedAt'}
                        direction={orderBy === 'updatedAt' ? order : 'asc'}
                        onClick={() => handleRequestSort('updatedAt')}
                      >
                        <Typography className="title-text fw600">
                          Modified
                        </Typography>
                      </TableSortLabel>
                    </TableCell>
                    {/* <TableCell>
                    <TableSortLabel
                      active={orderBy === 'filetype'}
                      direction={orderBy === 'filetype' ? order : 'asc'}
                      onClick={() => handleRequestSort('filetype')}
                    >
                      <Typography className="title-text fw600">Type</Typography>
                    </TableSortLabel>
                  </TableCell> */}
                    <TableCell></TableCell>
                  </TableRow>
                </TableHead>
                <DragDropContext onDragEnd={onDragEnd}>
                  <Droppable droppableId="droppable">
                    {(provided) => (
                      <TableBody
                        {...provided.droppableProps}
                        ref={provided.innerRef}
                      >
                        {sortedData?.map((item, index) => (
                          <Draggable
                            key={item?.id}
                            draggableId={!isFilter && item?.id.toString()}
                            index={index}
                          >
                            {(provided) => (
                              <TableRow
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                hover
                                //   role="checkbox"
                                aria-checked={
                                  selectedCat?.indexOf(item?.id) !== -1
                                }
                                tabIndex={-1}
                                key={item?.id}
                                {...provided.dragHandleProps}
                                selected={selectedCat?.indexOf(item?.id) !== -1}
                                className={
                                  isFilter
                                    ? 'details-table-row cursor-default'
                                    : 'details-table-row'
                                }
                              >
                                <>
                                  <TableCell sx={{ width: '50px' }}>
                                    <Checkbox
                                      className="table-checkbox"
                                      onClick={(event) =>
                                        handleCheckBoxClick(event, item?.id)
                                      }
                                      checked={
                                        selectedCat?.indexOf(item?.id) !== -1
                                      }
                                    />
                                  </TableCell>
                                  <TableCell sx={{ width: '400px' }}>
                                    <Box className="d-flex align-center">
                                      <Box
                                        className={`d-flex justify-start align-center gap-sm list-folder-name-view ${
                                          item?.category_status === 'inactive'
                                            ? 'inactive-folder'
                                            : ''
                                        }`}
                                      >
                                        <Box>
                                          {item?.category_type === 'file' ? (
                                            <Box
                                              className={`list-view-file ${
                                                !isOwn && 'cursor-pointer'
                                              }`}
                                              onClick={() => {
                                                if (
                                                  !isOwn &&
                                                  item?.category_type === 'file'
                                                ) {
                                                  router.push(
                                                    `/document-staff/${
                                                      item?.parent_id
                                                        ? item?.parent_id
                                                        : 'all'
                                                    }?details=true&subId=${
                                                      item?.id
                                                    }`
                                                  );
                                                }
                                              }}
                                            >
                                              {/* <LazyLoadImage
                                              src={item?.category_image_url}
                                              alt="not found"
                                              className="list-view-file-image"
                                            /> */}
                                              <HeaderImage
                                                imageUrl={
                                                  item?.category_image_url
                                                }
                                                alt="not found"
                                                className="list-view-file-image"
                                                type="lazyload"
                                              />
                                            </Box>
                                          ) : (
                                            <Box
                                              // className="list-view-folder"
                                              className={`list-view-folder ${
                                                item?.category_type ===
                                                  'folder' && 'cursor-pointer'
                                              }`}
                                              onClick={() => {
                                                item?.category_type ===
                                                  'folder' &&
                                                  router.push(
                                                    `/document-staff/${
                                                      item?.id
                                                        ? item?.id
                                                        : 'all'
                                                    }`
                                                  );
                                              }}
                                            >
                                              {item?.category_use === 'training'
                                                ? FolderDocListTraingIcon()
                                                : FolderDocListIcon()}
                                            </Box>
                                          )}
                                        </Box>
                                        <Typography
                                          // className={
                                          //   !isOwn &&
                                          //   folderData?.category_type === 'folder'
                                          //     ? 'title-text fw400 link-text-name cursor-pointer'
                                          //     : 'title-text fw400 link-text-name'
                                          // }
                                          className={`title-text fw400 link-text-name text-ellipsis-line 
                                          cursor-pointer`}
                                          onClick={() => {
                                            item?.category_type === 'folder'
                                              ? router.push(
                                                  `/document-staff/${
                                                    item?.id ? item?.id : 'all'
                                                  }`
                                                )
                                              : item?.category_type === 'file';
                                            router.push(
                                              `/document-staff/${
                                                item?.parent_id
                                                  ? item?.parent_id
                                                  : 'all'
                                              }?details=true&subId=${item?.id}`
                                            );
                                          }}
                                        >
                                          {item?.category_name}
                                        </Typography>
                                        {item?.category_status === 'draft' && (
                                          <Typography className="sub-title-text draft fw600">
                                            Draft
                                          </Typography>
                                        )}
                                      </Box>
                                    </Box>
                                    {/* <Typography className="title-text fw400">{row?.name}</Typography> */}
                                  </TableCell>
                                  <TableCell>
                                    <Typography className="title-text fw400">
                                      {item?.size}
                                    </Typography>
                                  </TableCell>
                                  <TableCell>
                                    <Typography className="title-text fw400">
                                      <Typography className="title-text fw400">
                                        <span className="d-flex align-center">
                                          <CalendarTodayIcon className="svg-icon" />
                                          {DateFormat(item?.updatedAt, 'dates')}
                                        </span>
                                      </Typography>
                                      <Typography className="title-text fw400">
                                        <span className="d-flex align-center">
                                          <AccessTimeIcon className="svg-icon" />
                                          {DateFormat(
                                            item?.updatedAt,
                                            'hoursUTC'
                                          )}
                                        </span>
                                      </Typography>
                                    </Typography>
                                  </TableCell>
                                  {/* <TableCell>
                                  <Typography className="title-text fw400">
                                    {item?.filetype}
                                  </Typography>
                                </TableCell> */}
                                  <TableCell align="center">
                                    <Box
                                      className="cursor-pointer"
                                      onClick={(e) => {
                                        handleClick(e);
                                        setListItem(item);
                                      }}
                                    >
                                      <MoreVertIcon />
                                    </Box>
                                  </TableCell>
                                </>
                              </TableRow>
                            )}
                          </Draggable>
                        ))}
                        {provided.placeholder}
                      </TableBody>
                    )}
                  </Droppable>
                </DragDropContext>
              </>
            ) : (
              <>
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ width: '400px' }}>
                      <Box className="d-flex">
                        <Typography className="title-text fw600">
                          Name
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography className="title-text fw600">Size</Typography>
                    </TableCell>
                    <TableCell>
                      <Typography className="title-text fw600">
                        Modified
                      </Typography>
                    </TableCell>
                    {/* <TableCell>
                    <Typography className="title-text fw600">Type</Typography>
                  </TableCell> */}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {sortedData?.map((row, i) => {
                    return (
                      <React.Fragment key={i}>
                        <TableRow className="blank-table-row"></TableRow>
                        <TableRow
                          hover
                          tabIndex={-1}
                          key={row?.id}
                          className={
                            isOwn
                              ? 'details-table-row cursor-pointer'
                              : 'details-table-row'
                          }
                          onClick={() => {
                            isOwn && row?.category_type === 'file'
                              ? router.push(
                                  `/document-own/${
                                    row?.parent_id ? row?.parent_id : 'all'
                                  }?details=true&subId=${row?.id}`
                                )
                              : isOwn
                                ? router.push(
                                    `/document-own/${row?.id ? row?.id : 'all'}`
                                  )
                                : '';
                          }}
                        >
                          <TableCell sx={{ width: '400px' }}>
                            <Box className="d-flex justify-start align-center gap-sm list-folder-name-view">
                              <Box>
                                {row?.category_type === 'file' ? (
                                  <Box className="list-view-file">
                                    {/* <LazyLoadImage
                                    src={row?.category_image_url}
                                    alt="not found"
                                    className="list-view-file-image"
                                  /> */}
                                    <HeaderImage
                                      imageUrl={row?.category_image_url}
                                      alt="not found"
                                      className="list-view-file-image"
                                      type="lazyload"
                                    />
                                  </Box>
                                ) : (
                                  <Box className="list-view-folder">
                                    {row?.category_use === 'training'
                                      ? FolderDocListTraingIcon()
                                      : FolderDocListIcon()}
                                  </Box>
                                )}
                              </Box>
                              <Typography
                                className={
                                  'title-text fw400 link-text-name link-text-name-active text-ellipsis-line'
                                }
                                onClick={() => {
                                  isOwn && row?.category_type === 'file'
                                    ? router.push(
                                        `/document-own/${row?.id}?details=true`
                                      )
                                    : isOwn
                                      ? router.push(`/document-own/${row?.id}`)
                                      : '';
                                }}
                              >
                                {row?.category_name}
                              </Typography>
                            </Box>
                            {/* <Typography className="title-text fw400">{row?.name}</Typography> */}
                          </TableCell>
                          <TableCell>
                            <Typography className="title-text fw400">
                              {row?.size}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography className="title-text fw400">
                              <Typography className="title-text fw400">
                                <span className="d-flex align-center">
                                  <CalendarTodayIcon className="svg-icon" />
                                  {DateFormat(row?.updatedAt, 'dates')}
                                </span>
                              </Typography>
                              <Typography className="title-text fw400">
                                <span className="d-flex align-center">
                                  <AccessTimeIcon className="svg-icon" />
                                  {DateFormat(row?.updatedAt, 'hoursUTC')}
                                </span>
                              </Typography>
                            </Typography>
                          </TableCell>
                          {/* <TableCell>
                          <Typography className="title-text fw400 text-capital">
                            {row?.filetype}
                          </Typography>
                        </TableCell> */}
                        </TableRow>
                      </React.Fragment>
                    );
                  })}
                </TableBody>
              </>
            )}
          </Table>
        </TableContainer>
        <Menu
          id="long-menu"
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          className="Action-menu-popover"
          sx={{
            '& .MuiPaper-root': {
              maxWidth: '448px',
              minWidth: '180px',
            },
          }}
        >
          {menuList?.map((option) => (
            <>
              {option?.id === 3 && Id === 'all' ? (
                <></>
              ) : (
                <MenuItem
                  key={option?.id}
                  // selected={option?.name === 'Download'}
                  onClick={() => onclickAction(option)}
                  className={option?.name}
                >
                  {' '}
                  <ListItemIcon>{option?.icon}</ListItemIcon>
                  {option?.name}
                </MenuItem>
              )}
            </>
          ))}
        </Menu>
      </Box>
      <DialogBox
        open={deleteDialogOpen}
        handleClose={handleCloseDeleteDialog}
        className="delete-modal"
        dividerClass="delete-modal-divider"
        title="Confirmation"
        content={
          <DeleteModal
            handleCancel={handleCloseDeleteDialog}
            handleConfirm={handleConfirmDelete}
            text="Are you sure you want to delete?"
          />
        }
      />
    </>
  );
};

export default FolderListViews;
