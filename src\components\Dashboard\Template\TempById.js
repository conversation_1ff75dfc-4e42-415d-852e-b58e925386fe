'use client';

import React, { useContext, useState } from 'react';
import AuthContext from '@/helper/authcontext';
import AddModels from '@/components/Dashboard/AddModels/index';
import RightDrawer from '@/components/UI/RightDrawer';
import ChartTemplate from '@/components/Dashboard/Template/index';
import '../dashboard.scss';

export default function ChartDashboard({ params }) {
  const { setIsDrawer, AllListsData } = useContext(AuthContext);
  const [openModel, setOpenModel] = useState(false);
  const [IsEdit, setIsEdit] = useState(false);
  const DashbaordId = params?.id;
  //   const [loader, setLoader] = useState(false);

  const handleCloseModel = () => {
    setOpenModel(false);
    setIsDrawer(false);
    setIsEdit(false);
  };

  return (
    <>
      <ChartTemplate
        branchList={AllListsData?.ActiveBranchList}
        DashbaordId={DashbaordId}
      />

      <RightDrawer
        anchor={'right'}
        open={openModel}
        onClose={handleCloseModel}
        title={IsEdit ? 'Edit Models' : 'Add Models'}
        content={
          <>
            <AddModels
              branchList={AllListsData?.ActiveBranchList}
              handleClose={handleCloseModel}
            />
          </>
        }
      />
    </>
  );
}
