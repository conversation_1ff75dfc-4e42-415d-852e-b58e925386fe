.shift-swap-container {
  min-width: 350px;
  font-family: var(--font-family-primary);
  .shift-swap-header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 30px;
    padding: 15px;
    .shift-swap-header-title {
      font-size: 18px;
      line-height: 20px;
      font-weight: 500;
      text-decoration: underline;
      text-decoration-style: dotted;
      text-underline-offset: 8px;
      text-decoration-color: var(--icon-color-slate-gray);
    }
    .shift-swap-header-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 10px;
      .update-shift-btn {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 10px;
      }
      .delete-shift-btn {
        cursor: pointer;
        height: var(--icon-size-md);
        width: var(--icon-size-md);
        color: var(--icon-bold-red-color);
        svg {
          height: var(--icon-size-md);
          width: var(--icon-size-md);
        }
      }
      .close-shift-tooltip {
        height: var(--icon-size-md);
        width: var(--icon-size-md);
        cursor: pointer;
        color: var(--icon-color-slate-gray);
      }
    }
  }
  .shift-swap-details-container {
    // padding: 10px 15px;
    .shift-swap-details-wrapper {
      background: var(--color-light-grayish-blue);
      border-left: 3px solid var(--border-color-primary);
      padding: 15px;
      .shift-swap-details {
        .shift-swap-user-details {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          gap: 15px;
          .shift-swap-avatar {
            .MuiAvatar-root {
              height: var(--icon-size-lg);
              width: var(--icon-size-lg);
              font-size: var(--font-size-sm);
            }
          }
          .shift-swap-badge-icon {
            background: var(--color-black);
            fill: var(--icon-color-white);
            border-radius: 50%;
            width: var(--icon-size-xs);
            height: var(--icon-size-xs);
            padding: var(--spacing-xxs);
          }
        }
        .shift-swap-shift-details {
          background: var(--color-white);
          margin-top: 10px;
          margin-left: 40px;
          padding: 10px;
          border-radius: 8px;
          .shift-swap-shift-time-role {
            border-left: 3px solid var(--color-light-grayish-blue);
            padding-left: 5px;
            .shift-swap-shift-time {
              color: var(--text-color-black);
              font-size: 14px;
              line-height: 21px;
              letter-spacing: 0px;
              font-weight: 600;
            }
            .shift-swap-shift-date {
              color: var(--text-light-dark);
              font-size: 14px;
              line-height: 15px;
              letter-spacing: 0px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              font-weight: 400;
              margin-top: 2px;
            }
          }
          .shift-swap-shift-icon-text {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            gap: 5px;
            margin-bottom: 2px;
            max-width: 350px;
            svg {
              width: 20px;
              height: 20px;
              fill: var(--icon-color-light-dark);
            }
            span {
              font-size: 14px;
              line-height: 21px;
              color: var(--icon-color-light-dark);
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
    .d-center-start {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: 10px;
    }
    .dropdown-wrapper {
      position: relative;
      width: 100%;
      font-size: 14px;
      border-radius: 5px;
      .dropdown-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
        padding: var(--field-padding);
        border-radius: var(--field-radius);
        font-size: var(--font-size-sm);
        line-height: var(--line-height-xs);
        font-weight: var(--font-weight-regular);
        &:hover {
          background: var(--color-secondary);
          color: var(--text-color-black);
        }
        .arrow {
          height: var(--field-icon-size);
          width: var(--field-icon-size);
          color: var(--icon-color-slate-gray);
          margin-left: var(--spacing-xsm);
          svg {
            height: var(--field-icon-size);
            width: var(--field-icon-size);
          }
        }
        .no-select-text {
          color: var(--text-light-dark);
        }
      }
    }

    // Shift Location / User
    .shift-select-dropdown {
      margin: 5px 10px;
      .dropdown-left-title {
        font-size: 14px;
        line-height: 20px;
        width: max-content;
      }
      .icon {
        height: var(--icon-size-sm);
        width: var(--icon-size-sm);
        color: var(--icon-color-slate-gray);
        svg {
          height: var(--icon-size-sm);
          width: var(--icon-size-sm);
        }
      }
    }
  }
}
