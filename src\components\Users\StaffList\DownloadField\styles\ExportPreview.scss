@import '@/app/_globals.scss';

// Export Preview Component Styles
.export-preview {
  background: var(--color-off-white);
  border-radius: var(--border-radius-md);
  border: var(--normal-sec-border);
  overflow: hidden;

  // Preview Header
  .preview-header {
    padding: var(--spacing-lg);
    border-bottom: var(--normal-sec-border);
    background: rgba(19, 94, 150, 0.02);

    .header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .header-text {
        .header-title {
          font-size: var(--font-size-lg);
          font-weight: var(--font-weight-semibold);
          color: var(--text-color-black);
          margin: 0 0 var(--spacing-xs) 0;
        }

        .header-description {
          font-size: var(--font-size-sm);
          color: var(--text-light-dark);
          margin: 0;
        }
      }

      .close-button {
        padding: var(--spacing-sm);
        color: var(--text-light-dark);
        background: transparent;
        border: none;
        cursor: pointer;
        transition: color 0.15s ease;

        &:hover {
          color: var(--text-color-black);
        }

        &:focus {
          outline: none;
          box-shadow:
            0 0 0 2px var(--color-primary),
            0 0 0 4px rgba(19, 94, 150, 0.1);
          border-radius: var(--border-radius-xs);
        }

        .close-icon {
          width: var(--icon-size-sm);
          height: var(--icon-size-sm);
          stroke-width: 2;
        }
      }
    }
  }

  // Preview Content
  .preview-content {
    padding: var(--spacing-lg);

    .table-container {
      overflow-x: auto;

      .preview-table {
        width: 100%;
        border-collapse: collapse;

        // Table Header
        .table-header {
          .header-row {
            border-bottom: var(--normal-sec-border);

            .header-cell {
              text-align: left;
              padding: var(--spacing-md);
              font-size: var(--font-size-sm);
              font-weight: var(--font-weight-medium);
              color: var(--text-color-black);
              background: rgba(19, 94, 150, 0.02);
              border-right: var(--normal-sec-border);

              &:last-child {
                border-right: none;
              }

              .header-content {
                display: flex;
                align-items: center;
                gap: 0.5rem;

                .order-badge {
                  font-size: var(--font-size-xs);
                  color: var(--color-primary);
                  background: var(--color-secondary);
                  padding: var(--spacing-xxs) var(--spacing-xsm);
                  border-radius: var(--border-radius-xs);
                }

                .field-name {
                  font-weight: var(--font-weight-medium);
                }

                .required-indicator {
                  width: var(--spacing-sm);
                  height: var(--spacing-sm);
                  color: var(--color-danger);
                  stroke-width: 3;
                }
              }
            }
          }
        }

        // Table Body
        .table-body {
          .data-row {
            border-bottom: var(--normal-sec-border);

            &:nth-child(even) {
              background: var(--color-white);
            }

            &:nth-child(odd) {
              background: var(--color-off-white);
            }

            .data-cell {
              padding: var(--spacing-md);
              font-size: var(--font-size-sm);
              color: var(--text-color-black);
              border-right: var(--normal-sec-border);

              &:last-child {
                border-right: none;
              }
            }
          }
        }
      }
    }

    // Preview Info
    .preview-info {
      margin-top: var(--spacing-lg);
      padding: var(--spacing-md);
      background: var(--color-secondary);
      border-radius: var(--border-radius-md);

      .info-content {
        display: flex;
        align-items: flex-start;
        gap: var(--spacing-sm);

        .info-icon {
          width: var(--icon-size-xs);
          height: var(--icon-size-xs);
          color: var(--color-primary);
          stroke-width: 2;
          margin-top: var(--spacing-xxs);
          flex-shrink: 0;
        }

        .info-text {
          font-size: var(--font-size-sm);

          .info-title {
            color: var(--color-primary);
            font-weight: var(--font-weight-medium);
            margin: 0 0 var(--spacing-xs) 0;
          }

          .info-description {
            color: var(--color-green);
            margin: 0;
            line-height: var(--line-height-base);
          }
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .export-preview {
    .preview-header {
      .header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;

        .close-button {
          align-self: flex-end;
        }
      }
    }

    .preview-content {
      .table-container {
        .preview-table {
          .table-header {
            .header-row {
              .header-cell {
                .header-content {
                  flex-direction: column;
                  align-items: flex-start;
                  gap: 0.25rem;
                }
              }
            }
          }
        }
      }

      .preview-info {
        .info-content {
          flex-direction: column;
          gap: 0.5rem;

          .info-icon {
            margin-top: 0;
          }
        }
      }
    }
  }
}

// Print Styles
@media print {
  .export-preview {
    .preview-header {
      .header-content {
        .close-button {
          display: none;
        }
      }
    }

    .preview-content {
      .table-container {
        overflow: visible;

        .preview-table {
          .table-header {
            .header-row {
              .header-cell {
                background: var(--color-white) !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
              }
            }
          }

          .table-body {
            .data-row {
              &:nth-child(even) {
                background: var(--color-white) !important;
              }

              &:nth-child(odd) {
                background: rgba(19, 94, 150, 0.05) !important;
              }

              -webkit-print-color-adjust: exact;
              color-adjust: exact;
            }
          }
        }
      }

      .preview-info {
        background: rgba(57, 89, 110, 0.1) !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
      }
    }
  }
}
