import { Box, Typography } from '@mui/material';
import {
  CardCvcElement,
  CardExpiryElement,
  CardNumberElement,
} from '@stripe/react-stripe-js';
import './cardElementField.scss';

const stripFieldStyle = {
  base: {
    fontSize: '14px',
    fontWeight: '400',
    color: '#000000',
    lineHeight: '1.25rem',
    fontFamily: `'Poppins', sans-serif`,
    '::placeholder': {
      color: '#8b8d97',
    },
    iconColor: '#006bff',
  },
  invalid: {
    color: '#d32f2f',
    iconColor: '#d32f2f',
  },
};

export const CustomCardNumberElement = ({
  label,
  required,
  error,
  helperText,
  onChange,
  placeholder,
  disabled,
}) => {
  return (
    <Box className={`custom-card-element-wrapper ${error ? 'Mui-error' : ''}`}>
      {label && (
        <Typography
          className={`field-label ${error ? 'error-label' : ''}`}
          variant="body2"
        >
          {label}
          {required && <span className="required">*</span>}
        </Typography>
      )}
      <Box
        className={`card-element-container ${disabled ? 'is-disabled' : ''}`}
      >
        <CardNumberElement
          onChange={onChange}
          options={{
            style: stripFieldStyle,
            placeholder: placeholder || '1234 1234 1234 1234',
            disabled: disabled || false,
          }}
        />
      </Box>
      {error && helperText && (
        <Typography className="field-error-text">{helperText}</Typography>
      )}
    </Box>
  );
};

export const CustomCardExpiryElement = ({
  label,
  required,
  error,
  helperText,
  onChange,
  placeholder,
  disabled,
}) => {
  return (
    <Box className={`custom-card-element-wrapper ${error ? 'Mui-error' : ''}`}>
      {label && (
        <Typography
          className={`field-label ${error ? 'error-label' : ''}`}
          variant="body2"
        >
          {label}
          {required && <span className="required">*</span>}
        </Typography>
      )}
      <Box
        className={`card-element-container ${disabled ? 'is-disabled' : ''}`}
      >
        <CardExpiryElement
          onChange={onChange}
          options={{
            style: stripFieldStyle,
            placeholder: placeholder || 'MM/YY',
            disabled: disabled || false,
          }}
        />
      </Box>
      {error && helperText && (
        <Typography className="field-error-text">{helperText}</Typography>
      )}
    </Box>
  );
};

export const CustomCardCvcElement = ({
  label,
  required,
  error,
  helperText,
  onChange,
  placeholder,
  disabled,
}) => {
  return (
    <Box className={`custom-card-element-wrapper ${error ? 'Mui-error' : ''}`}>
      {label && (
        <Typography
          className={`field-label ${error ? 'error-label' : ''}`}
          variant="body2"
        >
          {label}
          {required && <span className="required">*</span>}
        </Typography>
      )}
      <Box
        className={`card-element-container ${disabled ? 'is-disabled' : ''}`}
      >
        <CardCvcElement
          onChange={onChange}
          options={{
            style: stripFieldStyle,
            placeholder: placeholder || 'CVC',
            disabled: disabled || false,
          }}
        />
      </Box>
      {error && helperText && (
        <Typography className="field-error-text">{helperText}</Typography>
      )}
    </Box>
  );
};
