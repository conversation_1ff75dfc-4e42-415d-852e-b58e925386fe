'use client';

import React, { useContext, useEffect, useState } from 'react';
import { Box, Typography, CircularProgress, Tooltip } from '@mui/material';
import AuthContext from '@/helper/authcontext';
import { DataGrid } from '@mui/x-data-grid';
import CustomPagination from '@/components/UI/customPagination';
import CustomButton from '@/components/UI/CustomButton';
import Searchbar from '@/components/UI/CustomSearch';
import { setApiMessage, DateFormat } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import DialogBox from '@/components/UI/Modalbox';
// import VisibilityIcon from '@mui/icons-material/Visibility';
import ViewIcon from '@/components/ActionIcons/ViewIcon';
import CommonUserDetails from '@/components/UI/CommonUserDetails/index';
import NoDataView from '@/components/UI/NoDataView';
import './activity.scss';

export default function Activity() {
  const { authState, setUserdata } = useContext(AuthContext);
  const [activityList, setActivityList] = useState([{ id: '' }]);
  const [loader, setLoader] = useState(true);
  const [searchValue, setSearchValue] = useState('');
  const [toggleModal, setToggleModal] = useState(false);
  const [userAgentValue, setUserAgentValue] = useState('');
  const [page, setPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // List of activity
  const getActivityDetails = async (search, pageNo, Rpp) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.ACTIVITY_LOGS +
          `?search=${search}&page=${pageNo}&size=${Rpp ? Rpp : rowsPerPage}`
      );

      if (status === 200) {
        const activityData =
          data?.data &&
          data?.data?.length > 0 &&
          data?.data?.map((a, index) => {
            let newdata = JSON.parse(a?.new_data);
            return {
              ...a,
              user_id: a?.users?.id,
              id: index,
              user_full_name: a?.users?.user_full_name,
              user_email: a?.users?.user_email,
              branch_name: newdata?.branch_name ? newdata?.branch_name : '-',
              ip_address: a?.ip_address ? a?.ip_address : '-',
              new_data: newdata,
              department_name: newdata?.department_name
                ? newdata?.department_name
                : '-',
            };
          });
        setPage(data?.page);
        setTotalCount(data?.count);
        setActivityList(activityData ? activityData : []);
        setLoader(false);
      }
    } catch (error) {
      setLoader(false);
      setActivityList([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const columns = [
    {
      field: 'user_id',
      headerName: 'ID',
      width: 48,
      minWidth: 48,
      flex: 0,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      // renderCell: (params) => {
      //   return (
      //     <Box className="d-flex align-center h100">
      //       {params?.row?.users?.employment_number}
      //     </Box>
      //   );
      // }
    },
    {
      field: 'user_full_name',
      headerName: 'Name',
      width: 350,
      minWidth: 350,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <CommonUserDetails
            userData={params?.row?.users}
            searchValue={searchValue}
            page={page}
            rowsPerPage={rowsPerPage}
            authState={authState}
            setUserdata={setUserdata}
          />
        );
      },
    },
    {
      field: 'createdAt',
      headerName: 'Date & Time',
      width: 200,
      minWidth: 200,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-start h100">
            <Typography className="title-text text-ellipsis-line">
              {DateFormat(params?.value, 'datesWithhour')}
            </Typography>
          </Box>
        );
      },
    },
    // {
    //   field: 'user_email',
    //   headerName: 'Email Address',
    //   width: 200,
    //   minWidth: 200,
    //   flex: 1,
    //   sortable: false,
    //   headerAlign: 'center',
    //   align: 'center',
    //   renderCell: (params) => {
    //     return (
    //       <Box className="d-flex align-center justify-center ">
    //         {params?.value}
    //       </Box>
    //     );
    //   }
    // },
    // {
    //   field: 'branch_name',
    //   headerName: 'Branch Name',
    //   width: 200,
    //   minWidth: 200,
    //   flex: 1,
    //   sortable: false,
    //   headerAlign: 'center',
    //   align: 'center'
    // },
    // {
    //   field: 'department_name',
    //   headerName: 'Department Name',
    //   width: 200,
    //   minWidth: 200,
    //   flex: 1,
    //   sortable: false,
    //   headerAlign: 'center',
    //   align: 'center'
    // },
    // {
    //   field: 'olddata',
    //   headerName: 'Previous',
    //   width: 200,
    //   minWidth: 200,
    //   flex: 1,
    //   sortable: false,
    //   headerAlign: 'center',
    //   align: 'center'
    // },
    // {
    //   field: 'newData',
    //   headerName: 'Updated',
    //   width: 200,
    //   minWidth: 200,
    //   flex: 1,
    //   sortable: false,
    //   headerAlign: 'center',
    //   align: 'center'
    // },
    {
      field: 'activity_action',
      headerName: 'Activity actions',
      width: 200,
      minWidth: 200,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box className="text-ellipsis-line">
            {params?.row?.activity_table ? params?.row?.activity_table : ''}{' '}
            {params?.row?.activity_action ? params?.row?.activity_action : ''}
          </Box>
        );
      },
    },
    {
      field: 'ip_address',
      headerName: 'IP',
      width: 150,
      minWidth: 150,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center">
            {params?.value ? params?.value : '-'}
          </Box>
        );
      },
    },
    {
      field: 'location',
      headerName: 'Location',
      width: 150,
      minWidth: 150,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center">
            {params?.value ? params?.value : '-'}
          </Box>
        );
      },
    },
    {
      field: 'address',
      headerName: 'address',
      width: 150,
      minWidth: 150,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center">
            {params?.value ? params?.value : '-'}
          </Box>
        );
      },
    },
    {
      field: 'userAgent',
      headerName: 'User agent',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex actions align-center justify-center h100">
            <Tooltip
              title="User agent"
              arrow
              className="action-tooltip"
              placement="bottom"
              classes={{ tooltip: 'info-tooltip-container' }}
            >
              <Box>
                <ViewIcon
                  className="cursor-pointer"
                  onClick={() => {
                    setToggleModal(!toggleModal);
                    setUserAgentValue(params?.value);
                  }}
                />
              </Box>
            </Tooltip>
          </Box>
        );
      },
    },
    // {
    //   field: 'status',
    //   headerName: 'Login Status',
    //   width: 200,
    //   minWidth: 200,
    //   flex: 1,
    //   sortable: false,
    //   headerAlign: 'center',
    //   align: 'center',
    //   renderCell: (params) => {
    //     return (
    //       <Box className="d-flex align-center justify-center h100">
    //         {params?.value === 'Failed' ? (
    //           <Typography className="sub-title-text failed fw600">
    //             {' '}
    //             {params?.value}{' '}
    //           </Typography>
    //         ) : (
    //           <Typography className="sub-title-text success fw600">
    //             {' '}
    //             {params?.value}{' '}
    //           </Typography>
    //         )}
    //       </Box>
    //     );
    //   }
    // }
    // {
    //   field: 'actions',
    //   headerName: 'Actions',
    //   width: 100,
    //   minWidth: 100,
    //   flex: 1,
    //   sortable: false,
    //   headerAlign: 'center',
    //   align: 'center',
    //   renderCell: (params) => {
    //     // Custom actions icons for each row
    //     return (
    //       <Box className="d-flex justify-center actions">
    //         <EditIcon onClick={() => handleEdit(params)} />
    //         <DeleteIcon />
    //       </Box>
    //     );
    //   }
    // }
  ];

  const onPageChange = (newPage) => {
    setPage(newPage);
    getActivityDetails(searchValue, newPage);
  };
  const OnRowPerPage = (newPage) => {
    setRowsPerPage(newPage);
    setPage(1);
    getActivityDetails(searchValue, 1, newPage);
  };
  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      setPage(1);
      getActivityDetails(searchValue, 1);
    }
  };
  useEffect(() => {
    if (
      authState?.UserPermission?.activity_log === 1 ||
      authState?.UserPermission?.activity_log === 2
    ) {
      getActivityDetails('', 1);
    }
  }, [authState?.UserPermission?.activity_log]);
  return (
    <>
      <Box className="activity-logs">
        <Box className="d-flex justify-end pb8 gap-10">
          <Searchbar
            setSearchValue={setSearchValue}
            searchValue={searchValue}
            onKeyPress={handleKeyPress}
          />
          <CustomButton
            variant="contained"
            title="Search"
            fullWidth={false}
            onClick={() => {
              setPage(1);
              getActivityDetails(searchValue, 1);
            }}
          />
        </Box>
        <Box className="table-container table-layout">
          {loader ? (
            <Box className="content-loader">
              <CircularProgress className="loader" color="inherit" />
            </Box>
          ) : (
            <>
              {activityList && activityList?.length === 0 ? (
                <Box className="no-data d-flex align-center justify-center">
                  <NoDataView
                    title="No Activity Logs Found"
                    description="There is no Activity Logs data available at the moment."
                  />
                </Box>
              ) : (
                <>
                  <DataGrid
                    rows={activityList}
                    columns={columns}
                    pageSize={rowsPerPage}
                    checkboxSelection={false} // Disable default checkbox column
                    disableSelectionOnClick // Disable row selection on click
                    // selectedRows={selectedRows}
                    hideMenuIcon
                  />
                  <CustomPagination
                    currentPage={page}
                    // totalPages={totalPages}
                    totalCount={totalCount}
                    rowsPerPage={rowsPerPage}
                    onPageChange={onPageChange}
                    OnRowPerPage={OnRowPerPage}
                  />
                </>
              )}
            </>
          )}
        </Box>
        <DialogBox
          open={toggleModal}
          handleClose={() => {
            setToggleModal(!toggleModal);
            setUserAgentValue('');
          }}
          title="User Agent"
          className="staff-dialogbox"
          content={
            <Box>
              <Typography>{userAgentValue}</Typography>
            </Box>
          }
        />
      </Box>
    </>
  );
}
