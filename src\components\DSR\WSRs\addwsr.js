'use client';

import React, { useEffect, useState, useContext } from 'react';
import { Box, Typography } from '@mui/material';
import CustomButton from '@/components/UI/CustomButton';
import CustomSelect from '@/components/UI/CustomSelect';
import ReactDatePicker from '@/components/UI/ReactWeekDatePicker';
import {
  setApiMessage,
  TotalOfWSRArray,
  removeVAT,
} from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { useRouter } from 'next/navigation';
import { URLS } from '@/helper/constants/urls';
import AuthContext from '@/helper/authcontext';
import WSRCategory from '@/components/DSR/WSRs/wsrCategory';
import NoDataView from '@/components/UI/NoDataView';
import dayjs from 'dayjs';
import moment from 'moment';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import { fetchFromStorage, saveToStorage } from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import _ from 'lodash';
import '../dsr.scss';

export default function AddWSRPage() {
  const getWeekDates = () => {
    const currentDate = new Date();

    const startOfWeek = new Date(currentDate);
    const dayOfWeek = startOfWeek.getDay();
    const diffToMonday = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
    startOfWeek.setDate(startOfWeek.getDate() + diffToMonday);
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);

    return { startDate: startOfWeek, endDate: endOfWeek };
  };
  const { authState, AllListsData, userdata, setUserdata } =
    useContext(AuthContext);
  const router = useRouter();
  const [DsrData, setDsrData] = useState([]);
  const [DsrDataIncome, setDsrDataIncome] = useState([]);
  const [DsrDataOther, setDsrDataOther] = useState([]);
  const [VatValue, setVATValue] = useState([0, 20]);
  const [VAT, setVAT] = useState({
    NoneVat: '',
    VAT1: '',
    VAT2: '',
    AmountVAT1: '',
    AmountVAT2: '',
    diff1: '',
    diff2: '',
    TotalIncome: '',
  });
  const [loader, setLoader] = useState(false);
  const [checkloader, setCheckLoader] = useState(false);
  const [branch, setBranch] = useState();
  const [WSRDates, setDate] = useState(getWeekDates);
  const [random, setRandom] = useState();
  const [randomother, setRandomother] = useState();
  const [randomvat, setRandomvat] = useState();
  const [DsrExist, setISDsr] = useState(false);
  const [currency, setCurrency] = useState();
  const [isSubmit, setIsSubmit] = useState(false);
  const [VatIsView, setVatIsView] = useState(true);
  const [IsRemarkView, setIsRemarkView] = useState(true);

  // get WSR Data
  const getWSRData = async (branchID) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_DSR_DATA + `${branchID}?payment_type=wsr`
      );
      if (status === 200) {
        setLoader(false);

        const incomeData =
          data?.data &&
          data?.data?.length > 0 &&
          data?.data?.filter((f) => f?.payment_type_usage === 'income');
        incomeData && incomeData?.length > 0
          ? setDsrDataIncome(incomeData)
          : setDsrDataIncome([]);
        const otherData =
          data?.data &&
          data?.data?.length > 0 &&
          data?.data?.filter((f) => f?.payment_type_usage === 'other');
        otherData && otherData?.length > 0
          ? setDsrDataOther(otherData)
          : setDsrDataOther([]);
        data?.vat_per_data &&
          data?.vat_per_data?.length > 0 &&
          setVATValue(data?.vat_per_data);
        setDsrData(data?.data);
      }
    } catch (error) {
      setLoader(false);
      setDsrData([]);
      setDsrDataOther([]);
      setDsrDataIncome([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const WSRCheck = async (branch, wsrDate) => {
    setCheckLoader(true);
    const sendData = {
      branch_id: branch,
      wsr_start_date: dayjs(wsrDate?.startDate).format('YYYY-MM-DD'),
      wsr_end_date: dayjs(wsrDate?.endDate).format('YYYY-MM-DD'),
    };
    try {
      const { status, data } = await axiosInstance.post(
        URLS?.GET_WSR_CHECK,
        sendData
      );
      if (status === 200 || status === 202) {
        if (data?.status) {
          setCheckLoader(false);
          setISDsr(false);
          getWSRData(branch);
          // setApiMessage('success', data?.message);
        } else {
          setCheckLoader(false);
          setISDsr(true);
          setApiMessage('error', data?.message);
          setDsrData([]);
          setDsrDataIncome([]);
          setDsrDataOther([]);
        }
      }
    } catch (error) {
      setCheckLoader(false);
      setISDsr(true);
      setDsrData([]);
      setDsrDataIncome([]);
      setDsrDataOther([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const ValueOfTotal = (value) => {
    const parsedValue = parseFloat(value);
    if (!Number.isNaN(parsedValue)) {
      return parsedValue;
    }
    // if (parseFloat(value) && parseFloat(value) !== NaN) {
    //   return parseFloat(value);
    // }
    return 0;
  };
  //Total without VAT
  const TotalWithoutVAT = () => {
    return ValueOfTotal(
      parseFloat(
        removeVAT(
          ValueOfTotal(parseFloat(VAT?.VAT2)),
          VatValue && VatValue?.[1] ? VatValue?.[1] : 0
        )?.netAmount +
          ValueOfTotal(parseFloat(VAT?.VAT1)) +
          ValueOfTotal(parseFloat(VAT?.NoneVat))
      )
    ).toFixed(2);
  };
  const AddWSR = async () => {
    let dsrmerge = _.concat(DsrDataIncome, DsrDataOther);
    let vatCal = VAT;
    vatCal.TotalIncome = TotalOfWSRArray(DsrDataIncome)
      ? TotalOfWSRArray(DsrDataIncome)
      : 0;
    vatCal.AmountVAT1 =
      ValueOfTotal(parseFloat(VAT?.VAT2)) && VatValue && VatValue?.[1]
        ? removeVAT(ValueOfTotal(parseFloat(VAT?.VAT2)), VatValue?.[1])
            ?.vatAmount
        : 0;
    vatCal.AmountVAT2 =
      (ValueOfTotal(parseFloat(VAT?.VAT2)) ||
        ValueOfTotal(parseFloat(VAT?.VAT2)) === 0 ||
        ValueOfTotal(parseFloat(VAT?.VAT1)) ||
        ValueOfTotal(parseFloat(VAT?.VAT1)) === 0 ||
        ValueOfTotal(parseFloat(VAT?.NoneVat)) ||
        ValueOfTotal(parseFloat(VAT?.NoneVat)) === 0) &&
      VatValue &&
      VatValue?.[1]
        ? TotalWithoutVAT()
        : 0;
    vatCal.diff2 =
      VAT?.diff1 && VAT?.diff1 !== ''
        ? Math.abs(
            (
              removeVAT(ValueOfTotal(parseFloat(VAT?.VAT2)), VatValue?.[1])
                ?.vatAmount - ValueOfTotal(parseFloat(VAT?.diff1))
            ).toFixed(2)
          )
        : '';
    const sendData = {
      branch_id:
        authState?.web_user_active_role_id === 7 ||
        authState?.web_user_active_role_id === 14
          ? authState?.branch?.id
          : branch,
      wsr_start_date: dayjs(WSRDates?.startDate).format('YYYY-MM-DD'),
      wsr_end_date: dayjs(WSRDates?.endDate).format('YYYY-MM-DD'),
      data: dsrmerge,
      wsr_amount_total: vatCal,
      current_datetime: moment(Date()).format('YYYY-MM-DD HH:mm:ss'),
    };
    try {
      const { status, data } = await axiosInstance.post(
        URLS?.ADD_WSR,
        sendData
      );

      if (status === 200) {
        if (data?.status) {
          setLoader(false);
          setApiMessage('success', data?.message);
          router?.push('/wsr');
        } else {
          setLoader(false);
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  useEffect(() => {
    setDsrDataIncome(DsrDataIncome);
  }, [random]);
  useEffect(() => {
    setDsrDataOther(DsrDataOther);
  }, [randomother]);
  useEffect(() => {
    setVAT(VAT);
  }, [randomvat]);
  useEffect(() => {
    authState?.currency_details && setCurrency(authState?.currency_details);
  }, [authState]);

  //Total of VAT amount
  const totalofALLVat = () => {
    const sum =
      ValueOfTotal(parseFloat(VAT?.NoneVat)) +
      ValueOfTotal(parseFloat(VAT?.VAT1)) +
      ValueOfTotal(parseFloat(VAT?.VAT2));
    return parseFloat(sum.toFixed(2));
  };

  const getEndOfCurrentWeek = () => {
    const today = new Date();
    const dayOfWeek = today.getDay();
    const diffToSunday = 6 - dayOfWeek; // Calculate days to next Sunday
    const endOfWeek = new Date(today);
    endOfWeek.setDate(today.getDate() + diffToSunday);
    return endOfWeek;
  };
  const getStartOfPreviousWeek = () => {
    const today = new Date();
    const dayOfWeek = today.getDay();
    const diffToMonday = dayOfWeek === 0 ? -13 : 1 - dayOfWeek - 7; // Go back one week
    const startOfPreviousWeek = new Date(today);
    startOfPreviousWeek.setDate(today.getDate() + diffToMonday);
    return startOfPreviousWeek;
  };
  useEffect(() => {
    if (
      authState?.web_user_active_role_id === 7 ||
      authState?.web_user_active_role_id === 14
    ) {
      getWSRData(authState?.branch?.id);
    }
  }, [authState?.web_user_active_role_id]);
  const oldDSR = fetchFromStorage(identifiers?.RedirectData)
    ? fetchFromStorage(identifiers?.RedirectData)
    : userdata;

  useEffect(() => {
    if (oldDSR?.IsFromUser === undefined && oldDSR?.page !== undefined) {
      setUserdata({ ...userdata, IsFromUser: true });
      saveToStorage(identifiers?.RedirectData, {
        ...oldDSR,
        IsFromUser: true,
      });
    }
  }, [oldDSR]);
  return (
    <Box>
      <Box className="dsr-page-section Add-dsr-section">
        <ArrowBackIosIcon
          className="mb8 cursor-pointer"
          onClick={() => {
            setTimeout(() => {
              router?.push('/wsr');
            }, 1000);
          }}
        />{' '}
        {authState?.web_user_active_role_id === 7 ||
        authState?.web_user_active_role_id === 14 ? (
          <>
            {authState?.branch && authState?.branch?.branch_name && (
              <Box className="d-flex align-center">
                <Typography className="title-text fw400 user-date">
                  <span className="fw600">Branch : </span>
                  <span>{authState?.branch?.branch_name}</span>
                </Typography>
              </Box>
            )}

            <Box className="d-flex align-center pt32">
              <Box>
                <ReactDatePicker
                  label="Week (DD/MM/YYYY - DD/MM/YYYY)"
                  name="date"
                  value={WSRDates}
                  disabled={checkloader}
                  disableFuture={true}
                  onChange={(date) => {
                    setDate({
                      startDate: date?.startOfWeek,
                      endDate: date?.endOfWeek,
                    });
                    branch &&
                      WSRCheck(branch, {
                        startDate: date?.startOfWeek,
                        endDate: date?.endOfWeek,
                      });
                  }}
                  inputVariant="outlined"
                  isClearable={false}
                  showWeekNumbers={true}
                  showWeekPicker={true}
                  maxDate={getEndOfCurrentWeek()}
                  minDate={getStartOfPreviousWeek()}
                />
              </Box>
            </Box>
          </>
        ) : (
          <>
            <Box className="display-grid-branch pt8">
              <Box>
                <CustomSelect
                  placeholder="Select Branch"
                  showDot
                  options={AllListsData?.ActiveBranchList}
                  value={
                    AllListsData?.ActiveBranchList?.find((opt) => {
                      return opt?.value === branch;
                    }) || ''
                  }
                  name="branch"
                  className={checkloader ? 'disabled-select' : ''}
                  disabled={checkloader}
                  onChange={(e) => {
                    setBranch(e?.value);
                    WSRCheck(e?.value, WSRDates);
                  }}
                  label={<span>Branch</span>}
                />
              </Box>
              <Box>
                <ReactDatePicker
                  label="Week (DD/MM/YYYY - DD/MM/YYYY)"
                  name="date"
                  value={WSRDates}
                  disabled={checkloader}
                  disableFuture={true}
                  onChange={(date) => {
                    setDate({
                      startDate: date?.startOfWeek,
                      endDate: date?.endOfWeek,
                    });
                    branch &&
                      WSRCheck(branch, {
                        startDate: date?.startOfWeek,
                        endDate: date?.endOfWeek,
                      });
                  }}
                  inputVariant="outlined"
                  isClearable={false}
                  showWeekNumbers={true}
                  showWeekPicker={true}
                  maxDate={getEndOfCurrentWeek()}
                />
              </Box>
            </Box>
          </>
        )}
        {(!DsrData || DsrData?.length === 0) &&
          !DsrExist &&
          !loader &&
          (branch ||
            authState?.web_user_active_role_id === 7 ||
            authState?.web_user_active_role_id === 14) && (
            <Box className="no-data d-flex align-center justify-center">
              <NoDataView title="Not any categories created/selected in this branch." />
            </Box>
          )}
        <WSRCategory
          DsrDataIncome={DsrDataIncome}
          setVatIsView={setVatIsView}
          VatIsView={VatIsView}
          setIsRemarkView={setIsRemarkView}
          IsRemarkView={IsRemarkView}
          setIsSubmit={setIsSubmit}
          setDsrDataIncome={setDsrDataIncome}
          setRandom={setRandom}
          setDsrDataOther={setDsrDataOther}
          setRandomother={setRandomother}
          DsrDataOther={DsrDataOther}
          currency={currency}
          VAT={VAT}
          setVAT={setVAT}
          setRandomvat={setRandomvat}
          VatValue={VatValue}
          ValueOfTotal={ValueOfTotal}
          totalofALLVat={totalofALLVat}
          isSubmit={isSubmit}
          isEdit={false}
        />
        <Box className="mt24">
          {' '}
          <CustomButton
            fullWidth={false}
            variant="contained"
            disabled={
              loader ||
              DsrExist ||
              checkloader ||
              !DsrData ||
              DsrData?.length === 0 ||
              (!branch &&
                authState?.web_user_active_role_id !== 7 &&
                authState?.web_user_active_role_id !== 14)
            }
            onClick={() => {
              setIsSubmit(true);
              if (VatIsView && DsrDataIncome?.length > 0) {
                totalofALLVat() === TotalOfWSRArray(DsrDataIncome) &&
                  (!isNaN(parseFloat(VAT?.VAT1)) ||
                    !isNaN(parseFloat(VAT?.VAT2)) ||
                    !isNaN(parseFloat(VAT?.NoneVat)) ||
                    TotalOfWSRArray(DsrDataIncome) === 0) &&
                  AddWSR();
              } else {
                AddWSR();
              }
            }}
            title={`${loader ? 'Adding...' : 'Add WSR'}`}
          />
        </Box>
      </Box>
    </Box>
  );
}
