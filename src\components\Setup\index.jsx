'use client';
import React, { useContext, useLayoutEffect, useState } from 'react';
import { Box, Divider, Typography } from '@mui/material';
import SideMenuList from '@/components/UI/SideMenuList';
import { useRouter, useSearchParams } from 'next/navigation';
import CustomTabs from '../UI/CustomTabs';
import { setupMenuList } from '@/helper/common/commonMenus';
import AuthContext from '@/helper/authcontext';
import { getHighLevelManager } from '@/helper/common/commonFunctions';

const SetUp = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [activeMenuItem, setActiveMenuItem] = useState(1);
  const [activeTab, setActiveTab] = useState(1);
  const [activeItemSlug, setActiveItemSlug] = useState('company_settings');
  const queryParams = new URLSearchParams(searchParams);
  const isSetup = searchParams.get('is_setup');
  const isActiveTab = searchParams.get('is_tab');
  const { authState } = useContext(AuthContext);
  const { isHighLevelManager } = getHighLevelManager(authState);

  useLayoutEffect(() => {
    if (authState && authState?.UserPermission) {
      const isPermission = setupMenuList?.find(
        (item) =>
          item?.permission &&
          (authState?.UserPermission?.[item?.permission] === 2 ||
            authState?.UserPermission?.[item?.permission] === 1)
      );
      const isCheckPermission = setupMenuList?.find(
        (item) => item?.id === Number(isSetup)
      );
      if (
        authState?.UserPermission?.[isCheckPermission?.permission] !== 2 &&
        authState?.UserPermission?.[isCheckPermission?.permission] !== 1
      ) {
        router.push(`/org/setup?is_setup=${isPermission?.id}&is_tab=1`);
        setActiveMenuItem(Number(isPermission?.id));
        setActiveItemSlug(isPermission?.slug);
      } else {
        const isActiveSlug = setupMenuList?.find(
          (item) => item?.id === Number(isSetup)
        );
        setActiveMenuItem(Number(isSetup || 1));
        setActiveItemSlug(isActiveSlug?.slug);
      }
    }
  }, [authState, isSetup]);

  useLayoutEffect(() => {
    const tabIndex = Number(isActiveTab) || 1;
    if (
      activeItemSlug === 'dsr_settings' &&
      !isHighLevelManager &&
      tabIndex === 1
    ) {
      // queryParams.set('is_setup', 13);
      // queryParams.set('is_tab', 2);
      // router.push(`?${queryParams.toString()}`);
      router.push(`/org/setup?is_setup=13&is_tab=2`);
    } else {
      setActiveTab(tabIndex);
    }
  }, [isActiveTab, activeItemSlug, isHighLevelManager]);

  const handleActiveMenuItem = (item) => {
    setActiveMenuItem(item?.id);
    setActiveItemSlug(item?.slug);
    setActiveTab(1);
    // queryParams.set('is_setup', item?.id);
    // queryParams.set('is_tab', 1);
    // router.push(`?${queryParams.toString()}`);
    router.push(`/org/setup?is_setup=${item?.id}&is_tab=1`);
  };

  const handleTabChange = (val) => {
    setActiveTab(val);
    queryParams.set('is_tab', val);
    router.push(`?${queryParams.toString()}`);
  };

  const getCurrentTabs = () => {
    // const currentItem = setupMenuList?.find(
    //   (item) => item?.slug === activeItemSlug
    // );
    // return currentItem?.tabs || setupMenuList[0]?.tabs;
    const currentItem = setupMenuList.find(
      (item) => item.slug === activeItemSlug
    );
    let tabs = currentItem?.tabs || [];
    if (activeItemSlug === 'dsr_settings' && !isHighLevelManager) {
      tabs = tabs.filter((tab) => tab.id !== 1); // Remove "DSR Settings" tab
    }
    return tabs;
  };

  const getCurrentContent = () => {
    const currentItem = setupMenuList.find(
      (item) => item.slug === activeItemSlug
    );
    const tab = currentItem?.tabs?.find((tab) => tab.id === activeTab);

    if (
      activeItemSlug === 'dsr_settings' &&
      tab?.id === 1 &&
      !isHighLevelManager
    ) {
      return (
        <Typography>You are not authorized to access this tab.</Typography>
      );
    }

    return tab?.component || currentItem?.component;
  };

  return (
    <Box className="section-wrapper">
      <Box className="section-left">
        <Typography className="sub-header-text section-left-title">
          Setup
        </Typography>
        <Divider />

        <SideMenuList
          menuItem={setupMenuList}
          activeId={activeMenuItem}
          onSelect={handleActiveMenuItem}
        />
      </Box>
      <Box className="section-right">
        <Box className="section-right-tab-header">
          <CustomTabs
            tabs={getCurrentTabs()}
            initialTab={activeTab}
            onTabChange={handleTabChange}
            isSetup
          />
        </Box>
        <Box className="section-right-content">{getCurrentContent()}</Box>
      </Box>
    </Box>
  );
};

export default SetUp;
