'use client';

import React, { useEffect, useState, useRef, useContext } from 'react';
import {
  Box,
  Typography,
  Divider,
  Checkbox,
  FormControlLabel,
  FormGroup,
} from '@mui/material';
import AuthContext from '@/helper/authcontext';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { useRouter, useSearchParams } from 'next/navigation';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomButton from '@/components/UI/CustomButton';
import CustomSelect from '@/components/UI/CustomSelect';
import Multiselect from '@/components/UI/CustomMultiSelect';
import { useDropzone } from 'react-dropzone';
import CollectionsIcon from '@mui/icons-material/Collections';
import CancelIcon from '@mui/icons-material/Cancel';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { staticOptions } from '@/helper/common/staticOptions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { identifiers } from '@/helper/constants/identifier';
import { URLS } from '@/helper/constants/urls';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import CustomEditor from '@/components/UI/CustomEditor';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import HeaderImage from '@/components/UI/ImageSecurity';
import _ from 'lodash';
import '../CreateContent/createcategory.scss';

export default function CreateMainCategory() {
  const [error, setError] = useState('');
  const searchParams = useSearchParams();
  const redirectId = searchParams.get('id');
  const UpdateId = searchParams.get('UpdateId');
  const CategoryType = searchParams.get('type');
  const { setfolderdata, planDetail, authState, setRestrictedLimitModal } =
    useContext(AuthContext);
  const formikRef = useRef(null);
  const router = useRouter();
  const [emptymedia, setEmptymedia] = useState(false);
  const [acceptFile, setAcceptedFiles] = useState([]);
  const [branchList, setBranchList] = useState([]);
  const [departmentList, setDepartmentList] = useState([]);
  const [editioContent, setEditorContent] = useState('');
  const [loader, setLoader] = useState(false);
  const [CategoryDetails, setCategoryDetails] = useState('');
  const { getRootProps, getInputProps } = useDropzone({
    accept: {
      'image/*': [],
    },
    multiple: false,
    onDrop: (acceptedFile, rejectedFiles) => {
      // Check if storage is full
      const totalStorage = planDetail?.total_storage || 0;
      const usedStorage = authState?.subscriptionUsage?.total_size_gb || 0;
      const fileSizeInGB = acceptedFile[0]?.size / (1024 * 1024 * 1024); // Convert bytes to GB

      if (usedStorage + fileSizeInGB > totalStorage) {
        setRestrictedLimitModal({
          storageLimit: true,
          totalStorage: planDetail?.total_storage,
          usedStorage: authState?.subscriptionUsage?.total_size_gb,
        });
        setAcceptedFiles([]);
        formikRef.current.setFieldValue('filename', '');
        return;
      }

      const maxSize = 5 * 1024 * 1024; // 5MB in bytes

      const largeFiles = acceptedFile.filter((file) => file.size > maxSize);
      if (rejectedFiles.length > 0) {
        // setApiMessage('error', 'Please upload pdf files only.');
        setError('Please upload Image only.');
        // setAcceptedFiles([]);
      } else if (largeFiles.length > 0) {
        setApiMessage('error', 'File size should be less than 5MB.');
      } else {
        setError(null);
        setAcceptedFiles(acceptedFile);
        formikRef.current.setFieldValue('filename', acceptedFile[0]);
      }
    },
  });

  // List of branches
  const getBranchList = async () => {
    // setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_BRANCH_LIST +
          `?search=${''}&page=${1}&size=${''}&branchStatus=active`
      );
      if (status === 200) {
        setLoader(false);
        const alloption = [{ label: 'Select all', value: 'all' }];
        let filterUserList = data?.data?.map((user) => ({
          label: user?.branch_name,
          value: user?.id,
          color: user?.branch_color,
        }));
        let mergeList = _.concat(alloption, filterUserList);
        setBranchList(mergeList);
      }
    } catch (error) {
      setLoader(false);
      console.error(error);
      setBranchList([]);
    }
  };

  // List of departments
  const getDepartmentList = async () => {
    // setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_DEPARTMENT_LIST +
          `?search=${''}&page=${1}&size=${''}&departmentStatus=active`
      );

      if (status === 200) {
        setLoader(false);
        const alloption = [{ label: 'Select all', value: 'all' }];
        let filterUserList = data?.data?.map((user) => ({
          label: user?.department_name,
          value: user?.id,
        }));
        let mergeList = _.concat(alloption, filterUserList);

        setDepartmentList(mergeList);
      }
    } catch (error) {
      setLoader(false);
      console.error(error);
      // setApiMessage('error', error?.response?.data?.message);
    }
  };

  const descriptionValue = (value) => {
    setEditorContent(value);
    formikRef.current.setFieldValue('description', value);
  };
  // List of Category details
  const getDocumentCategory = async (ID) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_ONE_CATEGORY + `${ID}`
      );
      if (status === 200) {
        setLoader(false);
        const branchList = data?.data?.branches?.map((i) => ({
          value: i?.id,
          label: i?.branch_name,
        }));
        const depList = data?.data?.departments?.map((i) => ({
          value: i?.id,
          label: i?.department_name,
        }));
        setCategoryDetails({
          ...data?.data,
          branches: branchList,
          departments: depList,
        });
        setEditorContent(data?.data?.category_description);
        if (data?.data?.category_image_url && data?.data?.category_image) {
          const filedata = [];
          filedata.push({
            type: 'image',
            link: data?.data?.category_image_url,
            name: data?.data?.category_image,
            isUpdated: true,
          });
          setAcceptedFiles(filedata);
          setEmptymedia(false);
          formikRef.current.setFieldValue(
            'filename',
            data?.data?.category_image
          );
        }
      }
    } catch (error) {
      setLoader(false);
      setCategoryDetails();
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  useEffect(() => {
    if (CategoryDetails?.branches && CategoryDetails?.branches?.length > 0) {
      const isall = branchList?.filter((b) => b?.value === 'all');
      const allbranch = branchList?.filter((b) => b?.value !== 'all');
      if (
        CategoryDetails?.branches?.length === allbranch?.length &&
        isall?.length > 0
      ) {
        const filterall = branchList?.filter((b) => b?.value !== 'all');
        setBranchList(filterall);
      } else if (
        CategoryDetails?.branches?.length !== allbranch?.length &&
        isall?.length === 0
      ) {
        const alloption = [{ label: 'Select all', value: 'all' }];

        let mergeList = _.concat(alloption, branchList);
        setBranchList(mergeList);
      }
    }
  }, [CategoryDetails?.branches?.length]);
  useEffect(() => {
    if (
      CategoryDetails?.departments &&
      CategoryDetails?.departments?.length > 0
    ) {
      const isall = departmentList?.filter((b) => b?.value === 'all');
      const alldepart = departmentList?.filter((b) => b?.value !== 'all');
      if (
        CategoryDetails?.departments?.length === alldepart?.length &&
        isall?.length > 0
      ) {
        const filterall = departmentList?.filter((b) => b?.value !== 'all');
        setDepartmentList(filterall);
      } else if (
        CategoryDetails?.departments?.length !== alldepart?.length &&
        isall?.length === 0
      ) {
        const alloption = [{ label: 'Select all', value: 'all' }];
        let mergeList = _.concat(alloption, departmentList);
        setDepartmentList(mergeList);
      }
    }
  }, [CategoryDetails?.departments?.length]);
  useEffect(() => {
    getDepartmentList();
    getBranchList();
    setfolderdata();
  }, []);

  useEffect(() => {
    if (UpdateId) {
      getDocumentCategory(UpdateId);
    }
  }, [UpdateId]);
  const scrollToError = (errors) => {
    const firstErrorField = Object.keys(errors)[0];

    // Try to find the element by ID first
    let element = document.getElementById(firstErrorField);

    // If not found by ID, try other selectors
    if (!element) {
      element = document.querySelector(`[name="${firstErrorField}"]`);
    }

    // If still not found or it's a hidden input, try to find the parent container
    if (!element || element.type === 'hidden') {
      const selectBox = document.querySelector(
        `.select-box[data-field="${firstErrorField}"]`
      );
      const errorField = document.querySelector(
        `.textfeild-error[data-field="${firstErrorField}"]`
      );
      const customField = document.querySelector(
        `[data-field="${firstErrorField}"]`
      );

      element = selectBox || errorField || customField;
    }
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'center' });
      // For MultiSelect, focus the select input if it exists
      const selectInput = element.querySelector('input') || element;
      selectInput.focus();
    }
  };
  return (
    <Box>
      <Box>
        <Box className="create-document-page">
          <Box className="d-flex align-center">
            <ArrowBackIosIcon
              className="cursor-pointer mt4"
              onClick={() => {
                router.push(`/document-staff/${redirectId}`);
              }}
            />
            <Typography className="title-sm fw600 pr8">
              {redirectId === 'all'
                ? UpdateId
                  ? 'Update Category'
                  : 'Create Category'
                : UpdateId
                  ? 'Update Folder'
                  : 'Create Folder'}
            </Typography>
          </Box>
          <Divider className="mb16 mt16" />

          <Formik
            innerRef={formikRef}
            initialValues={{
              name:
                UpdateId && CategoryDetails?.category_name
                  ? CategoryDetails?.category_name
                  : '',

              description:
                UpdateId && CategoryDetails?.category_description
                  ? CategoryDetails?.category_description
                  : '',
              depname:
                UpdateId && CategoryDetails?.departments
                  ? CategoryDetails?.departments
                  : [],
              branchname:
                UpdateId && CategoryDetails?.branches
                  ? CategoryDetails?.branches
                  : [],
              status:
                UpdateId && CategoryDetails?.category_status
                  ? CategoryDetails?.category_status
                  : 'active',
              isNotify: false,
              dashboard:
                UpdateId && CategoryDetails?.dashboard_view ? true : false,
              contentType:
                UpdateId && CategoryDetails?.category_use
                  ? CategoryDetails?.category_use
                  : '',
              filename:
                UpdateId && CategoryDetails?.category_image
                  ? CategoryDetails?.category_image
                  : '',
            }}
            enableReinitialize={true}
            validationSchema={Yup.object().shape({
              name: Yup.string().trim().required('This field is required'),
              status: Yup.string().trim().required('This field is required'),
              depname: Yup.array().min(
                1,
                'At least one department must be selected'
              ),
              // description: Yup.string()
              //   .trim()
              //   .required('This field is required'),
              branchname: Yup.array().min(
                1,
                'At least one branch must be selected'
              ),
              filename: Yup.string().trim().required('This field is required'),
              contentType:
                redirectId === 'all' &&
                Yup.string().trim().required('This field is required'),
              notify_msg: Yup.string().when('isNotify', {
                is: true,
                then: (schema) =>
                  schema.trim().required('This field is required'),
              }),
            })}
            onSubmit={async (requestData) => {
              setLoader(true);
              const body = new FormData();
              const bdata = requestData?.branchname?.map((m) => m?.value);
              const ddata = requestData?.depname?.map((m) => m?.value);
              requestData?.name &&
                body.append('category_name', requestData?.name);
              if (requestData?.contentType) {
                body.append('category_use', requestData?.contentType);
              } else if (CategoryType) {
                body.append('category_use', CategoryType);
              }
              body.append(
                'category_description',
                requestData?.description ? requestData?.description : null
              );

              // bdata &&
              //   bdata.length > 0 &&
              //   body.append('branch_ids', bdata.toString());
              requestData?.branchname &&
                bdata &&
                bdata?.length > 0 &&
                bdata?.map((r, index) => {
                  body.append(`branch_ids[${index}]`, r);
                });
              // ddata &&
              //   ddata.length > 0 &&
              //   body.append('department_ids', ddata.toString());
              requestData?.depname &&
                ddata &&
                ddata?.length > 0 &&
                ddata?.map((r, index) => {
                  body.append(`department_ids[${index}]`, r);
                });
              body.append(
                'category_status',
                requestData?.status ? requestData?.status : ''
              );
              acceptFile && acceptFile?.[0] && acceptFile?.[0]?.isUpdated
                ? body.append('category_image', '') //acceptFile?.[0]?.name
                : body.append('category_image', acceptFile?.[0]);
              body.append('is_notify', requestData?.isNotify);

              requestData?.isNotify &&
                requestData?.notify_msg &&
                body.append('notification_content', requestData?.notify_msg);
              redirectId === 'all' &&
                body.append('dashboard_view', requestData?.dashboard);
              body.append('category_type', 'folder');
              redirectId !== 'all' && body.append('parent_id', redirectId);
              const config = {
                headers: {
                  'Content-Type': 'multipart/form-data',
                },
              };
              const ApiUrl = UpdateId
                ? URLS.UPDATE_CAT_DOCUMENT + UpdateId
                : URLS.CREATE_CAT_DOCUMENT;
              const method = UpdateId ? 'put' : 'post';
              try {
                const { status, data } = await axiosInstance[method](
                  ApiUrl,
                  body,
                  config
                );
                if (status === 200 || status === 201) {
                  if (data?.status) {
                    setApiMessage('success', data?.message);
                    setTimeout(() => {
                      router.push(`/document-staff/${redirectId}`);
                    }, 2000);
                  } else {
                    setApiMessage('error', data?.message);
                  }
                  setLoader(false);
                }
              } catch (error) {
                setLoader(false);
                setApiMessage('error', error?.response?.data?.message);
              }
            }}
          >
            {({
              errors,
              touched,
              handleBlur,
              setFieldValue,
              values,
              handleSubmit,
              handleChange,
              // dirty,
              // isValid,
            }) => (
              <Form onSubmit={handleSubmit}>
                <Box className="create-content-grid">
                  <Box className="content-left">
                    {acceptFile && acceptFile?.length === 1 ? (
                      <Box className="logo-section">
                        <HeaderImage
                          imageUrl={
                            acceptFile &&
                            acceptFile?.length === 1 &&
                            acceptFile?.[0]?.isUpdated === true
                              ? acceptFile[0]?.link
                              : acceptFile?.[0]?.type?.includes('image')
                                ? URL.createObjectURL(acceptFile[0])
                                : null
                          }
                          alt="not found"
                          className="media"
                          type="lazyload"
                        />
                        <CancelIcon
                          className="cancel-icon cursor-pointer"
                          onClick={() => {
                            setAcceptedFiles([]);
                            setEmptymedia(true);
                            formikRef.current.setFieldValue('filename', '');
                          }}
                        />
                      </Box>
                    ) : (
                      <Box
                        className="upload-sec cursor-pointer text-align upload-category-sec"
                        id="filename"
                      >
                        <Box
                          {...getRootProps({ className: 'dropzone' })}
                          className="upload-area"
                        >
                          <CollectionsIcon />
                          <input {...getInputProps()} />
                          <Typography className="title-text upload-text">
                            Drop your image here
                          </Typography>
                        </Box>
                      </Box>
                    )}
                    {/* {acceptFile &&
                    acceptFile?.length === 1 &&
                    acceptFile?.[0]?.isUpdated !== true ? (
                      <Box className="uploaded-media-sec">
                        {uploadedmedia(acceptFile?.[0]?.type, acceptFile)}
                        <DeleteOutlineIcon
                          className="cursor-pointer"
                          onClick={() => {
                            setAcceptedFiles([]);
                            setEmptymedia(true);
                            formikRef.current.setFieldValue('filename', '');
                          }}
                        />
                      </Box>
                    ) : acceptFile && acceptFile?.length === 1 ? (
                      <Box className="uploaded-media-sec">
                        {uploadedmedia(acceptFile?.[0]?.type, acceptFile)}
                        <DeleteOutlineIcon
                          className="cursor-pointer"
                          onClick={() => {
                            setAcceptedFiles([]);
                            setEmptymedia(true);
                            formikRef.current.setFieldValue('filename', '');
                          }}
                        />
                      </Box>
                    ) : (
                      <></>
                    )} */}
                    {error && (
                      <Typography
                        variant="body2"
                        color="error"
                        className="other-field-error-text"
                      >
                        {error}
                      </Typography>
                    )}{' '}
                    {emptymedia && acceptFile && acceptFile?.length === 0 && (
                      <Typography
                        variant="body2"
                        color="error"
                        className="other-field-error-text"
                      >
                        This field is required
                      </Typography>
                    )}
                    <Box className="display-grid pb8 pt8">
                      <Box>
                        <CustomTextField
                          fullWidth
                          id="name"
                          name="name"
                          value={values?.name}
                          label="Title"
                          required
                          placeholder="Enter Title"
                          error={Boolean(touched.name && errors.name)}
                          helperText={touched.name && errors.name}
                          onBlur={handleBlur}
                          onChange={(e) => {
                            if (
                              e?.target.value === '' ||
                              e?.target.value?.length < 51
                            ) {
                              handleChange(e);
                            }
                          }}
                        />
                        <Typography className="sub-title-text fw400 text-align-end">
                          {values?.name?.length + ' | ' + 50}
                        </Typography>
                      </Box>
                      <Box />
                      <Box>
                        <Multiselect
                          placeholder="Branch name"
                          options={branchList}
                          showDot={true}
                          error={touched.branchname && errors.branchname}
                          helperText={touched.branchname && errors.branchname}
                          name="branchname"
                          isOptionWithColor={true}
                          value={values?.branchname}
                          onChange={(e) => {
                            const filterValue = e?.find(
                              (f) => f?.value === 'all'
                            );
                            const branchValue = branchList?.filter(
                              (f) => f?.value !== 'all'
                            );
                            if (
                              filterValue ||
                              e?.length === branchValue?.length
                            ) {
                              const filterall = branchList?.filter(
                                (b) => b?.value !== 'all'
                              );
                              setBranchList(filterall);
                              const selectedValue = filterValue ? filterall : e;
                              setFieldValue('branchname', selectedValue);
                            }
                            if (!filterValue) {
                              const isAll = branchList?.find(
                                (f) => f?.value === 'all'
                              );
                              if (!isAll) {
                                const alloption = [
                                  { label: 'Select all', value: 'all' },
                                ];
                                let mergeList = _.concat(alloption, branchList);
                                setBranchList(mergeList);
                              }
                              setFieldValue('branchname', e);
                            }
                          }}
                          label={<span>Branch name</span>}
                          required
                        />
                      </Box>
                      <Box>
                        <Multiselect
                          placeholder="Department name"
                          options={departmentList}
                          name="depname"
                          value={values?.depname}
                          error={touched.depname && errors.depname}
                          helperText={touched.depname && errors.depname}
                          onChange={(e) => {
                            const filterValue = e?.find(
                              (f) => f?.value === 'all'
                            );
                            const depValue = departmentList?.filter(
                              (f) => f?.value !== 'all'
                            );
                            if (filterValue || e?.length === depValue?.length) {
                              const filterall = departmentList?.filter(
                                (b) => b?.value !== 'all'
                              );
                              setDepartmentList(filterall);
                              const selectedValue = filterValue ? filterall : e;
                              setFieldValue('depname', selectedValue);
                            }
                            if (!filterValue) {
                              const isAll = departmentList?.find(
                                (f) => f?.value === 'all'
                              );
                              if (!isAll) {
                                const alloption = [
                                  { label: 'Select all', value: 'all' },
                                ];
                                let mergeList = _.concat(
                                  alloption,
                                  departmentList
                                );
                                setDepartmentList(mergeList);
                              }
                              setFieldValue('depname', e);
                            }
                          }}
                          label={<span>Department name</span>}
                          required
                        />
                      </Box>
                      {redirectId === 'all' && (
                        <Box>
                          <CustomSelect
                            placeholder="Content type"
                            options={staticOptions?.CONTENT_TYPE}
                            value={
                              staticOptions?.CONTENT_TYPE?.find((opt) => {
                                return opt?.value === values?.contentType;
                              }) || ''
                            }
                            name="contentType"
                            error={touched?.contentType && errors?.contentType}
                            helperText={
                              touched?.contentType && errors?.contentType
                            }
                            onChange={(e) => {
                              setFieldValue('contentType', e?.value);
                            }}
                            disabled={UpdateId}
                            label={<span>Content type</span>}
                            required
                          />
                        </Box>
                      )}
                      <Box>
                        <CustomSelect
                          placeholder="Status"
                          options={identifiers?.STATUS}
                          value={
                            identifiers?.STATUS?.find((opt) => {
                              return opt?.value === values?.status;
                            }) || ''
                          }
                          name="status"
                          error={touched?.status && errors?.status}
                          helperText={touched?.status && errors?.status}
                          onChange={(e) => {
                            setFieldValue('status', e?.value);
                          }}
                          label={<span>Status</span>}
                          required
                        />
                      </Box>
                    </Box>
                    {redirectId === 'all' && (
                      <FormGroup
                        className={
                          redirectId !== 'all'
                            ? 'pb24 form-checkbox'
                            : 'form-checkbox'
                        }
                      >
                        <FormControlLabel
                          control={
                            <Checkbox
                              className="check-box "
                              icon={
                                <CheckBoxOutlineBlankIcon className="uncheck-icon" />
                              }
                              checkedIcon={
                                <CheckBoxIcon className="check-icon" />
                              }
                              // onChange={handleChange}
                              disableRipple
                              // disabled={isUpdate}
                            />
                          }
                          name="dashboard"
                          className="check-box-form form-row max-content sub-title-text"
                          // name="checkedRolesIds"
                          checked={values?.dashboard}
                          onChange={() => {
                            setFieldValue('dashboard', !values?.dashboard);
                          }}
                          label="Display in Dashboard"
                        />
                      </FormGroup>
                    )}
                    {redirectId === 'all' && ( //!UpdateId &&
                      <>
                        <Box className="pb24">
                          <FormGroup className="pb8 form-checkbox">
                            <FormControlLabel
                              control={
                                <Checkbox
                                  className="check-box "
                                  icon={
                                    <CheckBoxOutlineBlankIcon className="uncheck-icon" />
                                  }
                                  checkedIcon={
                                    <CheckBoxIcon className="check-icon" />
                                  }
                                  // onChange={handleChange}
                                  disableRipple
                                  // disabled={isUpdate}
                                />
                              }
                              name="isNotify"
                              className="check-box-form form-row max-content sub-title-text"
                              // name="checkedRolesIds"
                              checked={values?.isNotify}
                              onChange={() => {
                                setFieldValue('isNotify', !values?.isNotify);
                                setFieldValue('notify_msg', '');
                              }}
                              label="Notify"
                            />
                          </FormGroup>

                          {values?.isNotify && (
                            <Box className="pb8">
                              <CustomTextField
                                id="notify_msg"
                                name="notify_msg"
                                multiline
                                rows={2}
                                onChange={(e) => {
                                  handleChange(e);
                                }}
                                fullWidth
                                placeholder="Message"
                                value={values?.notify_msg}
                                error={Boolean(
                                  touched.notify_msg && errors.notify_msg
                                )}
                                className="additional-textfeild"
                                label={<span>Message</span>}
                                required
                                // disabled={isUpdate}
                              />
                              {touched.notify_msg && errors.notify_msg && (
                                <Typography
                                  variant="body2"
                                  color="error"
                                  className="other-field-error-text"
                                >
                                  {errors.notify_msg}
                                </Typography>
                              )}
                            </Box>
                          )}
                        </Box>
                      </>
                    )}
                  </Box>

                  <Box>
                    <Box>
                      <Typography className="field-label">
                        Description
                      </Typography>
                      <CustomEditor
                        content={editioContent}
                        setContent={descriptionValue}
                        height="400px"
                      />
                      {/* {touched.description && errors.description && (
                    <Typography
                      variant="body2"
                      color="error"
                      className="other-field-error-text"
                    >
                      {errors.description}
                    </Typography>
                  )} */}
                    </Box>
                    {/* {(redirectId !== 'all' || UpdateId) && <Box className="pb24" />} */}
                  </Box>
                </Box>

                <Box className="form-actions-btn">
                  <CustomButton
                    type="submit"
                    variant="contained"
                    onClick={async () => {
                      if (formikRef?.current) {
                        const errors = await formikRef.current.validateForm();
                        if (Object.keys(errors).length > 0) {
                          scrollToError(errors);
                          formikRef.current.setSubmitting(false);
                        }
                        setEmptymedia(true);
                      }
                    }}
                    disabled={loader}
                    title={
                      UpdateId
                        ? `${
                            loader
                              ? 'Updating...'
                              : redirectId === 'all'
                                ? 'Update Category'
                                : 'Update Folder'
                          }`
                        : `${
                            loader
                              ? 'Creating...'
                              : redirectId === 'all'
                                ? 'Create Category'
                                : 'Create Folder'
                          }`
                    }
                  />
                </Box>
              </Form>
            )}
          </Formik>
        </Box>
      </Box>
    </Box>
  );
}
