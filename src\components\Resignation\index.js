'use client';
import React, { useContext, useEffect, useState } from 'react';
import { Box, Typography, Tooltip } from '@mui/material';
import AuthContext from '@/helper/authcontext';
import CustomButton from '@/components/UI/CustomButton';
import DialogBox from '@/components/UI/Modalbox';
import CustomSearch from '@/components/UI/CustomSearch';
import CustomSelect from '@/components/UI/CustomSelect';
import { DataGrid } from '@mui/x-data-grid';
import TuneIcon from '@mui/icons-material/Tune';
import CustomDatePicker from '@/components/UI/CustomDatePicker';
import { DateFormat, setApiMessage } from '@/helper/common/commonFunctions';
import { identifiers } from '@/helper/constants/identifier';
import dayjs from 'dayjs';
import RemarkIcon from '@/components/ActionIcons/RemarkIcon';
import ChecklistIcon from '@/components/ActionIcons/ChecklistIcon';
import { fetchFromStorage, removeFromStorage } from '@/helper/context';
import JoiningRemovingChecklist from '@/app/(auth)/(users)/(resignation)/resignation-remark/@joiningRemove';
import ContentLoader from '../UI/ContentLoader';
import NoDataView from '../UI/NoDataView';
import CustomOrgPagination from '../UI/customPagination';
import CommonUserDetails from '../UI/CommonUserDetails';
import {
  getResignationList,
  getOneResignation,
  updateResignation,
} from '@/services/resignationService';
import './resignationremark.scss';
import useRoleList from '@/hooks/useRoleList';
import { useBranches } from '@/hooks/useBranches';
import useDepartmentList from '@/hooks/useDepartmentList';
import ResRemark from '@/app/(auth)/(users)/(resignation)/resignation-remark/@resremark';

export default function ResignationRemark() {
  const { authState, userdata, setUserdata } = useContext(AuthContext);
  const { roleList, fetchRoleList } = useRoleList();
  const { branchList, getBranchList } = useBranches();
  const { departmentList, fetchDepartmentList } = useDepartmentList();
  const [filter, setFilter] = useState(false);
  const [createModal, setCreateModal] = useState(false);
  const [loader, setLoader] = useState(true);
  const [leaveRemarkList, setLeaveRemarkList] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [singleList, setSingleList] = useState('');
  const [searchValue, setSearchValue] = useState('');
  const [joinChecklist, setJoiningChecklist] = useState(false);
  const [isLoader, setIsLoader] = useState(false);
  const [page, setPage] = useState(1);
  const [filterData, setFilterData] = useState({
    status: '',
    branch: '',
    department: '',
    role: '',
    updatedDate: '',
    lastServingDate: '',
    appliedDate: '',
  });
  const [filterDataApplied, setFilterDataApplied] = useState({
    status: '',
    branch: '',
    department: '',
    role: '',
    updatedDate: '',
    lastServingDate: '',
    appliedDate: '',
  });
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const getOneResignationData = async (id) => {
    try {
      const response = await getOneResignation(id);
      if (response.success) {
        setSingleList(response.data);
        setCreateModal(!createModal);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const getResignationListData = async (page, searchValue, filter, Rpp) => {
    setLoader(true);
    try {
      const response = await getResignationList(
        page,
        searchValue,
        filter,
        Rpp ? Rpp : rowsPerPage
      );
      if (response.success) {
        setLeaveRemarkList(response.data);
        setCurrentPage(response.page);
        setTotalCount(response.count);
        removeFromStorage(identifiers?.RedirectData);
        setUserdata();
      } else {
        setLeaveRemarkList([]);
        setTotalCount(0);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setLeaveRemarkList([]);
      setTotalCount(0);
    } finally {
      setLoader(false);
    }
  };

  const handleResignationRemak = async (payload, id) => {
    try {
      const response = await updateResignation(id, payload);
      if (response.success) {
        setIsLoader(false);
        setCreateModal(!createModal);
        getResignationListData(currentPage, searchValue, filterDataApplied);
        setApiMessage('success', response.message);
      }
    } catch (error) {
      setIsLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const columns = [
    {
      field: 'id',
      headerName: 'ID',
      width: 48,
      minWidth: 48,
      flex: 0,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      // renderCell: (params) => {
      //   return (
      //     <Box className="d-flex align-center h100">
      //       {params?.row?.resign_user?.employment_number}
      //     </Box>
      //   );
      // }
    },
    {
      field: 'user_full_name',
      headerName: 'Name',
      width: 260,
      minWidth: 260,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        let userData = params?.row?.resign_user;
        return (
          <>
            <CommonUserDetails
              userData={userData}
              filterDataApplied={filterDataApplied}
              searchValue={searchValue}
              page={page}
              rowsPerPage={rowsPerPage}
              setUserdata={setUserdata}
              authState={authState}
              navigationProps={{ regignationRemark: true }}
            />
          </>
        );
      },
    },
    {
      field: 'createdAt',
      headerName: 'Applied Date',
      width: 200,
      minWidth: 200,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center">
            {params?.value ? DateFormat(params?.value, 'dates') : '-'}
            {/* {DateFormat(params?.value, 'dates') ?? '-'} */}
          </Box>
        );
      },
    },
    {
      field: 'updatedAt',
      headerName: 'Updated Date',
      width: 200,
      minWidth: 200,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center">
            {params?.value ? DateFormat(params?.value, 'dates') : '-'}
            {/* {DateFormat(params?.value, 'dates') ?? '-'} */}
          </Box>
        );
      },
    },
    {
      field: 'last_serving_date',
      headerName: 'Last Serving Date',
      width: 200,
      minWidth: 200,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center">
            {params?.value ? DateFormat(params?.value, 'dates') : '-'}
            {/* {DateFormat(params?.value, 'dates') ?? '-'} */}
          </Box>
        );
      },
    },
    {
      field: 'resignation_status',
      headerName: 'Status',
      width: 150,
      minWidth: 150,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100 text-capital">
            {params?.value === 'rejected' ? (
              <Typography className="sub-title-text failed fw600">
                {params?.value}
              </Typography>
            ) : params?.value === 'in-discussion' ? (
              <Typography className="sub-title-text success fw600">
                {params?.value}
              </Typography>
            ) : params?.value === 'pending' ? (
              <Typography className="sub-title-text draft fw600">
                {params?.value}
              </Typography>
            ) : params?.value === 'cancelled' ? (
              <Typography className="sub-title-text cancelled  fw600">
                {params?.value}
              </Typography>
            ) : (
              <Typography className="sub-title-text active-onboarding  fw600">
                {params?.value}
              </Typography>
            )}
          </Box>
        );
      },
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        // Custom actions icons for each row
        return (
          <Box className="d-flex align-center justify-center actions">
            {authState?.UserPermission?.resignation === 2 ? (
              <Box className="d-flex actions-width gap-sm">
                <Tooltip
                  title={
                    <Typography className="sub-title-text">
                      Review Request
                    </Typography>
                  }
                  arrow
                  classes={{
                    tooltip: 'info-tooltip-container',
                  }}
                >
                  <Box>
                    <RemarkIcon
                      onClick={() => {
                        getOneResignationData(params?.row?.id);
                      }}
                    />
                  </Box>
                </Tooltip>
                {params?.row?.resignation_status === 'accepted' && (
                  <Tooltip
                    title={
                      <Typography className="sub-title-text">
                        Leaving checklist
                      </Typography>
                    }
                    arrow
                    classes={{
                      tooltip: 'info-tooltip-container',
                    }}
                  >
                    <Box>
                      <ChecklistIcon
                        onClick={() => setJoiningChecklist(params?.row)}
                      />
                    </Box>
                  </Tooltip>
                )}
              </Box>
            ) : (
              <Box className="d-flex align-center ">
                <Tooltip
                  title={
                    <Typography className="sub-title-text">Remark</Typography>
                  }
                  arrow
                  classes={{
                    tooltip: 'info-tooltip-container',
                  }}
                >
                  <Box>
                    <RemarkIcon
                      onClick={() => {
                        getOneResignationData(params?.row?.id);
                      }}
                    />
                  </Box>
                </Tooltip>
              </Box>
            )}
          </Box>
        );
      },
    },
  ];

  const onPageChange = (newPage) => {
    setPage(newPage);
    setCurrentPage(newPage);
    getResignationListData(newPage, searchValue, filterDataApplied);
  };
  const OnRowPerPage = (newPage) => {
    setPage(1);
    setRowsPerPage(newPage);
    setCurrentPage(1);
    getResignationListData(1, searchValue, filterDataApplied, newPage);
  };

  const handleClearSearch = () => {
    setSearchValue('');
    getResignationListData(1, '', filterDataApplied);
  };

  const handleFilterData = (type) => {
    setFilter(false);
    if (type === 'apply') {
      getResignationListData(1, searchValue, filterData);
      setFilterDataApplied(filterData);
    } else {
      const clearFilter = {
        status: '',
        branch: '',
        department: '',
        role: '',
        updatedDate: '',
        lastServingDate: '',
        appliedDate: '',
      };
      setFilterData(clearFilter);
      setFilterDataApplied(clearFilter);
      getResignationListData(1, searchValue, clearFilter);
    }
  };
  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      setPage(1);
      handleFilterData('apply');
    }
  };

  useEffect(() => {
    if (
      fetchFromStorage(identifiers?.RedirectData) &&
      fetchFromStorage(identifiers?.RedirectData)?.IsFromUser &&
      fetchFromStorage(identifiers?.RedirectData)?.regignationRemark
    ) {
      const fdata = fetchFromStorage(identifiers?.RedirectData);
      setPage(fdata?.page);
      setFilterDataApplied(fdata?.filterData);
      setSearchValue(fdata?.searchValue);
      getResignationListData(
        fdata?.page,
        fdata?.searchValue,
        fdata?.filterData
      );
    } else if (
      userdata &&
      userdata?.IsFromUser &&
      userdata?.regignationRemark
    ) {
      const fdata = userdata;
      setPage(fdata?.page);
      setFilterDataApplied(fdata?.filterData);
      setSearchValue(fdata?.searchValue);
      getResignationListData(
        fdata?.page,
        fdata?.searchValue,
        fdata?.filterData
      );
    } else {
      removeFromStorage(identifiers?.RedirectData);
      setUserdata();
    }
  }, [
    fetchFromStorage(identifiers?.RedirectData)?.IsFromUser,
    userdata?.IsFromUser,
  ]);

  useEffect(() => {
    if (
      authState?.UserPermission?.resignation === 1 ||
      authState?.UserPermission?.resignation === 2
    ) {
      getBranchList();
      fetchDepartmentList();
      fetchRoleList();
      if (
        (!fetchFromStorage(identifiers?.RedirectData) &&
          userdata?.page === undefined &&
          fetchFromStorage(identifiers?.RedirectData)?.IsFromUser ===
            undefined &&
          userdata?.IsFromUser === undefined) ||
        !fetchFromStorage(identifiers?.RedirectData)?.regignationRemark
      ) {
        getResignationListData(page, searchValue, filterDataApplied);
      }
    }
  }, [authState?.UserPermission?.resignation]);

  return (
    <Box className="resignation-section">
      <Box className="d-flex align-center flex-wrap justify-end gap-sm">
        <CustomSearch
          fullWidth
          setSearchValue={setSearchValue}
          onKeyPress={handleKeyPress}
          searchValue={searchValue}
          isClearSearch
          handleClearSearch={() => handleClearSearch()}
        />
        <CustomButton
          variant="outlined"
          isIconOnly
          startIcon={
            <Tooltip
              title={
                <Typography className="sub-title-text">Apply Filter</Typography>
              }
              classes={{
                tooltip: 'info-tooltip-container',
              }}
              arrow
            >
              <TuneIcon />
            </Tooltip>
          }
          onClick={() => {
            setFilter(!filter);
          }}
        />
        <CustomButton
          variant="outlined"
          title="Search"
          fullWidth={false}
          onClick={() => {
            setPage(1);
            handleFilterData('apply');
          }}
        />
      </Box>
      <Box className="table-container table-layout">
        {loader ? (
          <ContentLoader />
        ) : leaveRemarkList && leaveRemarkList.length > 0 ? (
          <>
            <DataGrid
              rows={leaveRemarkList}
              columns={columns}
              pageSize={rowsPerPage}
              checkboxSelection={false}
              disableSelectionOnClick
              hideMenuIcon
            />
            <CustomOrgPagination
              currentPage={page}
              totalCount={totalCount}
              rowsPerPage={rowsPerPage}
              onPageChange={onPageChange}
              OnRowPerPage={OnRowPerPage}
            />
          </>
        ) : (
          leaveRemarkList &&
          leaveRemarkList.length === 0 && (
            <NoDataView
              title="No resignation found"
              description="There is no resignation available at the moment."
            />
          )
        )}
      </Box>
      <DialogBox
        open={filter}
        handleClose={() => {
          setFilter(!filter);
        }}
        title={'Resignation filter'}
        className="small-dialog-box-container"
        content={
          <Box>
            <Box className="pt8">
              <CustomSelect
                placeholder="Status"
                options={identifiers?.RESIGNATION_OPTIONS}
                value={
                  identifiers?.RESIGNATION_OPTIONS?.find((opt) => {
                    return opt?.value === filterData?.status;
                  }) || ''
                }
                onChange={(e) => {
                  setFilterData({
                    ...filterData,
                    status: e?.value,
                  });
                }}
                label={<span>Status</span>}
              />
            </Box>
            <Box className="pt8">
              <CustomSelect
                placeholder="Branch"
                name="branch"
                options={branchList?.map((branch) => ({
                  value: branch.id,
                  label: branch.name,
                  color: branch.color,
                }))}
                value={
                  branchList?.find((opt) => {
                    return opt?.id === filterData?.branch;
                  })
                    ? {
                        value: branchList?.find(
                          (opt) => opt?.id === filterData?.branch
                        )?.id,
                        label: branchList?.find(
                          (opt) => opt?.id === filterData?.branch
                        )?.name,
                        color: branchList?.find(
                          (opt) => opt?.id === filterData?.branch
                        )?.color,
                      }
                    : ''
                }
                onChange={(e) => {
                  setFilterData({
                    ...filterData,
                    branch: e?.value,
                  });
                }}
                label={<span>Branch</span>}
                showDot={true}
              />
            </Box>
            <Box className="pt8">
              <CustomSelect
                placeholder="Department"
                options={departmentList}
                value={
                  departmentList?.find((opt) => {
                    return opt?.value === filterData?.department;
                  }) || ''
                }
                onChange={(e) => {
                  setFilterData({
                    ...filterData,
                    department: e?.value,
                  });
                }}
                label={<span>Department</span>}
              />
            </Box>
            <Box className="pt8">
              <CustomSelect
                placeholder="Role"
                options={roleList}
                value={
                  roleList?.find((opt) => {
                    return opt?.value === filterData?.role;
                  }) || ''
                }
                onChange={(e) => {
                  setFilterData({
                    ...filterData,
                    role: e?.value,
                  });
                }}
                label={<span>Role</span>}
              />
            </Box>
            <Box className="pt8">
              <CustomDatePicker
                error={false}
                label={<span>Applied date(DD/MM/YYYY)</span>}
                name="appliedDate"
                value={dayjs(filterData?.appliedDate)}
                onChange={(date) => {
                  setFilterData({
                    ...filterData,
                    appliedDate: date,
                  });
                }}
                inputVariant="outlined"
              />
            </Box>
            <Box className="pt8">
              <CustomDatePicker
                error={false}
                label={<span>Updated date(DD/MM/YYYY)</span>}
                name="updatedDate"
                value={dayjs(filterData?.updatedDate)}
                onChange={(date) => {
                  setFilterData({
                    ...filterData,
                    updatedDate: date,
                  });
                }}
                inputVariant="outlined"
              />
            </Box>
            <Box className="pt8">
              <CustomDatePicker
                error={false}
                label={<span>Last Serving Date(DD/MM/YYYY)</span>}
                name="last_serving_date"
                value={dayjs(filterData?.lastServingDate)}
                onChange={(date) => {
                  setFilterData({
                    ...filterData,
                    lastServingDate: date,
                  });
                }}
                inputVariant="outlined"
              />
            </Box>
            <Box className="form-actions-btn mt16">
              <CustomButton
                type="submit"
                fontWeight="600"
                variant="outlined"
                title="Clear"
                onClick={() => {
                  setPage(1);
                  handleFilterData('cancel');
                }}
              />
              <CustomButton
                type="submit"
                variant="contained"
                title="Apply"
                onClick={() => {
                  setPage(1);
                  handleFilterData('apply');
                }}
              />
            </Box>
          </Box>
        }
      />
      <DialogBox
        open={createModal}
        handleClose={() => {
          setCreateModal(!createModal);
        }}
        title="Resignation Remarks"
        className="small-dialog-box-container resignation-remark-dialog"
        content={
          <>
            <ResRemark
              singleList={singleList}
              setToggleModal={setCreateModal}
              toggleModal={createModal}
              handleResignationRemak={handleResignationRemak}
              isLoader={isLoader}
              authState={authState}
            />
          </>
        }
      />
      <DialogBox
        open={joinChecklist}
        handleClose={() => {
          setJoiningChecklist(false);
        }}
        className="dialog-box-container leaving-checklist-dialog"
        title={'Leaving checklist'}
        content={
          <>
            <JoiningRemovingChecklist
              resignId={joinChecklist?.id}
              Userdetails={joinChecklist}
              setJoiningChecklist={setJoiningChecklist}
              getResignationList={getResignationListData}
              currentPage={currentPage}
              searchValue={searchValue}
              filterData={filterDataApplied}
            />
          </>
        }
      />
    </Box>
  );
}
