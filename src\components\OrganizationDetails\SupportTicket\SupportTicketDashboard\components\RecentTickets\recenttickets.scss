// Recent Tickets Component Styles - Using global variables only

.recent-tickets {
  height: 100%;
  display: flex;
  flex-direction: column;

  &__header {
    padding: var(--spacing-lg);
    border-bottom: var(--normal-sec-border);
    background-color: var(--color-white);
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
  }

  &__content {
    flex: 1;
    padding: var(--spacing-lg);
    overflow-y: auto;
  }

  &__list {
    display: flex;
    flex-direction: column;
  }

  &__item {
    padding: var(--spacing-none) var(--spacing-md) var(--spacing-md);
    background-color: var(--color-white);
    cursor: pointer;
  }

  &__item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-sm);
    gap: var(--spacing-sm);
  }

  &__item-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__item-badges {
    display: flex;
    gap: var(--spacing-sm);
  }

  &__empty {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
  }
}

// Urgent Tickets Component Styles - Using global variables only

.urgent-tickets {
  height: 100%;
  display: flex;
  flex-direction: column;

  &__header {
    padding: var(--spacing-lg);
    border-bottom: var(--normal-sec-border);
    background-color: var(--color-white);
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
  }

  &__content {
    flex: 1;
    padding: var(--spacing-lg);
    overflow-y: auto;
  }

  &__list {
    display: flex;
    flex-direction: column;
  }

  &__item {
    padding: var(--spacing-none) var(--spacing-md) var(--spacing-md);
    background-color: var(--color-white);
    cursor: pointer;
  }

  &__item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-sm);
    gap: var(--spacing-sm);
  }

  &__item-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__empty {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .recent-tickets {
    &__header,
    &__content {
      padding: var(--spacing-md);
    }

    &__item {
      padding: var(--spacing-sm);
    }

    &__item-header {
      flex-direction: column;
      align-items: flex-start;
      gap: var(--spacing-xs);
    }

    &__item-badges {
      flex-wrap: wrap;
    }
  }
}

@media (max-width: 768px) {
  .urgent-tickets {
    &__header,
    &__content {
      padding: var(--spacing-md);
    }

    &__item {
      padding: var(--spacing-sm);
    }

    &__item-header {
      flex-direction: column;
      align-items: flex-start;
      gap: var(--spacing-xs);
    }
  }
}
