.filter-container {
  .subcategories-wrap {
    .intergalactic-multiselect {
      .___SFilterTrigger_l8hvk_gg_ {
        .___SInner_oiek1_gg_ {
          .___SText_oiek1_gg_ {
            .multiple-category-wrap {
              display: flex;
              justify-content: space-between;
              align-items: center;
            }
          }
        }
      }
    }
  }
}

.select-input-wrap {
  .MuiCheckbox-root {
    padding: 3px;

    svg {
      height: 20px;
      width: 20px;
      fill: var(--icon-color-primary);
    }
  }

  .all-category-wrap {
    padding: 10px 0px 10px 10px;
    max-width: 213px;

    .main-category-wrap {
      font-size: 16px;
      color: var(--color-primary);
    }
  }
}
