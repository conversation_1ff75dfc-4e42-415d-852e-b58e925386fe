import React from 'react';
import CreatableSelect from 'react-select/creatable';
import { Box, FormLabel, Typography } from '@mui/material';
import '../CustomSelect/customSelect.scss';

const CustomCreateSelect = ({
  options = [],
  id,
  name,
  value,
  onChange,
  onCreateOption,
  placeholder = 'Select or create an option',
  isClearable = true,
  isMulti = false,
  isDisabled = false,
  label,
  className = '',
  isHeight = '230px',
  onBlur,
  error,
  helperText,
  required = false,
  menuPlacement = 'auto',
}) => {
  const customStyles = {
    control: (provided, { isDisabled }) => ({
      ...provided,
      minHeight: '45px',
      borderRadius: '8px',
      border: error ? '1px solid #d32f2f' : '1px solid #d9dae2',
      backgroundColor: isDisabled
        ? 'var(--color-white)'
        : provided.backgroundColor,
      boxShadow: 'none',
      ':hover': {
        borderColor: error ? '#d32f2f' : '#d9dae2',
      },
    }),
    singleValue: (provided) => ({
      ...provided,
    }),
    indicatorSeparator: () => ({
      display: 'none',
    }),
    placeholder: (provided) => ({
      ...provided,
    }),
    menu: (provided) => ({
      ...provided,
      maxHeight: '230px',
      boxShadow: '4px 4px 6px rgba(0, 0, 0, 0.1)',
      border: '1px solid #a6a8b1',
      zIndex: 9999,
    }),
    menuList: (provided) => ({
      ...provided,
      maxHeight: isHeight,
    }),
    option: (provided, { isSelected }) => ({
      ...provided,
      backgroundColor: isSelected
        ? 'var(--color-primary)'
        : provided.backgroundColor,
      color: isSelected ? 'var(--color-white)' : provided.color,
    }),
  };

  const handleSelectChange = (selectedOption) => {
    onChange(selectedOption);
  };

  return (
    <Box className={`custom-select-wrapper ${className}`}>
      {label && (
        <FormLabel
          htmlFor={name}
          id={name}
          className={`field-label ${error ? 'error-label' : ''}`}
        >
          {label}
          {required && <span className="required">*</span>}
        </FormLabel>
      )}

      <CreatableSelect
        id={id}
        name={name}
        value={value}
        options={options}
        onChange={handleSelectChange}
        onCreateOption={onCreateOption}
        placeholder={placeholder}
        isClearable={isClearable}
        isMulti={isMulti}
        isDisabled={isDisabled}
        styles={customStyles}
        onBlur={onBlur}
        menuPlacement={menuPlacement}
        className={`select-container ${error ? 'error-border' : ''}`}
        classNamePrefix="select"
      />

      {error && helperText && (
        <Typography className="select-field-error-text">
          {helperText}
        </Typography>
      )}
    </Box>
  );
};

export default CustomCreateSelect;
