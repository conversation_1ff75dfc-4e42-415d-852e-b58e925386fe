import React, { useState } from 'react';
import { Typography, Box } from '@mui/material';
import CustomSelect from '@/components/UI/CustomSelect';
import CustomButton from '@/components/UI/CustomButton';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstanceOrg from '@/helper/axios/axiosInstanceOrg';
import { ROTA_URLS } from '@/helper/constants/urls';
import PreLoader from '@/components/UI/Loader';
import './rotaMenumodal.scss';

const EmployeesGroupingModal = ({
  setIsToolsModal,
  getRotaShiftList,
  // setFilterData,
  filterData,
}) => {
  const [empGroupBy, setEmpGroupBy] = useState(filterData?.isGroupBy || '');

  const [isLoader, setIsLoader] = useState(false);

  const empGroupingOptions = [
    { value: '', label: 'No Grouping' },
    { value: 'role', label: 'Roles' },
  ];

  const handleEmpGrouping = async () => {
    const sendData = {
      groupBy: empGroupBy,
    };

    setIsLoader(true);
    try {
      const { status, data } = await axiosInstanceOrg.post(
        ROTA_URLS?.SHIFTS_CHANGE_GROUP_BY,
        sendData
      );
      if (status === 200 || status === 201) {
        setApiMessage('success', data?.message);
        getRotaShiftList();
        setIsLoader(false);
        setIsToolsModal({ isEmpGroupModal: false });
      }
    } catch (err) {
      setApiMessage('error', err?.response?.data?.message);
      setIsLoader(false);
    }
  };

  return (
    <Box className="emp-group-modal">
      {isLoader && <PreLoader />}

      <Box className="rota-group-info-section">
        <Typography variant="body1" className="rota-group-info-text">
          Changing this will affect all users on this account
        </Typography>
      </Box>

      <CustomSelect
        label=""
        name="group_by"
        placeholder="Select Group By"
        options={empGroupingOptions}
        value={
          empGroupingOptions?.find((opt) => opt?.value === empGroupBy) || ''
        }
        onChange={(selectedOption) => {
          setEmpGroupBy(selectedOption?.value || '');
          // setFilterData({
          //   ...filterData,
          //   isGroupBy: selectedOption?.value || '',
          // });
        }}
        menuPosition="fixed"
      />

      <Box className="emp-grp-action-section">
        <CustomButton
          variant="contained"
          className=""
          title="Save"
          onClick={handleEmpGrouping}
        />
        <CustomButton
          variant="outlined"
          className=""
          title="Cancel"
          onClick={() => setIsToolsModal({ isEmpGroupModal: false })}
        />
      </Box>
    </Box>
  );
};

export default EmployeesGroupingModal;
