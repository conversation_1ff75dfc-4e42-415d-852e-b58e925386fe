import React, { useState, useRef, useEffect } from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import './AudioPlayer.scss';

const AudioPlayer = ({ audioUrl, onClose }) => {
  const audioRef = useRef(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isMuted, setIsMuted] = useState(false);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const updateTime = () => setCurrentTime(audio.currentTime);
    const updateDuration = () => setDuration(audio.duration);
    const handleEnded = () => setIsPlaying(false);
    const handlePause = () => setIsPlaying(false);
    const handlePlay = () => setIsPlaying(true);

    audio.addEventListener('timeupdate', updateTime);
    audio.addEventListener('loadedmetadata', updateDuration);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('pause', handlePause);
    audio.addEventListener('play', handlePlay);

    return () => {
      audio.removeEventListener('timeupdate', updateTime);
      audio.removeEventListener('loadedmetadata', updateDuration);
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('pause', handlePause);
      audio.removeEventListener('play', handlePlay);
    };
  }, [audioUrl]);

  const togglePlay = () => {
    const audio = audioRef.current;
    if (isPlaying) {
      audio.pause();
    } else {
      audio.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleProgressChange = (e) => {
    const audio = audioRef.current;
    const newTime = (e.target.value / 100) * duration;
    audio.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const toggleMute = () => {
    const audio = audioRef.current;
    if (isMuted) {
      audio.volume = 1; // Restore to full volume
      setIsMuted(false);
    } else {
      audio.volume = 0;
      setIsMuted(true);
    }
  };

  const formatTime = (time) => {
    if (isNaN(time)) return '0:00';
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const progressPercentage = duration ? (currentTime / duration) * 100 : 0;

  return (
    <div className="audio-player">
      <div className="audio-player__content">
        <audio
          ref={audioRef}
          preload="metadata"
          crossOrigin="anonymous"
          onError={() => {
            // Handle audio loading errors gracefully
            console.error('Audio failed to load:', audioUrl);
          }}
        >
          <source src={audioUrl} type="audio/mpeg" />
          <source src={audioUrl} type="audio/wav" />
          <source src={audioUrl} type="audio/ogg" />
          <source src={audioUrl} type="audio/mp4" />
          Your browser does not support the audio element.
        </audio>

        {/* Play/Pause Button */}
        <button className="audio-player__play-btn" onClick={togglePlay}>
          <Icon
            name={isPlaying ? 'Pause' : 'Play'}
            size={16}
            color="var(--color-primary)"
          />
        </button>

        {/* Current Time */}
        <span className="audio-player__time">{formatTime(currentTime)}</span>

        {/* Progress Bar */}
        <div className="audio-player__progress-container">
          <input
            type="range"
            min="0"
            max="100"
            value={progressPercentage}
            onChange={handleProgressChange}
            className="audio-player__progress"
            style={{
              '--progress': `${progressPercentage}%`,
            }}
          />
        </div>

        {/* Duration */}
        <span className="audio-player__time">{formatTime(duration)}</span>

        {/* Volume Control */}
        <button className="audio-player__volume-btn" onClick={toggleMute}>
          <Icon
            name={isMuted ? 'VolumeX' : 'Volume2'}
            size={16}
            color="var(--color-primary)"
          />
        </button>

        {/* Close Button */}
        <button className="audio-player__close" onClick={onClose}>
          <Icon name="X" size={16} color="var(--color-primary)" />
        </button>
      </div>
    </div>
  );
};

export default AudioPlayer;
