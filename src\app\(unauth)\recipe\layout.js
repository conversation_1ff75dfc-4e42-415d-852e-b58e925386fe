import { Box } from '@mui/material';
import PublicRecipeHeader from '@/components/PublicRecipe/Header';
import PublicRecipeFooter from '@/components/PublicRecipe/Footer';
import './publicrecipelayout.scss';

export default function PublicRecipeLayout({ children }) {
  return (
    <Box className="main-page-container">
      <Box className="public-recipe-layout-wrap">
        <PublicRecipeHeader />
        <Box className="">{children}</Box>
        <PublicRecipeFooter />
      </Box>
    </Box>
  );
}
