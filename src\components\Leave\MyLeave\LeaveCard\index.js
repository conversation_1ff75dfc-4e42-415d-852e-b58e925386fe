'use client';
import React from 'react';
import { Card, CardContent, Typography, Box, Tooltip } from '@mui/material';
import { LeavePaid, LeaveUnPaid } from '@/helper/common/images';
import InfoIcon from '@mui/icons-material/Info';
import NoDataView from '@/components/UI/NoDataView';
import '../myleave.scss';
const LeaveCard = ({ userDetails }) => {
  return (
    <Box className="my-leave-card-wrap">
      {userDetails && Object.keys(userDetails).length > 0 ? (
        <Box className="my-leave-card leave-balance-card pt0 pb16">
          <Card className="card-wrap leave-card">
            <CardContent className="card-content-wrap card-details-wrap">
              <Box className="d-flex align-center justify-space-between leave-wrap">
                <Box className="d-flex align-center">
                  <Box
                    className="leave-icon-wrap"
                    sx={{
                      backgroundColor: userDetails?.leave_type_color,
                    }}
                  >
                    {/* {leave?.name === 'Privileged leaves' ? (
                            <PrevillageLeaveIcon />
                          ) : leave?.name === 'Sick Leave' ? (
                            <SickLeaveIcon />
                          ) : leave?.name === 'Comp Off' ? (
                            <CampOffIcon />
                          ) : (
                            <LwpIcon />
                          )} */}
                  </Box>
                  <Box className="d-flex align-center">
                    <Box variant="h6" className="d-flex align-center">
                      <Tooltip
                        title={<Typography>{userDetails?.name}</Typography>}
                        arrow
                        classes={{ tooltip: 'info-tooltip-container' }}
                      >
                        <Typography
                          variant="h6"
                          className="title-text pl8 fw600 text-ellipsis-line"
                        >
                          {userDetails?.name}
                        </Typography>
                      </Tooltip>
                      <Typography variant="h6" className="fw600 title-text">
                        (
                        {userDetails?.has_leave_unlimited === true ? (
                          <span className="unlimited-text-wrap">Unlimited</span>
                        ) : (
                          userDetails?.leave_balance
                        )}
                        )
                      </Typography>
                    </Box>
                    <Tooltip
                      arrow
                      title={
                        <Typography>
                          {userDetails?.leave_deduction_type}
                        </Typography>
                      }
                      classes={{ tooltip: 'info-tooltip-container' }}
                    >
                      <span className="leave-type-wrap d-flex align-center cursor-pointer">
                        {userDetails?.leave_deduction_type === 'unpaid' ? (
                          <LeaveUnPaid />
                        ) : (
                          <LeavePaid />
                        )}
                      </span>
                    </Tooltip>
                  </Box>
                </Box>
                {/* <Box className="leave-type-wrap">
                    <MoreHorizIcon
                      className="cursor-pointer"
                      onClick={handleClick}
                    />
                  </Box> */}
              </Box>
              <Box className="d-flex align-center">
                <Box className="balance-wrap text-align">
                  <Typography className="title-text">Balance</Typography>
                  <Box className="d-flex align-center gap-10 justify-center">
                    <Typography className="title-text fw600 text-wrap">
                      {userDetails?.has_leave_unlimited === true ? (
                        <span className="unlimited-text-wrap">Unlimited</span>
                      ) : userDetails?.user_remaining_leave === null ? (
                        '0'
                      ) : (
                        userDetails?.user_remaining_leave
                      )}
                    </Typography>
                    <Tooltip
                      arrow
                      title={
                        <Typography>
                          The Planned leave balance will be debited once the
                          specified date has passed.
                        </Typography>
                      }
                      classes={{ tooltip: 'info-tooltip-container' }}
                    >
                      <InfoIcon className="info-icon-wrap cursor-pointer" />
                    </Tooltip>
                  </Box>
                </Box>
                <Box className="booked-wrap text-align">
                  <Typography className="title-text">Booked</Typography>
                  <Box className="d-flex align-center gap-10 justify-center">
                    <Typography
                      variant="body1"
                      className="title-text fw600 text-wrap"
                    >
                      {userDetails?.used_leave?.total}
                    </Typography>
                    <Tooltip
                      arrow
                      title={
                        <Box>
                          <Typography className="sub-title-text">
                            Used: {userDetails?.used_leave?.used_leaves}
                          </Typography>
                          <Typography className="sub-title-text">
                            Planned: {userDetails?.used_leave?.planned_leaves}
                          </Typography>
                        </Box>
                      }
                      placement="right"
                      classes={{ tooltip: 'info-tooltip-container' }}
                    >
                      <InfoIcon className="info-icon-wrap cursor-pointer" />
                    </Tooltip>
                  </Box>
                </Box>
              </Box>
              {/* <Typography
              variant="h6"
              className="apply-leave fw600 title-text text-align cursor-pointer"
              onClick={() => router.push('/apply-leave')}
            >
              Apply Leave
            </Typography> */}
            </CardContent>
          </Card>
        </Box>
      ) : (
        <Box className="no-data-show-wrap d-flex align-center justify-center mt32 mb32">
          <NoDataView
            title="No Leave Balance Found"
            description="There is no Leave Balance data available at the moment."
          />
        </Box>
      )}
    </Box>
  );
};

export default LeaveCard;
