// components/CustomCheckbox.tsx
import { Checkbox, FormControlLabel } from '@mui/material';
import { styled } from '@mui/system';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CheckBoxIcon from '@mui/icons-material/CheckBox';

const StyledCheckbox = styled(Checkbox)(() => ({
  color: 'var(--radio-btn-uncheck-color-primary)',
  padding: 'var(--radio-btn-spacing)',
  '&.Mui-checked': {
    color: 'var(--radio-btn-check-color-primary)',
  },
  '&.Mui-disabled': {
    color: 'var(--radio-btn-disabled-color-primary)',
  },
  '& .MuiSvgIcon-root': {
    width: 'var(--check-box-icon-size)',
    height: 'var(--check-box-icon-size)',
  },
  '&.MuiCheckbox-indeterminate': {
    color: 'var(--radio-btn-check-color-primary)',
  },
}));

const StyledFormControlLabel = styled(FormControlLabel)(() => ({
  marginLeft: '0px',
  marginRight: '0px',
  gap: 'var(--radio-btn-gap)',
  '.MuiTypography-root': {
    fontSize: 'var(--radio-btn-font-size)',
    fontFamily: 'var(--font-family-primary)',
    fontWeight: 'var(--font-weight-regular)',
    lineHeight: 'var(--line-height-base)',
    letterSpacing: 'var(--letter-spacing-normal)',
    color: 'var(--text-color-black) !important',
  },
}));

export default function CustomCheckbox({
  label,
  labelPlacement = 'end',
  ...props
}) {
  return (
    <StyledFormControlLabel
      control={
        <StyledCheckbox
          icon={<CheckBoxOutlineBlankIcon />}
          checkedIcon={<CheckBoxIcon />}
          {...props}
        />
      }
      labelPlacement={labelPlacement}
      label={label}
    />
  );
}
