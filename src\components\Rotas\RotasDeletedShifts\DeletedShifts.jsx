'use client';
import React, { useContext, useEffect, useState } from 'react';
import { Box, Tooltip, Typography } from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import dayjs from 'dayjs';
import moment from 'moment';
import MessageOutlinedIcon from '@mui/icons-material/MessageOutlined';
import PersonOutlineIcon from '@mui/icons-material/PersonOutline';
import LocationOnOutlinedIcon from '@mui/icons-material/LocationOnOutlined';
import BusinessCenterOutlinedIcon from '@mui/icons-material/BusinessCenterOutlined';
import ArrowBackOutlinedIcon from '@mui/icons-material/ArrowBackOutlined';
import CustomButton from '@/components/UI/CustomButton';
import CustomDatePicker from '@/components/UI/CustomDatePicker';
import { useRouter } from 'next/navigation';
import DialogBox from '@/components/UI/Modalbox';
import DeleteFilterModal from './DeleteFilterModal';
import axiosInstanceOrg from '@/helper/axios/axiosInstanceOrg';
import axiosInstance from '@/helper/axios/axiosInstance';
import { ROTA_URLS, URLS } from '@/helper/constants/urls';
import AuthContext from '@/helper/authcontext';
import { setApiMessage } from '@/helper/common/commonFunctions';
import ContentLoader from '@/components/UI/ContentLoader';
import NoDataView from '@/components/UI/NoDataView';
import CustomPagination from '@/components/UI/customPagination';
import '../rotas.scss';
import './deletedShift.scss';

const DeletedShifts = () => {
  const navigate = useRouter();
  const { authState, AllListsData } = useContext(AuthContext);
  const viewAccessOnly = authState?.web_user_active_role_id
    ? authState.web_user_active_role_id === 1 ||
      authState.web_user_active_role_id === 7 ||
      authState.web_user_active_role_id === 14
    : '';
  const [startDate, setStartDate] = useState(dayjs().subtract(1, 'month'));
  const [endDate, setEndDate] = useState(dayjs(startDate).add(1, 'month'));
  const [isDeleteFilterModal, setIsDeleteFilterModal] = useState({
    isEmployeesModal: false,
    isLocationModal: false,
    isRolesModal: false,
  });
  const [staffListData, setStaffListData] = useState([]);
  const [locationList, setLocationList] = useState([]);
  const [rolesList, setRolesList] = useState([]);
  const [formattedSelection, setFormattedSelection] = useState([]);

  const [isLoader, setIsLoader] = useState(false);
  const [deletedShiftList, setDeletedShiftList] = useState([]);
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  // console.log('deletedShiftList', deletedShiftList);

  const columns = [
    {
      field: 'user.user_full_name',
      headerName: 'Employee',
      width: 150,
      minWidth: 150,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        let userData = params?.row?.user;
        return (
          <Box className="d-flex align-center h100">
            {userData?.user_full_name}
          </Box>
        );
      },
    },
    {
      field: 'branch.branch_name',
      headerName: 'Location',
      width: 150,
      minWidth: 150,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        let brnachData = params?.row?.branch;
        return (
          <Box className="d-flex align-center h100">
            {brnachData?.branch_name}
          </Box>
        );
      },
    },
    {
      field: 'role.role_name',
      headerName: 'Role',
      width: 150,
      minWidth: 150,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        let roleData = params?.row?.role;
        return (
          <Box className="d-flex align-center h100">
            {roleData?.role_name ? roleData?.role_name : '-'}
          </Box>
        );
      },
    },
    {
      field: 'isPublished',
      headerName: 'Published',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        let shiftData = params?.row;
        return (
          <Box className="h100">{shiftData?.isPublished ? 'Yes' : 'No'}</Box>
        );
      },
    },
    {
      field: 'startTime',
      headerName: 'Shift Date',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        let shiftData = params?.row;
        return (
          <Box className="d-flex align-center h100">
            {moment(shiftData?.startTime)?.format('DD/MM/YYYY')}
          </Box>
        );
      },
    },
    {
      field: 'startTime-endTime',
      headerName: 'Shift Time',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        let shiftData = params?.row;
        const start_time = moment(shiftData?.startTime).utc().format('HH:mm');
        const end_time = moment(shiftData?.endTime).utc().format('HH:mm');
        return (
          <Box className="d-flex align-center h100">
            {start_time} - {end_time}
          </Box>
        );
      },
    },
    {
      field: 'minutesBreak',
      headerName: 'Break',
      width: 80,
      minWidth: 80,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        let shiftData = params?.row;

        return (
          <Box className="h100">
            {shiftData?.minutesBreak ? shiftData?.minutesBreak : '-'}
          </Box>
        );
      },
    },
    {
      field: 'notes',
      headerName: 'Notes',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        let shiftData = params?.row;
        return (
          <Box className="h100">
            {shiftData?.notes ? (
              <Tooltip
                title={
                  <div>
                    <p className="p12">{shiftData?.notes}</p>
                  </div>
                }
                arrow
                classes={{
                  tooltip:
                    'calendar-event-list-tooltip-custom calendar-event-other-details-tooltip',
                }}
              >
                <MessageOutlinedIcon />
              </Tooltip>
            ) : (
              '-'
            )}
          </Box>
        );
      },
    },
    {
      field: 'deletedBy',
      headerName: 'Deleted By',
      width: 180,
      minWidth: 180,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        let shiftData = params?.row;
        return (
          <Box className="d-flex align-center h100">{shiftData?.updatedBy}</Box>
        );
      },
    },
    {
      field: 'deletedAt',
      headerName: 'Date/Time Deleted',
      width: 160,
      minWidth: 160,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        let shiftData = params?.row;
        const delete_date = moment(shiftData?.updatedAt).format('DD/MM/YYYY');
        const delete_time = moment(shiftData?.updatedAt).format('HH:mm');

        return (
          <Box className="d-flex align-center h100">
            {delete_date} at {delete_time}
          </Box>
        );
      },
    },
    {
      field: 'actions',
      headerName: '',
      width: 80,
      minWidth: 80,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box
            className="table-action-restore-btn"
            onClick={() => handleRestoreShift(params?.row)}
          >
            Restore
          </Box>
        );
      },
    },
  ];

  const handleRestoreShift = async (shiftData) => {
    const sendData = {
      userId: shiftData?.userId,
      startTime: shiftData?.startTime,
      endTime: shiftData?.endTime,
      isPublished: !!shiftData?.isPublished,
      status: 'active',
    };

    try {
      const { status, data } = await axiosInstanceOrg.put(
        ROTA_URLS?.SHIFT_URL + `/${shiftData?.id}`,
        sendData
      );
      if (status === 200 || status === 201) {
        setApiMessage('success', data?.message);
        getDeletedShiftList(startDate, endDate, '', [], page);
      }
    } catch (err) {
      setApiMessage('error', err?.response?.data?.message);
    }
  };
  // Get deleted shift list
  const getDeletedShiftList = async (
    sDate,
    eDate,
    isFiltertype,
    filterVal,
    pageNo,
    Rpp
  ) => {
    let filterList = '';

    var firstDay = dayjs(sDate).format('YYYY-MM-DD');
    var lastDay = dayjs(eDate).format('YYYY-MM-DD');

    const filterOpt = filterVal
      ?.map((item) =>
        item.type == 'employees'
          ? `userId=${item?.data?.map((uid) => uid?.id)?.join(',')}`
          : item.type == 'locations'
            ? `branchId=${item?.data?.map((uid) => uid?.id)?.join(',')}`
            : item.type == 'roles'
              ? `roleId=${item?.data?.map((uid) => uid?.id)?.join(',')}`
              : ''
      )
      ?.join('&');

    filterList = `?status=deleted&from=${firstDay}&page=${pageNo}&size=${
      Rpp ? Rpp : rowsPerPage
    }&to=${lastDay}&isAdmin=${viewAccessOnly}${
      filterOpt ? `&${filterOpt}` : ''
    }`;

    try {
      setIsLoader(true);

      const { status, data } = await axiosInstanceOrg.get(
        ROTA_URLS?.SHIFT_URL + filterList
      );
      if (status === 200) {
        setDeletedShiftList(data?.data.length > 0 ? data?.data : []);
        setTotalCount(data?.count);
        setIsLoader(false);
      }
    } catch (err) {
      setApiMessage('error', err?.response?.data?.message);
      setIsLoader(false);
    }
  };
  // Get staff list
  const getStaffList = async () => {
    try {
      const { status, data } = await axiosInstanceOrg.get(
        ROTA_URLS?.SHIFT_STAFF_LIST
      );

      if (status === 200) {
        const copyRangeEmp = [...data?.userList];
        // copyRangeEmp.unshift({
        //   id: 'open_shift',
        //   user_full_name: 'Open Shifts',
        // });
        setStaffListData(
          copyRangeEmp?.map((item) => ({
            ...item,
            label_name: item?.user_full_name,
          }))
        );
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setStaffListData([]);
    }
  };
  // Get role list
  const getRoleList = async () => {
    try {
      const { status, data } = await axiosInstance.get(URLS?.GET_ROLE_LIST);

      if (status === 200) {
        let filterUserList = data?.data?.map((user) => ({
          ...user,
          label_name: user?.role_name,
        }));
        setRolesList(filterUserList);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setRolesList([]);
    }
  };

  useEffect(() => {
    getDeletedShiftList(startDate, endDate, '', [], 1);
    getStaffList();
    getRoleList();
  }, []);

  useEffect(() => {
    if (AllListsData?.ActiveBranchList) {
      setLocationList([
        ...AllListsData.ActiveBranchList?.map((item) => ({
          ...item,
          label_name: item?.label,
          id: item?.value,
        })),
      ]);
    }
  }, [AllListsData?.ActiveBranchList]);

  const handleCloseDeleteFilterModal = () => {
    setIsDeleteFilterModal({
      isEmployeesModal: false,
      isLocationModal: false,
      isRolesModal: false,
    });
  };

  const selectedFilter = (lists, isType) => {
    const selection =
      formattedSelection?.find((item) => item?.type === isType)?.data || [];
    const totalCount = lists?.length;
    const selectedCount = selection?.length;

    if (selectedCount === totalCount && totalCount !== 0) {
      return 'All';
    } else if (selectedCount > 0) {
      return selectedCount;
    } else {
      return 'All';
    }
  };

  const handleUpdateDate = (start_date, end_date) => {
    getDeletedShiftList(start_date, end_date, '', [], page);
  };

  const handleApplyFilter = (updateList, isType) => {
    getDeletedShiftList(startDate, endDate, isType, updateList, page);
  };
  const onPageChange = (newPage) => {
    setPage(newPage);
    getDeletedShiftList(startDate, endDate, '', [], newPage);
  };
  const OnRowPerPageDSR = (newPage) => {
    setRowsPerPage(newPage);
    setPage(1);
    getDeletedShiftList(startDate, endDate, '', [], 1, newPage);
  };
  return (
    <Box className="rota-deleted-shifts-container">
      {/* {isLoader && <PreLoader />} */}
      <Box className="dashboard-filter-section">
        <Box className="rota-filter-heading">
          <Typography variant="h5" className="rota-filter-title">
            Deleted Shifts
          </Typography>

          <CustomButton
            variant="outlined"
            startIcon={<ArrowBackOutlinedIcon />}
            title="Back to Rota"
            onClick={() => navigate.push('/rotas')}
          />
        </Box>
        <Box className="filter-date-picker-section">
          <CustomDatePicker
            placeholder="From"
            value={startDate}
            onChange={(newValue) => {
              setStartDate(newValue);
              handleUpdateDate(newValue, endDate);
            }}
            format="dddd DD MMMM YYYY"
          />
          <Typography className="rota-filter-to-text">to</Typography>
          <CustomDatePicker
            placeholder="To"
            value={endDate}
            onChange={(newValue) => {
              setEndDate(newValue);
              handleUpdateDate(startDate, newValue);
            }}
            format="dddd DD MMMM YYYY"
          />

          <Box className="filter-button-section">
            <CustomButton
              startIcon={<PersonOutlineIcon />}
              variant="outlined"
              onClick={() => setIsDeleteFilterModal({ isEmployeesModal: true })}
            >
              {selectedFilter(staffListData, 'employees')}
            </CustomButton>
            <CustomButton
              startIcon={<LocationOnOutlinedIcon />}
              variant="outlined"
              onClick={() => setIsDeleteFilterModal({ isLocationModal: true })}
            >
              {selectedFilter(locationList, 'locations')}
            </CustomButton>
            <CustomButton
              startIcon={<BusinessCenterOutlinedIcon />}
              variant="outlined"
              onClick={() => setIsDeleteFilterModal({ isRolesModal: true })}
            >
              {selectedFilter(rolesList, 'roles')}
            </CustomButton>
          </Box>
        </Box>
      </Box>
      <Box className="rota-deleted-shifts-table">
        <Box className="table-container">
          {isLoader ? (
            <ContentLoader />
          ) : !isLoader &&
            deletedShiftList &&
            deletedShiftList?.length === 0 ? (
            <NoDataView
              title="No Deleted Shift Data Found"
              description="There is no Deleted Shift Data at the moment."
            />
          ) : (
            <>
              <DataGrid
                rows={deletedShiftList}
                columns={columns}
                pageSize={rowsPerPage}
                checkboxSelection={false} // Disable default checkbox column
                disableSelectionOnClick // Disable row selection on click
                hideMenuIcon
              />
              <CustomPagination
                currentPage={page}
                // totalPages={totalPages}
                totalCount={totalCount}
                rowsPerPage={rowsPerPage}
                onPageChange={onPageChange}
                OnRowPerPage={OnRowPerPageDSR}
              />
            </>
          )}
        </Box>
      </Box>

      {/* Employees modal */}
      {isDeleteFilterModal?.isEmployeesModal && (
        <DialogBox
          open={isDeleteFilterModal?.isEmployeesModal}
          handleClose={() => {
            setIsDeleteFilterModal({
              isEmployeesModal: false,
            });
          }}
          className="common-header-menu-modal custom-range-modal"
          title="Employees"
          content={
            <>
              <DeleteFilterModal
                listData={staffListData}
                isFiltertype="employees"
                handleCloseDeleteFilterModal={handleCloseDeleteFilterModal}
                setFormattedSelection={setFormattedSelection}
                formattedSelection={formattedSelection}
                handleApplyFilter={handleApplyFilter}
              />
            </>
          }
        />
      )}
      {/* Location modal */}
      {isDeleteFilterModal?.isLocationModal && (
        <DialogBox
          open={isDeleteFilterModal?.isLocationModal}
          handleClose={() => {
            setIsDeleteFilterModal({
              isLocationModal: false,
            });
          }}
          className="common-header-menu-modal custom-range-modal"
          title="Locations"
          content={
            <>
              <DeleteFilterModal
                listData={locationList}
                isFiltertype="locations"
                handleCloseDeleteFilterModal={handleCloseDeleteFilterModal}
                setFormattedSelection={setFormattedSelection}
                formattedSelection={formattedSelection}
                handleApplyFilter={handleApplyFilter}
              />
            </>
          }
        />
      )}
      {/* Roles modal */}
      {isDeleteFilterModal?.isRolesModal && (
        <DialogBox
          open={isDeleteFilterModal?.isRolesModal}
          handleClose={() => {
            setIsDeleteFilterModal({
              isRolesModal: false,
            });
          }}
          className="common-header-menu-modal custom-range-modal"
          title="Roles"
          content={
            <>
              <DeleteFilterModal
                listData={rolesList}
                isFiltertype="roles"
                handleCloseDeleteFilterModal={handleCloseDeleteFilterModal}
                setFormattedSelection={setFormattedSelection}
                formattedSelection={formattedSelection}
                handleApplyFilter={handleApplyFilter}
              />
            </>
          }
        />
      )}
    </Box>
  );
};

export default DeletedShifts;
