import React from 'react';
import CustomCheckbox from '@/components/UI/CustomCheckbox';
import CustomSelect from '@/components/UI/CustomSelect';
import CustomTextField from '@/components/UI/CustomTextField';
import { Box, Tooltip, Typography } from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import { identifiers } from '@/helper/constants/identifier';

export default function LeaveEntitlementSection({
  values,
  handleChange,
  handleBlur,
  touched,
  errors,
  setFieldValue,
  leavePolicySubmit,
  setLeavePolicySubmit,
  Inputype,
}) {
  return (
    <>
      <Box className="effective-after-wrap">
        <Box className="d-flex align-center mt16 gap-5">
          <Typography className="sub-content-text">
            Leave Entitlement
          </Typography>
          <Tooltip
            arrow
            title={
              <Typography>
                Accrual helps you set the intervals for the leave balance to be
                credited to the employee's account. (i.e.) If monthly accrual is
                selected with the leave entitlement 1, the employee will be
                credited with 1 leave balance every month.
              </Typography>
            }
            classes={{ tooltip: 'info-tooltip-container' }}
          >
            <InfoIcon className="info-icon cursor-pointer" />
          </Tooltip>
        </Box>
        <Box className="d-flex gap-10 align-center mt16 flex-wrap">
          <Box className="d-flex align-center gap-10 flex-wrap">
            <Typography className="title-text">Effective After</Typography>
            <Box className="d-flex align-center">
              <Box className="small-text-field">
                <CustomTextField
                  fullWidth
                  id="effectiveDays"
                  name="effectiveDays"
                  placeholder=""
                  value={values.effectiveDays}
                  onChange={(e) => {
                    setFieldValue('effectiveDays', e?.target?.value);
                    setLeavePolicySubmit({
                      ...leavePolicySubmit,
                      leaveAccrual: false,
                    });
                  }}
                  className="text-field-right-border-none"
                  error={Boolean(
                    touched?.effectiveDays && errors?.effectiveDays
                  )}
                  helperText={touched?.effectiveDays && errors?.effectiveDays}
                  onBlur={handleBlur}
                  onInput={Inputype}
                />
              </Box>
              <CustomSelect
                fullWidth
                placeholder="Select"
                name="effectiveTimePeriod"
                options={identifiers?.DAY_MONTH_OPTIONS}
                className="text-field-left-border-none leave-select-wrap"
                value={
                  identifiers?.DAY_MONTH_OPTIONS?.find((opt) => {
                    return opt?.value === values.effectiveTimePeriod;
                  }) || ''
                }
                onChange={(e) => {
                  setFieldValue('effectiveTimePeriod', e?.value);
                  setLeavePolicySubmit({
                    ...leavePolicySubmit,
                    leaveAccrual: false,
                  });
                }}
                error={Boolean(
                  touched?.effectiveTimePeriod && errors?.effectiveTimePeriod
                )}
                helperText={
                  touched?.effectiveTimePeriod && errors?.effectiveTimePeriod
                }
                isClearable={false}
              />
            </Box>
          </Box>
          <Box className="d-flex align-center gap-10">
            <Typography className="title-text">From</Typography>
            <Box className="d-flex align-center gap-5">
              <CustomSelect
                fullWidth
                id="effectiveFrom"
                name="effectiveFrom"
                placeholder="Select"
                options={identifiers?.JOINING_OPTIONS}
                className="leave-select-wrap"
                value={
                  identifiers?.JOINING_OPTIONS?.find((opt) => {
                    return opt?.value === values.effectiveFrom;
                  }) || ''
                }
                onChange={(e) => {
                  setFieldValue('effectiveFrom', e?.value);
                  setFieldValue('newEmpLeaveTheirType', e?.value);
                  setFieldValue('probation', false);
                  setFieldValue('probationLimit', 0);
                  setLeavePolicySubmit({
                    ...leavePolicySubmit,
                    leaveAccrual: false,
                  });
                }}
                error={Boolean(touched?.effectiveFrom && errors?.effectiveFrom)}
                helperText={touched?.effectiveFrom && errors?.effectiveFrom}
                isClearable={false}
              />
              <Tooltip
                arrow
                title={
                  <Typography>
                    Leave balance can be viewed by employees after either "Date
                    of Joining" or "After Probation/Internship Completion".
                    <br />
                    For example:
                    <br />
                    1. If effective 10 days after "Date of Joining," both
                    full-time and probation/internship employees will see their
                    leave after 10 days.
                    <br />
                    2. If effective 30 days after completing "After
                    Probation/Internship Completion," only probation/internship
                    employees will see their leave balance post-completion.
                  </Typography>
                }
                classes={{ tooltip: 'info-tooltip-container' }}
              >
                <InfoIcon className="info-icon cursor-pointer" />
              </Tooltip>
            </Box>
          </Box>
        </Box>
        <Box className="d-flex gap-10 align-center mt16 flex-wrap">
          <Box className="d-flex align-center gap-10 flex-wrap">
            <Typography className="title-text">
              The new employee can apply for leave after{' '}
            </Typography>
            <Box className="d-flex align-center">
              <Box className="small-text-field">
                <CustomTextField
                  fullWidth
                  id="newEmpLeaveDay"
                  name="newEmpLeaveDay"
                  placeholder=""
                  value={values?.newEmpLeaveDay}
                  onChange={(e) => {
                    handleChange(e);
                    setLeavePolicySubmit({
                      ...leavePolicySubmit,
                      leaveAccrual: false,
                    });
                  }}
                  onBlur={handleBlur}
                  className="text-field-right-border-none"
                  error={Boolean(
                    touched?.newEmpLeaveDay && errors?.newEmpLeaveDay
                  )}
                  helperText={touched?.newEmpLeaveDay && errors?.newEmpLeaveDay}
                  onInput={Inputype}
                />
              </Box>
              <CustomSelect
                fullWidth
                id="newEmpLeaveType"
                name="newEmpLeaveType"
                placeholder="Effective Time Period"
                options={identifiers?.DAY_MONTH_OPTIONS}
                value={
                  identifiers?.DAY_MONTH_OPTIONS?.find((opt) => {
                    return opt?.value === values.newEmpLeaveType;
                  }) || ''
                }
                onChange={(e) => {
                  setFieldValue('newEmpLeaveType', e?.value);
                  setLeavePolicySubmit({
                    ...leavePolicySubmit,
                    leaveAccrual: false,
                  });
                }}
                className="text-field-left-border-none leave-select-wrap"
                error={Boolean(
                  touched?.newEmpLeaveType && errors?.newEmpLeaveType
                )}
                helperText={touched?.newEmpLeaveType && errors?.newEmpLeaveType}
                isClearable={false}
              />
            </Box>
          </Box>
          <Box className="d-flex align-center gap-10">
            <Typography className="title-text">their</Typography>
            <Box className="d-flex align-center gap-5">
              <CustomSelect
                fullWidth
                id="newEmpLeaveTheirType"
                name="newEmpLeaveTheirType"
                className="leave-select-wrap"
                placeholder="Select"
                options={identifiers?.JOINING_OPTIONS}
                value={
                  identifiers?.JOINING_OPTIONS?.find((opt) => {
                    return opt?.value === values.newEmpLeaveTheirType;
                  }) || ''
                }
                onChange={(e) => {
                  setFieldValue('newEmpLeaveTheirType', e?.value);
                  setFieldValue('probation', false);
                  setFieldValue('probationLimit', 0);
                  setLeavePolicySubmit({
                    ...leavePolicySubmit,
                    leaveAccrual: false,
                  });
                }}
                error={Boolean(
                  touched?.newEmpLeaveTheirType && errors?.newEmpLeaveTheirType
                )}
                helperText={
                  touched?.newEmpLeaveTheirType && errors?.newEmpLeaveTheirType
                }
                isDisabled={values?.effectiveFrom === 'after_probation_end'}
                isClearable={false}
              />
              <Tooltip
                arrow
                title={
                  <Typography>
                    Selecting "Date of Joining" restricts leave access for
                    full-time or Probation/Internship employees.
                  </Typography>
                }
                classes={{ tooltip: 'info-tooltip-container' }}
              >
                <InfoIcon className="info-icon cursor-pointer" />
              </Tooltip>
            </Box>
          </Box>
        </Box>
        {values?.effectiveFrom === 'date_of_joining' &&
        values?.newEmpLeaveTheirType === 'date_of_joining' ? (
          <Box className="d-flex align-center gap-sm mt16 flex-wrap">
            <Box>
              <CustomCheckbox
                checked={values?.probation}
                onChange={(e) => {
                  setFieldValue('probation', e?.target?.checked);
                  setLeavePolicySubmit({
                    ...leavePolicySubmit,
                    leaveAccrual: false,
                  });
                  setFieldValue('probationLimit', 0);
                }}
                disableRipple
                label={<Typography>Employee can apply leave for</Typography>}
              />
            </Box>
            <Box className="leave-policy-input">
              <CustomTextField
                fullWidth
                id="probationLimit"
                name="probationLimit"
                placeholder="Days"
                value={values?.probationLimit}
                onChange={(e) => {
                  handleChange(e);
                  setLeavePolicySubmit({
                    ...leavePolicySubmit,
                    leaveAccrual: false,
                  });
                }}
                error={Boolean(
                  touched?.probationLimit && errors?.probationLimit
                )}
                helperText={touched?.probationLimit && errors?.probationLimit}
                onBlur={handleBlur}
                onInput={Inputype}
                disabled={!values?.probation}
              />
            </Box>
            <Box className="d-flex align-center gap-5">
              <Typography className="title-text">
                days during their Probation/Internship period.
              </Typography>
              <Tooltip
                arrow
                title={
                  <Typography>
                    Employees on "Probation/Internship" may use the stated leave
                    balance while they are on Probation/Internship.
                  </Typography>
                }
                classes={{ tooltip: 'info-tooltip-container' }}
              >
                <InfoIcon className="info-icon cursor-pointer" />
              </Tooltip>
            </Box>
          </Box>
        ) : (
          <></>
        )}
      </Box>
    </>
  );
}
