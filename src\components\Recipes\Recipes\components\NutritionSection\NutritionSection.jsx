import React, { useState } from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import NutritionComponent from './components/NutritionComponent/NutritionComponent';
import AllergenComponent from './components/AllergenComponent/AllergenComponent';
import './NutritionSection.scss';

const NutritionSection = ({ formData, dispatch }) => {
  const [activeTab, setActiveTab] = useState('nutrition');

  return (
    <div className="nutrition-section">
      {/* Tab Navigation */}
      <div className="nutrition-section__tabs">
        <button
          onClick={() => setActiveTab('nutrition')}
          className={`nutrition-section__tab ${
            activeTab === 'nutrition'
              ? 'nutrition-section__tab--active'
              : 'nutrition-section__tab--inactive'
          }`}
        >
          <Icon
            name="BarChart3"
            size={16}
            color={
              activeTab === 'nutrition'
                ? 'var(--color-primary)'
                : 'var(--text-color-slate-gray)'
            }
          />
          <span>Nutrition Facts</span>
        </button>

        <button
          onClick={() => setActiveTab('allergens')}
          className={`nutrition-section__tab ${
            activeTab === 'allergens'
              ? 'nutrition-section__tab--active'
              : 'nutrition-section__tab--inactive'
          }`}
        >
          <Icon
            name="AlertTriangle"
            size={16}
            color={
              activeTab === 'allergens'
                ? 'var(--color-primary)'
                : 'var(--text-color-slate-gray)'
            }
          />
          <span>Allergens</span>
        </button>
      </div>

      {/* Nutrition Facts Tab */}
      {activeTab === 'nutrition' && (
        <div className="nutrition-section__content">
          <NutritionComponent formData={formData} dispatch={dispatch} />
        </div>
      )}

      {/* Allergens Tab */}
      {activeTab === 'allergens' && (
        <div className="nutrition-section__content">
          <AllergenComponent formData={formData} dispatch={dispatch} />
        </div>
      )}
    </div>
  );
};

export default NutritionSection;
