import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';

export const healthSafetyService = {
  /**
   * Get list of health and safety categories
   * @param {string} search - Search term for filtering categories
   * @returns {Promise<Array>} List of health and safety categories
   */
  getCategoryList: async (search = '') => {
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_BRANCH_CAT_LIST + `?search=${search}`
      );
      if (status === 200) {
        return data?.data || [];
      }
      return [];
    } catch (error) {
      throw error;
    }
  },

  /**
   * Link category with branch
   * @param {number} categoryId - ID of the category
   * @param {number} branchId - ID of the branch
   * @returns {Promise<Object>} Response data
   */
  linkWithCategory: async (categoryId, branchId) => {
    try {
      const sendData = {
        category_id: categoryId,
        branch_id: Number(branchId),
      };
      const { status, data } = await axiosInstance.post(
        URLS.ADD_BRANCH_CAT,
        sendData
      );
      if (status === 200) {
        return data;
      }
      return null;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Update category branch assignments
   * @param {number} categoryId - ID of the category
   * @param {Object} data - Assignment data
   * @returns {Promise<Object>} Response data
   */
  updateCategoryBranchAssignments: async (categoryId, data) => {
    try {
      const { status, data: responseData } = await axiosInstance.post(
        URLS?.ADD_MULTIPLE_CATEGORY_BRANCH,
        data
      );
      if (status === 200 || status === 202) {
        return responseData;
      }
      return null;
    } catch (error) {
      throw error;
    }
  },
};
