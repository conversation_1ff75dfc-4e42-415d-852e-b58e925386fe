import React, { useEffect, useState, useContext } from 'react';
import { Box, Typography, Tooltip } from '@mui/material';
import CustomTextField from '@/components/UI/CustomTextField';
import { ReactSortable } from 'react-sortablejs';
import InsertLinkIcon from '@mui/icons-material/InsertLink';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import CustomButton from '@/components/UI/CustomButton';
import RemoveCircleIcon from '@mui/icons-material/RemoveCircle';
import AddIcon from '@mui/icons-material/Add';
import RightDrawer from '@/components/UI/RightDrawer';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import AuthContext from '@/helper/authcontext';
import CancelIcon from '@mui/icons-material/Cancel';
import _ from 'lodash';
import './reorder.scss';

const sortableOptions = {
  animation: 150,
  fallbackOnBody: true,
  swapThreshold: 0.65,
  ghostClass: 'ghost',
  forceFallback: true,
};

function Container({
  block,
  blockIndex,
  setDSRReportsData,
  RemoveGroupBlocks,
  isMainItem,
  // For Totals
  toggleSelectBlock,
  selectedIds,
  authState,
  EditGroupName,
  FilterTotal,
}) {
  return (
    <>
      <ReactSortable
        key={block?.id}
        list={block?.children}
        setList={(currentList) => {
          if (isMainItem) {
            setDSRReportsData((sourceList) => {
              const tempList = [...sourceList];
              const _blockIndex = [...blockIndex];
              const lastIndex = _blockIndex.pop();
              const lastArr = _blockIndex.reduce(
                (arr, i) => arr[i]['children'],
                tempList
              );
              lastArr[lastIndex]['children'] = currentList;

              return tempList;
            });
          } else {
            return currentList;
          }
        }}
        {...sortableOptions}
      >
        {block?.children &&
          block?.children.map((childBlock, index) => {
            return (
              <BlockWrapper
                key={childBlock.id}
                block={childBlock}
                blockIndex={[...blockIndex, index]}
                setDSRReportsData={setDSRReportsData}
                RemoveGroupBlocks={RemoveGroupBlocks}
                // For Totals
                toggleSelectBlock={toggleSelectBlock}
                selectedIds={selectedIds}
                authState={authState}
                EditGroupName={EditGroupName}
                FilterTotal={FilterTotal}
              />
            );
          })}
      </ReactSortable>
    </>
  );
}
function BlockWrapper({
  block,
  blockIndex,
  setDSRReportsData,
  toggleSelectBlock,
  selectedIds,
  isMainItem,
  RemoveGroupBlocks,
  authState,
  EditGroupName,
  FilterTotal,
  FilterDeleteGroup,
}) {
  if (!block) return null;
  if (block?.type === 'Group') {
    return (
      <Box
        className={
          block.type === 'Group' &&
          block &&
          block?.children &&
          block?.children?.length > 0
            ? 'container-block group-container'
            : block.type === 'Group'
              ? 'container-block group-container empty-group'
              : 'text-block group-container'
        }
      >
        <Typography className="title-text fw600">{block?.content}</Typography>
        {block && block?.children && block?.children?.length > 0 ? (
          <Container
            block={block}
            setDSRReportsData={setDSRReportsData}
            blockIndex={blockIndex}
            RemoveGroupBlocks={RemoveGroupBlocks}
            isMainItem={isMainItem}
            toggleSelectBlock={toggleSelectBlock}
            selectedIds={selectedIds}
            authState={authState}
            EditGroupName={EditGroupName}
            FilterTotal={FilterTotal}
          />
        ) : (
          <CancelIcon
            className="delete-icon"
            onClick={() => FilterDeleteGroup(blockIndex)}
          />
        )}
        {}
      </Box>
    );
  } else {
    return (
      <Box
        className={
          selectedIds?.includes(block?.id)
            ? 'column-container selected-column'
            : !isMainItem
              ? 'column-container group-column'
              : 'column-container'
        }
      >
        <Box
          className={!isMainItem ? 'd-flex growname' : 'd-flex rowname'}
          onClick={() => {
            toggleSelectBlock(block?.id);
          }}
        >
          {selectedIds?.includes(block?.id) && (
            <CheckBoxIcon className="check-box" />
          )}
          <Typography className="title-text text-ellipsis-line category-name">
            {block?.type === 'total' && block?.SelectedData?.length > 0
              ? block?.content + ' ( ' + block?.SelectedData?.toString() + ' ) '
              : block?.content}
          </Typography>
        </Box>
        <Box className="delete-group">
          {!isMainItem && (
            <Tooltip
              title={<Typography>Remove from group</Typography>}
              classes={{
                tooltip: 'info-tooltip-container ',
              }}
            >
              <RemoveCircleIcon
                onClick={() => RemoveGroupBlocks(block?.id, blockIndex)}
                className="remove-from-group"
              />
            </Tooltip>
          )}
          {block?.type === 'total' && (
            <CancelIcon
              className="delete-icon"
              onClick={() => FilterTotal(isMainItem, block?.id, blockIndex)}
            />
          )}
        </Box>

        {block?.has_field_currency ? (
          <Typography className="currency-icon title-text">
            {' '}
            {authState?.currency_details && authState?.currency_details?.symbol
              ? authState?.currency_details?.symbol
              : '£'}
          </Typography>
        ) : (
          <></>
        )}
      </Box>
    );
  }
}
const GroupingReorder = ({
  onClose,
  GenGroupData,
  getDSRReportFilter,
  filterData,
  setAppliedFilter,
}) => {
  const { authState } = useContext(AuthContext);

  const [DSRReportsData, setDSRReportsData] = useState([]);
  const [selectedIds, setSelectedIds] = useState([]);
  const [openDrawer, setOpenDrawer] = useState(false);
  const [openAddToGroup, setOpenAddTOGroup] = useState(false);
  const [groupName, setGroupName] = useState('');
  const [SelectedGroup, setSelectedGroup] = useState();
  const [DSRReportsGroupData, setDSRReportsGroupData] = useState([]);

  const toggleSelectBlock = (id) => {
    setSelectedIds((prevSelected) =>
      prevSelected.includes(id)
        ? prevSelected.filter((selId) => selId !== id)
        : [...prevSelected, id]
    );
  };
  //Open group selection drawer
  const onChangeGroupName = () => {
    setOpenDrawer(true);
  };
  //Close group selection drawer
  const handleCloseDrawer = () => {
    setOpenDrawer(false);
    setGroupName('');
  };

  // Open Add to group drawer
  const AddToGroup = () => {
    setOpenAddTOGroup(true);
  };
  // Close Add to group
  const handleClose = () => {
    setOpenAddTOGroup(false);
    setGroupName('');
    setSelectedGroup();
  };
  const collectIds = (items) => {
    let ids = [];
    for (const item of items) {
      ids.push(item.id);
      if (item.children) {
        ids = ids.concat(collectIds(item.children));
      }
    }
    return ids;
  };

  const findSmallestMissingId = (ids) => {
    const idSet = new Set(ids);
    let smallestId = 1;
    while (idSet.has(smallestId)) {
      smallestId++;
    }
    return smallestId;
  };

  // Add object at passed index
  const addObjectAtIndex = (array, index, newObject) => [
    ...array.slice(0, index),
    newObject,
    ...array.slice(index),
  ];
  //  Add column Total from selected ids
  function findIndexInNestedArray(data, idToFind) {
    for (let parentIndex = 0; parentIndex < data.length; parentIndex++) {
      const block = data[parentIndex];
      if (block.id === idToFind) {
        return { parentIndex, childIndex: null }; // Found at the parent level
      }

      if (block.children) {
        const childIndex = block.children.findIndex(
          (child) => child.id === idToFind
        );
        if (childIndex !== -1) {
          return { parentIndex, childIndex }; // Found in the children array
        }
      }
    }

    return null; // Not found
  }

  function findParentIds(data, selectedIds) {
    const parentIdsSet = new Set();

    for (const idToFind of selectedIds) {
      for (const block of data) {
        if (block.id === idToFind && block.parent_id !== null) {
          parentIdsSet.add(block.parent_id);
          break; // Stop searching once the block is found
        }

        if (block.children?.some((child) => child.id === idToFind)) {
          const child = block.children.find((child) => child.id === idToFind);
          if (child?.parent_id !== null) {
            parentIdsSet.add(child.parent_id);
          }
          break; // Stop searching once the child is found
        }
      }
    }

    return Array.from(parentIdsSet); // Convert Set to Array
  }

  const getSelectedContent = (data, selectedIds, key) => {
    const result = [];

    const collectContent = (items) => {
      items.forEach((item) => {
        if (selectedIds.includes(item.id)) {
          result.push(item[key]);
        }
        if (item.children) {
          collectContent(item.children);
        }
      });
    };

    collectContent(data);
    return result;
  };
  // Selected Ids add into Group with Groupname
  const groupSelectedBlocks = (name) => {
    if (selectedIds.length === 0) return;

    const currentBlock = DSRReportsData;
    let filterBlock = currentBlock?.filter((f) => !selectedIds.includes(f?.id));
    const mergeList = _.concat(GenGroupData?.columns_group[0], DSRReportsData);
    const allIds = collectIds(mergeList);
    const smallestMissingId = findSmallestMissingId(allIds);

    const selectedLastId = selectedIds[selectedIds.length - 1]; // Find Selected last ID
    const findIndex = findIndexInNestedArray(DSRReportsData, selectedLastId); // Find Selected last ID index
    const updatedArray = addObjectAtIndex(
      filterBlock,
      findIndex.parentIndex + 1,
      {
        id: smallestMissingId,
        content: name,
        parent_id: null,
        type: 'Group',
        children: DSRReportsData.filter((block) =>
          selectedIds.includes(block.id)
        ).map((block) => ({
          ...block,
          type: block?.type ? block?.type : 'text',
          width: 2,
          parent_id: smallestMissingId,
        })),
      }
    );
    setDSRReportsData(updatedArray);

    setSelectedIds([]); // Clear selected blocks after grouping
  };

  // Add to Group for selected group and selected Ids
  const AddToGroupBlocks = (id) => {
    let currentBlock = DSRReportsData;
    let selectedItems = DSRReportsData.filter((block) =>
      selectedIds.includes(block.id)
    ).map((block) => ({
      ...block,
      type: 'text',
      width: 2,
      parent_id: id,
    }));
    let filterBlock = currentBlock?.map((f) =>
      f?.id === id
        ? { ...f, children: _.concat(f?.children, selectedItems) }
        : { ...f }
    );
    filterBlock = filterBlock?.filter(
      (block) => !selectedIds.includes(block.id)
    );
    setDSRReportsData(filterBlock);

    setSelectedIds([]); // Clear selected DSRReportsData after grouping
  };

  // Remove items from group and move it to outside group
  const RemoveGroupBlocks = (id, index) => {
    const removedData = DSRReportsData.map((group, groupIndex) =>
      groupIndex === index[0]
        ? {
            ...group,
            children: group?.children?.filter(
              (_, childIndex) => childIndex !== index[1]
            ),
          }
        : group
    );
    // Directly access the child item
    let newObjects = DSRReportsData[index[0]]?.children?.[index[1]];
    newObjects = { ...newObjects, parent_id: null };
    const updatedArray = addObjectAtIndex(
      removedData,
      index[0] + 1,
      newObjects
    );
    setDSRReportsData(updatedArray);
    setSelectedIds([]);
  };
  const FilterTotal = (ismain, id, gindex) => {
    const dsrData = DSRReportsData;
    if (ismain) {
      const filterData = dsrData?.filter((block) => id !== block.id);
      setDSRReportsData(filterData);
    } else {
      const removedData = DSRReportsData?.map((group, groupIndex) =>
        groupIndex === gindex[0]
          ? {
              ...group,
              children: group?.children?.filter(
                (_, childIndex) => childIndex !== gindex[1]
              ),
            }
          : group
      );
      setDSRReportsData(removedData);
    }
  };
  const FilterDeleteGroup = (index) => {
    const dsrData = DSRReportsData;
    const filterData = dsrData?.filter(
      (block, bindex) => bindex !== index?.[0]
    );
    setDSRReportsData(filterData);
  };
  const onChangeCreateTotal = () => {
    const dsrData = DSRReportsData;
    const selectedLastId = selectedIds[selectedIds.length - 1];

    const filterData = dsrData.filter((block) =>
      selectedIds.includes(block.id)
    );
    const uniqueParentIds = findParentIds(dsrData, selectedIds);
    const mergeList = _.concat(GenGroupData?.columns_group[0], DSRReportsData);
    const allIds = collectIds(mergeList);
    const smallestMissingId = findSmallestMissingId(allIds);
    const selectedContent = getSelectedContent(
      DSRReportsData,
      selectedIds,
      'content'
    );
    const selectedKey = getSelectedContent(DSRReportsData, selectedIds, 'key');
    const has_field_currency = getSelectedContent(
      DSRReportsData,
      selectedIds,
      'has_field_currency'
    );
    if (filterData.length > 0 || uniqueParentIds.length > 1) {
      const findIndex = findIndexInNestedArray(dsrData, selectedLastId);
      const newObject = {
        id: smallestMissingId,
        SelectedIds: selectedIds,
        content: `Total `,
        parent_id: null,
        type: 'total',
        has_field_currency: has_field_currency?.[0],
        key: `col${smallestMissingId}`,
        SelectedData:
          selectedContent && selectedContent?.length > 0 ? selectedContent : [],
        columns: selectedKey && selectedKey?.length > 0 ? selectedKey : [],
      };

      const updatedArray = addObjectAtIndex(
        dsrData,
        findIndex.parentIndex + 1,
        newObject
      );
      setDSRReportsData(updatedArray);
    } else {
      const findIndex = findIndexInNestedArray(dsrData, selectedLastId);
      const newObject = {
        id: smallestMissingId,
        SelectedIds: selectedIds,
        content: `Total `,
        parent_id: dsrData[findIndex.parentIndex]?.id,
        width: 2,
        type: 'total',
        key: `col${smallestMissingId}`,
        has_field_currency: has_field_currency?.[0],
        SelectedData:
          selectedContent && selectedContent?.length > 0 ? selectedContent : [],
        columns: selectedKey && selectedKey?.length > 0 ? selectedKey : [],
      };
      const updatedChildren = addObjectAtIndex(
        dsrData[findIndex.parentIndex]?.children || [],
        findIndex.childIndex + 1,
        newObject
      );

      const updatedData = [...dsrData];
      updatedData[findIndex.parentIndex] = {
        ...updatedData[findIndex.parentIndex],
        children: updatedChildren,
      };

      setDSRReportsData(updatedData);
    }

    setSelectedIds([]);
  };
  const CheckDsrSelectedData = () => {
    const filterData = DSRReportsData.filter((block) =>
      selectedIds.includes(block.id)
    );
    const uniqueParentIds = findParentIds(DSRReportsData, selectedIds);

    return (
      selectedIds &&
      selectedIds?.length > 1 &&
      uniqueParentIds &&
      filterData.length > 0 &&
      uniqueParentIds.length === 0
    );
  };
  const EditGroupName = () => {};
  const CheckAddDsrSelectedData = () => {
    const filterData = DSRReportsData.filter((block) =>
      selectedIds.includes(block.id)
    );
    const isGroup = DSRReportsData.find((block) => block?.type === 'Group');
    const uniqueParentIds = findParentIds(DSRReportsData, selectedIds);
    return (
      selectedIds &&
      selectedIds?.length > 0 &&
      uniqueParentIds &&
      isGroup &&
      filterData.length > 0 &&
      uniqueParentIds.length === 0
    );
  };
  const CheckTotalSelectedData = () => {
    const selecteddata = getSelectedContent(
      DSRReportsData,
      selectedIds,
      'has_field_currency'
    );
    const allSame = selecteddata?.every((value, _, arr) => value === arr[0]);

    return selectedIds && selectedIds?.length > 1 && allSame;
  };
  useEffect(() => {
    if (DSRReportsData && DSRReportsData?.length > 0) {
      // Filtered non group data and store in seprate state
      const filterGrp = DSRReportsData?.filter((f) => f?.type === 'Group');
      setDSRReportsGroupData(filterGrp);
    }
  }, [DSRReportsData]);

  useEffect(() => {
    if (
      GenGroupData &&
      GenGroupData?.columns_group &&
      GenGroupData?.columns_group?.length > 0
    ) {
      const filterFirst = GenGroupData?.columns_group?.filter(
        (f, findex) => findex !== 0
      );
      setDSRReportsData(filterFirst);
    }
  }, [GenGroupData]);

  return (
    <Box className="grouping-reorder-section">
      <Box>
        <Box className="group-options">
          <Box
            className={
              CheckDsrSelectedData()
                ? 'group-selection-section'
                : 'group-selection-section disabled-selection'
            }
            onClick={() => {
              CheckDsrSelectedData() && onChangeGroupName();
            }}
          >
            <InsertLinkIcon />
            <Typography className="title-text fw600">
              Group Selection
            </Typography>
          </Box>
          <Box
            className={
              CheckAddDsrSelectedData()
                ? 'group-selection-section'
                : 'group-selection-section disabled-selection'
            }
            onClick={() => {
              CheckAddDsrSelectedData() && AddToGroup();
            }}
          >
            <AddIcon />
            <Typography className="title-text fw600">Add to Group</Typography>
          </Box>
        </Box>
        <Box className="group-options mb8">
          <Box
            className={
              CheckTotalSelectedData()
                ? 'group-selection-section'
                : 'group-selection-section disabled-selection'
            }
            onClick={() => {
              CheckTotalSelectedData() && onChangeCreateTotal();
            }}
          >
            <InsertLinkIcon />
            <Box>
              <Tooltip
                title={<Typography>All selected value must be same</Typography>}
                classes={{
                  tooltip: 'info-tooltip-container ',
                }}
              >
                <Typography className="title-text fw600">
                  Total Selection
                </Typography>
              </Tooltip>
            </Box>
          </Box>
        </Box>

        <Box>
          {GenGroupData?.columns_group?.[0]?.content && (
            <Box className={'column-container disable-column-container'}>
              <Box className="d-flex">
                <Typography className="title-text">
                  {GenGroupData?.columns_group?.[0]?.content}
                </Typography>
              </Box>
            </Box>
          )}

          <ReactSortable
            list={DSRReportsData}
            setList={setDSRReportsData}
            {...sortableOptions}
            data-type="top-level"
          >
            {DSRReportsData?.map((block, blockIndex) => (
              <BlockWrapper
                key={block.id}
                block={block}
                blockIndex={[blockIndex]}
                setDSRReportsData={setDSRReportsData}
                toggleSelectBlock={toggleSelectBlock}
                selectedIds={selectedIds}
                isMainItem={true}
                RemoveGroupBlocks={RemoveGroupBlocks}
                authState={authState}
                EditGroupName={EditGroupName}
                FilterTotal={FilterTotal}
                FilterDeleteGroup={FilterDeleteGroup}
              />
            ))}
          </ReactSortable>
        </Box>
      </Box>

      <Box className="create-cancel-button pt32 pb16 justify-center">
        <CustomButton
          fullWidth
          className="title-text"
          variant="outlined"
          title="Cancel"
          onClick={() => {
            onClose();
          }}
        />
        <CustomButton
          fullWidth
          className="title-text"
          variant="contained"
          title={'Apply'}
          onClick={() => {
            onClose();
            setAppliedFilter({ ...filterData, isGroup: true });
            getDSRReportFilter(
              filterData,
              _.concat(GenGroupData?.columns_group[0], DSRReportsData)
            );
          }}
        />
      </Box>
      <RightDrawer
        className={''}
        anchor={'right'}
        open={openDrawer}
        onClose={handleCloseDrawer}
        title={'Group Selection'}
        content={
          <Box className="Group-name-selection">
            <CustomTextField
              fullWidth
              id="ename"
              name="ename"
              value={groupName ? groupName : ''}
              label="Group Name"
              required
              placeholder="Enter Group Name"
              onChange={(e) => {
                setGroupName(e.target.value);
              }}
            />
            <Box className="create-cancel-button pt32 justify-center">
              <CustomButton
                fullWidth
                variant="outlined"
                title="Cancel"
                onClick={() => {
                  handleCloseDrawer();
                }}
              />
              <CustomButton
                fullWidth
                variant="contained"
                title={'Save'}
                disabled={!groupName}
                onClick={() => {
                  groupSelectedBlocks(groupName);
                  handleCloseDrawer();
                  setGroupName('');
                }}
              />
            </Box>
          </Box>
        }
      />
      <RightDrawer
        className={''}
        anchor={'right'}
        open={openAddToGroup}
        onClose={handleClose}
        title={'Add to Group'}
        content={
          <Box className="Group-name-selection">
            <Box className="list-of-groups">
              {DSRReportsGroupData?.map((groups) => (
                <Box
                  className="group-select"
                  onClick={() => setSelectedGroup(groups?.id)}
                >
                  {SelectedGroup === groups?.id ? (
                    <CheckBoxIcon />
                  ) : (
                    <CheckBoxOutlineBlankIcon />
                  )}

                  <Typography className="title-text">
                    {groups?.content}
                  </Typography>
                </Box>
              ))}
            </Box>
            <Box className="create-cancel-button pt32 justify-center">
              <CustomButton
                fullWidth
                variant="outlined"
                title="Cancel"
                onClick={() => {
                  handleClose();
                }}
              />
              <CustomButton
                fullWidth
                variant="contained"
                title={'Save'}
                onClick={() => {
                  AddToGroupBlocks(SelectedGroup);
                  handleClose();
                }}
              />
            </Box>
          </Box>
        }
      />
    </Box>
  );
};

export default GroupingReorder;
