import React, { useRef, useState } from 'react';
import { getShiftsForDate } from './ShiftUtility';
import { ClickAwayListener, Popper, Tooltip } from '@mui/material';
import { AddShiftTooltip } from './AddShift/AddShiftTooltip';
import { TempCoverShiftTooltip } from './TempCoverShift/TempCoverShiftTooltip';
import { ShiftSwapTooltip } from './ShiftSwap/ShiftSwapTooltip';
import { ShiftHistory } from './ShiftHistory/ShiftHistory';
import { ShiftDropTooltip } from './ShiftDrop/ShiftDropTooltip';
import AddIcon from '@mui/icons-material/Add';
import LocalCafeIcon from '@mui/icons-material/LocalCafe';
import BlockIcon from '@mui/icons-material/Block';
import { CellMenu, ShiftMenu } from './ShiftMenu';
import {
  ShiftStatistics,
  ShiftSummaryStatistics,
} from './ShiftStatisticsTooltip';
import { AddAvailabilityTooltip } from './AddAvailability/AddAvailabilityTooltip';
import UserAvatar from '../Avatar/UserAvatar';
import moment from 'moment';
import DialogBox from '../Modalbox';
import axiosInstanceOrg from '@/helper/axios/axiosInstanceOrg';
import { ROTA_URLS } from '@/helper/constants/urls';
import { setApiMessage } from '@/helper/common/commonFunctions';
import PreLoader from '../Loader';
import SwapHorizIcon from '@mui/icons-material/SwapHoriz';
import { fetchFromStorage } from '@/helper/context';
import { identifiers } from '@/helper/constants/identifier';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import ConfirmationModal from '../ConfirmationModal';
import AirplanemodeActiveIcon from '@mui/icons-material/AirplanemodeActive';

// ========== Left Cell Components ==========
export const StaffNameCell = ({ name, shifts, isViewType, staff }) => {
  return (
    <td className="shift-details">
      <div className="shift-name-container">
        <UserAvatar
          name={name}
          src={staff?.user_avatar_link}
          classname="shift-avatar"
        />
        <Tooltip
          title={
            <div>
              <p className="p12">{name}</p>
            </div>
          }
          arrow
          classes={{
            tooltip:
              'calendar-event-list-tooltip-custom calendar-event-other-details-tooltip',
          }}
        >
          <p className="shift-name">{name}</p>
        </Tooltip>
      </div>
      {shifts?.length > 0 && (
        <div className="shift-details-wrp">
          <ShiftStatistics shifts={shifts} isViewType={isViewType} />
        </div>
      )}
    </td>
  );
};

// ========== Shift Cell Components ==========
export const ShiftCell = ({
  shifts,
  day,
  rowKey,
  hoveredCell,
  setHoveredCell,
  menuOpen,
  setMenuOpen,
  shiftMenuOpen,
  setShiftMenuOpen,
  staffListData,
  rolesList,
  departmentList,
  locationList,
  rowUserData,
  filterData,
  getRotaShiftList,
  isViewType,
  dayOffItem,
  dayOffList,
  setDayOffList,
  viewAccessOnly,
  availabilityItem,
  setIsUpdatedShift,
  userLeaveItem,
}) => {
  const authdata = fetchFromStorage(identifiers?.AUTH_DATA);
  const shiftsForDate = getShiftsForDate(shifts, day);
  const cellDate = `${day.format('YYYY-MM-DD')}-${rowKey}`;
  const isCellHovered = hoveredCell === cellDate;
  const isLoggedInUser = viewAccessOnly || authdata?.id === rowUserData?.id;

  const [addShiftTooltip, setAddShiftTooltip] = useState(false);
  const [selectedShiftData, setSelectedShiftData] = useState(null);
  const [addShiftAnchorEl, setAddShiftAnchorEl] = useState(null);
  const [menuTooltipAction, setMenuTooltipAction] = useState(null);
  const [shiftHistoryModal, setShiftHistoryModal] = useState(false);
  const [deleteShiftModal, setDeleteShiftModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const shiftActionsRef = useRef(null);

  const handleAddShiftTooltipOpen = (e, selectedIndex) => {
    setAddShiftTooltip(true);
    setAddShiftAnchorEl(shiftActionsRef.current);
    setShiftMenuOpen(false);
    setMenuOpen(false);
    setSelectedShiftData(
      selectedIndex !== ''
        ? shiftsForDate?.find((s, index) => index === selectedIndex)
        : null
    );
  };

  const handleAddShiftTooltipClose = () => {
    setAddShiftTooltip(false);
    setAddShiftAnchorEl(null);
    setMenuTooltipAction(null);
  };

  const handleShiftHistoryModalOpen = (e, selectedIndex) => {
    setSelectedShiftData(
      selectedIndex !== ''
        ? shiftsForDate?.find((s, index) => index === selectedIndex)
        : null
    );
    setShiftMenuOpen(false);
    setMenuOpen(false);
    setShiftHistoryModal(true);
  };

  const handleShiftHistoryModalClose = () => {
    setShiftHistoryModal(false);
  };

  const handleDeleteShiftModalOpen = (e, selectedIndex) => {
    setSelectedShiftData(
      selectedIndex !== ''
        ? shiftsForDate?.find((s, index) => index === selectedIndex)
        : null
    );
    setShiftMenuOpen(false);
    setMenuOpen(false);
    setDeleteShiftModal(true);
  };

  const handleDeleteShiftModalClose = () => {
    setDeleteShiftModal(false);
  };

  const staffListOptions = [...staffListData];

  staffListOptions.unshift({
    id: 'open_shift',
    user_full_name: 'Open Shift',
  });

  // handle delete shift
  const handleDeleteShift = async () => {
    setIsLoading(true);
    try {
      const { status, data } = await axiosInstanceOrg.delete(
        ROTA_URLS?.SHIFT_URL + `/${selectedShiftData?.id}`
      );
      if (status === 200 || status === 201) {
        setApiMessage('success', data?.message);
        setIsLoading(false);
        getRotaShiftList();
        handleDeleteShiftModalClose();
        handleAddShiftTooltipClose();
        setIsLoading(false);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setIsLoading(false);
    }
  };

  //  handle set as open shift
  const handleAddRemoveDayOff = async () => {
    const userID = dayOffItem?.userId || rowUserData?.id;
    const sendData = {
      userId: userID,
      date: day.format('YYYY-MM-DD'),
      dayOff: !dayOffItem?.dayOff,
      notes: '',
    };

    if (!dayOffItem?.dayOff) {
      try {
        const { status, data } = await axiosInstanceOrg.post(
          ROTA_URLS?.DAY_OFF_URL,
          sendData
        );
        if (status === 200 || status === 201) {
          setApiMessage('success', data?.message);
          setDayOffList([...dayOffList, data?.data]);
          dayOffItem.dayOff = data?.data?.dayOff;
          setMenuOpen(false);
          setShiftMenuOpen(false);
        }
      } catch (error) {
        setApiMessage('error', error?.response?.data?.message);
      }
    } else {
      try {
        const { status, data } = await axiosInstanceOrg.put(
          ROTA_URLS?.DAY_OFF_URL + `/${dayOffItem?.id}`,
          sendData
        );
        if (status === 200 || status === 201) {
          setApiMessage('success', data?.message);
          const removeDayOff = dayOffList?.filter(
            (item) => item?.id !== dayOffItem?.id
          );
          setDayOffList(removeDayOff);
          setMenuOpen(false);
          setShiftMenuOpen(false);
        }
      } catch (error) {
        setApiMessage('error', error?.response?.data?.message);
      }
    }
  };

  const shiftAddView = () => {
    return (
      <div>
        {availabilityItem !== null && availabilityItem !== undefined && (
          <div className="availability-view-container">
            {availabilityItem?.timeZone?.length > 0 ? (
              availabilityItem?.timeZone?.map((item, index) => {
                return (
                  <Tooltip
                    title={
                      <div>
                        <p className="p12">
                          {availabilityItem?.available === 1
                            ? 'Available'
                            : 'Unavailable'}{' '}
                          : {moment.utc(item?.startTime).format('HH:mm')} -{' '}
                          {moment.utc(item?.endTime).format('HH:mm')}
                        </p>
                      </div>
                    }
                    arrow
                    placement="top"
                    classes={{
                      tooltip:
                        'calendar-event-list-tooltip-custom calendar-event-other-details-tooltip',
                    }}
                  >
                    <div
                      key={index}
                      className={`availability-view-wrapper ${isViewType === 'week' ? 'week-view' : ''} ${availabilityItem?.available === 1 ? 'available' : 'unavailable'}`}
                    >
                      {availabilityItem?.available === 1 ? (
                        <CheckCircleOutlineIcon />
                      ) : (
                        <HighlightOffIcon />
                      )}
                      {isViewType === 'week' && (
                        <span>
                          {moment.utc(item?.startTime).format('HH:mm')} -{' '}
                          {moment.utc(item?.endTime).format('HH:mm')}
                        </span>
                      )}
                    </div>
                  </Tooltip>
                );
              })
            ) : (
              <Tooltip
                title={
                  <div>
                    <p className="p12">
                      {availabilityItem?.available === 1
                        ? 'Available'
                        : 'Unavailable'}
                      : All day
                    </p>
                  </div>
                }
                arrow
                placement="top"
                classes={{
                  tooltip:
                    'calendar-event-list-tooltip-custom calendar-event-other-details-tooltip',
                }}
              >
                <div
                  className={`availability-view-wrapper ${isViewType === 'week' ? 'week-view' : ''} ${availabilityItem?.available === 1 ? 'available' : 'unavailable'}`}
                >
                  {availabilityItem?.available === 1 ? (
                    <CheckCircleOutlineIcon />
                  ) : (
                    <HighlightOffIcon />
                  )}
                  {isViewType === 'week' && <span>All day</span>}
                </div>
              </Tooltip>
            )}
          </div>
        )}
        {userLeaveItem !== null && userLeaveItem !== undefined && (
          <div className="user-leave-view-container">
            <Tooltip
              title={
                <div>
                  <p className="p12 text-align">Holiday:</p>
                  <p className="p12">
                    {moment
                      .utc(userLeaveItem?.start_date)
                      .format('ddd DD MMM YYYY')}{' '}
                    -{' '}
                    {moment
                      .utc(userLeaveItem?.end_date)
                      .format('ddd DD MMM YYYY')}
                  </p>
                </div>
              }
              arrow
              placement="top"
              classes={{
                tooltip:
                  'calendar-event-list-tooltip-custom calendar-event-other-details-tooltip',
              }}
            >
              <div className={`user-leave-view-wrapper holiday`}>
                <AirplanemodeActiveIcon style={{ rotate: '90deg' }} />
              </div>
            </Tooltip>
          </div>
        )}
        {shiftsForDate?.length > 0 ? (
          <>
            {shiftsForDate?.map((shift, idx) => (
              <ShiftDetailsComponent
                key={idx}
                shift={shift}
                isHovered={isCellHovered || addShiftTooltip}
                shiftMenuOpen={shiftMenuOpen}
                onShiftMenuOpen={setShiftMenuOpen}
                isViewType={isViewType}
                handleAddShiftTooltipOpen={(e) =>
                  handleAddShiftTooltipOpen(e, idx)
                }
                rowKey={rowKey}
                getRotaShiftList={getRotaShiftList}
                setMenuTooltipAction={setMenuTooltipAction}
                handleShiftHistoryModalOpen={(e) =>
                  handleShiftHistoryModalOpen(e, idx)
                }
                handleDeleteShiftModalOpen={(e) =>
                  handleDeleteShiftModalOpen(e, idx)
                }
                dayOffItem={dayOffItem}
                handleAddRemoveDayOff={handleAddRemoveDayOff}
                viewAccessOnly={viewAccessOnly}
                isLoggedInUser={isLoggedInUser}
                authdata={authdata}
                setIsUpdatedShift={setIsUpdatedShift}
              />
            ))}
            <div className="shift-actions-wrp" ref={shiftActionsRef}>
              <div className="shift-actions shift-actions-bottom">
                {isLoggedInUser && (isCellHovered || addShiftTooltip) && (
                  <CellMenu
                    shiftsForDate={shiftsForDate}
                    isOpen={menuOpen}
                    rowKey={rowKey}
                    onOpen={() => setMenuOpen(true)}
                    onClose={() => setMenuOpen(false)}
                    handleAddShiftTooltipOpen={(e) =>
                      handleAddShiftTooltipOpen(e, '')
                    }
                    dayOffItem={dayOffItem}
                    handleAddRemoveDayOff={handleAddRemoveDayOff}
                    viewAccessOnly={viewAccessOnly}
                  />
                )}
              </div>
            </div>
          </>
        ) : (
          isLoggedInUser &&
          (isCellHovered || addShiftTooltip) && (
            <div className="shift-actions-wrp" ref={shiftActionsRef}>
              <div className="shift-actions">
                {viewAccessOnly ? (
                  <div
                    className={`add-shift-icon ${addShiftTooltip ? 'isActive' : ''}`}
                    onClick={(e) => handleAddShiftTooltipOpen(e, '')}
                  >
                    <AddIcon fontSize="small" />
                  </div>
                ) : (
                  <div className="add-shift-icon blank-add-shift-icon"></div>
                )}

                <CellMenu
                  shiftsForDate={shiftsForDate}
                  isOpen={menuOpen}
                  rowKey={rowKey}
                  onOpen={() => setMenuOpen(true)}
                  onClose={() => setMenuOpen(false)}
                  handleAddShiftTooltipOpen={(e) =>
                    handleAddShiftTooltipOpen(e, '')
                  }
                  dayOffItem={dayOffItem}
                  handleAddRemoveDayOff={handleAddRemoveDayOff}
                  viewAccessOnly={viewAccessOnly}
                />
              </div>
            </div>
          )
        )}
      </div>
    );
  };

  return (
    <td
      key={cellDate}
      className={`shift-cell ${dayOffItem?.dayOff ? 'user-day-off' : ''}`}
      onMouseEnter={() => setHoveredCell(cellDate)}
      onMouseLeave={() => setHoveredCell(null)}
    >
      {isLoading && <PreLoader />}
      {addShiftTooltip && menuTooltipAction === null && (
        <ShiftAddComponent
          staffListOptions={staffListOptions}
          rolesList={rolesList}
          departmentList={departmentList}
          locationList={locationList}
          rowUserData={rowUserData}
          addShiftTooltip={addShiftTooltip}
          addShiftAnchorEl={addShiftAnchorEl}
          handleAddShiftTooltipClose={handleAddShiftTooltipClose}
          currentCellDate={day}
          selectedShiftData={selectedShiftData}
          getRotaShiftList={getRotaShiftList}
          rowKey={rowKey}
          viewAccessOnly={viewAccessOnly}
          setDeleteShiftModal={setDeleteShiftModal}
          setShiftHistoryModal={setShiftHistoryModal}
        />
      )}
      {addShiftTooltip && menuTooltipAction === 'assignTempCover' && (
        <ShiftTempCoverComponent
          staffListOptions={staffListOptions}
          locationList={locationList}
          filterData={filterData}
          rowUserData={rowUserData}
          addShiftTooltip={addShiftTooltip}
          addShiftAnchorEl={addShiftAnchorEl}
          handleAddShiftTooltipClose={handleAddShiftTooltipClose}
          selectedShiftData={selectedShiftData}
          getRotaShiftList={getRotaShiftList}
          rowKey={rowKey}
        />
      )}
      {addShiftTooltip && menuTooltipAction === 'shiftSwap' && (
        <ShiftSwapComponent
          staffListOptions={staffListOptions}
          addShiftTooltip={addShiftTooltip}
          addShiftAnchorEl={addShiftAnchorEl}
          handleAddShiftTooltipClose={handleAddShiftTooltipClose}
          selectedShiftData={selectedShiftData}
          getRotaShiftList={getRotaShiftList}
          rowKey={rowKey}
        />
      )}
      {addShiftTooltip && menuTooltipAction === 'shiftDrop' && (
        <ShiftDropComponent
          addShiftTooltip={addShiftTooltip}
          addShiftAnchorEl={addShiftAnchorEl}
          handleAddShiftTooltipClose={handleAddShiftTooltipClose}
          selectedShiftData={selectedShiftData}
          getRotaShiftList={getRotaShiftList}
          rowKey={rowKey}
        />
      )}
      {shiftAddView()}
      {shiftHistoryModal && (
        <DialogBox
          open={shiftHistoryModal}
          handleClose={() => {
            handleShiftHistoryModalClose();
          }}
          className="shift-history-modal"
          title="History"
          content={
            <>
              <ShiftHistory
                shiftId={selectedShiftData?.id}
                staffListData={staffListData}
                rolesList={rolesList}
                departmentList={departmentList}
                locationList={locationList}
              />
            </>
          }
        />
      )}
      {deleteShiftModal && (
        <DialogBox
          open={deleteShiftModal}
          handleClose={() => {
            handleDeleteShiftModalClose();
          }}
          className="confirmation-modal"
          title="Are you sure?"
          content={
            <>
              <ConfirmationModal
                handleCancel={handleDeleteShiftModalClose}
                handleConfirm={handleDeleteShift}
                text="Are you sure you wish to delete this shift? Hold ALT while clicking to skip this message in future."
              />
            </>
          }
        />
      )}
    </td>
  );
};

// ========== Shift Details Components ==========
export const ShiftDetailsComponent = ({
  shift,
  isHovered,
  shiftMenuOpen,
  onShiftMenuOpen,
  isViewType,
  handleAddShiftTooltipOpen,
  rowKey,
  getRotaShiftList,
  setMenuTooltipAction,
  handleShiftHistoryModalOpen,
  handleDeleteShiftModalOpen,
  dayOffItem,
  handleAddRemoveDayOff,
  viewAccessOnly,
  isLoggedInUser,
  authdata,
  setIsUpdatedShift,
}) => {
  const start_time = moment(shift?.startTime).utc().format('HH:mm');
  const end_time = moment(shift?.endTime).utc().format('HH:mm');
  const role_name = shift?.role?.role_name || 'No Role';
  const break_time = shift?.minutesBreak;
  const shift_published = shift?.isPublished;

  const isSwapActive =
    shift?.swapRequests?.some(
      (swap) => swap?.status === 'active' && swap?.adminStatus === 'active'
    ) ?? false;

  const isAcceptSwap = shift?.isSwap === 1 && isSwapActive;

  const isSwapRejected =
    shift?.swapRequests?.some(
      (swap) => swap?.status === 'rejected' || swap?.adminStatus === 'rejected'
    ) ?? false;

  const isRequested =
    shift?.swapRequests?.some((swap) => swap?.toUserId === authdata?.id) ??
    false;

  const isSwap =
    (isLoggedInUser || isRequested) && shift?.isSwap === 1 && !isSwapRejected;

  const isDropActive =
    shift?.drops?.some((drop) => drop?.status === 'active') ?? false;

  const isAcceptDrop = isLoggedInUser && shift?.isDropped === 1 && isDropActive;

  const isDropRejected =
    shift?.drops?.some((drop) => drop?.status === 'deleted') ?? false;

  const isDropped = isLoggedInUser && shift?.isDropped === 1 && !isDropRejected;

  return (
    <div className={`user-shift ${shift_published ? '' : 'shift-unpublished'}`}>
      <div
        onClick={
          isLoggedInUser || isSwap || isDropped
            ? handleAddShiftTooltipOpen
            : null
        }
      >
        {isViewType === 'month' ? (
          <>
            <div className="shift-time-role">
              <p className="shift-time">{start_time}</p>
              <p className="shift-time">{end_time}</p>
              <p className="shift-role">{role_name}</p>
            </div>
            <div className="shift-break">
              {break_time > 0 && <LocalCafeIcon fontSize="small" />}
              {(isSwap || isSwapRejected) && (
                <SwapHorizIcon
                  className={`swap-icon ${isAcceptSwap ? 'isActive' : isSwapRejected ? 'isRejected' : ''}`}
                  fontSize="small"
                />
              )}
              {(isDropped || isDropRejected) && (
                <BlockIcon
                  className={`swap-icon ${isAcceptDrop ? 'isActive' : isDropRejected ? 'isRejected' : ''}`}
                  fontSize="small"
                />
              )}
            </div>
          </>
        ) : (
          <div className="shift-time-role">
            <p className="shift-time">
              {start_time} - {end_time}
            </p>
            <div className="shift-break">
              <p className="shift-role">{role_name}</p>
              <p className="shift-break-time">
                {break_time > 0 && <LocalCafeIcon fontSize="small" />}
                <span>{break_time > 0 && break_time + 'm'}</span>
                {(isSwap || isSwapRejected) && (
                  <SwapHorizIcon
                    className={`swap-icon ${isAcceptSwap ? 'isActive' : isSwapRejected ? 'isRejected' : ''}`}
                    fontSize="small"
                  />
                )}
                {(isDropped || isDropRejected) && (
                  <BlockIcon
                    className={`swap-icon ${isAcceptDrop ? 'isActive' : isDropRejected ? 'isRejected' : ''}`}
                    fontSize="small"
                  />
                )}
              </p>
            </div>
          </div>
        )}
      </div>

      {isLoggedInUser && isHovered && (
        <div className="user-shift-actions">
          <ShiftMenu
            shift={shift}
            rowKey={rowKey}
            isOpen={shiftMenuOpen === shift?.id}
            onOpen={onShiftMenuOpen}
            onClose={() => onShiftMenuOpen(null)}
            handleAddShiftTooltipOpen={handleAddShiftTooltipOpen}
            getRotaShiftList={getRotaShiftList}
            setMenuTooltipAction={setMenuTooltipAction}
            handleShiftHistoryModalOpen={handleShiftHistoryModalOpen}
            handleDeleteShiftModalOpen={handleDeleteShiftModalOpen}
            dayOffItem={dayOffItem}
            handleAddRemoveDayOff={handleAddRemoveDayOff}
            viewAccessOnly={viewAccessOnly}
            isSwap={isSwap || isDropped}
            setIsUpdatedShift={setIsUpdatedShift}
          />
        </div>
      )}
    </div>
  );
};

// ========== Shift Summary Components ==========
export const ShiftSummaryCell = ({ shifts, currentMonth, isViewType }) => (
  <ShiftSummaryStatistics
    shifts={shifts}
    currentMonth={currentMonth}
    isViewType={isViewType}
  />
);

// ========== Shift Add Components ==========
export const ShiftAddComponent = ({
  staffListOptions,
  rolesList,
  departmentList,
  locationList,
  rowUserData,
  addShiftTooltip,
  addShiftAnchorEl,
  handleAddShiftTooltipClose,
  currentCellDate,
  selectedShiftData,
  getRotaShiftList,
  rowKey,
  viewAccessOnly,
  setDeleteShiftModal,
  setShiftHistoryModal,
}) => {
  return (
    <ClickAwayListener onClickAway={handleAddShiftTooltipClose}>
      <div>
        <Popper
          id={rowKey}
          open={addShiftTooltip}
          anchorEl={addShiftAnchorEl}
          placement="right"
          className="calendar-rota-add-shift-tooltip-custom"
        >
          <AddShiftTooltip
            staffListOptions={staffListOptions}
            rolesList={rolesList}
            departmentList={departmentList}
            locationList={locationList}
            rowUserData={rowUserData}
            handleAddShiftTooltipClose={handleAddShiftTooltipClose}
            currentCellDate={currentCellDate}
            selectedShiftData={selectedShiftData}
            getRotaShiftList={getRotaShiftList}
            rowKey={rowKey}
            viewAccessOnly={viewAccessOnly}
            setDeleteShiftModal={setDeleteShiftModal}
            setShiftHistoryModal={setShiftHistoryModal}
          />
        </Popper>
      </div>
    </ClickAwayListener>
  );
};

// ========== Shift Temporary Cover Components ==========
export const ShiftTempCoverComponent = ({
  staffListOptions,
  locationList,
  filterData,
  rowUserData,
  addShiftTooltip,
  addShiftAnchorEl,
  handleAddShiftTooltipClose,
  selectedShiftData,
  getRotaShiftList,
  rowKey,
}) => {
  return (
    <ClickAwayListener onClickAway={handleAddShiftTooltipClose}>
      <div>
        <Popper
          id={rowKey}
          open={addShiftTooltip}
          anchorEl={addShiftAnchorEl}
          placement="right"
          className="calendar-rota-add-shift-tooltip-custom"
        >
          <TempCoverShiftTooltip
            staffListOptions={staffListOptions}
            locationList={locationList}
            filterData={filterData}
            rowUserData={rowUserData}
            handleAddShiftTooltipClose={handleAddShiftTooltipClose}
            selectedShiftData={selectedShiftData}
            getRotaShiftList={getRotaShiftList}
          />
        </Popper>
      </div>
    </ClickAwayListener>
  );
};

// ========== Shift Swap Components ==========
export const ShiftSwapComponent = ({
  staffListOptions,
  addShiftTooltip,
  addShiftAnchorEl,
  handleAddShiftTooltipClose,
  selectedShiftData,
  getRotaShiftList,
  rowKey,
}) => {
  return (
    <ClickAwayListener onClickAway={handleAddShiftTooltipClose}>
      <div>
        <Popper
          id={rowKey}
          open={addShiftTooltip}
          anchorEl={addShiftAnchorEl}
          placement="right"
          className="calendar-rota-add-shift-tooltip-custom"
        >
          <ShiftSwapTooltip
            staffListOptions={staffListOptions}
            handleAddShiftTooltipClose={handleAddShiftTooltipClose}
            selectedShiftData={selectedShiftData}
            getRotaShiftList={getRotaShiftList}
          />
        </Popper>
      </div>
    </ClickAwayListener>
  );
};

// ========== Shift Drop Components ==========
export const ShiftDropComponent = ({
  addShiftTooltip,
  addShiftAnchorEl,
  handleAddShiftTooltipClose,
  selectedShiftData,
  getRotaShiftList,
  rowKey,
}) => {
  return (
    <ClickAwayListener onClickAway={handleAddShiftTooltipClose}>
      <div>
        <Popper
          id={rowKey}
          open={addShiftTooltip}
          anchorEl={addShiftAnchorEl}
          placement="right"
          className="calendar-rota-add-shift-tooltip-custom"
        >
          <ShiftDropTooltip
            handleAddShiftTooltipClose={handleAddShiftTooltipClose}
            selectedShiftData={selectedShiftData}
            getRotaShiftList={getRotaShiftList}
          />
        </Popper>
      </div>
    </ClickAwayListener>
  );
};

// ========== Availability Components ==========
export const AvailabilityComponent = ({
  addAvailabilityTooltip,
  addAvailabilityAnchorEl,
  handleAddAvailabilityModalClose,
  singleAvailabilityData,
  currentCellDate,
  filterData,
  getDashboardAvailabilityList,
}) => {
  return (
    <ClickAwayListener onClickAway={handleAddAvailabilityModalClose}>
      <div>
        <Popper
          // id={rowKey}
          open={addAvailabilityTooltip}
          anchorEl={addAvailabilityAnchorEl}
          placement="right"
          className="calendar-rota-add-shift-tooltip-custom"
        >
          <AddAvailabilityTooltip
            handleAddAvailabilityModalClose={handleAddAvailabilityModalClose}
            singleAvailabilityData={singleAvailabilityData}
            filterData={filterData}
            currentCellDate={currentCellDate}
            getDashboardAvailabilityList={getDashboardAvailabilityList}
          />
        </Popper>
      </div>
    </ClickAwayListener>
  );
};
