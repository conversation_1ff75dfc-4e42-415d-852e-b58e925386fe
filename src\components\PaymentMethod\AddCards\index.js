'use client';
import React, { useState } from 'react';
import { Box, Tab } from '@mui/material';
import { TabContext, TabList, TabPanel } from '@mui/lab';
import PayNow from '../PayNow';
// import PaymentPublicLink from '../PaymentPublicLink';
import './addcard.scss';

export default function AddCard({
  paymentProvider,
  selectedPlan,
  PurchasedPlan,
  plansList,
}) {
  const [tab, setTab] = useState(1);
  const reports_tabs = [
    { id: 1, name: 'Pay Now' },
    // { id: 2, name: 'Payment Public Link' },
  ];
  const tabChangeHandler = (event, newValue) => {
    setTab(newValue);
  };
  return (
    <Box className="payment-details-sec">
      <TabContext value={String(tab)}>
        <Box className="pay-tabs-wrap">
          <Box className="report-tabs">
            <TabList
              variant="scrollable"
              scrollButtons="auto"
              onChange={tabC<PERSON>e<PERSON>and<PERSON>}
              aria-label="action tabs"
              className="tab-list-sec"
            >
              {reports_tabs?.map((obj, index) => {
                return (
                  <Tab
                    key={index}
                    label={obj?.name}
                    value={String(obj?.id)}
                    className="tab-name"
                  />
                );
              })}
            </TabList>
          </Box>
        </Box>
        <TabPanel value="1" className="pl0 pr0 pb0 pt0">
          <PayNow
            paymentProvider={paymentProvider}
            selectedPlan={selectedPlan}
            PurchasedPlan={PurchasedPlan}
            plansList={plansList}
          />
        </TabPanel>
        {/* <TabPanel value="2" className="pl0 pr0 pb0 pt0">
          <PaymentPublicLink selectedPlan={selectedPlan} />
        </TabPanel> */}
      </TabContext>
    </Box>
  );
}
