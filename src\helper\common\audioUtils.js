// Utility function to stop all audio players
export const stopAllAudio = () => {
  // Stop all HTML5 audio elements
  const audioElements = document.querySelectorAll('audio');
  audioElements.forEach((audio) => {
    if (!audio.paused) {
      audio.pause();
    }
  });

  // Stop all video elements (in case they have audio)
  const videoElements = document.querySelectorAll('video');
  videoElements.forEach((video) => {
    if (!video.paused) {
      video.pause();
    }
  });

  // Stop ReactPlayer components (if any)
  const reactPlayerElements = document.querySelectorAll('.react-player-audio');
  reactPlayerElements.forEach((player) => {
    const iframe = player.querySelector('iframe');
    if (iframe && iframe.src) {
      // For ReactPlayer, we need to reload the iframe to stop audio
      const currentSrc = iframe.src;
      iframe.src = '';
      setTimeout(() => {
        iframe.src = currentSrc;
      }, 100);
    }
  });
};
