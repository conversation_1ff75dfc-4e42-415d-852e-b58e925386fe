'use client';
import React, { useLayoutEffect } from 'react';
import { Box, Typography } from '@mui/material';
import PreLoader from '@/components/UI/Loader';
import DSRCategoryMap from '@/components/DSR/DSRCategory/Categorymap';
import AddMainCategory from '@/components/DSR/DSRCategory/MainCategory';
import SubCategory from '@/components/DSR/DSRCategory/SubCategory';

export default function DSRCategories({
  loader,
  paymentCateList,
  handleAddPayment,
  handleAddSubPayment,
  handleReorderPayment,
  deleteMainCategory,
  handleReorderSubPayment,
  deletesubCategory,
  addDSR,
  setAddDSR,
}) {
  useLayoutEffect(() => {
    if (paymentCateList && paymentCateList?.length > 0 && addDSR === '') {
      setAddDSR({
        mainCatgory: true,
        payment_type_usage: true,
        CategoryList: paymentCateList,
      });
    }
  }, [paymentCateList]);

  return (
    <>
      {loader && <PreLoader />}
      <Box className="dsr-setting-container">
        <Box className="dsr-settings-right-container">
          {paymentCateList &&
            (paymentCateList?.length > 0 || paymentCateList?.length === 0) && (
              <DSRCategoryMap
                paymentCateList={paymentCateList}
                AddDSR={addDSR}
                setAddDSR={setAddDSR}
              />
            )}
        </Box>
        <Box className="dsr-settings-left-container">
          {addDSR &&
          addDSR?.payment_type_usage &&
          paymentCateList?.length === 0 ? (
            <>
              <Typography className="title-text fw600 text-underline color-dark-blue pt8">
                {!addDSR?.mainCatgory && addDSR?.isUpdate
                  ? `Update ${addDSR?.list?.payment_type_title}`
                  : !addDSR?.mainCatgory
                    ? `Add item into DSR setting`
                    : 'DSR setting'}
              </Typography>
              <Box>
                <AddMainCategory
                  AddDSR={addDSR}
                  AddPayment={handleAddPayment}
                  deleteMainCategory={deleteMainCategory}
                />
              </Box>
            </>
          ) : addDSR &&
            addDSR?.payment_type_usage &&
            (addDSR?.payment_type_usage === 'income' ||
              addDSR?.payment_type_usage === 'other' ||
              addDSR?.payment_type_usage === 'expense' ||
              addDSR?.mainCatgory) ? (
            <>
              <Typography className="title-text fw600 text-underline color-dark-blue pt8">
                {!addDSR?.mainCatgory && addDSR?.isUpdate
                  ? `Update ${addDSR?.list?.payment_type_title}`
                  : !addDSR?.mainCatgory
                    ? `Add item into DSR setting`
                    : 'DSR setting'}
              </Typography>
              <Box>
                <AddMainCategory
                  AddDSR={addDSR}
                  AddPayment={handleAddPayment}
                  ReorderPayment={handleReorderPayment}
                  deleteMainCategory={deleteMainCategory}
                />
              </Box>
            </>
          ) : addDSR &&
            addDSR?.payment_type_usage &&
            (addDSR?.payment_type_usage === 'single' ||
              addDSR?.payment_type_usage === 'multiple') ? (
            <>
              <Box>
                <SubCategory
                  AddDSR={addDSR}
                  AddSubPayment={handleAddSubPayment}
                  ReorderSubPayment={handleReorderSubPayment}
                  deletesubCategory={deletesubCategory}
                />
              </Box>
            </>
          ) : (
            <></>
          )}
        </Box>
      </Box>
    </>
  );
}
