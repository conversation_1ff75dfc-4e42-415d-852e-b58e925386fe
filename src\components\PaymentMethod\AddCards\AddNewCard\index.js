'use client';
import React, { useEffect, useRef, useState } from 'react';
import CustomTextField from '@/components/UI/CustomTextField';
import { Box, Typography } from '@mui/material';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import CustomButton from '@/components/UI/CustomButton';
import { CloseOutlined } from '@mui/icons-material';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { ORG_URLS } from '@/helper/constants/urls';
import { setApiMessage, stripePromise } from '@/helper/common/commonFunctions';
import {
  CardNumberElement,
  Elements,
  useStripe,
  useElements,
} from '@stripe/react-stripe-js';
import {
  CustomCardCvcElement,
  CustomCardExpiryElement,
  CustomCardNumberElement,
} from '@/components/UI/CustomCardsElements/CardElementField';
import PreLoader from '@/components/UI/Loader';
import './addnewcard.scss';

const CardForm = ({
  setShowAddCardModal,
  cardToEdit,
  getAllCads,
  setSelectedCardToEdit,
  isClose = true,
  // cardData,
}) => {
  const stripe = useStripe();
  const elements = useElements();
  const formikRef = useRef(null);
  const [isLoading, setIsLoading] = useState(false);

  // update card holder name
  useEffect(() => {
    formikRef.current.setFieldValue(
      'cardHolderName',
      cardToEdit?.card_holder_name ? cardToEdit?.card_holder_name : ''
    );
  }, [cardToEdit]);

  const handleUpdateCard = async (values) => {
    try {
      setIsLoading(true);
      const { data, status } = await axiosInstance.put(
        ORG_URLS?.UPDATE_CARD + `/${cardToEdit?.id}`,
        {
          card_holder_name: values.cardHolderName,
        }
      );
      if (status === 200) {
        if (data?.status) {
          setApiMessage('success', data?.message);
          getAllCads();
          setShowAddCardModal(false);
          setSelectedCardToEdit(null);
          setIsLoading(false);
        } else {
          setApiMessage('error', data?.message);
          setIsLoading(false);
        }
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setIsLoading(false);
    }
  };

  return (
    <React.Fragment>
      {isLoading && <PreLoader />}
      <Box className="add-new-card-wrap">
        <Formik
          innerRef={formikRef}
          initialValues={{
            cardHolderName: cardToEdit?.card_holder_name
              ? cardToEdit?.card_holder_name
              : '',
            cardNumberComplete: false,
            cardNumberError: '',
            cardExpiryComplete: false,
            cardExpiryError: '',
            cardCvcComplete: false,
            cardCvcError: '',
          }}
          validationSchema={Yup.object().shape({
            cardHolderName: Yup.string().required(
              'Card holder name is required'
            ),
            ...(cardToEdit
              ? {}
              : {
                  cardNumberComplete: Yup.boolean().oneOf(
                    [true],
                    'Card number is required'
                  ),
                  cardExpiryComplete: Yup.boolean().oneOf(
                    [true],
                    'Card expiry date is required'
                  ),
                  cardCvcComplete: Yup.boolean().oneOf(
                    [true],
                    'Card CVC is required'
                  ),
                }),
          })}
          onSubmit={async (values) => {
            if (!!cardToEdit) {
              handleUpdateCard(values);
            } else {
              if (!stripe || !elements) {
                setApiMessage('error', 'Stripe has not loaded yet');
                return;
              }
              setIsLoading(true);
              try {
                const cardNumberElement =
                  elements.getElement(CardNumberElement);

                // Create a token with the card elements
                const { token, error } = await stripe.createToken(
                  cardNumberElement,
                  {
                    name: values.cardHolderName,
                  }
                );

                if (error) {
                  setApiMessage('error', error.message);
                  setIsLoading(false);
                  return;
                }
                try {
                  let payload = {
                    card: {
                      card_holder_name: values.cardHolderName,
                      card_token: token?.id,
                      card_exp:
                        token.card.exp_month +
                        '/' +
                        token.card.exp_year.toString().substr(-2),
                      fingerprint: token.card.fingerprint,
                    },
                  };

                  const { data, status } = await axiosInstance.post(
                    ORG_URLS?.CREATE_CARD,
                    payload
                  );
                  if (status === 200) {
                    if (data?.status) {
                      setApiMessage('success', data?.message);
                      getAllCads();
                      setShowAddCardModal(false);
                      setSelectedCardToEdit(null);
                    } else {
                      setApiMessage('error', data?.message);
                    }
                    setIsLoading(false);
                  }
                } catch (error) {
                  setApiMessage('error', error?.response?.data?.message);
                  setIsLoading(false);
                }
              } catch (error) {
                setApiMessage('error', error?.response?.data?.message);
                setIsLoading(false);
              }
            }
          }}
        >
          {({
            values,
            handleChange,
            errors,
            touched,
            handleSubmit,
            setFieldValue,
          }) => (
            <Form className="add-new-wrap" onSubmit={handleSubmit}>
              <Box className="add-new-card d-flex justify-space-between align-center">
                <Typography className="add-new-card-text sub-header-text">
                  {cardToEdit ? 'Edit card' : 'Add Card'}
                </Typography>
                {isClose && (
                  <CloseOutlined
                    sx={{ cursor: 'pointer' }}
                    onClick={() => {
                      setShowAddCardModal(false);
                      setSelectedCardToEdit(null);
                    }}
                  />
                )}
              </Box>

              <Box className="card-inputs">
                <Box className="card-details-row">
                  <Box className="cardholder-field-container">
                    <CustomTextField
                      fullWidth
                      required
                      name="cardHolderName"
                      label="Card Holder Name"
                      placeholder="Card Holder Name"
                      value={values?.cardHolderName}
                      onChange={handleChange}
                      error={
                        touched?.cardHolderName &&
                        Boolean(errors?.cardHolderName)
                      }
                      helperText={
                        touched?.cardHolderName && errors?.cardHolderName
                      }
                    />
                  </Box>
                  <Box className="cardnumber-field-container">
                    <CustomCardNumberElement
                      label="Card Number"
                      required
                      placeholder={
                        cardToEdit?.card_last4
                          ? `**** **** **** ${cardToEdit?.card_last4}`
                          : '1234 1234 1234 1234'
                      }
                      error={
                        !!cardToEdit === false &&
                        (Boolean(values.cardNumberError) ||
                          (touched.cardNumberComplete &&
                            !values.cardNumberComplete))
                      }
                      helperText={
                        values.cardNumberError ||
                        (touched.cardNumberComplete &&
                          errors.cardNumberComplete)
                      }
                      onChange={(event) => {
                        setFieldValue('cardNumberComplete', event.complete);
                        setFieldValue(
                          'cardNumberError',
                          event.error ? event.error.message : ''
                        );
                      }}
                      disabled={!!cardToEdit}
                    />
                  </Box>
                </Box>

                <Box className="card-details-row">
                  <Box className="card-expiry-container">
                    <CustomCardExpiryElement
                      label="Exp Date (MM/YY)"
                      required
                      placeholder={
                        cardToEdit?.card_expiry
                          ? cardToEdit?.card_expiry
                          : 'MM/YY'
                      }
                      error={
                        !!cardToEdit === false &&
                        (Boolean(values.cardExpiryError) ||
                          (touched.cardExpiryComplete &&
                            !values.cardExpiryComplete))
                      }
                      helperText={
                        values.cardExpiryError ||
                        (touched.cardExpiryComplete &&
                          errors.cardExpiryComplete)
                      }
                      onChange={(event) => {
                        setFieldValue('cardExpiryComplete', event.complete);
                        setFieldValue(
                          'cardExpiryError',
                          event.error ? event.error.message : ''
                        );
                      }}
                      disabled={!!cardToEdit}
                    />
                  </Box>

                  <Box className="card-cvc-container">
                    <CustomCardCvcElement
                      label="CVV"
                      required
                      placeholder={cardToEdit?.card_expiry ? '***' : 'CVV'}
                      error={
                        !!cardToEdit === false &&
                        (Boolean(values.cardCvcError) ||
                          (touched.cardCvcComplete && !values.cardCvcComplete))
                      }
                      helperText={
                        values.cardCvcError ||
                        (touched.cardCvcComplete && errors.cardCvcComplete)
                      }
                      onChange={(event) => {
                        setFieldValue('cardCvcComplete', event.complete);
                        setFieldValue(
                          'cardCvcError',
                          event.error ? event.error.message : ''
                        );
                      }}
                      disabled={!!cardToEdit}
                    />
                  </Box>
                </Box>
              </Box>

              <Box className="d-flex justify-end add-edit-card">
                <CustomButton
                  variant="contained"
                  title={cardToEdit ? 'Edit Card' : 'Add Card'}
                  onClick={(e) => {
                    e.preventDefault();
                    setFieldValue(
                      'cardNumberComplete',
                      values.cardNumberComplete,
                      true
                    );
                    setFieldValue(
                      'cardExpiryComplete',
                      values.cardExpiryComplete,
                      true
                    );
                    setFieldValue(
                      'cardCvcComplete',
                      values.cardCvcComplete,
                      true
                    );
                    handleSubmit();
                  }}
                />
              </Box>
            </Form>
          )}
        </Formik>
      </Box>
    </React.Fragment>
  );
};

export default function AddNewCard(props) {
  // // Define the appearance object with flat theme
  // const appearance = {
  //   theme: 'flat',
  // };

  // // Create options object with appearance
  // const elementsOptions = {
  //   appearance,
  // };

  return (
    <Elements
      stripe={stripePromise}
      // options={elementsOptions}
    >
      <CardForm {...props} />
    </Elements>
  );
}
