.general-settings {
  .logo-section {
    max-width: max-content;
    max-height: max-content;
    position: relative;
    img {
      max-height: 100px;
      object-fit: cover;
      border-radius: var(--border-radius-lg);
    }
    .cancel-icon {
      position: absolute;
      top: -11px;
      right: -11px;
      width: var(--icon-size-sm);
      height: var(--icon-size-sm);
    }
  }
  .setting-brand-logo-placeholder {
    width: 256px;
  }
  .display-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(0, 250px));
    gap: var(--spacing-lg);
  }

  .months-section {
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: var(--spacing-lg);
  }
  .month-text-field {
    max-width: 250px;
    width: 100%;
  }

  .convert-checkbox {
    color: var(--icon-color-warning) !important;
    svg {
      fill: var(--icon-color-warning);
    }
  }
  .cancel-checkbox {
    color: var(--icon-bold-red-color) !important;
    svg {
      fill: var(--icon-bold-red-color);
    }
  }
}
