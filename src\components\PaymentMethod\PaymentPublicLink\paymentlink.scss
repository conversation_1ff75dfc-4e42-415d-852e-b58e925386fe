@import '@/styles/variable.scss';
.plan-payment-wrap {
  .add-card-header {
    margin-top: var(--spacing-md);
    .public-link-sec {
      width: 100%;
      gap: 8px;
      margin-top: var(--spacing-md);
      .public-payment-input {
        width: 100%;
      }
      .generate-password-btn {
        margin-bottom: var(--spacing-sm);
        align-items: flex-end;
        svg {
          width: 24px;
          height: 24px;
        }
      }
    }
  }
}
