.statistics-tile {
  background-color: var(--color-white);
  border: var(--border-width-xs) solid #e5e7eb;
  border-radius: var(--border-radius-md);
  padding: var(--spacing-md);
  // cursor: pointer; // Keep cursor pointer as tiles are still clickable
  // Remove transition for hover effects
  // transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  @media (min-width: 640px) {
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
  }

  // Header Section
  &__header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
  }

  // Icon Container
  &__icon {
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;

    &--primary {
      color: var(--color-primary);
      background-color: var(--color-primary-opacity);
      border: var(--border-width-xs) solid rgba(19, 94, 150, 0.2);
    }

    &--success {
      color: var(--color-success);
      background-color: var(--color-success-opacity);
      border: var(--border-width-xs) solid rgba(3, 141, 42, 0.2);
    }

    &--warning {
      color: var(--color-warning);
      background-color: var(--color-warning-opacity);
      border: var(--border-width-xs) solid rgba(219, 151, 44, 0.2);
    }

    &--error {
      color: var(--color-danger);
      background-color: var(--color-danger-opacity);
      border: var(--border-width-xs) solid rgba(211, 47, 47, 0.2);
    }

    &--accent {
      color: var(--color-orange);
      background-color: rgba(253, 126, 20, 0.1);
      border: var(--border-width-xs) solid rgba(253, 126, 20, 0.2);
    }

    &--secondary {
      color: var(--text-color-slate-gray);
      background-color: var(--color-secondary);
      border: var(--border-width-xs) solid var(--color-light-grayish-blue);
    }
  }

  &__chart {
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;

    &--primary {
      color: var(--color-primary);
    }

    &--success {
      color: var(--color-success);
    }

    &--warning {
      color: var(--color-warning);
    }

    &--error {
      color: var(--color-danger);
    }

    &--accent {
      color: var(--color-orange);
    }

    &--secondary {
      color: var(--text-color-slate-gray);
    }
  }

  // Change/Trend Section
  &__change {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);

    &--up {
      color: var(--color-success);
    }

    &--down {
      color: var(--color-danger);
    }

    &--neutral {
      color: var(--text-color-slate-gray);
    }
  }

  &__change-text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
  }

  // Content Section
  &__content {
    margin-bottom: var(--spacing-sm);
  }

  &__title {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-slate-gray);
    margin-bottom: var(--spacing-xs);

    @media (min-width: 640px) {
      font-size: var(--font-size-sm);
    }
  }

  &__value-container {
    display: flex;
    align-items: baseline;
    gap: var(--spacing-sm);
  }

  &__value {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-bold);
    color: var(--text-color-primary);
    margin: 0;

    @media (min-width: 640px) {
      font-size: var(--font-size-md);
    }

    @media (min-width: 1024px) {
      font-size: var(--font-size-lg);
    }
  }

  &__updated {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    color: var(--color-success);
    font-weight: var(--font-weight-medium);
    animation: pulse 2s infinite;
  }

  // Footer Section
  &__footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &__description {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    color: var(--text-color-slate-gray);
    margin: 0;
    flex: 1;
  }

  &__sparkline-container {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__sparkline {
    opacity: 0.6;
  }
}

// Animations
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
