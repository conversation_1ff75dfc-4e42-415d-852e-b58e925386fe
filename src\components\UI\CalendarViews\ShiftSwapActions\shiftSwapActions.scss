@import '../../../../styles/variable.scss';
.swap-request-details-wrapper {
  background: rgba(var(--color-orange), 0.1);
  border-left: 3px solid var(--color-orange);
  padding: 15px;
  width: 393px;
  .swap-request-details {
    .swap-request-user-details {
      display: flex;
      justify-content: flex-start;
      gap: 15px;
      .swap-request-avatar {
        .MuiAvatar-root {
          height: var(--icon-size-lg);
          width: var(--icon-size-lg);
          font-size: var(--font-size-sm);
        }
      }
      .swap-request-badge-icon {
        background: var(--color-orange);
        fill: var(--icon-color-white);
        border-radius: 50%;
        width: var(--icon-size-xs);
        height: var(--icon-size-xs);
        padding: var(--spacing-xxs);
      }
      .swap-request-name-time {
        display: flex;
        justify-content: space-between;
        width: 100%;
        .swap-request-name {
          font-size: 14px;
          line-height: 21px;
          font-weight: 600;
          .swap-request-status {
            color: var(--text-color-slate-gray);
            font-weight: 400;
          }
        }
        .swap-request-time {
          color: var(--text-color-slate-gray);
          font-size: 12px;
          line-height: 16px;
          white-space: nowrap;
        }
      }
    }
    .swap-request-shift-details {
      margin-left: 40px;
      padding: 0px 5px;
      border-radius: 8px;
      .swap-request-shift-text {
        color: var(--color-dark-50);
        font-size: 12px;
        line-height: 16px;
        letter-spacing: 0px;
        font-weight: 400;
      }
    }
    .swap-request-to-user-details {
      align-items: center;
      margin-left: 40px;
      margin-top: 10px;
      gap: 10px;
    }
  }
}

.swap-request-button {
  margin: 15px 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;

  .swap-request-btn {
    width: 100%;
    font-weight: 400;
    padding: 5px !important;
    font-size: 12px !important;
    letter-spacing: 0px !important;
  }
  .cancel-cover-request-btn {
    background: var(--color-white);
    color: var(--text-color-danger) !important;
    // &:hover {
    //   background: var(--color-white);
    //   color: var(--text-color-danger) !important;
    //   box-shadow: none !important;
    // }
  }
  .accept-cover-request-btn {
    background: var(--color-primary);
    color: var(--text-color-white) !important;
    // &:hover {
    //   background: var(--color-primary);
    //   color: var(--text-color-white) !important;
    //   box-shadow: none !important;
    // }
  }
}

.swap-request-history {
  padding: 10px 20px;
  .swap-request-history-text {
    font-size: 14px;
    line-height: 21px;
    letter-spacing: 0px;
    font-weight: 600;
    color: var(--text-color-slate-gray);
  }
  .swap-request-history-details {
    display: flex;
    justify-content: flex-start;
    gap: 5px;
    margin-top: 10px;
    .swap-request-history-badge-icon {
      width: 20px;
      height: 20px;
      &.isRejected {
        fill: var(--icon-bold-red-color);
      }
    }
    .swap-request-history-name-time {
      display: flex;
      justify-content: space-between;
      width: 100%;
      .swap-request-history-name {
        font-size: 14px;
        line-height: 21px;
        font-weight: 500;
        .swap-request-history-status {
          color: var(--text-color-slate-gray);
          font-weight: 400;
        }
      }
      .swap-request-history-time {
        color: var(--text-color-slate-gray);
        font-size: 12px;
        line-height: 16px;
        white-space: nowrap;
      }
    }
  }
}

// Shift Drop Request Note
.shift-drop-request-note-row {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  padding: 10px 20px;
  .shift-drop-request-note-input-wrapper {
    width: 100%;
    .shift-drop-request-note-input {
      width: 100%;
      border: none;
      background: none;
      outline: none;
      padding: var(--field-padding);
      border-radius: var(--field-radius);
      font-size: var(--font-size-sm);
      line-height: var(--line-height-xs);
      font-family: var(--font-family-primary);
      color: var(--text-color-black);
      resize: none;
      &:hover {
        background: var(--color-secondary);
        color: var(--text-color-black);
      }
      &:focus {
        outline: var(--field-border);
      }
    }
  }
  .icon {
    height: var(--icon-size-sm);
    width: var(--icon-size-sm);
    color: var(--icon-color-slate-gray);
    svg {
      height: var(--icon-size-sm);
      width: var(--icon-size-sm);
    }
  }
}

// Shift Note Char Count
.shift-drop-request-note-char-count {
  .shift-drop-request-note-visibility-container {
    width: 100%;
    padding: 0px 20px;
    .char-count {
      text-align: right;
      font-size: var(--font-size-xs);
      color: var(--text-color-slate-gray);
      font-weight: var(--font-weight-regular);
    }
  }
}
