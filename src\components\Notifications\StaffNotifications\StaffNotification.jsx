'use client';
import React, { useContext, useEffect, useState } from 'react';
import { Box } from '@mui/material';
import AuthContext from '@/helper/authcontext';
import Notification from './Notification';
// import CustomButton from '@/components/UI/button';
import CampaignIcon from '@mui/icons-material/Campaign';
import DialogBox from '@/components/UI/Modalbox';
import SendNotification from './SendNotification';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import CustomOrgPagination from '@/components/UI/customPagination';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { useRouter } from 'next/navigation';
import _ from 'lodash';
import NoDataView from '../../UI/NoDataView';
import ContentLoader from '../../UI/ContentLoader';
import CustomButton from '../../UI/CustomButton';

export default function StaffNotification() {
  const { authState } = useContext(AuthContext);
  const router = useRouter();
  const [toggleModal, setToggleModal] = useState(false);

  const [loader, setLoader] = useState(true);
  const [notificationList, setNotificationList] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);

  const [isLoader, setIsLoader] = useState(false);

  const [branchList, setBranchList] = useState([]);
  const [departmentList, setDepartmentList] = useState([]);
  const [roleList, setRoleList] = useState([]);
  const [staffList, setStaffList] = useState([]);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const getStaffNotification = async (page, Rpp, isUpdated) => {
    !isUpdated && setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_STAFF_NOTIFICATON +
          `?size=${Rpp ? Rpp : rowsPerPage}&page=${page}`
      );
      if (status === 200) {
        setLoader(false);
        const notificationList = data?.notificationList;
        setCurrentPage(data?.page);
        setTotalCount(data?.count);
        setNotificationList(
          notificationList?.length > 0 ? notificationList : []
        );
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const getStaffList = async () => {
    setIsLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_USER_LIST + `?isAdmin=false`
      );

      if (status === 200) {
        setIsLoader(false);
        const alloption = [{ label: 'Select all', value: 'all' }];
        let filterUserList = data?.userList?.map((user) => ({
          label: user?.user_full_name,
          value: user?.id,
        }));
        let mergeList = _.concat(alloption, filterUserList);
        setStaffList(mergeList);
      }
    } catch (error) {
      setIsLoader(false);
      setStaffList([]);
      console.error(error);
    }
  };
  const getBranchList = async () => {
    setIsLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_BRANCH_LIST +
          `?search=${''}&page=${1}&size=${''}&branchStatus=active`
      );
      if (status === 200) {
        setIsLoader(false);
        const alloption = [{ label: 'Select all', value: 'all' }];
        let filterUserList = data?.data?.map((user) => ({
          label: user?.branch_name,
          value: user?.id,
          color: user?.branch_color,
        }));
        let mergeList = _.concat(alloption, filterUserList);
        setBranchList(mergeList);
      }
    } catch (error) {
      setIsLoader(false);
      setBranchList([]);
      console.error(error);
    }
  };
  const getDepartmentList = async () => {
    setIsLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_DEPARTMENT_LIST +
          `?search=${''}&page=${1}&size=${''}&departmentStatus=active`
      );

      if (status === 200) {
        setIsLoader(false);
        const alloption = [{ label: 'Select all', value: 'all' }];
        let filterUserList = data?.data?.map((user) => ({
          label: user?.department_name,
          value: user?.id,
        }));
        let mergeList = _.concat(alloption, filterUserList);
        setDepartmentList(mergeList);
      }
    } catch (error) {
      setIsLoader(false);
      setDepartmentList([]);
      console.error(error);
    }
  };
  const getRoleList = async () => {
    setIsLoader(true);
    try {
      const { status, data } = await axiosInstance.get(URLS?.GET_ROLE_LIST);

      if (status === 200) {
        setIsLoader(false);
        const alloption = [{ label: 'Select all', value: 'all' }];
        let filterUserList = data?.data?.map((user) => ({
          label: user?.role_name,
          value: user?.id,
        }));
        let mergeList = _.concat(alloption, filterUserList);
        setRoleList(mergeList);
      }
    } catch (error) {
      setIsLoader(false);
      setRoleList([]);
      console.error(error);
    }
  };

  useEffect(() => {
    if (
      authState?.UserPermission?.notification === 1 ||
      authState?.UserPermission?.notification === 2
    ) {
      getStaffNotification(1, '', false);
    }
  }, [authState?.UserPermission?.notification]);
  useEffect(() => {
    if (
      toggleModal &&
      (authState?.UserPermission?.notification === 1 ||
        authState?.UserPermission?.notification === 2)
    ) {
      getStaffList();
      getBranchList();
      getDepartmentList();
      getRoleList();
    }
  }, [toggleModal, authState?.UserPermission?.notification]);

  const onPageChange = (newPage) => {
    setCurrentPage(newPage);
    getStaffNotification(newPage, '', true);
  };
  const OnRowPerPage = (newPage) => {
    setRowsPerPage(newPage);
    setCurrentPage(1);
    getStaffNotification(1, newPage, '', true);
  };
  const handleSendNotification = async (payload) => {
    setIsLoader(true);
    const config = {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    };
    try {
      const { status, data } = await axiosInstance.post(
        URLS?.SEND_NOTIFICATION,
        payload,
        config
      );
      if (status === 200) {
        setIsLoader(false);
        setToggleModal(!toggleModal);
        getStaffNotification(currentPage, '', true);
        setApiMessage('success', data?.message);
      }
    } catch (error) {
      setIsLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const handleMarkAsRead = async (noti_data) => {
    if (noti_data?.notification_status !== 'read') {
      setLoader(true);
      try {
        const { status } = await axiosInstance.get(
          URLS?.MARK_AS_READ_NOTIFICATION + `/${noti_data?.id}`
        );
        if (status === 200) {
          setLoader(false);
          handleNotificationRedirect(noti_data);
          getStaffNotification(currentPage, '', true);
        }
      } catch (error) {
        setLoader(false);
        setApiMessage('error', error?.response?.data?.message);
      }
    } else {
      handleNotificationRedirect(noti_data);
    }
  };

  // NOTIFICATION REDIRECT
  const handleNotificationRedirect = (item) => {
    var notiObj = item?.redirection_type;
    var notiObjData = item?.redirection_object;
    const LEAVE = 'leave';
    const ONBOARDING = 'onboarding';
    const RESIGNATION = 'resignation';
    const MEDIA = 'media';
    const USER = 'profile';
    switch (notiObj) {
      case LEAVE:
        router.push(`/leave-remark`);
        return true;
      case RESIGNATION:
        router.push(`/resignation-remark/${notiObjData?.resignation_id}`);
        return true;
      case ONBOARDING:
        router.push(`/user/${notiObjData?.user_id}`);
        return true;
      case MEDIA:
        router.push(`/play-list/${notiObjData?.playlist_id}`);
        return true;
      case USER:
        router.push(`/user/${notiObjData?.user_id}`);
        return true;
      default:
        return true;
    }
  };

  return (
    <>
      <Box className="staff-notification-container">
        <Box className="d-flex justify-end">
          {authState?.UserPermission?.notification === 2 && (
            <CustomButton
              title="Send notification"
              startIcon={<CampaignIcon />}
              onClick={() => setToggleModal(true)}
            />
          )}
        </Box>
        {loader ? (
          <ContentLoader />
        ) : (
          <>
            {notificationList && notificationList?.length === 0 ? (
              <Box className="">
                <NoDataView
                  title="No Notification Found"
                  description="There is no Notification available at the moment."
                />
              </Box>
            ) : (
              <>
                <Notification
                  notificationList={notificationList}
                  handleMarkAsRead={handleMarkAsRead}
                />
                <CustomOrgPagination
                  currentPage={currentPage}
                  totalCount={totalCount}
                  rowsPerPage={rowsPerPage}
                  onPageChange={onPageChange}
                  OnRowPerPage={OnRowPerPage}
                />
              </>
            )}
          </>
        )}
      </Box>
      <DialogBox
        open={toggleModal}
        handleClose={() => {
          setToggleModal(!toggleModal);
        }}
        title="Send notification"
        className="dialog-box-container"
        content={
          <SendNotification
            handleSendNotification={handleSendNotification}
            isLoader={isLoader}
            staffList={staffList}
            branchList={branchList}
            departmentList={departmentList}
            roleList={roleList}
            setToggleModal={setToggleModal}
            setStaffList={setStaffList}
            setBranchList={setBranchList}
            setDepartmentList={setDepartmentList}
            setRoleList={setRoleList}
          />
        }
      />
    </>
  );
}
