import React, { useState, useEffect } from 'react';
import { Box, Typography } from '@mui/material';
import { Chart } from 'react-google-charts';

const PieCharts = ({ item, piechartsData, screenWidth }) => {
  const [options, setOptions] = useState({
    isEmpty: true,
    data: [['', 0]], // Empty data for the chart
    options: {
      title: '',
      legend: { position: 'none' }, // Hide legend
      pieHole: 0.4, // Keep the donut shape even with no data
    },
  });
  useEffect(() => {
    if (item && item?.data && item?.data?.length > 0) {
      const colors = item?.series?.[0]?.fills;
      const options = {
        title: '',
        pieHole: 0.4, // Creates a donut chart
        is3D: true, // Enables 3D effect
        pieStartAngle: 100, // Rotates the chart
        // sliceVisibilityThreshold: 0.02, // Hides slices smaller than 2%,
        legend: {
          position: 'bottom',
          alignment: 'center',
          maxLines: 6,
          textStyle: {
            color: '#233238',
            fontSize: 14,
          },
        },
        colors: colors,
      };
      const chartData = [
        ['Location', 'Income'], // Header for the chart
        ...item.data.map((citem) => [
          citem?.col1 ? citem?.col1 : citem?.branches,
          citem?.col2 ? citem?.col2 : citem?.branchValue,
        ]),
      ];
      setOptions({ ...item, options: options, data: chartData });
    }
  }, [item]);
  useEffect(() => {
    if (
      screenWidth < 768 &&
      options &&
      options?.data &&
      options?.data?.length > 0
    ) {
      const colors = item.series[0]?.fills;
      const options = {
        title: '',
        pieHole: 0.4, // Creates a donut chart
        is3D: true, // Enables 3D effect
        pieStartAngle: 100,
        // sliceVisibilityThreshold: 0.02, // Hides slices smaller than 2%
        legend: {
          position: 'bottom',
          alignment: 'center',
          maxLines: 2,
          textStyle: {
            color: '#233238',
            fontSize: 14,
          },
        },
        colors: colors,
      };
      const chartData = [
        ['Location', 'Income'], // Header for the chart
        ...item.data.map((citem) => [
          citem?.col1 ? citem?.col1 : citem?.branches,
          citem?.col2 ? citem?.col2 : citem?.branchValue,
        ]),
      ];
      setOptions({
        ...item,
        options: options,
        data: chartData,
        width: screenWidth - 60 - 50 - 20,
      });
    }
  }, [screenWidth]);
  return (
    <>
      <Box className="pie-charts">
        {options?.isEmpty ? (
          <Box className="piechart-no-data">
            <Typography className="body-text mt16 text-align title">
              {item?.title?.text}
            </Typography>
            <Box className="d-flex no-data">
              <Typography className="title-text">No data to display</Typography>
            </Box>
          </Box>
        ) : (
          <>
            <Typography className="body-text mt16 text-align title">
              {item?.title?.text}
            </Typography>
            <Chart
              chartType="PieChart"
              data={options?.data}
              options={options?.options}
              width={'100%'}
              height={'400px'}
            />
          </>
        )}

        {piechartsData && piechartsData?.yaxis_list ? (
          <Typography className="title-text text-align color-dark-blue text-capital">
            {`Total of ${piechartsData?.yaxis_list}`}
          </Typography>
        ) : options && options?.series?.[0]?.yName ? (
          <Typography className="title-text text-align color-dark-blue text-capital">
            {options?.series?.[0]?.yName}
          </Typography>
        ) : (
          <></>
        )}
      </Box>
    </>
  );
};

export default PieCharts;

// import React, { useState, useEffect } from 'react';
// import { Box, Typography } from '@mui/material';
// import { AgCharts } from 'ag-charts-react';

// const PieCharts = ({ item, piechartsData, screenWidth }) => {
//   const [options, setOptions] = useState({
//     // data: getData(),
//     title: {
//       text: 'Portfolio Composition'
//     },
//     data: [],
//     series: [],
//     // series: [
//     //   {
//     //     type: 'pie',
//     //     angleKey: 'amount',
//     //     calloutLabelKey: 'asset',
//     //     sectorLabelKey: 'amount',
//     //     fills: [
//     //       '#FF5733', // Custom color for Stocks
//     //       '#4287F5', // Custom color for Bonds
//     //       '#33FF57', // Custom color for Cash
//     //       '#FFC300', // Custom color for Real Estate
//     //       '#C70039' // Custom color for Commodities
//     //     ]
//     //     // sectorLabel: {
//     //     //   color: 'white',
//     //     //   fontWeight: 'bold',
//     //     //   formatter: ({ value }) => `$${(value / 1000).toFixed(0)}K`
//     //     // }
//     //   }
//     // ],
//     width: 600,
//     height: 400
//   });
//   useEffect(() => {
//     if (item && item?.data) {
//       setOptions(item);
//     }
//   }, [item]);
//   useEffect(() => {
//     if (
//       screenWidth < 768 &&
//       options &&
//       options?.data &&
//       options?.data?.length > 0
//     ) {
//       setOptions({ ...options, width: screenWidth - 60 - 50 - 20 });
//     }
//   }, [screenWidth]);
//   return (
//     <>
//       <Box className="pie-charts">
//         <AgCharts options={options} />
//         {piechartsData && piechartsData?.yaxis_list ? (
//           <Typography className="title-text text-align color-dark-blue text-capital">
//             {`Total of ${piechartsData?.yaxis_list}`}
//           </Typography>
//         ) : options && options?.series?.[0]?.yName ? (
//           <Typography className="title-text text-align color-dark-blue text-capital">
//             {options?.series?.[0]?.yName}
//           </Typography>
//         ) : (
//           <></>
//         )}
//       </Box>
//     </>
//   );
// };

// export default PieCharts;
