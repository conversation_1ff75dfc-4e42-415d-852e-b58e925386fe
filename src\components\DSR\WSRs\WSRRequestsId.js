'use client';

import React, { useState, useContext, useEffect } from 'react';
import { Box, Typography, CircularProgress, Tooltip } from '@mui/material';
import { DataGrid, gridClasses } from '@mui/x-data-grid';
import CustomPagination from '@/components/UI/customPagination';
import AuthContext from '@/helper/authcontext';
import { DateFormat } from '@/helper/common/commonFunctions';
import RequestIcon from '../../ActionIcons/RequestIcon';
import NoDataView from '@/components/UI/NoDataView';

export default function WSRRequestByIdPage({
  setIsRequestTable,
  dsrReqData,
  loader,
  totalCount,
  getWsrRequest,
  getWsrRequestList,
  setRowsPerPage,
  rowsPerPage,
}) {
  const { authState } = useContext(AuthContext);

  const [page, setPage] = useState(1);
  const [currency, setCurrency] = useState();

  const renderDetailStatus = (value) => {
    const statusMap = {
      deleted: 'failed',
      draft: 'draft',
      default: 'success',
    };
    const statusClass = statusMap[value] || statusMap.default;
    return (
      <Typography className={`sub-title-text ${statusClass} fw600`}>
        {value}
      </Typography>
    );
  };

  const renderRequestStatus = (value) => {
    const statusMap = {
      inactive: 'cancelled',
      rejected: 'failed',
      pending: 'draft',
      default: 'success',
    };
    const statusClass = statusMap[value] || statusMap.default;
    return (
      <Typography className={`sub-title-text ${statusClass} fw600`}>
        {value}
      </Typography>
    );
  };

  const columns = [
    {
      field: 'id',
      headerName: 'ID',
      width: 48,
      minWidth: 48,
      flex: 0,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100">
            {params?.value}
          </Box>
        );
      },
    },
    {
      field: 'wsr_start_date',
      headerName: 'Start Date',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100">
            {params?.value ? DateFormat(params?.value, 'dates') : ''}
          </Box>
        );
      },
    },
    {
      field: 'wsr_end_date',
      headerName: 'End Date',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100">
            {params?.value ? DateFormat(params?.value, 'dates') : ''}
          </Box>
        );
      },
    },
    {
      field: 'branch',
      headerName: 'Branch name',
      width: 150,
      minWidth: 150,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-start h100 gap-sm">
            <Typography className="title-text bg-branch-transparent text-ellipsis">
              <span>{params?.value}</span>
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'amount',
      headerName: `Amount ( ${currency?.symbol ? currency?.symbol : '£'} )`,
      width: 150,
      minWidth: 150,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100">
            {params?.value}
          </Box>
        );
      },
    },
    {
      field: 'submitedby',
      headerName: 'Submitted by',
      width: 200,
      minWidth: 200,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100">
            {params?.value}
          </Box>
        );
      },
    },
    {
      field: 'Updatedby',
      headerName: 'Updated by',
      width: 200,
      minWidth: 200,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100">
            {params?.row?.wsr_request_updated_by?.user_full_name}
          </Box>
        );
      },
    },
    {
      field: 'wsr_detail_status',
      headerName: 'Status',
      width: 150,
      minWidth: 150,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100 text-capital">
            {renderDetailStatus(params?.value)}
          </Box>
        );
      },
    },
    {
      field: 'wsr_request_status',
      headerName: 'Request Status',
      width: 150,
      minWidth: 150,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100 text-capital">
            {renderRequestStatus(params?.value)}
          </Box>
        );
      },
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex justify-center actions">
            <Tooltip
              title={<Typography>WSR request</Typography>}
              arrow
              classes={{
                tooltip: 'info-tooltip-container ',
              }}
            >
              <Box className="d-flex actions-dsr">
                <RequestIcon
                  // className="svg-icon"
                  onClick={() => {
                    setIsRequestTable(true);
                    getWsrRequest(params?.row?.id);
                    //   setUserdata({
                    //     id: params?.row?.id,
                    //     filterData: filterData,
                    //     searchValue: searchValue,
                    //     page: page
                    //   });
                    //   saveToStorage(identifiers?.RedirectData, {
                    //     id: params?.row?.id,
                    //     filterData: filterData,
                    //     searchValue: searchValue,
                    //     page: page
                    //   });
                    //   router.push(`/dsr/${params?.row?.id}?isRequest=true`);
                  }}
                />
                {/* {RequestIcon()} */}
              </Box>
            </Tooltip>
          </Box>
        );
      },
    },
  ];

  const onPageChange = (newPage) => {
    setPage(newPage);
    getWsrRequestList(newPage);
  };
  const OnRowPerPage = (newPage) => {
    setRowsPerPage(newPage);
    setPage(1);
    getWsrRequestList(1, newPage);
  };
  useEffect(() => {
    authState?.currency_details && setCurrency(authState?.currency_details);
  }, [authState]);
  return (
    <>
      <Box className="table-container table-layout">
        {loader ? (
          <Box className="content-loader">
            <CircularProgress className="loader" color="inherit" />
          </Box>
        ) : (
          <>
            {dsrReqData && dsrReqData?.length === 0 ? (
              <Box className="no-data d-flex align-center justify-center">
                <NoDataView
                  // image
                  title="No Weekly Status Reports Found"
                  description="There is no  weekly status reports available at the moment."
                />
              </Box>
            ) : (
              <>
                <DataGrid
                  rows={dsrReqData}
                  columns={columns}
                  pageSize={rowsPerPage}
                  checkboxSelection={false} // Disable default checkbox column
                  disableSelectionOnClick // Disable row selection on click
                  //   columnVisibilityModel={{
                  //     actions:
                  //       authState?.UserPermission?.dsr === 2 &&
                  //       authState?.web_user_active_role_id !== 7 &&
                  //       authState?.web_user_active_role_id !== 14
                  //         ? true
                  //         : false
                  //   }}
                  hideMenuIcon
                  getRowHeight={() => 'auto'}
                  sx={{
                    [`& .${gridClasses.cell}`]: {
                      py: 1,
                    },
                  }}
                />
                <CustomPagination
                  currentPage={page}
                  // totalPages={totalPages}
                  totalCount={totalCount}
                  rowsPerPage={rowsPerPage}
                  onPageChange={onPageChange}
                  OnRowPerPage={OnRowPerPage}
                />
              </>
            )}
          </>
        )}
      </Box>
    </>
  );
}
