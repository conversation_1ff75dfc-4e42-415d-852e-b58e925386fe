import React, { useState, useEffect } from 'react';
import { Box } from '@mui/material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Too<PERSON><PERSON>,
  Legend,
} from 'recharts';

const CustomLineChart = ({
  dsrData,
  GraphColors,
  setDSRLineView,
  filterFirst,
  DSRLineView,
}) => {
  const [dsrColumn, setDSRColumn] = useState();
  const [dsrDat, setDSRData] = useState([]);
  const [activeLine, setActiveLine] = useState(null);
  const CustomLegend = ({ payload }) => {
    return (
      <ul>
        {payload?.map((entry, index) => {
          const filByname =
            dsrColumn &&
            dsrColumn?.length > 0 &&
            dsrColumn?.find((f) => f?.headersKey === entry?.value);
          var SelectedBranch = DSRLineView?.find(
            (f) => f?.headersKey === entry?.value
          );
          if (filByname?.headersValue !== 'Total' || !filterFirst) {
            return (
              <li
                key={`item-${index}`}
                onClick={() => {
                  var filterData = DSRLineView?.map((f) =>
                    f?.headersKey === entry?.value
                      ? { ...f, Visible: !SelectedBranch?.Visible }
                      : f
                  );
                  setDSRLineView(filterData);
                }}
                className={
                  SelectedBranch?.Visible
                    ? 'Visible-branch cursor-pointer'
                    : 'Unvisible-branch cursor-pointer'
                }
              >
                <Box
                  className="legend-block"
                  style={{
                    background: SelectedBranch?.color
                      ? SelectedBranch?.color
                      : entry?.color,
                    width: '15px',
                    height: '15px',
                    marginRight: '5px',
                    // border:
                    //   filByname?.headersValue === 'Total'
                    //     ? `2px dashed ${SelectedBranch?.color}`
                    //     : `2px solid ${SelectedBranch?.color}`
                  }}
                ></Box>
                <span>
                  {filByname && filByname?.headersValue
                    ? filByname?.headersValue
                    : ''}
                </span>
              </li>
            );
          } else {
            return <></>;
          }
        })}
      </ul>
    );
  };

  const CustomTooltip = ({ active, payload, label }) => {
    if (active) {
      return (
        <div className="custom-tooltip-chart">
          <p
            className="chart-label"
            style={{ color: '#39596e' }}
          >{`${label}`}</p>
          {payload.map((entry, index) => {
            const filByname = dsrColumn?.find(
              (f) => f?.headersKey === entry?.name
            );
            var SelectedBranch = DSRLineView?.find(
              (f) => f?.headersKey === entry?.dataKey
            );
            if (
              SelectedBranch?.Visible &&
              (filByname?.headersValue !== 'Total' || !filterFirst)
            ) {
              return (
                <p
                  className="chart-value"
                  key={`value-${index}`}
                  style={{
                    color: '#39596e',
                    display: 'flex',
                    alignItems: 'center',
                  }}
                  // className={
                  //   SelectedBranch?.Visible
                  //     ? 'chart-value Visible-branch cursor-pointer'
                  //     : 'chart-value Unvisible-branch cursor-pointer'
                  // }
                >
                  <div
                    style={{
                      background: SelectedBranch?.color
                        ? SelectedBranch?.color
                        : entry?.color,
                      width: '10px',
                      height: '10px',
                      marginRight: '5px',
                    }}
                    // style={{
                    //   width: '15px',
                    //   marginRight: '5px',
                    //   border:
                    //     filByname?.headersValue === 'Total'
                    //       ? `1px dashed ${SelectedBranch?.color}`
                    //       : `1px solid ${SelectedBranch?.color}`
                    // }}
                  ></div>
                  {`${filByname?.headersValue}: ${entry?.value}`}
                </p>
              );
            } else {
              return <></>;
            }
          })}
        </div>
      );
    }
    return null;
  };
  useEffect(() => {
    // setDSR()
    if (dsrData) {
      var headArray = [];
      var ViewArray = [];
      var i = 0;
      Object.entries(dsrData?.column).forEach(([key, value]) => {
        // if (key !== 'id' && key !== 'name') {
        headArray.push({
          headersValue: value,
          headersKey: key,
          color: dsrData?.graphColumn[key]?.color
            ? dsrData?.graphColumn[key]?.color
            : GraphColors[i],
        });
        ViewArray.push({
          headersValue: value,
          headersKey: key,
          Visible: true,
          color: dsrData?.graphColumn[key]?.color
            ? dsrData?.graphColumn[key]?.color
            : GraphColors[i],
        });
        if (GraphColors[i + 1]) {
          i++;
        } else {
          i = 0;
        }
        setDSRColumn(headArray);
        setDSRLineView(ViewArray);
      });
      const transformedData = dsrData?.data?.map((item) => {
        const newItem = { ...item };

        Object.keys(item).forEach((col) => {
          if (typeof newItem[col] === 'object') {
            newItem[col] = newItem[col]?.total_amount;
          }
        });
        return newItem;
      });
      setDSRData(transformedData);
    }
  }, [dsrData]);

  // const divisions = 4;
  // const maxAmount = dsrData?.max_amount || 100;
  // const roundedMaxAmount = Math.ceil(maxAmount / 100000) * 100000;
  // const ticks = Array.from(
  //   { length: divisions + 1 },
  //   (_, i) => (roundedMaxAmount / divisions) * i
  // );
  // const slots =
  //   dsrData?.max_amount < 20000
  //     ? 2000
  //     : dsrData?.max_amount < 50000
  //     ? 10000
  //     : dsrData?.max_amount < 100000
  //     ? 20000
  //     : dsrData?.max_amount < 500000
  //     ? 20000
  //     : dsrData?.max_amount < 1000000
  //     ? 200000
  //     : dsrData?.max_amount < 10000000
  //     ? 500000
  //     : dsrData?.max_amount < 100000000
  //     ? 20000000
  //     : 10000000;

  const slots =
    dsrData?.max_amount < 5000
      ? 1000
      : dsrData?.max_amount < 10000
        ? 2000
        : dsrData?.max_amount < 20000
          ? 4000
          : dsrData?.max_amount < 50000
            ? 10000
            : dsrData?.max_amount < 100000
              ? 20000
              : dsrData?.max_amount < 500000
                ? 100000
                : dsrData?.max_amount < 1000000
                  ? 200000
                  : dsrData?.max_amount < 2500000
                    ? 500000
                    : dsrData?.max_amount < 5000000
                      ? 1000000
                      : dsrData?.max_amount < 10000000
                        ? 2000000
                        : dsrData?.max_amount < 50000000
                          ? 4000000
                          : dsrData?.max_amount < 100000000
                            ? 20000000
                            : 10000000;
  const generateTicks = (maxValue) => {
    const tickInterval = slots;
    const ticks = [];
    for (let i = 0; i <= maxValue + tickInterval; i += tickInterval) {
      ticks.push(i);
    }
    return ticks;
  };
  const ticks = generateTicks(dsrData?.max_amount);
  const roundedMaxAmount = Math.ceil(dsrData?.max_amount / slots) * slots;

  return (
    <>
      {dsrDat && (
        <Box className="custom-rechart-wrap">
          <LineChart width={1082} height={394} data={dsrDat}>
            <CartesianGrid vertical={false} />
            <XAxis dataKey="col1" />
            <YAxis
              domain={[0, roundedMaxAmount]}
              ticks={ticks}
              // ticks={[0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100]}
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend content={<CustomLegend />} />
            {dsrColumn &&
              dsrColumn?.length > 0 &&
              dsrColumn?.map((item) => {
                var SelectedBranch = DSRLineView?.find(
                  (f) => f?.headersKey === item?.headersKey
                );
                const isActive = activeLine === item.headersKey;
                if (item?.headersValue === 'Total' && !filterFirst) {
                  return (
                    <Line
                      type="monotone"
                      dataKey={item?.headersKey}
                      stroke={
                        SelectedBranch?.Visible ? item?.color : 'transparent'
                      }
                      strokeWidth={isActive ? 4 : 3}
                      onMouseEnter={() => setActiveLine(item.headersKey)}
                      onMouseLeave={() => setActiveLine(null)}
                      strokeDasharray="5 5"
                      style={{ visibility: SelectedBranch?.Visible ? 0 : 1 }}
                    />
                  );
                } else if (item?.headersKey !== 'col1' && filterFirst) {
                  return (
                    <Line
                      type="monotone"
                      dataKey={item?.headersKey}
                      stroke={
                        SelectedBranch?.Visible ? item?.color : 'transparent'
                      }
                      onMouseEnter={() => setActiveLine(item.headersKey)}
                      onMouseLeave={() => setActiveLine(null)}
                      strokeWidth={isActive ? 3 : 2}
                      style={{ visibility: SelectedBranch?.Visible ? 0 : 1 }}
                    />
                  );
                }
              })}

            {/* <Line
            type="monotone"
            dataKey={dsrData?.Total}
            stroke="red"
            strokeWidth={1}
            // strokeDasharray="5 5"
          /> */}
          </LineChart>
        </Box>
      )}
    </>
  );
};

export default CustomLineChart;
