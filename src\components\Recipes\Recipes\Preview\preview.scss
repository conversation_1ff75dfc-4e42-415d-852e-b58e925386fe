.recipe-preview {
  height: 100%;
  // background-color: var(--color-secondary);
  overflow: auto;

  // Header Section
  &__header {
    background-color: var(--color-white);
    border-bottom: var(--border-width-xs) solid var(--color-light-gray);
    padding: var(--spacing-xsm) var(--spacing-xl);

    @media (max-width: 768px) {
      padding: var(--spacing-md) var(--spacing-lg);
    }
  }

  &__header-content {
    display: flex;
    // flex-direction: column;
    // gap: var(--spacing-lg);

    // @media (min-width: 1024px) {
    // flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-lg);
    // }
  }

  &__header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
  }

  &__header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
  }

  &__action-btn {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
  }

  &__back-btn {
    margin-right: var(--spacing-lg);
  }

  // Container
  &__container {
    max-width: 1280px; // max-w-7xl
    margin: 0 auto;
    padding: var(--spacing-xl) var(--spacing-lg);
    position: relative;
    @media (min-width: 1024px) {
      padding: var(--spacing-xl) var(--spacing-xxl);
    }
  }

  // Header Card
  &__header-card {
    background-color: var(--color-white);
    border-radius: var(--border-radius-lg);
    border: var(--border-width-xs) solid var(--color-light-gray);
    padding: var(--spacing-xxl);
    margin-bottom: var(--spacing-xxl);
    box-shadow: var(--box-shadow-xs);
  }

  &__header-card-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);

    @media (min-width: 1024px) {
      flex-direction: row;
      align-items: flex-start;
      justify-content: space-between;
      gap: var(--spacing-none);
    }
  }

  &__header-info {
    flex: 1;
  }

  &__title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-3xl);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-sm);
  }

  &__subtitle {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-lg);
    color: var(--text-color-slate-gray);
    margin-bottom: var(--spacing-lg);
  }

  // Categories
  &__categories {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
  }

  &__category-tag {
    padding: var(--spacing-tiny) var(--spacing-md);
    background-color: var(--color-primary-opacity);
    color: var(--color-primary);
    border-radius: var(--border-radius-md);
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
  }

  // Stats Section
  &__stats {
    @media (min-width: 1024px) {
      margin-left: var(--spacing-xxl);
    }
  }

  &__stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
    text-align: center;

    @media (min-width: 1024px) {
      grid-template-columns: repeat(4, 1fr);
    }
  }

  &__stat-card {
    background-color: var(--color-off-white);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-tiny);

    &--time {
      background-color: var(--color-warning-opacity);
    }

    &--portions {
      background-color: var(--color-success-opacity);
    }

    &--cost {
      background-color: var(--color-danger-opacity);
    }

    &--size {
      background-color: var(--color-warning-opacity);
    }
  }

  &__stat-icon {
    margin-bottom: var(--spacing-tiny);
  }

  &__stat-value {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-primary);
  }

  &__stat-label {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    color: var(--text-color-slate-gray);
  }

  // Allergen Warning
  &__allergen-warning {
    background-color: var(--color-danger-background);
    border: var(--border-width-xs) solid var(--color-danger);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xxl);
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
  }

  &__allergen-icon {
    flex-shrink: 0;
    margin-top: var(--spacing-tiny);
  }

  &__allergen-title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-semibold);
    color: var(--color-danger);
    margin-bottom: var(--spacing-tiny);
  }

  &__allergen-text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--color-danger);
  }

  // Main Content Grid
  &__content-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-xxl);

    @media (min-width: 1024px) {
      grid-template-columns: 2fr 1fr;
    }
  }

  // Left Column
  &__left-column {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xxl);
  }

  // Right Column
  &__right-column {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xxl);
  }

  // Card Base Styles
  &__card {
    background-color: var(--color-white);
    border-radius: var(--border-radius-md);
    border: var(--border-width-xs) solid var(--color-light-gray);
    box-shadow: var(--box-shadow-xs);
  }

  &__card-header {
    padding: var(--spacing-lg);
    border-bottom: var(--border-width-xs) solid var(--color-light-gray);
  }

  &__card-title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-xl);
    color: var(--text-color-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }

  &__card-content {
    padding: var(--spacing-lg);
  }

  // Ingredients Section
  &__ingredients-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    list-style: none;
    padding: 0;
    margin: 0;
  }

  &__ingredient-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border: var(--border-width-xs) solid var(--color-light-gray);
    border-radius: var(--border-radius-md);
    transition: all 0.15s ease-out;

    &:hover {
      background-color: var(--color-off-white);
    }
  }

  &__ingredient-number {
    flex-shrink: 0;
    width: var(--spacing-xxl);
    height: var(--spacing-xxl);
    background-color: var(--color-primary);
    color: var(--text-color-white);
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
  }

  &__ingredient-content {
    flex: 1;
  }

  &__ingredient-text {
    font-family: var(--font-family-primary);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-tiny);
  }

  &__ingredient-notes {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--text-color-slate-gray);
  }

  // Cost Analysis Section
  &__cost-breakdown {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
  }

  &__cost-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;

    &--total {
      border-top: var(--border-width-xs) solid var(--color-light-gray);
      padding-top: var(--spacing-md);
      margin-top: var(--spacing-sm);
    }
  }

  &__cost-label {
    font-family: var(--font-family-primary);
    color: var(--text-color-slate-gray);

    &--total {
      font-weight: var(--font-weight-semibold);
      color: var(--text-color-primary);
    }
  }

  &__cost-value {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);

    &--total {
      font-weight: var(--font-weight-bold);
      font-size: var(--font-size-lg);
    }

    &--accent {
      color: var(--color-primary);
    }
  }

  // Instructions Section
  &__instructions-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xxl);
  }

  &__instruction-item {
    border-left: var(--border-width-lg) solid var(--color-primary);
    padding-left: var(--spacing-lg);
  }

  &__instruction-header {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
  }

  &__instruction-number {
    width: var(--spacing-3xl);
    height: var(--spacing-3xl);
    background-color: var(--color-primary);
    color: var(--text-color-white);
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-bold);
  }

  &__instruction-content {
    flex: 1;
  }

  &__instruction-text {
    font-family: var(--font-family-primary);
    color: var(--text-color-primary);
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
  }

  &__instruction-details {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-md);

    @media (min-width: 768px) {
      grid-template-columns: 1fr 1fr;
    }
  }

  &__instruction-detail {
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);

    &--timing {
      background-color: var(--color-warning-opacity);
    }

    &--equipment {
      background-color: var(--color-success-opacity);
    }
  }

  &__instruction-detail-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-tiny);
  }

  &__instruction-detail-label {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);

    &--timing {
      color: var(--color-warning);
    }

    &--equipment {
      color: var(--color-success);
    }
  }

  &__instruction-detail-text {
    font-family: var(--font-family-primary);
    color: var(--text-color-primary);
  }

  // Chef Notes & Quality Check
  &__instruction-note {
    margin-top: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);

    &--chef {
      background-color: var(--color-primary-opacity);
    }

    &--quality {
      background-color: var(--color-success-opacity);
    }
  }

  &__instruction-note-header {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  &__instruction-note-label {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);

    &--chef {
      color: var(--color-primary);
    }

    &--quality {
      color: var(--color-success);
    }
  }

  &__instruction-note-text {
    font-family: var(--font-family-primary);
    color: var(--text-color-primary);
    margin-top: var(--spacing-tiny);
  }

  // Chef Tips Grid
  &__chef-tips-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-xxl);

    @media (min-width: 768px) {
      grid-template-columns: 1fr 1fr;
    }
  }

  // Serving Information
  &__serving-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
  }

  &__serving-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-tiny);
  }

  &__serving-label {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-slate-gray);
  }

  &__serving-text {
    font-family: var(--font-family-primary);
    color: var(--text-color-primary);
  }
}
.view-on-print {
  display: none;
}
@media print {
  .hide-on-print {
    display: none !important;
  }
  .view-on-print {
    display: block !important;
  }
}
