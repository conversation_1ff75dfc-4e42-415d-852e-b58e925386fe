'use client';

import React, { useState, useEffect } from 'react';
import { Box, Typography } from '@mui/material';
import CustomButton from '@/components/UI/CustomButton';
import useUserByFilter from '@/hooks/useUserByFilter';
import UserListSection from './UserList';
import './assignemployee.scss';

const AssignEmployee = ({
  setClose,
  AssignEmployees,
  SelectedID,
  templateData,
  UserList,
}) => {
  const [selectedJobRole, setSelectedJobRole] = useState([]);
  const [selectedAddDuties, setSelectedAddDuties] = useState([]);
  const { getUserList } = useUserByFilter();
  const isGeneral = templateData?.emp_contract_category?.type === 'general';
  const handleItemClick = (id) => {
    const currentIndex = selectedJobRole.indexOf(id);
    const newChecked = [...selectedJobRole];
    if (currentIndex === -1) {
      newChecked.push(id);
    } else {
      newChecked.splice(currentIndex, 1);
    }
    setSelectedJobRole(newChecked);
  };
  const handleItemClickDuties = (id) => {
    const currentIndex = selectedAddDuties.indexOf(id);
    const newChecked = [...selectedAddDuties];
    if (currentIndex === -1) {
      newChecked.push(id);
    } else {
      newChecked.splice(currentIndex, 1);
    }
    setSelectedAddDuties(newChecked);
  };
  useEffect(() => {
    getUserList('', 1, {
      selectedBranches: '',
      selectedDepartment: '',
      selectedRole: '',
      status: '',
    });
  }, []);
  useEffect(() => {
    if (SelectedID) {
      const numbersArray = SelectedID?.split(',').map(Number);
      numbersArray &&
        numbersArray?.length > 0 &&
        setSelectedJobRole(numbersArray);
    }
  }, [SelectedID]);
  const allUsers = isGeneral
    ? UserList?.general_user_id.map((u) => u.id)
    : UserList?.department_user_id.map((u) => u.id);

  const toggleAll = (selected, setter) => {
    setter(selected.length === allUsers.length ? [] : allUsers);
  };
  const allUsersDuties = UserList?.additional_user_id.map((u) => u.id);
  const toggleAllDuties = (selected, setter) => {
    setter(selected.length === allUsersDuties.length ? [] : allUsersDuties);
  };
  return (
    <>
      <Box className="pb16">
        <Typography className="title-text fw500">
          Choose from existing users to assign the latest version. Updating will
          automatically regenerate their employment contract.
        </Typography>
      </Box>
      <Box className="assign-employee-section assign-emp-section">
        <Box className="assign-emp">
          <Box
            className={isGeneral ? 'Assign-contrct-grid' : 'assign-emp-grid'}
          >
            <UserListSection
              title={isGeneral ? 'General Contract' : 'Job Role'}
              userList={
                isGeneral
                  ? UserList?.general_user_id
                  : UserList?.department_user_id
              }
              selectedIds={selectedJobRole}
              onToggleAll={() => toggleAll(selectedJobRole, setSelectedJobRole)}
              onItemClick={handleItemClick}
              isGeneral={isGeneral}
            />
            {isGeneral ? (
              <></>
            ) : (
              <>
                <UserListSection
                  title="Additional Duties"
                  userList={UserList?.additional_user_id}
                  selectedIds={selectedAddDuties}
                  onToggleAll={() =>
                    toggleAllDuties(selectedAddDuties, setSelectedAddDuties)
                  }
                  onItemClick={handleItemClickDuties}
                  rightPadding
                />
              </>
            )}
          </Box>
        </Box>

        <Box className="form-actions-btn">
          <CustomButton
            variant="outlined"
            title="Cancel"
            onClick={() => setClose(false)}
          />
          <CustomButton
            title="Save"
            fullWidth={false}
            onClick={() => AssignEmployees(selectedJobRole, selectedAddDuties)}
          />
        </Box>
      </Box>
    </>
  );
};

export default AssignEmployee;
