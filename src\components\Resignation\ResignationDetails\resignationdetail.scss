.resignation-page {
  .failed-status {
    padding: var(--spacing-tiny) var(--spacing-sm);
    color: var(--text-error);
    border: 1px solid var(--border-color-red);
    border-radius: var(--border-radius-md);
  }
  .draft-status {
    padding: var(--spacing-tiny) var(--spacing-sm);
    color: var(--text-periwinkle-blue);
    border: 1px solid var(--border-color-periwinkle-blue);
    border-radius: var(--border-radius-md);
  }
  .ongoing-status {
    padding: var(--spacing-tiny) var(--spacing-sm);
    color: var(--text-periwinkle-blue);
    border: 1px solid var(--border-color-periwinkle-blue);
    border-radius: var(--border-radius-md);
  }
  .active-onboarding-status {
    padding: var(--spacing-tiny) var(--spacing-sm);
    color: var(--text-green);
    border: 1px solid var(--border-color-green);
    border-radius: var(--border-radius-md);
  }
  .success-status {
    padding: var(--spacing-tiny) var(--spacing-sm);
    color: var(--text-olive-green);
    border: 1px solid var(--border-color-olive-green);
    border-radius: var(--border-radius-md);
  }
  // .status-active-role {
  //   border: 1px solid var(--border-color-green);
  //   color: var(--color-green);
  //   padding: var(--spacing-tiny);
  //   border-radius: var(--border-radius-md);
  // }
  .action-by-date {
    border: var(--normal-sec-border);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-sm);
    width: max-content;
  }
  .res-remark-icon {
    font-size: var(--font-size-md);
    vertical-align: middle;
  }
  .resignation-reason {
    white-space: pre-line;
  }
}
