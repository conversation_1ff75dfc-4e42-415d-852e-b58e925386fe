import { useState, useCallback } from 'react';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import { setApiMessage } from '@/helper/common/commonFunctions';

const useDepartmentList = () => {
  const [departmentList, setDepartmentList] = useState([]);
  const [loading, setLoading] = useState(false);

  const fetchDepartmentList = useCallback(async () => {
    setLoading(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_DEPARTMENT_LIST +
          `?search=${''}&page=${1}&size=${''}&departmentStatus=active`
      );
      if (status === 200) {
        const formattedList = data?.data?.map((dept) => ({
          label: dept?.department_name,
          value: dept?.id,
        }));
        setDepartmentList(formattedList);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    } finally {
      setLoading(false);
    }
  }, []);

  return { departmentList, loading, fetchDepartmentList };
};

export default useDepartmentList;
