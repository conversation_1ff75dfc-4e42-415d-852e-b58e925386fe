'use client';

import React from 'react';
import { Box, Typography } from '@mui/material';
import { useRouter } from 'next/navigation';
import UserAvatar from '@/components/UI/Avatar/UserAvatar';
import VerifiedIcon from '@mui/icons-material/Verified';
import WarningIcon from '@mui/icons-material/Warning';

const UserDetails = ({ user, empcontract }) => {
  const router = useRouter();

  return (
    <Box className="assign-user-details">
      <Box className="d-flex align-center justify-start">
        <UserAvatar
          name={user?.user_full_name}
          src={user?.user_avatar_link}
          classname="list-user-icon"
        />
        <Box
          className="pl8"
          onClick={() => {
            router.push(`/user/${user?.id}`);
          }}
        >
          <Box className="d-flex align-center">
            <Typography className="body-sm cursor-pointer link-text mw100 text-ellipsis pr4 text-ellipsis-line">
              {user?.user_full_name}
            </Typography>
            {user?.user_status === 'verified' && (
              <VerifiedIcon className="list-verified-icon" />
            )}
            {empcontract && !user?.has_user_meta ? (
              <WarningIcon className="warning-icon user-warn" />
            ) : (
              <></>
            )}
          </Box>
          <Typography className="caption-text cursor-pointer text-ellipsis-line">
            {user?.user_email}
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

export default UserDetails;
