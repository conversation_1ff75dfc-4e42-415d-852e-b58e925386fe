.calendar-container {
  .fc-media-screen {
    margin-top: 10px;
    .fc-header-toolbar {
      margin-bottom: 8px;
      .fc-toolbar-chunk {
        width: 100%;
        max-width: 255px;
        &:nth-last-child(1) {
          display: flex;
          justify-content: flex-end;

          @media (max-width: 767px) {
            max-width: 100%;
          }
        }
        @media (max-width: 1199px) {
          &:nth-last-child(2) {
            display: flex;
            justify-content: center;
          }
        }
        @media (max-width: 991px) {
          &:nth-last-child(2) {
            display: flex;
            justify-content: flex-end;
          }
        }
        @media (max-width: 561px) {
          &:nth-last-child(3) {
            display: flex;
            justify-content: flex-end;
            max-width: 100%;
          }
          &:nth-last-child(2) {
            max-width: 100%;
            display: flex;
            justify-content: flex-end;
          }
        }
      }
      @media (max-width: 767px) {
        flex-wrap: wrap;
        row-gap: 15px;
      }
    }

    .fc-button-group {
      .fc-resourceTimelineWeek-button,
      .fc-dayGridMonth-button,
      .fc-timeGridDay-button {
        padding: 4px 8px !important;
        background-color: var(--color-white) !important;
        color: var(--text-color-primary) !important;
      }
      .fc-button-active {
        background-color: var(--color-primary) !important;
        color: var(--text-color-white) !important;
      }
      .fc-prev-button,
      .fc-next-button {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 30px;
        width: 30px;
        padding: 4px 5px !important;
        background-color: var(--color-white) !important;
        border-radius: 10px !important;
        margin-right: 10px !important;
        .fc-icon {
          font-size: 21px;
          color: var(--text-color-primary) !important;
        }
      }

      .fc-button {
        background-color: var(--color-white) !important;
        color: var(--text-color-primary) !important;
        border-color: var(--color-primary);
        line-height: 12px;
        font-size: 14px;
        text-transform: capitalize;
        &:focus {
          box-shadow: none !important;
        }
      }
      .fc-button-active {
        background-color: var(--color-primary) !important;
        color: var(--text-color-white) !important;
      }
    }
    .fc-today-button {
      line-height: 16px;
      font-size: 14px;
      text-transform: capitalize !important;
      background-color: var(--color-primary);
      color: var(--text-color-white);
      border-color: var(--color-primary);
      @media (max-width: 767px) {
        margin-left: 0px;
      }
    }
    .fc-toolbar-title {
      font-size: var(--font-size-base);
      font-family: var(--font-family-primary);
      font-weight: var(--font-weight-medium);
      line-height: var(--line-height-base);
      color: var(--text-color-black);
    }
    .fc-license-message {
      display: none !important;
    }
    .fc-daygrid-day-bottom {
      color: var(--text-color-primary);
    }
    .fc-daygrid-body {
      .fc-event {
        background: none;
        background-color: initial !important;
        border: none !important;
      }
      .events-wrap {
        width: 100%;
        background-color: transparent !important;
        .event-container-wrap {
          padding-bottom: 4px;
          cursor: pointer;
          .event-title {
            padding: 2px 2px 2px 10px;
            border-radius: 3px;
            display: block;
            width: 100%;
          }
        }
      }

      .more-btn-wrap {
        color: var(--text-color-primary);
        text-decoration: underline;
        cursor: pointer;
      }
      .holiday-event-wrap {
        background-color: var(--color-bg-holiday);
        border: 1px solid var(--color-holiday);
        color: var(--color-holiday);
        border-radius: 4px;
        padding: 2px 5px;
        margin-bottom: 5px;
      }
      .fc-daygrid-more-link {
        display: block;
        border: 0 !important;
        text-overflow: ellipsis;
        overflow: hidden;
        word-break: break-all;
        white-space: nowrap;
        width: 100%;
      }
    }
    .fc-dayGridMonth-view {
      .fc-daygrid-day-number {
        padding-top: 0;
        padding-bottom: 0;
      }
      .fc-daygrid-day-frame {
        min-height: 100px !important;
        max-height: 100px !important;
      }
    }
    .fc-day-today {
      background-color: var(--color-chip);
      .fc-daygrid-day-number {
        color: var(--text-color-white);
        background-color: var(--color-primary);
        border-radius: 50%;
        padding: 0px 7px;
        margin-top: 1px;
        margin-right: 2px;
      }
    }

    .fc-multiMonthYear-view {
      .fc-day-today {
        .fc-daygrid-day-number {
          padding: 2px 6px !important;
        }
      }
      .event-container-wrap {
        // display: none;
        .year-title {
          // max-height: 0 !important;
          // padding: 0 !important;
          // border: 0 !important;
          // display: none !important;
        }
      }
      // .fc-daygrid-day-events {
      //   .fc-daygrid-event-harness {
      //     display: none;
      //   }
      //   .fc-daygrid-event-harness:first-child {
      //     display: block;
      //   }
      // }
      .fc-daygrid-day {
        .fc-daygrid-day-frame {
          .fc-daygrid-day-events {
            .year-title {
              visibility: visible !important;
              min-height: auto !important;
              max-height: none !important;
              font-size: 10px;
            }
            .event-title {
              visibility: hidden !important;
              min-height: auto !important;
              max-height: none !important;
            }
            .fc-daygrid-more-link {
              z-index: 1000 !important;
            }
            .fc-daygrid-event-harness {
              // display: none;
              position: absolute;
              top: -5px !important;
              left: 2px !important;
              margin-top: 0 !important;
              right: auto !important;
              max-width: 64px;
              visibility: visible !important;
              width: 64px;
              .fc-event-start {
                margin-left: 0 !important;
              }
            }
            // .fc-daygrid-event-harness:first-child {
            //   display: block;
            //   max-width: 64px;
            //   margin-top: 0 !important;
            // }
          }
          .fc-daygrid-day-bottom {
            margin-top: 8px !important;
          }
        }
      }
      .fc-event-main {
        max-width: 64px;
        .year-holiday {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          max-width: 9px;
          //   flex-direction: column;
          .holiday-dots {
            width: 9px;
            height: 9px;
            min-width: 9px;
            background-color: var(--color-holiday);
            border-radius: 50%;
            // margin-right: 5px;
          }
        }
      }
    }
    // .fc-year-view {
    //   // .fc-daygrid-day-events {
    //   //   bottom: 10px;
    //   // }
    //   .fc-daygrid-day-bottom {
    //     margin-top: 0px !important;
    //   }
    //   .event-title {
    //     display: none !important;
    //   }
    //   .fc-daygrid-more-link {
    //     display: none !important;
    //   }
    //   // .fc-daygrid-event-harness {
    //   //   display: none !important;
    //   // }
    //   // .fc-daygrid-event-harness:first-child {
    //   //   display: block !important;
    //   // }
    //   .year-holiday {
    //     display: flex;
    //     align-items: center;
    //     gap: 2px;
    //     //   flex-direction: column;
    //   }
    //   .holiday-dots {
    //     width: 9px;
    //     height: 9px;
    //     background-color: #029ac4;
    //     border-radius: 50%;
    //   }
    // }
  }
  .leave-type-search-wrap {
    .leave-type {
      gap: 10px;

      .dot-wrap {
        height: 12px;
        width: 12px;
        border-radius: 50%;
      }
      .pending-leave {
        color: var(--color-warning);
      }
      .approved-leave {
        color: var(--color-green);
      }
      .rejected-leave {
        color: var(--color-danger);
      }
      .cancelled-leave {
        color: var(--color-black);
      }

      .calendar-holiday {
        color: var(--color-light-holiday);
      }

      .yellow-dot-wrap {
        background-color: var(--color-warning);
        display: inline-block;
      }
      .green-dot-wrap {
        background-color: var(--color-green);
        display: inline-block;
      }
      .red-dot-wrap {
        background-color: var(--color-danger);
        display: inline-block;
      }
      .black-dot-wrap {
        background-color: var(--color-black);
        display: inline-block;
      }

      .blue-dot-wrap {
        background-color: var(--color-light-holiday);
        display: inline-block;
      }

      @media (max-width: 575px) {
        flex-wrap: wrap;
        justify-content: flex-end !important;
      }
    }

    @media (max-width: 991px) {
      flex-wrap: wrap;
      row-gap: 15px;
      justify-content: space-between !important;
    }
    @media (max-width: 684px) {
      justify-content: flex-end !important;
    }
  }
}

.leave-drawer {
  .MuiPaper-root {
    width: 100% !important;
    min-width: 410px !important;
    max-width: 30%;
    padding: 5px 14px;
    .drawer-header {
      margin-bottom: 0px;
      align-items: center;
    }
  }
}
