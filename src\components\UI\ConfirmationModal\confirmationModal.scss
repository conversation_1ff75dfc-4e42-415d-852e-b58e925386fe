.confirmation-modal {
  .MuiPaper-root {
    padding: var(--spacing-xl) !important;
    .dialog-title-wrap {
      justify-content: center;
      .MuiSvgIcon-root {
        position: absolute;
        right: 15px;
        top: 15px;
        width: var(--icon-size-md);
        height: var(--icon-size-md);
      }
    }
    .confirmation-modal-divider {
      display: none;
    }
    .confirmation-modal-wrap {
      .confirmation-text {
        font-family: var(--font-family-primary);
        font-size: var(--font-size-base);
        line-height: var(--line-height-base);
        margin-bottom: var(--spacing-xl);
        margin-top: var(--spacing-xl);
        color: var(--text-color-slate-gray);
        text-align: center;
      }
      .confirmation-action {
        display: flex;
        justify-content: center;
        gap: var(--spacing-md);
        // border-top: var(--field-border);
      }
    }
  }
}
