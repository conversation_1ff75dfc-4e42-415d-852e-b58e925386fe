'use client';

import React, { useEffect, useState } from 'react';
import { Box, Typography, Tooltip, CircularProgress } from '@mui/material';
import BudgetTable from './BudgetTable';
import InfiniteScroll from 'react-infinite-scroll-component';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import CloseIcon from '@mui/icons-material/Close';
import HistoryIcon from '@mui/icons-material/History';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import ViewIcon from '@/components/ActionIcons/ViewIcon';
import InfoIcon from '@mui/icons-material/Info';
import { useRouter, useSearchParams } from 'next/navigation';
import NoDataView from '@/components/UI/NoDataView';
import moment from 'moment';
import '../budget.scss';
import '../BudgetDetails/budgetdetails.scss';
import './budgetdetails.scss';

export default function BudgetHistory({ BudgetId }) {
  const router = useRouter();

  const [templateData, setTemplateData] = useState('');
  const [budgetData, setBudgetData] = useState([]);
  const [isViewHistory, setIsViewHistory] = useState(true);
  const [loader, setLoader] = useState(false);

  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [latestVersion, setlatestVersion] = useState();
  const searchParams = useSearchParams();
  const isFromDelete = searchParams.get('isFromDelete');
  // GET TEMPLATE DETAILS
  const getTemplateDetails = async (pageNumber = 1) => {
    templateData?.length === 0 && setLoader(true);
    const apiurl = isFromDelete
      ? URLS?.GET_BUDGET_HISTORY +
        `?forecast_id=${BudgetId}&page=${pageNumber}&size=${10}`
      : URLS?.GET_BUDGET_HISTORY +
        `?forecast_id=${BudgetId}&page=${pageNumber}&size=${10}`;
    try {
      const { status, data } = await axiosInstance.get(apiurl);

      if (status === 200) {
        const tempData = data?.data;
        setTemplateData((prevData) => [...prevData, ...tempData]);
        setHasMore(tempData?.length > 0);
        setLoader(false);
        const dataOfTable = data?.data?.find(
          (f) =>
            f?.forecast_history_status !== 'deleted' &&
            f?.forecast_history_status !== 'assigned' &&
            f?.forecast_history_status !== 'revoked'
        );
        dataOfTable?.id &&
          pageNumber == 1 &&
          handleViewVersion(dataOfTable?.id);
        dataOfTable?.id && pageNumber == 1 && setlatestVersion(dataOfTable?.id);
        // setTemplateData(tempData);
      }
    } catch (error) {
      setLoader(false);
      setTemplateData('');
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const fetchMoreData = () => {
    setPage((prevPage) => prevPage + 1);
  };

  // Fetches the budget list by ID
  const handleViewVersion = async (id) => {
    setLoader(true);
    const apiurl = isFromDelete
      ? URLS.GET_BUDGET_HISTORY_BY_ID + `${id}?forecast_budget_status=deleted`
      : URLS.GET_BUDGET_HISTORY_BY_ID + `${id}`;
    try {
      const { status, data } = await axiosInstance.get(apiurl);
      if (status === 200) {
        setLoader(false);
        let budgetList = [];
        budgetList.push(data?.data);
        let budgetdatas = budgetList?.map((budget) => {
          if (budget?.forecast_bugdet_data) {
            const coulumnData = [
              ...(budget?.forecast_bugdet_data?.columns_group ?? []).map(
                (row) => row //calculatePastOrFuture(row, budget?.forecast_year, row) //budget?.forecast_year
              ),
            ];
            return {
              ...budget,
              toggleout: 'prior',
              forecast_bugdet_data: {
                ...budget?.forecast_bugdet_data,
                columns_group: coulumnData,
              },
            };
          }
          return budget;
        });
        setBudgetData(budgetdatas);
      }
    } catch (error) {
      setLoader(false);
      setBudgetData([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  useEffect(() => {
    getTemplateDetails(page);
  }, [page]);
  return (
    <Box className={`${isViewHistory && 'budget-history-container'} `}>
      <Box className={isViewHistory && 'd-flex'}>
        {' '}
        <Box className="budget-section budget-version-section">
          <Box className={`Budget-details-section ${loader ? 'h100' : ''}`}>
            {loader ? (
              <Box className="content-loader">
                <CircularProgress className="loader" color="inherit" />
              </Box>
            ) : budgetData && budgetData?.length === 0 ? (
              <>
                <Box className="d-flex justify-space-between cursor-pointer pb16">
                  <Box className="d-flex align-center">
                    <ArrowBackIosIcon
                      className="cursor-pointer back-icon"
                      onClick={() => {
                        router.push(`/budget-forecast`);
                      }}
                    />
                  </Box>

                  <Tooltip
                    title={<Typography>Version History</Typography>}
                    classes={{
                      tooltip: 'info-tooltip-container ',
                    }}
                  >
                    <HistoryIcon onClick={() => setIsViewHistory(true)} />
                  </Tooltip>
                </Box>{' '}
                <Box className="no-data d-flex align-center justify-center">
                  <NoDataView
                    title="No Budget Data Found"
                    description="There is no budget data available at the moment."
                  />
                </Box>
              </>
            ) : (
              budgetData &&
              budgetData?.length > 0 &&
              budgetData?.map((item, index) => {
                return (
                  <Box className="">
                    {index === 0 && (
                      <>
                        <Box className="d-flex justify-space-between cursor-pointer pb16">
                          <Box className="d-flex align-center">
                            <ArrowBackIosIcon
                              className="cursor-pointer back-icon"
                              onClick={() => {
                                router.push(`/budget-forecast`);
                              }}
                            />
                            <Box className="Budget-details-action ">
                              {item?.forecast_data?.forecast_branch
                                ?.branch_name && (
                                <Typography className="title-text fw600">
                                  Branch :{' '}
                                  <span className="fw400">
                                    {' '}
                                    {
                                      item?.forecast_data?.forecast_branch
                                        ?.branch_name
                                    }
                                  </span>
                                </Typography>
                              )}
                              {item?.forecast_data?.forecast_year && (
                                <Typography className="title-text fw600">
                                  Year :{' '}
                                  <span className="fw400">
                                    {item?.forecast_data?.forecast_year}
                                  </span>
                                </Typography>
                              )}
                            </Box>
                          </Box>

                          <Tooltip
                            title={<Typography>Version History</Typography>}
                            classes={{
                              tooltip: 'info-tooltip-container ',
                            }}
                          >
                            <HistoryIcon
                              onClick={() => setIsViewHistory(true)}
                            />
                          </Tooltip>
                        </Box>{' '}
                      </>
                    )}
                    {item?.forecast_bugdet_data &&
                    item?.forecast_bugdet_data?.columns_group?.length > 1 ? (
                      <BudgetTable
                        BudgetData={item?.forecast_bugdet_data}
                        setBudgetData={setBudgetData}
                        BudgetDataReal={budgetData}
                        parentIndex={index}
                        Targetdata={item?.forecast_bugdet_data?.targetarray}
                        budgetDetails={item}
                        isFromHistory={true}
                      />
                    ) : (
                      <>
                        <Box className="no-data d-flex align-center justify-center">
                          <NoDataView
                            title="No Budget Data Found"
                            description="There is no budget data available at the moment."
                          />
                        </Box>
                      </>
                    )}
                  </Box>
                );
              })
            )}
          </Box>
        </Box>
        {isViewHistory && (
          <Box className="right-sidebar-container">
            <Box className="d-flex justify-space-between align-center">
              <Typography className="title-sm fw600 contract-sidebar-header d-flex align-center">
                <span className="title-sm fw600">Version History</span>
                <Tooltip
                  arrow
                  title={
                    <Typography>
                      The history will only display the values that were changed
                      when the user applies a category filter, modifies the
                      data, and saves it. This means only the selected and
                      updated categories will appear in the history, while
                      unchanged categories will not be shown.
                    </Typography>
                  }
                  classes={{
                    tooltip: 'info-tooltip-container ',
                  }}
                >
                  <InfoIcon className="ml4 info-icon cursor-pointer" />
                </Tooltip>
              </Typography>
              <Box
                className="pr16 cursor-pointer"
                onClick={() => setIsViewHistory(!isViewHistory)}
              >
                <CloseIcon />
              </Box>
            </Box>
            <Box
              id="scrollableSidebar"
              style={{
                height: 'calc(100vh - 150px - var(--banner-height))',
                overflow: 'auto',
              }}
            >
              <InfiniteScroll
                dataLength={templateData?.length}
                next={fetchMoreData}
                hasMore={hasMore}
                loader={<Typography>Loading more versions...</Typography>}
                scrollableTarget="scrollableSidebar"
              >
                {templateData &&
                  templateData?.map((item, index) => {
                    return (
                      <Box
                        key={index}
                        className={`d-flex justify-space-between align-center gap-sm pb16 cursor-pointer history-log ${
                          item?.id === latestVersion ? 'active' : ''
                        }`}
                        // onClick={() => handleViewVersion(item)}
                        onClick={() => {
                          if (
                            item?.forecast_history_status !== 'deleted' &&
                            item?.forecast_history_status !== 'assigned' &&
                            item?.forecast_history_status !== 'revoked'
                          ) {
                            handleViewVersion(item?.id);
                            setlatestVersion(item?.id);
                          }
                        }}
                      >
                        {' '}
                        {item?.forecast_history_status === 'revoked' ? (
                          <>
                            <Box>
                              <Typography className="title-text fw600">
                                {item?.updatedAt
                                  ? moment(item?.updatedAt)?.format(
                                      'YYYY-MM-DD hh:mm'
                                    )
                                  : null}
                              </Typography>

                              <Tooltip
                                title={
                                  <Typography>
                                    {
                                      item?.forecast_history_created_by
                                        ?.user_full_name
                                    }
                                  </Typography>
                                }
                                classes={{
                                  tooltip: 'info-tooltip-container ',
                                }}
                              >
                                <Typography className="sub-title-text history-log-remark">
                                  {`${
                                    item?.forecast_history_created_by
                                      ?.user_full_name
                                  } has revoked the Budget of year ${
                                    item?.forecast_year
                                  }`}
                                </Typography>
                              </Tooltip>
                            </Box>
                          </>
                        ) : item?.forecast_history_status === 'assigned' ? (
                          <>
                            <Box>
                              <Typography className="title-text fw600">
                                {item?.updatedAt
                                  ? moment(item?.updatedAt)?.format(
                                      'YYYY-MM-DD hh:mm'
                                    )
                                  : null}
                              </Typography>

                              <Tooltip
                                title={
                                  <Typography>
                                    {
                                      item?.forecast_history_created_by
                                        ?.user_full_name
                                    }
                                  </Typography>
                                }
                                classes={{
                                  tooltip: 'info-tooltip-container ',
                                }}
                              >
                                <Typography className="sub-title-text history-log-remark">
                                  {`${
                                    item?.forecast_history_created_by
                                      ?.user_full_name
                                  } has assigned the Budget of year ${
                                    item?.forecast_year
                                  }`}
                                </Typography>
                              </Tooltip>
                            </Box>
                          </>
                        ) : item?.forecast_history_status === 'deleted' ? (
                          <>
                            <Box>
                              <Typography className="title-text fw600">
                                {item?.updatedAt
                                  ? moment(item?.updatedAt)?.format(
                                      'YYYY-MM-DD hh:mm'
                                    )
                                  : null}
                              </Typography>

                              <Tooltip
                                title={
                                  <Typography>
                                    {
                                      item?.forecast_history_created_by
                                        ?.user_full_name
                                    }
                                  </Typography>
                                }
                                classes={{
                                  tooltip: 'info-tooltip-container ',
                                }}
                              >
                                <Typography className="sub-title-text history-log-remark">
                                  {`${
                                    item?.forecast_history_created_by
                                      ?.user_full_name
                                  } has deleted the Budget of year ${
                                    item?.forecast_year
                                  }`}
                                </Typography>
                              </Tooltip>
                            </Box>
                          </>
                        ) : (
                          <>
                            <Box>
                              <Typography className="title-text fw600">
                                {item?.updatedAt
                                  ? moment(item?.updatedAt)?.format(
                                      'YYYY-MM-DD hh:mm'
                                    )
                                  : null}
                              </Typography>

                              <Tooltip
                                title={
                                  <Typography>
                                    {
                                      item?.forecast_history_created_by
                                        ?.user_full_name
                                    }
                                  </Typography>
                                }
                                classes={{
                                  tooltip: 'info-tooltip-container ',
                                }}
                              >
                                <Typography className="sub-title-text history-log-remark">
                                  <span className="sub-title-text">
                                    {item?.forecast_history_status ===
                                    'approved'
                                      ? 'Approved by'
                                      : 'Update by'}
                                  </span>{' '}
                                  {
                                    item?.forecast_history_created_by
                                      ?.user_full_name
                                  }
                                </Typography>
                              </Tooltip>
                            </Box>
                            <Box
                              className="cursor-pointer"
                              onClick={() => {
                                handleViewVersion(item?.id);
                                setlatestVersion(item?.id);
                              }}
                            >
                              <ViewIcon />
                            </Box>{' '}
                          </>
                        )}
                      </Box>
                    );
                  })}
              </InfiniteScroll>
            </Box>
          </Box>
        )}
      </Box>
    </Box>
  );
}
