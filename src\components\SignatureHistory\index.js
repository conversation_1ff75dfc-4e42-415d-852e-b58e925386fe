'use client';
import React from 'react';
import { <PERSON>, Typography, Avatar } from '@mui/material';
import { useRouter } from 'next/navigation';
import moment from 'moment';
import { stringAvatar } from '@/helper/common/commonFunctions';
import './signaturehistory.scss';

const SignatureHistory = ({
  signatureHistory,
  logbookKey,
  logBookValues,
  LogBook,
  title,
  avatarClassName,
  listClassName,
  activityClassName,
  showDivider = true,
  dateFormat = 'DD/MM/YYYY hh:mm A',
  onAvatarClick,
}) => {
  const router = useRouter();

  if (!signatureHistory || signatureHistory?.length === 0) {
    return null;
  }

  const handleAvatarClick = (user) => {
    if (onAvatarClick) {
      onAvatarClick(user);
    } else {
      router.push(`/user/${user?.user_invite?.id}?IsInvite=true`);
    }
  };

  const formatDate = (date) => {
    return moment(date).format(dateFormat);
  };

  const renderActivityContent = (list) => {
    if (list?.activity_action === 'created') {
      return (
        <Box className={`sign-activity ${activityClassName || ''}`}>
          {Object.entries(list?.new_data)
            ?.filter(([key]) => logbookKey?.includes(key))
            ?.map(([key, value], index) => {
              const isLast =
                Object.entries(list?.new_data).filter(([key]) =>
                  logbookKey?.includes(key)
                ).length -
                  1 >
                index;
              if (key && logBookValues[key] && value !== null) {
                return (
                  <React.Fragment key={key}>
                    {index === 0 && <span className="fw600">{'Created '}</span>}
                    <span className="fw600">{logBookValues[key]}</span>
                    <span className="fw600">{' : '}</span>
                    <span className="fw400">{value}</span>
                    {isLast && <span className="fw400">{', '}</span>}
                  </React.Fragment>
                );
              }
              return null;
            })}
        </Box>
      );
    }

    if (list?.differenceData) {
      return (
        <Box className={`sign-activity ${activityClassName || ''}`}>
          {Object.entries(list?.differenceData)
            .filter(([key]) => logbookKey?.includes(key))
            ?.map(([key, value], index) => {
              const isLast =
                Object.entries(list?.differenceData).filter(([key]) =>
                  logbookKey?.includes(key)
                ).length -
                  1 >
                index;

              return (
                <React.Fragment key={key}>
                  {index === 0 && <span className="fw600">{'Updated '}</span>}
                  {LogBook(key, value, list)}
                  {isLast && <span className="fw400">{', '}</span>}
                </React.Fragment>
              );
            })}
        </Box>
      );
    }

    return null;
  };

  return (
    <Box className="Signature-history">
      {showDivider && <Box className="folder-desc-divider" />}
      <Typography className="p20 fw600">{title}</Typography>
      <ul className="tree">
        <ul className="event-type-tree">
          {signatureHistory?.map((list, i) => (
            <li key={i} className="list-view">
              <div className={`signature-list ${listClassName || ''}`}>
                <Box className="Image-profile">
                  <Avatar
                    className={avatarClassName}
                    {...stringAvatar(list?.user_full_name)}
                    onClick={() => handleAvatarClick(list)}
                  />
                </Box>
                <Typography className="p14 text-capitalize fw600">
                  {list?.user_full_name}
                  {list?.updatedAt || list?.createdAt ? ', ' : ''}
                  {list?.updatedAt ? (
                    <span className="fw400 p12">
                      {formatDate(list?.updatedAt)}
                    </span>
                  ) : list?.createdAt ? (
                    <span className="fw400 p12">
                      {formatDate(list?.createdAt)}
                    </span>
                  ) : (
                    <></>
                  )}
                </Typography>
                {renderActivityContent(list)}
              </div>
            </li>
          ))}
        </ul>
      </ul>
    </Box>
  );
};

export default SignatureHistory;
