'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Box, Typography, Tooltip, CircularProgress } from '@mui/material';
import CustomSearch from '@/components/UI/CustomSearch';
import CustomButton from '@/components/UI/CustomButton';
import AddIcon from '@mui/icons-material/Add';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import ClearOutlinedIcon from '@mui/icons-material/ClearOutlined';
import CheckIcon from '@mui/icons-material/Check';
import { useBranches } from '@/hooks/useBranches';
import NoDataView from '@/components/UI/NoDataView';
import { branchService } from '@/services/branchService';
import { setApiMessage } from '@/helper/common/commonFunctions';
import './branchassign.scss';

// Move renderBranchItem outside the component
const renderBranchItem = (branch, isSelected, onToggle) => (
  <Box
    key={branch.id}
    className={`d-flex justify-space-between align-center ${
      isSelected ? 'pl4' : ''
    } branch-name-sec`}
  >
    <Box className="d-flex align-center">
      <Box
        className="branch-name-color"
        style={{
          backgroundColor: branch?.branch_color || branch?.color,
          marginRight: '8px',
        }}
      />
      <Typography className="title-text">
        {branch?.branch_name || branch?.name}
      </Typography>
    </Box>
    {isSelected ? (
      <Box className="secondary-small-icon d-flex align-center justify-center secondary-assign-emp-icon">
        <Tooltip arrow title="Remove Branch">
          <DeleteOutlineIcon
            className="svg-icon cursor-pointer"
            onClick={() => onToggle(branch?.id)}
          />
        </Tooltip>
      </Box>
    ) : (
      <Tooltip arrow title="Add Branch">
        <Box className="primary-small-icon d-flex align-center justify-center primary-assign-emp-icon mr20">
          <AddIcon
            className="download-icon cursor-pointer"
            onClick={() => onToggle(branch?.id)}
          />
        </Box>
      </Tooltip>
    )}
  </Box>
);

// Move BranchListPanel outside the component
const BranchListPanel = ({
  title,
  branches,
  isSelected,
  actionLabel,
  onAction,
  onItemToggle,
}) => (
  <Box className={isSelected ? 'right-grid' : 'left-grid'}>
    <Box
      className={`assign-role-title ${isSelected ? 'pl4' : 'pr4'} d-flex justify-space-between align-center`}
    >
      <Typography className="title-text fw600">{`${title} (${branches.length})`}</Typography>
      <Typography
        className={`sub-title-text fw600 ${isSelected ? 'color-red' : 'color-green'} ${branches.length ? 'cursor-pointer' : 'cursor-default disabled-field'}`}
        onClick={branches.length ? onAction : undefined}
      >
        {actionLabel}
      </Typography>
    </Box>
    {branches.length ? (
      branches.map((b) => renderBranchItem(b, isSelected, onItemToggle))
    ) : (
      <NoDataView
        title="No Branches Found"
        description="There is no Branches available at the moment."
      />
    )}
  </Box>
);

const BranchAssign = ({ setClose, selectedCategory, onAssignmentsUpdate }) => {
  // State management
  const [selectedMenu, setSelectedMenu] = useState([]);
  const [removedBranches, setRemovedBranches] = useState([]);
  const [assignFilter, setAssignFilter] = useState({
    searchValue: '',
    status: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [oldSelectedMenu, setOldSelectedMenu] = useState([]);

  const { branchCatList, getCategoryBranchList } = useBranches();

  // Memoized values for branch management
  const { assignedBranches, unassignedBranches, initialAssignedIds } =
    useMemo(() => {
      if (!Array.isArray(branchCatList)) {
        return {
          assignedBranches: [],
          unassignedBranches: [],
          initialAssignedIds: [],
        };
      }

      const initialIds = branchCatList
        .filter((branch) => branch.is_health_and_safety === 1)
        .map((branch) => branch.id);

      const assigned = branchCatList.filter((branch) =>
        selectedMenu.includes(branch.id)
      );
      const unassigned = branchCatList.filter(
        (branch) => !selectedMenu.includes(branch.id)
      );

      return {
        assignedBranches: assigned,
        unassignedBranches: unassigned,
        initialAssignedIds: initialIds,
      };
    }, [branchCatList, selectedMenu]);

  // Check if selection has changed
  const isSelectionChanged = useMemo(() => {
    const current = [...selectedMenu].sort();
    const initial = [...initialAssignedIds].sort();
    return JSON.stringify(current) !== JSON.stringify(initial);
  }, [selectedMenu, initialAssignedIds]);

  // API calls
  const updateCategoryBranchAssignments = useCallback(
    async (categoryId, branchIds) => {
      try {
        if (!isSelectionChanged) {
          setApiMessage('info', 'No changes detected');
          return;
        }

        setIsLoading(true);
        const sendData = {
          category_id: categoryId,
          branch_id: branchIds,
          remove_branch_id: removedBranches,
        };

        const data = await branchService.updateCategoryBranchAssignments(
          categoryId,
          sendData
        );

        if (data?.status) {
          setApiMessage('success', data?.message);
          setClose(false);
          onAssignmentsUpdate?.(branchIds.length);
        } else {
          setApiMessage(
            'error',
            data?.message || 'Failed to update branch assignments'
          );
          setClose(false);
        }
      } catch (error) {
        setApiMessage(
          'error',
          error?.response?.data?.message ||
            'Failed to update branch assignments'
        );
        setClose(false);
      } finally {
        setIsLoading(false);
      }
    },
    [removedBranches, isSelectionChanged, onAssignmentsUpdate, setClose]
  );

  // Event handlers
  const handleSearch = useCallback(() => {
    // Store current selections before search
    const currentSelections = [...selectedMenu];
    getCategoryBranchList(selectedCategory, assignFilter?.searchValue, '').then(
      () => {
        // Restore selections after search
        setSelectedMenu(currentSelections);
      }
    );
  }, [selectedCategory, assignFilter?.searchValue, selectedMenu]);

  const handleClearSearch = () => {
    getCategoryBranchList(selectedCategory, '', '');
  };

  const handleClearFilter = useCallback(() => {
    // Store current selections before clearing
    const currentSelections = [...selectedMenu];
    setAssignFilter({ searchValue: '', status: '' });
    getCategoryBranchList(selectedCategory, '', '').then(() => {
      // Restore selections after clearing
      setSelectedMenu(currentSelections);
    });
  }, [selectedCategory, selectedMenu]);

  const handleItemClick = useCallback(
    (id) => {
      setSelectedMenu((prevSelected) => {
        const newSelected = prevSelected.includes(id)
          ? prevSelected.filter((branchId) => branchId !== id)
          : [...prevSelected, id];

        setRemovedBranches((prev) => {
          const branch = branchCatList.find((b) => b.id === id);
          if (branch?.is_health_and_safety === 1 && !newSelected.includes(id)) {
            return [...prev, id];
          }
          return prev.filter((branchId) => branchId !== id);
        });

        return newSelected;
      });
    },
    [branchCatList]
  );

  // Add handlers for Add All and Remove All
  const handleAddAll = useCallback(() => {
    if (branchCatList?.length) {
      const allBranchIds = branchCatList.map((branch) => branch.id);
      setSelectedMenu(allBranchIds);
      setRemovedBranches([]);
    }
  }, [branchCatList]);

  const handleRemoveAll = useCallback(() => {
    setSelectedMenu(oldSelectedMenu);
    setRemovedBranches(initialAssignedIds);
  }, [oldSelectedMenu, initialAssignedIds]);

  // Effects
  useEffect(() => {
    const fetchBranches = async () => {
      if (selectedCategory) {
        setIsInitialLoading(true);
        try {
          await getCategoryBranchList(selectedCategory, '', '');
        } catch (error) {
          console.error('Error fetching branch list:', error);
          setApiMessage(
            'error',
            error?.response?.data?.message || 'Failed to fetch branches'
          );
        } finally {
          setIsInitialLoading(false);
        }
      }
    };
    fetchBranches();
  }, [selectedCategory]);

  useEffect(() => {
    if (branchCatList?.length && !oldSelectedMenu.length) {
      const healthAndSafetyCategories = branchCatList
        .filter((branch) => branch.is_health_and_safety === 1)
        .map((branch) => branch.id);
      setSelectedMenu(healthAndSafetyCategories);
      setOldSelectedMenu(healthAndSafetyCategories);
    }
  }, [branchCatList, oldSelectedMenu.length]);

  // Loading state
  if (isInitialLoading) {
    return (
      <Box
        className="assign-branch-section d-flex justify-center align-center"
        style={{ minHeight: '200px' }}
      >
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box className="assign-branch-section">
      <Box className="divider" />
      <Box className="filters-wrap d-flex justify-end align-center">
        <Box className="search-section-fields">
          <CustomSearch
            setSearchValue={(e) =>
              setAssignFilter((prev) => ({ ...prev, searchValue: e }))
            }
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            searchValue={assignFilter?.searchValue}
            isClearSearch
            handleClearSearch={() => handleClearSearch()}
          />
        </Box>

        <Box className="d-flex align-center gap-sm">
          <CustomButton
            isIconOnly
            startIcon={
              <Tooltip
                title={
                  <Typography className="sub-title-text">
                    Apply Filter
                  </Typography>
                }
                classes={{ tooltip: 'info-tooltip-container' }}
                arrow
              >
                <CheckIcon />
              </Tooltip>
            }
            onClick={handleSearch}
          />

          <CustomButton
            variant="outlined"
            isIconOnly
            startIcon={
              <Tooltip
                title={
                  <Typography className="sub-title-text">
                    Clear Filter
                  </Typography>
                }
                classes={{ tooltip: 'info-tooltip-container' }}
                arrow
              >
                <ClearOutlinedIcon />
              </Tooltip>
            }
            onClick={handleClearFilter}
          />
        </Box>
      </Box>

      <Box className="assign-emp">
        <Box className="Assign-emp-grid">
          <BranchListPanel
            title="Un-Assign Branches"
            branches={unassignedBranches}
            isSelected={false}
            actionLabel="Add All"
            onAction={handleAddAll}
            onItemToggle={handleItemClick}
          />
          <BranchListPanel
            title="Assign Branches"
            branches={assignedBranches}
            isSelected={true}
            actionLabel="Remove All"
            onAction={handleRemoveAll}
            onItemToggle={handleItemClick}
          />
        </Box>
      </Box>

      <Box className="form-actions-btn">
        <CustomButton
          variant="outlined"
          title="Cancel"
          onClick={() => setClose(false)}
          disabled={isLoading}
        />
        <CustomButton
          variant="contained"
          title="Save"
          fullWidth={false}
          onClick={() =>
            updateCategoryBranchAssignments(selectedCategory, selectedMenu)
          }
          disabled={isLoading || !isSelectionChanged}
        />
      </Box>
    </Box>
  );
};

export default BranchAssign;
