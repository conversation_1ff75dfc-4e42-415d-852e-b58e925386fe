import React from 'react';
import Select, { components } from 'react-select';
import { Box, FormLabel, Typography } from '@mui/material';
import '../CustomSelect/customSelect.scss';

const MultiSelect = ({
  options,
  value,
  onChange,
  placeholder = 'Select...',
  isMulti = true,
  label,
  error,
  helperText,
  className = '',
  isSearchable = true,
  isClearable = true,
  isDisabled = false,
  required = false,
  name,
  defaultValue = '',
  menuPosition = 'fixed',
  menuPortalTarget = document.body,
  isHeight,
  isAreaManager = false,
  showDot = false,
}) => {
  const dots = (color = '#39596e') => ({
    alignItems: 'center',
    display: 'flex',
    ':before': {
      backgroundColor: color,
      borderRadius: 10,
      content: '" "',
      display: 'block',
      height: 10,
      width: 10,
    },
  });

  const customStyles = {
    menu: (provided) => ({
      ...provided,
      maxHeight: '230px',
      boxShadow: '4px 4px 6px rgba(0, 0, 0, 0.1)',
      border: '1px solid #a6a8b1',
    }),
    menuList: (provided) => ({
      ...provided,
      maxHeight: isHeight || '230px',
    }),
    menuPortal: (base) => ({
      ...base,
      zIndex: 99999,
    }),
    multiValue: (provided, { data }) =>
      showDot ? { ...provided, ...dots(data.color || '#39596e') } : provided,
    option: (provided) => provided,
    multiValueRemove: (provided) => ({
      ...provided,
      display: isDisabled ? 'none' : 'flex',
      cursor: isDisabled ? 'not-allowed' : 'pointer',
    }),
    control: (provided) => ({
      ...provided,
      backgroundColor: isDisabled ? '#f5f5f5' : 'white',
      cursor: isDisabled ? 'not-allowed' : 'default',
    }),
  };

  const DotOption = (props) => (
    <components.Option {...props}>
      <Box display="flex" alignItems="center">
        {showDot && (
          <span
            style={{
              backgroundColor: props.data.color || '#39596e',
              width: 10,
              height: 10,
              borderRadius: '50%',
              marginRight: 8,
            }}
          />
        )}
        {props.data.label}
      </Box>
    </components.Option>
  );

  const DotSingleValue = (props) => (
    <components.SingleValue {...props}>
      <Box display="flex" alignItems="center">
        {showDot && (
          <span
            style={{
              backgroundColor: props.data.color || '#39596e',
              width: 10,
              height: 10,
              borderRadius: '50%',
              marginRight: 8,
            }}
          />
        )}
        {props.data.label}
      </Box>
    </components.SingleValue>
  );

  const MultiValueRemove = (props) => {
    const { data, selectProps } = props;
    const firstOptionValue = selectProps.value?.[0]?.value;
    if (data.value === firstOptionValue || selectProps.isDisabled) {
      return null;
    }
    return <components.MultiValueRemove {...props} />;
  };

  return (
    <Box className={`custom-select-wrapper ${className}`}>
      {label && (
        <FormLabel
          htmlFor={name}
          id={name}
          className={`field-label ${error ? 'error-label' : ''}`}
        >
          {label}
          {required && <span className="required">*</span>}
        </FormLabel>
      )}

      <Select
        options={options}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        isMulti={isMulti}
        isSearchable={isSearchable}
        isClearable={!isDisabled && isClearable}
        isDisabled={isDisabled}
        name={name}
        aria-labelledby={name}
        className={`select-container ${error ? 'error-border' : ''}`}
        classNamePrefix="select"
        defaultValue={defaultValue}
        menuPosition={menuPosition}
        menuPortalTarget={menuPortalTarget}
        styles={customStyles}
        components={{
          Option: DotOption,
          SingleValue: DotSingleValue,
          ...(isAreaManager && { MultiValueRemove }),
        }}
      />
      {error && helperText && (
        <Typography className="select-field-error-text">
          {helperText}
        </Typography>
      )}
    </Box>
  );
};

export default MultiSelect;
