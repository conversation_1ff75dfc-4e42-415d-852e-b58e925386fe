.recipe-settings-container {
  .org-slug-section {
    margin-bottom: var(--spacing-xl);
    margin-top: var(--spacing-sm);
    &__link-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      padding: var(--spacing-md);
      background-color: var(--color-primary-opacity);
      border: var(--border-width-xs) solid var(--border-color-light-gray);
      border-radius: var(--border-radius-md);
      transition: all 0.15s ease-out;
      width: fit-content;
      &:hover {
        background-color: var(--color-off-white);
      }
    }
    &__link-icon {
      flex-shrink: 0;
      width: var(--spacing-xl);
      height: var(--spacing-xl);
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--color-white);
      color: var(--icon-color-primary);
      border-radius: var(--border-radius-sm);
      cursor: pointer;
    }
    &__link-content {
      flex: 1;
      min-width: 0;
    }
    &__link-url {
      font-family: var(--font-family-primary);
      font-size: var(--font-size-xs);
      color: var(--text-color-slate-gray);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      display: grid;
    }
    &__link-title {
      font-family: var(--font-family-primary);
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      color: var(--text-color-primary);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-bottom: var(--spacing-tiny);
    }
  }
  .recipe-public-settings {
    display: grid;
    // grid-template-columns: repeat(4, 1fr);
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    column-gap: var(--spacing-sm);
    row-gap: var(--spacing-none);
    margin-top: var(--spacing-sm);
    .MuiFormControlLabel-root {
      margin: 0;
    }
  }
}
.recipe-settings-form {
  margin-left: var(--spacing-2xl);
  .contact-form-fields {
    max-width: 200px;
    width: 100%;
  }
}
