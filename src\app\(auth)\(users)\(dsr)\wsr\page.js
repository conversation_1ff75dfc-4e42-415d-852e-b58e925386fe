import React from 'react';
import { Box } from '@mui/material';
import { generateMetadata } from '@/helper/common/commonFunctions';
import WSRs from '@/components/DSR/WSRs/wsr';

export const metadata = generateMetadata({
  pageTitle: 'WSR',
});

const WSRsPage = ({ params }) => {
  return (
    <Box className="main-page-container">
      <WSRs params={params} />
    </Box>
  );
};

export default WSRsPage;
