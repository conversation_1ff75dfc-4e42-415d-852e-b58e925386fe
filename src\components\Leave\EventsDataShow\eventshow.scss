@import '@/styles/variable.scss';

.user-leave-wrap {
  .user-leave-details-wrap {
    .user-extra-data-wrap {
      border: 1px solid $color-Dark-10;
      border-radius: 4px;
      padding: 10px;
      gap: 10px;
      .user-details-wrap {
        .user-details {
          gap: 10px;
          .user-avatar-wrap {
            .MuiAvatar-root {
              width: 35px;
              height: 35px;
              font-size: 14px;
            }
          }
          .user-name-wrap {
            .popover-title-wrap {
              line-height: 16px !important;
            }
          }
        }
      }
      .leave-wrap {
        gap: 10px;
        .leave-type-wrap {
          width: 25%;
        }
        .leave-status-wrap {
          .approved-status {
            text-transform: capitalize;
            color: $color-green;
          }
          .pending-status {
            text-transform: capitalize;
            color: $color-Warning;
          }
          .rejected-status {
            text-transform: capitalize;
            color: $color-dark-red;
          }
          .canceled-status {
            text-transform: capitalize;
            color: $color-Black;
          }
        }
        .leave-type-text {
          text-transform: capitalize;
        }
        @media (max-width: 400px) {
          flex-wrap: wrap;
        }
      }
      .user-role {
        color: $color-Dark-50;
      }
      .leave-text-wrap {
        color: $color-Dark-50;
      }
      .status-wrap {
        color: $color-Success;
      }
      .right-arrow-wrap {
        line-height: 0px;
        background-color: $color-primary-bg-color;
        padding: 5px;
        border-radius: 3px;
        .right-arrow-icon {
          cursor: pointer;
          fill: $color-primary;
          height: 21px;
          width: 21px;
        }
      }
    }
  }
  .holiday-wrap {
    .holiday-text {
      background-color: $sky-blue-tint;
      border: 1px solid $deep-aqua-blue;
      color: $deep-aqua-blue;
      border-radius: 4px;
      padding: 0px 5px;
      margin-bottom: 5px;
    }
  }
  .calender-search-wrap {
    .search-bar {
      width: 100% !important;
      max-width: 100% !important;
    }
    .MuiInputBase-root {
      min-height: 30px;
      border-radius: 4px;
      .MuiSvgIcon-root {
        height: 21px;
        width: 21px;
      }
      .MuiInputBase-input {
        padding: 2px 5px !important;
        &::placeholder {
          font-size: 14px;
        }
      }
      .MuiOutlinedInput-notchedOutline {
        border-color: none !important;
        border-color: $color-Dark-10 !important;
      }
    }
    @media (max-width: 575px) {
      width: 100%;
    }
  }
}
