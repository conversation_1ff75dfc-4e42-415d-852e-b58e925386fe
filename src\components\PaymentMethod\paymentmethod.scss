@import '@/styles/variable.scss';

.payment-method-wrap {
  min-height: calc(100vh - 130px - var(--banner-height));

  .payment-header-wrap {
    .payment-text-wrap {
      font-size: 24px;
      font-family: $PrimaryFont;
      font-weight: 500;
    }

    .payment-subtext-wrap {
      font-size: 14px;
      color: #a6a8b1;
      font-family: $PrimaryFont;
      padding-bottom: 15px;
    }
  }

  .plans-wrap {
    gap: 30px;

    .choose-plan-wrap {
      flex: 1;
    }

    .plan-payment-wrap {
      flex: 1;
    }

    @media (max-width: 991px) {
      flex-direction: column;
    }
  }
}
