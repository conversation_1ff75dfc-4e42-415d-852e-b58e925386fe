@import '@/styles/variable.scss';
.leave-report-section {
  background-color: var(--color-white);
  padding: var(--spacing-xxl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-xs);
  position: relative;
  overflow: auto;
  .tabs-wrap {
    // display: flex;
    // justify-content: space-between;
    // align-items: center;
    border-bottom: 1px solid #ededed;
    @media (max-width: 768px) {
      // display: flex;
      // flex-direction: column;
      // gap: 10px;
      // align-items: flex-start;
      border-bottom: 0;
    }
    .report-tabs {
      display: flex;
      align-items: center;
      justify-content: space-between;
      overflow: auto;
      border-bottom: var(--normal-sec-border);
      .MuiTabs-root {
        border-bottom: 0;
      }
      .tab-header-wrap {
        .year-select-box {
          width: 100%;
          max-width: 220px;
          .MuiSelect-select {
            font-size: 14px;
            padding: 2px 7px 3px 15px;
            margin-top: 0px !important;
          }

          fieldset {
            height: 30px !important;
            border-radius: 4px;
            margin-top: 4px !important;
          }

          .MuiSvgIcon-root {
            margin-top: 1px;
          }

          .placeholder {
            margin-top: 2px;
            font-family: Inter, sans-serif;
            font-size: 14px;
            font-weight: 300 !important;
          }

          @media (max-width: 575px) {
            max-width: 100%;
          }
        }
        @media (max-width: 767px) {
          margin-bottom: 15px;
        }
      }
      @media (max-width: 768px) {
        border-bottom: 1px solid #ededed;
        width: 100%;
      }
      @media (max-width: 767px) {
        flex-wrap: wrap;
        row-gap: 10px;
      }
    }
  }
  @media (max-width: 575px) {
    padding: 10px 14px 56px;
  }
}
.download-popover {
  .MuiPaper-root {
    width: 120px !important;
    margin-top: 8px;
    padding: 10px 20px;
  }

  .export-option {
    p {
      text-align: center;
    }
  }
}
.Add-Holiday-popover {
  .MuiPaper-root {
    width: 280px !important;
    margin-top: 8px;
    padding: 10px 20px;
  }
  .holiday-option {
    svg {
      margin-right: 8px;
      width: 21px;
      height: 21px;
      margin-top: 4px;
    }
    .add-icon {
      // fill: $color-Dark-70;
      fill: var(--color-primary);
    }
    .import-icon {
      // fill: $color-green;
      fill: var(--color-primary);
    }
  }
}
.select-date-wrap {
  width: 100%;
  max-width: 240px;
  height: 100%;
  max-height: 30px;
  .MuiOutlinedInput-root {
    background-color: var(--color-secondary);
    color: var(--text-color-black);
    border-radius: var(--field-radius);
    font-family: var(--font-family-primary);
    .MuiOutlinedInput-input {
      padding: var(--field-padding);
      font-size: var(--font-size-sm);
      line-height: var(--line-height-xs);
      height: auto;
    }

    &.Mui-focused fieldset {
      border: var(--field-border-primary);
    }

    &:hover fieldset {
      border: var(--field-border-primary);
    }

    .MuiInputAdornment-root {
      color: var(--color-primary);
      margin-right: 0px;

      .MuiSelect-select {
        border: none;
      }

      fieldset {
        border: none;
      }

      &:hover fieldset {
        border: none;
      }

      .MuiSvgIcon-root {
        font-size: var(--field-icon-size);
        fill: var(--icon-color-primary);
      }
    }
  }
  .rmdp-container {
    width: 100%;
    > div {
      z-index: 1000 !important;
    }
    .rmdp-ep-arrow {
      z-index: 1001 !important;
      top: 1px !important;
      &::after {
        top: 4px !important;
      }
    }
    .rmdp-arrow-container {
      &:hover {
        background-color: var(--color-white);
        box-shadow: none;
      }
    }
    .select-date-range {
      width: 100%;
      max-width: 240px;
      height: 100%;
      max-height: 30px;
      padding: var(--field-padding);
      border: var(--field-border);
      // font-size: 14px;
      // font-weight: 400 !important;
      background-color: var(--color-secondary);
      color: var(--text-color-black);
      border-radius: var(--field-radius);
      font-family: var(--font-family-primary);
      font-size: var(--font-size-sm);
      line-height: var(--line-height-xs);
      &:focus {
        box-shadow: none;
      }
      .calender-icon {
        height: 21px;
        width: 21px;
        fill: var(--icon-color-primary);
        font-size: var(--field-icon-size);
      }
      .placeholder-wrap {
        font-family: var(--font-family-primary);
        color: var(--field-placeholder);
        font-size: var(--font-size-sm);
        line-height: var(--line-height-sm);
        letter-spacing: 0px;
        opacity: 0.8;
      }
      @media (max-width: 575px) {
        max-width: 100%;
      }
    }

    .rmdp-calendar {
      .rmdp-arrow {
        border-color: var(--color-primary);
      }
      .rmdp-week-day {
        color: var(--color-primary);
      }
      .rmdp-today {
        background-color: var(--color-white);
        .sd {
          background-color: var(--color-white);
          color: var(--color-primary);
          font-weight: 500;
        }
      }
      .rmdp-range {
        background-color: var(--color-primary);
        box-shadow: none;
      }
    }
  }
  @media (max-width: 575px) {
    max-width: 100%;
  }
}
