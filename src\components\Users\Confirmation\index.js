'use client';

import React from 'react';
import { Box, Typography } from '@mui/material';
import CustomButton from '@/components/UI/CustomButton';
import './confirmation.scss';

export default function Confirmation({
  handleClose,
  text,
  ResetUserDetails,
  isRedirect,
}) {
  return (
    <Box className="confirmation-modal-wrap">
      <Typography className="confirmation-text">{text}</Typography>
      <Box className="confirmation-action">
        <CustomButton
          className="cancel-button"
          fullWidth
          variant="outlined"
          title="Cancel"
          onClick={() => handleClose()}
        />
        <CustomButton
          className="confirm-button"
          fullWidth
          variant="contained"
          title={isRedirect ? 'Save' : 'Confirm'}
          onClick={() => ResetUserDetails()}
        />
      </Box>
    </Box>
  );
}
