.own-notification-container {
  background-color: var(--color-white);
  padding: 0px var(--spacing-xxl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-xs);
  height: 100%;
  overflow: auto;
  .own-notification-sec {
    .own-notification-details {
      display: flex;
      justify-content: space-between;
      border-radius: var(--border-radius-md);
      border: 1px solid var(--border-color-primary);
      background-color: var(--color-white);
      width: 100%;
      border-inline-start: 3px solid var(--border-color-primary);
      padding: var(--spacing-xl);
      margin-top: var(--spacing-xl);
      @media (max-width: 599px) {
        display: flex;
        flex-direction: column;
      }
      .notification-left {
        display: flex;
        .name-icon {
          margin-right: var(--spacing-lg);
        }
        .notification-image {
          width: var(--icon-size-xl);
          height: var(--icon-size-xl);
          border-radius: var(--border-radius-full);
          margin-right: var(--spacing-lg);
          object-fit: cover;
        }
      }
      .notification-right {
        margin-left: var(--spacing-lg);
        @media (max-width: 599px) {
          text-align: right;
          margin-left: var(--spacing-none);
          margin-top: var(--spacing-lg);
        }
        .notification-date {
          width: max-content;
        }
      }
    }
    .unread-notification {
      background: var(--color-primary-opacity);
    }
  }
  .notification-icon {
    border: 0;
    width: var(--icon-size-xl);
    height: var(--icon-size-xl);
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: var(--spacing-lg);
    background-color: var(--icon-color-primary);
    svg {
      fill: var(--icon-color-white) !important;
      width: var(--icon-size-md);
      height: var(--icon-size-md);
    }
  }
  .noti-dsr-icon {
    svg {
      stroke: var(--icon-color-white) !important;
      fill: none !important;
    }
  }
  .resign-icon {
    svg {
      stroke: var(--icon-color-white) !important;
      fill: none !important;
      width: var(--icon-size-xsm) !important;
      height: var(--icon-size-xsm) !important;
      stroke-width: 2px !important;
    }
  }
}
.notification-preview-image {
  width: 100%;
  height: 100%;
  padding: var(--spacing-xl);
  img {
    border-radius: var(--border-radius-sm);
    width: auto;
    max-width: 100%;
    height: auto;
    margin: auto;
    display: block;
    aspect-ratio: 16/9;
    object-fit: contain;
  }
}
