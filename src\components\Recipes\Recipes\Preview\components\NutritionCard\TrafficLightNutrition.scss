.traffic-light-nutrition {
  display: flex;
  flex-direction: column;
  font-family: Arial, sans-serif;
  overflow: hidden;
  max-width: 100%;
  border: var(--normal-sec-border);
  border-radius: var(--border-radius-md);
  margin: var(--spacing-md) 0px 0px;

  .nutrition-header {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: #f5f5f5;
    color: var(--text-color-black);
    border-bottom: var(--normal-sec-border);
  }

  .nutrition-items-container {
    display: grid;
    grid-template-columns: repeat(8, 1fr); // Responsive columns
    gap: 0; // Remove gap, use border instead
    overflow: auto;
    background-color: var(--color-light-gray);
  }

  .nutrition-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    text-align: center;
    padding: var(--spacing-sm) var(--spacing-xs);
    background-color: var(--color-white);
    border-right: 1px solid var(--color-light-gray);
    border-bottom: 1px solid var(--color-light-gray);

    .nutrition-item__icon-placeholder {
      // Basic style for icon placeholder
      width: 24px;
      height: 24px;
      border-radius: 50%;
      margin-bottom: 5px;
      // Apply icon-specific background colors from your field.color
      // This requires generating these classes or using inline styles in JS

      // Example of how you might target specific icon colors if classes are like 'nutrition-section__icon--orange'
      &.nutrition-section__icon--orange {
        background-color: var(--color-orange);
      }
      &.nutrition-section__icon--red {
        background-color: var(--text-color-danger);
      } // Note: this red is for icon, not traffic light
      &.nutrition-section__icon--yellow {
        background-color: var(--color-muted-mustard);
      } // Note: this is for icon
      &.nutrition-section__icon--blue {
        background-color: var(--color-holiday);
      }
      &.nutrition-section__icon--green {
        background-color: var(--color-green);
      } // Note: this is for icon
      &.nutrition-section__icon--pink {
        background-color: var(--color-pink);
      }
      &.nutrition-section__icon--gray {
        background-color: var(--text-color-slate-gray);
      }
      &.nutrition-section__icon--purple {
        background-color: var(--color-purple);
      }
    }

    &__label {
      font-family: var(--font-family-primary);
      font-size: var(--font-size-xxs);
      font-weight: var(--font-weight-medium);
    }

    &__value {
      font-family: var(--font-family-primary);
      font-size: var(--font-size-xs);
      font-weight: var(--font-weight-medium);
    }

    &__ri {
      font-family: var(--font-family-primary);
      font-size: var(--font-size-xs);
      font-weight: var(--font-weight-medium);
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--border-radius-lg);
      color: var(--text-color-white);
      min-width: 35px;
      line-height: 1;
      margin-top: var(--spacing-xs);
    }

    // Color classes for the RI pill background (Traffic Lights)
    &--green .nutrition-item__ri {
      background-color: var(--color-green);
    }
    &--amber .nutrition-item__ri {
      background-color: var(--color-muted-mustard);
      color: var(--text-color-black);
    }
    &--red .nutrition-item__ri {
      background-color: var(--text-color-danger);
    }
    &--grey .nutrition-item__ri {
      background-color: var(--text-color-slate-gray);
    }
  }

  .nutrition-item:last-child {
    border-right: none;
  }
}