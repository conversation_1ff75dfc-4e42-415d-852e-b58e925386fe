import React from 'react';
import CustomSelect from '@/components/UI/CustomSelect';
import CustomTextField from '@/components/UI/CustomTextField';
import { Box, Typography } from '@mui/material';
import { identifiers } from '@/helper/constants/identifier';
export default function CarryForwardSection({
  values,
  handleChange,
  handleBlur,
  touched,
  errors,
  setFieldValue,
  leavePolicySubmit,
  setLeavePolicySubmit,
  Inputype,
}) {
  return (
    <>
      <Box className="d-flex gap-10 align-center mt16 flex-wrap">
        <Box className="d-flex align-center gap-10">
          <Typography className="title-text">Carry Forward</Typography>
          <Box className="d-flex align-center">
            <Box className="small-text-field">
              <CustomTextField
                fullWidth
                id="carryForwardDays"
                name="carryForwardDays"
                placeholder=""
                className="text-field-right-border-none"
                value={values?.carryForwardDays}
                error={Boolean(
                  touched?.carryForwardDays && errors?.carryForwardDays
                )}
                helperText={
                  touched?.carryForwardDays && errors?.carryForwardDays
                }
                onChange={(e) => {
                  handleChange(e);
                  setLeavePolicySubmit({
                    ...leavePolicySubmit,
                    leaveAccrual: false,
                  });
                }}
                onBlur={handleBlur}
                onInput={Inputype}
              />
            </Box>
            <CustomSelect
              name="carryForwardPercentage"
              placeholder="Percentage"
              className="text-field-left-border-none leave-select-wrap"
              options={identifiers?.CARRY_FORWARD_OPTIONS}
              value={
                identifiers?.CARRY_FORWARD_OPTIONS?.find((opt) => {
                  return opt?.value === values.carryForwardPercentage;
                }) || ''
              }
              onChange={(e) => {
                setFieldValue('carryForwardPercentage', e?.value);
                setLeavePolicySubmit({
                  ...leavePolicySubmit,
                  leaveAccrual: false,
                });
              }}
              error={Boolean(
                touched.carryForwardPercentage && errors.carryForwardPercentage
              )}
              helperText={
                touched.carryForwardPercentage && errors.carryForwardPercentage
              }
              isClearable={false}
            />
          </Box>
        </Box>
        <Box className="effective-after-end d-flex align-center gap-10 flex-wrap">
          <Box className="d-flex align-center gap-sm">
            <Typography className="title-text">Max Limit</Typography>
            <Box>
              <CustomTextField
                fullWidth
                id="carryForwardMaxLimit"
                name="carryForwardMaxLimit"
                value={values?.carryForwardMaxLimit}
                placeholder="Days"
                error={Boolean(
                  touched?.carryForwardMaxLimit && errors?.carryForwardMaxLimit
                )}
                helperText={
                  touched?.carryForwardMaxLimit && errors?.carryForwardMaxLimit
                }
                onBlur={handleBlur}
                onChange={(e) => {
                  handleChange(e);
                  setLeavePolicySubmit({
                    ...leavePolicySubmit,
                    leaveAccrual: false,
                  });
                }}
                onInput={Inputype}
              />
            </Box>
          </Box>
          <Box className="d-flex align-center gap-sm">
            <Typography className="title-text">Min Limit</Typography>
            <Box>
              <CustomTextField
                fullWidth
                id="carryForwardMinLimit"
                name="carryForwardMinLimit"
                value={values?.carryForwardMinLimit}
                placeholder="Days"
                error={Boolean(
                  touched?.carryForwardMinLimit && errors?.carryForwardMinLimit
                )}
                helperText={
                  touched?.carryForwardMinLimit && errors?.carryForwardMinLimit
                }
                onBlur={handleBlur}
                onChange={(e) => {
                  handleChange(e);
                  setLeavePolicySubmit({
                    ...leavePolicySubmit,
                    leaveAccrual: false,
                  });
                }}
                onInput={Inputype}
              />
            </Box>
          </Box>
        </Box>
      </Box>
    </>
  );
}
