'use client';
import React, { useState } from 'react';
import { Box, Typography } from '@mui/material';
import DialogBox from '@/components/UI/Modalbox';
import moment from 'moment';
import HeaderImage from '@/components/UI/ImageSecurity';

export default function Notification({
  notificationList,
  handleMarkAsRead,
  handleNotificationIcon,
}) {
  const [mediaPreview, setMediaPreview] = useState(false);
  const [mediaImage, setMediaImage] = useState(false);
  return (
    <Box className="own-notification-sec">
      {notificationList &&
        notificationList?.length > 0 &&
        notificationList?.map((item, index) => {
          return (
            <Box
              key={index}
              className={`own-notification-details ${
                item?.notification_status === 'read'
                  ? ''
                  : 'cursor-pointer unread-notification'
              }`}
              onClick={() => {
                item?.notification_status !== 'read' && handleMarkAsRead(item);
              }}
            >
              <Box className="notification-left">
                <Box>
                  {item?.notification_image_url ? (
                    <>
                      <HeaderImage
                        imageUrl={item?.notification_image_url}
                        width={40}
                        height={40}
                        alt="not found"
                        className="notification-image"
                        type="lazyload"
                        onClick={() => {
                          setMediaPreview(true);
                          setMediaImage(item?.notification_image_url);
                        }}
                      />
                    </>
                  ) : (
                    <>{handleNotificationIcon(item?.redirection_type)}</>
                  )}
                </Box>
                <Box className="leave-left">
                  <Typography className="body-text fw600 pb4">
                    {item?.notification_subject}
                  </Typography>
                  <Typography className="title-text">
                    {item?.notification_content}
                  </Typography>
                </Box>
              </Box>
              <Box className="notification-right">
                <Typography className="title-text notification-date bg-date-transparent">
                  <span>
                    {item?.createdAt
                      ? moment(item?.createdAt)?.format('DD/MM/YYYY hh:mm A')
                      : ''}
                  </span>
                </Typography>
              </Box>
            </Box>
          );
        })}
      <DialogBox
        open={mediaPreview}
        handleClose={() => {
          setMediaPreview(!mediaPreview);
        }}
        title={'Notification image'}
        className="staff-dialogbox"
        content={
          <>
            <Box className="notification-preview-image">
              {mediaImage ? (
                <>
                  <HeaderImage
                    imageUrl={mediaImage}
                    alt="not found"
                    className="meida"
                    type="lazyload"
                  />
                </>
              ) : (
                <></>
              )}
            </Box>
          </>
        }
      />
    </Box>
  );
}
