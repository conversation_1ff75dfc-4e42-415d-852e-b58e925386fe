@import '@/styles/variable.scss';

.verify-number-wrap {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
    box-shadow: 0px 0px 5px lightgray;
    border-radius: 8px;
    text-align: center;
    padding: 30px;

    .heading-wrap {
        padding: 15px 0px 30px 0px;
        color: $color-primary;
        font-weight: 600;

        @media(max-width:374px) {
            font-size: 32px;
        }
    }

    .text-wrap {
        color: $color-Dark-50;
    }

    .phone-text-wrap {
        color: $color-Dark-50;
        width: 100%;
        max-width: 430px;
        margin: 0 auto;
    }

    .code-text-wrap {
        padding: 25px 0px;
        color: $color-green;

        .try-again-wrap {
            color: $color-primary;
            font-weight: 500;
        }
    }

    .input-wrap {
        width: 55%;
        margin: 0 auto;
        gap: 20px;

        .opt-input {
            .MuiInputBase-input {
                padding-left: 25px;
            }

            .MuiFormHelperText-root {
                margin-left: 7.1px;
            }

            @media(max-width:767px) {

                .MuiInputBase-input {
                    padding-left: 20px;

                }

                .MuiFormHelperText-root {
                    margin-left: 1px;
                }
            }

            @media(max-width:574px) {
                .MuiInputBase-input {
                    padding: 8.5px 14px 8.5px 19px;
                }

                .MuiInputBase-root {
                    min-height: 43px;
                }
            }

            @media(max-width:424px) {
                .MuiInputBase-input {
                    padding: 8.5px 14px 8.5px 17px;
                }
            }

            @media(max-width:374px) {
                .MuiInputBase-input {
                    padding: 6.5px 10px 7.5px 15px;
                }

                .MuiInputBase-root {
                    min-height: 40px;
                }
            }
        }

        @media(max-width:767px) {
            gap: 12px;
        }

        @media(max-width:575px) {
            width: 80%;
        }

    }

    .verify-btn-wrap {
        .verify-btn {
            &:hover {
                color: white !important;
            }

            @media(max-width:575px) {
                padding: 7px 20px !important;
            }
        }
    }

    @media(max-width:424px) {
        padding: 30px 14px;
    }

    @media(max-width:374px) {
        padding: 10px 14px;
    }
}