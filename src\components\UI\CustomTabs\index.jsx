import React, { useEffect, useState } from 'react';
import { Tabs, Tab, Box, styled } from '@mui/material';

const StyledTabs = styled(Tabs)(() => ({
  borderBottom: 'var(--normal-sec-border)',
  '.MuiTab-root': {
    textTransform: 'none',
    color: 'var(--text-color-black)',
    fontFamily: 'var(--font-family-primary)',
    fontSize: 'var(--font-size-base)',
    lineHeight: 'var(--line-height-base)',
    fontWeight: 'var(--font-weight-semibold)',
    color: 'var(--text-color-black)',
    opacity: 1,
    width: 'max-content',
    padding: 'var(--spacing-xs)',
    marginRight: ' var(--spacing-md)',
    minHeight: 'var(--spacing-4xl)',
    '&.Mui-selected': {
      color: 'var(--text-color-primary)',
    },
  },
  '.MuiTabs-indicator': {
    backgroundColor: 'var(--color-primary)',
    height: '3px',
  },
}));

const CustomTabs = ({ tabs, initialTab = 0, onTabChange }) => {
  const [activeTab, setActiveTab] = useState(initialTab);

  const handleChange = (e, newValue) => {
    // const selectedTabIndex = tabs?.findIndex((tab) => tab.id === newValue);

    // if (isSetup) {
    setActiveTab(newValue);
    onTabChange?.(newValue);
    // } else {
    //   setActiveTab(selectedTabIndex);
    //   onTabChange?.(selectedTabIndex);
    // }
  };

  useEffect(() => {
    setActiveTab(initialTab);
  }, [initialTab]);

  return (
    <Box>
      <StyledTabs
        value={activeTab}
        onChange={handleChange}
        variant="scrollable"
        scrollButtons="auto"
      >
        {tabs?.map((tab, index) => (
          <Tab
            key={tab?.id || index}
            label={tab?.label}
            icon={tab?.icon}
            iconPosition={tab?.icon ? 'start' : undefined}
            // value={isSetup ? tab?.id : index}
            value={tab?.id}
          />
        ))}
      </StyledTabs>
    </Box>
  );
};

export default CustomTabs;
