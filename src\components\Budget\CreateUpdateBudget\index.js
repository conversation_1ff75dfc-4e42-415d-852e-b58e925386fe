'use client';

import React, { useContext, useEffect, useState } from 'react';
import {
  Box,
  Typography,
  InputLabel,
  Popover,
  MenuItem,
  Tooltip,
} from '@mui/material';
import AuthContext from '@/helper/authcontext';
import BranchFilter from '@/components/DSR/Reports/BranchFilter';
import BudgetCategoryFilter from '../CategoryFilter';
import ReactYearRangePicker from '@/components/UI/ReactYearRangePicker';
import CustomButton from '@/components/UI/CustomButton';
import InfoIcon from '@mui/icons-material/Info';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import { setApiMessage } from '@/helper/common/commonFunctions';
import CustomDatePicker from '@/components/UI/CustomDatePicker';
import LightDarkSwitch from '@/components/UI/Switch';
import dayjs from 'dayjs';
import _ from 'lodash';
import moment from 'moment';

export default function CreateUpdateBudget({
  dsrCatData,
  getCategorieList,
  handleCloseDrawer,
  editBudget,
  page,
  getBudgetList,
  FilterBudget,
  createBudgetBM,
  setPage,
  IsBranchManger,
  getBudgetListBM,
  getOneBudgetList,
}) {
  const { authState, AllListsData } = useContext(AuthContext);
  const [anchorElInfo, setAnchorElInfo] = useState(null);
  // const [loader, setLoader] = useState(false);
  const [isSubmit, setIsSubmit] = useState(false);
  const IsForecastYearJanuary =
    authState?.generalSeetings?.financial_month === 'january - december';
  const [createBudget, setCreateBudget] = useState({
    Year: '',
    Branches: [],
    validDate: '',
    income: [
      {
        selectMainCategories: [],
        selectedSubcategories: [],
        selectedToggle: [],
      },
    ],
    expense: [
      {
        selectMainCategories: [],
        selectedSubcategories: [],
        selectedToggle: [],
      },
    ],
    other: [
      {
        selectMainCategories: [],
        selectedSubcategories: [],
        selectedToggle: [],
      },
    ],
  });
  const open = Boolean(anchorElInfo);
  const id = open ? 'simple-popover' : undefined;
  const handleClickProfile = (event) => {
    setAnchorElInfo(event.currentTarget);
  };
  const handleCloseProfile = () => {
    setAnchorElInfo(null);
  };
  const ConvertToBackendFormat = (data) => {
    let mergeCat = _.concat(data?.incomeAPI, data?.expenseAPI, data?.otherAPI);
    mergeCat = mergeCat?.filter((f) => f);
    const transformedData = {
      ...(!createBudgetBM &&
        !editBudget && { forecast_year: data?.ForecastYear || '' }),
      ...(!createBudgetBM &&
        !editBudget && { branch_id: data?.Branches || [] }),
      forecast_due_date: dayjs(data?.validDate).format('YYYY-MM-DD') || null,
      forecast_data: mergeCat,
      ...(createBudgetBM && {
        id: createBudgetBM?.id,
        branch_id: [createBudgetBM?.forecast_branch?.id],
      }),
    };

    return transformedData;
  };
  const createBudgetFun = async () => {
    const requestData = ConvertToBackendFormat(createBudget);
    const ApiUrl = editBudget
      ? URLS.UPDATE_FORCAST + editBudget?.id
      : URLS.ADD_FORECAST;
    const method = editBudget ? 'put' : 'post';
    try {
      // setLoader(true);
      const { status, data } = await axiosInstance[method](ApiUrl, requestData);
      if (status === 200) {
        if (data.status) {
          setApiMessage('success', data?.message);
          handleCloseDrawer();
          if (IsBranchManger) {
            getBudgetListBM(authState?.branch?.id, FilterBudget, '');
          } else if (editBudget) {
            getOneBudgetList(
              editBudget?.branch?.id,
              editBudget?.forecast_year,
              FilterBudget,
              editBudget?.index
            );
          } else {
            getBudgetList(authState?.assign_branch_ids, FilterBudget, '', page);
          }
          setPage(1);
        } else {
          setApiMessage('error', data?.message);
        }
      } else {
        setApiMessage('error', data?.message);
      }
      // setLoader(false);
    } catch (error) {
      // setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const ShowDetails = (type) => {
    return (
      <>
        <InputLabel className="field-label info-label mt8">
          {type}
          <span onClick={handleClickProfile} className="info-icon">
            <InfoIcon />
          </span>
          <Popover
            anchorEl={anchorElInfo}
            className="tooltip-menu budget-com-info"
            id={id}
            open={open}
            onClose={handleCloseProfile}
            anchorOrigin={{
              horizontal: 'center',
              vertical: 'top',
            }}
            transformOrigin={{
              horizontal: 'center',
              vertical: 'bottom',
            }}
          >
            <MenuItem className="">
              <Box>
                <Typography
                  className={`title-text fw600 category-toggle unchecked-toggle`}
                >
                  <LightDarkSwitch checked={false} />
                  <span className="title-text fw600 category-text">
                    Separate :
                  </span>
                </Typography>
                <Typography className="sub-title-text fw500 pt4 sub-text">
                  Each category will be displayed separately in the table
                </Typography>
              </Box>
            </MenuItem>
            <MenuItem>
              <Box>
                <Typography
                  className={`title-text fw600 category-toggle checked-toggle`}
                >
                  <LightDarkSwitch checked={true} />
                  <span className="title-text fw600 category-text">
                    Combine :
                  </span>
                </Typography>
                <Typography className="sub-title-text fw500 pt4 sub-text">
                  All categories will be displayed under their respective main
                  categories.
                </Typography>
                <Typography className="sub-title-text fw500 noted-text">
                  You’ll need to re-enter amounts after switching to combined
                  view.
                </Typography>
              </Box>
            </MenuItem>
          </Popover>
        </InputLabel>
        <Box className="budget-filter-selection mt0">
          <Box className="add-budget-section">
            <BudgetCategoryFilter
              selectedMainCategories={
                createBudget?.[type]?.[0]?.selectMainCategories
              }
              selectedSubcategories={
                createBudget?.[type]?.[0]?.selectedSubcategories
              }
              selectedToggle={createBudget?.[type]?.[0]?.selectedToggle}
              setSelectedToggle={(value) => {
                const array = createBudget[type];
                array[0].selectedToggle = value;
                const result = generateForecastData(
                  array[0].selectMainCategories,
                  array[0].selectedSubcategories,
                  array[0].selectedToggle,
                  dsrCatData[type]
                );

                setCreateBudget({
                  ...createBudget,
                  [type]: array,
                  [type + 'API']: result,
                });
                setIsSubmit(false);
              }}
              dsrCatData={dsrCatData[type]}
              setSelectedMainCategories={(value, subValue) => {
                const array = createBudget[type];
                if (array?.length === 0) {
                  // Initialize array with an object at index 0
                  array.push({
                    selectMainCategories: value,
                    selectedSubcategories: subValue,
                  });
                } else {
                  // Modify the first object if it exists
                  array[0] = {
                    ...array[0],
                    selectMainCategories: value,
                    selectedSubcategories: subValue,
                  };
                }
                const result = generateForecastData(
                  array[0].selectMainCategories,
                  array[0].selectedSubcategories,
                  array[0].selectedToggle,
                  dsrCatData[type]
                );
                setCreateBudget({
                  ...createBudget,
                  [type]: array,
                  [type + 'API']: result,
                });
                setIsSubmit(false);
              }}
              placeholder={`${type} categories`}
              isCreate={!editBudget}
            />
          </Box>
        </Box>
      </>
    );
  };

  // Validation function for create budget/forcast
  const checkValidation = () => {
    if (
      !createBudgetBM &&
      !editBudget &&
      (!createBudget?.ForecastYear ||
        !createBudget?.Branches ||
        createBudget?.Branches?.length === 0)
    ) {
      return true; // Invalid if ForecastYear or Branches are missing/empty
    }
    if (
      (!createBudget?.incomeAPI || createBudget?.incomeAPI?.length === 0) &&
      (!createBudget?.expenseAPI || createBudget?.expenseAPI?.length === 0) &&
      (!createBudget?.otherAPI || createBudget?.otherAPI?.length === 0)
    ) {
      return true; // Invalid if ForecastYear or Branches are missing/empty
    }
    if (
      !createBudget?.validDate ||
      dayjs(createBudget?.validDate).format('YYYY-MM-DD') === 'Invalid Date'
    ) {
      return true; // Invalid if ForecastYear or Branches are missing/empty
    }
  };
  const checkValidationMessage = () => {
    if (!editBudget && !createBudget?.ForecastYear && !createBudgetBM) {
      return 'Budget year must be require.';
    } else if (
      (!createBudgetBM &&
        !editBudget &&
        createBudget?.Branches?.length === 0) ||
      (!createBudgetBM && !editBudget && !createBudget?.Branches)
    ) {
      return 'Branches must be require.';
    } else if (
      (!createBudget?.incomeAPI || createBudget?.incomeAPI?.length === 0) &&
      (!createBudget?.expenseAPI || createBudget?.expenseAPI?.length === 0) &&
      (!createBudget?.otherAPI || createBudget?.otherAPI?.length === 0)
    ) {
      return 'Please select at least one category.';
    }
    if (
      !createBudget?.validDate ||
      dayjs(createBudget?.validDate).format('YYYY-MM-DD') === 'Invalid Date'
    ) {
      return 'Budget valid date must be require.'; // Invalid if ForecastYear or Branches are missing/empty
    }
  };
  // const getMainIncomeId = (selectedCat) => {
  //   return (
  //     dsrCatData?.income &&
  //     dsrCatData?.income?.length > 0 &&
  //     dsrCatData?.income
  //       .filter((item) =>
  //         item?.catList?.some((cat) =>
  //           selectedCat.includes(cat.payment_type_category_id)
  //         )
  //       )
  //       .map((item) => item.id)
  //   );
  // };

  const backendToOriginalFormat = (backendData) => {
    const formattedData = {
      income: [],
      expense: [],
      other: [],
      incomeAPI: [],
      incomeAPI: [],
      otherAPI: [],
      expenseAPI: [],
      validDate: backendData?.forecast_due_date,
    };
    backendData?.forecast_data?.forEach((category) => {
      const categoryType = category?.forecast_category_type;
      const mainCategories = [];
      const subcategories = [];
      const toggles = [];

      category?.forecast_category_data?.forEach((item) => {
        const subcategoryIds = item?.payment_type_category_id
          .split(',')
          .map(Number);
        mainCategories.push(item?.payment_type_id);
        subcategories.push(...subcategoryIds);
        if (item?.forecast_category_status === 'combined') {
          toggles.push(item?.payment_type_id);
        }
      });

      if (categoryType === 'income') {
        formattedData?.income?.push({
          selectMainCategories: mainCategories,
          selectedSubcategories: subcategories,
          selectedToggle: toggles,
        });
        formattedData?.incomeAPI?.push({
          forecast_category_type: category.forecast_category_type,
          forecast_category_data: category.forecast_category_data.map(
            ({
              payment_type_category_id,
              payment_type_id,
              forecast_category_status,
            }) => ({
              payment_type_category_id,
              payment_type_id,
              forecast_category_status,
            })
          ),
        });
      } else if (categoryType === 'other') {
        formattedData.other.push({
          selectMainCategories: mainCategories,
          selectedSubcategories: subcategories,
          selectedToggle: toggles,
        });
        formattedData.otherAPI.push({
          forecast_category_type: category.forecast_category_type,
          forecast_category_data: category.forecast_category_data.map(
            ({
              payment_type_category_id,
              payment_type_id,
              forecast_category_status,
            }) => ({
              payment_type_category_id,
              payment_type_id,
              forecast_category_status,
            })
          ),
        });
      } else if (categoryType === 'expense') {
        formattedData.expense.push({
          selectMainCategories: mainCategories,
          selectedSubcategories: subcategories,
          selectedToggle: toggles,
        });
        formattedData.expenseAPI.push({
          forecast_category_type: category.forecast_category_type,
          forecast_category_data: category.forecast_category_data.map(
            ({
              payment_type_category_id,
              payment_type_id,
              forecast_category_status,
            }) => ({
              payment_type_category_id,
              payment_type_id,
              forecast_category_status,
            })
          ),
        });
      }
    });

    return formattedData;
  };
  const generateForecastData = (
    selectedMain,
    selectedSub,
    selectedToggle,
    paymentData
  ) => {
    const forecastData = [];

    paymentData?.forEach(
      ({ id, payment_type_usage, payment_type_category }) => {
        if (!selectedMain?.includes(id) && !selectedToggle?.includes(id))
          return;

        // Filter selected subcategories
        const selectedCategories = payment_type_category
          .filter((cat) => selectedSub.includes(cat?.payment_type_category_id))
          .map((cat) => cat?.payment_type_category_id);

        if (selectedCategories.length === 0) return;

        // Check if category type already exists
        let categoryType = forecastData.find(
          (item) => item?.forecast_category_type === payment_type_usage
        );

        if (!categoryType) {
          categoryType = {
            forecast_category_type: payment_type_usage,
            forecast_category_data: [],
          };
          forecastData.push(categoryType);
        }

        categoryType.forecast_category_data.push({
          payment_type_category_id: selectedCategories.join(','),
          payment_type_id: id,
          forecast_category_status: selectedToggle?.includes(id)
            ? 'combined'
            : 'separate', // Defaulting to 'separate', modify as needed
        });
      }
    );

    return forecastData;
  };
  useEffect(() => {
    if (editBudget) {
      // getCategorieList(editBudget?.branch?.id, true);
      if (dsrCatData?.income && dsrCatData?.income?.length > 0) {
        setTimeout(() => {
          let fromatdata = backendToOriginalFormat(editBudget);
          if (fromatdata?.income?.length === 0 || !fromatdata?.income) {
            fromatdata.income = [
              {
                selectMainCategories: [],
                selectedSubcategories: [],
                forecast_type: 'budget',
                target: '',
              },
            ];
          }
          setCreateBudget(fromatdata);
        }, 500);
      }
    }
  }, [editBudget, dsrCatData?.income?.length]);
  const FinancialYear = (monthRange, year) => {
    if (!monthRange || !year) return null; // Handle undefined cases

    const months = monthRange
      .split(' - ')
      .map(
        (month) => month.charAt(0).toUpperCase() + month.slice(1).toLowerCase()
      );

    const [startMonth, endMonth] = months;
    const nextYear = Number(year) + 1;

    return (
      <>
        {startMonth} {year} - {endMonth} {nextYear}
      </>
    );
  };
  const FinancialYearJan = (monthRange, year) => {
    if (!monthRange || !year) return null; // Handle undefined cases

    const months = monthRange
      .split(' - ')
      .map(
        (month) => month.charAt(0).toUpperCase() + month.slice(1).toLowerCase()
      );

    const [startMonth, endMonth] = months;
    return (
      <>
        {startMonth} {year} - {endMonth} {year}
      </>
    );
  };
  useEffect(() => {
    if (createBudgetBM) {
      getCategorieList(createBudgetBM?.forecast_branch?.id, true);
      const formattedData = {
        incomeAPI: [],
        incomeAPI: [],
        otherAPI: [],
        expenseAPI: [],
        income: [
          {
            selectMainCategories: [],
            selectedSubcategories: [],
            selectedToggle: [],
          },
        ],
        expense: [
          {
            selectMainCategories: [],
            selectedSubcategories: [],
            selectedToggle: [],
          },
        ],
        other: [
          {
            selectMainCategories: [],
            selectedSubcategories: [],
            selectedToggle: [],
          },
        ],
        validDate: createBudgetBM?.forecast_due_date,
      };
      setCreateBudget(formattedData);
    }
  }, [createBudgetBM]);
  return (
    <Box className="Create-budget">
      <Box className="budget-filters">
        <Typography className="sub-title-text fw500 pb16 d-flex  align-center">
          {/* Configure and assign budgets to branch managers for forecasting. */}
          Configure budgets by selecting categories for forecasting.
        </Typography>

        {createBudgetBM ? (
          <>
            {createBudgetBM?.forecast_branch?.branch_name && (
              <Typography className="title-text fw600">
                Branch :{' '}
                <span className="fw400">
                  {' '}
                  {createBudgetBM?.forecast_branch?.branch_name}
                </span>
              </Typography>
            )}
            {createBudgetBM?.forecast_year && (
              <Typography className="title-text fw600">
                Year :{' '}
                <span className="fw400">{createBudgetBM?.forecast_year}</span>
              </Typography>
            )}
          </>
        ) : editBudget ? (
          <>
            {editBudget?.branch?.branch_name && (
              <Typography className="title-text fw600">
                Branch :{' '}
                <span className="fw400">
                  {' '}
                  {editBudget?.branch?.branch_name}
                </span>
              </Typography>
            )}
            {editBudget?.forecast_year && (
              <Typography className="title-text fw600">
                Year :{' '}
                <span className="fw400">{editBudget?.forecast_year}</span>
              </Typography>
            )}
          </>
        ) : (
          <Box className="">
            <Box className="budget-custom-date-fields ">
              {IsForecastYearJanuary ? (
                <ReactYearRangePicker
                  value={createBudget}
                  disableFuture={true}
                  onChange={(date) => {
                    setCreateBudget({
                      ...createBudget,
                      Year: date?.Year,
                      ForecastYear: `${date?.Year.getFullYear()}-${
                        date?.Year.getFullYear() + 1
                      }`,
                    });
                    setIsSubmit(false);
                  }}
                  inputVariant="outlined"
                  isClearable={false}
                  placeholder="Select Year"
                  label={
                    <span className="field-label">
                      Choose Forecasting Year
                      <span className="required"> *</span>
                    </span>
                  }
                  isSingleMonthpicker={true}
                  // maxDate={getEndOfCurrentWeek()}
                />
              ) : (
                <ReactYearRangePicker
                  value={createBudget}
                  disableFuture={true}
                  onChange={(date) => {
                    setCreateBudget({
                      ...createBudget,
                      Year: date?.Year,
                      ForecastYear: `${date?.Year.getFullYear()}-${
                        date?.Year.getFullYear() + 1
                      }`,
                    });
                    setIsSubmit(false);
                  }}
                  inputVariant="outlined"
                  isClearable={false}
                  placeholder="Select Year"
                  label={
                    <span className="field-label">
                      Choose Forecasting Year
                      <span className="required"> *</span>
                    </span>
                  }
                  // maxDate={getEndOfCurrentWeek()}
                />
              )}
              {authState?.generalSeetings?.financial_month &&
              createBudget?.Year ? (
                <Typography className="year-range pt4">
                  {' '}
                  {IsForecastYearJanuary
                    ? FinancialYearJan(
                        authState?.generalSeetings?.financial_month,
                        moment(createBudget?.Year).format('yyyy')
                      )
                    : FinancialYear(
                        authState?.generalSeetings?.financial_month,
                        moment(createBudget?.Year).format('yyyy')
                      )}
                </Typography>
              ) : (
                <></>
              )}
            </Box>
            {/* Branch Filter */}
            <Box className="pt8">
              <InputLabel className="field-label">
                <span className="field-info">
                  Assign to Branches
                  <Tooltip
                    title="All or Select Specific"
                    placement="bottom"
                    classes={{
                      tooltip: 'info-tooltip-container ',
                    }}
                    arrow
                  >
                    <InfoIcon
                      sx={{ marginLeft: '8px' }}
                      className="info-icon cursor-poniter"
                    />
                  </Tooltip>
                  <span className="required"> *</span>
                </span>
              </InputLabel>
              <BranchFilter
                selectedBranches={createBudget?.Branches}
                setSelectedBranches={(value) => {
                  setCreateBudget({ ...createBudget, Branches: value });
                  setIsSubmit(false);
                  value && value?.length > 0 && getCategorieList(value);
                }}
                branchList={AllListsData?.ActiveBranchList}
              />
            </Box>
            {/* <Typography className="sub-title-text mt16 d-flex align-center noted-text fw600">
            When you create a budget and forecast for the first time, all
            categories linked to the branches will be added with default values.
            After the initial forecast is created, you’ll have the option to
            show or hide specific categories as needed.{' '}
          </Typography> */}
          </Box>
        )}

        {((createBudget?.Branches &&
          createBudget?.Branches?.length > 0 &&
          createBudget?.Year) ||
          editBudget ||
          createBudgetBM) && (
          <>
            <InputLabel className="field-label info-label mt8">
              Income{' '}
              <span onClick={handleClickProfile} className="info-icon">
                <InfoIcon />
              </span>
              <Popover
                anchorEl={anchorElInfo}
                className="tooltip-menu budget-com-info"
                id={id}
                open={open}
                onClose={handleCloseProfile}
                anchorOrigin={{
                  horizontal: 'center',
                  vertical: 'top',
                }}
                transformOrigin={{
                  horizontal: 'center',
                  vertical: 'bottom',
                }}
              >
                <MenuItem className="">
                  <Box>
                    <Typography
                      className={`title-text fw600 category-toggle unchecked-toggle`}
                    >
                      <LightDarkSwitch checked={false} />
                      <span className="title-text fw600 category-text">
                        Separate :
                      </span>
                    </Typography>
                    <Typography className="sub-title-text fw500 pt4 sub-text">
                      Each category will be displayed separately in the table
                    </Typography>
                  </Box>
                </MenuItem>
                <MenuItem>
                  <Box>
                    <Typography
                      className={`title-text fw600 category-toggle checked-toggle`}
                    >
                      <LightDarkSwitch checked={true} />
                      <span className="title-text fw600 category-text">
                        Combine :
                      </span>
                    </Typography>
                    <Typography className="sub-title-text fw500 pt4 sub-text">
                      All categories will be displayed under their respective
                      main categories.
                    </Typography>
                    <Typography className="sub-title-text fw500 noted-text">
                      You’ll need to re-enter amounts after switching to
                      combined view.
                    </Typography>
                  </Box>
                </MenuItem>
              </Popover>
            </InputLabel>
            <Box className={'budget-filter-selection mt0'}>
              <Box className="mt0">
                <Box className="budget-50-filter budget-cat-selection">
                  <BudgetCategoryFilter
                    selectedMainCategories={
                      createBudget?.income?.[0]?.selectMainCategories
                    }
                    selectedSubcategories={
                      createBudget?.income?.[0]?.selectedSubcategories
                    }
                    selectedToggle={createBudget?.income?.[0]?.selectedToggle}
                    setSelectedToggle={(value) => {
                      const array = createBudget?.income;
                      array[0].selectedToggle = value;
                      const result = generateForecastData(
                        array[0].selectMainCategories,
                        array[0].selectedSubcategories,
                        array[0].selectedToggle,
                        dsrCatData?.income
                      );
                      setCreateBudget({
                        ...createBudget,
                        income: array,
                        incomeAPI: result,
                      });
                      setIsSubmit(false);
                    }}
                    dsrCatData={dsrCatData?.income}
                    setSelectedMainCategories={(value, subValue) => {
                      const array = createBudget?.income;
                      if (array?.length === 0) {
                        // Initialize array with an object at index 0
                        array.push({
                          selectMainCategories: value,
                          selectedSubcategories: subValue,
                        });
                      } else {
                        // Modify the first object if it exists
                        array[0] = {
                          ...array[0],
                          selectMainCategories: value,
                          selectedSubcategories: subValue,
                        };
                      }
                      const result = generateForecastData(
                        array[0].selectMainCategories,
                        array[0].selectedSubcategories,
                        array[0].selectedToggle,
                        dsrCatData?.income
                      );
                      setCreateBudget({
                        ...createBudget,
                        income: array,
                        incomeAPI: result,
                      });
                      setIsSubmit(false);
                    }}
                    placeholder="Income categories"
                    isCreate={!editBudget}
                  />
                </Box>
              </Box>
            </Box>
            {ShowDetails('expense')}
            {ShowDetails('other')}
          </>
        )}

        <Box className="pt8">
          <CustomDatePicker
            label={<span>Budget Submission Deadline</span>}
            required
            name="birthdate"
            value={
              createBudget?.validDate ? dayjs(createBudget?.validDate) : null
            }
            onChange={(date) => {
              setCreateBudget({ ...createBudget, validDate: date });
            }}
            // disableFuture={true}
            disabled={IsBranchManger}
            disablePast={true}
            inputVariant="outlined"
          />
        </Box>
        {/* <Box className="budget-target pt32">
          <CustomTextField
            InputLabelProps={{
              shrink: true
            }}
            fullWidth
            id="ename"
            name="ename"
            value={createBudget?.target}
            label="Budget Target *"
            variant="filled"
            placeholder="Budget Target"
            onChange={(e) => {
              setCreateBudget({ ...createBudget, target: e.target.value });
            }}
            InputProps={{
              ...(true && {
                startAdornment: (
                  <InputAdornment position="start">
                    <Typography className="title-text currency">
                      {authState?.currency_details &&
                      authState?.currency_details?.symbol
                        ? authState?.currency_details?.symbol
                        : '£'}
                    </Typography>{' '}
                  </InputAdornment>
                )
              })
            }}
            onInput={(e) => {
              // Remove non-numeric characters
              let value = e.target.value;
              if (value === '' || /^\d*\.?\d{0,2}$/.test(value)) {
                e.target.value = value;
              } else {
                e.target.value = value.slice(0, -1);
              }
            }}
            onPaste={(e) => {
              if (/^[0-9,.]+$/.test(e.clipboardData.getData('text'))) {
                // setValue(pasteData);
              } else {
                e.preventDefault();
              }
            }}
          />
        </Box> */}
      </Box>
      <Box className="pt-56 pb16">
        {isSubmit && (
          <Typography className="error-text mb8">
            <span className="fw600"> {checkValidationMessage()}</span>
          </Typography>
        )}

        <Box className="form-actions-btn">
          <CustomButton
            fullWidth
            variant="outlined"
            title="Cancel"
            onClick={() => {
              setIsSubmit(false);
              handleCloseDrawer();
            }}
          />
          <CustomButton
            fullWidth
            variant="contained"
            title={editBudget ? 'Update' : 'Create'}
            onClick={() => {
              setIsSubmit(true);
              if (!checkValidation()) {
                createBudgetFun();
              }
            }}
          />
        </Box>
      </Box>
    </Box>
  );
}
