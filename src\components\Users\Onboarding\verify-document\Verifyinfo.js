'use client';

import React from 'react';
import {
  Box,
  Typography,
  FormGroup,
  FormControlLabel,
  Checkbox,
} from '@mui/material';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CheckBoxIcon from '@mui/icons-material/CheckBox';

export default function VerifyInfo() {
  return (
    <Box className="sign-verify-info">
      <Box className="sign-home-verify">
        <Typography className="title-text">
          A Home Office online right to work check will provide you with a
          statutory excuse against a civil penalty in the event of illegal
          working involving the subject of the check, provided the check is
          carried out before the commencement of employment and at the
          prescribed intervals.{' '}
        </Typography>
        <Typography className="title-text pt8">
          It will not be possible to conduct a Home Office online right to work
          check in all circumstances. In circumstances in which an online check
          is not possible, you should complete a manual check.
        </Typography>
        <Typography className="title-text pt8">
          Currently, the Home Office online service supports checks for a range
          of individuals, depending on the type of immigration documentation
          they are issued with.{' '}
        </Typography>
        <Typography className="title-text pt8">
          Biometric Residence Card (BRC), Biometric Residence Permit (BRP) and
          Frontier Worker Permit (FWP) holders are also only able to evidence
          their right to work using the Home Office online service. This means
          you cannot accept or check a physical BRC, BRP or FWP as proof of
          right to work.
        </Typography>
        <Typography className="title-text pt8">
          Please see the following steps to conduct an online right to work
          check:
        </Typography>
        <FormGroup className={'form-checkbox pt8'}>
          <FormControlLabel
            control={
              <Checkbox
                className="check-box "
                icon={<CheckBoxOutlineBlankIcon className="uncheck-icon" />}
                checkedIcon={<CheckBoxIcon className="check-icon" />}
              />
            }
            className="check-box-form form-row max-content title-text"
            name="confirmation"
            disabled={true}
            label=<>
              Use the Home Office online right to work checking service
              <a
                href="https://www.gov.uk/viewright-to-work"
                target="blank"
                style={{ color: '#0462c1', textDecoration: 'underline' }}
              >
                https://www.gov.uk/viewright-to-work
              </a>
              on GOV.UK, enter the ‘share code’ provided to you by the
              individual and enter their date of birth.
            </>
          />
        </FormGroup>
        <FormGroup className={'form-checkbox'}>
          <FormControlLabel
            control={
              <Checkbox
                className="check-box "
                icon={<CheckBoxOutlineBlankIcon className="uncheck-icon" />}
                checkedIcon={<CheckBoxIcon className="check-icon" />}
              />
            }
            className="check-box-form form-row max-content title-text"
            name="confirmation"
            disabled={true}
            label={
              'Check that the online check confirms that the employee named in it is allowed to work in the UK and is allowed to carry out the work in question.'
            }
          />
        </FormGroup>
        <FormGroup className={'form-checkbox'}>
          <FormControlLabel
            control={
              <Checkbox
                className="check-box "
                icon={<CheckBoxOutlineBlankIcon className="uncheck-icon" />}
                checkedIcon={<CheckBoxIcon className="check-icon" />}
              />
            }
            className="check-box-form form-row max-content title-text"
            name="confirmation"
            disabled={true}
            label={
              'Satisfy yourself that the photograph on the online right to work check is of the individual presenting themselves for work (i.e. the information provided by the check relates to the individual and they are not an imposter). This can be done in person or by video call.'
            }
          />
        </FormGroup>
        <FormGroup className={'form-checkbox'}>
          <FormControlLabel
            control={
              <Checkbox
                className="check-box "
                icon={<CheckBoxOutlineBlankIcon className="uncheck-icon" />}
                checkedIcon={<CheckBoxIcon className="check-icon" />}
              />
            }
            className="check-box-form form-row max-content title-text"
            name="confirmation"
            disabled={true}
            label={
              'If the employee or prospective employee is a student who has permission to work for a limited number of hours per week during term time whilst studying in the UK, obtain and retain details of the term and vacation dates of the course that the employee is undertaking'
            }
          />
        </FormGroup>
        <FormGroup className={'form-checkbox'}>
          <FormControlLabel
            control={
              <Checkbox
                className="check-box "
                icon={<CheckBoxOutlineBlankIcon className="uncheck-icon" />}
                checkedIcon={<CheckBoxIcon className="check-icon" />}
              />
            }
            className="check-box-form form-row max-content title-text"
            name="confirmation"
            disabled={true}
            label={
              'Retain evidence of the online right to work check. This should be the ‘profile’ page confirming the individual’s right to work. You should store this securely, (electronically or in hardcopy) for the duration of employment and for two years afterwards. The file must then be securely destroyed. You should also be able to produce these document copies quickly in the event that you are requested to show them to demonstrate that you have performed a right to work check and retain a statutory excuse.'
            }
          />
        </FormGroup>
      </Box>
      <Box className="sign-confim">
        <Typography className="title-text pt8 fw600">
          You may wish to include a declaration in line with the check, such a
          statement could include:
        </Typography>
        <Typography className="title-text pt8">
          ‘I confirm that I have carried out the right to work check above in
          compliance with the instructions within and I believe a valid
          statutory excuse is established for this worker.’
        </Typography>
      </Box>
      <Box className="sign-confim more-info">
        <Typography className="title-text">
          More information on how to conduct a manual, IDVT and online check can
          be found in guidance at
          <a
            href="https://www.gov.uk/government/publications/right-to-workchecks-employers-guide"
            target="blank"
            style={{ color: '#0462c1', textDecoration: 'underline' }}
          >
            https://www.gov.uk/government/publications/right-to-workchecks-employers-guide
          </a>
        </Typography>
      </Box>
    </Box>
  );
}
