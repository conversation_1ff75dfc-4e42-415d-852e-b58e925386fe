.contract-renewal-container {
  background-color: var(--color-white);
  padding: var(--spacing-xxl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-xs);
  .notify-svg {
    path {
      stroke-width: 0px;
    }
  }
  // .search-filter-section {
  //   display: flex;
  //   column-gap: 25px;
  //   align-items: center;
  //   .staff-search {
  //     max-width: calc(100% - 200px) !important;
  //   }
  //   .filter-icon {
  //     border: 1px solid $color-Black;
  //     border-radius: 3px;
  //     width: 25px;
  //     height: 25px;
  //     cursor: pointer;
  //   }
  //   @media (max-width: 799px) {
  //     column-gap: 15px;
  //     // .staff-search {
  //     //   max-width: calc(100% - 200px) !important;
  //     // }
  //   }
  //   @media (max-width: 599px) {
  //     display: flex;
  //     flex-wrap: wrap;
  //     row-gap: 25px;
  //     .staff-search {
  //       width: 85% !important;
  //       max-width: 85% !important;
  //     }
  //     .filter-icon {
  //       max-width: 15%;
  //     }
  //     button {
  //       width: 45%;
  //     }
  //   }
  //   @media (max-width: 599px) {
  //     row-gap: 15px;
  //     button {
  //       width: 100%;
  //     }
  //   }
  // }
}

.contract-renewal-dialogbox {
  .display-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(0, 250px));
    gap: var(--spacing-lg);
    align-items: flex-start;
    width: 100%;
  }
  .space-grid {
    padding-top: var(--spacing-lg);
  }
  .display-grid-50 {
    display: grid;
    grid-template-columns: repeat(
      auto-fit,
      minmax(0, calc(500px + var(--spacing-lg)))
    );
    gap: var(--spacing-lg);
    align-items: flex-start;
    width: 100%;
  }
  .employee-contract-section {
    margin-top: 24px;
    padding-left: 3px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(0, 250px));
    gap: var(--spacing-lg);
    div {
      width: 100%;
    }
    .selected-files {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 250px;
      width: 100%;
      padding: 6px 12px;
      border-radius: 8px;
      margin-bottom: 8px;
      box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;

      svg {
        width: 21px;
        height: 21px;
        cursor: pointer;
      }

      .file-name {
        width: calc(250px - 10px - 24px);

        p {
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden;
          word-break: break-all;
        }
      }
    }
  }
}
