import React, { useEffect, useState } from 'react';
import { Box } from '@mui/material';
import { AgCharts } from 'ag-charts-react';

const LineCharts = ({ item }) => {
  const [options, setOptions] = useState({
    title: {
      text: 'Graphs',
    },
    data: [],
    series: [],
  });
  useEffect(() => {
    if (item && item?.data) {
      setOptions(item);
    }
  }, [item]);
  return (
    <>
      <Box className="">
        <AgCharts options={options} />
      </Box>
    </>
  );
};

export default LineCharts;
