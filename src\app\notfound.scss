@import '@/styles/variable.scss';

.not-found-page-sec {
  background-color: #e8f6ff;
  overflow: auto;
  .not-found-container {
    width: 100%;
    max-width: 1440px;
    margin: 0 auto;
    height: 100vh;
    padding: 100px var(--spacing-base);
    .not-found-page {
      max-height: 701px;
      padding: var(--spacing-6xl) var(--spacing-7xl) var(--spacing-5xl);
      max-width: 650px;
      margin: 0 auto;
      background-color: white;
      box-shadow: 0px 0px 110px 0px #0260e133;
      border-top-left-radius: var(--border-radius-2xl);
      border-top-right-radius: var(--border-radius-2xl);
      .not-found-icon-wrap {
        width: 100%;
        max-width: 407px;
        max-height: 300px;
        margin: 0 auto;
        margin-bottom: var(--spacing-3xl);
        @media (max-width: 599px) {
          margin-bottom: var(--font-size-2xl);
        }
        .not-found-icon {
          height: 100%;
          width: 100%;
        }
      }
      .log-in-btn-wrap {
        .log-in-btn {
          border-radius: var(--border-radius-lg) !important;
          background-color: var(--color-primary) !important;
          font-size: var(--font-size-base) !important;
          line-height: var(--line-height-base) !important;
          font-weight: var(--font-weight-light) !important;
          padding: var(--spacing-xl) !important;
          border: none !important;
          color: var(--text-color-white) !important;
          font-family: var(--font-family-poly-median) !important;

          &:hover {
            font-family: var(--font-family-poly-median) !important;
            background-color: var(--color-primary) !important;
            color: var(--text-color-white) !important;
            box-shadow: none !important;
          }
        }
      }
      .heading-text {
        font-family: var(--font-family-poly-median);
        font-size: var(--font-size-4xl);
        line-height: var(--line-height-sm);
        font-weight: var(--font-weight-semibold);
        color: var(--text-color-primary);
        @media (max-width: 599px) {
          font-size: var(--font-size-3xl);
        }
      }

      .not-found-text {
        font-size: var(--font-size-md);
        line-height: var(--line-height-md);
        font-weight: var(--font-weight-light);
        text-align: center;
        color: var(--color-light-dark);
        padding: var(--spacing-xxl) 0px;
        font-family: var(--font-family-poly-slim) !important;
        letter-spacing: 0px;
      }
      @media (max-width: 767px) {
        padding: var(--spacing-3xl);
      }
      @media (max-width: 575px) {
        padding: var(--spacing-2xl) var(--spacing-base);
      }
    }
  }
}
.not-found-recipe-section {
  height: 100vh;
  width: 100%;
  @media (max-width: 768px) {
    margin-top: 50px;
    height: 100%;
  }
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
