'use client';

import React, { useEffect, useContext, useState } from 'react';
import {
  Box,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
} from '@mui/material';
import AuthContext from '@/helper/authcontext';
import { useRouter, useSearchParams } from 'next/navigation';
import { DateFormat, setApiMessage } from '@/helper/common/commonFunctions';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import { identifiers } from '@/helper/constants/identifier';
import AddChangeRequest from '@/components/ChangeRequest/AddChangeReq';
import EditChangeRequest from '@/components/ChangeRequest/EditChangeReq';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
// import RemoveRedEyeIcon from '@mui/icons-material/RemoveRedEye';
import { fetchFromStorage, saveToStorage } from '@/helper/context/storage';
// import HeaderImage from '@/components/UI/ImageSecurity';
import { getChangeRequestById } from '@/services/changeRequestService';

const ChangeRequestPage = () => {
  const router = useRouter();
  const { userdata, setUserdata } = useContext(AuthContext);

  // const [loader, setLoader] = useState(false);
  const [expanded, setExpanded] = useState(false);
  const [crData, setCrData] = useState();

  const searchParams = useSearchParams();
  const IsEdit = searchParams.get('isEdit');
  const CrId = searchParams.get('id');
  const handleChangeAccordion = (panel) => {
    expanded === panel ? setExpanded() : setExpanded(panel);
  };

  // Change request data by Id
  const getCRData = async () => {
    try {
      const data = await getChangeRequestById(CrId);
      setCrData(data);
    } catch (error) {
      setCrData([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const handleRequestStatus = (status) => {
    return (
      <>
        {status === 'reopened' ? (
          <span className="sub-title-text  status-yellow fw600">Re-opened</span>
        ) : status === 'pending' ? (
          <span className="sub-title-text draft fw600 text-capital">
            {status}
          </span>
        ) : status === 'approved' ? (
          <span className="sub-title-text active-onboarding fw600 text-capital">
            {status}
          </span>
        ) : status === 'closed' ? (
          <span className="sub-title-text closed fw600 text-capital">
            {status}
          </span>
        ) : status === 'rejected' ||
          status === 'deleted' ||
          status === 'cancelled' ? (
          <span className="sub-title-text failed fw600 text-capital">
            {status}
          </span>
        ) : (
          <span className="sub-title-text success fw600 text-capital">
            {status}
          </span>
        )}
      </>
    );
  };
  const oldCR = fetchFromStorage(identifiers?.RedirectData)
    ? fetchFromStorage(identifiers?.RedirectData)
    : userdata;

  useEffect(() => {
    if (oldCR?.IsFromUser === undefined && oldCR?.page !== undefined) {
      setUserdata({ ...oldCR, IsFromUser: true });
      saveToStorage(identifiers?.RedirectData, {
        ...oldCR,
        IsFromUser: true,
      });
    }
  }, [oldCR]);
  useEffect(() => {
    if (CrId) {
      getCRData();
    }
  }, [CrId]);
  return (
    <>
      <Box className="main-page-container change-request">
        <Box className="add-edit-change-request">
          <Box className="d-flex align-center mb8">
            <ArrowBackIosIcon
              className="cursor-pointer"
              onClick={() => {
                setTimeout(() => {
                  router?.push('/change-request');
                }, 600);
              }}
            />
            <Typography className="body-text fw600 pr8">
              {IsEdit ? 'Update Change Request' : 'Add Change Request'}
            </Typography>
          </Box>
          <Divider className="mb16 mt16" />
          {IsEdit ? (
            <EditChangeRequest
              crData={crData}
              handleRequestStatus={handleRequestStatus}
            />
          ) : (
            <AddChangeRequest />
          )}
          {IsEdit && (
            <Box className="pt24">
              <Box className="">
                {crData &&
                  crData?.request_history &&
                  crData?.request_history?.length > 0 &&
                  crData?.request_history?.map((cdata, index) => {
                    return (
                      <Accordion
                        // key={1}
                        elevation={0}
                        className="cr-accordion"
                        expanded={expanded === index}
                        onChange={() => {
                          handleChangeAccordion(index);
                        }}
                      >
                        <AccordionSummary
                          expandIcon={<KeyboardArrowDownIcon />}
                          className="accordion-heading"
                        >
                          <Box className="subject-section">
                            <Typography className="title-sm fw600 subject-text text-ellipsis-line">
                              {cdata?.change_request_subject}
                            </Typography>
                            <Box className="d-flex align-center justify-center h100">
                              {handleRequestStatus(
                                cdata?.change_request_status
                              )}
                            </Box>
                          </Box>
                          <Typography className="title-text fw600 date-text">
                            {cdata?.createdAt
                              ? DateFormat(cdata?.createdAt, 'dates')
                              : ''}
                          </Typography>
                        </AccordionSummary>
                        <AccordionDetails className="">
                          {cdata?.old_data && (
                            <>
                              <Typography className="body-text fw600">
                                Old information
                              </Typography>
                              <Typography className="title-text">
                                {cdata?.old_data
                                  .split('\n')
                                  .map((line, index) => (
                                    <React.Fragment key={index}>
                                      {line}
                                      <br />
                                    </React.Fragment>
                                  ))}
                              </Typography>
                            </>
                          )}
                          {cdata?.new_data && (
                            <>
                              <Typography className="body-text fw600 pt4">
                                New information
                              </Typography>
                              <Typography className="title-text">
                                {cdata?.new_data
                                  .split('\n')
                                  .map((line, index) => (
                                    <React.Fragment key={index}>
                                      {line}
                                      <br />
                                    </React.Fragment>
                                  ))}
                              </Typography>
                            </>
                          )}
                          {/* {cdata &&
                            cdata?.change_request_files &&
                            cdata?.change_request_files?.length > 0 && (
                              <>
                                <Typography className="body-text fw600">
                                  Attached file{' '}
                                </Typography>
                                <Box>
                                  {cdata?.change_request_files?.map((f) => {
                                    const url = crData?.baseUrl + f;
                                    return (
                                      <Box className="selected-files selected-view-files">
                                        {' '}
                                        <Box className="file-name">
                                          <Typography className="title-text text-ellipsis-line">
                                            {f}
                                          </Typography>
                                        </Box>
                                        <RemoveRedEyeIcon
                                          onClick={() => {
                                            window.open(url, '_blank');
                                          }}
                                        />
                                        <HeaderImage
                                          type="url"
                                          imageUrl={url}
                                          Content={<RemoveRedEyeIcon />}
                                          className="d-flex align-center"
                                        />
                                      </Box>
                                    );
                                  })}
                                </Box>
                              </>
                            )} */}

                          {cdata?.change_request_remark && (
                            <>
                              <Typography className="body-text fw600 pt8">
                                Remark
                              </Typography>
                              <Typography className="title-text">
                                {cdata?.change_request_remark}
                              </Typography>
                            </>
                          )}
                        </AccordionDetails>
                      </Accordion>
                    );
                  })}
              </Box>
            </Box>
          )}
        </Box>
      </Box>
    </>
  );
};

export default ChangeRequestPage;
