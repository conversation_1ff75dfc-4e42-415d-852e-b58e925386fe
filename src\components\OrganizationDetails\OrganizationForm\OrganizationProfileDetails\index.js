import React, { useContext } from 'react';
import HeaderImage from '@/components/UI/ImageSecurity';
import { Avatar, Box, IconButton, Tooltip, Typography } from '@mui/material';
import { Business, Edit } from '@mui/icons-material';
import PersonIcon from '@mui/icons-material/Person';
import { useRouter } from 'next/navigation';
import KeyIcon from '@mui/icons-material/Key';
import './organizationprofile.scss';
import AuthContext from '@/helper/authcontext';
export default function OrganizationProfileDetails({
  previewImage,
  selectedUserOption,
  setSelectedUserOption,
  userOptionsList,
  organizationDetails,
  handleLogoChange,
  isAdminProfile,
  fileInputRef,
}) {
  const router = useRouter();
  const { folderdata } = useContext(AuthContext);
  return (
    <Box className="organization-profile-sec d-flex flex-col">
      <Box
        className={`${!isAdminProfile ? 'profile-avatar-section justify-center' : 'admin-profile-sec justify-start'} d-flex`}
      >
        {isAdminProfile || previewImage ? (
          <HeaderImage
            imageUrl={previewImage?.url}
            alt="Profile"
            className={`${!isAdminProfile ? ' profile-avatar ' : ' admin-profile-avatar '}`}
            type="avtar"
          />
        ) : (
          <Avatar className="profile-avatar">
            {isAdminProfile ? <PersonIcon /> : <Business />}
          </Avatar>
        )}
        {isAdminProfile && (
          <Tooltip
            title={<Typography>Change Logo</Typography>}
            arrow
            classes={{ tooltip: 'info-tooltip-container' }}
          >
            <IconButton className="edit-avatar-button" component="label">
              <input
                type="file"
                hidden
                ref={fileInputRef}
                accept="image/*"
                onChange={handleLogoChange}
              />
              <Edit />
            </IconButton>
          </Tooltip>
        )}
      </Box>
      {!isAdminProfile && (
        <>
          <Box className="profile-info text-align">
            <Typography
              variant="h5"
              className="profile-name text-ellipsis-line"
            >
              {organizationDetails?.name || 'Organization Name'}
            </Typography>
            <Typography
              variant="body2"
              className="profile-subtitle text-ellipsis-line"
            >
              {organizationDetails?.website && (
                <a
                  href={organizationDetails?.website}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {organizationDetails?.website}
                </a>
              )}
            </Typography>
          </Box>
          <Box className="d-flex flex-col org-navigation-tabs">
            <Box className="user-details-wrap mt32">
              {userOptionsList?.map((userOption, i) => (
                <Box
                  key={i}
                  className={`userOption-wrap cursor-pointer d-flex ${
                    selectedUserOption === userOption?.id
                      ? 'userOption-active'
                      : ''
                  }`}
                  onClick={() => {
                    router.push(`/org/organization?tab=${userOption?.id}`);
                    setSelectedUserOption(userOption?.id);
                  }}
                >
                  <Typography
                    key={userOption}
                    className={`d-flex align-center gap-sm userOption-text-wrap body-sm-regular cursor-pointer ${
                      selectedUserOption === userOption?.id
                        ? 'userOption-active'
                        : ''
                    }`}
                  >
                    {userOption?.icon}
                    {userOption?.name}
                  </Typography>
                </Box>
              ))}
            </Box>
            <Box className="support-pin-wrap">
              <Box className="d-flex align-start support-pin-details mb16 gap-5">
                <PersonIcon className="key-icon" />
                <Box>
                  <Typography className="support-pin-text body-sm-regular d-flex align-center mb8">
                    Customer ID :
                  </Typography>
                  <Typography className="support-pin-text body-sm-regular">
                    {folderdata?.organizationData?.attributes?.customer_id}
                  </Typography>
                </Box>
              </Box>
              <Box className="d-flex align-start gap-5 support-pin-details">
                <KeyIcon className="key-icon" />
                <Box>
                  <Typography className="support-pin-text body-sm-regular d-flex align-center mb8">
                    Support PIN :
                  </Typography>
                  <Typography className="support-pin body-sm-regular">
                    {folderdata?.organizationData?.attributes?.support_pin}
                  </Typography>
                </Box>
              </Box>
            </Box>
          </Box>
        </>
      )}
    </Box>
  );
}
