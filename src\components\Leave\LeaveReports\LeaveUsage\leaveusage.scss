@import '@/styles/variable.scss';

.leave-usage-sec {
  min-height: calc(100vh - 260px - var(--banner-height));
  .filters-wrap {
    padding-bottom: 20px;
    gap: 10px;
    flex-wrap: wrap;
    .select-box {
      width: 100%;
      max-width: 220px;
      .MuiSelect-select {
        font-size: 14px;
        padding: 2px 7px 3px 15px;
        margin-top: 0px !important;
      }

      fieldset {
        height: 30px !important;
        border-radius: 4px;
        margin-top: 4px !important;
      }

      .MuiSvgIcon-root {
        margin-top: 1px;
      }

      .placeholder {
        margin-top: 2px;
        font-family: Inter, sans-serif;
        font-size: 14px;
        font-weight: 300 !important;
      }
      @media (max-width: 575px) {
        max-width: 100%;
      }
    }
    .apply-clear-btns-wrap {
      gap: 10px;
      .filter-apply-btn {
        min-width: 40px !important;
        padding: 3.5px 0px !important;
        &:hover {
          .MuiSvgIcon-root {
            fill: $color-primary !important;
          }
        }
      }
      .filter-clear-btn {
        min-width: 40px !important;
        padding: 3.5px 0px !important;
        border: 1px solid $color-primary;
        .MuiSvgIcon-root {
          fill: $color-primary !important;
        }
        &:hover {
          .MuiSvgIcon-root {
            fill: $color-White !important;
          }
        }
      }
      @media (max-width: 575px) {
        max-width: 100% !important;
      }
    }
  }
  .usage-table-wrap {
    margin-top: 0px;
    padding: 0px;
    box-shadow: none;
    .MuiDataGrid-root {
      margin-top: 0px;
    }
    .leave-usage-name {
      .leave-name-dot {
        height: 12px;
        width: 12px;
        border-radius: 50px;
      }
    }
  }
}
