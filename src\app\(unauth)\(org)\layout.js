'use client';

import {
  useEffect,
  //  useState
} from 'react';
import { Box } from '@mui/material';
import { useRouter, usePathname } from 'next/navigation';
import { identifiers } from '@/helper/constants/identifier';
import UnAuthHeader from './Header';
import NotificationBanner from '@/components/UI/NotificationBanner';

export default function PublicLayout({ children }) {
  const router = useRouter();
  const pathname = usePathname();
  // const [authdata, setAuthData] = useState(true);
  useEffect(() => {
    if (
      localStorage.getItem(identifiers?.AUTH_DATA) &&
      JSON.parse(localStorage.getItem(identifiers?.AUTH_DATA)) &&
      (pathname === '/login' ||
        pathname === '/otp' ||
        pathname === '/forgot-password' ||
        pathname === '/reset-password' ||
        pathname === '/resetpassword')
    ) {
      router?.push('/chart-dashboard');
    }

    // setAuthData(JSON.parse(localStorage.getItem(identifiers?.AUTH_DATA)));
  }, []);

  return (
    <Box>
      <NotificationBanner />

      {/* {!authdata && ( */}
      <Box className="page-conatiner">
        {pathname !== '/payment-success' ? (
          <UnAuthHeader pathname={pathname} />
        ) : (
          ''
        )}
        {children}
      </Box>
      {/* )} */}
    </Box>
  );
}
