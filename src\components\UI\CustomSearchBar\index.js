'use client';
import React, { useEffect, useState } from 'react';
import { TextField, InputAdornment, Box } from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import './searchbar.scss';
const CustomSearchBar = ({
  searchclass,
  setSearchValue,
  isWidth,
  searchValue,
  onKeyPress,
}) => {
  const [search, setSearch] = useState('');
  const handleSearchChange = (e) => {
    const newValue = e.target.value;
    setSearch(newValue);
    setSearchValue(newValue);
  };
  useEffect(() => {
    if (searchValue || searchValue === '') {
      setSearch(searchValue);
    }
  }, [searchValue]);

  return (
    <Box className="custom-search-bar-sec">
      <TextField
        variant="outlined"
        className={`custom-search-bar ${searchclass}`}
        style={{
          width: isWidth && '100% !important',
          maxWidth: isWidth && '100% !important',
        }}
        placeholder="Search"
        value={search}
        onChange={(e) => setSearch(e.target.value)}
        onBlur={(e) => handleSearchChange(e)}
        onKeyDown={(e) => {
          if (e.key === 'Enter') {
            handleSearchChange(e);
          }
        }}
        onKeyPress={onKeyPress}
        InputProps={{
          startAdornment: (
            <InputAdornment position="end">
              <SearchIcon className="search-icon cursor-pointer" />
            </InputAdornment>
          ),
        }}
      />
      {/* <CustomButton
        variant="contained"
        background="#39596e"
        backgroundhover="#FFFFFF"
        colorhover="#000000"
        className="p14"
        fontWeight="600"
        title="Search"
        fullWidth={false}
        onClick={() => searchdata(search)}
      /> */}
    </Box>
  );
};

export default CustomSearchBar;
