.calender-wrap {
  margin-top: 0px !important;
  background-color: var(--color-white);
  padding: var(--spacing-xxl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-xs);

  .icons-container {
    margin-bottom: 8px;
    .calender-search-wrap {
      width: 100%;
      max-width: 350px;
    }

    @media (max-width: 767px) {
      flex-wrap: wrap;
      row-gap: 10px;
    }
  }
  .more-text-wrap {
    color: var(--text-color-primary);
  }

  .table-container {
    .leave-status-wrap {
      .leave-rejected {
        border-radius: 8px;
        padding: 1.5px 8px;
        text-transform: capitalize;
        border: 1px solid var(--color-danger);
        color: var(--color-danger);
      }
      .leave-draft {
        border-radius: 8px;
        padding: 1.5px 8px;
        text-transform: capitalize;
        color: var(--color-warning);
        border: 1px solid var(--color-warning);
      }
      .leave-success {
        border-radius: 8px;
        padding: 1.5px 8px;
        text-transform: capitalize;
        border: 1px solid var(--color-green);
        color: var(--color-green);
      }
      .leave-cancelled {
        border-radius: 8px;
        padding: 1.5px 8px;
        text-transform: capitalize;
        border: 1px solid var(--color-black);
        color: var(--color-black);
      }
    }
    .MuiDataGrid-root {
      border-radius: 8px;
      margin-top: 20px;
      padding-bottom: 0px;

      .MuiDataGrid-columnHeaders {
        .MuiDataGrid-row--borderBottom {
          background-color: var(--color-off-white);
        }
      }

      .view-icon-wrap {
        @media (max-width: 899px) {
          padding: 0px;
          .action-icon {
            padding: 3px;
          }
        }
      }
    }
    .no-of-days-wrap {
      .days-wrap {
        color: var(--color-primary);
      }
    }
    .no-data-found {
      min-height: calc(100vh - 325px - var(--banner-height));
    }
  }
  .leave-days-wrap {
    color: var(--color-primary);
    &:hover {
      font-weight: 600;
    }
  }
  @media (max-width: 767px) {
    padding: 20px 14px;
  }
}

.deatils-container {
  margin-top: 50px;
  .details-text {
    color: var(--color-dark);
    margin-bottom: 15px;
  }
}

.update-leave-drawer {
  .MuiPaper-root {
    width: 100% !important;
    max-width: 90% !important;
    margin: 10px;
    margin-bottom: 50px !important;
    border-radius: 16px;
    padding: 12px 20px 22px !important;
    max-height: calc(100vh - 20px - var(--banner-height));
    border-radius: 16px;
    &::-webkit-scrollbar {
      display: none !important;
    }
  }
}

.dialog-box {
  .MuiPaper-root {
    padding: 20px 30px;
    .divider {
      margin: 0px;
    }
    .dialog-title-wrap {
      margin-bottom: 15px;
      .dialog_title {
        font-size: 18px !important;
      }
      .MuiSvgIcon-root {
        font-size: 25px !important;
      }
    }
  }
}
