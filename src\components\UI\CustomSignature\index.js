import React, { useState, useRef } from 'react';
import { Box, Typography } from '@mui/material';
import SignaturePad from 'react-signature-canvas';
import CustomButton from '@/components/UI/CustomButton';
import './customsignature.scss';

const CustomSignature = ({
  setSignValue,
  formikRef,
  setSign,
  setSignApi,
  isSetapi,
}) => {
  // const [imageURL, setImageURL] = useState(null);
  const [error, setError] = useState('');
  const sigCanvas = useRef({});

  const clear = () => {
    sigCanvas.current.clear();
    setError('');
  };
  const save = () => {
    if (sigCanvas.current.isEmpty()) {
      setError('This field is required.');
      return;
    }

    // setImageURL(sigCanvas.current.getTrimmedCanvas().toDataURL('image/png'));

    setSignValue({
      url: sigCanvas.current.getTrimmedCanvas().toDataURL('image/png'),
    });
    formikRef.current.setFieldValue(
      'emp_sign',
      sigCanvas.current.getTrimmedCanvas().toDataURL('image/png')
    );
    isSetapi && setSignApi(false);
    setSign(false);
    setError('');
  };
  return (
    <Box>
      <SignaturePad
        ref={sigCanvas}
        canvasProps={{
          className: 'customSignatureCanvas',
        }}
      />
      {error && (
        <Typography variant="body2" color="error" className="field-error">
          {error}
        </Typography>
      )}
      <Box className="form-actions-btn">
        <CustomButton
          type="submit"
          fullWidth
          className=""
          variant="outlined"
          title="Clear"
          onClick={clear}
        />
        <CustomButton
          variant="contained"
          fullWidth
          className=""
          type="submit"
          onClick={save}
          title="Save"
        />
      </Box>
      {/* {imageURL ? (
        <img
          src={imageURL}
          alt="my signature"
          style={{
            display: 'block',
            margin: '0 auto',
            border: '1px solid black',
            width: '150px'
          }}
        />
      ) : null} */}
    </Box>
  );
};

export default CustomSignature;
