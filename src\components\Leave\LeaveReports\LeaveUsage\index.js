'use client';
import { Box, CircularProgress, Typography } from '@mui/material';
import React from 'react';
import { DataGrid, gridClasses } from '@mui/x-data-grid';
// import LeavePopOver from '@/components/Leave/LeavePopOver';
// import CustomSelect from '@/components/UI/selectbox';
// import CustomButton from '@/components/UI/button';
// import ClearOutlinedIcon from '@mui/icons-material/ClearOutlined';
// import FilterListIcon from '@mui/icons-material/FilterList';
import NoDataView from '@/components/UI/NoDataView';
import './leaveusage.scss';

export default function LeaveUsage({
  leaveUsageData,
  loader,
  // usageFilterData,
  // setUsageFilterData,
  // getLeaveUsageList,
  // usageFilterDataApplied,
  // setUsageFilterDataApplied,
}) {
  const columns = [
    {
      field: 'id',
      headerName: 'ID',
      width: 80,
      minWidth: 80,
      flex: 0,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-start h100 gap-sm flex-wrap">
            <Typography>
              <span>{params?.row?.id}</span>
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'name',
      headerName: 'Name',
      width: 200,
      minWidth: 200,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',

      renderCell: (params) => {
        let userData = params?.row;
        return (
          <>
            <Box className="d-flex align-center justify-start h100 leave-usage-name">
              <Box className="d-flex align-center gap-5">
                <Box
                  className={'leave-name-dot'}
                  sx={{
                    backgroundColor: userData?.leave_type_color,
                  }}
                ></Box>
                <Typography className="p14 fw600 cursor-pointer mw100 text-ellipsis pr4">
                  {userData?.name}
                </Typography>
              </Box>
            </Box>
          </>
        );
      },
    },
    {
      field: 'total_allocated_leave',
      headerName: 'Allocated Leaves',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        // let leaveDetails = params?.row?.leave_details?.map((leave) => {
        //   return {
        //     leaveCount: leave?.used_leave?.total,
        //     leaveName: leave?.name,
        //   };
        // });
        return (
          <Box className="d-flex align-center justify-start h100 gap-sm flex-wrap">
            <Box sx={{ boxShadow: 'none' }}>
              <Typography className="p14">
                {/* <Tooltip
                  className="event-title text-ellipsis"
                  variant="h6"
                  interactive
                  title={
                    <LeavePopOver
                      title="Total Leave Days"
                      Leavedays="Leave Type"
                      leave="No of leaves"
                      // startDate={params?.row?.start_date}
                      // endDate={params?.row?.end_date}
                      leaveTypes={params?.row?.total_allocated_leave}
                    />
                  }
                  classes={{
                    tooltip: 'leave-days-popover',
                  }}
                >
                </Tooltip> */}
                {params?.row?.total_allocated_leave}
              </Typography>
            </Box>
          </Box>
        );
      },
    },
    {
      field: 'total_hours_applied',
      headerName: 'Usage (%)',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        // let leaveDetails = params?.row?.leave_details?.map((leave) => {
        //   return {
        //     leaveCount: leave?.used_leave?.total,
        //     leaveName: leave?.name,
        //   };
        // });
        return (
          <Box className="d-flex align-center justify-start h100 gap-sm flex-wrap">
            <Typography className="p14">
              {/* <Tooltip
                className="event-title text-ellipsis"
                variant="h6"
                interactive
                title={
                  <LeavePopOver
                    title="Total Leave Days"
                    Leavedays="Leave Type"
                    leave="No of leaves"
                    startDate={params?.row?.start_date}
                    endDate={params?.row?.end_date}
                    leaveTypes={params?.row?.total_hours_applied}
                  />
                }
                classes={{
                  tooltip: 'leave-days-popover',
                }}
              >
              </Tooltip> */}
              {params?.row?.total_percentage_usage}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'total_percentage_usage',
      headerName: 'Total Hours',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        // let leaveDetails = params?.row?.leave_details?.map((leave) => {
        //   return {
        //     leaveCount: leave?.used_leave?.total,
        //     leaveName: leave?.name,
        //   };
        // });
        return (
          <Box className="d-flex align-center justify-start h100 gap-sm flex-wrap">
            <Typography className="p14">
              {/* <Tooltip
                className="event-title text-ellipsis"
                variant="h6"
                interactive
                title={
                  <LeavePopOver
                    title="Total Leave Days"
                    Leavedays="Leave Type"
                    leave="No of leaves"
                    startDate={params?.row?.start_date}
                    endDate={params?.row?.end_date}
                    leaveTypes={params?.row?.total_percentage_usage}
                  />
                }
                classes={{
                  tooltip: 'leave-days-popover',
                }}
              >
              </Tooltip> */}
              {params?.row?.total_hours_applied}
            </Typography>
          </Box>
        );
      },
    },
  ];
  // const handleFilterData = (type) => {
  //   if (type === 'apply') {
  //     getLeaveUsageList(usageFilterData);
  //     setUsageFilterDataApplied(usageFilterData);
  //   } else {
  //     const clearFilter = {
  //       year: '',
  //     };
  //     setConsuFilterData(clearFilter);
  //     setUsageFilterDataApplied(clearFilter);
  //     getLeaveUsageList(clearFilter);
  //   }
  // };
  return (
    <Box className="leave-usage-sec">
      {/* <Box className="filters-wrap d-flex justify-end align-center">
        <Box className="select-box">
          <CustomSelect
            placeholder="Year"
            options={yearOption}
            value={usageFilterData?.year}
            onChange={(e) => {
              setUsageFilterData({
                ...usageFilterData,
                year: e?.target?.value,
              });
            }}
          />
        </Box>
        <Box className="apply-clear-btns-wrap d-flex align-center">
          <CustomButton
            variant="contained"
            background="#39596e"
            backgroundhover="#FFFFFF"
            colorhover="#000000"
            className="p14 filter-apply-btn"
            fontWeight="600"
            leftIcon={
              <Tooltip title="Apply Filter" arrow>
                <FilterListIcon />
              </Tooltip>
            }
            fullWidth={false}
            onClick={() => {
              setLeaveCunsPage(1);
              handleFilterData('apply');
            }}
          />
          <CustomButton
            className="p14 filter-clear-btn"
            type="submit"
            fontWeight="600"
            variant="contained"
            background="#FFFFFF"
            backgroundhover="#39596e"
            colorhover="#FFFFFF"
            leftIcon={
              <Tooltip title="Clear Filter" arrow>
                <ClearOutlinedIcon />
              </Tooltip>
            }
            onClick={() => {
              handleFilterData('cancel');
            }}
          />
        </Box>
      </Box> */}
      <Box className="table-container table-border-wrap usage-table-wrap">
        {loader ? (
          <Box className="content-loader">
            <CircularProgress className="loader" color="inherit" />
          </Box>
        ) : (
          <>
            {leaveUsageData && leaveUsageData?.length === 0 ? (
              <Box className="no-data d-flex align-center justify-center">
                <NoDataView
                  title="No Leave Usage Records Found"
                  description="There is no leave usage records available at the moment."
                />
              </Box>
            ) : (
              <>
                <DataGrid
                  rows={leaveUsageData}
                  columns={columns}
                  pageSize={100}
                  checkboxSelection={false}
                  disableSelectionOnClick
                  autoHeight
                  rowHeight={40}
                  // getRowHeight={() => 'auto'}
                  sx={{
                    [`& .${gridClasses.cell}`]: {
                      py: 1,
                    },
                  }}
                />
                {/* <CustomPagination
                  currentPage={page}
                  totalCount={totalCount}
                  rowsPerPage={rowsPerPage}
                  onPageChange={onPageChange}
                  OnRowPerPage={OnRowPerPage}
                /> */}
              </>
            )}
          </>
        )}
      </Box>
    </Box>
  );
}
