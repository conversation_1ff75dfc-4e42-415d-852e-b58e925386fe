.add-leave-policy-container {
  .step-container-wrap {
    padding-top: var(--spacing-xxl);
    position: sticky;
    background-color: var(--color-white);
    top: -24px;
    z-index: 1111;
    .step-container {
      border: var(--field-border);
      border-radius: var(--border-radius-xs);
      overflow: auto;
      .steper-wrap {
        // width: 65%;
        padding: 10px;
        // @media (max-width: 1340px) {
        //   width: 75%;
        // }
        // @media (max-width: 1199px) {
        //   width: 100%;
        // }
        // @media (max-width: 991px) {
        //   overflow: auto;
        // }
        .MuiStep-root {
          min-width: 155px;
        }
        .MuiStepConnector-root {
          top: 17px;
        }
        .custom-step-icon {
          color: var(--text-color-white);
          background-color: var(--color-primary);
          border: 1px solid var(--border-color-primary);
          border-radius: 50%;
          width: var(--icon-size-lg);
          height: var(--icon-size-lg);
        }
        .disable-step {
          color: var(--text-color-muted);
          background-color: var(--color-white);
          border: 1px solid var(--border-color-light-gray);
        }
        .MuiStepLabel-label {
          margin-top: var(--spacing-xs);
          .step-label {
            color: var(--text-color-primary);
          }
          .disable-label {
            color: var(--text-color-black);
          }
        }
        .active-step {
          background-color: var(--color-white);
          color: var(--text-color-primary);
        }
      }
    }
  }
  .step-content {
    margin-top: 16px;
  }
}
