.schedule-container {
  // width: 100%;
  overflow-x: auto;
  margin: 20px;
  font-family: var(--font-family-primary);
  max-height: calc(100vh - 240px - var(--banner-height));
  border: var(--normal-sec-border);
}

.schedule-table {
  width: 100%;
  border-collapse: collapse;
  th,
  td {
    border: var(--normal-sec-border);
    padding: 2px 5px;
  }
  th {
    font-weight: 600;
  }
  .date-column {
    min-width: 60px;
    max-width: 60px;
    font-size: 12px;
    height: 40px;
    position: sticky;
    top: -1px;
    background: var(--color-white);
    z-index: 1;
    box-shadow: inset 0px -1px 1px -1px var(--color-black);
    align-content: flex-start;
    &.isCurrentdate {
      color: var(--text-color-primary);
    }
  }
  .current-date {
    border-bottom: 2px solid var(--border-color-primary);
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
  }
  .shift-column {
    text-align: left;
    min-width: 200px;
    max-width: 200px;
    font-size: 14px;
    padding: 5px 10px;
    position: sticky;
    top: -1px;
    left: -1px;
    z-index: 3;
    background: var(--color-white);
    border-bottom: var(--normal-sec-border);
    box-shadow: inset 0px -1px 1px -1px var(--color-black);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-decoration: underline;
    text-decoration-style: dotted;
    text-underline-offset: 2px;
  }
  .shift-details {
    min-width: 200px;
    max-width: 200px;
    height: 104px;
    align-content: flex-start;
    padding: 5px 10px;
    position: sticky;
    left: -1px;
    background: var(--color-white);
    z-index: 2;
    box-shadow: inset -1px 1px 1px -1px var(--color-black);
    .shift-name-container {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      gap: 5px;
      height: fit-content;
      .shift-avatar {
        .MuiAvatar-root {
          background-color: var(--color-light-grayish-blue);
          color: var(--text-light-dark);
          font-weight: 400;
          width: 25px;
          height: 25px;
          font-size: 11.42px;
          font-family: var(--font-family-primary);
        }
      }
      .shift-name {
        font-size: 14px;
        line-height: 20px;
        font-weight: 600;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        text-decoration: underline;
        text-decoration-style: dotted;
        text-underline-offset: 2px;
      }
    }
    .shift-details-wrp {
      margin-left: 30px;
      text-decoration: underline;
      text-decoration-style: dotted;
      text-underline-offset: 2px;
      .shift-total-time-count {
        color: var(--text-color-black);
        font-size: 11.42px;
        line-height: 14px;
        letter-spacing: 0px;
        margin-top: 5px;
      }
      .shift-total-count {
        color: var(--text-light-dark);
        font-size: 11.42px;
        line-height: 14px;
        letter-spacing: 0px;
        margin-top: 5px;
      }
    }
  }
  .shift-cell {
    min-width: 60px;
    max-width: 60px;
    height: 80px;
    background: var(--color-secondary);
    align-content: flex-start;
    .shift-time-role {
      border-left: 2px solid var(--color-light-grayish-blue);
      padding-left: 3px;
      .shift-time {
        color: var(--text-color-black);
        font-size: 11.42px;
        line-height: 14px;
        letter-spacing: 0px;
        font-weight: 600;
      }
      .shift-role {
        color: var(--text-color-slate-gray);
        font-size: 11.42px;
        line-height: 14px;
        letter-spacing: 0px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-weight: 500;
        margin-top: 2px;
      }
    }
    .shift-break {
      width: 13px;
      height: 20px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      svg {
        width: 13px;
        height: 13px;
        fill: var(--icon-color-black);
      }
      .swap-icon {
        fill: var(--icon-color-orange);
        &.isActive {
          fill: var(--icon-color-green);
        }
        &.isRejected {
          fill: var(--icon-bold-red-color);
        }
      }
    }
    .shift-actions {
      .shift-menu-icon {
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 24px;
        margin-top: 5px;
        height: 20px;
        cursor: pointer;
        &:hover {
          background: var(--color-light-grayish-blue);
        }
        svg {
          width: 20px;
          height: 20px;
          fill: var(--icon-color-black);
        }
      }
      .add-shift-icon {
        display: flex;
        justify-content: center;
        align-items: center;
        color: var(--text-color-white);
        padding: 2px 5px;
        margin: 4px 0px;
        height: 70px;
        cursor: pointer;
        &:hover {
          border-radius: 3px;
          border: 1px dashed var(--color-light-grayish-blue);
          background: var(--color-secondary);
        }
        &.isActive {
          border-radius: 3px;
          border: 1px dashed var(--color-light-grayish-blue);
        }
        svg {
          fill: var(--icon-color-black);
        }
      }
      .blank-add-shift-icon {
        cursor: auto;
        &:hover {
          border: none;
          background: transparent;
        }
      }
    }
    .shift-actions-bottom {
      height: 22px;
      margin-top: 5px;
    }
  }
  .user-shift {
    background-color: var(--color-white);
    color: var(--text-color-black);
    padding: 2px 5px;
    border-radius: 3px;
    margin: 4px 0px;
    text-align: left;
    border: var(--normal-sec-border);
    position: relative;
    cursor: pointer;
    .user-shift-actions {
      opacity: 1;
      .user-shift-menu-icon {
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 2px;
        position: absolute;
        top: 3px;
        right: 3px;
        padding: 3px;
        background: var(--color-light-grayish-blue);
        cursor: pointer;
        svg {
          width: 15px;
          height: 15px;
          fill: var(--icon-color-black);
        }
      }
    }
  }
  .shift-unpublished {
    border: 1px dashed var(--color-light-grayish-blue);
    .shift-time-role {
      .shift-time {
        color: var(--color-light-grayish-blue);
      }
      .shift-role {
        color: var(--text-color-slate-gray);
      }
    }
    .shift-break {
      svg {
        fill: var(--color-light-grayish-blue);
      }
    }
  }
  .user-day-off {
    background-image: url('/images/dayoff.svg');
  }
  .availability-view-container {
    .availability-view-wrapper {
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: var(--border-radius-xs);
      padding: var(--spacing-sm);
      margin-bottom: var(--spacing-xs);
      margin-top: var(--spacing-xxs);
      svg {
        width: var(--icon-size-xxs);
        height: var(--icon-size-xxs);
      }
      &.week-view {
        justify-content: left;
        padding: var(--spacing-xs);
        gap: 3px;
        span {
          font-size: var(--font-size-xs);
          line-height: var(--line-height-xs);
        }
      }
      &.available {
        background: var(--color-success-opacity);
        color: var(--color-success);
        border: var(--border-width-xs) var(--border-style-solid)
          var(--border-color-green);
      }
      &.unavailable {
        background: var(--color-danger-opacity);
        color: var(--color-danger);
        border: var(--border-width-xs) var(--border-style-solid)
          var(--border-color-red);
      }
    }
  }
  .user-leave-view-container {
    .user-leave-view-wrapper {
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: var(--border-radius-xs);
      padding: var(--spacing-sm);
      margin-bottom: var(--spacing-xs);
      margin-top: var(--spacing-xxs);
      svg {
        width: var(--icon-size-sm);
        height: var(--icon-size-sm);
      }
      &.holiday {
        background: var(--color-primary-opacity);
        color: var(--color-primary);
        border: var(--border-width-xs) var(--border-style-solid)
          var(--border-color-primary);
      }
      &.sickness {
        background: var(--color-muted-mustard-opacity);
        color: var(--color-muted-mustard);
        border: var(--border-width-xs) var(--border-style-solid)
          var(--border-color-muted-mustard);
      }
    }
  }
}

.schedule-table-week {
  .date-column {
    min-width: 170px;
    max-width: 170px;
    text-align: left;
  }
  .shift-sub-column {
    text-align: left;
    min-width: 200px;
    max-width: 200px;
    font-size: 14px;
    padding: 15px 10px;
    position: sticky;
    top: 38px;
    left: -1px;
    z-index: 3;
    background: var(--color-white);
    border-bottom: var(--normal-sec-border);
    box-shadow: inset 0px -1px 1px -1px var(--color-black);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .shift-details {
    height: 86px;
  }
  .date-sub-column {
    min-width: 60px;
    max-width: 60px;
    font-size: 12px;
    height: 40px;
    position: sticky;
    top: 38px;
    background: var(--color-white);
    z-index: 1;
    box-shadow: inset 0px -1px 1px -1px var(--color-black);
  }
  .shift-sub-summary {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 5px;
    margin-bottom: 5px;
    &:last-child {
      margin-bottom: 0px;
    }
    .shift-sub-title {
      font-size: 14px;
      line-height: 20px;
      color: var(--text-color-slate-gray);
      font-weight: 400;
    }
    .shift-sub-value {
      font-size: 14px;
      line-height: 20px;
      color: var(--text-color-slate-gray);
      font-weight: 500;
    }
  }
  .shift-cell {
    .shift-break {
      width: 100%;
      height: 18px;
      justify-content: space-between;
      margin-top: 2px;
      .shift-role {
        font-size: 12px;
        line-height: 14px;
        margin-top: 0px;
      }
      .shift-break-time {
        display: flex;
        gap: 5px;
        svg {
          width: 15px;
          height: 15px;
        }
        span {
          font-size: 11.42px;
          line-height: 14px;
          color: var(--text-color-slate-gray);
        }
      }
    }
    .shift-actions {
      .add-shift-icon {
        height: 50px;
      }
    }
  }
  .user-shift {
    padding: 5px;
    .shift-time-role {
      .shift-time {
        font-size: 14px;
        line-height: 18px;
      }
    }
  }
}
// rota clandar view
.calendar-event-left-shift-tooltip {
  min-width: 300px !important;
  width: auto !important;
  font-family: var(--font-family-primary) !important;
  padding: 15px !important;
  .tooltip-shift-details {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 15px;
    border-bottom: 1px solid var(--text-color-slate-gray);
    padding: 10px 0px;
    &:last-child {
      border: none;
      padding-top: 10px;
      padding-bottom: 0px;
    }
    .tooltip-shift-title {
      font-size: 14px;
      line-height: 20px;
      color: var(--text-color-slate-gray);
      max-width: 70px;
      width: 100%;
    }
    .tooltip-shift-value {
      font-size: 14px;
      line-height: 20px;
      color: var(--text-color-white);
      // white-space: nowrap;
      // overflow: hidden;
      // text-overflow: ellipsis;
    }
  }
}

.calendar-menu-list-tooltip-custom {
  max-width: 300px;
  width: 100%;
  box-shadow: var(--box-shadow-xs);
  background-color: var(--color-white) !important;
  font-family: var(--font-family-primary) !important;
  color: var(--text-color-black) !important;
  border-radius: 5px !important;
  padding: 5px !important;
  margin: 0px !important;
  max-height: calc(100vh - 200px - var(--banner-height));
  overflow: auto;
  .MuiTooltip-arrow {
    color: var(--text-color-black);
  }
  .calendar-menu-list-cotentiner {
    .calendar-menu-list {
      padding: 10px;
      cursor: pointer;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      gap: 10px;
      &:hover {
        background: var(--color-primary-opacity);
        border-radius: 5px;
      }
      .calendar-menu-list-icon {
        display: flex;
        svg {
          width: 25px;
          height: 25px;
          fill: var(--icon-color-black);
        }
      }
      &.comming-soon {
        cursor: not-allowed;
      }
      &.is-menu-disabled {
        cursor: not-allowed;
        .calendar-menu-list-name {
          color: var(--text-color-slate-gray);
        }
      }
      .calendar-menu-list-name {
        color: var(--text-color-black);
        font-size: 14px;
        line-height: 24px;
        font-weight: 600;
      }
      .calendar-menu-list-sub-name {
        color: var(--text-color-slate-gray);
        font-size: 12px;
        line-height: 20px;
        font-weight: 500;
      }
      .toltip-list-check-box {
        width: 100%;
        .toltip-list-check-box-label {
          margin-left: 0px;
          .check-box {
            padding: 0px;
          }
          .MuiFormControlLabel-label {
            color: var(--text-color-black);
            font-size: 14px;
            line-height: 24px;
            font-weight: 600;
            margin-left: 10px;
          }
        }
      }
    }
    .calendar-menu-tip-container {
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      gap: 10px;
      padding: 10px;
      .calendar-menu-tip-icon {
        display: flex;
        svg {
          width: 25px;
          height: 25px;
          fill: var(--icon-color-slate-gray);
        }
      }
      .calendar-menu-tip-title {
        color: var(--text-color-slate-gray);
        font-size: 12px;
        line-height: 20px;
        font-weight: 600;
      }
      .calendar-menu-tip-text {
        color: var(--text-color-slate-gray);
        font-size: 12px;
        line-height: 20px;
        font-weight: 500;
      }
    }
    .delete-shift-list {
      .calendar-menu-list-icon {
        svg {
          fill: var(--icon-bold-red-color);
        }
      }
      .calendar-menu-list-name {
        color: var(--icon-bold-red-color);
      }
    }
    .calendar-menu-list-title {
      font-size: 14px;
      line-height: 20px;
      color: var(--text-color-slate-gray);
      padding: 10px;
    }
  }
}

// Add shift tooltip
.calendar-rota-add-shift-tooltip-custom {
  max-width: fit-content !important;
  width: 100%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  background-color: var(--color-white) !important;
  font-family: var(--font-family-primary) !important;
  color: var(--text-color-black) !important;
  border-radius: 5px !important;
  padding: 0px !important;
  z-index: 1300;
  .MuiTooltip-arrow {
    color: var(--text-color-white);
    font-size: 20px;
  }
  .calendar-rota-add-shift-tooltip-role {
    font-weight: 600;
    letter-spacing: 0px !important;
  }
  .calendar-rota-add-shift-tooltip-user-details {
    margin-top: 15px;
    .calendar-rota-add-shift-tooltip-location {
      letter-spacing: 0px !important;
      font-style: italic;
    }
    .calendar-rota-add-shift-tooltip-users {
      letter-spacing: 0px !important;
      margin-left: 10px;
      list-style-type: none;
      span {
        font-style: italic;
      }
    }
  }
}

// custom dropdown list
.dropdown-popper {
  z-index: 1300;
  .dropdown-content {
    min-width: 230px;
    max-width: 230px;
    background: var(--color-white);
    border: var(--normal-sec-border);
    border-radius: var(--border-radius-xs);
    box-shadow: var(--box-shadow-xs);
    z-index: 100;
    margin-top: var(--spacing-xsm);

    .search-input-wrapper {
      display: flex;
      align-items: center;
      background: var(--color-white);
      padding: var(--field-padding);
      border-bottom: var(--normal-sec-border);
      border-radius: 5px 5px 0px 0px;
      gap: 5px;
      .search-icon {
        color: var(--icon-color-slate-gray);
        height: var(--field-icon-size);
        width: var(--field-icon-size);
      }
      .search-input {
        width: 100%;
        border: none;
        background: none;
        outline: none;
        font-size: var(--font-size-sm);
        line-height: var(--line-height-xs);
        font-family: var(--font-family-primary);
      }
    }

    ul {
      list-style: none;
      padding: var(--spacing-xs);
      margin: var(--spacing-none);
      max-height: 150px;
      overflow-y: auto;
      li {
        cursor: pointer;
        padding: var(--spacing-xsm);
        font-size: var(--font-size-sm);
        line-height: var(--line-height-xs);
        border-radius: var(--border-radius-xs);
        margin-bottom: var(--spacing-xs);
        color: var(--text-color-black);
        &:hover {
          background: var(--color-secondary);
          color: var(--text-color-black);
        }
        &.selected {
          background: var(--color-primary);
          color: var(--text-color-white);
        }
      }
    }
  }
}

// shift history modal
.shift-history-modal {
  .history-log {
    height: calc(100vh - 300px - var(--banner-height));
    overflow: auto;
  }
}

// highlight row
.highlight-row {
  td {
    background-color: var(--color-success-opacity) !important;
    transition: background-color 0.5s ease;
  }
}

// Group header
.group-header-row {
  border: var(--normal-sec-border);
  .group-header-cell {
    min-width: 200px;
    max-width: 200px;
    align-content: flex-start;
    position: sticky;
    left: -1px;
    padding: var(--spacing-xs);
    z-index: 2;
    box-shadow: inset -1px 1px 1px -1px var(--color-black);
  }
  .group-details-cell {
    align-content: flex-start;
    position: sticky;
    left: 200px;
    border: none;
  }
  .group-header {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: var(--spacing-xs) var(--spacing-md);
    background: var(--color-white);
    gap: 8px;
    .group-name {
      font-family: var(--font-family-primary);
      font-size: var(--font-size-sm);
      line-height: var(--line-height-sm);
      font-weight: var(--font-weight-semibold);
    }
    .group-shift-details {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 10px;
      .group-shift-total-time-count {
        color: var(--text-color-black);
        font-size: var(--font-size-sm);
        line-height: var(--line-height-sm);
        letter-spacing: var(--letter-spacing-normal);
      }
      .group-shift-total-count {
        color: var(--text-color-black);
        font-size: var(--font-size-sm);
        line-height: var(--line-height-sm);
        letter-spacing: var(--letter-spacing-normal);
      }
    }
  }
}
