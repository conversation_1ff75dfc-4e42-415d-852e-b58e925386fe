import React from 'react';
import { Box } from '@mui/material';
import { generateMetadata } from '@/helper/common/commonFunctions';
import CreateCategory from '@/components/DocumentCenter/CreateCategory';

export const metadata = generateMetadata({
  pageTitle: 'Create Category',
});

const CreateCategoryPage = ({ params }) => {
  return (
    <Box className="main-page-container">
      <CreateCategory params={params} />
    </Box>
  );
};

export default CreateCategoryPage;
