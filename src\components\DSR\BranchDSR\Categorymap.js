'use client';

import React, { useState, useEffect } from 'react';
import { Box, Typography, Tooltip } from '@mui/material';
import ControlPointIcon from '@mui/icons-material/ControlPoint';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import ReorderIcon from '@mui/icons-material/Reorder';
import CustomButton from '@/components/UI/CustomButton';
import CustomCheckbox from '@/components/UI/CustomCheckbox';

export default function DSRCategoryMap({
  dsrCatData,
  AddDSR,
  setAddDSR,
  updateBranchActive,
}) {
  const [checkedItems, setCheckedItems] = useState({});
  const [isChecked, setIsCheck] = useState(false);
  const getItemData = async (id, list) => {
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_FIELD_SUB + `${id}`
      );
      if (status === 200) {
        setAddDSR({
          paymentDetails: data?.data,
          id: id,
          title: list?.first_field_value,
          payment_type_usage: 'single',
          IsUpdate: true,
        });
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const getFiledData = async (id, list) => {
    setAddDSR({
      payment_type_title: list?.payment_type_category_title,
      CategoryList: list?.categoryBranchValue,
      id: id,
      payment_type_usage: 'single',
      Reorder: true,
    });
  };
  const updateCheckedState = (item, isChecked) => {
    const newCheckedItems = { ...checkedItems };

    const updateItemAndChildren = (item, checked) => {
      newCheckedItems[item.id] = checked;

      if (item.catList && item.catList.length > 0) {
        item.catList.forEach((child) => updateItemAndChildren(child, checked));
      }
    };

    updateItemAndChildren(item, isChecked);

    const updateParentState = (currentItem) => {
      const parent = findParent(currentItem.id);
      if (parent) {
        const allSiblingsUnchecked = parent.catList.every(
          (sibling) => !newCheckedItems[sibling.id]
        );

        if (allSiblingsUnchecked) {
          newCheckedItems[parent.id] = false;
        } else {
          newCheckedItems[parent.id] = true;
        }

        updateParentState(parent);
      }
    };

    const findParent = (id) => {
      const findParentInTree = (nodes) => {
        for (let node of nodes) {
          if (node.catList && node.catList.find((child) => child.id === id)) {
            return node;
          }
          if (node.catList) {
            const found = findParentInTree(node.catList);
            if (found) return found;
          }
        }
        return null;
      };

      return findParentInTree(dsrCatData);
    };

    updateParentState(item);

    setCheckedItems(newCheckedItems);
  };
  const categoriesView = (
    catData,
    catName,
    isMultiple,
    paymentdata,
    isFirst
  ) => {
    return (
      <ul className={isFirst && 'main-category-ul'}>
        {isMultiple && catData?.length === 0 && (
          <li
            className={`list-view-add ${
              AddDSR?.Add &&
              AddDSR?.payment_type_title &&
              AddDSR?.payment_type_title === catName
                ? 'list-view-selected'
                : ''
            }`}
          >
            <div
              className="list dsr-list add-dsr-category"
              onClick={() =>
                setAddDSR({
                  categoryDetails: paymentdata,
                  payment_type_title: catName,
                  Add: true,
                })
              }
            >
              <ControlPointIcon className="add-icon" />
              <Typography className="title-text text-capitalize fw600">
                Add
              </Typography>
            </div>
          </li>
        )}
        {catData &&
          catData?.map((list, i) => (
            <React.Fragment key={i}>
              {list &&
                !list?.payment_type_usage &&
                !list?.payment_type_category_pattern &&
                i == 0 && (
                  <li
                    className={`list-view-add ${
                      AddDSR?.Add &&
                      AddDSR?.payment_type_title &&
                      AddDSR?.payment_type_title === catName
                        ? 'list-view-selected'
                        : ''
                    }`}
                  >
                    <div
                      className="list dsr-list add-dsr-category"
                      onClick={() =>
                        setAddDSR({
                          categoryDetails: paymentdata,
                          payment_type_title: catName,
                          Add: true,
                        })
                      }
                    >
                      <ControlPointIcon className="add-icon" />
                      <Typography className="title-text text-capitalize fw600">
                        Add
                      </Typography>
                    </div>
                  </li>
                )}
              <li className="list-view">
                <div key={i} className="list dsr-list">
                  <Box className="branch-list-view-text dsr-list-view d-flex gap-sm">
                    <CustomCheckbox
                      checked={!!checkedItems[list?.id]}
                      onChange={(e) => {
                        updateCheckedState(list, e?.target?.checked);
                        setIsCheck(true);
                      }}
                      disableRipple
                      label={
                        <Box>
                          <span
                            className={`title-text text-capitalize d-flex fw600 ${
                              (list?.payment_type_usage ||
                                list?.payment_type_category_pattern) &&
                              'cursor-text'
                            } ${
                              AddDSR?.IsUpdate &&
                              AddDSR?.id &&
                              AddDSR?.id === list?.id &&
                              !list?.payment_type_usage &&
                              !list?.payment_type_category_pattern &&
                              'list-view-selected'
                            }`}
                            onClick={() =>
                              !list?.payment_type_usage &&
                              !list?.payment_type_category_pattern &&
                              getItemData(list?.id, list)
                            }
                          >
                            <span className="">
                              {list?.payment_type_title
                                ? list?.payment_type_title
                                : list?.payment_type_category_title
                                  ? list?.payment_type_category_title
                                  : list?.first_field_value
                                    ? list?.first_field_value
                                    : '-'}
                            </span>
                            {(list?.payment_type_usage === 'income' ||
                              list?.payment_type_usage === 'expense' ||
                              list?.payment_type_usage === 'other') && (
                              <span className="category-status">
                                {list?.payment_type_usage === 'income' ? (
                                  <span className="sub-title-text category-accepted  fw600 text-capital">
                                    {list?.payment_type_usage}
                                  </span>
                                ) : list?.payment_type_usage === 'expense' ? (
                                  <span className="sub-title-text category-draft fw600 text-capital">
                                    {list?.payment_type_usage}
                                  </span>
                                ) : list?.payment_type_usage === 'other' ? (
                                  <span className="sub-title-text category-ongoing fw600 text-capital">
                                    {list?.payment_type_usage}
                                  </span>
                                ) : (
                                  <></>
                                )}
                              </span>
                            )}
                          </span>
                        </Box>
                      }
                    />
                    {list?.categoryBranchValue &&
                      list?.categoryBranchValue?.length > 0 &&
                      list?.payment_type_category_pattern === 'multiple' && (
                        <Tooltip title="Reorder Sub lists">
                          <ReorderIcon
                            className={`reorder-icon ${
                              AddDSR?.Reorder &&
                              AddDSR?.payment_type_title ===
                                list?.payment_type_category_title &&
                              'selected-reorder'
                            } `}
                            onClick={() => {
                              getFiledData(list?.id, list);
                            }}
                          />
                        </Tooltip>
                      )}
                  </Box>

                  <Box className="sign-activity sub-title-text">
                    {categoriesView(
                      list?.payment_type_category
                        ? list?.payment_type_category
                        : list?.categoryBranchValue
                          ? list?.categoryBranchValue
                          : [],
                      list?.payment_type_title
                        ? list?.payment_type_title
                        : list?.payment_type_category_title
                          ? list?.payment_type_category_title
                          : '',
                      list?.payment_type_category_pattern === 'multiple',
                      list
                    )}
                  </Box>
                </div>
              </li>
            </React.Fragment>
          ))}
      </ul>
    );
  };
  const filteredKeys = Object.keys(checkedItems)
    .filter((key) => checkedItems[key] === true)
    .map(Number)
    .filter((key) => !isNaN(key));
  useEffect(() => {
    if (dsrCatData && dsrCatData?.length > 0) {
      const result = {};

      // Helper function to add valid IDs to the result object
      const addValidIds = (item) => {
        if (
          item?.has_default_active === true ||
          item?.has_default_active === 1
        ) {
          result[item.id] = true;
        }
      };
      dsrCatData.forEach((paymentType) => {
        // Check the main object
        addValidIds(paymentType);

        // Check the payment_type_category array
        paymentType?.payment_type_category.forEach((category) => {
          addValidIds(category);

          // Check categoryBranchValue array
          category?.categoryBranchValue.forEach((branchValue) => {
            addValidIds(branchValue);
          });
        });

        // Check the catList array in the main object
        paymentType.catList.forEach((cat) => {
          addValidIds(cat);
        });
      });
      setCheckedItems(result);
    }
  }, [dsrCatData]);

  return (
    <>
      <Box className="dsr-settings-payment-category dsr-branch-settings-payment-category">
        <Box className="d-flex align-center justify-space-between flex-wrap mr8">
          <Typography className="title-sm fw600">DSR Setting</Typography>
          <CustomButton
            title="Save"
            disabled={!isChecked || filteredKeys.length <= 0}
            onClick={() => {
              setIsCheck(false);
              updateBranchActive(filteredKeys);
            }}
          />
        </Box>

        <ul className="dsr-setting-tree">
          {categoriesView(dsrCatData, null, null, null, true)}
        </ul>
      </Box>
    </>
  );
}
