import React from 'react';
import Box from '@mui/material/Box';
import InputLabel from '@mui/material/InputLabel';
// import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
// import Select from '@mui/material/Select';
// import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
// import { Typography } from '@mui/material';
import Select, { components } from 'react-select';
import './selectbox.scss';

const MultiSelect = ({
  label,
  options,
  value,
  onChange,
  className,
  menuPoisition,
  placeholder,
  disabled,
  isHeight,
  isOptionWithColor,
  isAreaManager,
  // placeholder,
  // disabled
}) => {
  const dot = (color = 'transparent') => ({
    alignItems: 'center',
    display: 'flex',

    ':before': {
      backgroundColor: color,
      borderRadius: 10,
      content: '" "',
      display: 'block',
      marginRight: 8,
      height: 10,
      width: 10,
    },
  });
  const dots = (color = 'transparent') => ({
    alignItems: 'center',
    display: 'flex',

    ':before': {
      backgroundColor: color,
      borderRadius: 10,
      content: '" "',
      display: 'block',
      marginLeft: 8,
      height: 10,
      width: 10,
    },
  });
  const customStylesWithColor = {
    menu: (provided) => ({
      ...provided,
      maxHeight: '230px',
      boxShadow: '4px 4px 6px rgba(0, 0, 0, 0.1)',
      border: '1px solid #a6a8b1',
    }),

    menuList: (provided) => ({
      ...provided,
      maxHeight: isHeight ? isHeight : '230px',
    }),
    menuPortal: (base) => ({
      ...base,
      zIndex: 99999,
    }),
    multiValue: (provided, { data }) => ({
      ...provided,
      ...dots(data.color ? data.color : '#39596e'),
    }),
    option: (provided, { data }) => ({
      ...provided,
      ...dot(data.color ? data.color : '#39596e'),
    }),
  };
  const customStyles = {
    menu: (provided) => ({
      ...provided,
      maxHeight: '230px',
      boxShadow: '4px 4px 6px rgba(0, 0, 0, 0.1)',
      border: '1px solid #a6a8b1',
    }),

    menuList: (provided) => ({
      ...provided,
      maxHeight: isHeight ? isHeight : '230px',
    }),
    menuPortal: (base) => ({
      ...base,
      zIndex: 99999,
    }),
  };
  const MultiValueRemove = (props) => {
    // Do not render the remove button for the first option
    const { data, selectProps } = props;
    const firstOptionValue = selectProps.value?.[0]?.value;

    if (data.value === firstOptionValue) {
      return null; // Hide the remove button for the first option
    }

    return <components.MultiValueRemove {...props} />;
  };
  return (
    <Box className={className ? `${className} select-wrap` : 'select-wrap'}>
      <FormControl fullWidth>
        <InputLabel id="custom-select-label">{label}</InputLabel>
        <Select
          className="basic-multi-select"
          classNamePrefix="select"
          menuPlacement={menuPoisition ? 'top' : 'auto'}
          menuPosition="fixed"
          isMulti
          isDisabled={disabled}
          value={value}
          placeholder={placeholder}
          onChange={onChange}
          options={options}
          menuPortalTarget={document.body}
          styles={isOptionWithColor ? customStylesWithColor : customStyles}
          components={isAreaManager ? { MultiValueRemove } : {}}
        />
      </FormControl>
    </Box>
  );
};

export default MultiSelect;
