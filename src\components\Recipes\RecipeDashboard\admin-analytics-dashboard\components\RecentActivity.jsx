'use client';

import React, { useState, useEffect } from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import NoDataView from '@/components/UI/NoDataView';
import './recentactivity.scss';

const RecentActivity = ({ activities: propActivities = [] }) => {
  const [activities, setActivities] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Use API activities directly without transformation
    setIsLoading(true);
    setTimeout(() => {
      setActivities(propActivities);
      setIsLoading(false);
    }, 500);
  }, [propActivities]);

  const getActivityIcon = (type) => {
    const iconMap = {
      recipe_view: 'Eye',
      cta_click: 'MousePointer',
      contact_submission: 'MessageCircle',
      contact_form_submit: 'MessageCircle',
    };
    return iconMap[type] || 'Activity';
  };

  const getActivityColorClass = (type) => {
    const classMap = {
      recipe_view: 'recent-activity__item-icon--recipe-view',
      cta_click: 'recent-activity__item-icon--cta-click',
      contact_submission: 'recent-activity__item-icon--contact-submission',
      contact_form_submit: 'recent-activity__item-icon--contact-submission',
    };
    return classMap[type] || 'recent-activity__item-icon--default';
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now - date) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;

    return date.toLocaleDateString();
  };

  return (
    <div className="recent-activity">
      {/* Header */}
      <div className="recent-activity__header">
        <div className="recent-activity__header-top">
          <h3 className="sub-header-text">Recent Activity</h3>
        </div>
      </div>

      {/* Activity List */}
      <div className="recent-activity__content">
        {isLoading ? (
          <div className="recent-activity__loading">
            {[...Array(5)].map((_, index) => (
              <div key={index} className="recent-activity__loading-item">
                <div className="recent-activity__loading-content">
                  <div className="recent-activity__loading-avatar"></div>
                  <div className="recent-activity__loading-text">
                    <div className="recent-activity__loading-line recent-activity__loading-line--title"></div>
                    <div className="recent-activity__loading-line recent-activity__loading-line--subtitle"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : activities.length === 0 ? (
          <div className="recent-activity__empty-state">
            <NoDataView
              image={<Icon name="BarChart3" size={48} />}
              title="No Recent Activity"
              description="There is no recent activity available at the moment."
              className="no-data-auto-margin-height-conainer"
            />
          </div>
        ) : (
          <div className="recent-activity__list">
            {activities.map((activity, index) => (
              <div
                key={activity.entity_id || index}
                className="recent-activity__item"
              >
                {/* Activity Icon */}
                <div
                  className={`recent-activity__item-icon ${getActivityColorClass(activity.event_type)}`}
                >
                  <Icon name={getActivityIcon(activity.event_type)} size={14} />
                </div>

                {/* Activity Content */}
                <div className="recent-activity__item-content">
                  <div className="recent-activity__item-header">
                    <div className="recent-activity__item-main">
                      {/* Recipe Info */}
                      <div className="recent-activity__user-info">
                        <span className="recent-activity__user-name">
                          {activity.recipe_name || 'Unknown Recipe'}
                        </span>
                      </div>

                      {/* Activity Description */}
                      <p className="recent-activity__description">
                        {activity.activity_description || 'performed action'}
                      </p>
                    </div>

                    {/* Timestamp */}
                    <span className="recent-activity__timestamp">
                      {formatDate(activity.created_at)}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default RecentActivity;
