'use client';

import React, { useState, useContext } from 'react';
import AuthContext from '@/helper/authcontext';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import {
  // Card,
  // CardContent,
  Typography,
  // TextField,
  // Button,
  Box,
  Divider,
} from '@mui/material';
// import CustomTextField from '@/components/UI/CustomTextField';
import CustomButton from '@/components/UI/CustomButton';
// import moment from 'moment';
import './discountcode.scss';

const validationSchema = Yup.object().shape({
  discountCode: Yup.number()
    .typeError('Discount must be a number') // Ensures the input is numeric
    .min(0, 'Discount cannot be less than 0%')
    .max(100, 'Discount cannot exceed 100%')
    .nullable(),
});
export default function DiscountCode({
  planDiscount,
  handleSubmitForm,
  plansList,
}) {
  const { authState } = useContext(AuthContext);
  const [isDiscountApplied, setIsDiscountApplied] = useState(false);
  const [customDiscount, setCustomDiscount] = useState(0); // Store custom discount as a percentage
  const basePrice = planDiscount?.total_amount || 0;
  const transactionCharges = planDiscount?.transaction_fees || 0;
  const vatValue = planDiscount?.total_vat || 0;
  const vatPercentage = planDiscount?.subs_vat_amount || 0;
  // const nextPaymentDate = planDiscount?.expiry_date
  //   ? moment(planDiscount?.expiry_date).format('Do MMMM YYYY')
  //   : moment()
  //       .add(planDiscount?.subs_total_days, 'day')
  //       .format('Do MMMM YYYY') || '';

  const calculateDiscount = (price, discountPercentage) => {
    return (price * discountPercentage) / 100;
  };

  const isUpgradePlan =
    !!plansList?.find(
      (item) =>
        item?.purchased_plan &&
        item?.subs_is_free_trial === 0 &&
        planDiscount?.subs_plan_type !== 'one_time' &&
        item?.subs_plan_category === planDiscount?.subs_plan_category &&
        item?.subs_plan_cost < planDiscount?.subs_plan_cost
    ) || false;

  const isDowngradePlan =
    !!plansList?.find(
      (item) =>
        item?.purchased_plan &&
        item?.subs_is_free_trial === 0 &&
        planDiscount?.subs_plan_type !== 'one_time' &&
        item?.subs_plan_category === planDiscount?.subs_plan_category &&
        item?.subs_plan_cost > planDiscount?.subs_plan_cost
    ) || false;

  const isQueuedPlan =
    !!plansList?.find(
      (item) =>
        item?.purchased_plan &&
        item?.subs_is_free_trial === 0 &&
        planDiscount?.subs_plan_type !== 'one_time' &&
        item?.subs_plan_category === planDiscount?.subs_plan_category &&
        item?.sp_status === 'queued'
    ) || false;

  const isUserLimitExceeded =
    !!plansList?.find(
      (item) =>
        item?.purchased_plan &&
        item?.subs_is_free_trial === 0 &&
        item?.subs_plan_category === planDiscount?.subs_plan_category &&
        item?.sp_status !== 'queued' &&
        item?.total_emp > planDiscount?.subs_limit_max
    ) || false;

  const isUserStorageExceeded =
    !!plansList?.find(
      (item) =>
        item?.purchased_plan &&
        item?.subs_is_free_trial === 0 &&
        item?.subs_plan_category === planDiscount?.subs_plan_category &&
        item?.sp_status !== 'queued' &&
        Number(authState?.subscriptionUsage?.total_size_gb) >
          planDiscount?.subs_limit_max
    ) || false;

  const calculateTotal = () => {
    let total = basePrice; // Start with the base price of the selected plan

    // Apply the custom discount if it exists
    if (isDiscountApplied && customDiscount > 0) {
      total -= calculateDiscount(basePrice, customDiscount); // Apply custom discount
    }

    // Apply the plan discount if it exists
    if (planDiscount?.discount > 0) {
      total -= calculateDiscount(basePrice, planDiscount.discount); // Apply plan discount
    }

    return total; // Return the final total after applying both discounts
  };

  return (
    <Formik
      initialValues={{ discountCode: '' }}
      validationSchema={validationSchema}
      onSubmit={(values, { setFieldValue, resetForm }) => {
        const discountValue = parseFloat(values?.discountCode);
        if (!isNaN(discountValue) && discountValue > 0) {
          setCustomDiscount(discountValue);
          setIsDiscountApplied(true);
          setFieldValue('discountCode', values?.discountCode);
          resetForm();
        } else {
          setCustomDiscount(0);
          setIsDiscountApplied(false);
          resetForm();
        }
      }}
    >
      {() => (
        // {
        //   values,
        //   errors,
        //   touched,
        //   isSubmitting,
        //   handleBlur,
        //   handleChange,
        //   handleSubmit,
        // }
        <Form className="discount-form-wrap">
          {/* <Box className="add-custom-discount">
            <Box className="d-flex gap-10 align-center justify-space-between flex-wrap">
              <Typography
                variant="h6"
                className="sub-header-text discount-code-text"
              >
                Discount Code
              </Typography>
              <Box className="d-flex align-center justify-end gap-10">
                <Box className="coupon-code-input">
                  <CustomTextField
                    fullWidth
                    required
                    name="discountCode"
                    placeholder="Enter Coupon Code"
                    value={values?.discountCode}
                    error={
                      touched?.discountCode && Boolean(errors?.discountCode)
                    }
                    helperText={touched?.discountCode && errors?.discountCode}
                    onBlur={handleBlur}
                    onChange={handleChange}
                  />
                </Box>
                <CustomButton
                  variant="contained"
                  title="Apply"
                  onClick={() => {
                    handleSubmit();
                  }}
                />
              </Box>
            </Box>
          </Box> */}

          {/* {isDiscountApplied && customDiscount > 0 && (
            <Typography component="p" className="custom-applied-discount">
              {customDiscount}% Discount applied
            </Typography>
          )} */}

          {/* {isDiscountApplied &&
            !customDiscount &&
            planDiscount?.discount === 0 && (
              <Typography component="p" className="custom-applied-discount">
                $0% Discount applied
              </Typography>
            )} */}

          <Box className="discount-inner-content-wrap">
            {/* <Box className="team-plan-wrap d-flex justify-space-betweeen">
              <Box className="team-plan-text w100">
                <Typography component="p" className="team-plan-text-wrap">
                  Team Plan
                </Typography>
                <Typography component="p" className="team-details-wrap">
                  5 Users Quarterly
                </Typography>
              </Box>
              <Typography className="base-price-wrap">
                ${basePrice?.toFixed(2)}
              </Typography>
            </Box> */}

            {/* <Divider /> */}
            {/* <Box className="plan-discount-wrap d-flex justify-space-between align-center">
              <Typography component="p" className="title-text team-plan-wrap">
                Discount ({planDiscount?.discount || 0}%)
              </Typography>
              <Typography
                component="p"
                className="discount-number-wrap title-text title-text"
              >
                -$
                {calculateDiscount(
                  basePrice,
                  planDiscount?.discount || 0
                ).toFixed(2)}
              </Typography>
            </Box> */}

            {/* <Divider /> */}

            <Box className="team-plan-wrap d-flex justify-space-betweeen">
              <Box className="team-plan-text w100">
                <Typography
                  component="p"
                  className="team-plan-text-wrap title-text"
                >
                  Transaction charge
                </Typography>
                {/* <Typography component="p" className="team-details-wrap">
                  5 Users Quarterly
                </Typography> */}
              </Box>
              <Typography className="base-price-wrap title-text">
                ${transactionCharges?.toFixed(2)}
              </Typography>
            </Box>
            <Divider />
            <Box className="team-plan-wrap d-flex justify-space-betweeen">
              <Box className="team-plan-text w100">
                <Typography
                  component="p"
                  className="team-plan-text-wrap title-text"
                >
                  VAT {vatPercentage}%
                </Typography>
                {/* <Typography component="p" className="team-details-wrap">
                  5 Users Quarterly
                </Typography> */}
              </Box>
              <Typography className="base-price-wrap title-text">
                ${vatValue?.toFixed(2)}
              </Typography>
            </Box>
            <Divider />

            {customDiscount > 0 && (
              <>
                <Box className="custom-discount d-flex justify-space-between align-center">
                  <Typography
                    component="p"
                    className="discount-text title-text"
                  >
                    Coupon Discount ({customDiscount}%)
                  </Typography>
                  <Typography
                    component="p"
                    className="discount-number title-text"
                  >
                    -${calculateDiscount(basePrice, customDiscount).toFixed(2)}
                  </Typography>
                </Box>
                <Divider />
              </>
            )}

            <Box className="total-wrap">
              <Box className="total d-flex align-center justify-space-between">
                <Typography variant="h6" className="sub-header-text">
                  Total
                </Typography>
                <Typography variant="h6" className="total-number-wrap">
                  ${calculateTotal().toFixed(2)}
                </Typography>
              </Box>
              {planDiscount && (
                <>
                  {isUserLimitExceeded ? (
                    <Typography
                      component="p"
                      className="content-text next-payment-text mt16"
                    >
                      Cannot purchase this plan as your current staff count
                      exceeds the new plan's limit.
                    </Typography>
                  ) : isUserStorageExceeded ? (
                    <Typography
                      component="p"
                      className="content-text next-payment-text mt16"
                    >
                      Cannot purchase this plan because your current storage
                      exceeds the new plan's limit.
                    </Typography>
                  ) : isQueuedPlan ? (
                    <Typography
                      component="p"
                      className="content-text next-payment-text mt16"
                    >
                      Downgrade plan request already queued. Please wait for the
                      current one to finish.
                    </Typography>
                  ) : !isUserStorageExceeded &&
                    !isUserLimitExceeded &&
                    !isQueuedPlan &&
                    isDowngradePlan ? (
                    <Typography
                      component="p"
                      className="content-text next-payment-text mt16"
                    >
                      You've selected to <b>downgrade</b> your plan! <br />
                      Your payment will be charged immediately, but the
                      downgrade will take effect after your current plan ends.
                    </Typography>
                  ) : !isUserStorageExceeded &&
                    !isUserLimitExceeded &&
                    !isQueuedPlan &&
                    isUpgradePlan ? (
                    <Typography
                      component="p"
                      className="content-text next-payment-text mt16 color-green"
                    >
                      You've selected to <b>upgrade</b> your plan! <br />
                      Your payment will be charged immediately, and your
                      upgraded plan will be activated right away!
                    </Typography>
                  ) : (
                    ''
                  )}
                </>
              )}
            </Box>
          </Box>
          <CustomButton
            fullWidth
            variant="contained"
            title={
              isUpgradePlan
                ? 'Upgrade Plan'
                : isDowngradePlan
                  ? 'Downgrade Plan'
                  : 'Pay Now'
            }
            onClick={() => handleSubmitForm()}
            disabled={
              planDiscount === null ||
              planDiscount?.subs_plan_cost === 0 ||
              planDiscount?.purchased_plan ||
              isQueuedPlan ||
              isUserLimitExceeded
            }
          />
        </Form>
      )}
    </Formik>
  );
}
