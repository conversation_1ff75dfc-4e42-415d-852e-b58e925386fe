'use client';

import React, { useContext, useEffect, useState } from 'react';
import {
  Box,
  Checkbox,
  CircularProgress,
  Typography,
  Tooltip,
  FormControlLabel,
  FormGroup,
} from '@mui/material';
import CustomButton from '@/components/UI/CustomButton';
import { setApiMessage, DateFormat } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import LaunchIcon from '@mui/icons-material/Launch';
import HeaderImage from '@/components/UI/ImageSecurity';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined';
import DialogBox from '@/components/UI/Modalbox';
import DeleteModal from '@/components/UI/DeleteModal';
import AuthContext from '@/helper/authcontext';
import AddIcon from '@mui/icons-material/Add';
import CustomSearch from '@/components/UI/CustomSearch';
import AddLetters from './AddLetter';
import InfoIcon from '@mui/icons-material/Info';
import SaveAltIcon from '@mui/icons-material/SaveAlt';
import MoveToInboxIcon from '@mui/icons-material/MoveToInbox';
import EventAvailableIcon from '@mui/icons-material/EventAvailable';
import PersonIcon from '@mui/icons-material/Person';
import CustomPagination from '@/components/UI/customPagination';
import NoDataView from '@/components/UI/NoDataView';
import './sideletter.scss';

const SideLetter = ({ UserId, isMyProfile }) => {
  const [loader, setLoader] = useState(false);
  const [templateData, setTemplateData] = useState([]);
  const [selectedContracts, setSelectedContracts] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState('');
  const [rowsPerPage, setRowsPerPage] = useState(12);
  const [deleteConfirmation, setDeleteConfirmation] = useState(false);
  const { authState } = useContext(AuthContext);
  const [searchValue, setSearchValue] = useState('');
  const [createModal, setCreateModal] = useState(false);
  // const [readLetter, setReadLetter] = useState(false);
  const isAdmins = [1, 2, 3, 4, 6, 7, 14].includes(
    authState?.web_user_active_role_id
  );
  const isDeleteAdmin = authState?.web_user_active_role_id === 1;
  // GET TEMPLATE DETAILS
  const getTemplateDetails = async (page, search, rpp) => {
    // templateData?.length === 0 &&
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_SLIDER_LIST +
          `?search=${search}&recipient_user_id=${UserId}&page=${page}&size=${rpp ? rpp : rowsPerPage}`
      );

      if (status === 200) {
        const tempData = data?.data;
        tempData && tempData?.length > 0
          ? setTemplateData(tempData)
          : setTemplateData([]);
        // setHasMore(tempData?.length > 0);
        data?.count ? setTotalCount(data?.count) : setTotalCount(0);
        setLoader(false);
        // setTemplateData(tempData);
      }
    } catch (error) {
      setLoader(false);
      setTemplateData('');
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const handleDeleteCloseModal = () => {
    setDeleteConfirmation(false);
  };

  const deleteUserContract = async () => {
    // templateData?.length === 0 &&
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.delete(
        URLS?.DELETE_SLIDE_LETTER + `?id=${selectedContracts}`
      );

      if (status === 200) {
        if (templateData?.length === 1 && currentPage !== 1) {
          getTemplateDetails(Number(currentPage) - 1, searchValue);
          setCurrentPage(Number(currentPage) - 1);
        } else {
          getTemplateDetails(1, searchValue);
        }
        setSelectedContracts([]); // Reset selection
        setApiMessage('success', data?.message);
        setLoader(false);
      }
    } catch (error) {
      setLoader(false);
      // setTemplateData([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const UpdateConfirmations = async (ID, statuss) => {
    // templateData?.length === 0 &&
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.put(
        URLS?.UPDATE_SLIDE_STATUS + `${ID}?confirmation_status=${statuss}`
      );

      if (status === 200) {
        let tmp = templateData?.map((item) =>
          item?.id === ID
            ? {
                ...item,
                side_letter_confirmation_status: 'completed',
                side_letter_confirmation_date: Date(),
                isconfim: true,
              }
            : item
        );
        setTemplateData(tmp);
        setApiMessage('success', data?.message);
        setLoader(false);
      }
    } catch (error) {
      setLoader(false);
      // setTemplateData([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      setCurrentPage(1);
      getTemplateDetails(1, searchValue);
    }
  };
  const handleDeleteUserContart = () => {
    deleteUserContract();
    setDeleteConfirmation(false);
  };

  const handleCheckboxChange = (contarcts) => {
    setSelectedContracts((prevSelected) =>
      prevSelected.includes(contarcts?.id)
        ? prevSelected?.filter((contractId) => contractId !== contarcts?.id)
        : [...prevSelected, contarcts?.id]
    );
  };

  const download = async (contract_link, name) => {
    const response = await fetch(contract_link);
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', name);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };
  const handleClose = () => {
    setCreateModal(false);
  };
  const onPageChange = (newPage) => {
    setCurrentPage(newPage);
    getTemplateDetails(newPage, searchValue);
  };
  const OnRowPerPage = (newPage) => {
    setRowsPerPage(newPage);
    setCurrentPage(1);
    getTemplateDetails(1, searchValue, newPage);
  };
  useEffect(() => {
    getTemplateDetails(1, '');
  }, []);
  const unsupportedExtensions = [
    'doc',
    'docx',
    'xls',
    'xlsx',
    'ppt',
    'pptx',
    'csv',
    'txt',
  ];
  const getFileExtension = (filename) => {
    return filename?.split('.').pop().toLowerCase();
  };
  const canPreviewInBrowser = (filename) => {
    const ext = getFileExtension(filename);
    return !unsupportedExtensions.includes(ext);
  };
  return (
    <>
      <Box className="side-letter-page">
        <Box className="delete-wrap d-flex align-center justify-space-between pb24 flex-wrap gap-10">
          <Typography className="title-text">
            Total letters : {totalCount}
          </Typography>
          <Box className="d-flex align-center gap-10 justify-end search-section">
            <CustomSearch
              setSearchValue={setSearchValue}
              onKeyPress={handleKeyPress}
              searchValue={searchValue}
            />

            <Box className="select-delete-wrap">
              <CustomButton
                variant="outlined"
                title={'Search'}
                onClick={() => {
                  setCurrentPage(1);
                  getTemplateDetails(1, searchValue);
                }}
              />
              {isAdmins && !isMyProfile ? (
                <>
                  <Box>
                    <CustomButton
                      isIconOnly
                      startIcon={
                        <Tooltip
                          title={
                            <Typography className="sub-title-text">
                              Apply Filter
                            </Typography>
                          }
                          classes={{
                            tooltip: 'info-tooltip-container',
                          }}
                          arrow
                        >
                          <AddIcon />
                        </Tooltip>
                      }
                      onClick={() => {
                        setCreateModal(true);
                      }}
                    />
                  </Box>
                </>
              ) : (
                <></>
              )}
              {isDeleteAdmin && templateData && templateData?.length !== 0 ? (
                <>
                  <CustomButton
                    variant="contained"
                    className="delete-button"
                    startIcon={<DeleteOutlineOutlinedIcon />}
                    title={'Delete'}
                    disabled={selectedContracts?.length === 0}
                    onClick={() => setDeleteConfirmation(true)}
                  />
                </>
              ) : (
                <></>
              )}
            </Box>
          </Box>
        </Box>
        {loader ? (
          <Box className="content-loader pt16">
            <CircularProgress className="loader" color="inherit" />
          </Box>
        ) : templateData && templateData?.length === 0 ? (
          <Box className="no-data d-flex align-center justify-center">
            <NoDataView
              title="No Side Letter Records Found"
              description="There is no Side Letter data available at the moment."
            />
          </Box>
        ) : (
          <Box>
            <Box className="employee-contract-checklist contract-history-container ">
              {templateData &&
                templateData?.length > 0 &&
                templateData?.map((item, i) => {
                  return (
                    <Box className="contract-history-wrap h100" key={i}>
                      <Box className="title-section">
                        <Box>
                          <Box className="d-flex align-center contract-name">
                            <HeaderImage
                              type="url"
                              imageUrl={
                                item?.side_letter_confirmation_items?.[0]
                                  ?.item_image_url
                              }
                              pagetitle={
                                item?.side_letter_confirmation_items?.[0]
                                  ?.item_image_name
                              }
                              Content={
                                <Typography
                                  className="title-text color-green cursor-pointer "
                                  onClick={() => {
                                    item?.side_letter_confirmation_status ===
                                      'pending' &&
                                      isMyProfile &&
                                      !item?.has_confirmation_required &&
                                      UpdateConfirmations(
                                        item?.id,
                                        item?.has_confirmation_required
                                      );
                                  }}
                                >
                                  {item?.side_letter_title}
                                </Typography>
                              }
                            />
                            {item?.side_letter_description && !isMyProfile ? (
                              <Tooltip
                                classes={{ tooltip: 'info-tooltip-container' }}
                                title={
                                  <Box className="d-flex align-center tooltip-text ">
                                    <p className="sub-title-text">
                                      {' '}
                                      {item?.side_letter_description}
                                    </p>
                                  </Box>
                                }
                              >
                                <InfoIcon className="info-icon cursor-pointer" />
                              </Tooltip>
                            ) : (
                              <></>
                            )}
                          </Box>
                          <Box className="d-flex align-center">
                            <Tooltip
                              title={<Typography>Assigned on</Typography>}
                              placement="bottom"
                              classes={{ tooltip: 'info-tooltip-container' }}
                            >
                              <MoveToInboxIcon className="created-date cursor-pointer" />
                            </Tooltip>
                            <Typography className="title-text user-date">
                              {DateFormat(item?.createdAt, 'datesWithhourUTC')}
                            </Typography>
                          </Box>
                          {item?.side_letter_confirmation_date ? (
                            <Box className="d-flex align-center pt4 confimation-dates">
                              <Tooltip
                                title={<Typography>Completed on</Typography>}
                                placement="bottom"
                                classes={{ tooltip: 'info-tooltip-container' }}
                              >
                                <EventAvailableIcon className="created-date cursor-pointer" />
                              </Tooltip>
                              <Typography className="title-text user-date">
                                {item?.isconfim
                                  ? DateFormat(
                                      item?.side_letter_confirmation_date,
                                      'datesWithhour'
                                    )
                                  : item?.side_letter_confirmation_date
                                    ? DateFormat(
                                        item?.side_letter_confirmation_date,
                                        'datesWithhourUTC'
                                      )
                                    : '-'}
                              </Typography>
                            </Box>
                          ) : (
                            <></>
                          )}

                          <Box className="d-flex align-center">
                            <Tooltip
                              title={<Typography>Assign by</Typography>}
                              placement="bottom"
                              classes={{ tooltip: 'info-tooltip-container' }}
                            >
                              <PersonIcon className="created-date cursor-pointer" />
                            </Tooltip>
                            <Typography className="title-text user-date">
                              {item?.sender_user?.user_full_name}
                            </Typography>
                          </Box>
                        </Box>
                        <Box className="right-side-letter h100">
                          <Box className="d-flex justify-end">
                            {item?.side_letter_confirmation_status ===
                            'pending' ? (
                              <Typography className="sub-title-text status-yellow fw600">
                                Pending
                              </Typography>
                            ) : item?.side_letter_confirmation_status ===
                              'completed' ? (
                              <Typography className="sub-title-text active-onboarding fw600">
                                {item?.side_letter_confirmation_status}
                              </Typography>
                            ) : (
                              '-'
                            )}
                          </Box>
                          <Box className="donload-checked">
                            <Box className="d-flex align-end gap-sm">
                              <Tooltip
                                arrow
                                title="Download"
                                placement="bottom"
                                classes={{ tooltip: 'info-tooltip-container' }}
                              >
                                <SaveAltIcon
                                  className="download-icon cursor-pointer"
                                  onClick={() => {
                                    item?.side_letter_confirmation_items?.[0]
                                      ?.item_image_url &&
                                      download(
                                        item
                                          ?.side_letter_confirmation_items?.[0]
                                          ?.item_image_url,
                                        item
                                          ?.side_letter_confirmation_items?.[0]
                                          ?.item_image_name
                                      );
                                  }}
                                />
                              </Tooltip>
                              <Tooltip
                                arrow
                                title="View"
                                placement="bottom"
                                classes={{ tooltip: 'info-tooltip-container' }}
                              >
                                <HeaderImage
                                  type="url"
                                  imageUrl={
                                    item?.side_letter_confirmation_items?.[0]
                                      ?.item_image_url
                                  }
                                  className="view-icon-sec"
                                  pagetitle={
                                    item?.side_letter_confirmation_items?.[0]
                                      ?.item_image_name
                                  }
                                  Content={
                                    <LaunchIcon
                                      className="view-icon cursor-pointer"
                                      onClick={() => {
                                        item?.side_letter_confirmation_status ===
                                          'pending' &&
                                          isMyProfile &&
                                          !item?.has_confirmation_required &&
                                          UpdateConfirmations(
                                            item?.id,
                                            item?.has_confirmation_required
                                          );
                                      }}
                                    />
                                  }
                                />
                              </Tooltip>

                              {isDeleteAdmin ? (
                                <Box className="action-sec d-flex justify-space-between">
                                  <Checkbox
                                    className="inactive-check-box"
                                    icon={<CheckBoxOutlineBlankIcon />}
                                    checkedIcon={<CheckBoxIcon />}
                                    checked={selectedContracts?.includes(
                                      item?.id
                                    )}
                                    onChange={() => handleCheckboxChange(item)}
                                    disableRipple
                                  />
                                </Box>
                              ) : (
                                <></>
                              )}
                            </Box>
                          </Box>
                        </Box>
                      </Box>
                      {(!isMyProfile && isAdmins) ||
                      !item?.has_confirmation_required ||
                      item?.side_letter_confirmation_status === 'completed' ? (
                        <></>
                      ) : (
                        <FormGroup className="check-read-confirmation">
                          <FormControlLabel
                            control={
                              <Checkbox
                                className="check-box "
                                icon={
                                  <CheckBoxOutlineBlankIcon className="uncheck-icon" />
                                }
                                checkedIcon={
                                  <CheckBoxIcon className="check-icon" />
                                }
                                disableRipple
                              />
                            }
                            name="requireCheck"
                            className="check-box-form title-text"
                            onChange={() => {
                              UpdateConfirmations(
                                item?.id,
                                item?.has_confirmation_required
                              );
                            }}
                            label="I confirm I have read and understood this letter."
                          />
                        </FormGroup>
                      )}
                      {!canPreviewInBrowser(
                        item?.side_letter_confirmation_items?.[0]
                          ?.item_image_name
                      ) ? (
                        <Typography className="sub-title-text pt8 fw500">
                          This file type cannot be previewed. Your file will be
                          downloaded instead.
                        </Typography>
                      ) : (
                        <></>
                      )}
                    </Box>
                  );
                })}
            </Box>
          </Box>
        )}
        {templateData && templateData?.length === 0 && currentPage === 1 ? (
          <></>
        ) : (
          <>
            <CustomPagination
              className="bt0"
              currentPage={currentPage}
              totalCount={totalCount}
              rowsPerPage={rowsPerPage}
              onPageChange={onPageChange}
              OnRowPerPage={OnRowPerPage}
              isInvited={true}
            />
          </>
        )}
      </Box>
      <DialogBox
        open={deleteConfirmation}
        handleClose={() => {
          handleDeleteCloseModal();
        }}
        title={'Confirmation'}
        className="delete-modal"
        dividerClass="delete-modal-divider"
        content={
          <>
            <DeleteModal
              handleCancel={handleDeleteCloseModal}
              handleConfirm={handleDeleteUserContart}
              text="Are you sure you want to delete this? This action cannot be undone."
            />
          </>
        }
      />
      <DialogBox
        open={createModal}
        handleClose={() => {
          handleClose();
        }}
        title={'Create New Letter'}
        content={
          <>
            <AddLetters
              setCreateModal={setCreateModal}
              UserId={UserId}
              getTemplateDetails={getTemplateDetails}
              currentPage={currentPage}
              searchValue={searchValue}
            />
          </>
        }
      />
    </>
  );
};

export default SideLetter;
