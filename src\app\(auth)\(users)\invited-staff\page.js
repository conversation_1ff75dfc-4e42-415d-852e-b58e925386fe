import React from 'react';
import { Box } from '@mui/material';
import { generateMetadata } from '@/helper/common/commonFunctions';
import InvitedStaff from '@/components/Users/<USER>';

export const metadata = generateMetadata({
  pageTitle: 'Invited Staff',
});

const StaffListPage = ({ params }) => {
  return (
    <Box className="main-page-container">
      <InvitedStaff params={params} />
    </Box>
  );
};

export default StaffListPage;
