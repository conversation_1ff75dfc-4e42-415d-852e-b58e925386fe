import React from 'react';
import CustomCheckbox from '@/components/UI/CustomCheckbox';
import CustomSelect from '@/components/UI/CustomSelect';
import CustomTextField from '@/components/UI/CustomTextField';
import { Box, Tooltip, Typography } from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import { identifiers } from '@/helper/constants/identifier';
import {
  dayOptions,
  monthsDays,
  monthsOption,
} from '@/helper/common/commonFunctions';
import CustomRadio from '@/components/UI/CustomRadio';
import CustomDropdown from '../CustomDropdown';
import CustomButton from '@/components/UI/CustomButton';
import AddIcon from '@mui/icons-material/Add';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import EncashmentSection from './EncashmentSection';
import CarryForwardSection from './CarryForwardSection';
export default function ResetSection({
  values,
  handleChange,
  handleBlur,
  touched,
  errors,
  setFieldValue,
  leavePolicySubmit,
  setLeavePolicySubmit,
  Inputype,
  isAnuual,
  resetTime,
  setResetTime,
  encashment,
  setRandom,
  setEncashment,
  leaveType,
}) {
  return (
    <>
      <Box className="d-flex gap-10 mt8 flex-wrap">
        <Box>
          <CustomSelect
            id="resetTimePeriod"
            name="resetTimePeriod"
            placeholder="Time Period"
            className="leave-select-wrap"
            options={identifiers?.TIME_PERIOD_OPTIONS?.filter((opt) => {
              return opt?.value !== 'one_time' && opt?.value !== 'half_yearly';
            })}
            value={
              identifiers?.TIME_PERIOD_OPTIONS?.find((opt) => {
                return opt?.value === values.resetTimePeriod;
              }) || ''
            }
            onChange={(e) => {
              setFieldValue('resetTimePeriod', e?.value);
              let resetTimes = resetTime;
              if (e?.value === 'yearly' || e?.value === 'one_time') {
                resetTimes.type = e?.value;
                resetTimes.reset_value = [
                  {
                    on_date: 1,
                    month: 1,
                  },
                ];
              } else if (e?.value === 'monthly') {
                resetTimes.type = e?.value;
                const montharray = monthsDays.map((month, index) => ({
                  on_date: 1,
                  month: index + 1,
                }));
                resetTimes.reset_value = montharray;
              } else if (e?.value === 'quarterly') {
                const montharray = [
                  {
                    on_date: 1,
                    month: 1,
                    months: '1,2,3',
                  },
                  {
                    on_date: 1,
                    month: 4,
                    months: '4,5,6',
                  },
                  {
                    on_date: 1,
                    month: 7,
                    months: '7,8,9',
                  },
                  {
                    on_date: 1,
                    month: 10,
                    months: '10,11,12',
                  },
                ];
                resetTimes.type = e?.value;
                resetTimes.reset_value = montharray;
              } else if (e?.value === 'half_yearly') {
                const montharray = [
                  {
                    on_date: 1,
                    month: 1,
                  },
                  {
                    on_date: 1,
                    month: 7,
                  },
                ];
                resetTimes.type = e?.value;
                resetTimes.reset_value = montharray;
              }
              setResetTime(resetTimes);
              setLeavePolicySubmit({
                ...leavePolicySubmit,
                leaveAccrual: false,
              });
            }}
            error={Boolean(touched?.resetTimePeriod && errors?.resetTimePeriod)}
            helperText={touched?.resetTimePeriod && errors?.resetTimePeriod}
            isClearable={false}
            isDisabled={values?.empcontract && isAnuual === 'true'}
          />
        </Box>
        <Box>
          {(values?.resetTimePeriod === 'yearly' ||
            values?.resetTimePeriod === 'one_time') && (
            <Box className="d-flex align-center gap-10 flex-wrap">
              <Typography className="title-text">On the</Typography>
              <Box>
                <CustomSelect
                  id="resetDays"
                  name="resetDays"
                  placeholder="Last Day"
                  className="leave-select-wrap"
                  options={dayOptions}
                  value={
                    dayOptions?.find((opt) => {
                      return (
                        opt?.value === resetTime?.reset_value?.[0]?.on_date
                      );
                    }) || ''
                  }
                  onChange={(e) => {
                    const resetTimes = resetTime;
                    resetTimes.reset_value[0].on_date = e?.value;
                    setResetTime(resetTimes);
                    setLeavePolicySubmit({
                      ...leavePolicySubmit,
                      leaveAccrual: false,
                    });
                  }}
                  error={Boolean(touched?.resetDays && errors?.resetDays)}
                  helperText={touched?.resetDays && errors?.resetDays}
                  isClearable={false}
                />
              </Box>
              <Box>
                <CustomSelect
                  id="resetLeaveMonth"
                  name="resetLeaveMonth"
                  className="leave-select-wrap"
                  placeholder="Month"
                  options={monthsOption}
                  value={
                    monthsOption?.find((opt) => {
                      return opt?.value === resetTime?.reset_value?.[0]?.month;
                    }) || ''
                  }
                  onChange={(e) => {
                    const resetTimes = resetTime;
                    resetTimes.reset_value[0].month = e?.value;
                    setResetTime(resetTimes);
                    setLeavePolicySubmit({
                      ...leavePolicySubmit,
                      leaveAccrual: false,
                    });
                  }}
                  error={Boolean(touched?.resetMonths && errors?.resetMonths)}
                  helperText={touched?.resetMonths && errors?.resetMonths}
                  isClearable={false}
                />
              </Box>
            </Box>
          )}

          {values?.resetTimePeriod === 'monthly' &&
            resetTime &&
            resetTime?.reset_value &&
            resetTime?.reset_value?.length > 0 &&
            resetTime?.reset_value?.map((month, index) => (
              <Box
                className="d-flex align-center justify-space-between gap-10 mb8"
                key={index}
              >
                <CustomDropdown
                  type="reset"
                  months={month}
                  dayOptions={dayOptions}
                  resetTime={resetTime}
                  setResetTime={setResetTime}
                  monthOptions={monthsOption}
                  onChange={() => {
                    setLeavePolicySubmit({
                      ...leavePolicySubmit,
                      leaveAccrual: false,
                    });
                  }}
                  index={index}
                  leaveType={leaveType}
                />
              </Box>
            ))}

          {values?.resetTimePeriod === 'quarterly' &&
            resetTime &&
            resetTime?.reset_value &&
            resetTime?.reset_value?.length > 0 &&
            resetTime?.reset_value?.map((quarter, index) => {
              const startIndex = [0, 3, 6, 9][index];
              const endIndex = [3, 6, 9, 12][index];
              return (
                <Box
                  className="d-flex align-center justify-space-between gap-10 mb8"
                  key={index}
                >
                  <CustomDropdown
                    type="reset"
                    month={{
                      value: monthsOption.slice(startIndex, endIndex),
                    }}
                    quarter={true}
                    months={quarter}
                    resetTime={resetTime}
                    setResetTime={setResetTime}
                    dayOptions={dayOptions}
                    monthOptions={monthsOption.slice(startIndex, endIndex)}
                    index={index}
                    onChange={() => {
                      setLeavePolicySubmit({
                        ...leavePolicySubmit,
                        leaveAccrual: false,
                      });
                    }}
                    leaveType={leaveType}
                  />
                </Box>
              );
            })}

          {values?.resetTimePeriod === 'half_yearly' &&
            resetTime &&
            resetTime?.reset_value &&
            resetTime?.reset_value?.length > 0 &&
            resetTime?.reset_value?.map((half, index) => {
              const startIndex = index === 0 ? 0 : 6;
              const endIndex = index === 0 ? 6 : 12;
              return (
                <Box
                  className="d-flex align-center justify-space-between gap-10 mb8"
                  key={index}
                >
                  <CustomDropdown
                    type="reset"
                    months={half}
                    quarter={true}
                    month={{
                      value: monthsOption.slice(startIndex, endIndex),
                    }}
                    resetTime={resetTime}
                    setResetTime={setResetTime}
                    index={index}
                    dayOptions={dayOptions}
                    monthOptions={monthsOption.slice(startIndex, endIndex)}
                    onChange={() => {
                      setLeavePolicySubmit({
                        ...leavePolicySubmit,
                        leaveAccrual: false,
                      });
                    }}
                    leaveType={leaveType}
                  />
                </Box>
              );
            })}
        </Box>
      </Box>
      <Box className="mt16">
        <Typography className="sub-content-text">
          Prioritize between Encashment and Carry Forward of Leaves?
        </Typography>
        <Box className="mt4">
          <Box>
            <CustomRadio
              name="encashOrCarryForward"
              checked={values?.encashOrCarryForward === 'encashment'}
              onChange={() => {
                setFieldValue(
                  'encashOrCarryForward',
                  values?.encashOrCarryForward === 'encashment'
                    ? ''
                    : 'encashment'
                );
                setLeavePolicySubmit({
                  ...leavePolicySubmit,
                  leaveAccrual: false,
                });
              }}
              disableRipple
              label={
                <Typography className="sub-content-text">Encashment</Typography>
              }
            />
          </Box>
          <Box>
            <CustomRadio
              name="encashOrCarryForward"
              checked={values?.encashOrCarryForward === 'carry_forward'}
              onChange={() => {
                setFieldValue(
                  'encashOrCarryForward',
                  values?.encashOrCarryForward === 'carry_forward'
                    ? ''
                    : 'carry_forward'
                );
                setLeavePolicySubmit({
                  ...leavePolicySubmit,
                  leaveAccrual: false,
                });
              }}
              disableRipple
              label={
                <Typography className="sub-content-text">
                  Carry Forward
                </Typography>
              }
            />
          </Box>
        </Box>
        <Box className="mt8">
          <CustomCheckbox
            checked={values?.deductEncashAmount}
            onChange={(e) => {
              setFieldValue('deductEncashAmount', e?.target?.checked);
              setLeavePolicySubmit({
                ...leavePolicySubmit,
                leaveAccrual: false,
              });
            }}
            disableRipple
            label={
              <Typography className="sub-content-text">
                {values?.encashOrCarryForward === 'encashment'
                  ? 'Deduct Encashmnet Amount'
                  : 'Deduct Carry Forward Amount'}
              </Typography>
            }
          />
        </Box>
      </Box>
      <Box className="mt16">
        <Typography className="sub-content-text">
          How is perquisite value calculated (Leave Encashment)?
        </Typography>
        <Box className="mt4 d-flex align-center gap-sm">
          <CustomRadio
            name="leavePaymentMethod"
            value="fixed_amount"
            checked={values?.leavePaymentMethod === 'fixed_amount'}
            onChange={(e) => {
              setFieldValue('leavePaymentMethod', e?.target?.value);
              setLeavePolicySubmit({
                ...leavePolicySubmit,
                leaveAccrual: false,
              });
            }}
            disableRipple
            label={
              <Typography className="sub-content-text">Fixed Amount</Typography>
            }
          />

          <CustomRadio
            name="leavePaymentMethod"
            value="formula_based"
            checked={values?.leavePaymentMethod === 'formula_based'}
            onChange={(e) => {
              setFieldValue('leavePaymentMethod', e?.target?.value);
              setLeavePolicySubmit({
                ...leavePolicySubmit,
                leaveAccrual: false,
              });
            }}
            disableRipple
            label={
              <Typography className="sub-content-text">
                Formula based
              </Typography>
            }
          />
        </Box>
        <Box className="mt8 mb8 d-flex align-center flex-wrap gap-sm">
          <Typography className="title-text">
            Encashment Multiply By :
          </Typography>
          <Box>
            <CustomTextField
              fullWidth
              id="encashmentMultiplier"
              name="encashmentMultiplier"
              placeholder="Days"
              value={values?.encashmentMultiplier}
              onChange={(e) => {
                handleChange(e);
                setLeavePolicySubmit({
                  ...leavePolicySubmit,
                  leaveAccrual: false,
                });
              }}
              error={Boolean(
                touched?.encashmentMultiplier && errors?.encashmentMultiplier
              )}
              helperText={
                touched?.encashmentMultiplier && errors?.encashmentMultiplier
              }
              onBlur={handleBlur}
              onInput={Inputype}
            />
          </Box>
        </Box>
        <Box>
          <Box className="d-flex align-center gap-5">
            <CustomCheckbox
              checked={values?.encashmentRestriction}
              onChange={(e) => {
                setFieldValue('encashmentRestriction', e?.target?.checked);
                setLeavePolicySubmit({
                  ...leavePolicySubmit,
                  leaveAccrual: false,
                });
              }}
              disableRipple
              label={
                <Typography className="sub-content-text">
                  Restrict leave encashment for employees serving their notice
                  period
                </Typography>
              }
            />
            <Tooltip
              arrow
              title={
                <Typography>
                  When this option is selected, employees serving their notice
                  period will not be eligible to leave encashment.
                </Typography>
              }
              classes={{ tooltip: 'info-tooltip-container' }}
            >
              <InfoIcon className="info-icon cursor-pointer" />
            </Tooltip>
          </Box>
          <Box>
            <CustomCheckbox
              checked={values?.allowEncashmentAnytime}
              onChange={(e) => {
                setFieldValue('allowEncashmentAnytime', e?.target?.checked);
                setFieldValue('encashMentAllowed', false);
                setFieldValue('encashMentAllowedDays', 0);
                setFieldValue('encashMentAllowedIn', 'week');
                setFieldValue('encashmentAllowedPeriods', false);
                setFieldValue('encashmentAllowedStart', 1);
                setFieldValue('encashmentAllowedEnd', 1);
                setFieldValue('employeesInProbation', false);
                setFieldValue('employeeCanEncash', 'percentage');
                setEncashment([
                  {
                    encashment_number: '',
                    encashment_max_limit: '',
                    encashment_leave_balance_exceeds: '',
                  },
                ]);
                setFieldValue('fieldArray', [
                  {
                    encashment_number: '',
                    encashment_max_limit: '',
                    encashment_leave_balance_exceeds: '',
                  },
                ]);
                setLeavePolicySubmit({
                  ...leavePolicySubmit,
                  leaveAccrual: false,
                });
              }}
              disableRipple
              label={
                <Typography className="sub-content-text">
                  Allow employees to request for leave encashment anytime during
                  the year?
                </Typography>
              }
            />
          </Box>
          {values?.allowEncashmentAnytime && (
            <>
              <Box className="ml24">
                <Box className="d-flex align-center gap-sm mt8 flex-wrap">
                  <Box>
                    <CustomCheckbox
                      checked={values?.encashMentAllowed}
                      onChange={(e) => {
                        setFieldValue('encashMentAllowed', e?.target?.checked);
                        setFieldValue('encashMentAllowedDays', 0);
                        setFieldValue('encashMentAllowedIn', 'week');
                        setLeavePolicySubmit({
                          ...leavePolicySubmit,
                          leaveAccrual: false,
                        });
                      }}
                      disableRipple
                      label={
                        <Typography className="sub-content-text">
                          Encashment is allowed
                        </Typography>
                      }
                    />
                  </Box>
                  <Box>
                    <CustomTextField
                      fullWidth
                      id="encashMentAllowedDays"
                      name="encashMentAllowedDays"
                      placeholder="Days"
                      value={values?.encashMentAllowedDays}
                      error={Boolean(
                        touched?.encashMentAllowedDays &&
                          errors?.encashMentAllowedDays
                      )}
                      helperText={
                        touched?.encashMentAllowedDays &&
                        errors?.encashMentAllowedDays
                      }
                      onBlur={handleBlur}
                      onChange={(e) => {
                        handleChange(e);
                        setLeavePolicySubmit({
                          ...leavePolicySubmit,
                          leaveAccrual: false,
                        });
                      }}
                      onInput={Inputype}
                      disabled={!values?.encashMentAllowed}
                    />
                  </Box>
                  <Typography className="title-text">times in a</Typography>
                  <Box className="d-flex align-center gap-sm">
                    <CustomSelect
                      fullWidth
                      id="encashMentAllowedIn"
                      name="encashMentAllowedIn"
                      placeholder="Percentage"
                      className="leave-select-wrap"
                      // value={values.encashMentAllowedIn}
                      options={identifiers?.ENCASHMENT_PERIOD_OPTIONS}
                      value={
                        identifiers?.ENCASHMENT_PERIOD_OPTIONS?.find((opt) => {
                          return opt?.value === values.encashMentAllowedIn;
                        }) || ''
                      }
                      onChange={(e) => {
                        setFieldValue('encashMentAllowedIn', e?.value);
                        setLeavePolicySubmit({
                          ...leavePolicySubmit,
                          leaveAccrual: false,
                        });
                      }}
                      error={Boolean(
                        touched.encashMentAllowedIn &&
                          errors.encashMentAllowedIn
                      )}
                      helperText={
                        touched.encashMentAllowedIn &&
                        errors.encashMentAllowedIn
                      }
                      isDisabled={!values?.encashMentAllowed}
                      isClearable={false}
                    />
                  </Box>
                </Box>
                <Box className="d-flex align-center gap-sm mt16 flex-wrap">
                  <Box>
                    <CustomCheckbox
                      checked={values?.encashmentAllowedPeriods}
                      onChange={(e) => {
                        setFieldValue(
                          'encashmentAllowedPeriods',
                          e?.target?.checked
                        );
                        setFieldValue('encashmentAllowedStart', 1);
                        setFieldValue('encashmentAllowedEnd', 1);
                        setLeavePolicySubmit({
                          ...leavePolicySubmit,
                          leaveAccrual: false,
                        });
                      }}
                      disableRipple
                      label={
                        <Typography className="sub-content-text">
                          Leave encashment is being submitted within the allowed
                          period of
                        </Typography>
                      }
                    />
                  </Box>
                  <Box>
                    <CustomSelect
                      fullWidth
                      id="encashmentAllowedStart"
                      name="encashmentAllowedStart"
                      placeholder="Period Start"
                      className="leave-select-wrap"
                      options={monthsOption}
                      value={
                        monthsOption?.find((opt) => {
                          return opt?.value === values.encashmentAllowedStart;
                        }) || ''
                      }
                      onChange={(e) => {
                        setFieldValue('encashmentAllowedStart', e?.value);
                        setLeavePolicySubmit({
                          ...leavePolicySubmit,
                          leaveAccrual: false,
                        });
                      }}
                      error={Boolean(
                        touched.encashmentAllowedStart &&
                          errors.encashmentAllowedStart
                      )}
                      helperText={
                        touched.encashmentAllowedStart &&
                        errors.encashmentAllowedStart
                      }
                      isDisabled={!values?.encashmentAllowedPeriods}
                      isClearable={false}
                    />
                  </Box>
                  <Typography className="title-text">to</Typography>
                  <Box className="d-flex align-center gap-5">
                    <CustomSelect
                      id="encashmentAllowedEnd"
                      name="encashmentAllowedEnd"
                      placeholder="Period End"
                      className="leave-select-wrap"
                      options={monthsOption}
                      value={
                        monthsOption?.find((opt) => {
                          return opt?.value === values.encashmentAllowedEnd;
                        }) || ''
                      }
                      onChange={(e) => {
                        setFieldValue('encashmentAllowedEnd', e?.value);
                        setLeavePolicySubmit({
                          ...leavePolicySubmit,
                          leaveAccrual: false,
                        });
                      }}
                      error={Boolean(
                        touched.encashmentAllowedEnd &&
                          errors.encashmentAllowedEnd
                      )}
                      helperText={
                        touched.encashmentAllowedEnd &&
                        errors.encashmentAllowedEnd
                      }
                      isDisabled={!values?.encashmentAllowedPeriods}
                      isClearable={false}
                    />
                    <Tooltip
                      arrow
                      title={
                        <Typography>
                          To restrict leave encashment requests to a specific
                          month, set the allowed period as "January to January,"
                          allowing employees to apply only in January.
                        </Typography>
                      }
                      classes={{
                        tooltip: 'info-tooltip-container',
                      }}
                    >
                      <InfoIcon className="info-icon cursor-pointer" />
                    </Tooltip>
                  </Box>
                </Box>
                <Box className="mt8">
                  <CustomCheckbox
                    checked={values?.employeesInProbation}
                    onChange={(e) => {
                      setFieldValue('employeesInProbation', e?.target?.checked);
                      setLeavePolicySubmit({
                        ...leavePolicySubmit,
                        leaveAccrual: false,
                      });
                    }}
                    disableRipple
                    label={
                      <Typography className="sub-content-text">
                        Employee in probation period cannot apply for leave
                        encashment
                      </Typography>
                    }
                  />
                </Box>
                <Box className="d-flex align-center gap-sm mt8">
                  <Typography className="title-text">
                    Employee can encash
                  </Typography>
                  <Box>
                    <CustomSelect
                      fullWidth
                      id="employeeCanEncash"
                      name="employeeCanEncash"
                      placeholder="Employee can encash"
                      className="leave-select-wrap"
                      options={identifiers?.LEAVE_DEDUCTION_OPTIONS}
                      value={
                        identifiers?.LEAVE_DEDUCTION_OPTIONS?.find((opt) => {
                          return opt?.value === values.employeeCanEncash;
                        }) || ''
                      }
                      onChange={(e) => {
                        setFieldValue('employeeCanEncash', e?.value);
                        setLeavePolicySubmit({
                          ...leavePolicySubmit,
                          leaveAccrual: false,
                        });
                      }}
                      error={Boolean(
                        touched.employeeCanEncash && errors.employeeCanEncash
                      )}
                      helperText={
                        touched.employeeCanEncash && errors.employeeCanEncash
                      }
                      isClearable={false}
                    />
                  </Box>
                </Box>
                {values?.fieldArray &&
                  values?.fieldArray?.length > 0 &&
                  values?.fieldArray?.map((item, index) => {
                    return (
                      <Box
                        className="d-flex align-center gap-sm mt16 flex-wrap"
                        key={index}
                      >
                        <Typography className="title-text">
                          Employee can encash up to
                        </Typography>
                        <Box>
                          <CustomTextField
                            fullWidth
                            id="employeeCanEncashUpTo"
                            name="employeeCanEncashUpTo"
                            placeholder="Days"
                            value={item?.encashment_number}
                            onChange={(e) => {
                              let list = encashment;
                              list[index].encashment_number = e.target.value;
                              setEncashment(list);
                              setFieldValue('fieldArray', list);
                              setLeavePolicySubmit({
                                ...leavePolicySubmit,
                                leaveAccrual: false,
                              });
                            }}
                            onBlur={handleBlur}
                            onInput={Inputype}
                          />
                        </Box>
                        <Typography className="title-tex">
                          percentage of leave balance, limited to max. of
                        </Typography>
                        <Box>
                          <CustomTextField
                            fullWidth
                            id="percentageOfLeaveBalance"
                            name="percentageOfLeaveBalance"
                            placeholder="Days"
                            value={item?.encashment_max_limit}
                            onChange={(e) => {
                              let list = encashment;
                              list[index].encashment_max_limit = e.target.value;
                              setEncashment(list);
                              setFieldValue('fieldArray', list);
                              setLeavePolicySubmit({
                                ...leavePolicySubmit,
                                leaveAccrual: false,
                              });
                            }}
                            onBlur={handleBlur}
                            onInput={Inputype}
                          />
                        </Box>
                        <Typography className="title-text">
                          days, if his/her total leave balance exceeds
                        </Typography>
                        <Box>
                          <CustomTextField
                            fullWidth
                            id="totalLeaveBalanceExceed"
                            name="totalLeaveBalanceExceed"
                            placeholder="Days"
                            value={item?.encashment_leave_balance_exceeds}
                            onChange={(e) => {
                              let list = encashment;
                              list[index].encashment_leave_balance_exceeds =
                                e.target.value;
                              setEncashment(list);
                              setFieldValue('fieldArray', list);
                              setLeavePolicySubmit({
                                ...leavePolicySubmit,
                                leaveAccrual: false,
                              });
                            }}
                            onBlur={handleBlur}
                            onInput={Inputype}
                          />
                        </Box>
                        <Box className="d-flex align-center gap-5">
                          <Typography className="title-text">days.</Typography>
                          <Tooltip
                            arrow
                            title={
                              <Typography>
                                You can encash 0 % of your leave balance, up to
                                a maximum of 0 days, if your total leave balance
                                is more than 0 days. Encashment is not allowed
                                for leave balances is less than 0 days.
                              </Typography>
                            }
                            classes={{
                              tooltip: 'info-tooltip-container',
                            }}
                          >
                            <InfoIcon className="info-icon cursor-pointer" />
                          </Tooltip>
                        </Box>
                        {encashment?.length !== 1 ? (
                          <DeleteOutlineIcon
                            className="info-icon delete-icon cursor-pointer"
                            onClick={() => {
                              let list = encashment;
                              list = list?.filter((f, i) => i !== index);
                              setEncashment(list);
                              setFieldValue('fieldArray', list);
                            }}
                          />
                        ) : (
                          <></>
                        )}
                      </Box>
                    );
                  })}
              </Box>
              <Box className="mt16">
                <CustomButton
                  startIcon={<AddIcon />}
                  variant="contained"
                  title="Add New"
                  onClick={() => {
                    let list = encashment;
                    list.push({
                      leave_period_days: '',
                      emp_provide_notice_period: '',
                      minimum_working_days: '',
                    });
                    setEncashment(list);
                    setFieldValue('fieldArray', list);
                    setRandom(Math.random());
                  }}
                  // onClick={handleBack}
                />
              </Box>
              <Box>
                <Typography className="sub-content-text mt16">
                  Does this encashment request require an approval?
                </Typography>
                <Box className="d-flex align-center gap-10">
                  <Box className="d-flex align-center gap-sm">
                    <CustomRadio
                      name="encashmentRequestApproval"
                      value="no"
                      checked={values?.encashmentRequestApproval === 'no'}
                      onChange={(e) =>
                        setFieldValue(
                          'encashmentRequestApproval',
                          e?.target?.value
                        )
                      }
                      disableRipple
                      label={
                        <Typography className="sub-content-text">No</Typography>
                      }
                    />
                    <Tooltip
                      arrow
                      title={
                        <Typography>
                          Encashment requests will get auto approved without
                          taking any action from the concerned person.
                        </Typography>
                      }
                      classes={{
                        tooltip: 'info-tooltip-container',
                      }}
                    >
                      <InfoIcon className="info-icon cursor-pointer" />
                    </Tooltip>
                  </Box>
                  <Box className="d-flex align-center gap-sm">
                    <CustomRadio
                      name="encashmentRequestApproval"
                      value="yes"
                      checked={values?.encashmentRequestApproval === 'yes'}
                      onChange={(e) =>
                        setFieldValue(
                          'encashmentRequestApproval',
                          e?.target?.value
                        )
                      }
                      disableRipple
                      label={
                        <Typography className="sub-content-text">
                          Yes
                        </Typography>
                      }
                    />
                    <Tooltip
                      arrow
                      title={
                        <Typography>
                          Encashment requests will be sent to the concerned
                          person and they will take action accordingly.
                        </Typography>
                      }
                      classes={{
                        tooltip: 'info-tooltip-container',
                      }}
                    >
                      <InfoIcon className="info-icon cursor-pointer" />
                    </Tooltip>
                  </Box>
                </Box>
              </Box>
            </>
          )}
        </Box>
      </Box>
      <Box>
        {!values?.allowEncashmentAnytime && (
          <>
            <EncashmentSection
              values={values}
              handleChange={handleChange}
              handleBlur={handleBlur}
              touched={touched}
              errors={errors}
              setFieldValue={setFieldValue}
              leavePolicySubmit={leavePolicySubmit}
              setLeavePolicySubmit={setLeavePolicySubmit}
              Inputype={Inputype}
            />
          </>
        )}
        <CarryForwardSection
          values={values}
          handleChange={handleChange}
          handleBlur={handleBlur}
          touched={touched}
          errors={errors}
          setFieldValue={setFieldValue}
          leavePolicySubmit={leavePolicySubmit}
          setLeavePolicySubmit={setLeavePolicySubmit}
          Inputype={Inputype}
        />
      </Box>
    </>
  );
}
