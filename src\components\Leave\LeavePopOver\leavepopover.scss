.leave-days-popover {
  width: 350px !important; // Set popover width explicitly
  max-width: 350px !important;
  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.1) !important;
  height: 100%;
  max-height: 250px;
  min-height: 230px;
  overflow: auto;
  background-color: var(--color-white) !important;
  margin: 0px !important;
  padding: 0px !important;
  .leave-table {
    .popover-title-wrap {
      position: sticky;
      top: 0;
      text-align: center;
      padding: 10px;
      background-color: var(--color-primary);
      color: var(--text-color-white);
    }
    .table-header {
      background-color: var(--color-dark-50);
    }
    .MuiTableCell-root {
      border-right: var(--normal-sec-border);
      &:last-child {
        border-right: none; /* Remove border for the last cell */
      }
    }

    .table-header {
      background-color: var(--color-off-white);
      .days-wrap {
        width: 45%;
      }
      .leave-wrap {
        width: 55%;
      }
    }
  }
  .MuiTooltip-arrow {
    display: none;
  }
}
