'use client';

import React, { useState, useEffect, useContext } from 'react';
import { Box, Typography, CircularProgress, Tooltip } from '@mui/material';
import { DataGrid, gridClasses } from '@mui/x-data-grid';
import CustomPagination from '@/components/UI/customPagination';
import CustomSelect from '@/components/UI/CustomSelect';
import CustomButton from '@/components/UI/CustomButton';
import DialogBox from '@/components/UI/Modalbox';
import Searchbar from '@/components/UI/CustomSearch';
// import { RequestIcon } from '@/components/images';
import { useRouter } from 'next/navigation';
import TuneIcon from '@mui/icons-material/Tune';
import CustomDatePicker from '@/components/UI/CustomDatePicker';
import { fetchFromStorage, saveToStorage } from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import AuthContext from '@/helper/authcontext';
import dayjs from 'dayjs';
import RequestIcon from '../../ActionIcons/RequestIcon';
import NoDataView from '@/components/UI/NoDataView';
import BranchDepartmentDisplay from '@/components/BranchDepartmentDisplay';
import { staticOptions } from '@/helper/common/staticOptions';
import { DateFormat } from '@/helper/common/commonFunctions';

export default function WSRRequestPage({
  loader,
  branchList,
  getDsrData,
  dsrData,
  totalCount,
  setFilterData,
  filterData,
  setFilterDataAppliedReq,
  filterDataAppliedReq,
  rowsPerPage,
  setRowsPerPage,
}) {
  const { authState, userdata, setUserdata } = useContext(AuthContext);

  const [searchValue, setSearchValue] = useState('');
  const [page, setPage] = useState(1);
  const [filter, setFilter] = useState(false);
  const [currency, setCurrency] = useState();

  const router = useRouter();

  const renderDetailStatus = (value) => {
    const statusMap = {
      deleted: 'failed',
      draft: 'draft',
      default: 'success',
    };
    const statusClass = statusMap[value] || statusMap.default;
    return (
      <Typography className={`sub-title-text ${statusClass} fw600`}>
        {value}
      </Typography>
    );
  };

  const renderStatus = (value) => {
    const statusMap = {
      inactive: 'cancelled',
      rejected: 'failed',
      pending: 'draft',
      default: 'success',
    };
    const statusClass = statusMap[value] || statusMap.default;
    return (
      <Typography className={`sub-title-text ${statusClass} fw600`}>
        {value}
      </Typography>
    );
  };

  const columns = [
    {
      field: 'id',
      headerName: 'ID',
      width: 48,
      minWidth: 48,
      flex: 0,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100">
            <Typography className="title-text text-ellipsis">
              <span>{params?.value}</span>
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'wsr_start_date',
      headerName: 'Start Date',
      width: 150,
      minWidth: 150,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100">
            {params?.value ? DateFormat(params?.value, 'dates') : ''}
          </Box>
        );
      },
    },
    {
      field: 'wsr_end_date',
      headerName: 'End Date',
      width: 150,
      minWidth: 150,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100">
            {params?.value ? DateFormat(params?.value, 'dates') : ''}
          </Box>
        );
      },
    },
    {
      field: 'wsr_branchz',
      headerName: 'Branch name',
      width: 200,
      minWidth: 200,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <BranchDepartmentDisplay
            row={{ branch: params?.row?.branch }}
            isBranchOnly={true}
          />
        );
      },
    },
    {
      field: 'amount',
      headerName: `Amount ( ${currency?.symbol ? currency?.symbol : '£'} )`,
      width: 150,
      minWidth: 150,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-start h100 gap-sm">
            <Typography className="title-text bg-amount-transparent text-ellipsis">
              <span>{params?.value}</span>
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'wsr_user',
      headerName: 'Submitted by',
      width: 200,
      minWidth: 200,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100">
            <Typography className="title-text text-ellipsis">
              <span>{params?.value?.user_full_name}</span>
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'wsr_request_updated_by',
      headerName: 'Action by',
      width: 200,
      minWidth: 200,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100">
            <Typography className="title-text text-ellipsis">
              <span>{params?.value?.user_full_name}</span>
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'wsr_detail_status',
      headerName: 'Status',
      width: 120,
      minWidth: 120,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100 text-capital">
            {renderDetailStatus(params?.value)}
          </Box>
        );
      },
    },
    {
      field: 'wsr_request_status',
      headerName: 'Request Status',
      width: 150,
      minWidth: 150,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100 text-capital">
            {renderStatus(params?.value)}
          </Box>
        );
      },
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex justify-center actions">
            <Box className="d-flex actions-dsr">
              <Tooltip
                title="WSR request"
                arrow
                classes={{
                  tooltip: 'info-tooltip-container ',
                }}
              >
                <Box>
                  <RequestIcon
                    // className="svg-icon"
                    onClick={() => {
                      setUserdata({
                        id: params?.row?.id,
                        filterData: filterDataAppliedReq,
                        searchValue: searchValue,
                        page: page,
                        wsrr: true,
                      });
                      saveToStorage(identifiers?.RedirectData, {
                        id: params?.row?.id,
                        filterData: filterDataAppliedReq,
                        searchValue: searchValue,
                        page: page,
                        wsrr: true,
                      });
                      router.push(`/wsr/${params?.row?.id}?isRequests=true`);
                    }}
                  />
                  {/* {RequestIcon()} */}
                </Box>
              </Tooltip>
            </Box>
          </Box>
        );
      },
    },
  ];

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      setPage(1);
      getDsrData(
        searchValue,
        1,
        filterDataAppliedReq?.branch,
        filterDataAppliedReq?.sdate,
        filterDataAppliedReq?.edate,
        filterDataAppliedReq?.status
      );
    }
  };
  const onPageChange = (newPage) => {
    setPage(newPage);
    getDsrData(
      searchValue,
      newPage,
      filterDataAppliedReq?.branch,
      filterDataAppliedReq?.sdate,
      filterDataAppliedReq?.edate,
      filterDataAppliedReq?.status
    );
  };

  const OnRowPerPageDSR = (newPage) => {
    setRowsPerPage(newPage);
    setPage(1);
    getDsrData(
      searchValue,
      1,
      filterDataAppliedReq?.branch,
      filterDataAppliedReq?.sdate,
      filterDataAppliedReq?.edate,
      filterDataAppliedReq?.status,
      newPage
    );
  };

  useEffect(() => {
    if (
      fetchFromStorage(identifiers?.RedirectData) &&
      fetchFromStorage(identifiers?.RedirectData)?.IsFromUser &&
      fetchFromStorage(identifiers?.RedirectData)?.wsrr
    ) {
      const fdata = fetchFromStorage(identifiers?.RedirectData);
      setPage(fdata?.page);
      setFilterData(fdata?.filterData);
      setFilterDataAppliedReq(fdata?.filterData);
      setSearchValue(fdata?.searchValue);
      getDsrData(
        fdata?.searchValue,
        fdata?.page,
        fdata?.filterData?.branch,
        fdata?.filterData?.sdate,
        fdata?.filterData?.edate,
        fdata?.filterData?.status
      );
    } else if (userdata && userdata?.IsFromUser && userdata?.wsrr) {
      const fdata = userdata;
      setPage(fdata?.page);
      setFilterData(fdata?.filterData);
      setFilterDataAppliedReq(fdata?.filterData);
      setSearchValue(fdata?.searchValue);
      getDsrData(
        fdata?.searchValue,
        fdata?.page,
        fdata?.filterData?.branch,
        fdata?.filterData?.sdate,
        fdata?.filterData?.edate,
        fdata?.filterData?.status
      );
    }
  }, [
    fetchFromStorage(identifiers?.RedirectData)?.IsFromUser,
    userdata?.IsFromUser,
  ]);

  useEffect(() => {
    authState?.currency_details && setCurrency(authState?.currency_details);
  }, [authState]);

  return (
    <>
      <Box className="d-flex gap-sm align-center flex-wrap justify-end">
        <Searchbar
          setSearchValue={setSearchValue}
          searchValue={searchValue}
          onKeyPress={handleKeyPress}
        />
        <CustomButton
          variant="outlined"
          isIconOnly
          startIcon={
            <Tooltip
              title={
                <Typography className="sub-title-text">Apply Filter</Typography>
              }
              classes={{
                tooltip: 'info-tooltip-container',
              }}
              arrow
            >
              <TuneIcon />
            </Tooltip>
          }
          onClick={() => {
            setFilter(!filter);
          }}
        />

        <CustomButton
          variant="outlined"
          title="Apply filter"
          fullWidth={false}
          onClick={() => {
            setPage(1);
            getDsrData(
              searchValue,
              1,
              filterDataAppliedReq?.branch,
              filterDataAppliedReq?.sdate,
              filterDataAppliedReq?.edate,
              filterDataAppliedReq?.status
            );
          }}
        />
      </Box>

      <Box className="table-container table-layout">
        {loader ? (
          <Box className="content-loader">
            <CircularProgress className="loader" color="inherit" />
          </Box>
        ) : (
          <>
            {dsrData && dsrData?.length === 0 ? (
              <Box className="no-data d-flex align-center justify-center">
                <NoDataView
                  // image
                  title="No WSR Requests Found"
                  description="There is no WSR Requests available at the moment."
                />
              </Box>
            ) : (
              <>
                <DataGrid
                  rows={dsrData}
                  columns={columns}
                  pageSize={rowsPerPage}
                  checkboxSelection={false} // Disable default checkbox column
                  disableSelectionOnClick // Disable row selection on click
                  columnVisibilityModel={{
                    actions:
                      authState?.UserPermission?.dsr === 2 &&
                      authState?.web_user_active_role_id !== 7 &&
                      authState?.web_user_active_role_id !== 14
                        ? true
                        : false,
                  }}
                  hideMenuIcon
                  getRowHeight={() => 'auto'}
                  sx={{
                    [`& .${gridClasses.cell}`]: {
                      py: 1,
                    },
                  }}
                />
                <CustomPagination
                  currentPage={page}
                  // totalPages={totalPages}
                  totalCount={totalCount}
                  rowsPerPage={rowsPerPage}
                  onPageChange={onPageChange}
                  OnRowPerPage={OnRowPerPageDSR}
                />
              </>
            )}
          </>
        )}
      </Box>
      <DialogBox
        open={filter}
        handleClose={() => {
          setFilter(!filter);
        }}
        title={'WSR filter'}
        className="small-dialog-box-container"
        content={
          <>
            <Box className="staff-filter">
              <Box>
                <Box>
                  <CustomSelect
                    placeholder="Branch name"
                    options={branchList}
                    showDot
                    value={
                      branchList?.find((opt) => {
                        return opt?.value === filterData?.branch;
                      }) || ''
                    }
                    onChange={(e) => {
                      setFilterData({
                        ...filterData,
                        branch: e?.value,
                      });
                    }}
                    label={<span>Branch name</span>}
                    menuPortalTarget={document.body}
                    styles={{
                      menuPortal: (base) => ({
                        ...base,
                        zIndex: 9999,
                      }),
                    }}
                  />
                </Box>

                <Box className="pt8">
                  <CustomDatePicker
                    label={<span>Start Date(DD/MM/YYYY)</span>}
                    name="date"
                    error={false}
                    value={dayjs(filterData?.sdate)}
                    onChange={(date) => {
                      setFilterData({
                        ...filterData,
                        sdate: date,
                      });
                    }}
                    format="DD/MM/YYYY"
                    inputVariant="outlined"
                    maxDate={dayjs(filterData?.edate)}
                  />
                </Box>
                <Box className="pt8">
                  <CustomDatePicker
                    label={<span>End Date(DD/MM/YYYY)</span>}
                    name="date"
                    error={false}
                    value={dayjs(filterData?.edate)}
                    onChange={(date) => {
                      setFilterData({
                        ...filterData,
                        edate: date,
                      });
                    }}
                    format="DD/MM/YYYY"
                    inputVariant="outlined"
                    minDate={dayjs(filterData?.sdate)}
                  />
                </Box>
                <Box className="pt8">
                  <CustomSelect
                    placeholder="Request status"
                    options={staticOptions?.DSR_STATUS_OPTIONS}
                    value={
                      staticOptions?.DSR_STATUS_OPTIONS?.find((opt) => {
                        return opt?.value === filterData?.status;
                      }) || ''
                    }
                    onChange={(e) => {
                      setFilterData({
                        ...filterData,
                        status: e?.value,
                      });
                    }}
                    label={<span>Request status</span>}
                    menuPortalTarget={document.body}
                    styles={{
                      menuPortal: (base) => ({
                        ...base,
                        zIndex: 9999,
                      }),
                    }}
                  />
                </Box>

                <Box className="form-actions-btn">
                  <CustomButton
                    fullWidth
                    variant="outlined"
                    title="Clear"
                    onClick={() => {
                      setFilter(false);
                      setPage(1);
                      setFilterData({
                        branch: '',
                        sdate: '',
                        edate: '',
                        status: '',
                      });
                      setFilterDataAppliedReq({
                        branch: '',
                        sdate: '',
                        edate: '',
                        status: '',
                      });
                      getDsrData(searchValue, 1, '', '', '', '');
                    }}
                  />
                  <CustomButton
                    fullWidth
                    variant="contained"
                    title="Apply"
                    onClick={() => {
                      setFilter(false);
                      setPage(1);
                      setFilterDataAppliedReq({
                        branch: filterData?.branch,
                        sdate: filterData?.sdate,
                        edate: filterData?.edate,
                        status: filterData?.status,
                      });
                      getDsrData(
                        searchValue,
                        1,
                        filterData?.branch,
                        filterData?.sdate,
                        filterData?.edate,
                        filterData?.status
                      );
                    }}
                  />
                </Box>
              </Box>
            </Box>
          </>
        }
      />
    </>
  );
}
