import { BlankPinIcon, PinIcon, ResetIcon } from '@/helper/common/images';
import { Box, Typography } from '@mui/material';
import CustomSelect from '@/components/UI/CustomSelect';
import CustomButton from '@/components/UI/CustomButton';
import CustomSearch from '@/components/UI/CustomSearch';
import './filtercomponent.scss';
const FilterComponent = ({
  filters,
  filterData,
  setFilterData,
  selectedFilters,
  toggleFilter,
  saveLayout,
  setOpenFilterDrawer,
  getFirstFourFilters,
  searchValue,
  setSearchValue,
  handleKeyPress,
  isMobile = false,
  handleApplyFilter,
  handleClearFilter,
}) => {
  return (
    <Box className="common-filter-sec">
      {!isMobile && (
        <Box className="d-flex align-center justify-space-between">
          <Typography className="caption-text">
            You can pin multiple filters.
          </Typography>
          <Box
            className="d-flex align-center gap-5 reset-wrap cursor-pointer"
            onClick={() => getFirstFourFilters()}
          >
            <ResetIcon className="reset-icon" />
            <Typography className="caption-text">Reset</Typography>
          </Box>
        </Box>
      )}
      {/* <CustomSearchBar
        setSearchValue={setSearchValue}
        searchValue={searchValue}
        onKeyPress={handleKeyPress}
      /> */}
      {filters?.map(({ key, label, options, permission }) =>
        permission ? (
          <Box key={key} className="pt16 d-flex align-center gap-10">
            {/* Pin Icon (Multiple Selection Support) - Hide on mobile */}
            {!isMobile && (
              <Box onClick={() => toggleFilter(key)} className="cursor-pointer">
                {selectedFilters?.includes(key) ? (
                  <PinIcon className="cursor-pointer pin-icon" />
                ) : (
                  <BlankPinIcon className="cursor-pointer" />
                )}
              </Box>
            )}
            {key === 'search' ? (
              <Box className="w100">
                <CustomSearch
                  setSearchValue={setSearchValue}
                  searchValue={searchValue}
                  onKeyPress={handleKeyPress}
                />
              </Box>
            ) : (
              <CustomSelect
                placeholder={label}
                options={options}
                value={
                  options?.find((opt) => {
                    return opt?.value === filterData[key];
                  }) || ''
                }
                onChange={(e) => {
                  setFilterData({ ...filterData, [key]: e?.value });
                }}
              />
            )}
          </Box>
        ) : null
      )}

      <Box className="pt32 w100 d-flex justify-end gap-10">
        {isMobile ? (
          <>
            <CustomButton
              variant="outlined"
              title="Clear Filter"
              onClick={handleClearFilter}
            />
            <CustomButton
              variant="contained"
              title="Apply Filter"
              onClick={handleApplyFilter}
            />
          </>
        ) : (
          <CustomButton
            variant="contained"
            title="Save Layout"
            onClick={() => {
              saveLayout();
              setOpenFilterDrawer(false);
            }}
          />
        )}
      </Box>
    </Box>
  );
};

export default FilterComponent;
