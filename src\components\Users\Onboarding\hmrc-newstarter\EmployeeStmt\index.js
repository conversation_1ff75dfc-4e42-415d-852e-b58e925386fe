import { Box, Typography, Tooltip } from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import CustomCheckbox from '@/components/UI/CustomCheckbox';

export default function EmployeeStatement({
  touched,
  errors,
  values,
  // handleChange,
  setFieldValue,
  // handleBlur,
  ViewAccessOnly,
}) {
  return (
    <Box>
      <Typography className="title-text pt16">
        These questions will help you choose the statement that matches your
        circumstances. The statement you choose helps your employer apply the
        correct tax code.
      </Typography>
      <Box className="pt16">
        <Typography className="field-label" id="es_job">
          Do you have another job? <span className="required">*</span>
        </Typography>
        <Box
          className={
            ViewAccessOnly ? 'checkbox-details pt8' : 'checkbox-details pt8'
          }
        >
          <CustomCheckbox
            name="job"
            checked={values?.es_job === 'yes'}
            onChange={(e) => {
              setFieldValue('es_job', e.target.checked ? 'yes' : '');
              e.target.checked
                ? setFieldValue('statementc', 'yes')
                : setFieldValue('statementc', '');
              e.target.checked
                ? setFieldValue('es_payments', 'yes')
                : setFieldValue('es_payments', '');
              if (e.target.checked) {
                setFieldValue('statementa', '');
                setFieldValue('statementb', '');
                setFieldValue('es_payments_april', '');
              }
            }}
            disabled={ViewAccessOnly}
            label="Yes"
            className="mr8"
            labelPlacement="start"
          />
          <Typography className="title-text">
            Put an "X" in the statement C Box Below
          </Typography>
        </Box>
        <Box
          className={
            ViewAccessOnly ? 'checkbox-details pt8' : 'checkbox-details pt8'
          }
        >
          <CustomCheckbox
            name="job"
            checked={values?.es_job === 'no'}
            onChange={(e) => {
              setFieldValue('es_job', e.target.checked ? 'no' : '');
              values?.es_payments !== 'yes' && setFieldValue('statementc', '');
            }}
            disabled={ViewAccessOnly}
            label="No"
            className="mr8"
            labelPlacement="start"
          />
          <Typography className="title-text">Go to next question</Typography>
        </Box>
        {touched.es_job && errors.es_job && (
          <Typography variant="body2" className="other-field-error-text">
            {errors.es_job}
          </Typography>
        )}
      </Box>
      <Box className="pt32">
        <Typography className="field-label" id="es_payments">
          Do you receive payments from a State, workplace or private pension?
          <span className="required">*</span>
        </Typography>
        <Box
          className={
            ViewAccessOnly || values?.es_job === 'yes'
              ? 'checkbox-details pt8'
              : 'checkbox-details pt8'
          }
        >
          <CustomCheckbox
            name="payments"
            checked={values?.es_payments === 'yes'}
            onChange={(e) => {
              setFieldValue('es_payments', e.target.checked ? 'yes' : '');
              e.target.checked
                ? setFieldValue('statementc', 'yes')
                : setFieldValue('statementc', '');
              if (e.target.checked) {
                setFieldValue('statementa', '');
                setFieldValue('statementb', '');
                setFieldValue('es_payments_april', '');
              }
            }}
            disabled={ViewAccessOnly || values?.es_job === 'yes'}
            label="Yes"
            className="mr8"
            labelPlacement="start"
          />
          <Typography className="title-text">
            Put an "X" in the statement C Box Below
          </Typography>
        </Box>
        <Box
          className={
            ViewAccessOnly || values?.es_job === 'yes'
              ? 'checkbox-details pt8'
              : 'checkbox-details pt8'
          }
        >
          <CustomCheckbox
            name="es_payments"
            checked={values?.es_payments === 'no'}
            onChange={(e) => {
              setFieldValue('es_payments', e.target.checked ? 'no' : '');
              values?.es_job !== 'yes' &&
                e.target.checked &&
                setFieldValue('statementc', '');
            }}
            disabled={ViewAccessOnly || values?.es_job === 'yes'}
            label="No"
            className="mr8"
            labelPlacement="start"
          />
          <Typography className="title-text">Go to next question</Typography>
          {/* question 10 */}
        </Box>
        {touched.es_payments && errors.es_payments && (
          <Typography variant="body2" className="other-field-error-text">
            {errors.es_payments}
          </Typography>
        )}
      </Box>
      {values?.es_job &&
        values?.es_payments &&
        values?.es_job !== 'yes' &&
        values?.es_payments !== 'yes' && (
          <Box className="pt32">
            <Typography className="field-label">
              Since 6 April have you received payments from:
              <span className="required">*</span>
            </Typography>
            <Box className="pt8">
              <Box className="payment-section">
                <Box className="dots"></Box>
                <Typography className="title-text">
                  Another job which has ended or any of the following taxable
                  benefits.
                </Typography>
              </Box>
              <Box className="payment-section">
                <Box className="dots"></Box>
                <Typography className="title-text">
                  Jobseeker's Allowance (JSA)
                </Typography>
              </Box>
              <Box className="payment-section">
                <Box className="dots"></Box>
                <Typography className="title-text">
                  Employment & Support Allowance (ESA)
                </Typography>
              </Box>
              <Box className="payment-section">
                <Box className="dots"></Box>
                <Typography className="title-text">
                  Incapacity Benefits
                </Typography>
              </Box>
              <Box className="payment-section">
                <Box className="dots"></Box>
                <Typography className="title-text">
                  Another job which has ended or any of the following taxable
                  benefits.
                </Typography>
              </Box>
            </Box>
            <Box
              className={
                ViewAccessOnly ? 'checkbox-details pt8' : 'checkbox-details pt8'
              }
            >
              <CustomCheckbox
                name="es_payments_april"
                checked={values?.es_payments_april === 'yes'}
                onChange={(e) => {
                  setFieldValue(
                    'es_payments_april',
                    e.target.checked ? 'yes' : ''
                  );
                  e.target.checked
                    ? setFieldValue('statementb', 'yes')
                    : setFieldValue('statementb', '');
                  setFieldValue('statementa', '');
                }}
                disabled={ViewAccessOnly}
                label="Yes"
                className="mr8"
                labelPlacement="start"
              />
              <Typography className="title-text">
                Put an "X" in the statement B Box Below
              </Typography>
            </Box>
            <Box
              className={
                ViewAccessOnly ? 'checkbox-details pt8' : 'checkbox-details pt8'
              }
            >
              <CustomCheckbox
                name="es_payments_april"
                checked={values?.es_payments_april === 'no'}
                onChange={(e) => {
                  setFieldValue(
                    'es_payments_april',
                    e.target.checked ? 'no' : ''
                  );
                  e.target.checked
                    ? setFieldValue('statementa', 'yes')
                    : setFieldValue('statementa', '');
                  setFieldValue('statementb', '');
                }}
                disabled={ViewAccessOnly}
                label="No"
                className="mr8"
                labelPlacement="start"
              />
              <Typography className="title-text">
                Put an "X" in the statement A Box Below
              </Typography>
            </Box>
            {touched.es_payments_april && errors.es_payments_april && (
              <Typography variant="body2" className="other-field-error-text">
                {errors.es_payments_april}
              </Typography>
            )}
          </Box>
        )}

      <Box className="statement-info pt16">
        <Typography className="title-text fw600">Statement A </Typography>
        <Tooltip
          classes={{
            tooltip: 'info-tooltip-container',
          }}
          title={
            <div>
              <p className="">
                This is my first job since 6 April and since the 6 April I have
                not received payments from any of the following:
              </p>
              <p>Jobseeker's Allowance</p>
              <p>Employment and Support Allowance</p>
              <p>Incapacity Benefit </p>
            </div>
          }
          arrow
        >
          <InfoIcon className="info-icon" />
        </Tooltip>
      </Box>
      <Box className={'statement-checkbox pt16'}>
        <CustomCheckbox
          name="statementa"
          checked={values?.statementa === 'yes'}
          onChange={(e) => {
            setFieldValue('statementa', e.target.checked ? 'yes' : '');
          }}
          disabled={ViewAccessOnly || true}
          label="Current personal allowance"
        />
      </Box>
      {touched.statementa && errors.statementa && (
        <Typography variant="body2" className="other-field-error-text">
          {errors.statementa}
        </Typography>
      )}
      <Box className="statement-info pt16">
        <Typography className="title-text fw600">Statement B </Typography>
        <Tooltip
          classes={{
            tooltip: 'info-tooltip-container',
          }}
          title={
            <div>
              <p className="">
                Since 6 April I have had another job but I do not have a
                P45.And/or since the 6 April I have received payments from any
                of the following:
              </p>
              <p>Jobseeker's Allowance</p>
              <p>Employment and Support Allowance</p>
              <p>Incapacity Benefit </p>
            </div>
          }
          arrow
        >
          <InfoIcon className="info-icon" />
        </Tooltip>
      </Box>

      <Box className={'statement-checkbox pt16'}>
        <CustomCheckbox
          name="statementb"
          checked={values?.statementb === 'yes'}
          onChange={(e) => {
            setFieldValue('statementb', e.target.checked ? 'yes' : '');
          }}
          disabled={ViewAccessOnly || true}
          label="Current personal allowance on week 1/month 1 basis"
        />
      </Box>
      {touched.statementb && errors.statementb && (
        <Typography variant="body2" className="other-field-error-text">
          {errors.statementb}
        </Typography>
      )}
      <Box className="statement-info pt16">
        <Typography className="title-text fw600">Statement C </Typography>
        <Tooltip
          classes={{
            tooltip: 'info-tooltip-container',
          }}
          title={
            <div>
              <p className="">
                I have another job and/or I am in receipt of a State, workplace
                or private pension.
              </p>
            </div>
          }
          arrow
        >
          <InfoIcon className="info-icon" />
        </Tooltip>
      </Box>
      <Box className={'statement-checkbox pt16'}>
        <CustomCheckbox
          name="statementc"
          checked={values?.statementc === 'yes'}
          onChange={(e) => {
            setFieldValue('statementc', e.target.checked ? 'yes' : '');
          }}
          disabled={ViewAccessOnly || true}
          label="Tax Code BR"
        />
      </Box>
      {touched.statementc && errors.statementc && (
        <Typography variant="body2" className="other-field-error-text">
          {errors.statementc}
        </Typography>
      )}
      <Typography className="title-text pt16">
        Jobseeker's Allowance (JSA) is an unemployment benefit which can be
        claimed while looking for work.
      </Typography>
      <Typography className="title-text pt4">
        Employment and Support Allowance (ESA) is a benefit which can be claimed
        if you have a disability or health condition that affects how much you
        can work.
      </Typography>
      <Typography className="title-text pt4">
        Incapacity Benefit is help if you could not work because of an illness
        or disability before 31 January 2011.
      </Typography>
      <Typography className="title-text pt4">
        State Pension is a pension paid when you reach State Pension age.
      </Typography>
      <Typography className="title-text pt4">
        Workplace pension is a pension which was arranged by your employer and
        is being paid to you.
      </Typography>
      <Typography className="title-text pt4">
        Private pension is a pension arranged by you and is being paid to you.
      </Typography>
      <Typography className="title-text pt4">
        Please note that no other Government or HRMC paid benefits need to be
        considered when completing this form.
      </Typography>
    </Box>
  );
}
