import { Box, Typography } from '@mui/material';
import React, { useEffect, useState } from 'react';
import CustomButton from '@/components/UI/CustomButton';
import CustomSelect from '@/components/UI/CustomSelect';
import { URLS } from '@/helper/constants/urls';
import axiosInstance from '@/helper/axios/axiosInstance';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { staticOptions } from '@/helper/common/staticOptions';
import './workschedule.scss';

const daysOfWeek = [
  'monday',
  'tuesday',
  'wednesday',
  'thursday',
  'friday',
  'saturday',
  'sunday',
];

export default function WorkSchedule({ UserDetails }) {
  // const [loader, setLoader] = useState(false);
  const [workValues, setWorkValues] = useState();

  const submitHandler = async () => {
    const filteredWeekDays = daysOfWeek?.reduce((acc, day) => {
      acc[day] = workValues[day];
      return acc;
    }, {});
    const sendData = {
      user_week_day: filteredWeekDays,
      user_id: UserDetails?.id,
    };
    try {
      // setLoader(true);
      const { status, data } = await axiosInstance.put(
        URLS?.UPDATE_WORK_SCHEDULE,
        sendData
      );

      if (status === 200 || status === 201) {
        // setLoader(false);
        setApiMessage('success', data?.message);
      }
    } catch (error) {
      // setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  useEffect(() => {
    UserDetails?.user_week_day && setWorkValues(UserDetails?.user_week_day);
  }, [UserDetails?.user_week_day]);

  return (
    <Box className="work-schedule-wrap">
      <Typography className="body-sm">
        Use the work schedule to set your employee's regular days off. They will
        then display on the rota accordingly. Changes to work schedules will
        apply from the current day forwards.
      </Typography>

      <Box className="pt32 work-schedule-select-box-wrap">
        {daysOfWeek &&
          daysOfWeek?.length > 0 &&
          daysOfWeek?.map((day, index) => {
            return (
              <Box key={index}>
                <CustomSelect
                  placeholder="Work Schedule"
                  options={staticOptions?.WORKING_OPTIONS}
                  value={
                    staticOptions?.WORKING_OPTIONS?.find(
                      (f) => f?.value === workValues?.[day]
                    ) || ''
                  }
                  name={day}
                  onChange={(e) => {
                    setWorkValues({
                      ...workValues,
                      [day]: e?.value,
                    });
                  }}
                  label={<span className="text-capital">{day}</span>}
                />
              </Box>
            );
          })}
      </Box>

      <Box className="d-flex justify-end align-center">
        <Box className="">
          <CustomButton
            variant="contained"
            onClick={() => submitHandler()}
            type="submit"
            title="Save"
          />
        </Box>
        {/* <Box className="">
          <CustomButton variant="contained" type="submit" title="Cancel" />
        </Box> */}
      </Box>
    </Box>
  );
}
