@import '../../../styles/variable.scss';

.select-wrap {
  position: relative;
  // .MuiFormControl-root {
  //   border: 1px solid $color-Dark-10;
  // }
  .MuiFormLabel-root {
    left: 3px;
    top: -21px;
    transform: none;
  }
  .MuiSelect-select {
    padding: 7px 7px 7px 15px;
    z-index: 999;
    position: relative;
    margin-top: 2px;
  }
  .MuiSvgIcon-root {
    z-index: 999;
    color: $color-Dark-80 !important;
  }
  fieldset {
    height: 45px;
    background-color: $color-White;
    padding: 1px 16px;
    border-radius: 8px;
    border: 1px solid $color-Dark-10;
    margin-top: 2px;
  }

  .MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline {
    border: 1px solid $color-Dark-10;
  }
  legend {
    display: none !important;
  }
  .MuiFormLabel-root {
    font-family: $PrimaryFont;
    font-size: 12px;
    font-style: normal;
    color: $color-Dark-80;
    font-weight: 500;
    line-height: 18px;
    letter-spacing: -0.5px;
    text-transform: capitalize;
  }
  .placeholder {
    color: $color-Dark-20;
    text-transform: capitalize;
  }
  // .Mui-disabled {
  //   .MuiOutlinedInput-notchedOutline {
  //     // background-color: $color-Dark-10;
  //     // border: none;
  //   }
  // }
  .basic-multi-select {
    position: relative;
    box-shadow: none !important;
  }
  .select__control {
    // padding: 6px 3px 6px;
    padding: 4px 3px 3px;
    border: 1px solid $color-Dark-10;
    border-radius: 8px;
  }
  .select__control:hover,
  .select__control:focus,
  .select__control--is-focused {
    border-color: $color-Dark-10 !important;
    box-shadow: none !important;
  }
  .select__value-container {
    max-height: 90px;
    overflow: scroll;
  }
  .select__menu-portal {
    position: absolute;
    z-index: 9999;
    top: 39px;
    left: 0;
    box-shadow: 10px 10px 10px 10px rgba(0, 0, 0, 0.05);
    // overflow-x: scroll;
    // background-color: red($color: #000000);
  }
  .select__indicator-separator {
    display: none;
  }
  .select__placeholder {
    color: #a6a8b1;
    opacity: 1;
    font-weight: 400;
    font-size: 15px;
    line-height: 18px;
    text-transform: capitalize;
    font-family: Inter, sans-serif;
  }
}
.textfeild-error {
  .select__control {
    border: 1px solid $error;
  }
  .MuiFormLabel-root {
    color: $error;
  }
  .select__indicator-separator {
    background-color: $error;
  }
  .select__control:hover {
    border: 1px solid $error;
  }
}
.select-wrap {
  position: relative;
}
.disabled-multifield {
  cursor: not-allowed;
  // .basic-multi-select .select__control {
  //   cursor: not-allowed;
  //   background-color: $color-White !important;
  // }
  ::-webkit-scrollbar-track {
    background-color: hsl(0, 0%, 95%) !important;
  }
}

// .multi-list {
//   .select__menu-portal {
//     max-height: 10px !important;
//   }
// }
