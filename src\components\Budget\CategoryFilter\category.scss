@import '@/styles/variable.scss';

.all-category-wrap-budget {
  .main-category-wrap {
    width: calc(100% - 36px);
    display: flex;
    justify-content: space-between;
    .category-name {
      width: calc(100% - 36px);
    }
    .category-toggle {
      width: 30px;
      height: 16px;
      .MuiFormControlLabel-root {
        margin-right: 0;
        margin-left: 0;
      }
      .MuiSwitch-root {
        width: 30px;
        height: 16px;
      }
      .MuiSwitch-thumb {
        width: 12px;
        height: 12px;
      }

      .MuiSwitch-track {
        background-color: var(--color-primary) !important;
      }
    }
    .checked-toggle {
      .MuiSwitch-switchBase {
        margin: 2px -1px;
      }
    }
    .unchecked-toggle {
      .MuiSwitch-switchBase {
        margin: 2px 3px;
      }
    }
  }
}
.tooltip-budget {
  z-index: 9999999999999999999 !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  transform: none !important;
}
