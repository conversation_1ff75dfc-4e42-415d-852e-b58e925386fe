.right-to-work-page {
  .display-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(0, 250px));
    gap: var(--spacing-lg);
    align-items: flex-start;
    width: 100%;
  }

  .address-grid {
    .address-section {
      width: 70%;

      @media (max-width: 768px) {
        width: 100%;
      }
    }
  }

  .hs-heading {
    display: flex;
    justify-content: space-between;
    width: 70%;

    @media (max-width: 1299px) {
      width: 100%;
    }
  }

  .select-all-wrap {
    .select-all-checkbox {
      padding: 0px;
      .MuiSvgIcon-root {
        fill: var(--color-green);
      }
    }
  }
}
.rtwc-upload-grid {
  display: grid;
  grid-template-columns: 49% 49% !important;
  row-gap: 20px !important;
  column-gap: 20px !important;
  justify-content: space-between !important;
  align-items: center;

  .rtwc-upload {
    display: flex !important;
    flex-direction: column !important;
    box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
    padding: 12px 12px;

    .title-section {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      column-gap: 10px;

      .title {
        text-align: start;
        width: calc(100% - 100px);
      }
    }

    .action-sec {
      display: flex;
      margin-top: 6px;
      align-items: center;

      .delete-icon,
      .view-icon {
        margin-left: 20px;
        width: 18px;
        height: 18px;
        cursor: pointer;
      }

      .delete-check-box {
        line-height: 0px;
      }

      .view-icon {
        margin-right: 5px;
      }
    }
  }

  .upload-download-sec.rtwc-upload {
    .action-sec {
      color: var(--text-green);
      align-items: center;

      svg {
        fill: var(--text-green) !important;
      }

      .delete-icon {
        fill: var(--color-danger) !important;
        width: 17px;
        height: 17px;
      }
    }
  }

  @media (max-width: 1200px) {
    grid-template-columns: 100% !important;
  }
}
.form-checkbox {
  display: flex;

  .check-box-form {
    flex-direction: row-reverse;
    margin-left: 0;

    .MuiFormControlLabel-label {
      font-family: var(--font-family-primary);
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-regular);
      color: var(--text-color-black);
      line-height: var(--line-height-base);
    }
    .MuiCheckbox-root.Mui-checked {
      color: var(--color-primary);
    }
  }
  .form-row {
    flex-direction: row !important;
    .MuiButtonBase-root {
      padding-left: 0 !important;
    }
  }
  .approved-checkbox {
    color: var(--text-green);

    .MuiFormControlLabel-label {
      font-weight: 600;
    }

    .Mui-disabled.MuiTypography-root {
      color: var(--text-green) !important;
    }

    .MuiTypography-root {
      color: var(--text-green) !important;
    }

    svg {
      fill: var(--text-green) !important;
    }
  }

  .reject-checkbox {
    color: var(--color-danger);

    .MuiFormControlLabel-label {
      font-weight: 600;
    }

    .Mui-disabled.MuiTypography-root {
      color: var(--color-danger) !important;
    }

    .MuiTypography-root {
      color: var(--color-danger) !important;
    }

    svg {
      fill: var(--color-danger) !important;
    }
  }
  .Mui-disabled {
    opacity: 1;
    cursor: not-allowed !important;
    .MuiCheckbox-root.Mui-checked {
      color: var(--color-primary);
    }
    .MuiTypography-root {
      color: var(--color-black) !important;
    }
  }
}
.form-checkbox.reject-checkbox-details {
  .check-box-form {
    .MuiButtonBase-root {
      margin-left: 0 !important;
      padding: 2px 0;
      margin-right: 8px;
    }
  }
}
