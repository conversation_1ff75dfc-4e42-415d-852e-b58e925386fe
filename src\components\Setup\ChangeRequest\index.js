'use client';

import React, { useEffect, useState } from 'react';
import { Box, Typography, CircularProgress } from '@mui/material';
import FieldCategorySection from './CRFields';
import CustomSearch from '@/components/UI/CustomSearch';
import CustomButton from '@/components/UI/CustomButton';
import Icon from '@/components/UI/AppIcon/AppIcon';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { changeRequestService } from '@/services/changeRequestService';
import './cr.scss';
import _ from 'lodash';

export default function DownloadField() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFields, setSelectedFields] = useState([]);
  const [expandedCategories, setExpandedCategories] = useState(['personal']);
  const [fieldCategories, setFieldCategories] = useState([]);
  const [loaderUser, setLoaderUser] = useState(true);

  const sectionMeta = {
    personal_details: {
      id: 'personal_details',
      name: 'Personal Information',
      icon: 'User',
    },
    employee_contract: {
      id: 'employee_contract',
      name: 'Employee Contract',
      icon: 'FileText',
    },
    right_to_work: {
      id: 'right_to_work',
      name: 'Right to Work',
      icon: 'Briefcase',
    },
    starter_form: {
      id: 'starter_form',
      name: 'Starter Form',
      icon: 'FileText',
    },
  };

  const transformResponse = (response) => {
    return Object.entries(response).map(([sectionKey, fields]) => {
      const meta = sectionMeta[sectionKey] || {
        id: sectionKey,
        name: sectionKey
          .replace(/_/g, ' ')
          .replace(/\b\w/g, (l) => l.toUpperCase()),
        icon: 'File',
      };

      return {
        id: meta?.id,
        name: meta?.name,
        icon: meta?.icon,
        fields: fields?.map((field) => ({
          id: field?.key,
          name: field?.label,
          type: getFieldType(field?.key), // Helper function to determine field type
          required: isFieldRequired(field?.key), // Helper function to determine if field is required
        })),
      };
    });
  };

  const getFieldType = (fieldKey) => {
    // Define field types based on field key patterns
    if (fieldKey.includes('date')) return 'date';
    if (fieldKey.includes('email')) return 'email';
    if (fieldKey.includes('phone') || fieldKey.includes('mobile')) return 'tel';
    if (fieldKey.includes('address')) return 'textarea';
    if (
      fieldKey.includes('gender') ||
      fieldKey.includes('status') ||
      fieldKey.includes('type')
    )
      return 'select';
    if (fieldKey.includes('number')) return 'number';
    if (fieldKey.startsWith('has_') || fieldKey.startsWith('is_'))
      return 'boolean';
    return 'text'; // Default type
  };

  const isFieldRequired = (fieldKey) => {
    // Define required fields based on business logic
    const requiredFields = [
      // 'employment_number',
      // 'username',
      'user_first_name',
      'user_last_name',
      'user_email',
    ];
    return requiredFields.includes(fieldKey);
  };

  const getFieldOrder = (fieldKey) => {
    const requiredFields = [
      // 'employment_number',
      // 'username',
      'user_first_name',
      'user_last_name',
      'user_email',
    ];
    const index = requiredFields.indexOf(fieldKey);
    return index !== -1 ? index + 1 : requiredFields.length + 1;
  };

  const getFieldList = async () => {
    setLoaderUser(true);
    try {
      const response = await changeRequestService.getCRFields();

      if (response.success) {
        const transformedFields = transformResponse(response.data);

        setFieldCategories(transformedFields);
        const requiredFields = transformedFields?.flatMap((category) =>
          category?.fields
            .filter((field) => field?.required)
            .map((field) => ({
              ...field,
              categoryId: category?.id,
              categoryName: category?.name,
              order: getFieldOrder(field?.id),
            }))
        );
        getStoredField(transformedFields, requiredFields);
        setLoaderUser(false);
      }
    } catch (error) {
      setLoaderUser(false);
      setFieldCategories([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const extractMatchingFields = (forms, keysToKeep) => {
    return keysToKeep
      .flatMap((section) =>
        section?.fields
          ?.filter((field) => forms?.includes(field?.id))
          .map((field) => ({
            ...field,
            categoryId: section?.id,
            categoryName: section?.name,
          }))
      )
      .map((field, index) => ({
        ...field,
        order: index + 1,
      }));
  };

  const getStoredField = async (requiredFields, selected) => {
    try {
      const response = await changeRequestService.getStoredCRFields();

      if (response.success) {
        const matchingFields =
          response.data?.key && response.data?.key?.length > 0
            ? extractMatchingFields(response.data?.key, requiredFields)
            : selected;
        setSelectedFields(matchingFields);
      }
    } catch (error) {
      setFieldCategories([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // Initialize with required fields
  useEffect(() => {
    getFieldList();
  }, []);

  const handleFieldToggle = (field, categoryId, categoryName) => {
    const fieldWithCategory = { ...field, categoryId, categoryName };

    setSelectedFields((prev) => {
      const isSelected = prev.some((f) => f.id === field.id);

      if (isSelected) {
        // Don't allow removing required fields
        if (field.required) return prev;
        return prev.filter((f) => f.id !== field.id);
      } else {
        const newOrder = prev.length + 1;
        return [...prev, { ...fieldWithCategory, order: newOrder }];
      }
    });
  };

  const handleCategorySelectAll = (categoryId, fields) => {
    setSelectedFields((prev) => {
      // Keep track of existing fields and their orders
      const existingFields = new Map(prev.map((field) => [field.id, field]));

      // Add only new fields that aren't already selected
      const newFields = fields
        .filter((field) => !existingFields.has(field.id))
        .map((field, index) => ({
          ...field,
          categoryId,
          categoryName: fieldCategories.find((cat) => cat?.id === categoryId)
            ?.name,
          order: prev.length + index + 1,
        }));

      // Return existing fields in their original order plus new fields at the end
      return [...prev, ...newFields];
    });
  };

  const handleCategoryDeselectAll = (categoryId) => {
    setSelectedFields((prev) => {
      // Keep required fields and fields from other categories
      const remainingFields = prev.filter(
        (f) => f.categoryId !== categoryId || f.required
      );

      // Reorder the remaining fields sequentially
      return remainingFields.map((field, index) => ({
        ...field,
        order: index + 1,
      }));
    });
  };

  const toggleCategoryExpansion = (categoryId) => {
    setExpandedCategories((prev) =>
      prev.includes(categoryId)
        ? prev.filter((id) => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  const filteredCategories = fieldCategories
    .map((category) => ({
      ...category,
      fields: category.fields.filter((field) =>
        field.name.toLowerCase().includes(searchTerm.toLowerCase())
      ),
    }))
    .filter((category) => category?.fields?.length > 0);

  // Save field sequence
  const handleSaveConfiguration = async () => {
    const selectedFieldIds = _.map(selectedFields, 'id');
    try {
      const response =
        await changeRequestService.storeCRFields(selectedFieldIds);

      if (response.success) {
        if (response.data?.status) {
          setApiMessage('success', response.data?.message);
        } else {
          setApiMessage('error', response.data?.message);
        }
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  return (
    <Box className="download-field-container">
      {/* Page Header */}
      <Box className="page-header">
        <Box className="header-content">
          <Box className="header-text">
            <Typography className="title-sm">
              Field Selection & Change Request Configuration
            </Typography>
            <Typography className="title-text">
              Automatically track changes to stored fields and trigger a change
              request when user data is updated.
            </Typography>
          </Box>

          <Box className="preview-actions">
            <CustomButton
              variant="contained"
              title="Save Configuration"
              startIcon={
                <Icon
                  name="Save"
                  size={16}
                  strokeWidth={2}
                  className="button-icon"
                />
              }
              fullWidth={false}
              onClick={handleSaveConfiguration}
            />
          </Box>
        </Box>
      </Box>
      <>
        {/* Search Bar */}
        <Box className="search-section">
          <Box className="search-section-fields">
            <CustomSearch
              setSearchValue={setSearchTerm}
              searchValue={searchTerm}
              // onKeyPress={handleKeyPress}
            />
          </Box>
        </Box>

        {/* Main Content */}
        <Box className="main-contents">
          {/* Available Fields */}
          <Box className="available-fields">
            <Typography className="body-text section-title">
              Field selection{' '}
              <span className="field-count">({selectedFields?.length})</span>
            </Typography>

            <Box className="categories-container">
              {loaderUser ? (
                <Box className="content-loader no-data-found">
                  <CircularProgress className="loader" color="inherit" />
                </Box>
              ) : (
                <>
                  {filteredCategories?.map((category) => (
                    <FieldCategorySection
                      key={category?.id}
                      category={category}
                      selectedFields={selectedFields}
                      isExpanded={expandedCategories?.includes(category?.id)}
                      onToggleExpansion={toggleCategoryExpansion}
                      onFieldToggle={handleFieldToggle}
                      onSelectAll={handleCategorySelectAll}
                      onDeselectAll={handleCategoryDeselectAll}
                    />
                  ))}
                </>
              )}
            </Box>
          </Box>
        </Box>
      </>

      {/* Action Buttons */}
      {/* <Box className="action-buttons">
        <Box className="preview-actions">
          <button onClick={handlePreviewExport} className="preview-button">
            <Icon
              name="Eye"
              size={16}
              strokeWidth={2}
              className="button-icon"
            />
            Preview Export
          </button>

          <button onClick={handleSaveConfiguration} className="save-button">
            <Icon
              name="Save"
              size={16}
              strokeWidth={2}
              className="button-icon"
            />
            Save Configuration
          </button>
        </Box>

        <Box className="main-actions">
          <button
            onClick={() => navigate('/user-export-dashboard')}
            className="cancel-button"
          >
            Cancel
          </button>

          <button
            onClick={handleGenerateExport}
            disabled={selectedFields.length === 0}
            className="continue-button"
          >
            Continue to Preview
          </button>
        </Box>
      </Box> */}
    </Box>
  );
}
