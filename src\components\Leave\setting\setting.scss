@import '@/styles/variable.scss';

.leave-general-setting {
  .info-icon {
    height: 18px;
    width: 18px;
  }
  .allow-aplying-box-wrap {
    .allow-aplying-box {
      padding: 2px 4px 2px 9px;

      .MuiSvgIcon-root {
        height: 21px;
        width: 21px;
      }
    }
    .document-proof-label-wrap {
      flex-direction: row !important;
      .MuiFormControlLabel-root {
        margin-right: 0;
      }
    }
  }
  .document-proof-wrap {
    .document-proof-input {
      max-width: 120px;
    }
  }
  .btns-wrap {
    gap: 10px;
    .apply-btn {
      padding: 4px 16px !important;

      .MuiTypography-root {
        font-size: 14px !important;
        font-weight: 500 !important;
      }
    }

    .apply-btn {
      background-color: $color-primary !important;
      color: $color-White !important;
      &:hover {
        background-color: $color-White !important ;
        color: $color-primary !important;
      }
    }
  }
}
.leave-approval-wrap {
  .allow-aplying-box-wrap {
    .allow-aplying-box {
      padding: 2px 4px 2px 9px;

      .MuiSvgIcon-root {
        height: 21px;
        width: 21px;
      }
    }
    .document-proof-label-wrap {
      flex-direction: row !important;
      .MuiFormControlLabel-root {
        margin-right: 0;
      }
    }
  }
  .info-icon {
    height: 18px;
    width: 18px;
  }
  .leave-approval-select {
    .select__control {
      padding: 0px;
      border-radius: 4px;
      min-height: 36px;
      max-height: 100px;
      overflow: scroll;
      .select__value-container {
        padding: 0px 10px !important;
        overflow: unset !important;
      }
      .select__indicators {
        .select__clear-indicator {
          padding: 8px 0px;
        }
      }
      .select__placeholder {
        font-size: 14px !important;
      }
    }
  }
  .note-text {
    background-color: $color-light-pink;
    padding: 8px;
    border-radius: 4px;
  }
}
