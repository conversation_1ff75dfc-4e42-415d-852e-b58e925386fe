@import '@/styles/variable.scss';

.org-list-wrap {
  .org-header {
    display: flex;
    justify-content: space-between;

    .filter-input-wrap {
      align-items: flex-end;
      .filter-input {
        .MuiInputBase-root {
          min-height: 30px;

          .MuiInputBase-input {
            padding: 3px 16px;

            &::placeholder {
              font-size: 14px !important;
            }
          }
        }
      }

      .search-btn {
        padding: 3px 12px 3px 8px !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        max-height: max-content;

        .search-icon {
          height: 19px;
          width: 19px;
        }

        &:hover {
          color: $color-secondary !important;
          box-shadow: none !important;

          .search-icon {
            fill: white !important;
          }
        }
      }
    }
  }

  .org-list-table-wrap {
    border: 1px solid $color-Dark-10;
    border-radius: 15px;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;

    .org-table-wrap {
      width: 100%;
      border-collapse: unset;
      min-width: 1160px;

      .MuiTableCell-root {
        padding: 10px !important;
      }

      .id-wrap {
        width: 70px;
      }

      .org-name {
        width: 250px;
      }

      .reg-date {
        width: 130px;
      }

      .org-status {
        width: 80px;
      }
      .plan-type {
        width: 100px;
      }
      .org-current-plan {
        width: 200px;
      }

      .payment-status {
        width: 140px;
      }

      .next-renew-date {
        width: 160px;
      }

      .action {
        width: 80px;
      }

      .table-body-wrap {
        .MuiTableRow-root:last-child {
          .MuiTableCell-root {
            border-bottom: none;
          }
        }

        .image-wrap {
          height: 35px;
          width: 35px;
          border-radius: 100%;
        }

        .avatar {
          font-family: var(--font-family-primary);
          color: #fff;
          width: 35px;
          height: 35px;
          font-size: 14px;

          &.active {
            background-color: $color-medium-sea-green;
          }

          &.pending {
            background-color: $color-blue;
          }

          &.inactive {
            background-color: $error;
          }

          &.default {
            background-color: $color-Primary-90;
          }
        }

        .org-name-wrap {
          color: $color-primary;
        }

        .plan-name {
          color: $color-Dark-80;
        }
        .plan-type {
          text-transform: capitalize;
          font-weight: var(--font-weight-semibold);
          .plan-sub-type {
            font-weight: var(--font-weight-regular);
          }
        }
        .sub-price {
          padding-top: 5px;
          color: $color-Dark-50;
        }

        .sub-emp {
          border: 1px solid $color-Dark-40;
          border-radius: 8px;
          max-width: 200px;
          width: max-content;
          text-align: center;
          margin-top: 5px;
          padding: 0 5px;
          text-transform: uppercase;
        }

        .sub-payment-source {
          color: $color-Dark-30;
        }

        .user-wrap {
          width: 100%;
          max-width: 200px;
        }

        .reg-date {
          font-weight: 500;
        }

        .MuiTableCell-root {
          color: $color-Dark-40;
        }

        .org-status.active {
          text-transform: capitalize;
          .org-status-icon {
            border: 4px solid $color-medium-sea-green;
            border-radius: 100%;
            display: inline-block;
            height: 15px;
            width: 15px;
          }
        }

        .org-status.inactive {
          text-transform: capitalize;
          .org-status-icon {
            border: 4px solid $error;
            border-radius: 100%;
            display: inline-block;
            height: 15px;
            width: 15px;
          }
        }

        .org-status.pending {
          text-transform: capitalize;
          .org-status-icon {
            border: 4px solid $color-blue;
            border-radius: 100%;
            display: inline-block;
            height: 15px;
            width: 15px;
          }
        }

        .next-renew-date {
          color: $error;
          font-weight: 500;
        }

        .edit-icon {
          fill: $color-primary;
          height: 21px;
          width: 21px;
          cursor: pointer;
        }
      }
    }
  }

  @media (max-width: 767px) {
    padding: 20px 20px 56px 20px;
  }
}
