.apply-leave-center {
  margin-top: 0px;
  min-height: calc(100vh - 152px - var(--banner-height));
  background-color: var(--color-white);
  padding: var(--spacing-xxl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-xs);

  .leave-policy-end-date {
    background-color: var(--color-danger-background);
    color: var(--color-danger);
    padding: 5px 10px;
    border: 1px solid var(--color-danger);
    border-radius: 8px;
    margin: 20px 0px;
    .dot-wrap {
      height: 6px;
      width: 6px;
    }
  }
  .apply-leave-title-wrap {
    margin-bottom: 20px;
  }
  .leave-form-wrap {
    .left-side-leave-form-wrap {
      width: 35%;
      max-height: calc(100vh - 108px - var(--banner-height));
      overflow: auto;
      overflow-x: hidden;
      padding-right: 20px;
      border-right: var(--normal-sec-border);

      .date-picker-wrap {
        display: flex;
        gap: var(--spacing-lg);
        justify-content: space-between;
        max-width: 100%;
      }
      .leave-add-inputs-wrap {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
        max-width: calc(100% - 10px);
      }
      .display-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
      }

      .leave-duration-chip {
        gap: 10px;
      }
      .chip-container {
        padding-top: 3px;
        .chip-wrap {
          gap: 10px;
          .textfield-error {
            border: 1px solid var(--color-danger);
          }
        }
      }
      .day-based-container {
        padding: 3px;
        width: 100%;
        background-color: #89cefb4d;
        border-radius: 16px;
        margin-top: 8px;
        margin-bottom: 8px;
        color: var(--color-dark);
        .day-based-wrap {
          gap: 10px;
          width: 100%;
          .day-chip {
            width: calc(50% - 5px);
            border: 0;
          }
          .selected-chip {
            background-color: var(--color-white);
            color: var(--text-color-black);
          }
        }
      }

      .MuiButtonBase-root {
        .MuiChip-label {
          font-size: 13px;
          font-weight: 400;
        }
      }
      .chip-selected {
        background-color: var(--color-chip);
        border-color: var(--color-primary);
      }
      .Mui-error {
        color: var(--color-danger) !important;
      }

      @media (max-width: 991px) {
        max-height: 100%;
        width: 100%;
        border-right: none;
        padding-right: 0px;
        padding-bottom: 20px;
      }
      @media (max-width: 575px) {
        padding-right: 0px;
      }
      .chip-error {
        border-color: var(--border-color-error);
      }
    }
    .right-side-leave-form-wrap {
      width: 65%;
      max-height: calc(100vh - 108px - var(--banner-height));
      overflow: auto;
      overflow-x: hidden;
      padding: 20px 20px 0px 20px;

      .calendar-sec-wrap {
        .calender-leave-wrap {
          height: 100%;
          max-height: calc(100vh - 152px - var(--banner-height));
        }
      }
      @media (max-width: 991px) {
        width: 100%;
        max-height: 100%;
        padding: 15px 0px 0px;
      }
    }

    @media (max-width: 991px) {
      flex-direction: column !important;
    }
  }

  @media (max-width: 991px) {
    max-height: calc(100vh - 108px - var(--banner-height));
    overflow: auto;
    overflow-x: hidden;
  }
  @media (max-width: 575px) {
    padding: 20px 14px 24px;
  }
}
.MuiPickersPopper-root {
  z-index: 99999 !important;
}
