import React, { useState } from 'react';
import { Box, ClickAwayListener, InputAdornment } from '@mui/material';
import { ChromePicker } from 'react-color';
import CustomTextField from '../CustomTextField';
import './colorPicker.scss';

const CustomColorPicker = ({
  label,
  required,
  error,
  helperText,
  value,
  onChange,
  disabled,
  className = '',
  placeholder,
  name,
  id,
}) => {
  const [clrPicker, setClrPicker] = useState(false);

  const handleColorPicker = () => {
    if (!disabled) {
      setClrPicker(!clrPicker);
    }
  };

  return (
    <Box
      className={`custom-colorpicker-wrapper ${className} ${error ? 'Mui-error' : ''}`}
    >
      <ClickAwayListener onClickAway={() => setClrPicker(false)}>
        <Box className="color-picker-section">
          <CustomTextField
            fullWidth
            id={id}
            name={name}
            label={label}
            placeholder={placeholder}
            value={value}
            required={required}
            disabled={disabled}
            error={error}
            helperText={helperText}
            InputProps={{
              readOnly: true,
              startAdornment: (
                <InputAdornment position="start">
                  <Box
                    onClick={handleColorPicker}
                    className="color-picker-icon"
                    style={{
                      background: value,
                    }}
                  />
                </InputAdornment>
              ),
            }}
          />

          <Box className="colorpicker-popover">
            {clrPicker && (
              <ChromePicker
                color={value}
                onChangeComplete={(color) => onChange(color.hex)}
                disableAlpha
              />
            )}
          </Box>
        </Box>
      </ClickAwayListener>
    </Box>
  );
};

export default CustomColorPicker;
