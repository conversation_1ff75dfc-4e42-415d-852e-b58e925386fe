.free-demo-dialog {
  ::-webkit-scrollbar {
    display: none !important;
  }
  .MuiDialog-paper {
    overflow: visible !important;
  }
  .MuiPaper-root {
    width: 100%;
    max-width: 1000px;
    border-radius: 22px;
    margin: 14px;
    position: relative;
    .free-demo-dialog-inner-wrap {
      overflow: auto;
    }
    .free-demo-dialog-inner {
      // position: relative;
    }

    .free-demo-close {
      position: absolute;
      top: -14px;
      right: -14px;
      border-radius: 50%;
      // box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      z-index: 1400 !important;
      background-color: var(--color-white);
      width: 54px;
      height: 54px;
      display: flex;
      justify-content: center;
      align-items: center;
      svg {
        fill: var(--color-white);
        width: 45px;
        height: 45px;
        @media (max-width: 991px) {
          width: 35px;
          height: 35px;
        }
        @media (max-width: 575px) {
          width: 25px;
          height: 25px;
        }
      }

      &:hover {
        cursor: pointer;
      }
      @media (max-width: 991px) {
        width: 45px;
        height: 45px;
      }
      @media (max-width: 575px) {
        width: 35px;
        height: 35px;
        top: -10px;
        right: -8px;
      }
    }
    .dialog-content {
      padding: 0 !important;
      max-height: 90vh;
      overflow-y: scroll;
    }
    .free-demo-wrap {
      padding: 24px;
      // style="mix-blend-mode: color-burn;
      .free-demo-left-content-wrap {
        width: 50%;
        background-repeat: no-repeat;
        background-size: cover;
        background-position: center;
        // background-color: #006BFF;
        padding: 99px 22px;
        border-top-left-radius: 22px;
        border-bottom-left-radius: 22px;
        position: relative;
        z-index: 1;
        .free-demo-left-content {
          .free-demo-left-title {
            padding-bottom: 22px;
            color: var(--text-color-white) !important;
          }

          .free-demo-left-desc {
            font-size: 18px;
            line-height: 26px;
            font-weight: 300;
            font-family: var(--font-family-poly-slim) !important;
            color: var(--text-color-white);
            padding-bottom: 32px;
          }

          .important-points-wrap {
            background-color: var(--color-white);
            border-radius: 22px;
            padding: 32px 42px 16px 32px;

            .important-points-title {
              font-weight: 600;
              font-size: 20px;
              line-height: 20px;
              font-family: var(--font-family-poly-median) !important;
              padding-bottom: 22px;
              color: #1d1e25 !important;
            }

            .important-points-list {
              padding: 0px;

              .list-item {
                font-family: var(--font-family-poly-slim) !important;
                gap: 8px;
                padding: 0px 0px 16px 0px;
                color: rgb(29 30 37 / 80%);
              }
            }
          }
        }

        .free-demo-left-back-svg {
          // position: absolute;
          // top: 0;
          // bottom: 0;
          // right: 0;
          // left: 0;
          // z-index: -1;

          // svg {
          //   border-top-left-radius: 22px;
          //   border-bottom-left-radius: 22px;
          //   width: 100%;
          //   height: 100%;
          // }
        }

        @media (max-width: 991px) {
          display: none;
        }
      }

      .free-demo-right-content-wrap {
        background-color: rgba(146, 146, 157, 0.08);
        border-top-right-radius: 22px;
        border-bottom-right-radius: 22px;
        width: 50%;

        .free-demo-right-title {
          padding: 32px 32px 0px;
          display: none;

          @media (max-width: 991px) {
            display: block;
          }

          @media (max-width: 575px) {
            padding: 14px !important;
          }

          @media (max-width: 374px) {
            font-size: 27px !important;
          }
        }

        .free-demo-right-form-wrap {
          padding: 32px;
          overflow: auto;
          max-height: 770px;
          height: 100%;

          .input-field-wrap {
            .input-wrap {
              margin-bottom: 18px;
              height: 100%;
              min-height: 62px;

              .phone-input-wrap {
                border-bottom: 1px solid #e2e2ea;

                .phone-county-wrap {
                  .MuiFormLabel-root {
                    display: none;
                  }

                  .MuiInputBase-root {
                    border: none;
                    border-radius: 0px;
                    padding-left: 0px;
                    min-height: 40px;
                    background-color: #f6f6f7;

                    .MuiInputAdornment-root {
                      margin: 0px 0px 7px;

                      .MuiInputBase-root {
                        border: none !important;

                        .MuiSelect-select {
                          margin: 0px 30px 0px 0px;
                        }
                      }
                    }

                    .MuiInputBase-input {
                      min-height: 25px !important;
                    }
                  }
                }

                .phone-icon {
                  line-height: 0px;
                  padding: 8px;
                }

                .phone-icon-wrap {
                  line-height: 0px;
                  padding: 8px;

                  svg {
                    path {
                      stroke: var(--color-danger);
                    }
                  }
                }

                &::after {
                  border-bottom: 1px solid green !important;
                }
              }

              .error {
                border-bottom: 1px solid var(--color-danger);
              }

              .error-message {
                font-family: var(--font-family-poly-slim) !important;
                font-weight: 400;
                font-size: 12px;
                line-height: 1.66;
                letter-spacing: 0.03333em;
                text-align: left;
                margin-top: 3px;
              }

              .MuiInputBase-root {
                display: flex;
                align-items: center;

                .eye-wrap {
                  svg {
                    cursor: pointer;
                  }
                }

                .MuiInputBase-input {
                  font-size: 16px;
                  line-height: 26px;
                  font-weight: 400;
                  margin: 5px 0px 10px;
                  height: 100%;
                  min-height: 26px;
                  padding: 0px;
                  color: #1d1e25 !important;
                  font-family: var(--font-family-poly-slim) !important;

                  .image-wrap {
                    height: 24px;
                    width: 24px;
                  }
                  &::placeholder {
                    font-size: 16px !important;
                    line-height: 26px !important;
                    font-weight: 400 !important;
                    margin: 5px 0px 10px !important;
                    padding: 0px !important;
                    font-family: var(--font-family-poly-slim) !important;
                    text-transform: capitalize !important;
                  }
                }

                &::before {
                  border-bottom: 1px solid #e2e2ea;
                }

                &::after {
                  border-bottom: none !important;
                }
              }

              .Mui-error {
                font-family: var(--font-family-poly-slim) !important;

                .eye-wrap {
                  cursor: none !important;

                  path {
                    stroke: var(--color-danger);
                  }

                  svg {
                    fill: none;
                  }
                }

                &::before {
                  border-bottom: 1px solid var(--color-danger);
                }
              }
            }

            @media (max-width: 767px) {
              grid-template-columns: repeat(1, 1fr);
            }
          }

          .submit-btn-wrap {
            padding-top: 14px;

            .submit-btn {
              border-radius: 12px !important;
              background-color: var(--color-primary) !important;
              font-size: 16px !important;
              line-height: 20px !important;
              font-weight: 600 !important;
              padding: 16px !important;
              border: none !important;
              color: #ffffff !important;
              font-family: var(--font-family-poly-median) !important;

              &:hover {
                background-color: var(--color-primary) !important;
                color: white !important;
                box-shadow: none !important;
                font-family: var(--font-family-poly-median) !important;
              }

              @media (max-width: 575px) {
                padding: 12px !important;
              }
            }
          }

          @media (max-width: 991px) {
            max-height: 100%;
          }

          @media (max-width: 575px) {
            padding: 14px;
          }
        }

        @media (max-width: 991px) {
          width: 100%;
          // border-top-right-radius: none;
          // border-bottom-right-radius: none;
          border-radius: 22px;
        }
      }

      @media (max-width: 991px) {
        flex-direction: column;
      }

      @media (max-width: 575px) {
        padding: 14px;
      }
      @media (max-width: 991px) {
        padding: 15px;
      }
    }
    @media (max-width: 991px) {
      max-width: 550px;
    }
    @media (max-width: 767px) {
      max-width: 450px;
    }
  }
}
