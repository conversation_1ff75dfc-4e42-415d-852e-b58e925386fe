'use client';
import React, { useEffect, useState } from 'react';
import * as Yup from 'yup';
import { Box, Typography, Dialog, DialogContent } from '@mui/material';
import { Formik, Form } from 'formik';
import { CustomTextField } from '@/components/UI/CommonField';
import CustomButton from '@/components/UI/button';
import CustomSelect from '@/components/UI/selectbox';
import CloseIcon from '@mui/icons-material/Close';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { ORG_URLS } from '@/helper/constants/urls';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { identifiers } from '@/helper/constants/identifier';
import './identifierwrap.scss';

const validationSchema = Yup.object({
  provider_name: Yup.string().required('Provider name is required'),
  provider_identifier: Yup.string().required('Provider identifier is required'),
  provider_status: Yup.string().required('Provider status is required'),
});

export default function PaymentIdentifier({
  open,
  handleClose,
  userId,
  setUserId,
  getPaymetProviderDetails,
  search,
}) {
  const [paymentProvData, setPaymentProvData] = useState([]);

  const getPaymentProviderDetailsById = async () => {
    try {
      const { status, data } = await axiosInstance.get(
        ORG_URLS?.GET_PAYMENT_PROVIDER_BY_ID + `/${userId}`
      );
      if (status === 200) {
        setPaymentProvData(data?.data);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  useEffect(() => {
    if (userId) {
      getPaymentProviderDetailsById();
    } else {
      setPaymentProvData([]);
    }
  }, [userId]);

  return (
    <Box className="identifier-wrap">
      <Dialog open={open} onClose={handleClose} maxWidth="xs" fullWidth>
        <DialogContent>
          <Formik
            enableReinitialize
            initialValues={{
              provider_name: paymentProvData?.provider_name || '',
              provider_identifier: paymentProvData?.provider_identifier || '',
              provider_status: paymentProvData?.provider_status || 'active',
            }}
            validationSchema={validationSchema}
            onSubmit={async (requestedData) => {
              let sendData = {
                provider_name: requestedData?.provider_name,
                ...(!userId && {
                  provider_identifier: requestedData?.provider_identifier,
                }),
                provider_status: requestedData?.provider_status,
              };
              try {
                const { status, data } = userId
                  ? await axiosInstance.put(
                      ORG_URLS?.UPDATE_PAYMENT_PROVIDER + `/${userId}`,
                      sendData
                    )
                  : await axiosInstance.post(
                      ORG_URLS?.CREATE_NEW_PAYMENT_PROVIDER,
                      sendData
                    );
                if (status === 200 || status === 201) {
                  setApiMessage('success', data?.message);
                  setUserId('');
                  handleClose();
                  getPaymetProviderDetails(search);
                  setPaymentProvData([]);
                } else {
                  setApiMessage('error', data?.message);
                }
              } catch (error) {
                setApiMessage('error', error?.response?.data?.message);
              }
            }}
          >
            {({
              values,
              handleChange,
              handleBlur,
              touched,
              errors,
              setFieldValue,
            }) => (
              <Form>
                <Box className="d-flex justify-space-between align-center">
                  <Typography className="header-text" variant="h5">
                    {userId
                      ? 'Update Payment Identifier'
                      : 'Payment Identifier'}
                  </Typography>
                  <CloseIcon
                    onClick={() => {
                      handleClose();
                    }}
                    sx={{ cursor: 'pointer' }}
                  />
                </Box>
                <Box className="form-wrap display-grid">
                  <Box className="sub-plan-wraps w100 pt32">
                    <CustomTextField
                      InputLabelProps={{ shrink: true }}
                      id="provider_name"
                      label="Provider Name*"
                      name="provider_name"
                      placeholder="Enter provider name"
                      value={values?.provider_name || ''}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={
                        touched?.provider_name && Boolean(errors?.provider_name)
                      }
                      helperText={
                        touched?.provider_name && errors?.provider_name
                      }
                      className="w100 sub-plan-input-wrap"
                      variant="filled"
                      fullWidth
                    />
                  </Box>

                  <Box className="sub-plan-wraps w100 pt32">
                    <CustomTextField
                      InputLabelProps={{ shrink: true }}
                      id="provider_identifier"
                      label="Provider Identifier*"
                      name="provider_identifier"
                      placeholder="Enter provider identifier"
                      value={values?.provider_identifier}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={
                        touched?.provider_identifier &&
                        Boolean(errors?.provider_identifier)
                      }
                      helperText={
                        touched?.provider_identifier &&
                        errors?.provider_identifier
                      }
                      className="w100 sub-plan-input-wrap"
                      variant="filled"
                      fullWidth
                      disabled={userId}
                    />
                  </Box>

                  <Box className="pt32">
                    <CustomSelect
                      className={`selected-wrap ${
                        touched?.provider_status && errors?.provider_status
                          ? 'error-border'
                          : ''
                      }`}
                      placeholder="Select Status"
                      options={identifiers?.PROVIDER_STATUS}
                      value={values?.provider_status}
                      name="provider_status"
                      onChange={(e) =>
                        setFieldValue('provider_status', e?.target?.value)
                      }
                      label={
                        <span
                          className={
                            touched?.provider_status && errors?.provider_status
                              ? 'error-label'
                              : ''
                          }
                        >
                          Provider Status*
                        </span>
                      }
                    />
                    {touched?.provider_status && errors?.provider_status && (
                      <Typography
                        variant="body2"
                        color="error"
                        className="field-error"
                      >
                        {errors?.provider_status}
                      </Typography>
                    )}
                  </Box>
                </Box>
                <Box className="pt32 d-flex justify-end create-plan-btn-wrap">
                  <CustomButton
                    className="p16 create-plan-btn"
                    type="submit"
                    fontWeight="600"
                    variant="contained"
                    background="#39596e"
                    backgroundhover="#39596e"
                    colorhover="#FFFFFF"
                    title={userId ? 'Update plan' : 'Create plan'}
                  />
                </Box>
              </Form>
            )}
          </Formik>
        </DialogContent>
      </Dialog>
    </Box>
  );
}
