const conversions = {
  // Weight (base: g)
  g: { type: 'weight', factor: 1 },
  kg: { type: 'weight', factor: 1000 },
  oz: { type: 'weight', factor: 28.3495 },
  lb: { type: 'weight', factor: 453.592 },

  // Volume (base: ml)
  ml: { type: 'volume', factor: 1 },
  l: { type: 'volume', factor: 1000 },
  liter: { type: 'volume', factor: 1000 },
  tsp: { type: 'volume', factor: 4.92892 },
  tbsp: { type: 'volume', factor: 14.7868 },
  cup: { type: 'volume', factor: 236.588 },
  pint: { type: 'volume', factor: 473.176 },
  quart: { type: 'volume', factor: 946.353 },
  gallon: { type: 'volume', factor: 3785.41 },
  pinch: { type: 'volume', factor: 0.3 },

  // Piece (base: unit)
  unit: { type: 'piece', factor: 1 },
  each: { type: 'piece', factor: 1 },
  clove: { type: 'piece', factor: 1 },
  ball: { type: 'piece', factor: 1 },
  slice: { type: 'piece', factor: 1 },
  serving: { type: 'piece', factor: 1 },
  leaf: { type: 'piece', factor: 1 },
  wedge: { type: 'piece', factor: 1 },
  sprig: { type: 'piece', factor: 1 },
  pack: { type: 'piece', factor: 1 },
  case: { type: 'piece', factor: 1 },
  dozen: { type: 'piece', factor: 12 },
};

export const convertCost = (cost, fromUnitSlug, toUnitSlug, density = 1) => {
  const fromUnit = conversions[fromUnitSlug?.toLowerCase()];
  const toUnit = conversions[toUnitSlug?.toLowerCase()];

  if (!fromUnit || !toUnit) return cost;

  const sameType = fromUnit.type === toUnit.type;

  if (sameType) {
    // Convert within the same type (e.g., kg -> g or l -> ml)
    const costPerBase = cost / fromUnit.factor;
    return costPerBase * toUnit.factor;
  }

  // Cross conversion between weight <-> volume using density
  const isWeightToVolume =
    fromUnit.type === 'weight' && toUnit.type === 'volume';
  const isVolumeToWeight =
    fromUnit.type === 'volume' && toUnit.type === 'weight';

  if (isWeightToVolume || isVolumeToWeight) {
    // Convert cost to base unit
    // const costPerBase = cost / fromUnit.factor;

    // Convert via density bridge (g <-> ml)
    // If weight → volume: g / density = ml
    // If volume → weight: ml * density = g
    const weightInG = isWeightToVolume ? 1 : density;
    const volumeInMl = isWeightToVolume ? density : 1;

    const crossFactor =
      (toUnit.factor / fromUnit.factor) * (volumeInMl / weightInG);

    return cost * crossFactor;
  }

  // Incompatible types (e.g., weight -> piece)
  return cost;
};
