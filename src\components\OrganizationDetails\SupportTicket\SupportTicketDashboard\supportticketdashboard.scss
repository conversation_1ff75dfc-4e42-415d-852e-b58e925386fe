// Support Ticket Dashboard Styles - Using global variables only

.support-ticket-dashboard-wrap {

  .support-ticket-dashboard {
    // Header Section
    &__header {
      margin-bottom: var(--spacing-xl);
    }

    // Statistics Grid - 4 columns
    &__stats-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: var(--spacing-lg);
      margin-bottom: var(--spacing-xl);

      @media (max-width: 1200px) {
        grid-template-columns: repeat(2, 1fr);
      }

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
      }
    }

    // Content Grid - 2 columns for Recent and Urgent tickets
    &__content-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-xl);

      @media (max-width: 1024px) {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
      }
    }

    // Recent Tickets Section
    &__recent-tickets {
      background-color: var(--color-white);
      border-radius: var(--border-radius-lg);
      box-shadow: var(--box-shadow-xs);
      border: var(--normal-sec-border);

      // Override height constraint to allow natural content flow
      .recent-tickets {
        height: auto !important;
        min-height: 400px;
      }
    }

    // Urgent Tickets Section
    &__urgent-tickets {
      background-color: var(--color-white);
      border-radius: var(--border-radius-lg);
      box-shadow: var(--box-shadow-xs);
      border: var(--normal-sec-border);

      // Override height constraint to allow natural content flow
      .urgent-tickets {
        height: auto !important;
        min-height: 400px;
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 1024px) {
  .support-ticket-dashboard {
    padding: var(--spacing-lg);

    &__header {
      margin-bottom: var(--spacing-lg);
    }

    &__stats-grid {
      margin-bottom: var(--spacing-lg);
    }
  }
}

@media (max-width: 768px) {
  .support-ticket-dashboard {
    padding: var(--spacing-md);

    &__header {
      margin-bottom: var(--spacing-md);
    }

    &__stats-grid {
      margin-bottom: var(--spacing-md);
    }

    &__content-grid {
      gap: var(--spacing-md);
    }
  }
}
