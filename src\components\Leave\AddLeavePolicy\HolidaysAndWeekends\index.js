import React, { useEffect, useContext } from 'react';
import { Formik, Form } from 'formik';
// import * as Yup from 'yup';
import { Box, Tooltip, Typography } from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import AuthContext from '@/helper/authcontext';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomCheckbox from '@/components/UI/CustomCheckbox';
import CustomRadio from '@/components/UI/CustomRadio';
import './holidayweekend.scss';

export default function HolidaysAndWeekends({
  setLeavePolicyDetails,
  formikRefHoliday,
  leavePolicyDetails,
  setLeavePolicySubmit,
  leavePolicySubmit,
  LeaveStep4Payload,
  leavePolicyBack,
  setLeavePolicyBack,
}) {
  const { authState } = useContext(AuthContext);

  const { holidaysAndWeekends } = leavePolicyDetails || {};

  const holidayData =
    holidaysAndWeekends?.holiday_between_leave_period_data || {};
  const weekendData =
    holidaysAndWeekends?.weekend_between_leave_period_data || {};

  const isHour =
    authState?.generalSeetings?.leave_period_type &&
    authState?.generalSeetings?.leave_period_type === 'hour'
      ? true
      : false;
  useEffect(() => {
    setLeavePolicySubmit({
      ...leavePolicySubmit,
      holidaysAndWeekends: false,
    });
    setLeavePolicyBack({
      ...leavePolicyBack,
      holidaysAndWeekends: false,
    });
  }, []);
  const Inputype = (e) => {
    let value = e.target.value;
    if (isHour) {
      {
        // Remove non-numeric characters
        if (value === '' || /^\d*\.?\d{0,2}$/.test(value)) {
          e.target.value = value;
        } else {
          e.target.value = value.slice(0, -1);
        }
      }
    } else {
      {
        e.target.value = e.target.value.replace(/[^0-9]/g, ''); // Remove non-numeric characters
      }
    }
  };

  return (
    <Formik
      innerRef={formikRefHoliday}
      enableReinitialize
      initialValues={{
        holidaysDontCount:
          holidaysAndWeekends?.holiday_between_leave_period || 'dont_count',
        AccompayingDays: holidayData?.holiday_accompaying_days || 0,
        sandwichRuleDuration: holidayData?.configure_sandwich_days || 0,
        sandwichAfterHoliday:
          holidayData?.apply_sandwich_after_holiday || false,
        sandwichBeforeHoliday:
          holidayData?.apply_sandwich_before_holiday || false,
        leaveBetweenHoliday:
          holidayData?.apply_sandwich_between_holiday || false,
        weekendsDontCount:
          holidaysAndWeekends?.weekend_between_leave_period || 'dont_count',
        sandwichRuleDaysForWeekend: weekendData?.weekend_accompaying_days || 0,
        sandwichRuleDurWeekend: weekendData?.configure_sandwich_days || 0,
        sandwichAfterWeekend:
          weekendData?.apply_sandwich_after_weekend || false,
        sandwichBeforeWeekend:
          weekendData?.apply_sandwich_before_weekend || false,
        leaveBetweenWeekend:
          weekendData?.apply_sandwich_between_weekend || false,
      }}
      // validationSchema={Yup.object().shape({
      //   // holidaysCountAfterDays: Yup.number()
      //   //   .typeError('Days must be a number')
      //   //   .min(0, 'Days cannot be negative')
      //   //   .nullable(),
      //   // weekendsCountAfterDays: Yup.number()
      //   //   .typeError('Days must be a number')
      //   //   .min(0, 'Days cannot be negative')
      //   //   .nullable(),
      // })}
      onSubmit={(values) => {
        setLeavePolicyDetails((prevState) => ({
          ...prevState,
          holidaysAndWeekends: LeaveStep4Payload(values),
        }));
      }}
    >
      {({
        values,
        errors,
        touched,
        handleChange,
        handleBlur,
        setFieldValue,
      }) => (
        <Form>
          <Box className="holidays-weekends-wrap">
            <Box>
              <Typography className="sub-content-text">
                Holidays Between Leave Period
              </Typography>
              <Box className="holidays-weekends-box-wrap mt4">
                <Box className="d-flex align-center gap-sm">
                  <CustomRadio
                    disableRipple
                    name="holidaysDontCount"
                    checked={values?.holidaysDontCount === 'dont_count'}
                    onChange={() => {
                      setFieldValue('holidaysDontCount', 'dont_count');
                      setFieldValue('AccompayingDays', 0);
                      setFieldValue('sandwichRuleDuration', 0);
                      setFieldValue('sandwichAfterHoliday', false);
                      setFieldValue('leaveBetweenHoliday', false);
                      setFieldValue('sandwichBeforeHoliday', false);
                    }}
                    label={
                      <Typography className="sub-content-text">
                        Don't count as a leave
                      </Typography>
                    }
                  />
                  <Tooltip
                    arrow
                    title={
                      <Typography>
                        This option implies that Holidays will not be included
                        while taking leave.
                      </Typography>
                    }
                    classes={{ tooltip: 'info-tooltip-container' }}
                  >
                    <InfoIcon className="info-icon cursor-pointer" />
                  </Tooltip>
                </Box>
                <Box className="d-flex align-center">
                  <Box className="count-as-leave-wrap">
                    <CustomRadio
                      disableRipple
                      name="holidaysDontCount"
                      checked={values?.holidaysDontCount === 'count_as_leave'}
                      onChange={() => {
                        setFieldValue('holidaysDontCount', 'count_as_leave');
                        setFieldValue('AccompayingDays', 0);
                        setFieldValue('sandwichRuleDuration', 0);
                        setFieldValue('sandwichAfterHoliday', false);
                        setFieldValue('leaveBetweenHoliday', true);
                        setFieldValue('sandwichBeforeHoliday', false);
                      }}
                      label={
                        <Typography className="sub-content-text">
                          Would you like to count holiday as leave ?
                        </Typography>
                      }
                    />
                  </Box>
                </Box>
                {values?.holidaysDontCount === 'count_as_leave' && (
                  <>
                    <Box className="pt16 d-flex align-center gap-10 flex-wrap">
                      <Typography className="title-text">
                        Holidays accompanying leave is treated as leave when
                        total number of leave includes
                      </Typography>
                      <Box>
                        <CustomTextField
                          fullWidth
                          id="AccompayingDays"
                          name="AccompayingDays"
                          value={values?.AccompayingDays}
                          placeholder="Days"
                          error={Boolean(
                            touched?.AccompayingDays && errors?.AccompayingDays
                          )}
                          helperText={
                            touched?.AccompayingDays && errors?.AccompayingDays
                          }
                          onBlur={handleBlur}
                          onChange={handleChange}
                          onInput={Inputype}
                        />
                      </Box>
                      <Box className="d-flex align-center gap-5">
                        <Typography className="title-text">
                          leave days.
                        </Typography>
                        <Tooltip
                          arrow
                          title={
                            <Typography>
                              This option implies that holidays will be
                              considered as a part of applied leave. (i.e.)
                              holiday between leave days, leave before/after
                              holiday.
                            </Typography>
                          }
                          classes={{ tooltip: 'info-tooltip-container' }}
                        >
                          <InfoIcon className="info-icon cursor-pointer" />
                        </Tooltip>
                      </Box>
                    </Box>
                    <Box className="pt16 d-flex align-center gap-10 flex-wrap">
                      <Typography className="title-text">
                        Configure the sandwich rule to consider
                      </Typography>
                      <Box>
                        <CustomTextField
                          fullWidth
                          id="sandwichRuleDuration"
                          name="sandwichRuleDuration"
                          value={values?.sandwichRuleDuration}
                          placeholder="Days"
                          error={Boolean(
                            touched?.sandwichRuleDuration &&
                              errors?.sandwichRuleDuration
                          )}
                          helperText={
                            touched?.sandwichRuleDuration &&
                            errors?.sandwichRuleDuration
                          }
                          onBlur={handleBlur}
                          onChange={handleChange}
                          onInput={Inputype}
                        />
                      </Box>
                      <Box className="d-flex align-center gap-5">
                        <Typography className="title-text">
                          days before and after the leave date or range for any
                          sandwich leave.
                        </Typography>
                        <Tooltip
                          arrow
                          title={
                            <Typography>
                              Let's say an employee takes a vacation leave from
                              May 15th to May 20th. With the sandwich rule
                              configured to consider a 10-day period before and
                              after the leave date, it would impact the
                              calculation of sandwich leaves in the following
                              scenarios:
                              <br />
                              <br />
                              1. Before Leave Date (May 5th to May 14th): <br />{' '}
                              The system will check if there is any holiday
                              immediately preceding the leave. If found, the
                              system will consider the 14th day as a sandwich
                              leave. However, if there is any gap between the
                              holiday and the leave, the chain will be broken,
                              and it will not be considered a sandwich leave.
                              same thing will work for after 10 days
                            </Typography>
                          }
                          classes={{ tooltip: 'info-tooltip-container' }}
                        >
                          <InfoIcon className="info-icon cursor-pointer" />
                        </Tooltip>
                      </Box>
                    </Box>
                    <Box className="pt8">
                      <Box>
                        <CustomCheckbox
                          checked={values?.sandwichAfterHoliday}
                          onChange={(e) => {
                            setFieldValue(
                              'sandwichAfterHoliday',
                              e?.target?.checked
                            );
                            setFieldValue('leaveBetweenHoliday', false);
                          }}
                          disableRipple
                          label={
                            <Typography className="sub-content-text">
                              Apply sandwich leave after holiday
                            </Typography>
                          }
                          disabled={values?.leaveBetweenHoliday}
                        />
                      </Box>
                      <Box>
                        <CustomCheckbox
                          checked={values?.sandwichBeforeHoliday}
                          onChange={(e) => {
                            setFieldValue(
                              'sandwichBeforeHoliday',
                              e?.target?.checked
                            );
                            setFieldValue('leaveBetweenHoliday', false);
                          }}
                          disableRipple
                          label={
                            <Typography className="sub-content-text">
                              Apply sandwich leave before holiday
                            </Typography>
                          }
                          disabled={values?.leaveBetweenHoliday}
                        />
                      </Box>
                      <Box>
                        <CustomCheckbox
                          checked={values?.leaveBetweenHoliday}
                          onChange={(e) =>
                            setFieldValue(
                              'leaveBetweenHoliday',
                              e?.target?.checked
                            )
                          }
                          disableRipple
                          label={
                            <Typography className="sub-content-text">
                              Apply sandwich leave between holiday
                            </Typography>
                          }
                          disabled={
                            values?.sandwichAfterHoliday ||
                            values?.sandwichBeforeHoliday
                          }
                        />
                      </Box>
                    </Box>
                  </>
                )}
              </Box>
            </Box>
            <Box className=" mt16">
              <Typography className="sub-content-text">
                Weekend Between Leave Period
              </Typography>
              <Box className="mt4">
                <Box className="d-flex align-center gap-sm">
                  <CustomRadio
                    disableRipple
                    name="weekendsDontCount"
                    checked={values?.weekendsDontCount === 'dont_count'}
                    onChange={() => {
                      setFieldValue('weekendsDontCount', 'dont_count');
                      setFieldValue('sandwichRuleDaysForWeekend', 0);
                      setFieldValue('sandwichRuleDurWeekend', 0);
                      setFieldValue('sandwichAfterWeekend', false);
                      setFieldValue('sandwichBeforeWeekend', false);
                      setFieldValue('leaveBetweenWeekend', false);
                    }}
                    label={
                      <Typography className="sub-content-text">
                        Don't count as a leave
                      </Typography>
                    }
                  />
                  <Tooltip
                    arrow
                    title={
                      <Typography>
                        This option implies that Weekends will not be included
                        while taking leave.
                      </Typography>
                    }
                    classes={{ tooltip: 'info-tooltip-container' }}
                  >
                    <InfoIcon className="info-icon cursor-pointer" />
                  </Tooltip>
                </Box>
                <Box className="d-flex align-center">
                  <Box>
                    <CustomRadio
                      disableRipple
                      name="weekendsDontCount"
                      checked={values?.weekendsDontCount === 'count_as_leave'}
                      onChange={() => {
                        setFieldValue('weekendsDontCount', 'count_as_leave');
                        setFieldValue('sandwichRuleDaysForWeekend', 0);
                        setFieldValue('sandwichRuleDurWeekend', 0);
                        setFieldValue('sandwichAfterWeekend', false);
                        setFieldValue('sandwichBeforeWeekend', false);
                        setFieldValue('leaveBetweenWeekend', true);
                      }}
                      label={
                        <Typography className="sub-content-text">
                          Would you like to count weekend as leave?
                        </Typography>
                      }
                    />
                  </Box>
                </Box>
                {values?.weekendsDontCount === 'count_as_leave' && (
                  <>
                    <Box className="pt16 d-flex align-center gap-10 flex-wrap">
                      <Typography className="title-text">
                        Weekends accompanying leave is treated as leave when
                        total number of leave includes
                      </Typography>
                      <Box>
                        <CustomTextField
                          fullWidth
                          id="sandwichRuleDaysForWeekend"
                          name="sandwichRuleDaysForWeekend"
                          value={values?.sandwichRuleDaysForWeekend}
                          placeholder="Days"
                          error={Boolean(
                            touched?.sandwichRuleDaysForWeekend &&
                              errors?.sandwichRuleDaysForWeekend
                          )}
                          helperText={
                            touched?.sandwichRuleDaysForWeekend &&
                            errors?.sandwichRuleDaysForWeekend
                          }
                          onBlur={handleBlur}
                          onChange={handleChange}
                          onInput={Inputype}
                        />
                      </Box>
                      <Box className="d-flex align-center gap-5">
                        <Typography className="title-text">
                          leave days.
                        </Typography>
                        <Tooltip
                          arrow
                          title={
                            <Typography>
                              This option implies that weekends will be
                              considered as a part of applied leave. (i.e.)
                              holiday between leave days, leave before/after
                              holiday.
                            </Typography>
                          }
                          classes={{ tooltip: 'info-tooltip-container' }}
                        >
                          <InfoIcon className="info-icon cursor-pointer" />
                        </Tooltip>
                      </Box>
                    </Box>
                    <Box className="pt16 d-flex align-center gap-10 flex-wrap">
                      <Typography className="title-text">
                        Configure the sandwich rule to consider
                      </Typography>
                      <Box>
                        <CustomTextField
                          fullWidth
                          id="sandwichRuleDurWeekend"
                          name="sandwichRuleDurWeekend"
                          value={values?.sandwichRuleDurWeekend}
                          placeholder="Days"
                          error={Boolean(
                            touched?.sandwichRuleDurWeekend &&
                              errors?.sandwichRuleDurWeekend
                          )}
                          helperText={
                            touched?.sandwichRuleDurWeekend &&
                            errors?.sandwichRuleDurWeekend
                          }
                          onBlur={handleBlur}
                          onChange={handleChange}
                          onInput={Inputype}
                        />
                      </Box>
                      <Box className="d-flex align-center gap-5">
                        <Typography className="titile-text">
                          days before and after the leave date or range for any
                          sandwich leave.
                        </Typography>
                        <Tooltip
                          arrow
                          title={
                            <Typography>
                              Let's say an employee takes a vacation leave from
                              May 15th to May 20th. With the sandwich rule
                              configured to consider a 10-day period before and
                              after the leave date, it would impact the
                              calculation of sandwich leaves in the following
                              scenarios:
                              <br />
                              <br />
                              1. Before Leave Date (May 5th to May 14th): <br />{' '}
                              The system will check if there is any holiday
                              immediately preceding the leave. If found, the
                              system will consider the 14th day as a sandwich
                              leave. However, if there is any gap between the
                              holiday and the leave, the chain will be broken,
                              and it will not be considered a sandwich leave.
                              same thing will work for after 10 days
                            </Typography>
                          }
                          classes={{ tooltip: 'info-tooltip-container' }}
                        >
                          <InfoIcon className="info-icon cursor-pointer" />
                        </Tooltip>
                      </Box>
                    </Box>
                    <Box className="pt8">
                      <Box>
                        <CustomCheckbox
                          checked={values?.sandwichAfterWeekend}
                          onChange={(e) => {
                            setFieldValue(
                              'sandwichAfterWeekend',
                              e?.target?.checked
                            );
                            setFieldValue('leaveBetweenWeekend', false);
                          }}
                          disableRipple
                          label={
                            <Typography className="sub-content-text">
                              Apply sandwich leave after weekend
                            </Typography>
                          }
                          disabled={values?.leaveBetweenWeekend}
                        />
                      </Box>
                      <Box>
                        <CustomCheckbox
                          checked={values?.sandwichBeforeWeekend}
                          onChange={(e) => {
                            setFieldValue(
                              'sandwichBeforeWeekend',
                              e?.target?.checked
                            );
                            setFieldValue('leaveBetweenWeekend', false);
                          }}
                          disableRipple
                          label={
                            <Typography className="sub-content-text">
                              Apply sandwich leave before weekend
                            </Typography>
                          }
                          disabled={values?.leaveBetweenWeekend}
                        />
                      </Box>
                      <Box>
                        <CustomCheckbox
                          checked={values?.leaveBetweenWeekend}
                          onChange={(e) => {
                            {
                              setFieldValue(
                                'leaveBetweenWeekend',
                                e?.target?.checked
                              );
                              setFieldValue('sandwichBeforeWeekend', false);
                              setFieldValue('sandwichAfterWeekend', false);
                            }
                          }}
                          disableRipple
                          label={
                            <Typography className="sub-content-text">
                              Apply sandwich leave between weekend
                            </Typography>
                          }
                          disabled={
                            values?.sandwichBeforeWeekend ||
                            values?.sandwichAfterWeekend
                          }
                        />
                      </Box>
                    </Box>
                  </>
                )}
              </Box>
            </Box>
          </Box>
        </Form>
      )}
    </Formik>
  );
}
