.chart-widget {
  background-color: var(--color-white);
  border-radius: var(--border-radius-lg);
  border: var(--border-width-xs) solid #e5e7eb;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  overflow: hidden;

  // Fullscreen modifier
  &--fullscreen {
    position: fixed;
    inset: var(--spacing-lg);
    z-index: 50;
    border-radius: var(--border-radius-md);
  }

  // Header Section
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-bottom: var(--border-width-xs) solid #f3f4f6;
    background-color: var(--color-white);

    @media (max-width: 768px) {
      flex-direction: column;
      gap: var(--spacing-md);
      align-items: flex-start;
    }
  }

  &__header-info {
    flex: 1;
  }

  &__title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-lg);
    color: #111827;
    margin-bottom: var(--spacing-tiny);
  }

  &__subtitle {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: #6b7280;
  }

  // Options and Controls
  &__options {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);

    @media (max-width: 768px) {
      width: 100%;
      justify-content: flex-end;
    }
  }

  &__controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
  }

  &__option-btn {
    background: transparent;
    border: none;
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-xs);
    color: #6b7280;
    cursor: pointer;
    transition: all 0.15s ease-out;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: var(--font-family-primary);

    &:hover {
      color: var(--color-primary);
    }

    &--active {
      background-color: var(--color-primary-opacity);
      color: var(--color-primary);
    }
  }

  &__list-view-btn,
  &__fullscreen-btn {
    background: transparent;
    border: none;
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-xs);
    color: #6b7280;
    cursor: pointer;
    transition: all 0.15s ease-out;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: var(--font-family-primary);

    &:hover {
      color: var(--color-primary);
    }
  }

  &__list-view-btn {
    margin-right: var(--spacing-xs);
  }

  // Content Section
  &__content {
    padding: var(--spacing-md);
    height: 16rem;
    position: relative;
    display: flex;
    flex-direction: column;

    .chart-widget--fullscreen & {
      height: calc(100vh - 8rem);
    }

    @media (min-width: 640px) {
      padding: var(--spacing-lg);
      height: 18rem;
    }

    @media (min-width: 1024px) {
      height: 20rem;
    }

    // Dynamic height for funnel charts based on data
    &.chart-widget__content--funnel {
      height: auto;
      min-height: 16rem;
      max-height: 35rem;

      @media (min-width: 640px) {
        min-height: 18rem;
        max-height: 40rem;
      }

      @media (min-width: 1024px) {
        min-height: 20rem;
        max-height: 45rem;
      }
    }
  }

  // Empty State
  &__empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    padding: var(--spacing-lg);
  }

  &__empty-icon {
    color: #d1d5db;
    margin-bottom: var(--spacing-md);
  }

  &__empty-text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    color: #6b7280;
    margin-bottom: var(--spacing-xs);
  }

  &__empty-subtext {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: #9ca3af;
    margin: 0;
  }

  &__content {
    // Ensure charts are responsive
    .recharts-wrapper {
      width: 100% !important;
      height: 100% !important;
      min-height: 250px !important;
    }

    .recharts-responsive-container {
      width: 100% !important;
      height: 100% !important;
      min-height: 250px !important;
      flex: 1 !important;
    }

    // Heatmap and funnel charts responsive styling
    .chart-widget__heatmap-grid,
    .chart-widget__funnel-grid {
      height: 100%;
      overflow-y: auto;
      flex: 1;
    }
  }

  // Footer Section
  &__footer {
    padding: var(--spacing-md) var(--spacing-lg);
    border-top: var(--border-width-xs) solid #f3f4f6;
    background-color: #f9fafb;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: var(--font-size-sm);

    @media (max-width: 768px) {
      flex-direction: column;
      gap: var(--spacing-md);
      align-items: flex-start;
    }
  }

  // Legend Section
  &__legend {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);

    @media (max-width: 768px) {
      flex-wrap: wrap;
      gap: var(--spacing-md);
    }
  }

  &__legend-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }

  &__legend-color {
    width: var(--spacing-md);
    height: var(--spacing-md);
    border-radius: var(--border-radius-full);
    flex-shrink: 0;
  }

  &__legend-text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: #6b7280;
    font-weight: var(--font-weight-regular);
  }

  // Export Button
  &__export-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--color-primary);
    cursor: pointer;
    background: transparent;
    border: none;
    transition: color 0.15s ease-out;
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-regular);

    &:hover {
      color: var(--color-dark-blue);
    }
  }

  &__export-text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-regular);
  }

  // Heatmap Styles
  &__heatmap-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-sm);
    height: 100%;

    @media (max-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  &__heatmap-cell {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-lg);
  }

  &__heatmap-hour,
  &__heatmap-day {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
  }

  &__heatmap-value {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-bold);
    margin-top: var(--spacing-tiny);
  }

  // Funnel Chart Styles
  &__funnel-grid {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    height: 100%;
    justify-content: flex-start;
    padding: 0;
    margin: 0;
    min-height: fit-content;

    // Dynamic sizing based on number of stages
    &[data-stages="1"],
    &[data-stages="2"],
    &[data-stages="3"] {
      gap: var(--spacing-lg);
    }

    &[data-stages="4"],
    &[data-stages="5"] {
      gap: var(--spacing-md);
    }

    &[data-stages="6"],
    &[data-stages="7"],
    &[data-stages="8"] {
      gap: var(--spacing-sm);
    }
  }

  &__funnel-stage {
    position: relative;
    min-height: 60px;
    margin: 0;
    padding: var(--spacing-sm);
    background-color: #fafafa;
    border-radius: var(--border-radius-sm);
    // Remove hover effects for Conversion Analytics
    // transition: all 0.2s ease;

    // &:hover {
    //   background-color: #f5f5f5;
    //   transform: translateX(2px);
    // }
  }

  &__funnel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-xs);
    min-height: 20px; // Ensure header is always visible
  }

  &__funnel-stage-name {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: #111827;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &__funnel-stage-percent {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-regular);
    color: #6b7280;
    margin-left: var(--spacing-sm);
  }

  &__funnel-bar-bg {
    width: 100%;
    height: var(--spacing-md);
    background-color: #e5e7eb;
    border-radius: var(--border-radius-full);
    overflow: hidden;
    position: relative;
  }

  &__funnel-bar {
    height: var(--spacing-md);
    border-radius: var(--border-radius-full);
    transition: width 1s ease-out;
    min-width: 2px; // Ensure bar is always visible
  }

  &__funnel-count {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    color: #6b7280;
    margin-top: var(--spacing-tiny);
    margin-bottom: 0;
  }

  // Fullscreen dialog content
  &__fullscreen-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    height: 100%;
    overflow: hidden;
    padding: 0;
    margin: 0;
  }

  &__fullscreen-chart {
    flex: 1;
    background: var(--color-white);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    border: var(--normal-sec-border);
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 0;

    @media (min-width: 768px) {
      padding: var(--spacing-lg);
    }
  }

  &__fullscreen-chart-container {
    width: 100%;
    height: 100%;
    min-height: 0;
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: column;

    // Ensure charts are responsive in dialog
    .recharts-wrapper {
      width: 100% !important;
      height: 100% !important;
      min-height: 400px !important;
    }

    .recharts-responsive-container {
      width: 100% !important;
      height: 100% !important;
      min-height: 400px !important;
      flex: 1 !important;
    }

    // Heatmap and funnel charts responsive styling
    .chart-widget__heatmap-grid,
    .chart-widget__funnel-grid {
      height: 100%;
      overflow-y: auto;
      margin: 0;
      padding: 0;
      flex: 1;
    }

    // Remove hover effects from heatmap cells in modal
    .chart-widget__heatmap-cell {
      transition: none !important;
      cursor: default !important;

      &:hover {
        transform: none !important;
      }
    }
  }

  &__fullscreen-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--color-light-gray);
    border-radius: var(--border-radius-md);
    border: var(--normal-sec-border);

    @media (max-width: 767px) {
      flex-direction: column;
      gap: var(--spacing-md);
    }
  }
}

// Chart fullscreen dialog styling
.chart-fullscreen-dialog {
  .MuiDialog-paperScrollPaper {
    max-width: 95vw !important;
    max-height: 95vh !important;
    width: 95vw !important;
    height: 95vh !important;
    margin: 0 !important;
    display: flex !important;
    flex-direction: column !important;
    padding: var(--spacing-lg) !important;
    top: 0 !important;
    bottom: 0 !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    position: fixed !important;

    @media (min-width: 768px) {
      max-width: 90vw !important;
      max-height: 90vh !important;
      width: 90vw !important;
      height: 90vh !important;
    }

    @media (min-width: 1024px) {
      max-width: 85vw !important;
      max-height: 85vh !important;
      width: 85vw !important;
      height: 85vh !important;
    }
  }

  .MuiDialogContent-root {
    padding: var(--spacing-lg) !important;
    padding-bottom: var(--spacing-lg) !important;
    overflow: hidden !important;
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    margin: 0 !important;
    min-height: 0 !important;
    height: 100% !important;
  }

  .MuiDialogTitle-root {
    padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-md) var(--spacing-lg) !important;
    font-family: var(--font-family-primary) !important;
    font-size: var(--font-size-lg) !important;
    font-weight: var(--font-weight-semibold) !important;
    color: var(--text-color-primary) !important;
    border-bottom: var(--normal-sec-border) !important;
    flex-shrink: 0 !important;
    margin: 0 !important;
  }

  // Remove any default margins/paddings that might cause bottom space
  .MuiDialog-container {
    padding: 0 !important;
  }

  .MuiBackdrop-root {
    background-color: rgba(0, 0, 0, 0.5) !important;
  }
}
