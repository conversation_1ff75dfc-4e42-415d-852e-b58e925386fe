import React from 'react';
import ContactForm from './ContactForm';
import ContactInfo from './ContactInfo';
import CustomCtaLink from './CustomCtaLink';
import { trackCtaClick, submitContactForm } from '@/services/recipeService';
import { setApiMessage } from '@/helper/common/commonFunctions';
import './ContactCard.scss';

const ContactCard = ({ recipeData }) => {
  const contactSettings =
    recipeData?.organization_settings?.publicRecipeCallToAction;

  // Don't render if no contact settings are available or if none is explicitly set
  if (!contactSettings || contactSettings.none) {
    return null;
  }

  const handleFormSubmit = async (formValues, { setSubmitting, resetForm }) => {
    try {
      setSubmitting(true);

      // Prepare payload according to required structure
      const payload = {
        recipe_id: recipeData?.id,
        recipe_name:
          recipeData?.recipe_public_title || recipeData?.recipe_title,
        name: formValues?.name,
        email: formValues?.email,
        mobile: formValues?.phone,
        message: formValues?.message,
      };

      // Call the API
      const data = await submitContactForm(payload);

      // Show success message from API if available, else fallback
      setApiMessage('success', data?.message);

      // Reset form
      resetForm();
    } catch (error) {
      // Handle error
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        'Failed to send message. Please try again.';
      setApiMessage('error', errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  const handleCustomCtaClick = async () => {
    // Determine CTA type based on current contact settings
    let ctaType = 'custom_cta';
    if (contactSettings?.contactForm) {
      ctaType = 'contact_form';
    } else if (contactSettings?.contactInfo?.enabled) {
      ctaType = 'contact_info';
    } else if (contactSettings?.customCtaLink?.enabled) {
      ctaType = 'custom_cta';
    }

    // Prepare data according to required structure - only 4 fields
    const ctaData = {
      recipe_id: recipeData?.id,
      recipe_name: recipeData?.recipe_public_title || recipeData?.recipe_title,
      cta_type: ctaType,
      cta_text:
        contactSettings?.customCtaLink?.text ||
        contactSettings?.contactInfo?.name ||
        'Get Recipe Details',
    };

    // Track CTA click for analytics - API call only
    try {
      await trackCtaClick(ctaData);

      // Show success message if needed
      // setApiMessage('success', 'CTA click tracked successfully');

      // Navigate to the provided link after successful tracking
      if (contactSettings?.customCtaLink?.link) {
        // Add https:// if protocol is missing
        let link = contactSettings.customCtaLink.link;
        if (!link.startsWith('http://') && !link.startsWith('https://')) {
          link = 'https://' + link;
        }

        // Open link in new tab
        window.open(link, '_blank', 'noopener,noreferrer');
      }
    } catch (error) {
      // Check if error has message property and display it
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        'Failed to track CTA click';
      setApiMessage('error', errorMessage);

      // Still navigate to link even if tracking fails
      if (contactSettings?.customCtaLink?.link) {
        let link = contactSettings.customCtaLink.link;
        if (!link.startsWith('http://') && !link.startsWith('https://')) {
          link = 'https://' + link;
        }
        window.open(link, '_blank', 'noopener,noreferrer');
      }
    }
  };

  // Handler for tracking CTA clicks (shared for form and info)
  const handleTrackCta = async (ctaTypeOverride) => {
    let ctaType = ctaTypeOverride || 'custom_cta';
    if (ctaTypeOverride === 'contact_form') {
      ctaType = 'contact_form';
    } else if (ctaTypeOverride === 'contact_info') {
      ctaType = 'contact_info';
    } else if (contactSettings?.contactForm) {
      ctaType = 'contact_form';
    } else if (contactSettings?.contactInfo?.enabled) {
      ctaType = 'contact_info';
    } else if (contactSettings?.customCtaLink?.enabled) {
      ctaType = 'custom_cta';
    }
    const ctaData = {
      recipe_id: recipeData?.id,
      recipe_name: recipeData?.recipe_public_title || recipeData?.recipe_title,
      cta_type: ctaType,
      cta_text:
        contactSettings?.customCtaLink?.text ||
        contactSettings?.contactInfo?.name ||
        'Get Recipe Details',
    };
    try {
      await trackCtaClick(ctaData);
    } catch {
      // Optionally handle error, but do not block UI
    }
  };

  // Priority order: contactForm > contactInfo > customCtaLink
  // Render Contact Form (Highest Priority) - only if enabled
  if (contactSettings?.contactForm) {
    return (
      <ContactForm
        handleFormSubmit={handleFormSubmit}
        onTrackCta={() => handleTrackCta('contact_form')}
        recipeData={recipeData}
      />
    );
  }

  // Render Contact Info (Second Priority) - only if enabled and has actual contact data
  if (
    contactSettings?.contactInfo?.enabled &&
    (contactSettings?.contactInfo?.name ||
      contactSettings?.contactInfo?.phone ||
      contactSettings?.contactInfo?.email ||
      contactSettings?.contactInfo?.link)
  ) {
    const contactInfo = contactSettings.contactInfo;
    return (
      <ContactInfo
        contactInfo={contactInfo}
        onTrackCta={() => handleTrackCta('contact_info')}
      />
    );
  }

  // Render Custom CTA Link (Lowest Priority) - only if enabled and has actual link/text data
  if (
    contactSettings?.customCtaLink?.enabled &&
    contactSettings?.customCtaLink?.text &&
    contactSettings?.customCtaLink?.link
  ) {
    const ctaLink = contactSettings?.customCtaLink;
    return (
      <CustomCtaLink
        ctaLink={ctaLink}
        handleCustomCtaClick={handleCustomCtaClick}
      />
    );
  }

  return null;
};

export default ContactCard;
