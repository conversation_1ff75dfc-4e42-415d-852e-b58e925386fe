'use client';

import React, { useEffect, useState, useContext } from 'react';
import { Box, CircularProgress, Typography, Tooltip } from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import CustomPagination from '@/components/UI/customPagination';
import { DateFormat, setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
// import VisibilityIcon from '@mui/icons-material/Visibility';
import AuthContext from '@/helper/authcontext';
import { useRouter } from 'next/navigation';
import { fetchFromStorage, saveToStorage } from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import DialogBox from '../../UI/Modalbox';
import { findDifferences } from '@/helper/common/commonFunctions';
import ViewIcon from '../../ActionIcons/ViewIcon';
import NoDataView from '@/components/UI/NoDataView';

export default function ActivityLogs({ dsrId }) {
  const { userdata, setUserdata } = useContext(AuthContext);
  const [activityList, setActivityList] = useState([{ id: '' }]);
  const [activityDetails, setActivityDetails] = useState();
  const [loader, setLoader] = useState(true);
  const [toggleModal, setToggleModal] = useState(false);
  const [userAgentValue, setUserAgentValue] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const router = useRouter();
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const getActivityDetails = async (pageNo, Rpp) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_WSR_ACTIVITY_LOG +
          `${dsrId}?page=${pageNo}&size=${Rpp ? Rpp : rowsPerPage}`
      );

      if (status === 200) {
        const activityData =
          data?.data &&
          data?.data?.wsr_activity &&
          data?.data?.wsr_activity?.length > 0 &&
          data?.data?.wsr_activity?.map((a) => {
            let newdata = JSON.parse(a?.new_data);
            let previous_data = JSON.parse(a?.previous_data);
            if (a?.activity_action !== 'created') {
              var diffs = findDifferences(previous_data, newdata);
            }
            return {
              ...a,
              // branch_name: newdata?.branch_name ? newdata?.branch_name : '-',
              new_data: newdata,
              previous_data: previous_data,
              differenceData:
                a?.activity_action !== 'created' ? diffs : newdata,
              username: a?.users?.user_full_name,
              department_name: newdata?.department_name
                ? newdata?.department_name
                : '-',
            };
          });
        setCurrentPage(data?.page);
        setTotalCount(data?.count);
        setActivityDetails(data?.data);
        activityData ? setActivityList(activityData) : setActivityList([]);
        setLoader(false);
      }
    } catch (error) {
      setLoader(false);
      setActivityList([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const columns = [
    {
      field: 'id',
      headerName: 'ID',
      width: 100,
      minWidth: 100,
      flex: 0,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
    },
    {
      field: 'createdAt',
      headerName: 'Date & Time',
      width: 200,
      minWidth: 200,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-start h100">
            <Typography className="title-text text-ellipsis">
              {DateFormat(params?.value, 'datesWithhourUTC')}
            </Typography>
          </Box>
        );
      },
    },
    // {
    //   field: 'createdAta',
    //   headerName: 'Branch name',
    //   width: 200,
    //   minWidth: 200,
    //   flex: 1,
    //   sortable: false,
    //   headerAlign: 'start',
    //   align: 'start',
    //   renderCell: (params) => {
    //     return (
    //       <Box className="d-flex align-center justify-start h100">
    //         <Typography className="title-text text-ellipsis">Norwich</Typography>
    //       </Box>
    //     );
    //   }
    // },
    {
      field: 'username',
      headerName: 'Submitted by',
      width: 200,
      minWidth: 200,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-start h100">
            <Typography className="title-text text-ellipsis">
              {params?.value}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'activity_action',
      headerName: 'Activity actions',
      width: 200,
      minWidth: 200,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box className="text-ellipsis">
            {params?.row?.activity_table ? params?.row?.activity_table : ''}{' '}
            {params?.row?.activity_action ? params?.row?.activity_action : ''}
          </Box>
        );
      },
    },
    {
      field: 'userAgent',
      headerName: 'Info',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center actions justify-center h100">
            <Tooltip
              title={<Typography>Info</Typography>}
              arrow
              classes={{
                tooltip: 'info-tooltip-container ',
              }}
            >
              <Box>
                <ViewIcon
                  className="cursor-pointer"
                  onClick={() => {
                    setToggleModal(!toggleModal);
                    setUserAgentValue({
                      userAgent: params?.value,
                      differenceData: params?.row?.differenceData,
                      rowdata: params?.row,
                    });
                  }}
                />
              </Box>
            </Tooltip>
          </Box>
        );
      },
    },
  ];

  const onPageChange = (newPage) => {
    setCurrentPage(newPage);
    getActivityDetails(newPage);
  };
  const OnRowPerPage = (newPage) => {
    setRowsPerPage(newPage);
    setCurrentPage(1);
    getActivityDetails(1, newPage);
  };
  useEffect(() => {
    getActivityDetails(1);
  }, []);
  const oldDSR = fetchFromStorage(identifiers?.RedirectData)
    ? fetchFromStorage(identifiers?.RedirectData)
    : userdata;

  useEffect(() => {
    if (oldDSR?.IsFromUser === undefined && oldDSR?.page !== undefined) {
      setUserdata({ ...userdata, IsFromUser: true });
      saveToStorage(identifiers?.RedirectData, {
        ...oldDSR,
        IsFromUser: true,
      });
    }
  }, [oldDSR]);

  return (
    <Box className="user-activity">
      <Box className="table-container">
        <ArrowBackIosIcon
          className="mb8 cursor-pointer"
          onClick={() => {
            setTimeout(() => {
              router?.push('/wsr');
            }, 1000);
          }}
        />
        {!loader && (
          <>
            <Box>
              {activityDetails?.wsr_start_date && (
                <Box className="d-flex align-center">
                  <Typography className="title-text fw400">
                    <span className="fw600">Start Date : </span>
                    <span>
                      {DateFormat(activityDetails?.wsr_start_date, 'dates')}
                    </span>
                  </Typography>
                </Box>
              )}
              {activityDetails?.wsr_end_date && (
                <Box className="d-flex align-center">
                  <Typography className="title-text fw400">
                    <span className="fw600">End Date : </span>
                    <span>
                      {DateFormat(activityDetails?.wsr_end_date, 'dates')}
                    </span>
                  </Typography>
                </Box>
              )}
              {activityDetails?.wsr_branch && (
                <Box className="d-flex align-center pt4">
                  <Typography className="title-text fw400">
                    <span className="fw600"> Branch : </span>
                    <span>{activityDetails?.wsr_branch}</span>
                  </Typography>
                </Box>
              )}

              {activityDetails?.wsr_user && (
                <Box className="d-flex align-center pt4">
                  <Typography className="title-text fw400">
                    <span className="fw600"> Submitted By : </span>
                    <span>{activityDetails?.wsr_user}</span>
                  </Typography>
                </Box>
              )}
            </Box>
          </>
        )}

        {loader ? (
          <Box className="content-loader pt16">
            <CircularProgress className="loader" color="inherit" />
          </Box>
        ) : activityList && activityList?.length === 0 ? (
          <Box className="no-data d-flex align-center justify-center">
            <NoDataView
              title="No Activity Records Found"
              description="There is no Activity data available at the moment."
            />
          </Box>
        ) : (
          <Box className="table-container table-layout pt24">
            <DataGrid
              rows={activityList}
              columns={columns}
              pageSize={rowsPerPage}
              checkboxSelection={false}
              disableSelectionOnClick
              hideMenuIcon
            />
            <CustomPagination
              currentPage={currentPage}
              totalCount={totalCount}
              rowsPerPage={rowsPerPage}
              onPageChange={onPageChange}
              OnRowPerPage={OnRowPerPage}
            />
          </Box>
        )}
      </Box>
      <DialogBox
        open={toggleModal}
        handleClose={() => {
          setToggleModal(!toggleModal);
          setUserAgentValue('');
        }}
        title="Details"
        className="staff-dialogbox"
        content={
          <Box>
            {userAgentValue?.userAgent && (
              <>
                <Typography className="body-text fw600 text-underline">
                  User Agent
                </Typography>
                <Typography className="title-text pb8 pt8">
                  {userAgentValue?.userAgent}
                </Typography>
              </>
            )}
            {userAgentValue?.rowdata &&
            (userAgentValue?.rowdata?.ip_address ||
              userAgentValue?.rowdata?.location ||
              userAgentValue?.rowdata?.address) ? (
              <Typography className="body-text fw600 text-underline pt16 pb8">
                Location Details
              </Typography>
            ) : (
              <></>
            )}

            {userAgentValue?.rowdata && (
              <>
                {userAgentValue?.rowdata?.ip_address && (
                  <Box className="d-flex align-center">
                    <Typography className="title-text fw400 user-date">
                      <span className="fw600">IP : </span>
                      <span>{userAgentValue?.rowdata?.ip_address}</span>
                    </Typography>
                  </Box>
                )}
                {userAgentValue?.rowdata?.location && (
                  <Box className="d-flex align-center">
                    <Typography className="title-text fw400 user-date">
                      <span className="fw600">Location : </span>
                      <span>{userAgentValue?.rowdata?.location}</span>
                    </Typography>
                  </Box>
                )}
                {userAgentValue?.rowdata?.address && (
                  <Box className="d-flex align-center pb8">
                    <Typography className="title-text fw400 user-date">
                      <span className="fw600">address : </span>
                      <span>{userAgentValue?.rowdata?.address}</span>
                    </Typography>
                  </Box>
                )}
              </>
            )}
            {userAgentValue?.differenceData && (
              <Typography className="body-text fw600 text-underline pt16">
                Updated Details
              </Typography>
            )}
            {userAgentValue?.differenceData &&
              Object.entries(userAgentValue?.differenceData) &&
              Object.entries(userAgentValue?.differenceData)?.map(
                ([key, value]) => (
                  <div key={key}>
                    <Typography className="title-text fw600 pb4 pt16">
                      {key ? key?.replace(/_/g, ' ') : ''}
                    </Typography>
                    {value?.oldValue &&
                      userAgentValue?.rowdata?.activity_action !==
                        'created' && (
                        <Box className="d-flex align-center">
                          <Typography className="title-text fw400 user-date">
                            <span className="">Old data: </span>
                            <span>
                              {key === 'updatedAt' || key === 'createdAt'
                                ? value?.oldValue
                                  ? DateFormat(
                                      value?.oldValue,
                                      'datesWithhourUTC'
                                    )
                                  : '-'
                                : value?.oldValue
                                  ? value?.oldValue
                                  : '-'}
                            </span>
                          </Typography>
                        </Box>
                      )}
                    {(value?.newValue ||
                      (userAgentValue?.rowdata?.activity_action === 'created' &&
                        value)) && (
                      <Box className="d-flex align-center">
                        <Typography className="title-text fw400 user-date">
                          <span className="">New data: </span>
                          {userAgentValue?.rowdata?.activity_action ===
                          'created' ? (
                            <span>
                              {' '}
                              {key === 'updatedAt' || key === 'createdAt'
                                ? value
                                  ? DateFormat(value, 'datesWithhourUTC')
                                  : '-'
                                : value
                                  ? value
                                  : '-'}
                            </span>
                          ) : (
                            <span>
                              {' '}
                              {key === 'updatedAt' || key === 'createdAt'
                                ? value?.newValue
                                  ? DateFormat(
                                      value?.newValue,
                                      'datesWithhourUTC'
                                    )
                                  : '-'
                                : value?.newValue
                                  ? value?.newValue
                                  : '-'}
                            </span>
                          )}
                        </Typography>
                      </Box>
                    )}
                  </div>
                )
              )}
          </Box>
        }
      />
    </Box>
  );
}
