'use client';

import React, { useContext } from 'react';
import { Box, Typography } from '@mui/material';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { useDropzone } from 'react-dropzone';
import CustomButton from '@/components/UI/CustomButton';
import AuthContext from '@/helper/authcontext';

export default function FileUpload({
  name,
  handleChangeData,
  index,
  ViewAccessOnly,
  Subname,
  PdfUploadOnly,
  isRequire,
}) {
  const { planDetail, authState, setRestrictedLimitModal } =
    useContext(AuthContext);

  const acceptFilesType = PdfUploadOnly
    ? {
        'application/pdf': [],
      }
    : {
        'application/pdf': [],
        'image/*': [],
      };

  const { getRootProps, getInputProps } = useDropzone({
    accept: acceptFilesType,
    multiple: false,
    onDrop: (acceptedFile, rejectedFiles) => {
      // Check if storage is full
      const totalStorage = planDetail?.total_storage || 0;
      const usedStorage = authState?.subscriptionUsage?.total_size_gb || 0;
      const fileSizeInGB = acceptedFile[0]?.size / (1024 * 1024 * 1024); // Convert bytes to GB

      if (usedStorage + fileSizeInGB > totalStorage) {
        setRestrictedLimitModal({
          storageLimit: true,
          totalStorage: planDetail?.total_storage,
          usedStorage: authState?.subscriptionUsage?.total_size_gb,
        });
        return;
      }

      const maxSize = 5 * 1024 * 1024; // 5MB in bytes

      const largeFiles = acceptedFile.filter((file) => file.size > maxSize);
      if (rejectedFiles.length > 0) {
        setApiMessage('error', 'Please upload PDF/Image files only.');
        // setError();
      } else if (largeFiles.length > 0) {
        setApiMessage('error', 'File size should be less than 5MB.');
      } else {
        if (acceptedFile && acceptedFile[0]) {
          handleChangeData(acceptedFile, index);
        }
      }
    },
  });

  return (
    <Box className="upload-upload-sec rtwc-upload h100">
      <Box className="title-section">
        {' '}
        <Box className="title">
          <Typography className="body-text fw600">
            {' '}
            {name}
            {isRequire && <span className="color-red">*</span>}
          </Typography>
          {Subname && (
            <Typography className="sub-title-text">{Subname}</Typography>
          )}
        </Box>
        <div {...getRootProps()} className="dropzone">
          <input {...getInputProps()} />
          <CustomButton
            variant="contained"
            title={'Upload'}
            fullWidth={false}
            disabled={ViewAccessOnly}
          />
        </div>
      </Box>
    </Box>
  );
}
