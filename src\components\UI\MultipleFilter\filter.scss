@import '@/styles/variable.scss';

// .select-input-wrap {
//   z-index: 99999 !important;
//   animation-fill-mode: none !important;

//   .select-all-wrap {
//     padding: 5px;
//     color: $color-primary;
//     font-size: 14px;
//     cursor: pointer;
//   }

//   [class*='__selected'] {
//     background-color: $color-platinum !important;
//     box-shadow: none !important;

//     [class*='__selected'] {
//       &::before {
//         background-color: $color-primary;
//         border-color: $color-primary !important;
//       }
//     }
//   }

//   .___SSlider_v6mlw_gg_ {
//     background-color: $color-primary !important;
//     width: 4px;
//   }

//   .apply-btn {
//     background-color: $color-primary !important;
//   }
// }

.multiple-filter-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-bottom: 8px;
  max-width: 300px;
  width: 100%;
  .multiple-filter-chip {
    background: var(--color-primary-opacity);
    border: var(--normal-sec-border);
    padding: var(--spacing-xs) var(--spacing-none);
    width: max-content;

    span {
      font-size: var(--font-size-xxs);
      color: var(--text-color-primary);
      font-family: var(--font-family-primary);
    }
    .MuiChip-deleteIcon {
      color: var(--color-primary);
      font-size: var(--icon-size-xxs);
    }
  }
}
