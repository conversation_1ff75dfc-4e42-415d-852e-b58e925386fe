'use client';

import React, { useState, useEffect, useContext } from 'react';
import { Box, Typography, CircularProgress, Tooltip } from '@mui/material';
import { DataGrid, gridClasses } from '@mui/x-data-grid';
import CustomPagination from '@/components/UI/customPagination';
import CustomSelect from '@/components/UI/CustomSelect';
import AddIcon from '@mui/icons-material/Add';
import CustomButton from '@/components/UI/CustomButton';
import DialogBox from '@/components/UI/Modalbox';
import Searchbar from '@/components/UI/CustomSearch';
import { useRouter } from 'next/navigation';
import TuneIcon from '@mui/icons-material/Tune';
import CustomDatePicker from '@/components/UI/CustomDatePicker';
import { fetchFromStorage, saveToStorage } from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import AuthContext from '@/helper/authcontext';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import dayjs from 'dayjs';
import moment from 'moment';
import EditIcon from '../../ActionIcons/EditIcon';
import ActivityLogIcon from '../../ActionIcons/ActivityLogIcon';
import RequestIcon from '../../ActionIcons/RequestIcon';
import NoDataView from '@/components/UI/NoDataView';
import BranchDepartmentDisplay from '@/components/BranchDepartmentDisplay';

export default function ExpensePage({
  loader,
  branchList,
  getDsrData,
  dsrData,
  totalCount,
  setFilterData,
  setFilterDataApplied,
  filterData,
  filterDataApplied,
  rowsPerPage,
  setRowsPerPage,
}) {
  const { authState, userdata, setUserdata } = useContext(AuthContext);

  const [searchValue, setSearchValue] = useState('');
  const [page, setPage] = useState(1);
  const [filter, setFilter] = useState(false);
  const [DsrExist, setISDsr] = useState(false);
  const [currency, setCurrency] = useState();

  const router = useRouter();

  const renderDetailStatus = (value) => {
    const statusMap = {
      deleted: 'failed',
      draft: 'draft',
      default: 'success',
    };
    const statusClass = statusMap[value] || statusMap.default;
    return (
      <Typography className={`sub-title-text ${statusClass} fw600`}>
        {value}
      </Typography>
    );
  };

  const columns = [
    {
      field: 'id',
      headerName: 'ID',
      width: 48,
      minWidth: 48,
      flex: 0,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100">
            <Typography className="title-text text-ellipsis">
              <span>{params?.value}</span>
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'expense_month',
      headerName: 'Month',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100">
            {params?.value + '-' + params?.row?.expense_year}
          </Box>
        );
      },
    },
    {
      field: 'branchz',
      headerName: 'Branch name',
      width: 200,
      minWidth: 200,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <BranchDepartmentDisplay
            row={{ branch: params?.row?.branch }}
            isBranchOnly={true}
          />
        );
      },
    },
    {
      field: 'amount',
      headerName: `Amount ( ${currency?.symbol ? currency?.symbol : '£'} )`,
      width: 150,
      minWidth: 150,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-start h100 gap-sm">
            <Typography className="title-text bg-amount-transparent text-ellipsis">
              <span>{params?.value}</span>
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'submitedby',
      headerName: 'Submitted by',
      width: 200,
      minWidth: 200,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100">
            <Typography className="title-text text-ellipsis">
              <span>{params?.row?.expense_user?.user_full_name}</span>
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'updatedby',
      headerName: 'Updated by',
      width: 200,
      minWidth: 200,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100">
            <Typography className="title-text text-ellipsis">
              <span>{params?.value}</span>
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'expense_detail_status',
      headerName: 'Status',
      width: 150,
      minWidth: 150,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100 text-capital">
            {renderDetailStatus(params?.value)}
          </Box>
        );
      },
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 120,
      minWidth: 120,
      maxWidth: 120,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box className="d-flex  actions dsr-action">
            <Box className="d-flex actions-dsr">
              <Tooltip
                title={<Typography>Edit</Typography>}
                arrow
                classes={{
                  tooltip: 'info-tooltip-container ',
                }}
              >
                <Box>
                  <EditIcon
                    onClick={() => {
                      setUserdata({
                        id: params?.row?.id,
                        filterData: filterDataApplied,
                        searchValue: searchValue,
                        page: page,
                        payroll: true,
                        rowsPerPage: rowsPerPage,
                      });
                      saveToStorage(identifiers?.RedirectData, {
                        id: params?.row?.id,
                        filterData: filterDataApplied,
                        searchValue: searchValue,
                        page: page,
                        payroll: true,
                        rowsPerPage: rowsPerPage,
                      });
                      router.push(`/payroll/${params?.row?.id}?isEdit=true`);
                    }}
                    className="edit-icon"
                  />
                </Box>
              </Tooltip>
              {authState?.UserPermission?.activity_log &&
              authState?.web_user_active_role_id !== 7 &&
              authState?.web_user_active_role_id !== 14 ? (
                <Tooltip
                  title={<Typography>Activity logs</Typography>}
                  arrow
                  classes={{
                    tooltip: 'info-tooltip-container ',
                  }}
                >
                  <Box>
                    <ActivityLogIcon
                      // className="svg-icon"
                      onClick={() => {
                        setUserdata({
                          id: params?.row?.id,
                          filterData: filterDataApplied,
                          searchValue: searchValue,
                          page: page,
                          payroll: true,
                          rowsPerPage: rowsPerPage,
                        });
                        saveToStorage(identifiers?.RedirectData, {
                          id: params?.row?.id,
                          filterData: filterDataApplied,
                          searchValue: searchValue,
                          page: page,
                          payroll: true,
                          rowsPerPage: rowsPerPage,
                        });
                        router.push(
                          `/payroll/${params?.row?.id}?isActivity=true`
                        );
                      }}
                    />
                    {/* {ActivityIcon()} */}
                    {/* </Box> */}
                  </Box>
                </Tooltip>
              ) : (
                <></>
              )}
              {authState?.UserPermission?.dsr === 2 &&
              authState?.web_user_active_role_id !== 7 &&
              authState?.web_user_active_role_id !== 14 &&
              params?.row?.has_request ? (
                <Tooltip
                  title={<Typography>Expense request</Typography>}
                  arrow
                  classes={{
                    tooltip: 'info-tooltip-container ',
                  }}
                >
                  <Box>
                    <RequestIcon
                      // className="svg-icon"
                      onClick={() => {
                        setUserdata({
                          id: params?.row?.id,
                          filterData: filterDataApplied,
                          searchValue: searchValue,
                          page: page,
                          rowsPerPage: rowsPerPage,
                          payroll: true,
                        });
                        saveToStorage(identifiers?.RedirectData, {
                          id: params?.row?.id,
                          filterData: filterDataApplied,
                          searchValue: searchValue,
                          page: page,
                          rowsPerPage: rowsPerPage,
                          payroll: true,
                        });
                        router.push(
                          `/payroll/${params?.row?.id}?isRequest=true`
                        );
                      }}
                    />
                    {/* {RequestIcon()} */}
                    {/* </Box> */}
                  </Box>
                </Tooltip>
              ) : (
                <></>
              )}
            </Box>
          </Box>
        );
      },
    },
  ];

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      setPage(1);
      getDsrData(
        searchValue,
        1,
        filterDataApplied?.branch,
        filterDataApplied?.sdate,
        filterDataApplied?.edate
      );
    }
  };
  const onPageChange = (newPage) => {
    setPage(newPage);
    getDsrData(
      searchValue,
      newPage,
      filterDataApplied?.branch,
      filterDataApplied?.sdate,
      filterDataApplied?.edate
    );
  };
  const OnRowPerPageDSR = (newPage) => {
    setRowsPerPage(newPage);
    setPage(1);
    getDsrData(
      searchValue,
      1,
      filterDataApplied?.branch,
      filterDataApplied?.sdate,
      filterDataApplied?.edate,
      newPage
    );
  };
  useEffect(() => {
    if (
      fetchFromStorage(identifiers?.RedirectData) &&
      fetchFromStorage(identifiers?.RedirectData)?.IsFromUser &&
      fetchFromStorage(identifiers?.RedirectData)?.payroll
    ) {
      const fdata = fetchFromStorage(identifiers?.RedirectData);
      setPage(fdata?.page);
      setFilterData(fdata?.filterData);
      setFilterDataApplied(fdata?.filterData);
      setSearchValue(fdata?.searchValue);
      setRowsPerPage(fdata?.rowsPerPage);
      getDsrData(
        fdata?.searchValue,
        fdata?.page,
        fdata?.filterData?.branch,
        fdata?.filterData?.sdate,
        fdata?.filterData?.edate
      );
    } else if (userdata && userdata?.IsFromUser && userdata?.payroll) {
      const fdata = userdata;
      setPage(fdata?.page);
      setFilterData(fdata?.filterData);
      setFilterDataApplied(fdata?.filterData);
      setSearchValue(fdata?.searchValue);
      setRowsPerPage(fdata?.rowsPerPage);
      getDsrData(
        fdata?.searchValue,
        fdata?.page,
        fdata?.filterData?.branch,
        fdata?.filterData?.sdate,
        fdata?.filterData?.edate
      );
    }
  }, [
    fetchFromStorage(identifiers?.RedirectData)?.IsFromUser,
    userdata?.IsFromUser,
  ]);
  const DSRCheck = async (id) => {
    // setCheckLoader(true);
    const sendData = {
      branch_id: id,
      // dsr_date: moment(Date()).format('YYYY-MM-DD'),
      expense_month: moment(Date()).format('MM'),
      expense_year: moment(Date()).format('YYYY'),
    };
    try {
      const { status, data } = await axiosInstance.post(
        URLS?.GET_EXPENSE_CHECK,
        sendData
      );
      if (status === 200 || status === 202) {
        if (data?.status) {
          setISDsr(true);
        } else {
          setISDsr(false);
        }
      }
    } catch {
      // setCheckLoader(false);
      setISDsr(false);
    }
  };
  useEffect(() => {
    if (authState?.branch?.id) {
      DSRCheck(authState?.branch?.id);
    }
  }, [authState?.branch?.id]);
  useEffect(() => {
    authState?.currency_details && setCurrency(authState?.currency_details);
  }, [authState]);
  return (
    <>
      <Box className="d-flex gap-sm align-center flex-wrap justify-end">
        <Searchbar
          setSearchValue={setSearchValue}
          searchValue={searchValue}
          onKeyPress={handleKeyPress}
        />
        <CustomButton
          variant="outlined"
          isIconOnly
          startIcon={
            <Tooltip
              title={
                <Typography className="sub-title-text">Apply Filter</Typography>
              }
              classes={{
                tooltip: 'info-tooltip-container',
              }}
              arrow
            >
              <TuneIcon />
            </Tooltip>
          }
          onClick={() => {
            setFilter(!filter);
          }}
        />

        <CustomButton
          variant="outlined"
          title="Apply filter"
          fullWidth={false}
          onClick={() => {
            setPage(1);
            getDsrData(
              searchValue,
              1,
              filterDataApplied?.branch,
              filterDataApplied?.sdate,
              filterDataApplied?.edate
            );
          }}
        />
        {authState?.UserPermission?.dsr === 2 && (
          <CustomButton
            variant="contained"
            title="Add Expense"
            startIcon={<AddIcon />}
            fullWidth={false}
            onClick={() => {
              if (
                !DsrExist &&
                (authState?.web_user_active_role_id === 7 ||
                  authState?.web_user_active_role_id === 14)
              ) {
                setApiMessage(
                  'error',
                  'The Expense of current month already exists. '
                );
              } else {
                setUserdata({
                  filterData: filterDataApplied,
                  searchValue: searchValue,
                  page: page,
                  payroll: true,
                  rowsPerPage: rowsPerPage,
                });
                saveToStorage(identifiers?.RedirectData, {
                  filterData: filterDataApplied,
                  searchValue: searchValue,
                  page: page,
                  payroll: true,
                  rowsPerPage: rowsPerPage,
                });
                router.push('/payroll/addexpenses');
              }
            }}
          />
        )}
      </Box>

      <Box className="table-container table-layout">
        {loader ? (
          <Box className="content-loader">
            <CircularProgress className="loader" color="inherit" />
          </Box>
        ) : (
          <>
            {dsrData && dsrData?.length === 0 ? (
              <Box className="no-data d-flex align-center justify-center">
                <NoDataView
                  // image
                  title="No Expenses Found"
                  description="There is no expenses available at the moment."
                />
              </Box>
            ) : (
              <>
                <DataGrid
                  rows={dsrData}
                  columns={columns}
                  pageSize={rowsPerPage}
                  checkboxSelection={false} // Disable default checkbox column
                  disableSelectionOnClick // Disable row selection on click
                  columnVisibilityModel={{
                    actions:
                      authState?.UserPermission?.dsr === 2 ? true : false,
                  }}
                  hideMenuIcon
                  getRowHeight={() => 'auto'}
                  sx={{
                    [`& .${gridClasses.cell}`]: {
                      py: 1,
                    },
                  }}
                />
                <CustomPagination
                  currentPage={page}
                  // totalPages={totalPages}
                  totalCount={totalCount}
                  rowsPerPage={rowsPerPage}
                  onPageChange={onPageChange}
                  OnRowPerPage={OnRowPerPageDSR}
                />
              </>
            )}
          </>
        )}
      </Box>
      <DialogBox
        open={filter}
        handleClose={() => {
          setFilter(!filter);
        }}
        title={'Expense filter'}
        className="small-dialog-box-container"
        content={
          <>
            <Box className="staff-filter">
              <Box>
                {authState?.web_user_active_role_id !== 7 &&
                  authState?.web_user_active_role_id !== 14 && (
                    <Box>
                      <CustomSelect
                        placeholder="Branch name"
                        options={branchList}
                        value={
                          branchList?.find((opt) => {
                            return opt?.value === filterData?.branch;
                          }) || ''
                        }
                        onChange={(e) => {
                          setFilterData({
                            ...filterData,
                            branch: e?.value,
                          });
                        }}
                        label={<span>Branch name</span>}
                        menuPortalTarget={document.body}
                        styles={{
                          menuPortal: (base) => ({
                            ...base,
                            zIndex: 9999,
                          }),
                        }}
                      />
                    </Box>
                  )}

                {/* <Box className="select-box ">
                  <CustomSelect
                    placeholder="Request by"
                    options={branchList}
                    value={filterData?.requestedby}
                    onChange={(e) => {
                      setFilterData({
                        ...filterData,
                        requestedby: e.target.value
                      });
                    }}
                    label={<span>Request by</span>}
                  />
                </Box> */}
                {/* <Box className="select-box pb32">
                  <CustomSelect
                    placeholder="Status"
                    options={identifiers?.USER_STATUS_OPTIONS}
                    value={filterData?.status}
                    onChange={(e) => {
                      // setFilterData({ ...filterData, status: e.target.value });
                    }}
                    label={<span>Status</span>}
                  />
                </Box> */}
                <Box className="pt8">
                  <CustomDatePicker
                    label={<span>Start Month (MM/YYYY)</span>}
                    error={false}
                    name="sdate"
                    value={dayjs(filterData?.sdate)}
                    onChange={(date) => {
                      setFilterData({
                        ...filterData,
                        sdate: date,
                      });
                    }}
                    inputVariant="outlined"
                    months={true}
                    format={'M/YYYY'}
                    views={['month', 'year']}
                    maxDate={dayjs(filterData?.edate)}
                  />
                </Box>
                <Box className="pt8">
                  <CustomDatePicker
                    label={<span>End Month (MM/YYYY)</span>}
                    error={false}
                    name="edate"
                    value={dayjs(filterData?.edate)}
                    onChange={(date) => {
                      setFilterData({
                        ...filterData,
                        edate: date,
                      });
                    }}
                    inputVariant="outlined"
                    months={true}
                    format={'M/YYYY'}
                    views={['month', 'year']}
                    minDate={dayjs(filterData?.sdate)}
                  />
                </Box>

                <Box className="form-actions-btn">
                  <CustomButton
                    fullWidth
                    variant="outlined"
                    title="Clear"
                    onClick={() => {
                      setFilter(false);
                      setPage(1);
                      setFilterData({
                        branch: '',
                        sdate: '',
                        edate: '',
                      });
                      setFilterDataApplied({
                        branch: '',
                        sdate: '',
                        edate: '',
                      });
                      getDsrData(searchValue, 1, '', '', '');
                    }}
                  />
                  <CustomButton
                    fullWidth
                    variant="contained"
                    title="Apply"
                    onClick={() => {
                      setFilter(false);
                      setPage(1);
                      setFilterDataApplied({
                        branch: filterData?.branch,
                        sdate: filterData?.sdate,
                        edate: filterData?.edate,
                      });
                      getDsrData(
                        searchValue,
                        1,
                        filterData?.branch,
                        filterData?.sdate,
                        filterData?.edate
                      );
                    }}
                  />
                </Box>
              </Box>
            </Box>
          </>
        }
      />
    </>
  );
}
