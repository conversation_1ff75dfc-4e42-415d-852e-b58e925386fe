'use client';

import React, { useEffect, useContext, useState } from 'react';
import { Box, Typography } from '@mui/material';
import CustomTextField from '@/components/UI/CustomTextField';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import CustomButton from '@/components/UI/CustomButton';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import AuthContext from '@/helper/authcontext';
import { useRouter } from 'next/navigation';
import { fetchFromStorage, saveToStorage } from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import DSRRequestByIdPage from './PayrollRequestsId';
import ExpenseCategory from './ExpenseCatReq';

export default function RequestPayroll({ dsrId }) {
  const { authState, userdata, setUserdata } = useContext(AuthContext);

  const [remark, setRemark] = useState('');
  const [isSubmit, setIsSubmit] = useState(false);
  const router = useRouter();
  const [DsrDetails, setDsrDetails] = useState();
  const [expenseData, setExpenseData] = useState([]);

  const [loader, setLoader] = useState(false);
  const [isRequestTable, setIsRequestTable] = useState(false);
  const [approve, setApprove] = useState('');
  const [dsrReqData, setdsrReqData] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [requestId, setRequestId] = useState();
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [currency, setCurrency] = useState();
  const [IsRemarkView, setIsRemarkView] = useState(true);

  // List of Expenses request
  const getExpenseRequestList = async (pageNo, Rpp) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_EXPENSE_REQUEST_LIST +
          `?expense_id=${dsrId}&page=${pageNo}&size=${Rpp ? Rpp : rowsPerPage}`
      );
      if (status === 200) {
        setLoader(false);
        let dsr =
          data?.data &&
          data?.data?.length > 0 &&
          data?.data?.map((d) => {
            return {
              ...d,
              branch: d?.expense_branch?.branch_name,
              submitedby: d?.expense_branch?.user_full_name,
            };
          });
        dsr ? setdsrReqData(dsr) : setdsrReqData([]);

        setTotalCount(data?.count);
      }
    } catch (error) {
      setLoader(false);
      setTotalCount(0);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // Expense data by id
  const getExpenseRequest = async (id) => {
    setLoader(true);
    setRequestId(id);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_REQUEST_EXPENSE_ID + `${id}`
      );
      if (status === 200 || status === 201) {
        setLoader(false);
        setDsrDetails(data?.data);
        const incomeData =
          data?.data &&
          data?.data?.expense_items &&
          data?.data?.expense_items?.length > 0 &&
          data?.data?.expense_items?.filter(
            (f) => f?.payment_type_usage === 'expense'
          );
        incomeData && setExpenseData(incomeData);
      }
    } catch (error) {
      setLoader(false);
      setDsrDetails();
      setExpenseData();
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const handleDsrRequest = async (status) => {
    setLoader(true);
    let request = {
      expense_request_id: requestId,
      request_status: status,
      request_remark: remark,
    };
    try {
      const { status, data } = await axiosInstance.post(
        URLS?.APPROVE_REJECT_REQUEST_EXPENSE,
        request
      );
      if (status === 200) {
        setLoader(false);
        if (data.status) {
          setIsRequestTable(false);
          getExpenseRequestList(1);
          setLoader(false);
          setApiMessage('success', data?.message);
        } else {
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const oldDSR = fetchFromStorage(identifiers?.RedirectData)
    ? fetchFromStorage(identifiers?.RedirectData)
    : userdata;

  useEffect(() => {
    if (oldDSR?.IsFromUser === undefined && oldDSR?.page !== undefined) {
      setUserdata({ ...userdata, IsFromUser: true });
      saveToStorage(identifiers?.RedirectData, {
        ...oldDSR,
        IsFromUser: true,
      });
    }
  }, [oldDSR]);
  useEffect(() => {
    if (dsrId) {
      getExpenseRequestList(1);
    }
  }, [dsrId]);
  useEffect(() => {
    authState?.currency_details && setCurrency(authState?.currency_details);
  }, [authState]);
  return (
    <Box className="Add-dsr-section">
      <ArrowBackIosIcon
        className="mb8 cursor-pointer"
        onClick={() => {
          setTimeout(() => {
            router?.push('/payroll');
          }, 1000);
        }}
      />
      {isRequestTable ? (
        <>
          <Box>
            {DsrDetails?.expense_month && DsrDetails?.expense_year && (
              <Box className="d-flex align-center">
                <Typography className="title-text fw400">
                  <span className="fw600"> Month : </span>
                  <span>
                    {DsrDetails?.expense_month + '-' + DsrDetails?.expense_year}
                  </span>
                </Typography>
              </Box>
            )}
            {DsrDetails?.expense_branch && (
              <Box className="d-flex align-center pt4">
                <Typography className="title-text fw400">
                  <span className="fw600"> Branch : </span>
                  <span>{DsrDetails?.expense_branch}</span>
                </Typography>
              </Box>
            )}
            {DsrDetails?.submitted_user && (
              <Box className="d-flex align-center pt4">
                <Typography className="title-text fw400">
                  <span className="fw600"> Submitted By : </span>
                  <span>{DsrDetails?.submitted_user}</span>
                </Typography>
              </Box>
            )}
            {DsrDetails?.expense_request_updated_by &&
              DsrDetails?.expense_request_updated_by?.user_full_name && (
                <Box className="d-flex align-center pt4">
                  <Typography className="title-text fw400">
                    <span className="fw600"> Action By : </span>
                    <span>
                      {DsrDetails?.expense_request_updated_by?.user_full_name}
                    </span>
                  </Typography>
                </Box>
              )}
            {DsrDetails?.expense_request_status && (
              <Box className="d-flex align-center pt4">
                <Typography className="title-text fw400">
                  <span className="fw600"> Status : </span>
                  <span>
                    {DsrDetails?.expense_request_status === 'rejected' ? (
                      <span className="p12 failed fw600 text-capital">
                        {' '}
                        {DsrDetails?.expense_request_status}{' '}
                      </span>
                    ) : DsrDetails?.expense_request_status === 'pending' ? (
                      <span className="p12 draft fw600 text-capital">
                        {' '}
                        {DsrDetails?.expense_request_status}{' '}
                      </span>
                    ) : (
                      <span className="p12 success fw600 text-capital">
                        {' '}
                        {DsrDetails?.expense_request_status}{' '}
                      </span>
                    )}
                  </span>
                </Typography>
              </Box>
            )}
          </Box>
          {/* {expenseData && expenseData?.length > 0 && (
            <>
              <FormGroup className="pt16">
                <FormControlLabel
                  control={
                    <Checkbox
                      className="check-box "
                      icon={
                        <CheckBoxOutlineBlankIcon className="uncheck-icon" />
                      }
                      checkedIcon={<CheckBoxIcon className="check-icon" />}
                      disableRipple
                    />
                  }
                  name="weekly"
                  className="weekly-check-box p12"
                  checked={IsRemarkView}
                  onChange={(e) => {
                    setIsRemarkView(e.target.checked);
                  }}
                  label="View Remark"
                />
              </FormGroup>
              <Box className="dsr-add-view pt16">
                {expenseData &&
                  expenseData?.length > 0 &&
                  expenseData?.map((item, dindex) => {
                    return (
                      <Box className="dsr-main-category income">
                        <Typography className="p16 fw600 text-capital main-category-text expense-text">
                          {item?.payment_type_title}
                        </Typography>
                        <span className="category-status income">
                          <span className="p12 category-draft  fw600 text-capital">
                            {' '}
                            Expense
                          </span>
                        </span>
                        {item?.payment_type_category?.length > 0 &&
                          item?.payment_type_category?.map((citem, cindex) => {
                            return (
                              <Box className="">
                                {' '}
                                {citem?.payment_type_category_pattern ===
                                  'multiple' && (
                                  <Typography className="title-text fw600 text-capital pt16">
                                    {citem?.payment_type_category_title}
                                  </Typography>
                                )}
                                {citem?.payment_type_category_pattern ===
                                'multiple' ? (
                                  <>
                                    {citem?.categoryBranchValue &&
                                      citem?.categoryBranchValue?.length > 0 &&
                                      citem?.categoryBranchValue?.map(
                                        (csitem, subIndex) => {
                                          return (
                                            <>
                                              <Box className="card-details-section pt16">
                                                <Box>
                                                  <Typography className="title-text">
                                                    {csitem?.first_field_value}
                                                  </Typography>
                                                </Box>
                                                <Box className="amount-field">
                                                  <CustomTextField
                                                    InputLabelProps={{
                                                      shrink: true
                                                    }}
                                                    value={
                                                      csitem?.expense_amount
                                                    }
                                                    disabled={true}
                                                    name={`amount ${subIndex}`}
                                                    variant="filled"
                                                    placeholder={
                                                      item?.has_field_currency
                                                        ? 'Amount'
                                                        : 'Enter value'
                                                    }
                                                    className={
                                                      'amount-textfield'
                                                    }
                                                    InputProps={{
                                                      ...(item?.has_field_currency && {
                                                        startAdornment: (
                                                          <InputAdornment position="start">
                                                            <Typography className="title-text currency">
                                                              {currency?.symbol
                                                                ? currency?.symbol
                                                                : '£'}
                                                            </Typography>{' '}
                                                          </InputAdornment>
                                                        )
                                                      })
                                                    }}
                                                  />
                                                </Box>
                                              </Box>
                                            </>
                                          );
                                        }
                                      )}
                                  </>
                                ) : (
                                  <>
                                    <Box className="card-details-section pt16">
                                      <Box>
                                        <Typography className="title-text fw600 text-capital">
                                          {citem?.payment_type_category_title}
                                        </Typography>
                                      </Box>
                                      <Box className="amount-field">
                                        <CustomTextField
                                          InputLabelProps={{
                                            shrink: true
                                          }}
                                          value={citem?.expense_amount}
                                          disabled={true}
                                          name={`amount ${cindex}`}
                                          variant="filled"
                                          placeholder={
                                            item?.has_field_currency
                                              ? 'Amount'
                                              : 'Enter value'
                                          }
                                          className={'amount-textfield'}
                                          InputProps={{
                                            ...(item?.has_field_currency && {
                                              startAdornment: (
                                                <InputAdornment position="start">
                                                  <Typography className="title-text currency">
                                                    {currency?.symbol
                                                      ? currency?.symbol
                                                      : '£'}
                                                  </Typography>{' '}
                                                </InputAdornment>
                                              )
                                            })
                                          }}
                                        />
                                      </Box>
                                    </Box>
                                  </>
                                )}
                              </Box>
                            );
                          })}
                        <>
                          <Box className="card-details-section pt16 totol-dsr">
                            <Box>
                              <Typography className="title-text fw600">
                                Total
                              </Typography>
                            </Box>
                            <Box className="amount-field">
                              <CustomTextField
                                InputLabelProps={{
                                  shrink: true
                                }}
                                value={TotalOfExpenseObj(expenseData, dindex)}
                                name={`amount `}
                                variant="filled"
                                disabled={true}
                                placeholder={
                                  item?.has_field_currency
                                    ? 'Amount'
                                    : 'Enter value'
                                }
                                className={'amount-textfield'}
                                InputProps={{
                                  ...(item?.has_field_currency && {
                                    startAdornment: (
                                      <InputAdornment position="start">
                                        <Typography className="title-text currency">
                                          {currency?.symbol
                                            ? currency?.symbol
                                            : '£'}
                                        </Typography>{' '}
                                      </InputAdornment>
                                    )
                                  })
                                }}
                              />
                            </Box>
                          </Box>
                          {IsRemarkView && (
                            <Box className="card-details-section  card-details-remark-section pt16">
                              <Box>
                                <Typography className="title-text fw600">
                                  Remark
                                </Typography>
                              </Box>
                              <Box className="amount-field">
                                <CustomTextField
                                  InputLabelProps={{
                                    shrink: true
                                  }}
                                  value={item?.payment_type_remark}
                                  onChange={(e) => {
                                    if (
                                      e.target.value === '' ||
                                      e.target.value?.length < 161
                                    ) {
                                      const income = expenseData;
                                      income[dindex].payment_type_remark =
                                        e.target.value;
                                      setExpenseData(income);
                                      setRandom(Math.random());
                                    }
                                  }}
                                  onPaste={(e) => {
                                    if (
                                      e.target.value === '' ||
                                      e.target.value?.length < 161
                                    ) {
                                      setIsSubmit(false);
                                      const income = DsrDataIncome;
                                      income[dindex].payment_type_remark =
                                        e.target.value;
                                      setDsrDataIncome(income);
                                      setRandom(Math.random());
                                    }
                                  }}
                                  multiline
                                  rows={2}
                                  disabled={true}
                                  name={`amount ${dindex}`}
                                  variant="filled"
                                  placeholder={'Enter value'}
                                  className={
                                    'amount-textfield additional-textfeild'
                                  }
                                />
                              </Box>
                              <Typography className="p12 text-align-end">
                                {(item?.payment_type_remark?.length
                                  ? item?.payment_type_remark?.length
                                  : 0) + ' / 160'}
                              </Typography>
                            </Box>
                          )}
                        </>
                      </Box>
                    );
                  })}
              </Box>
            </>
          )} */}
          <ExpenseCategory
            expenseData={expenseData}
            isEdit={true}
            setIsRemarkView={setIsRemarkView}
            IsRemarkView={IsRemarkView}
            setExpenseData={setExpenseData}
            currency={currency}
          />
          {DsrDetails?.expense_request_status !== 'approved' &&
            DsrDetails?.expense_request_status !== 'rejected' && (
              <>
                <Box className="pt64">
                  <CustomTextField
                    id="remark"
                    name="remark"
                    multiline
                    rows={2}
                    onChange={(e) => {
                      setIsSubmit(false);
                      setApprove();
                      setRemark(e.target.value);
                    }}
                    fullWidth
                    error={Boolean(!remark && isSubmit)}
                    helperText={isSubmit && !remark}
                    placeholder="Remark"
                    value={remark}
                    className="additional-textfeild"
                    label="Remark"
                    required
                  />
                  {!remark && isSubmit && (
                    <Typography
                      variant="body2"
                      color="error"
                      className="other-field-error-text"
                    >
                      This field is required
                    </Typography>
                  )}
                </Box>
                <Box className="create-cancel-button mt16">
                  <CustomButton
                    variant="contained"
                    disabled={loader}
                    className="red-button"
                    title={`${
                      loader && approve === 'rejected'
                        ? 'Rejecting...'
                        : 'Reject'
                    }`}
                    onClick={() => {
                      setIsSubmit(true);
                      if (remark) {
                        setApprove('rejected');
                        handleDsrRequest('rejected');
                      }
                    }}
                  />
                  <CustomButton
                    variant="contained"
                    disabled={loader} //|| !remark
                    className="green-button"
                    title={`${
                      loader && approve === 'approved'
                        ? 'Approving...'
                        : 'Approve'
                    }`}
                    onClick={() => {
                      setIsSubmit(true);
                      if (remark) {
                        setApprove('approved');
                        handleDsrRequest('approved');
                      }
                    }}
                  />
                </Box>
              </>
            )}
        </>
      ) : (
        <>
          <Box>
            {dsrReqData && dsrReqData?.length > 0 && (
              <>
                {dsrReqData?.[0]?.expense_month &&
                  dsrReqData?.[0]?.expense_year && (
                    <Box className="d-flex align-center">
                      <Typography className="title-text fw400">
                        <span className="fw600"> Date : </span>
                        <span>
                          {dsrReqData?.[0]?.expense_month +
                            '-' +
                            dsrReqData?.[0]?.expense_year}
                        </span>
                      </Typography>
                    </Box>
                  )}
                {dsrReqData?.[0]?.expense_branch &&
                  dsrReqData?.[0]?.expense_branch?.branch_name && (
                    <Box className="d-flex align-center pt4">
                      <Typography className="title-text fw400">
                        <span className="fw600"> Branch : </span>
                        <span>
                          {dsrReqData?.[0]?.expense_branch?.branch_name}
                        </span>
                      </Typography>
                    </Box>
                  )}
                {dsrReqData?.[0]?.expense_user &&
                  dsrReqData?.[0]?.expense_user?.user_full_name && (
                    <Box className="d-flex align-center pt4">
                      <Typography className="title-text fw400">
                        <span className="fw600"> Submitted By : </span>
                        <span>
                          {dsrReqData?.[0]?.expense_user?.user_full_name}
                        </span>
                      </Typography>
                    </Box>
                  )}
                {dsrReqData?.[0]?.expense_request_updated_by &&
                  dsrReqData?.[0]?.expense_request_updated_by
                    ?.user_full_name && (
                    <Box className="d-flex align-center pt4">
                      <Typography className="title-text fw400">
                        <span className="fw600"> Action By : </span>
                        <span>
                          {
                            dsrReqData?.[0]?.expense_request_updated_by
                              ?.user_full_name
                          }
                        </span>
                      </Typography>
                    </Box>
                  )}
                {dsrReqData?.[0]?.expense_request_status && (
                  <Box className="d-flex align-center pt4">
                    <Typography className="title-text fw400">
                      <span className="fw600"> Status : </span>
                      <span>
                        <span>
                          {dsrReqData?.[0]?.expense_request_status ===
                          'rejected' ? (
                            <span className="p12 failed fw600 text-capital">
                              {' '}
                              {dsrReqData?.[0]?.expense_request_status}{' '}
                            </span>
                          ) : dsrReqData?.[0]?.expense_request_status ===
                            'pending' ? (
                            <span className="p12 draft fw600 text-capital">
                              {' '}
                              {dsrReqData?.[0]?.expense_request_status}{' '}
                            </span>
                          ) : (
                            <span className="p12 success fw600 text-capital">
                              {' '}
                              {dsrReqData?.[0]?.expense_request_status}{' '}
                            </span>
                          )}
                        </span>
                      </span>
                    </Typography>
                  </Box>
                )}
              </>
            )}
          </Box>
          <DSRRequestByIdPage
            setIsRequestTable={setIsRequestTable}
            dsrReqData={dsrReqData}
            loader={loader}
            totalCount={totalCount}
            getDsrRequest={getExpenseRequest}
            getDsrRequestList={getExpenseRequestList}
            setRowsPerPage={setRowsPerPage}
            rowsPerPage={rowsPerPage}
          />
        </>
      )}
    </Box>
  );
}
