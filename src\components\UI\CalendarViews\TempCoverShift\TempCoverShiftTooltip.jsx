'use client';

import React, { useRef, useState } from 'react';
import {
  <PERSON><PERSON>,
  ClickAwayListener,
  Di<PERSON><PERSON>,
  <PERSON><PERSON>,
  Tooltip,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import SupervisorAccountOutlinedIcon from '@mui/icons-material/SupervisorAccountOutlined';
import BusinessCenterOutlinedIcon from '@mui/icons-material/BusinessCenterOutlined';
import LocationOnOutlinedIcon from '@mui/icons-material/LocationOnOutlined';
import SearchIcon from '@mui/icons-material/Search';
import AccountCircleOutlinedIcon from '@mui/icons-material/AccountCircleOutlined';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import CustomButton from '../../CustomButton';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import moment from 'moment';
import axiosInstanceOrg from '@/helper/axios/axiosInstanceOrg';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { ROTA_URLS } from '@/helper/constants/urls';
import PreLoader from '@/components/UI/Loader';
import UserAvatar from '../../Avatar/UserAvatar';
import './tempCoverShift.scss';

// Validation schema using Yup
const AddShiftSchema = Yup.object().shape({
  selectedLocation: Yup.object()
    .shape({
      value: Yup.string().required(),
      label: Yup.string().required(),
    })
    .required('Location is required'),
  selectedEmployee: Yup.object()
    .shape({
      id: Yup.string().required(),
      user_full_name: Yup.string().required(),
    })
    .required('Employee is required'),
});

export const TempCoverShiftTooltip = ({
  staffListOptions,
  locationList,
  rowUserData,
  handleAddShiftTooltipClose,
  selectedShiftData,
  getRotaShiftList,
}) => {
  const hoverUserLocationDetails = rowUserData?.branch
    ? {
        label: rowUserData?.branch?.branch_name,
        value: rowUserData?.branch?.id,
      }
    : null;

  const selected_location = selectedShiftData?.branch
    ? {
        label: selectedShiftData?.branch?.branch_name,
        value: selectedShiftData?.branch?.id,
      }
    : null;

  const [isLoading, setIsLoading] = useState(false);
  const [isLocationDropdownOpen, setIsLocationDropdownOpen] = useState(false);
  const [searchLocation, setSearchLocation] = useState('');

  const [staffList, setStaffList] = useState(staffListOptions?.slice(1));
  const [isShiftDropdownOpen, setIsShiftDropdownOpen] = useState(false);
  const [searchStaff, setSearchStaff] = useState('');

  const locationDropdownRef = useRef(null);
  const shiftDropdownRef = useRef(null);

  // Initial form values
  const initialValues = {
    selectedLocation:
      selected_location || hoverUserLocationDetails || locationList?.[0],
    selectedEmployee: null,
  };

  const start_time = moment(selectedShiftData?.startTime).utc().format('HH:mm');
  const end_time = moment(selectedShiftData?.endTime).utc().format('HH:mm');
  const role_name = selectedShiftData?.role?.role_name || 'No Role';
  const shift_location =
    selectedShiftData?.branch?.branch_name || 'No Location';

  // Filter locationList based on search
  const filteredLocationList = locationList?.filter((role) =>
    role?.label?.toLowerCase().includes(searchLocation.toLowerCase())
  );
  // Filter staffListOptions based on search
  const filteredStaffList = staffList
    ?.filter((item) =>
      item?.user_roles?.some(
        (role) =>
          role?.role_id === selectedShiftData?.roleId ||
          role?.id === selectedShiftData?.roleId
      )
    )
    ?.filter((dpt) => dpt?.department?.id === selectedShiftData?.departmentId)
    ?.filter((obj) => obj?.id !== selectedShiftData?.userId)
    ?.filter((emp) =>
      emp?.user_full_name?.toLowerCase().includes(searchStaff.toLowerCase())
    );

  // user list
  const getStaffList = async (branchID) => {
    setIsLoading(true);
    try {
      const { status, data } = await axiosInstanceOrg.get(
        ROTA_URLS?.SHIFT_STAFF_LIST +
          `?isAdmin=false&branch_id=${branchID}&isRotaList=true`
      );

      if (status === 200) {
        setIsLoading(false);
        setStaffList(data?.userList);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setIsLoading(false);
    }
  };

  const handleSubmit = async (values, { setSubmitting }) => {
    const date_time = {
      start_date: selectedShiftData?.startTime,
      end_date: selectedShiftData?.endTime,
    };

    const sendCheckAvailabilityData = {
      id: selectedShiftData?.id,
      userId: values?.selectedEmployee?.id,
      startTime: date_time?.start_date,
      endTime: date_time?.end_date,
      status: 'active',
      isOpen: values?.selectedEmployee?.id ? false : true,
    };

    const sendData = {
      userId: values?.selectedEmployee?.id,
      startTime: date_time?.start_date,
      endTime: date_time?.end_date,
      status: 'active',
      minutesBreak: selectedShiftData?.minutesBreak,
      branchId: selectedShiftData?.branchId,
      departmentId: selectedShiftData?.departmentId,
      role: selectedShiftData?.roleId,
      isOpen: values?.selectedEmployee?.id ? false : true,
      isPublished: selectedShiftData?.isPublished ? true : false,
      isSwap: false,
      acknowledged: true,
      notes: selectedShiftData?.notes,
    };

    setIsLoading(true);
    try {
      const { status } = await axiosInstanceOrg.post(
        ROTA_URLS?.SHIFT_CHECK_AVILABILITY,
        sendCheckAvailabilityData
      );

      if (status === 200 || status === 201) {
        try {
          const { status, data } = await axiosInstanceOrg.put(
            ROTA_URLS?.SHIFT_URL + `/${selectedShiftData?.id}`,
            sendData
          );
          if (status === 200 || status === 201) {
            setApiMessage('success', data?.message);
            setIsLoading(false);
            getRotaShiftList();
            handleAddShiftTooltipClose();
            setIsLoading(false);
          }
        } catch (error) {
          setApiMessage('error', error?.response?.data?.message);
          setIsLoading(false);
        }
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setIsLoading(false);
    }

    setSubmitting(false);
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={AddShiftSchema}
      onSubmit={handleSubmit}
    >
      {({ values, setFieldValue, isSubmitting, dirty }) => {
        return (
          <Form className="temp-cover-container">
            <div>{isLoading && <PreLoader />}</div>
            <div className="temp-cover-header-container">
              <div>
                <Tooltip
                  title={
                    <p className="p12">
                      Temporary cover allows you to assign an employee from
                      another location to cover a particular shift
                    </p>
                  }
                  placement="top"
                  arrow
                  classes={{
                    tooltip:
                      'calendar-event-list-tooltip-custom calendar-event-left-shift-tooltip',
                  }}
                >
                  <p className="temp-cover-header-title">
                    Assign temporary cover
                  </p>
                </Tooltip>
              </div>

              <div className="temp-cover-header-actions">
                <div>
                  <CustomButton
                    title={isSubmitting ? 'Confirm...' : 'Confirm'}
                    type="submit"
                    disabled={isSubmitting || !dirty}
                  />
                </div>
                <div
                  className="close-shift-tooltip"
                  onClick={handleAddShiftTooltipClose}
                >
                  <CloseIcon />
                </div>
              </div>
            </div>
            <Divider />
            <div className="temp-cover-details-container">
              <div className="temp-cover-details-wrapper">
                <div className="temp-cover-details">
                  <div className="temp-cover-user-details">
                    <div>
                      <Badge
                        overlap="circular"
                        anchorOrigin={{
                          vertical: 'bottom',
                          horizontal: 'right',
                        }}
                        badgeContent={
                          <SupervisorAccountOutlinedIcon className="temp-cover-badge-icon" />
                        }
                      >
                        <UserAvatar
                          name={selectedShiftData?.user?.user_full_name}
                          src={selectedShiftData?.user?.user_avatar_link}
                          classname="temp-cover-avatar"
                        />
                      </Badge>
                    </div>
                    <p className="temp-cover-name">
                      {selectedShiftData?.user?.user_full_name
                        ? selectedShiftData?.user?.user_full_name + `'s shift`
                        : ''}
                    </p>
                  </div>
                  <div className="temp-cover-shift-details">
                    <div className="temp-cover-shift-time-role">
                      <p className="temp-cover-shift-date">
                        {moment(
                          selectedShiftData?.startTime || new Date()
                        ).format('ddd DD MMM YYYY')}
                      </p>
                      <p className="temp-cover-shift-time">
                        {start_time} - {end_time}
                      </p>
                      {/* <div className="temp-cover-shift-break"> */}
                      <p className="temp-cover-shift-icon-text">
                        <BusinessCenterOutlinedIcon />
                        <span>{role_name}</span>
                      </p>
                      <p className="temp-cover-shift-icon-text">
                        <LocationOnOutlinedIcon />
                        <span>{shift_location}</span>
                      </p>
                      {/* </div> */}
                    </div>
                  </div>
                </div>
              </div>
              <div>
                {/* Location Dropdown */}
                <div className="d-center-start shift-select-dropdown">
                  <div className="icon">
                    <LocationOnOutlinedIcon />
                  </div>
                  <div>
                    <p className="dropdown-left-title">Filter By:</p>
                  </div>
                  <div className="dropdown-wrapper" ref={locationDropdownRef}>
                    <div
                      className="dropdown-header"
                      onClick={() =>
                        setIsLocationDropdownOpen(!isLocationDropdownOpen)
                      }
                    >
                      <p>
                        <span className="role-left-border"></span>
                        {values.selectedLocation?.label ? (
                          <span>{values.selectedLocation?.label}</span>
                        ) : (
                          <span className="no-select-text">All Location</span>
                        )}
                      </p>
                      <span className="arrow">
                        {isLocationDropdownOpen ? (
                          <ExpandLessIcon />
                        ) : (
                          <ExpandMoreIcon />
                        )}
                      </span>
                    </div>

                    <Popper
                      open={isLocationDropdownOpen}
                      anchorEl={locationDropdownRef.current}
                      placement="bottom-start"
                      className="dropdown-popper"
                      modifiers={[
                        {
                          name: 'preventOverflow',
                          options: { boundary: 'window' },
                        },
                        {
                          name: 'flip',
                          options: {
                            fallbackPlacements: ['top-start', 'right-start'],
                          },
                        },
                      ]}
                    >
                      <ClickAwayListener
                        onClickAway={() => setIsLocationDropdownOpen(false)}
                      >
                        <div className="dropdown-content">
                          <div className="search-input-wrapper">
                            <SearchIcon className="search-icon" />
                            <input
                              type="text"
                              placeholder="Search location"
                              className="search-input"
                              value={searchLocation}
                              onChange={(e) =>
                                setSearchLocation(e.target.value)
                              }
                            />
                          </div>
                          <ul>
                            {filteredLocationList?.map((role) => (
                              <li
                                key={role?.value}
                                className={
                                  values.selectedLocation?.value === role?.value
                                    ? 'selected'
                                    : ''
                                }
                                onClick={() => {
                                  setFieldValue('selectedLocation', role);
                                  setFieldValue('selectedEmployee', null);
                                  setIsLocationDropdownOpen(false);
                                  getStaffList(role?.value);
                                }}
                              >
                                <span className="role-left-border"></span>
                                {role?.label}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </ClickAwayListener>
                    </Popper>
                  </div>
                </div>
                <Divider />
                {/* User Dropdown */}
                <div className="d-center-start shift-select-dropdown">
                  <div className="icon">
                    <AccountCircleOutlinedIcon />
                  </div>
                  <div className="dropdown-wrapper" ref={shiftDropdownRef}>
                    {filteredStaffList?.length === 0 ? (
                      <p className="no-select-text title-text pt8 pb8">
                        No employees available to cover this shift
                      </p>
                    ) : (
                      <div
                        className="dropdown-header"
                        onClick={() =>
                          setIsShiftDropdownOpen(!isShiftDropdownOpen)
                        }
                      >
                        <p>
                          <span className="role-left-border"></span>
                          {/* <span>
                          {values.selectedEmployee?.user_full_name
                            ? values.selectedEmployee?.user_full_name
                            : 'Select employee to cover this shift'}
                        </span> */}
                          {values.selectedEmployee?.user_full_name ? (
                            <span>
                              {values.selectedEmployee?.user_full_name}
                            </span>
                          ) : (
                            <span className="no-select-text">
                              Select employee to cover this shift
                            </span>
                          )}
                        </p>
                        <span className="arrow">
                          {isShiftDropdownOpen ? (
                            <ExpandLessIcon />
                          ) : (
                            <ExpandMoreIcon />
                          )}
                        </span>
                      </div>
                    )}

                    <Popper
                      open={isShiftDropdownOpen}
                      anchorEl={shiftDropdownRef.current}
                      placement="bottom-start"
                      className="dropdown-popper"
                      modifiers={[
                        {
                          name: 'preventOverflow',
                          options: { boundary: 'window' },
                        },
                        {
                          name: 'flip',
                          options: {
                            fallbackPlacements: ['top-start', 'right-start'],
                          },
                        },
                      ]}
                    >
                      <ClickAwayListener
                        onClickAway={() => setIsShiftDropdownOpen(false)}
                      >
                        <div className="dropdown-content">
                          <div className="search-input-wrapper">
                            <SearchIcon className="search-icon" />
                            <input
                              type="text"
                              placeholder="Search employee"
                              className="search-input"
                              value={searchStaff}
                              onChange={(e) => setSearchStaff(e.target.value)}
                            />
                          </div>
                          <ul>
                            {filteredStaffList.map((emp) => (
                              <li
                                key={emp?.id}
                                className={
                                  values.selectedEmployee?.id === emp?.id
                                    ? 'selected'
                                    : ''
                                }
                                onClick={() => {
                                  setFieldValue('selectedEmployee', emp);
                                  setIsShiftDropdownOpen(false);
                                }}
                              >
                                <span className="role-left-border"></span>
                                {emp?.user_full_name}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </ClickAwayListener>
                    </Popper>
                  </div>
                </div>
              </div>
            </div>
          </Form>
        );
      }}
    </Formik>
  );
};
