@import '@/styles/variable.scss';

.contract-type {
  .search-filter-section {
    display: flex;
    column-gap: 25px;
    align-items: center;
    .staff-leave-search {
      max-width: calc(100% - 400px) !important;
    }
    .filter-icon {
      border: 1px solid $color-Black;
      border-radius: 3px;
      width: 25px;
      height: 25px;
      cursor: pointer;
    }
    @media (max-width: 799px) {
      column-gap: 15px;
      .staff-leave-search {
        max-width: calc(100% - 370px) !important;
      }
    }
    @media (max-width: 599px) {
      display: flex;
      flex-wrap: wrap;
      row-gap: 25px;
      .staff-leave-search {
        width: 85% !important;
        max-width: 85% !important;
      }
      .filter-icon {
        max-width: 15%;
      }
      button {
        width: 45%;
      }
    }
    @media (max-width: 599px) {
      row-gap: 15px;
      button {
        width: 100%;
      }
    }
  }
  .actions {
    min-width: 65px;
  }
}
.create-contract-type {
  background-color: $color-White;
  padding: 26px 15px;
  border-radius: 12px;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.05);
  .display-grid {
    display: grid;
    grid-template-columns: 32% 32% 32%;
    justify-content: space-between;
    column-gap: 15px;
    row-gap: 15px;
    @media (max-width: 899px) {
      display: block;
    }
  }
  .select-wrap {
    .MuiInputBase-root {
      min-height: 48px;
      margin-top: 2px;
      .MuiSelect-select {
        margin-top: 0px;
      }
    }
    fieldset {
      margin-top: 0px;
    }
    .MuiOutlinedInput-notchedOutline {
      top: 0;
    }
  }
}
