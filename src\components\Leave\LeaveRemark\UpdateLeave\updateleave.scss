.update-leave-wrap {
  background-color: var(--color-white);
  padding: var(--spacing-xxl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-xs);
  .update-leave-header-text {
    margin-bottom: 20px;
  }
  .leave-container {
    .update-leave {
      width: 35%;
      max-height: calc(100vh - 108px - var(--banner-height));
      overflow: auto;
      overflow-x: hidden;
      padding-right: 20px;
      border-right: var(--normal-sec-border);
      .leave-left-wrap {
        .leave-left {
          gap: 10px;
          .user-detail-wrap {
            .user-email {
              color: var(--text-color-primary);
            }
          }
        }
        .leave-icons-wrap {
          margin-top: 4px;
        }
        .leave-info {
          gap: 10px;
          .circle-icon {
            height: 5px;
            width: 5px;
          }
        }
        .leave-right {
          @media (max-width: 575px) {
            width: 100%;
            text-align: center;
          }
        }
        @media (max-width: 575px) {
          flex-wrap: wrap;
          row-gap: 15px;
        }
      }
      .leave-date-wrap {
        .leave-date {
          .start-date-wrap {
            width: 50%;
          }
        }
      }

      .icon-wrap {
        gap: 10px;
        .text-wrap {
          font-size: 13px !important;
        }
        .circle-icon {
          height: 5px;
          width: 5px;
        }
      }

      .leave-status-text-wrap {
        gap: 8px;
        span {
          display: inline-block;
          padding: 0px 3px;
        }
        .status-icon-wrap {
          .approved-icon {
            fill: var(--color-green);
            height: 18px;
            width: 18px;
          }
          .rejected-icon {
            height: 18px;
            width: 18px;
            fill: var(--color-danger);
          }
          .pending-icon {
            fill: var(--color-warning);
            height: 18px;
            width: 18px;
          }
          .cancelled-icon {
            fill: var(--color-black);
            height: 18px;
            width: 18px;
          }
        }
        .approved-status {
          text-transform: capitalize;
          color: var(--color-green);
        }
        .rejected-status {
          text-transform: capitalize;
          color: var(--color-danger);
        }
        .pending-status {
          text-transform: capitalize;
          color: var(--color-warning);
        }
        .cancelled-status {
          text-transform: capitalize;
          color: var(--color-black);
        }
      }
      .leave-right {
        .leave-rejected {
          border-radius: 8px;
          padding: 1.5px 8px;
          text-transform: capitalize;
          border: 1px solid var(--color-danger);
          color: var(--color-danger);
        }
        .leave-draft {
          border-radius: 8px;
          padding: 1.5px 8px;
          text-transform: capitalize;
          color: var(--color-warning);
          border: 1px solid var(--color-warning);
        }
        .leave-success {
          border-radius: 8px;
          padding: 1.5px 8px;
          text-transform: capitalize;
          border: 1px solid var(--color-green);
          color: var(--color-green);
        }
        .leave-cancelled {
          border-radius: 8px;
          padding: 1.5px 8px;
          text-transform: capitalize;
          border: 1px solid var(--color-black);
          color: var(--color-black);
        }
      }
      .rejected-status {
        color: var(--text-color-danger);
      }
      .approved-status {
        color: var(--text-green);
      }
      .aproved-pending {
        color: var(--text-bright-blue);
      }

      .MuiButtonBase-root {
        padding: 4px 6px !important;
        font-size: 14px;
        font-weight: 500;
        .MuiChip-label {
          padding: 0px 8px !important;
        }
      }
      .approved-leave {
        font-size: 13px;
        color: var(--text-green);
        .check-icon {
          height: 18px;
          width: 18px;
          fill: var(--color-green);
        }
      }
      .rejected-leave {
        font-size: 13px;
        color: var(--text-color-danger);
        .clear-icon {
          height: 18px;
          width: 18px;
          fill: var(--text-color-danger) !important;
        }
      }
      .chip-selected {
        background-color: var(--color-chip);
        border-color: var(--color-primary);
      }
      .chip-container-wrap {
        .chip-container {
          gap: 10px;
        }
        .chip-container .approved-leave.chip-selected.approved-selected {
          background-color: var(--color-light-green);
          color: var(--color-green);
          border-color: var(--color-green);
        }

        .chip-container .rejected-leave.chip-selected {
          background-color: var(--color-danger-background);
          color: var(--text-color-danger);
          border-color: var(--text-color-danger);
        }
      }

      .leave-type-wrap {
        .leave-type {
          gap: 10px;
        }
        .leave-type-text {
          font-size: 13px;
        }
        @media (max-width: 1199px) {
          flex-wrap: wrap;
          padding-top: 8px;
        }
      }
      .chip-error {
        border-color: var(--color-danger);
      }

      .Mui-error {
        color: var(--color-danger) !important;
      }

      @media (max-width: 1199px) {
        max-height: 100%;
        width: 100%;
        border-right: none;
        padding-right: 0px;
        padding-bottom: 20px;
      }
      @media (max-width: 575px) {
        padding-right: 0px;
      }
    }
    .right-side-update-leave-wrap {
      width: 65%;
      max-height: calc(100vh - 108px - var(--banner-height));
      overflow: auto;
      overflow-x: hidden;
      padding: 20px;

      @media (max-width: 1199px) {
        width: 100%;
        max-height: 100%;
        padding: 15px 0px 0px;
      }
    }
    @media (max-width: 1199px) {
      flex-direction: column;
    }
  }
  @media (max-width: 1199px) {
    max-height: calc(100vh - 108px - var(--banner-height));
    overflow: auto;
    overflow-x: hidden;
  }
  @media (max-width: 575px) {
    padding: 20px 14px 24px;
  }

  .day-based-container {
    padding: 3px;
    width: 100%;
    background-color: var(--color-chip);
    border-radius: 16px;
    margin-top: 8px;
    margin-bottom: 8px;
    color: var(--text-color-black);
    .day-based-wrap {
      gap: 10px;
      width: 100%;
      .day-chip {
        width: calc(50% - 5px);
        border: 0;
      }
      .selected-chip {
        background-color: var(--color-white);
        color: var(--color-black);
      }
    }
  }

  .display-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }
  .leave-add-inputs-wrap {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    max-width: calc(100% - 10px);
    .leave-add-inputs {
      // gap: 8px;
      max-width: 100%;
    }
  }
}
