.User-profile-page {
  background-color: var(--color-white);
  padding: var(--spacing-xxl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-xs);

  .Generate-password-link {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;

    .gen-password {
      display: flex;
      align-items: center;
      margin-right: 16px;
      // width: 175px;
      .generate-pin {
        width: 18px;
        height: 18px;
      }

      svg {
        margin-right: 8px;
        width: 24px;
        height: 24px;
        cursor: pointer;
        fill: var(--color-primary) !important;
      }
    }

    @media (max-width: 399px) {
      display: block;

      .generate-pin {
        margin-right: 12px !important;
        margin-left: 3px;
      }
    }
  }

  // Below code
  .User-verify-details {
    display: flex;
    justify-content: space-between;

    .user-all-details {
      width: calc(100% - 400px);
    }

    .invitation-icon {
      max-width: 400px;

      @media (max-width: 1200px) {
        margin-top: 16px;
        width: 100%;
      }
      svg {
        // cursor: pointer;
        margin-right: 4px;
        width: 20px;
        height: 20px;
      }

      .verified-icon {
        fill: var(--color-green) !important;
      }

      .user-deatils {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        row-gap: 4px;
      }
    }
    .mail-icon {
      fill: var(--color-primary) !important;
    }
    .invite-icon {
      width: max-content;
      max-width: 30%;

      @media (max-width: 1200px) {
        width: 100%;
        margin-top: 16px;
      }

      svg {
        // cursor: pointer;
        margin-right: 4px;
        width: 20px;
        height: 20px;
      }

      .resend-button {
        cursor: pointer;
        max-width: max-content;
      }

      .user-deatils {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
      }
    }

    .invitation-icon,
    .invite-icon {
      .invited-status {
        border: 0;
        padding: 0.5px 4px;
        border-radius: 3px;
        margin-left: 4px;
      }

      .invited-draft {
        background-color: var(--color-light-success);
        color: var(--text-color-white);
      }

      .invited-ongoing {
        background-color: var(--color-primary);
        color: var(--text-color-white);
      }

      .invited-accepted {
        background-color: var(--color-green);
        color: var(--text-color-white);
      }
    }

    .invitation-user {
      color: var(--text-color-white) !important;

      .deatils {
        display: flex;
        flex-direction: column;

        .user-name {
          margin-bottom: 16px;
        }
      }
    }

    @media (max-width: 1200px) {
      display: flex;
      flex-direction: column;

      .user-all-details {
        width: 100%;
      }
    }
  }

  .user-roles {
    display: flex;
    align-items: center;
    margin-right: 8px;
    svg {
      width: 16px;
      height: 16px;
    }
  }
  .created-date {
    width: 20px;
    height: 20px;
    margin-right: 6px;
    margin-left: -2px;
  }
  .profile-image {
    position: relative;

    .MuiAvatar-root {
      width: 60px !important;
      height: 60px !important;
    }

    .edit-icon {
      position: absolute;
      bottom: -4px;
      right: 0;
      width: 22px !important;
      height: 22px !important;
      background-color: var(--color-white);
      border-radius: 100%;
      padding: 3px;
      // path {
      //   width: 15px !important;
      //   height: 15px !important;
      // }

      cursor: pointer;
    }
    .edit-profile {
      bottom: 6px;
    }
  }

  .invitation-user {
    .deatils {
      display: flex;
      flex-direction: column;

      .user-name {
        margin-bottom: 16px;
      }

      .resend-button {
        cursor: pointer;
        max-width: max-content;
      }
    }
  }

  .verified-by-user {
    width: 220px !important;

    .deatils {
      .user-name,
      .user-date {
        .fw700 {
          width: 100px;
        }
      }
    }
  }

  .sign-verify-info {
    color: var(--color-black) !important;

    .sign-home-verify {
      padding: 26px 26px;
      background-color: var(--color-light-blue);
      border-radius: 20px;
    }

    .sign-confim {
      padding: 16px 26px;
    }

    .more-info {
      background-color: var(--action-icon-bg-color);
      border-radius: 20px;
    }

    .checkbox-form {
      cursor: text;

      .MuiTypography-root {
        cursor: text;
      }

      .check-box-text {
        align-items: flex-start;

        .MuiButtonBase-root {
          margin-right: 8px;
        }
      }
    }

    .Mui-disabled {
      opacity: 1 !important;
    }

    a {
      margin-left: 3px;
      margin-right: 3px;
    }
  }

  .user-edit-deatils {
    width: 100%;

    .display-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(0, 250px));
      gap: var(--spacing-lg);
      align-items: flex-start;
      width: 100%;
    }
    .space-grid {
      padding-top: var(--spacing-lg);
    }
    .display-grid-50 {
      display: grid;
      grid-template-columns: repeat(
        auto-fit,
        minmax(0, calc(500px + var(--spacing-lg)))
      );
      gap: var(--spacing-lg);
      align-items: flex-start;
      width: 100%;
    }
    .holiday-select-box {
      .MuiFormLabel-root {
        line-height: 16px;
      }

      .MuiInputBase-root {
        .MuiOutlinedInput-notchedOutline {
          margin-top: 3px !important;
        }
      }
    }
  }

  .user-deatils {
    display: flex;

    // justify-content: end;
    .user-box {
      width: 64px;
      height: 64px;
      border-radius: 100%;

      svg {
        width: 64px;
        height: 64px;
      }
    }

    .deatils {
      margin-left: 12px;

      .user-name {
        color: var(--text-dark);
        margin-bottom: 0px;
      }

      .user-date {
        color: var(--text-dark);
        margin-right: 4px;
      }

      .user-delete {
        color: var(----color-danger);
        margin-left: 8px;

        svg {
          margin-left: 8px;
        }
      }
    }
  }
  .create-cancel-button {
    margin-top: var(--spacing-lg);
  }

  .sign-sec {
    .signature-edit-wrap {
      cursor: pointer;
      line-height: 0px;
      .signature-edit {
        font-size: var(--icon-size-xsm);
        fill: var(--color-black);
      }
    }
    .field-label {
      width: 183px;
      justify-content: space-between;
      align-items: flex-end;
    }
    .signature-section {
      margin-top: 3px;
      border: var(--normal-border);
      border-radius: var(--border-radius-xs);
      background-color: var(--color-white);
      width: 100%;
      max-width: 180px;
      height: 60px;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .employee-contract-section {
    margin-top: 24px;
    padding-left: 3px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(0, 250px));
    gap: var(--spacing-lg);
    div {
      width: 100%;
    }
    .selected-files {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 250px;
      width: 100%;
      padding: 6px 12px;
      border-radius: 8px;
      margin-bottom: 8px;
      box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;

      svg {
        width: 21px;
        height: 21px;
        cursor: pointer;
      }

      .file-name {
        width: calc(250px - 10px - 24px);

        p {
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden;
          word-break: break-all;
        }
      }
    }
  }
}

.onboarding-process-section {
  width: 100%;

  .onboarding-form-details {
    display: flex;
    justify-content: space-between;
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 16px;
    width: 100%;
    box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;

    @media (max-width: 1200px) {
      width: 100%;
    }

    @media (max-width: 499px) {
      display: block;

      .onboarding-status-details {
        max-width: max-content;
        margin-top: 10px;
      }
    }

    .onboarding-status-details {
      display: flex;
      align-items: center;
      width: auto;
      height: fit-content;
      padding: 5px 10px 5px 10px;
      border-radius: 20px;
      color: var(--text-color-white);

      .onboarding-status {
        text-transform: capitalize;
        color: var(--text-color-white);
      }
    }

    .check-green {
      background: var(--color-green);
    }

    .check-red {
      background: var(--color-danger);
    }

    .checklist-icon {
      fill: transparent !important;
      margin-right: 10px;
    }
  }

  .disabled-button {
    background-color: var(--color-danger) !important;
  }
}

.onboarding-accordion {
  &::before {
    position: initial !important;
  }
}

.accordion-content {
  border-radius: 8px !important;
  margin-top: 30px !important;
  box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px !important;

  .table-container {
    box-shadow: none;
    padding: 0;
  }
}

.onboarding-accordion.MuiPaper-root {
  border-radius: 8px;
  margin-top: 20px;
  box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
  .reset-button-wrap {
    border: none !important;
    font-weight: 500 !important;
    color: var(--text-green) !important;
  }
  &:last-of-type {
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
  }
  .reset-wrap {
    svg {
      path {
        fill: var(--color-green);
      }
    }
  }
}

.Reset-password-pin-sec {
  .generate-password-section {
    .reset-password-display-grid {
      display: flex;
      align-items: flex-end;

      .reset-password-textfield-sec {
        width: calc(100% - 80px);
      }

      .generate-password-btn {
        svg {
          width: 24px;
          height: 24px;
          margin-left: 16px;
        }
      }

      .disabled-copy {
        opacity: 0.7;
        cursor: default;
      }
    }
  }
}

.Rejected-details {
  padding: 8px 12px;
  display: flex;
  align-items: center;
  background-color: var(--color-light-champagne);
  svg {
    fill: var(--icon-color-warning) !important;
    margin-right: 16px;
    align-self: flex-start;
    width: 20px;
    height: 20px;
  }
}

.Rejected-details.more-zero {
  svg {
    fill: var(--color-warning) !important;
  }
}

.Rejected-details.equal-zero {
  svg {
    fill: var(--color-orange) !important;
  }
}

.Rejected-details.less-zero {
  background-color: var(--color-danger-background);

  svg {
    fill: var(--color-danger) !important;
  }
}
