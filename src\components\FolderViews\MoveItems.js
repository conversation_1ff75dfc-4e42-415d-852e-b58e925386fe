import React, { useState } from 'react';
import {
  Box,
  ListItem,
  ListItemButton,
  ListItemText,
  Collapse,
  Checkbox,
} from '@mui/material';
import CustomButton from '@/components/UI/CustomButton';
import { FolderDocIcon } from '../../helper/common/images';
import ArrowRightIcon from '@mui/icons-material/ArrowRight';
import './folder.scss';

const MoveItems = ({
  DocumentFolderList,
  isMove,
  handleCloseDialog,
  createModalTitle,
  CopyDoctMedia,
  MoveDoctMedia,
}) => {
  const [selectedMenu, setSelectedMenu] = useState([]); // Default to first item
  const [openMenu, setOpenMenu] = useState({});

  const handleListItemClick = (event, item) => {
    const selected = [];
    selected.push(item?.id);
    setSelectedMenu(selected);
  };

  const handleClick = (item) => {
    setOpenMenu((prevState) => ({
      ...prevState,
      [item?.category_name]: !prevState[item?.category_name],
    }));
  };

  const handleNestedMenus = (children) => {
    return children?.map((subOption) => {
      const isSelected = selectedMenu?.indexOf(subOption?.id) !== -1;

      if (!subOption?.children) {
        return (
          <div key={subOption?.id}>
            <ListItem
              className={`folder-list-item popover-list-item ${
                isSelected ? 'selected' : ''
              }`}
            >
              <Box style={{ marginLeft: '25px' }}></Box>
              <ListItemButton className="folder-list-btn document-folder-btn">
                <FolderDocIcon className="sidebar-list-icon" />
              </ListItemButton>
              <ListItemText
                className="sidebar-list-menu-name"
                primary={subOption?.category_name}
              />
              <Checkbox
                className="table-checkbox"
                onClick={(event) => handleListItemClick(event, subOption)}
                checked={selectedMenu?.indexOf(subOption?.id) !== -1}
              />
            </ListItem>
          </div>
        );
      }
      return (
        <div key={subOption?.id}>
          <ListItem
            className="folder-list-item popover-list-item"
            button
            onClick={() => handleClick(subOption)}
          >
            {subOption && subOption?.children?.length !== 0 ? (
              openMenu[subOption?.name] ? (
                <ArrowRightIcon />
              ) : (
                <ArrowRightIcon />
              )
            ) : (
              <Box style={{ marginLeft: '25px' }}></Box>
            )}
            <ListItemButton className="folder-list-btn document-folder-btn cursor-pointer">
              {' '}
              <FolderDocIcon className="sidebar-list-icon" />
            </ListItemButton>
            <ListItemText
              className="sidebar-list-menu-name"
              primary={subOption?.category_name}
            />
            <Checkbox
              className="table-checkbox"
              onClick={(event) => handleListItemClick(event, subOption)}
              checked={selectedMenu?.indexOf(subOption?.id) !== -1}
            />
          </ListItem>
          <Collapse
            in={openMenu[subOption?.category_name]}
            timeout="auto"
            unmountOnExit
            style={{ marginLeft: '21px' }}
          >
            <Box className="submenu-option">
              {handleNestedMenus(subOption?.children)}
            </Box>
          </Collapse>
        </div>
      );
    });
  };

  return (
    <Box className="folder-view-container move-copy-document">
      <Box className="folder-list-wrap ">
        {' '}
        {handleNestedMenus(DocumentFolderList)}
      </Box>
      <Box className="form-actions-btn">
        <CustomButton
          fullWidth
          variant="outlined"
          title="Cancel"
          onClick={handleCloseDialog}
        />
        <CustomButton
          fullWidth
          variant="contained"
          disabled={!selectedMenu || selectedMenu?.length === 0}
          onClick={() => {
            if (isMove) {
              MoveDoctMedia(selectedMenu?.[0], createModalTitle?.id);
            } else {
              let id = [];
              id?.push(createModalTitle?.id);
              CopyDoctMedia(selectedMenu?.[0], id);
            }
          }}
          title={isMove ? 'Move' : 'Copy'}
        />
      </Box>
    </Box>
  );
};

export default MoveItems;
