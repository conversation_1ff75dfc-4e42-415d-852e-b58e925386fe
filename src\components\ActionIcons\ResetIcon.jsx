import React from 'react';

const ResetIcon = ({ onClick }) => {
  return (
    <div className="action-icon d-flex reset-icon" onClick={onClick}>
      <Reset />
    </div>
  );
};

export default ResetIcon;

const Reset = (props) => {
  return (
    <>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="icon icon-tabler icon-tabler-rotate-clockwise"
        width="44"
        height="44"
        viewBox="0 0 24 24"
        stroke-width="2"
        stroke="#2c3e50"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        {...props}
      >
        <path d="M4.05 11a8 8 0 1 1 .5 4m-.5 5v-5h5" />
      </svg>
    </>
  );
};
