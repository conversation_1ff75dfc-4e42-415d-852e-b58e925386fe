import React, { useEffect, useState } from 'react';
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Menu,
  MenuItem,
  Typography,
  TableSortLabel,
  ListItemIcon,
} from '@mui/material';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import VisibilityOutlinedIcon from '@mui/icons-material/VisibilityOutlined';
import {
  DocIcon,
  FolderIcon,
  PdfIcon,
  XlsIcon,
} from '../../helper/common/images';
import CustomCheckbox from '../UI/CustomCheckbox';
import moment from 'moment';
import './folder.scss';

const FolderListViews = ({
  folderData,
  menuList,
  selectedFolderData,
  setSelectedFolderData,
  handleOptionManage,
  handleView,
}) => {
  const [selected, setSelected] = useState(selectedFolderData || []);
  const [anchorEl, setAnchorEl] = useState(null);
  const [menuRowId, setMenuRowId] = useState(null);
  const [order, setOrder] = useState('asc');
  const [orderBy, setOrderBy] = useState('name');

  useEffect(() => {
    setSelected(selectedFolderData);
  }, [selectedFolderData]);

  const open = Boolean(anchorEl);
  const handleClick = (event, id) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
    setMenuRowId(id);
  };

  const handleClose = () => {
    setAnchorEl(null);
    setMenuRowId(null);
  };

  const handleSelectAllClick = (event) => {
    if (event.target.checked) {
      const newSelecteds = folderData?.map((n) => n?.id);
      setSelected(newSelecteds);
      setSelectedFolderData(newSelecteds);
      return;
    }
    setSelected([]);
    setSelectedFolderData([]);
  };

  const handleCheckBoxClick = (event, id) => {
    const selectedIndex = selected?.indexOf(id);
    let newSelected = [];

    if (selectedIndex === -1) {
      newSelected = newSelected?.concat(selected, id);
    } else if (selectedIndex === 0) {
      newSelected = newSelected?.concat(selected?.slice(1));
    } else if (selectedIndex === selected?.length - 1) {
      newSelected = newSelected?.concat(selected?.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelected = newSelected?.concat(
        selected?.slice(0, selectedIndex),
        selected?.slice(selectedIndex + 1)
      );
    }
    setSelectedFolderData(newSelected);
    setSelected(newSelected);
  };

  const isSelected = (id) => selected?.indexOf(id) !== -1;

  const handleRequestSort = (property) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  // const sortedData = folderData?.slice().sort((a, b) => {
  //   if (orderBy === 'updateDate') {
  //     return order === 'asc'
  //       ? moment(a[orderBy]).unix() - moment(b[orderBy]).unix()
  //       : moment(b[orderBy]).unix() - moment(a[orderBy]).unix();
  //   }
  //   if (a[orderBy] < b[orderBy]) {
  //     return order === 'asc' ? -1 : 1;
  //   }
  //   if (a[orderBy] > b[orderBy]) {
  //     return order === 'asc' ? 1 : -1;
  //   }
  //   return 0;
  // });
  const sortedData = folderData?.slice().sort((a, b) => {
    const aVal = a?.[orderBy];
    const bVal = b?.[orderBy];

    // Handle null or undefined
    if (aVal == null || bVal == null) return 0;

    // Special case for updateDate using moment
    if (orderBy === 'updateDate') {
      const aDate = moment(aVal).unix();
      const bDate = moment(bVal).unix();
      return order === 'asc' ? aDate - bDate : bDate - aDate;
    }

    // Case-insensitive string comparison
    if (typeof aVal === 'string' && typeof bVal === 'string') {
      return order === 'asc'
        ? aVal.localeCompare(bVal)
        : bVal.localeCompare(aVal);
    }

    // Numeric or other type comparison
    if (aVal < bVal) return order === 'asc' ? -1 : 1;
    if (aVal > bVal) return order === 'asc' ? 1 : -1;
    return 0;
  });

  return (
    <Box className="folder-list-view">
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell sx={{ width: '50px' }}>
                <CustomCheckbox
                  indeterminate={
                    selected?.length > 0 &&
                    selected?.length < folderData?.length
                  }
                  checked={
                    folderData?.length > 0 &&
                    selected?.length === folderData?.length
                  }
                  onChange={handleSelectAllClick}
                  className="table-checkbox"
                />
              </TableCell>
              <TableCell sx={{ width: '400px' }}>
                <TableSortLabel
                  active={orderBy === 'name'}
                  direction={orderBy === 'name' ? order : 'asc'}
                  onClick={() => handleRequestSort('name')}
                >
                  <Typography className="body-text fw600">Name</Typography>
                </TableSortLabel>
                {/* <Typography sx={{ width: '400px' }} className="body-text fw600">
                  Name
                </Typography> */}
              </TableCell>
              <TableCell>
                <TableSortLabel
                  active={orderBy === 'filesize'}
                  direction={orderBy === 'filesize' ? order : 'asc'}
                  onClick={() => handleRequestSort('filesize')}
                >
                  <Typography className="body-text fw600">Size</Typography>
                </TableSortLabel>
                {/* <Typography className="body-text fw600">Size</Typography> */}
              </TableCell>
              <TableCell>
                <TableSortLabel
                  active={orderBy === 'updateDate'}
                  direction={orderBy === 'updateDate' ? order : 'asc'}
                  onClick={() => handleRequestSort('updateDate')}
                >
                  <Typography className="body-text fw600">Modified</Typography>
                </TableSortLabel>
                {/* <Typography className="body-text fw600">Modified</Typography> */}
              </TableCell>
              <TableCell>
                <TableSortLabel
                  active={orderBy === 'cat_name'}
                  direction={orderBy === 'cat_name' ? order : 'asc'}
                  onClick={() => handleRequestSort('cat_name')}
                >
                  <Typography className="body-text fw600">Type</Typography>
                </TableSortLabel>
                {/* <Typography className="body-text fw600">Type</Typography> */}
              </TableCell>
              <TableCell></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {sortedData?.map((row, i) => {
              const isItemSelected = isSelected(row?.id);
              const isInactive = row?.status === 'inactive';
              return (
                <React.Fragment key={i}>
                  <TableRow className="blank-table-row"></TableRow>
                  <TableRow
                    hover
                    //   role="checkbox"
                    aria-checked={isItemSelected}
                    tabIndex={-1}
                    key={row?.id}
                    selected={isItemSelected}
                    className={`details-table-row ${
                      isInactive ? 'inactive-folder' : ''
                    }`}
                  >
                    <TableCell sx={{ width: '50px' }}>
                      <CustomCheckbox
                        className="table-checkbox"
                        checked={isItemSelected}
                        onChange={(event) =>
                          handleCheckBoxClick(event, row?.id)
                        }
                      />
                    </TableCell>
                    <TableCell
                      sx={{ width: '400px' }}
                      onClick={() => handleView(row)}
                    >
                      <Box className="d-flex justify-start align-center gap-sm list-folder-name-view">
                        <Box>
                          {row?.filetype === 'pdf' ? (
                            <PdfIcon className="list-view-folder" />
                          ) : row?.filetype === 'doc' ? (
                            <DocIcon className="list-view-folder" />
                          ) : row?.filetype === 'xls' ? (
                            <XlsIcon className="list-view-folder" />
                          ) : (
                            <FolderIcon className="list-view-folder" />
                          )}
                        </Box>
                        <Typography className="title-text text-ellipsis-line">
                          {row?.name}
                        </Typography>
                      </Box>
                      {/* <Typography className="body-text fw400">{row?.name}</Typography> */}
                    </TableCell>
                    <TableCell onClick={() => handleView(row)}>
                      <Typography className="title-text">
                        {row?.filesize}
                      </Typography>
                    </TableCell>
                    <TableCell onClick={() => handleView(row)}>
                      <Typography className="title-text">
                        {moment(row?.updateDate).format('DD.MM.YYYY')}
                      </Typography>
                    </TableCell>
                    <TableCell onClick={() => handleView(row)}>
                      <Typography className="title-text text-capital">
                        {row?.emp_contract_category?.name}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Box
                        className="cursor-pointer"
                        onClick={(event) => handleClick(event, row?.id)}
                      >
                        <MoreVertIcon />
                      </Box>
                      <Menu
                        id="long-menu"
                        anchorEl={anchorEl}
                        open={open && menuRowId === row?.id}
                        onClose={handleClose}
                        sx={{
                          '& .MuiPaper-root': {
                            maxWidth: '350px',
                            minWidth: '250px',
                          },
                        }}
                      >
                        {menuList?.map((option) => {
                          const updatedOption =
                            row?.status === 'inactive' &&
                            option?.value === 'inactive'
                              ? {
                                  id: 5,
                                  name: 'Active',
                                  value: 'active',
                                  icon: <VisibilityOutlinedIcon />,
                                }
                              : option;
                          if (
                            row?.emp_contract_category?.type === 'general' &&
                            (option?.id == 7 || option?.id == 8)
                          ) {
                            return <></>;
                          } else if (
                            row?.emp_contract_category?.type !== 'general' &&
                            option?.id == 9
                          ) {
                            return <></>;
                          } else if (isInactive && option?.id == 1) {
                            return <></>;
                          } else if (
                            row?.department_user_id?.length > 0 &&
                            option?.id == 6
                          ) {
                            return <></>;
                          } else {
                            return (
                              <MenuItem
                                key={updatedOption?.id}
                                onClick={() => {
                                  handleOptionManage(updatedOption, row);
                                  handleClose();
                                }}
                              >
                                <ListItemIcon>
                                  {updatedOption?.icon}
                                </ListItemIcon>
                                {updatedOption?.name}
                              </MenuItem>
                            );
                          }
                        })}
                      </Menu>
                    </TableCell>
                  </TableRow>
                </React.Fragment>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default FolderListViews;
