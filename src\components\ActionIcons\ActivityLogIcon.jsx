import React from 'react';

const ActivityLogIcon = ({ onClick }) => {
  return (
    <div className="action-icon d-flex" onClick={onClick}>
      <Activity className="border-svg" />
    </div>
  );
};

export default ActivityLogIcon;

const Activity = (props) => {
  return (
    <svg
      {...props}
      width="24px"
      height="24px"
      viewBox="-2 -2 24 24"
      strokeWidth="0px"
    >
      <path d="M6 0h8a6 6 0 0 1 6 6v8a6 6 0 0 1-6 6H6a6 6 0 0 1-6-6V6a6 6 0 0 1 6-6zm0 2a4 4 0 0 0-4 4v8a4 4 0 0 0 4 4h8a4 4 0 0 0 4-4V6a4 4 0 0 0-4-4H6zm6 7h3a1 1 0 0 1 0 2h-3a1 1 0 0 1 0-2zm-2 4h5a1 1 0 0 1 0 2h-5a1 1 0 0 1 0-2zm0-8h5a1 1 0 0 1 0 2h-5a1 1 0 1 1 0-2zm-4.172 5.243L7.95 8.12a1 1 0 1 1 1.414 1.415l-2.828 2.828a1 1 0 0 1-1.415 0L3.707 10.95a1 1 0 0 1 1.414-1.414l.707.707z"></path>
    </svg>
  );
};
