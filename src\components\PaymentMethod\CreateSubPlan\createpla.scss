@import '@/styles/variable.scss';

.create-plan-wrap {
 background-color: var(--color-white);
  padding: var(--spacing-xxl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-xs);
  margin: 0 auto;
  .create-sub-plan-form {
    .range-input-wrap {
      display: flex;
      gap: var(--spacing-sm);
    }
    .create-form-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: var(--spacing-xl);

      @media (max-width: 767px) {
        grid-template-columns: repeat(1, 1fr);
      }
    }

    .checkbox-wrap {
      margin-right: 10px;
      padding: 0px !important;

      .MuiSvgIcon-root {
        height: var(--icon-size-sm);
        width: var(--icon-size-sm);
        fill: var(--icon-color-primary);
      }
      .set-as-default {
        font-family: var(--font-family-primary);
      }
    }
  }

  @media (max-width: 1200px) {
    width: 80%;
  }

  @media (max-width: 992px) {
    width: 90%;
  }

  @media (max-width: 899px) {
    width: 100%;
  }

  @media (max-width: 575px) {
    padding: 20px 12px 56px;
  }
}
