@import '@/styles/variable.scss';

.react-datepicker-sec {
  // fieldset {
  //   height: 30px !important;
  //   min-height: 30px !important;
  //   top: -6px !important;
  // }
  .react-datepicker-popper {
    z-index: 1000;
  }
  .react-datepicker-wrapper {
    width: 100%;

    .react-datepicker__close-icon {
      right: 40px;
      top: 1px;
      cursor: pointer;
      z-index: 999;

      &::after {
        background-color: rgba(0, 0, 0, 0.54);
      }
    }

    .MuiInputBase-input {
      padding: 8px 7px 8px 8px;
    }
    fieldset,
    .MuiOutlinedInput-notchedOutline {
      border: 1px solid $color-Dark-10 !important;
      border-radius: 8px !important;
    }
  }
  .MuiFormLabel-root,
  .MuiFormLabel-root span {
    font-family: var(--font-family-primary);
    margin-bottom: var(--spacing-xxs);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-bright-blue);
    // display: flex;
    // align-items: center;
    // width: 100%;

    &.error-label {
      color: var(--text-error);
    }
    .label-icon {
      width: var(--font-size-sm);
      height: var(--font-size-sm);
      cursor: pointer;
    }
    .required {
      color: var(--text-error);
      margin-left: var(--spacing-xxs);
    }
  }
  .MuiFormLabel-root {
    width: 300px;
  }
  .react-datepicker__tab-loop {
    z-index: 1;

    .react-datepicker__month-container {
      .react-datepicker__day {
        border-radius: 20px;
      }

      .react-datepicker__day--today {
        font-weight: 600;
      }
      .react-datepicker__week-number--selected {
        background: var(--color-primary);
        color: var(--text-color-white);
      }
      .react-datepicker__day--selected {
        background: var(--color-primary);
        color: var(--text-color-white);
        font-size: var(--font-size-sm);
      }

      .react-datepicker__week {
        .react-datepicker__day--keyboard-selected {
          background: var(--color-primary);
          color: var(--color-white);
          border: var(--border-width-sm);
          border-radius: 20px;
          font-weight: 600;

          &:hover {
            background: var(--color-primary);
          }
        }
      }
    }

    .react-datepicker__time {
      .react-datepicker__time-box {
        .react-datepicker__time-list {
          .react-datepicker__time-list-item {
            align-content: center;
          }

          .react-datepicker__time-list-item--selected {
            background: var(--field-background);
            font-weight: 600;
            color: var(--text-color-black);

            &:hover {
              background: var(--field-background);
            }
          }
        }
      }
    }
  }

  .MuiFormControl-root {
    .MuiInputBase-root {
      background: var(--field-background);
      font-family: var(--font-family-primary);
      color: var(--text-color-black);
      font-size: var(--font-size-sm);
      line-height: var(--line-height-sm);
      margin: var(--spacing-none);
      .MuiSelect-select {
        z-index: 1;
      }
    }

    .MuiFormLabel-root {
      z-index: -1;
    }
  }
}
