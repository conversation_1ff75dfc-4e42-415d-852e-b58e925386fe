'use client';
import React, { useState, useEffect } from 'react';
import { Box, Typography } from '@mui/material';
import AttachmentHistory from './AttachmentHistory';
import { supportTicketService } from '@/services/supportTicketService';
import { setApiMessage } from '@/helper/common/commonFunctions';
import ContentLoader from '@/components/UI/ContentLoader';
import { EmptyHistorySVG } from '@/helper/common/images';
import './history.scss';

export default function History({ ticketId }) {
  const [loading, setLoading] = useState(false);
  const [historyData, setHistoryData] = useState([]);

  // Fetch ticket history from API
  const fetchTicketHistory = async () => {
    if (!ticketId) return;

    setLoading(true);
    try {
      const response = await supportTicketService.getTicketHistory(ticketId, {
        page: 1,
        limit: 20,
      });

      if (response?.data) {
        // Store API response directly in state
        setHistoryData(response?.data);
      } else {
        setHistoryData([]);
      }
    } catch (error) {
      console.error('Error fetching ticket history:', error);
      setApiMessage('error', error?.response?.data?.message);
      setHistoryData([]);
    } finally {
      setLoading(false);
    }
  };

  // Fetch data on component mount
  useEffect(() => {
    fetchTicketHistory();
  }, [ticketId]);

  // Render content
  const renderContent = () => {
    if (loading) {
      return (
        <Box className="d-flex justify-center align-center flex-col pt16 pb32">
          <ContentLoader />
        </Box>
      );
    }

    if (!historyData || historyData.length === 0) {
      return (
        <Box className="d-flex justify-center align-center flex-col pt16 pb32">
          <EmptyHistorySVG />
          <Typography className="no-history-wrap body-sm">
            No history entries found
          </Typography>
        </Box>
      );
    }

    return <AttachmentHistory attachmentData={historyData} />;
  };

  return <Box className="history-wrap">{renderContent()}</Box>;
}
