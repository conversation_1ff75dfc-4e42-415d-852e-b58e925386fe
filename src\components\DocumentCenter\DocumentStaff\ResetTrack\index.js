'use client';
import React, { useRef, useState } from 'react';
import {
  Box,
  Typography,
  ListItem,
  ListItemButton,
  ListItemText,
  Collapse,
  Checkbox,
} from '@mui/material';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { FolderDocIcon } from '@/helper/common/images';
import CustomButton from '@/components/UI/CustomButton';
import Multiselect from '@/components/UI/CustomMultiSelect';
import ArrowRightIcon from '@mui/icons-material/ArrowRight';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import ConfirmationModal from '@/components/UI/ConfirmationModal';
import DialogBox from '@/components/UI/Modalbox';

export default function ResetTrack({
  staffList,
  isLoader,
  branchList,
  departmentList,
  //   roleList,
  setToggleModal,
  setStaffList,
  setBranchList,
  setDepartmentList,
  DocumentFolderList,
  getDocumentFolderListTrack,
  onSaveTrack,
  //   setRoleList
}) {
  const formikRef = useRef(null);
  const [selectedMenu, setSelectedMenu] = useState([]);
  const [openMenu, setOpenMenu] = useState({});
  const [resetTrack, setResetTrack] = useState(false);
  const [resetTrackData, setResetTrackData] = useState();
  const handleClose = () => {
    setResetTrack(false);
    setResetTrackData();
  };
  const handleListItemClick = (event, item) => {
    // const selected = [];
    // selected.push(item?.id);
    // setSelectedMenu(selected);
    const currentIndex = selectedMenu.indexOf(item?.id);
    const newChecked = [...selectedMenu];
    if (currentIndex === -1) {
      newChecked.push(item?.id);
    } else {
      newChecked.splice(currentIndex, 1);
    }
    setSelectedMenu(newChecked);
  };
  const handleResetTrack = async () => {
    const extractIds = (array) => array?.map((item) => item.value);
    const userValues = extractIds(
      resetTrackData?.individualuser ? resetTrackData?.individualuser : []
    );
    const branchValues = extractIds(
      resetTrackData?.branch ? resetTrackData?.branch : []
    );
    const departmentValues = extractIds(
      resetTrackData?.department ? resetTrackData?.department : []
    );

    const body = new FormData();
    userValues && body.append('user_ids', userValues);
    branchValues && body.append('branches', branchValues);
    departmentValues && body.append('departments', departmentValues);
    selectedMenu && body.append('category_ids', selectedMenu);
    const ApiUrl = URLS.USER_RESET_ALL_CATEGORY_TRACK;
    const method = 'post';
    const config = {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    };
    try {
      const { status, data } = await axiosInstance[method](
        ApiUrl,
        body,
        config
      );
      if (status === 200 || status === 201) {
        if (data?.status) {
          handleClose();
          setApiMessage('success', data?.message);
          setTimeout(() => {
            setToggleModal(false);
            onSaveTrack();
          }, 500);
        } else {
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const handleClick = (item) => {
    setOpenMenu((prevState) => ({
      ...prevState,
      [item?.category_name]: !prevState[item?.category_name],
    }));
  };
  const handleNestedMenus = (children, values) => {
    return children?.map((subOption) => {
      const isSelected = selectedMenu?.indexOf(subOption?.id) !== -1;

      if (!subOption?.children) {
        return (
          <div key={subOption?.id}>
            <ListItem
              className={`folder-list-item popover-list-item ${
                isSelected ? 'selected' : ''
              }`}
            >
              <Box style={{ marginLeft: '25px' }}></Box>
              <ListItemButton className="folder-list-btn document-folder-btn">
                <FolderDocIcon className="sidebar-list-icon" />
              </ListItemButton>
              <ListItemText
                className="sidebar-list-menu-name"
                primary={subOption?.category_name}
              />
              <Checkbox
                className="table-checkbox"
                onClick={(event) =>
                  values?.individualuser?.length === 0 &&
                  handleListItemClick(event, subOption)
                }
                checked={selectedMenu?.indexOf(subOption?.id) !== -1}
                disabled={values?.individualuser?.length > 0}
              />
            </ListItem>
          </div>
        );
      }
      return (
        <div key={subOption?.id}>
          <ListItem
            className={`folder-list-item popover-list-item ${
              values?.individualuser?.length > 0 && `cursor-default`
            }`}
            button
            onClick={() =>
              values?.individualuser?.length === 0 && handleClick(subOption)
            }
          >
            {subOption && subOption?.children?.length !== 0 ? (
              openMenu[subOption?.name] ? (
                <ArrowRightIcon />
              ) : (
                <ArrowRightIcon />
              )
            ) : (
              <Box style={{ marginLeft: '25px' }}></Box>
            )}
            <ListItemButton
              className={`folder-list-btn document-folder-btn ${
                values?.individualuser?.length > 0
                  ? 'cursor-default'
                  : 'cursor-pointer'
              }`}
            >
              {' '}
              <FolderDocIcon className="sidebar-list-icon" />
            </ListItemButton>
            <ListItemText
              className="sidebar-list-menu-name"
              primary={subOption?.category_name}
            />
            <Checkbox
              className="table-checkbox"
              onClick={(event) =>
                values?.individualuser?.length === 0 &&
                handleListItemClick(event, subOption)
              }
              checked={selectedMenu?.indexOf(subOption?.id) !== -1}
              disabled={values?.individualuser?.length > 0}
            />
          </ListItem>
          <Collapse
            in={openMenu[subOption?.category_name]}
            timeout="auto"
            unmountOnExit
            style={{ marginLeft: '21px' }}
          >
            <Box className="submenu-option">
              {handleNestedMenus(subOption?.children, values)}
            </Box>
          </Collapse>
        </div>
      );
    });
  };
  return (
    <Box>
      <Formik
        innerRef={formikRef}
        initialValues={{
          individualuser: [],
          branch: [],
          department: [],
          //   role: []
        }}
        enableReinitialize={true}
        validationSchema={Yup.object().shape({})}
        onSubmit={async (requestdata) => {
          setResetTrack(true);
          setResetTrackData(requestdata);
        }}
      >
        {({ errors, touched, values, setFieldValue, handleSubmit }) => (
          <Form onSubmit={handleSubmit}>
            <Box className="pt8">
              <Multiselect
                placeholder="Branch"
                className={
                  values?.individualuser?.length > 0
                    ? 'disabled-multifield'
                    : touched.branch && errors.branch
                      ? 'textfeild-error'
                      : ''
                }
                options={branchList}
                isOptionWithColor={true}
                value={values?.branch}
                error={touched.branch && errors.branch}
                // onChange={(e) => setFieldValue('branch', e)}
                onChange={(e) => {
                  const filterValue = e?.find((f) => f?.value === 'all');
                  const branchValue = branchList?.filter(
                    (f) => f?.value !== 'all'
                  );
                  if (filterValue || e?.length === branchValue?.length) {
                    const filterall = branchList?.filter(
                      (b) => b?.value !== 'all'
                    );
                    setBranchList(filterall);
                    const selectedValue = filterValue ? filterall : e;
                    setFieldValue('branch', selectedValue);
                  }
                  if (!filterValue) {
                    const isAll = branchList?.find((f) => f?.value === 'all');
                    if (!isAll) {
                      const alloption = [{ label: 'Select all', value: 'all' }];
                      let mergeList = _.concat(alloption, branchList);
                      setBranchList(mergeList);
                    }
                    setFieldValue('branch', e);
                    const selectBranch = e?.map((m) => m?.value);
                    const selectDep = values?.department?.map((m) => m?.value);
                    getDocumentFolderListTrack(selectBranch, selectDep);
                    setSelectedMenu([]);
                  }
                }}
                label={
                  <span>
                    Branch
                    {/* <span className="primary-color"> *</span> */}
                  </span>
                }
                disabled={values?.individualuser?.length > 0}
              />
              {touched.branch && errors.branch && (
                <Typography
                  variant="body2"
                  color="error"
                  className="field-error"
                >
                  {errors.branch}
                </Typography>
              )}
            </Box>
            <Box className="pt8">
              <Multiselect
                placeholder="Department"
                className={
                  values?.individualuser?.length > 0
                    ? 'disabled-multifield'
                    : touched.department && errors.department
                      ? 'textfeild-error'
                      : ''
                }
                options={departmentList}
                value={values?.department}
                error={touched.department && errors.department}
                // onChange={(e) => setFieldValue('department', e)}
                onChange={(e) => {
                  const filterValue = e?.find((f) => f?.value === 'all');
                  const branchValue = departmentList?.filter(
                    (f) => f?.value !== 'all'
                  );
                  if (filterValue || e?.length === branchValue?.length) {
                    const filterall = departmentList?.filter(
                      (b) => b?.value !== 'all'
                    );
                    setDepartmentList(filterall);
                    const selectedValue = filterValue ? filterall : e;
                    setFieldValue('department', selectedValue);
                  }
                  if (!filterValue) {
                    const isAll = departmentList?.find(
                      (f) => f?.value === 'all'
                    );
                    if (!isAll) {
                      const alloption = [{ label: 'Select all', value: 'all' }];
                      let mergeList = _.concat(alloption, departmentList);
                      setDepartmentList(mergeList);
                    }
                    setFieldValue('department', e);
                    const selectDeps = e?.map((m) => m?.value);
                    const selectBranchs = values?.branch?.map((m) => m?.value);
                    getDocumentFolderListTrack(selectBranchs, selectDeps);
                    setSelectedMenu([]);
                  }
                }}
                label={
                  <span>
                    Department
                    {/* <span className="primary-color"> *</span> */}
                  </span>
                }
                disabled={
                  values?.individualuser?.length > 0
                  //   || values?.role?.length > 0
                }
              />
              {touched.department && errors.department && (
                <Typography
                  variant="body2"
                  color="error"
                  className="field-error"
                >
                  {errors.department}
                </Typography>
              )}
            </Box>
            {/* <Box className="select-box pt32 pb32">
              <Multiselect
                placeholder="Description"
                className={touched.role && errors.role ? 'textfeild-error' : ''}
                options={roleList}
                value={values?.role}
                error={touched.role && errors.role}
                // onChange={(e) => setFieldValue('role', e)}
                onChange={(e) => {
                  const filterValue = e?.find((f) => f?.value === 'all');
                  const branchValue = roleList?.filter(
                    (f) => f?.value !== 'all'
                  );
                  if (filterValue || e?.length === branchValue?.length) {
                    const filterall = roleList?.filter(
                      (b) => b?.value !== 'all'
                    );
                    setRoleList(filterall);
                    const selectedValue = filterValue ? filterall : e;
                    setFieldValue('role', selectedValue);
                  }
                  if (!filterValue) {
                    const isAll = roleList?.find((f) => f?.value === 'all');
                    if (!isAll) {
                      const alloption = [{ label: 'Select all', value: 'all' }];
                      let mergeList = _.concat(alloption, roleList);
                      setRoleList(mergeList);
                    }
                    setFieldValue('role', e);
                  }
                }}
                menuPoisition="top"
                label={
                  <span>
                    Description
          
                  </span>
                }
                disabled={
                  values?.individualuser?.length > 0 ||
                  values?.department?.length > 0
                }
              />
              {touched.role && errors.role && (
                <Typography
                  variant="body2"
                  color="error"
                  className="field-error"
                >
                  {errors.role}
                </Typography>
              )}
            </Box> */}
            <Box className="folder-view-container move-copy-document pt8">
              <Typography className="field-label pt8">Documents</Typography>
              <Box
                className={`folder-list-wrap pt4 ${
                  values?.individualuser?.length > 0 && 'cursor-default'
                }`}
              >
                {' '}
                {handleNestedMenus(DocumentFolderList, values)}
              </Box>
            </Box>
            <Box className="select-box pt32 ">
              <Multiselect
                placeholder="Staff user"
                className={
                  values?.branch?.length > 0 || values?.department?.length > 0
                    ? 'disabled-multifield'
                    : touched.individualuser && errors.individualuser
                      ? 'textfeild-error'
                      : ''
                }
                options={staffList}
                value={values?.individualuser}
                error={touched.individualuser && errors.individualuser}
                // onChange={(e) => setFieldValue('individualuser', e)}
                onChange={(e) => {
                  const filterValue = e?.find((f) => f?.value === 'all');
                  const branchValue = staffList?.filter(
                    (f) => f?.value !== 'all'
                  );
                  if (filterValue || e?.length === branchValue?.length) {
                    const filterall = staffList?.filter(
                      (b) => b?.value !== 'all'
                    );
                    setStaffList(filterall);
                    const selectedValue = filterValue ? filterall : e;
                    setFieldValue('individualuser', selectedValue);
                  }
                  if (!filterValue) {
                    const isAll = staffList?.find((f) => f?.value === 'all');
                    if (!isAll) {
                      const alloption = [{ label: 'Select all', value: 'all' }];
                      let mergeList = _.concat(alloption, staffList);
                      setStaffList(mergeList);
                    }
                    setFieldValue('individualuser', e);
                  }
                }}
                label={
                  <span>
                    Staff user
                    {/* <span className="primary-color"> *</span> */}
                  </span>
                }
                disabled={
                  values?.branch?.length > 0 || values?.department?.length > 0
                  //  || values?.role?.length > 0
                }
              />
              {touched.individualuser && errors.individualuser && (
                <Typography
                  variant="body2"
                  color="error"
                  className="field-error"
                >
                  {errors.individualuser}
                </Typography>
              )}
            </Box>
            <Box className="form-actions-btn">
              <CustomButton
                fullWidth
                variant="outlined"
                title="Cancel"
                onClick={() => setToggleModal(false)}
              />
              <CustomButton
                fullWidth
                variant="contained"
                title={isLoader ? 'Reset...' : 'Reset'}
                type="submit"
                // disabled={isLoader}
                disabled={
                  isLoader ||
                  (values?.individualuser?.length === 0 &&
                    values?.branch?.length === 0 &&
                    values?.department?.length === 0 &&
                    values?.role?.length === 0)
                }
              />
            </Box>
          </Form>
        )}
      </Formik>
      <DialogBox
        open={resetTrack}
        handleClose={() => {
          handleClose();
        }}
        className="confirmation-modal"
        dividerClass="confirmation-modal-divider"
        title="Confirmation"
        content={
          <>
            <ConfirmationModal
              handleCancel={handleClose}
              handleConfirm={handleResetTrack}
              text={`Are you sure you want to reset the track?`}
            />
          </>
        }
      />
    </Box>
  );
}
