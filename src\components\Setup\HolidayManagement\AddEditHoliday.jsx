'use client';
import React, { useState, useRef } from 'react';
import { Box, Tooltip, Typography } from '@mui/material';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import InfoIcon from '@mui/icons-material/Info';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomCheckbox from '@/components/UI/CustomCheckbox';
import CustomButton from '@/components/UI/CustomButton';

export default function AddEditHolidayModal({
  holidayAppliedFil,
  getHolidayList,
  setCreateModal,
  editType,
  setEditType,
}) {
  const [loader, setLoader] = useState(false);

  const formikRef = useRef(null);

  return (
    <Box>
      <Formik
        innerRef={formikRef}
        initialValues={{
          policyName: editType?.holiday_type_name || '',
          remark: editType?.holiday_type_description || '',
          defaultHoliday: editType?.has_holiday_type_default || false,
        }}
        enableReinitialize={true}
        validationSchema={Yup.object().shape({
          policyName: Yup.string().trim().required('This field is required'),
        })}
        onSubmit={async (requestData) => {
          setLoader(true);
          const sendData = {
            holiday_type_name: requestData?.policyName,
            holiday_type_description: requestData?.remark,
            has_holiday_type_default: requestData?.defaultHoliday,
          };
          let url = '';
          let method = '';
          if (editType) {
            url = URLS.UPDATE_HOLIDAY_TYPE + `${editType?.id}`;
            method = 'put';
          } else {
            url = URLS.CREATE_HOLIDAY_TYPE;
            method = 'post';
          }
          try {
            const { status, data } = await axiosInstance[method](url, sendData);

            if (status === 200 || status === 201) {
              if (data?.status) {
                setApiMessage('success', data?.message);
                setCreateModal(false);
                setTimeout(() => {
                  setEditType();
                }, 500);
                getHolidayList(
                  1,
                  holidayAppliedFil?.searchValue,
                  holidayAppliedFil,
                  '',
                  true
                );
              } else {
                setApiMessage('error', data?.message);
              }
              setLoader(false);
            }
          } catch (error) {
            setLoader(false);
            setApiMessage('error', error?.response?.data?.message);
          }
        }}
      >
        {({
          errors,
          touched,
          handleBlur,
          values,
          handleSubmit,
          handleChange,
          setFieldValue,
          dirty,
          isValid,
        }) => (
          <Form onSubmit={handleSubmit}>
            <Box className="pb8 add-leave-wrap">
              <Box>
                <CustomTextField
                  fullWidth
                  id="policyName"
                  name="policyName"
                  label="Holiday Name"
                  placeholder="Holiday Name"
                  value={values?.policyName}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={Boolean(touched.policyName && errors.policyName)}
                  helperText={touched.policyName && errors.policyName}
                  required
                />
              </Box>
              <Box className="pt16">
                <CustomTextField
                  fullWidth
                  id="remark"
                  name="remark"
                  label="Remark"
                  placeholder="Remark"
                  value={values?.remark}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  multiline
                  rows={4}
                  //   className="additional-textfeild"
                />
              </Box>
              <Box className="pt16 d-flex align-center gap-5">
                <CustomCheckbox
                  name="defaultHoliday"
                  checked={values?.defaultHoliday}
                  onChange={(e) => {
                    setFieldValue('defaultHoliday', e.target.checked);
                  }}
                  disableRipple
                  label={
                    <Typography className="sub-title-text">
                      Default Holiday
                    </Typography>
                  }
                />
                <Tooltip
                  title={
                    <Typography className="">
                      If you select this check box holiday will assigned to new
                      employee.
                    </Typography>
                  }
                  placement="right"
                  classes={{
                    tooltip: 'info-tooltip-container',
                  }}
                  arrow
                >
                  <InfoIcon className="info-icon cursor-poniter" />
                </Tooltip>
              </Box>
            </Box>

            <Box className="form-actions-btn">
              <CustomButton
                fullWidth
                variant="outlined"
                title="Cancel"
                onClick={() => {
                  setCreateModal(false);
                  setTimeout(() => {
                    setEditType();
                  }, 500);
                }}
              />
              <CustomButton
                variant="contained"
                title={`${loader ? 'Saving...' : 'Save'}`}
                disabled={!(dirty && isValid) || loader}
                fullWidth={false}
                type="submit"
              />
            </Box>
          </Form>
        )}
      </Formik>
    </Box>
  );
}
