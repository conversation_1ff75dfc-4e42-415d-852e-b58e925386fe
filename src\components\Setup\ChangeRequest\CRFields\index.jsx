import React from 'react';
import { Box, Typography } from '@mui/material';
import CustomButton from '@/components/UI/CustomButton';
import Icon from '@/components/UI/AppIcon/AppIcon';
import './crfields.scss';

const FieldCategorySection = ({
  category,
  selectedFields,
  isExpanded,
  onToggleExpansion,
  onFieldToggle,
  onSelectAll,
  onDeselectAll,
}) => {
  const selectedFieldsInCategory = selectedFields?.filter(
    (f) => f?.categoryId === category?.id
  );
  const allFieldsSelected =
    selectedFieldsInCategory?.length === category?.fields?.length;
  const someFieldsSelected =
    selectedFieldsInCategory?.length > 0 && !allFieldsSelected;

  const handleSelectAllToggle = () => {
    if (allFieldsSelected) {
      onDeselectAll(category?.id);
    } else {
      onSelectAll(category?.id, category?.fields);
    }
  };

  return (
    <Box className="field-category-section">
      {/* Category Header */}
      <Box className="category-header">
        <Box className="header-content">
          <CustomButton
            onClick={() => onToggleExpansion(category?.id)}
            className="category-toggle"
          >
            <Box className="category-icon">
              <Icon
                name={category?.icon}
                size={16}
                color="var(--color-primary)"
                strokeWidth={2}
                className="icon"
              />
            </Box>
            <Box className="category-info">
              <Typography className="body-text category-title">
                {category?.name}
              </Typography>
              <Typography className="title-text category-description">
                {category?.fields?.length} fields available
              </Typography>
            </Box>
            <Icon
              name={isExpanded ? 'ChevronDown' : 'ChevronDown'} //ChevronUp
              size={20}
              color="#64748B"
              strokeWidth={2}
              className={`expand-icon ${isExpanded ? 'expanded' : ''}`}
            />
          </CustomButton>

          <Box className="category-actions">
            <CustomButton
              onClick={handleSelectAllToggle}
              className={`select-all-button ${
                allFieldsSelected
                  ? 'all-selected'
                  : someFieldsSelected
                    ? 'some-selected'
                    : 'none-selected'
              }`}
              variant="text"
              title={allFieldsSelected ? 'Deselect All' : 'Select All'}
            />

            {selectedFieldsInCategory?.length > 0 && (
              <span className="sub-title-text selection-count">
                {selectedFieldsInCategory?.length} selected
              </span>
            )}
          </Box>
        </Box>
      </Box>

      {/* Category Fields */}
      {isExpanded && (
        <Box className="category-fields">
          <Box className="fields-container">
            {category?.fields?.map((field) => {
              const isSelected = selectedFields?.some(
                (f) => f?.id === field?.id
              );

              return (
                <Box
                  key={field.id}
                  className={`field-item ${isSelected ? 'selected' : ''}`}
                >
                  <Box className="field-content">
                    <button
                      onClick={() =>
                        onFieldToggle(field, category?.id, category?.name)
                      }
                      disabled={field?.required && isSelected}
                      className={`field-checkbox ${isSelected ? 'checked' : ''} ${
                        field?.required && isSelected ? 'disabled' : ''
                      }`}
                    >
                      {isSelected && (
                        <Icon
                          name="Check"
                          size={12}
                          color="white"
                          strokeWidth={3}
                          className="check-icon"
                        />
                      )}
                    </button>

                    <Box className="field-info">
                      <Box className="field-header">
                        <span
                          className={`title-text field-name ${isSelected ? 'selected' : ''}`}
                        >
                          {field?.name}
                        </span>
                        {field?.required && (
                          <span className="sub-title-text required-badge">
                            Required
                          </span>
                        )}
                      </Box>
                      {/* <Typography className="sub-title-text field-type">
                        {field?.type?.replace(/([A-Z])/g, ' $1').trim()}
                      </Typography> */}
                    </Box>
                  </Box>

                  {isSelected && (
                    <Box className="field-status">
                      <span className="sub-title-text order-badge">
                        #
                        {
                          selectedFields?.find((f) => f?.id === field?.id)
                            ?.order
                        }
                      </span>
                      <Icon
                        name="Check"
                        size={16}
                        color="var(--color-primary)"
                        strokeWidth={2}
                        className="check-icon"
                      />
                    </Box>
                  )}
                </Box>
              );
            })}
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default FieldCategorySection;
