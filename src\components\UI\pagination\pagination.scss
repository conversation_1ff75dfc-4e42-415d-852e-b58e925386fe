@import '../../../styles/variable.scss';

.pagination-wrap {
  border-top: 1px solid $color-Primary-10;
  display: flex;
  align-items: center;
  justify-content: space-between;
  @media (max-width: 499px) {
    display: flex;
    flex-direction: column;
    row-gap: 20px;
  }
  .page-number {
    color: $color-Dark-40;
  }

  .arrow-icon {
    background-color: transparent !important;
    padding: 0px;
    svg {
      width: 16px;
      height: 16px;
    }
  }

  .Mui-disabled {
    svg {
      //   opacity: 0.4;
    }
  }

  .ml24 {
    margin-left: 24px;
  }

  .mr8 {
    margin-right: 8px;
  }

  .page-select {
    min-width: 60px;
    border-radius: 8px;
    color: $color-Dark-60-60;

    .MuiSelect-select {
      padding: 9.5px 11px;
      font-family: $PrimaryFont;
      font-size: 14px;
      line-height: 21px;
      letter-spacing: -0.5px;
    }

    .MuiSelect-nativeInput {
      padding: 0px;
      border: none;
    }

    svg {
      top: 7px;
      @media (max-width: 599px) {
        top: 8px;
      }
    }

    .MuiOutlinedInput-input:hover {
      .MuiOutlinedInput-notchedOutline {
        border-color: transparent;
      }
    }
  }

  .Mui-focused {
    .MuiOutlinedInput-notchedOutline {
      border: 1px solid $color-Dark-10;
    }
  }
  .page-limit {
    width: 70px;
    margin-left: 16px;
    .MuiFormLabel-root {
      display: none;
    }
    .MuiInputBase-root {
      min-height: 40px !important;
    }
    .MuiInputBase-input {
      padding: 7.5px;
      text-align: center;
    }
  }
  .per-page-text {
    margin-right: 8px;
  }
}
.bt0 {
  border-top: 0 !important;
}
