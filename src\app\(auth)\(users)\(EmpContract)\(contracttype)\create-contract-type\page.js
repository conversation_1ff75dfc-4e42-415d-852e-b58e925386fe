'use client';

import React from 'react';
import { Box, Typography } from '@mui/material';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import { useRouter } from 'next/navigation';
import _ from 'lodash';
import AddEditContractType from '@/components/EMPContrat/AddEditContractType';
import '../contract-type/contracttype.scss';

export default function CreateContractType() {
  const router = useRouter();

  return (
    <>
      <Box className="page-container">
        <Box className="create-contract-type">
          <Box className="d-flex align-center">
            <ArrowBackIosIcon
              className="cursor-pointer"
              onClick={() => {
                router.push('/contract-type');
              }}
            />
            <Typography className="p16 fw600 pr8">
              Create Contract Type
            </Typography>
          </Box>
          <AddEditContractType isEdit={false} />
        </Box>
      </Box>
    </>
  );
}
