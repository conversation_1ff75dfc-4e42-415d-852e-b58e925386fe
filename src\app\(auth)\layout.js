'use client';

import { useEffect, useState, useContext } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { identifiers } from '@/helper/constants/identifier';
import AuthContext from '@/helper/authcontext';
import { Box } from '@mui/material';
import Header from './Header';
import MenuBar from './Sidebar';
import NotificationBanner from '@/components/UI/NotificationBanner';

export default function PrivateLayout({ children }) {
  const { IsDrawer, setIsDrawer } = useContext(AuthContext);
  const router = useRouter();
  const pathname = usePathname();
  const [authdata, setAuthData] = useState(false);
  const [open, setOpen] = useState(false);
  const [screenWidth, setScreenWidth] = useState(null);

  useEffect(() => {
    if (
      !JSON.parse(localStorage.getItem(identifiers?.AUTH_DATA)) &&
      pathname !== '/login' &&
      pathname !== '/otp' &&
      pathname !== '/forgot-password' &&
      pathname !== '/reset-password' &&
      pathname !== '/resetpassword'
    ) {
      router?.push('/login');
    }
    setAuthData(JSON.parse(localStorage.getItem(identifiers?.AUTH_DATA)));
    if (JSON.parse(localStorage.getItem('IsjustLogin'))) {
      setOpen(true);
      localStorage.removeItem('IsjustLogin');
    }
  }, []);
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const handleResize = () => {
        setScreenWidth(window.innerWidth);
        if (window.innerWidth < 900) {
          setOpen(false);
        }
      };

      window.addEventListener('resize', handleResize);
      handleResize();
      return () => {
        window.removeEventListener('resize', handleResize);
      };
    }
  }, [screenWidth]);
  useEffect(() => {
    if (IsDrawer) {
      setOpen(false);
      setIsDrawer(false);
    }
  }, [IsDrawer]);

  return (
    <Box className="layout-wrap">
      {authdata && (
        <>
          <NotificationBanner />
          <Header open={open} />
          <Box
            style={{ paddingTop: 'var(--header-height)' }}
            className={open ? 'opened-layout' : ''}
          >
            {children}
          </Box>

          <MenuBar open={open} setOpen={setOpen} />
        </>
      )}
    </Box>
  );
}
