'use client';

import React from 'react';
import { Box } from '@mui/material';
import { useSearchParams } from 'next/navigation';
import EditPayroll from '@/components/DSR/Payrolls/EditPayroll';
import ActivityLogs from '@/components/DSR/Payrolls/Activitylog';
import RequestPayroll from '@/components/DSR/Payrolls/RequestPayroll';
import RequestPayrollTab from '@/components/DSR/Payrolls/RequestPayrollTab';
import '../dsr.scss';

export default function ExpenseDetails({ params }) {
  const searchParams = useSearchParams();
  const IsEdit = searchParams.get('isEdit');
  const isRequest = searchParams.get('isRequest');
  const isRequestTab = searchParams.get('isRequests');
  const isActivity = searchParams.get('isActivity');
  const dsrId = params?.id;

  return (
    <>
      <Box>
        <Box className={isActivity ? '' : 'DSR-details-section'}>
          {IsEdit ? (
            <EditPayroll dsrId={dsrId} />
          ) : isActivity ? (
            <ActivityLogs dsrId={dsrId} />
          ) : isRequestTab ? (
            <RequestPayrollTab dsrId={dsrId} />
          ) : isRequest ? (
            <RequestPayroll dsrId={dsrId} />
          ) : (
            <></>
          )}
        </Box>
      </Box>
    </>
  );
}
