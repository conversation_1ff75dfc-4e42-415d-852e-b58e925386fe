'use client';

import React, { Fragment, useContext, useState } from 'react';
import { Badge } from '@mui/material';
import UserAvatar from '../../Avatar/UserAvatar';
import SwapHorizIcon from '@mui/icons-material/SwapHoriz';
import SupervisorAccountOutlinedIcon from '@mui/icons-material/SupervisorAccountOutlined';
import moment from 'moment';
import CustomButton from '../../CustomButton';
import AuthContext from '@/helper/authcontext';
import './shiftSwapActions.scss';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstanceOrg from '@/helper/axios/axiosInstanceOrg';
import { ROTA_URLS } from '@/helper/constants/urls';
import PreLoader from '../../Loader';

export const ShiftSwapRequest = ({
  selectedShiftData,
  viewAccessOnly,
  getRotaShiftList,
  handleAddShiftTooltipClose,
}) => {
  const { authState } = useContext(AuthContext);
  const [isLoading, setIsLoading] = useState(false);

  const dateFormat = (date) => {
    return moment(date).startOf().fromNow();
    // moment(date).calendar(null, {
    //   sameDay: '[Today at] HH:mm',
    //   nextDay: '[Tomorrow at] HH:mm',
    //   nextWeek: 'dddd [at] HH:mm',
    //   lastDay: '[Yesterday at] HH:mm',
    //   lastWeek: '[Last] dddd [at] HH:mm',
    //   sameElse: 'DD/MM/YYYY HH:mm',
    // });
  };

  const handleShiftSwapAction = async (swapId, isStatus) => {
    const sendData = {
      id: swapId,
      status: isStatus,
    };

    setIsLoading(true);
    try {
      const { status, data } = await axiosInstanceOrg.post(
        ROTA_URLS?.SWAP_ACTION_URL,
        sendData
      );
      if (status === 200 || status === 201) {
        setApiMessage('success', data?.message);
        setIsLoading(false);
        getRotaShiftList();
        handleAddShiftTooltipClose();
        setIsLoading(false);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setIsLoading(false);
    }
  };

  return (
    <>
      {isLoading && <PreLoader />}
      {selectedShiftData?.swapRequests?.map((item, index) => {
        const isRequested = authState?.id === item?.toUserId;

        const isPending =
          item?.status === 'active' && item?.adminStatus === 'pending';
        const isActive =
          item?.status === 'active' && item?.adminStatus === 'active';
        const isUserApprove = item?.status === 'active';

        return (
          <Fragment key={index}>
            {isRequested ? (
              <>
                <div className="swap-request-details-wrapper" key={index}>
                  <div className="swap-request-details">
                    <div className="swap-request-user-details">
                      <div>
                        <UserAvatar
                          name={selectedShiftData?.user?.user_full_name}
                          src={selectedShiftData?.user?.user_avatar_link}
                          classname="swap-request-avatar"
                        />
                      </div>
                      <div className="swap-request-name-time">
                        <p className="swap-request-name">
                          Swap request{' '}
                          <span className="swap-request-status">from</span>{' '}
                          {item?.from?.user_full_name}
                        </p>
                        <p className="swap-request-time">
                          {dateFormat(item?.createdAt)}
                        </p>
                      </div>
                    </div>
                    <div className="swap-request-shift-details">
                      {isPending ? (
                        <p className="swap-request-shift-text">
                          You've agreed to cover this shift,{' '}
                          <b>pending admin approval.</b>
                        </p>
                      ) : (
                        <p className="swap-request-shift-text">
                          {item?.from?.user_first_name} has asked you to cover
                          their shift.
                        </p>
                      )}
                    </div>
                  </div>
                </div>
                <div className="swap-request-button">
                  {item?.status === 'pending' && (
                    <>
                      <CustomButton
                        variant="outlined"
                        className="swap-request-btn cancel-cover-request-btn"
                        title="Reject"
                        onClick={() =>
                          handleShiftSwapAction(item?.id, 'rejected')
                        }
                      />

                      <CustomButton
                        className="swap-request-btn accept-cover-request-btn"
                        title="Accept"
                        onClick={() =>
                          handleShiftSwapAction(item?.id, 'active')
                        }
                      />
                    </>
                  )}
                </div>
              </>
            ) : viewAccessOnly ? (
              <>
                <div className="swap-request-details-wrapper" key={index}>
                  <div className="swap-request-details">
                    <div className="swap-request-user-details">
                      <div>
                        <Badge
                          overlap="circular"
                          anchorOrigin={{
                            vertical: 'bottom',
                            horizontal: 'right',
                          }}
                          badgeContent={
                            <SupervisorAccountOutlinedIcon className="swap-request-badge-icon" />
                          }
                        >
                          <UserAvatar
                            name={selectedShiftData?.user?.user_full_name}
                            src={selectedShiftData?.user?.user_avatar_link}
                            classname="swap-request-avatar"
                          />
                        </Badge>
                      </div>
                      <div className="swap-request-name-time">
                        <p className="swap-request-name">Shift cover request</p>
                        <p className="swap-request-time">
                          {dateFormat(item?.createdAt)}
                        </p>
                      </div>
                    </div>
                    <div className="swap-request-shift-details">
                      <p className="swap-request-shift-text">
                        Shift will be covered by:
                      </p>
                    </div>
                    <div className="swap-request-user-details swap-request-to-user-details">
                      <div>
                        <UserAvatar
                          name={item?.to?.user_full_name}
                          src={item?.to?.user_avatar_link}
                          classname="swap-request-avatar"
                        />
                      </div>
                      <div className="swap-request-name-time">
                        <p className="swap-request-name">
                          {item?.to?.user_full_name}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="swap-request-button">
                  <CustomButton
                    variant="outlined"
                    className="swap-request-btn cancel-cover-request-btn"
                    title="Deny"
                    onClick={() => handleShiftSwapAction(item?.id, 'rejected')}
                  />

                  <CustomButton
                    className="swap-request-btn accept-cover-request-btn"
                    title="Approve"
                    onClick={() => handleShiftSwapAction(item?.id, 'active')}
                  />
                </div>
              </>
            ) : (
              <>
                <div className="swap-request-details-wrapper" key={index}>
                  <div className="swap-request-details">
                    <div className="swap-request-user-details">
                      <div>
                        <Badge
                          overlap="circular"
                          anchorOrigin={{
                            vertical: 'bottom',
                            horizontal: 'right',
                          }}
                          badgeContent={
                            <SwapHorizIcon className="swap-request-badge-icon" />
                          }
                        >
                          <UserAvatar
                            name={selectedShiftData?.user?.user_full_name}
                            src={selectedShiftData?.user?.user_avatar_link}
                            classname="swap-request-avatar"
                          />
                        </Badge>
                      </div>
                      <div className="swap-request-name-time">
                        <p className="swap-request-name">
                          Cover request{' '}
                          <span className="swap-request-status">
                            {isActive ? 'active' : 'pending'}
                          </span>
                        </p>
                        <p className="swap-request-time">
                          {dateFormat(item?.createdAt)}
                        </p>
                      </div>
                    </div>
                    <div className="swap-request-shift-details">
                      {isUserApprove ? (
                        <p className="swap-request-shift-text">
                          {item?.to?.user_full_name} has agreed to cover this
                          shift for you, <b>pending admin approval.</b>
                        </p>
                      ) : (
                        <p className="swap-request-shift-text">
                          You've asked {item?.to?.user_full_name} to cover this
                          shift for you
                        </p>
                      )}
                    </div>
                  </div>
                </div>
                <div className="swap-request-button">
                  <CustomButton
                    className="swap-request-btn cancel-cover-request-btn"
                    title="Cancel shift cover request"
                    onClick={() => handleShiftSwapAction(item?.id, 'deleted')}
                  />
                </div>
              </>
            )}
          </Fragment>
        );
      })}
    </>
  );
};
