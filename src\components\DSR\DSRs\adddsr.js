'use client';

import React, { useEffect, useState, useContext } from 'react';
import { Box, Typography } from '@mui/material';
import CustomButton from '@/components/UI/CustomButton';
import CustomSelect from '@/components/UI/CustomSelect';
import CustomDatePicker from '@/components/UI/CustomDatePicker';
import {
  setApiMessage,
  TotalOfDSRArray,
  removeVAT,
} from '@/helper/common/commonFunctions';
import DSRCategory from '@/components/DSR/DSRs/dsrCategory';
import axiosInstance from '@/helper/axios/axiosInstance';
import { useRouter } from 'next/navigation';
import { URLS } from '@/helper/constants/urls';
import AuthContext from '@/helper/authcontext';
import dayjs from 'dayjs';
import moment from 'moment';
import _ from 'lodash';
import NoDataView from '@/components/UI/NoDataView';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import { fetchFromStorage, saveToStorage } from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import '../dsr.scss';

export default function AddDSRPage() {
  const { authState, AllListsData, userdata, setUserdata } =
    useContext(AuthContext);
  const router = useRouter();
  const [DsrData, setDsrData] = useState([]);
  const [DsrDataIncome, setDsrDataIncome] = useState([]);
  const [DsrDataOther, setDsrDataOther] = useState([]);
  const [VatValue, setVATValue] = useState([0, 20]);
  const [VAT, setVAT] = useState({
    NoneVat: '',
    VAT1: '',
    VAT2: '',
    AmountVAT1: '',
    AmountVAT2: '',
    diff1: '',
    diff2: '',
    TotalIncome: '',
  });
  const [loader, setLoader] = useState(false);
  const [checkloader, setCheckLoader] = useState(false);
  const [branch, setBranch] = useState();
  const [sdate, setDate] = useState();
  const [random, setRandom] = useState();
  const [randomother, setRandomother] = useState();
  const [randomvat, setRandomvat] = useState();
  const [DsrExist, setISDsr] = useState(false);
  const [DsrExistBM, setISDsrBM] = useState(true);

  const [currency, setCurrency] = useState();
  const [isSubmit, setIsSubmit] = useState(false);
  const [VatIsView, setVatIsView] = useState(true);
  const [IsRemarkView, setIsRemarkView] = useState(true);
  // const vatper = VatValue?.[1] ? VatValue?.[1] / 100 : 0.2;

  // get DSR Data
  const getDSRData = async (branchID) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_DSR_DATA + `${branchID}?payment_type=dsr`
      );
      if (status === 200) {
        setLoader(false);

        const incomeData =
          data?.data &&
          data?.data?.length > 0 &&
          data?.data?.filter((f) => f?.payment_type_usage === 'income');
        incomeData && incomeData?.length > 0
          ? setDsrDataIncome(incomeData)
          : setDsrDataIncome([]);
        const otherData =
          data?.data &&
          data?.data?.length > 0 &&
          data?.data?.filter((f) => f?.payment_type_usage === 'other');
        otherData && otherData?.length > 0
          ? setDsrDataOther(otherData)
          : setDsrDataOther([]);
        data?.vat_per_data &&
          data?.vat_per_data?.length > 0 &&
          setVATValue(data?.vat_per_data);
        setDsrData(data?.data);
      }
    } catch (error) {
      setLoader(false);
      setDsrData([]);
      setDsrDataOther([]);
      setDsrDataIncome([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const DSRCheck = async (branch, date) => {
    setCheckLoader(true);
    const sendData = {
      branch_id: branch,
      dsr_date: date
        ? dayjs(date).format('YYYY-MM-DD')
        : moment(Date()).format('YYYY-MM-DD'),
    };
    try {
      const { status, data } = await axiosInstance.post(
        URLS?.GET_DSR_CHECK,
        sendData
      );
      if (status === 200 || status === 202) {
        if (data?.status) {
          setCheckLoader(false);
          setISDsr(false);
          getDSRData(branch);
          // setApiMessage('success', data?.message);
        } else {
          setCheckLoader(false);
          setISDsr(true);
          setApiMessage('error', data?.message);
          setDsrData([]);
          setDsrDataIncome([]);
          setDsrDataOther([]);
        }
      }
    } catch (error) {
      setCheckLoader(false);
      setISDsr(true);
      setDsrData([]);
      setDsrDataIncome([]);
      setDsrDataOther([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const DSRCheckForBM = async (branch, date) => {
    const sendData = {
      branch_id: branch,
      dsr_date: date
        ? dayjs(date).format('YYYY-MM-DD')
        : moment(Date()).format('YYYY-MM-DD'),
    };
    try {
      const { status, data } = await axiosInstance.post(
        URLS?.GET_DSR_CHECK,
        sendData
      );
      if (status === 200 || status === 202) {
        if (data?.status) {
          setISDsrBM(false);
        } else {
          setISDsrBM(true);
        }
      }
    } catch (error) {
      setISDsrBM(true);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  //Total without VAT
  const TotalWithoutVAT = () => {
    return ValueOfTotal(
      parseFloat(
        removeVAT(
          ValueOfTotal(parseFloat(VAT?.VAT2)),
          VatValue && VatValue?.[1] ? VatValue?.[1] : 0
        )?.netAmount +
          ValueOfTotal(parseFloat(VAT?.VAT1)) +
          ValueOfTotal(parseFloat(VAT?.NoneVat))
      )
    ).toFixed(2);
  };
  const AddDSR = async () => {
    let dsrmerge = _.concat(DsrDataIncome, DsrDataOther);
    let vatCal = VAT;
    vatCal.TotalIncome = TotalOfDSRArray(DsrDataIncome)
      ? TotalOfDSRArray(DsrDataIncome)
      : 0;
    vatCal.AmountVAT1 =
      ValueOfTotal(parseFloat(VAT?.VAT2)) && VatValue && VatValue?.[1]
        ? removeVAT(ValueOfTotal(parseFloat(VAT?.VAT2)), VatValue?.[1])
            ?.vatAmount
        : 0;
    vatCal.AmountVAT2 =
      (ValueOfTotal(parseFloat(VAT?.VAT2)) ||
        ValueOfTotal(parseFloat(VAT?.VAT2)) === 0 ||
        ValueOfTotal(parseFloat(VAT?.VAT1)) ||
        ValueOfTotal(parseFloat(VAT?.VAT1)) === 0 ||
        ValueOfTotal(parseFloat(VAT?.NoneVat)) ||
        ValueOfTotal(parseFloat(VAT?.NoneVat)) === 0) &&
      VatValue &&
      VatValue?.[1]
        ? TotalWithoutVAT()
        : 0;
    vatCal.diff2 =
      VAT?.diff1 && VAT?.diff1 !== ''
        ? Math.abs(
            (
              removeVAT(ValueOfTotal(parseFloat(VAT?.VAT2)), VatValue?.[1])
                ?.vatAmount - ValueOfTotal(parseFloat(VAT?.diff1))
            ).toFixed(2)
          )
        : '';
    const sendData = {
      branch_id:
        authState?.web_user_active_role_id === 7 ||
        authState?.web_user_active_role_id === 14
          ? authState?.branch?.id
          : branch,
      dsr_date:
        authState?.web_user_active_role_id === 7 ||
        authState?.web_user_active_role_id === 14
          ? !DsrExistBM
            ? calculateDisplayDate()
            : moment(Date()).format('YYYY-MM-DD')
          : sdate
            ? dayjs(sdate).format('YYYY-MM-DD')
            : moment(Date()).format('YYYY-MM-DD'),
      data: dsrmerge,
      dsr_amount_total: vatCal,
      current_datetime: moment(Date()).format('YYYY-MM-DD HH:mm:ss'),
    };
    try {
      const { status, data } = await axiosInstance.post(
        URLS?.ADD_DSR,
        sendData
      );

      if (status === 200) {
        if (data?.status) {
          setLoader(false);
          setApiMessage('success', data?.message);
          router?.push('/dsr');
        } else {
          setLoader(false);
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const ValueOfTotal = (value) => {
    const parsedValue = parseFloat(value);
    if (!Number.isNaN(parsedValue)) {
      return parsedValue;
    }
    // if (parseFloat(value) && parseFloat(value) !== NaN) {
    //   return parseFloat(value);
    // }
    return 0;
  };
  //Total of VAT amount
  const totalofALLVat = () => {
    const sum =
      ValueOfTotal(parseFloat(VAT?.NoneVat)) +
      ValueOfTotal(parseFloat(VAT?.VAT1)) +
      ValueOfTotal(parseFloat(VAT?.VAT2));
    return parseFloat(sum.toFixed(2));
  };
  //Total of Difference VAT amount
  //   const totalofDiffVat = () => {
  //     const sum =
  //       ValueOfTotal(parseFloat(VAT?.diff1)) +
  //       ValueOfTotal(parseFloat(VAT?.diff2));
  //     return parseFloat(sum.toFixed(2));
  //   };
  useEffect(() => {
    setDsrDataIncome(DsrDataIncome);
  }, [random]);
  useEffect(() => {
    setDsrDataOther(DsrDataOther);
  }, [randomother]);
  useEffect(() => {
    setVAT(VAT);
  }, [randomvat]);
  useEffect(() => {
    authState?.currency_details && setCurrency(authState?.currency_details);
  }, [authState]);

  const calculateDisplayDate = (isUpdate) => {
    const now = new Date();

    const FiveThirtyPM = new Date();
    FiveThirtyPM.setHours(17, 30, 0, 0);
    // FiveThirtyPM.setHours(8, 0, 0, 0);
    if (now < FiveThirtyPM) {
      now.setDate(now.getDate() - 1);
    }

    return isUpdate
      ? moment(now).format('DD-MM-YYYY')
      : moment(now).format('YYYY-MM-DD');
  };
  useEffect(() => {
    if (
      authState?.web_user_active_role_id === 7 ||
      authState?.web_user_active_role_id === 14
    ) {
      getDSRData(authState?.branch?.id);
      DSRCheckForBM(authState?.branch?.id, calculateDisplayDate());
    }
  }, [authState?.web_user_active_role_id]);

  const oldDSR = fetchFromStorage(identifiers?.RedirectData)
    ? fetchFromStorage(identifiers?.RedirectData)
    : userdata;

  useEffect(() => {
    if (oldDSR?.IsFromUser === undefined && oldDSR?.page !== undefined) {
      setUserdata({ ...userdata, IsFromUser: true });
      saveToStorage(identifiers?.RedirectData, {
        ...oldDSR,
        IsFromUser: true,
      });
    }
  }, [oldDSR]);
  return (
    <Box>
      <Box className="dsr-page-section Add-dsr-section">
        <ArrowBackIosIcon
          className="mb8 cursor-pointer"
          onClick={() => {
            setTimeout(() => {
              router?.push('/dsr');
            }, 1000);
          }}
        />{' '}
        {authState?.web_user_active_role_id === 7 ||
        authState?.web_user_active_role_id === 14 ? (
          <>
            {authState?.branch && authState?.branch?.branch_name && (
              <Box className="d-flex align-center">
                <Typography className="title-text fw400 user-date">
                  <span className="fw700">Branch : </span>
                  <span>{authState?.branch?.branch_name}</span>
                </Typography>
              </Box>
            )}

            <Box className="d-flex align-center">
              <Typography className="title-text fw400 user-date">
                <span className="fw700">Date : </span>
                <span>
                  {!DsrExistBM
                    ? calculateDisplayDate(true)
                    : moment(Date()).format('DD-MM-YYYY')}
                </span>
              </Typography>
            </Box>
          </>
        ) : (
          <>
            <Box className="display-grid-branch">
              <Box>
                <CustomSelect
                  placeholder="Select Branch"
                  showDot
                  options={AllListsData?.ActiveBranchList}
                  value={
                    AllListsData?.ActiveBranchList?.find((opt) => {
                      return opt?.value === branch;
                    }) || ''
                  }
                  name="branch"
                  disabled={checkloader}
                  onChange={(e) => {
                    setBranch(e?.value);
                    if (e?.value) {
                      DSRCheck(e?.value, sdate);
                    } else {
                      setDsrData([]);
                      setDsrDataIncome([]);
                      setDsrDataOther([]);
                    }
                  }}
                  label={<span>Branch</span>}
                />
              </Box>
              <Box>
                <CustomDatePicker
                  label={<span>Date(DD/MM/YYYY)</span>}
                  name="date"
                  value={dayjs(sdate)}
                  disabled={checkloader}
                  disableFuture={true}
                  format="DD/MM/YYYY"
                  onChange={(date) => {
                    setDate(date);
                    branch && DSRCheck(branch, date);
                  }}
                  inputVariant="outlined"
                />
              </Box>
            </Box>
          </>
        )}
        {(!DsrData || DsrData?.length === 0) &&
          !DsrExist &&
          !loader &&
          (branch ||
            authState?.web_user_active_role_id === 7 ||
            authState?.web_user_active_role_id === 14) && (
            <Box className="no-data d-flex align-center justify-center">
              <NoDataView title="Not any categories created/selected in this branch." />
            </Box>
          )}
        <DSRCategory
          DsrDataIncome={DsrDataIncome}
          setVatIsView={setVatIsView}
          VatIsView={VatIsView}
          setIsRemarkView={setIsRemarkView}
          IsRemarkView={IsRemarkView}
          setIsSubmit={setIsSubmit}
          setDsrDataIncome={setDsrDataIncome}
          setRandom={setRandom}
          setDsrDataOther={setDsrDataOther}
          setRandomother={setRandomother}
          DsrDataOther={DsrDataOther}
          currency={currency}
          VAT={VAT}
          setVAT={setVAT}
          setRandomvat={setRandomvat}
          VatValue={VatValue}
          ValueOfTotal={ValueOfTotal}
          totalofALLVat={totalofALLVat}
          isSubmit={isSubmit}
          isEdit={false}
        />
        <Box className="mt24">
          {' '}
          <CustomButton
            fullWidth={false}
            variant="contained"
            disabled={
              loader ||
              DsrExist ||
              checkloader ||
              !DsrData ||
              DsrData?.length === 0 ||
              (!branch &&
                authState?.web_user_active_role_id !== 7 &&
                authState?.web_user_active_role_id !== 14)
            }
            onClick={() => {
              setIsSubmit(true);
              if (VatIsView && DsrDataIncome?.length > 0) {
                totalofALLVat() === TotalOfDSRArray(DsrDataIncome) &&
                  (!isNaN(parseFloat(VAT?.VAT1)) ||
                    !isNaN(parseFloat(VAT?.VAT2)) ||
                    !isNaN(parseFloat(VAT?.NoneVat)) ||
                    TotalOfDSRArray(DsrDataIncome) === 0) &&
                  AddDSR();
              } else {
                AddDSR();
              }
            }}
            title={`${loader ? 'Adding...' : 'Add DSR'}`}
          />
        </Box>
      </Box>
    </Box>
  );
}
