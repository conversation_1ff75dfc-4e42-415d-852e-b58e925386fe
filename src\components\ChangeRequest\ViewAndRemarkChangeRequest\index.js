'use client';

import React from 'react';
import { Box } from '@mui/material';
import { useSearchParams } from 'next/navigation';
import CRRemark from '@/components/ChangeRequest/CRRemark';
import CRView from '@/components/ChangeRequest/CRView';
import '../../../app/(auth)/(users)/(change-req)/change-request/changereq.scss';

export default function CRDetails({ params }) {
  const searchParams = useSearchParams();
  const IsView = searchParams.get('isView');
  const isRequest = searchParams.get('isRemark');
  const crId = params?.id;
  return (
    <>
      <Box className="main-page-container">
        <Box className={'cr-details-section'}>
          {isRequest ? (
            <CRRemark crId={crId} />
          ) : IsView ? (
            <CRView crId={crId} />
          ) : (
            <></>
          )}
        </Box>
      </Box>
    </>
  );
}
