@import '@/styles/variable.scss';
.organization-profile-wrap {
  .left-profile-header {
    background-color: var(--color-white);
    padding-bottom: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    width: 100%;
    max-width: 250px;
    box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.05);
    padding: var(--spacing-lg);
    min-height: calc(100vh - 90px - var(--banner-height));
    @media (max-width: 991px) {
      width: 100%;
      max-width: 100%;
      min-height: auto !important;
    }
  }

  .profile-card {
    background: var(--color-white);
    border-radius: var(--border-radius-xl);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: visible;

    .profile-header {
      display: flex;
      align-items: center;
      gap: 20px;
      padding-bottom: var(--spacing-lg);
      border-bottom: var(--border-width-xs) solid
        var(--color-light-grayish-blue);
      .profile-avatar-section {
        position: relative;
        .profile-avatar {
          width: 80px;
          height: 80px;
          border: var(--border-width-md) var(--border-style-solid)
            var(--border-color-white);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

          &.MuiAvatar-root {
            background-color: $color-secondary;
            color: $color-Dark-30;
          }
        }
      }

      .profile-info {
        .profile-name {
          font-family: var(--font-family-primary);
          color: var(--text-bright-blue);
          font-weight: var(--font-weight-semibold);
          margin-bottom: var(--spacing-sm);
        }

        .profile-subtitle {
          font-family: var(--font-family-primary);
          color: var(--color-light-dark);
        }
      }
    }
    .org-form-title {
      font-family: $PrimaryFont;
      color: $color-Black;
      font-size: 16px;
      font-weight: 600;
      margin-top: 20px;
    }
    .form-actions-btn {
      display: flex;
      justify-content: flex-end;
      gap: 20px;
      margin-top: 20px;
      padding-top: 20px;
      padding-bottom: 20px;
      border-top: 1px solid $color-Dark-10;
    }
  }

  .organization-sec-wrap {
    width: 100%;
    padding: 0px 20px;
    background-color: $color-White;
    border-radius: 12px;
    box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.05);
    height: 100%;
    overflow: hidden;
    .tabs-wrap {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #ededed;
      gap: var(--spacing-sm);
      flex-wrap: wrap;
      .report-tabs {
        display: flex;
        align-items: center;
        justify-content: space-between;
        // border-bottom: 1px solid red;
        overflow: auto;

        .tab-list-sec {
          .tab-name {
            text-transform: none;
            font-family: $PrimaryFont;
            font-size: 16px;
            line-height: 20px;
            font-weight: 600;
            color: #000000;
            opacity: 1;
            width: max-content;
            padding: 5px;
            margin-right: var(--spacing-lg);

            @media (max-width: 1200px) {
              width: max-content;
              // min-width: 50%;
            }
          }

          .MuiTabs-flexContainer {
            align-items: center;
            // justify-content: space-between;
          }

          .MuiTabs-indicator {
            background-color: var(--color-primary);
            height: 3px;
          }

          .Mui-selected {
            color: var(--text-bright-blue);
          }

          .MuiTabs-scroller {
            display: block !important;
            max-height: 48px;
          }

          .MuiTabScrollButton-root {
            display: block !important;
            max-height: 48px;
            .MuiSvgIcon-root {
              margin: 14px;
            }
          }
        }
      }
    }
    @media (max-width: 991px) {
      width: 100%;
    }
  }

  @media (max-width: 991px) {
    flex-wrap: wrap;
  }
}

// Responsive styles
// @media (max-width: 1024px) {
//   .profile-form-container {
//     .profile-card {
//       .form-grid {
//         grid-template-columns: calc(50% - 9px) calc(50% - 9px);
//       }
//     }
//   }
// }
// @media (max-width: 768px) {
//   .profile-form-container {
//     margin: 1rem;
//     padding: 0;

//     .profile-card {
//       .profile-header {
//         flex-direction: column;
//         text-align: center;
//         gap: 1rem;
//       }
//       // .form-actions {
//       //   flex-direction: column-reverse;
//       //   .custom-button-wrapper {
//       //     button {
//       //       width: 100%;
//       //     }
//       //   }
//       // }
//     }
//   }
// }
