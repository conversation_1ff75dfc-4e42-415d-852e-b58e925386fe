@import '@/styles/variable.scss';

.add-new-card-wrap {
  margin-top: var(--spacing-md);
  box-shadow: var(--box-shadow-xs);
  border: var(--normal-sec-border);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-base);
  margin-bottom: var(--spacing-xl);
  .input-field {
    .MuiInputBase-root {
      min-height: 40px;
      .MuiInputBase-input {
        padding: 7px 14px !important;
      }
    }
  }
}
.add-new-wrap {
  .card-inputs {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-base);
    margin-top: var(--spacing-md);
  }
  .card-details-row {
    display: flex;
    gap: var(--spacing-base);
    @media (max-width: 559px) {
      flex-direction: column;
    }
    .cardholder-field-container {
      flex: 1;
    }
    .cardnumber-field-container {
      flex: 1;
    }
    .card-expiry-container {
      flex: 1;
    }
    .card-cvc-container {
      flex: 1;
    }
  }
  .add-edit-card {
    margin-top: var(--spacing-md);
  }
}
