'use client';

import React, { useEffect, useState } from 'react';
import {
  Box,
  Typography,
  FormGroup,
  FormControlLabel,
  Checkbox,
} from '@mui/material';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CustomButton from '@/components/UI/CustomButton';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import LaunchIcon from '@mui/icons-material/Launch';
import HeaderImage from '@/components/UI/ImageSecurity';
import '../onboarding.scss';

export default function EmploymentContract({
  isUserScreen,
  isMyProfile,
  UserId,
  formEmpContract,
  getOnboardingChecklist,
  ViewAccessOnly,
  // UserDetails,
  // setLoaderMain,
  // IsMainLoader,
}) {
  const [loader, setLoader] = useState(false);
  const [checked, setChecked] = useState(false);
  const OnboardingForms = async (isUpdate) => {
    setLoader(true);
    let sendData = {
      is_confirm_sign: checked,
    };
    const method = isMyProfile ? 'post' : 'put';
    const ApiUrl = isMyProfile
      ? URLS.CREATE_ONBOARDING_FORM + `?checklist_id=${4}`
      : URLS.UPDATE_ONBOARDING_FORM +
        `?form_user_id=${UserId}&checklist_id=${4}`;
    try {
      const { status, data } = await axiosInstance[method](ApiUrl, sendData);

      if (status === 200) {
        if (data?.status) {
          setLoader(false);
          if (isUpdate !== 'first') {
            setApiMessage('success', data?.message);
            getOnboardingChecklist(UserId);
          }
        } else {
          setLoader(false);
          setApiMessage('error', data?.message);
        }
        setLoader(false);
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const download = async () => {
    const response = await fetch(formEmpContract?.employment_contract);
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'EMPLOYMENT_CONTRACT.PDF');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };
  // const regenerateEmp = async () => {
  //   IsMainLoader && setLoaderMain(true);
  //   try {
  //     setLoader(true);
  //     const { status, data } = await axiosInstance.get(
  //       URLS.REGENERATE_EMP_CONTRACT +
  //         `${UserDetails?.id ? UserDetails?.id : UserId}`
  //     );

  //     if (status === 200) {
  //       setLoader(false);
  //       getOnboardingChecklist(UserId);
  //     }
  //   } catch (error) {
  //     setApiMessage('error', error?.response?.data?.message);
  //     setLoader(false);
  //   }
  // };

  useEffect(() => {
    if (
      formEmpContract?.is_confirm_sign === false ||
      formEmpContract?.is_confirm_sign === true
    ) {
      setChecked(formEmpContract?.is_confirm_sign);
    }
  }, [formEmpContract?.is_confirm_sign]);

  useEffect(() => {
    if (
      typeof formEmpContract === 'object' &&
      Object.keys(formEmpContract).length === 0 &&
      isMyProfile !== undefined
    ) {
      OnboardingForms('first');
    }
  }, [formEmpContract]);

  return (
    <>
      <Box className={!isUserScreen && 'page-container'}>
        <Box
          className={
            isUserScreen
              ? 'right-to-work-page'
              : 'page-section right-to-work-page'
          }
        >
          <Typography className="body-text pb24">
            Please upload your All Document as per given Instruction & Ahead
            step 2 for on boarding process from the company.
          </Typography>

          <Box className="employee-contract-checklist rtwc-upload-grid ">
            <Box className="upload-download-sec rtwc-upload h100">
              <Box className="title-section">
                <Box className="title">
                  <Typography className="body-text fw600">
                    Read Agreement
                  </Typography>
                </Box>
                <CustomButton
                  variant="contained"
                  className="green-button"
                  title={'Download'}
                  fullWidth={false}
                  onClick={() =>
                    formEmpContract?.employment_contract && download()
                  }
                  disabled={ViewAccessOnly}
                />
              </Box>

              <Box className="action-sec">
                <>
                  <HeaderImage
                    type="url"
                    imageUrl={formEmpContract?.employment_contract}
                    pagetitle="EMPLOYMENT_CONTRACT.PDF"
                    Content={
                      <Typography className="body-text fw600 cursor-pointer color-green">
                        EMPLOYMENT CONTRACT
                      </Typography>
                    }
                  />{' '}
                  <HeaderImage
                    type="url"
                    imageUrl={formEmpContract?.employment_contract}
                    Content={<LaunchIcon className="view-icon" />}
                    pagetitle="EMPLOYMENT_CONTRACT.PDF"
                    className="d-flex align-center"
                  />
                </>
              </Box>
            </Box>
          </Box>
          {!formEmpContract?.is_confirm_sign && isMyProfile && (
            <FormGroup className={'form-checkbox pt16'}>
              <FormControlLabel
                control={
                  <Checkbox
                    className="check-box "
                    icon={<CheckBoxOutlineBlankIcon className="uncheck-icon" />}
                    checkedIcon={<CheckBoxIcon className="check-icon" />}
                  />
                }
                className="check-box-form form-row max-content title-text"
                name="confirmation"
                checked={checked}
                onChange={(e) => {
                  setChecked(e.target.checked);
                }}
                disabled={ViewAccessOnly || formEmpContract?.is_confirm_sign}
                label="I confirm that I have read the employment contract carefully and accept the terms by signing it."
              />
            </FormGroup>
          )}
          {!formEmpContract?.is_confirm_sign ? (
            <Box className={`pt32 ${!isUserScreen ? 'text-align' : ''}`}>
              <CustomButton
                variant="contained"
                className="red-button"
                title={`${loader ? 'Saving...' : 'Save'}`}
                onClick={OnboardingForms}
                fullWidth={false}
                disabled={ViewAccessOnly || loader || !checked}
              />
            </Box>
          ) : (
            <></>
          )}
        </Box>
      </Box>
    </>
  );
}
