import React from 'react';

const DragIcon = ({ onClick }) => {
  return (
    <div className="action-icon d-flex" onClick={onClick}>
      <Drag />
    </div>
  );
};

export default DragIcon;

const Drag = (props) => {
  return (
    <svg
      {...props}
      width="24px"
      height="24px"
      viewBox="0 -2 24 24"
      strokeWidth="0px"
    >
      <path
        d="M4 6H20M4 12H20M4 18H20"
        stroke="#000000"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
