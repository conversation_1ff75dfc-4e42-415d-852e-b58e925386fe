.rota-deleted-shifts-container {
  .rota-filter-heading {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    .rota-filter-title {
      font-family: var(--font-family-primary);
      color: var(--text-color-black);
      font-size: var(--font-size-lg);
      line-height: var(--line-height-lg);
      font-weight: var(--font-weight-semibold);
    }
  }
  .filter-date-picker-section {
    display: flex;
    justify-content: left;
    align-items: center;
    gap: var(--spacing-md);
    .rota-filter-to-text {
      font-family: var(--font-family-primary);
      color: var(--text-color-black);
      font-size: var(--font-size-sm);
      line-height: var(--line-height-sm);
    }
    .filter-button-section {
      display: flex;
      margin-left: auto;
      gap: var(--spacing-md);
    }
  }
  .rota-deleted-shifts-table {
    background-color: var(--color-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-xs);
    padding: var(--spacing-lg);
    margin-top: var(--spacing-sm);
    .table-container {
      margin: var(--spacing-none);
      padding: var(--spacing-none);
      background-color: none;
      box-shadow: none;
      border-radius: none;
      .table-action-restore-btn {
        color: var(--text-color-primary);
        cursor: pointer;
      }
    }
  }
}

.delete-filter-modal {
  .delete-filter-section {
    margin-bottom: var(--spacing-xxl);
    .search-box {
      display: flex;
      justify-content: left;
      align-items: center;
      gap: var(--spacing-md);
      margin-bottom: var(--spacing-md);
      .action-link {
        color: var(--color-primary);
        cursor: pointer;
        font-size: var(--font-size-sm);
        &:hover {
          text-decoration: underline;
        }
      }
    }
    .select-actions {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      margin-top: var(--spacing-xs);
    }
    .employees-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
      gap: var(--spacing-md);
      margin-top: var(--spacing-md);
      .employee-item {
        display: flex;
        align-items: center;
        .MuiFormControlLabel-root {
          margin-left: 0;
          margin-right: 0;
          span {
            font-family: var(--font-family-primary);
            font-size: var(--font-size-sm);
            line-height: var(--line-height-sm);
          }
        }
        .MuiCheckbox-root {
          padding: var(--spacing-xs);
        }
      }
    }
  }
}
