.drawer-sec {
  z-index: 1300 !important;
  .MuiPaper-root {
    width: 398px;
    min-width: 320px;
    max-width: 30%;
    padding: 12px 27px 15px;

    .cross-icon {
      background-color: var(--color-white) !important;

      .MuiIconButton-label {
        justify-content: flex-end;
      }
    }
    @media (max-width: 399px) {
      min-width: 100%;
    }
  }
  .drawer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }
}
.assign-employee-drawer {
  .MuiPaper-root {
    width: 785px;
    // min-width: 785px;
    max-width: 70%;
    padding: 12px 12px 22px;
    overflow: hidden;
    @media (max-width: 1024px) {
      max-width: 100%;
    }
    @media (max-width: 399px) {
      min-width: 100%;
    }
  }
}
// .Create-budget-drawer {
.MuiPopover-root {
  z-index: 99999 !important;
}
// }
