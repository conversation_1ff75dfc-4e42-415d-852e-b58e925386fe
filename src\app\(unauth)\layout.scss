.main-container {
  background-color: var(--color-secondary-auth) !important;
  overflow: auto;

  .page-conatiner {
    width: 100%;
    max-width: 1440px;
    margin: 0 auto;
    height: calc(100vh - var(--banner-height));
  }
  .un-auth-header-wrap {
    // margin: 0px 120px 62px 120px;
    width: 100%;
    max-width: 1024px;
    margin: 0 auto;
    margin-bottom: 32px;
    padding: var(--spacing-none) var(--spacing-base);
    .logo-wrap {
      line-height: 0px;
      .logo-text-wrap {
        color: var(--text-color-primary);
        font-size: 28px;
        line-height: 28px;
        font-weight: 600;
        font-family: var(--font-family-poly-median) !important;
        cursor: pointer;

        @media (max-width: 575px) {
          font-size: 20px;
        }
      }
    }
    .org-unauth-menu-icon {
      display: none;
      @media (max-width: 767px) {
        display: block;
      }
    }
    .un-auth-header-btn-wrap {
      .login-btn-wrap {
        gap: var(--spacing-2xl);

        .login-btn {
          background-color: transparent !important;
          font-size: 18px;
          line-height: 26px;
          font-weight: 400;
          color: #1d1e25 !important;
          border: none !important;
          font-family: var(--font-family-poly-slim) !important;

          @media (max-width: 375px) {
            font-size: 14px;
          }
        }
        @media (max-width: 771px) {
          gap: var(--font-size-lg);
        }
      }

      .free-demo-btn-wrap {
        margin-left: 52px;

        .free-demo-btn {
          font-size: 18px;
          line-height: 13px;
          font-weight: 300;
          padding: 16px 24px !important;
          color: var(--text-color-white) !important;
          background-color: var(--color-primary) !important;
          border-radius: 12px !important;
          border: none !important;
          box-shadow: 0px 0px 0px 1px var(--color-primary) !important;
          font-family: var(--font-family-poly-median) !important;

          &:hover {
            box-shadow: 0px 0px 0px 1px var(--color-primary) !important;
            background-color: var(--color-primary) !important;
            color: var(--text-color-white) !important;
            border: none !important;
          }

          @media (max-width: 575px) {
            font-size: 14px;
            padding: 10px 12px !important;
          }

          @media (max-width: 575px) {
            font-size: 14px;
            padding: 8px 8px !important;
          }
        }

        @media (max-width: 575px) {
          margin-left: 20px;
        }

        @media (max-width: 375px) {
          margin-left: 10px;
        }
      }
      @media (max-width: 767px) {
        display: none !important;
      }
    }
  }
  .hight-priority-notification-banner {
    margin-top: 16px;
  }
}

.unauth-menu-drawer {
  .MuiPaper-root {
    width: 300px !important;
    .drawer-header {
      display: flex;
      align-items: center;
    }
    .un-auth-header-menu {
      .login-btn {
        margin: var(--spacing-xsm) var(--spacing-none);
      }
    }
    .menu-free-demo-btn-wrap {
      margin-top: 20px;
      .free-demo-btn {
        font-size: var(--font-size-sm);
        line-height: 13px;
        font-weight: var(--font-weight-medium);
        padding: 10px 24px !important;
        color: var(--text-color-white) !important;
        background-color: var(--color-primary) !important;
        border-radius: 12px !important;
        border: none !important;
        box-shadow: 0px 0px 0px 1px var(--color-primary) !important;
        font-family: var(--font-family-poly-median) !important;

        &:hover {
          box-shadow: 0px 0px 0px 1px var(--color-primary) !important;
          background-color: var(--color-primary) !important;
          color: var(--text-color-white) !important;
          border: none !important;
        }
        @media (max-width: 575px) {
          font-size: 14px;
          padding: 8px 18px !important;
        }
      }
    }
  }
}

body {
  .heading-text {
    font-size: 38px !important;
    line-height: 48px !important;
    // font-weight: 600 !important;
    color: var(--text-color-primary);
    font-family: var(--font-family-poly-median) !important;

    @media (max-width: 575px) {
      font-size: 30px !important;
      line-height: 40px !important;
    }
  }

  .sub-heading-text {
    font-size: 18px !important;
    font-weight: 300 !important;
    line-height: 26px !important;
    color: var(--text-lavender-gray);
    font-family: var(--font-family-poly-slim) !important;
  }

  .un-auth-label-wrap {
    font-size: 14px !important;
    line-height: 20px !important;
    font-weight: 300 !important;
    color: var(--text-slate-gray) !important;
    font-family: var(--font-family-poly-slim) !important;
  }
}
