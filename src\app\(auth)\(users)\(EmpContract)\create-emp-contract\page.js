import { generateMetadata } from '@/helper/common/commonFunctions';
import { Box } from '@mui/material';
import AddEditEMPContract from '@/components/EMPContrat/AddEditContract';
import './createemp.scss';

export const metadata = generateMetadata({
  pageTitle: 'Contract Create',
});

export default function CreateEMPContract() {
  return (
    <>
      <Box className="main-page-container">
        <AddEditEMPContract isEdit={false} />
      </Box>
    </>
  );
}
