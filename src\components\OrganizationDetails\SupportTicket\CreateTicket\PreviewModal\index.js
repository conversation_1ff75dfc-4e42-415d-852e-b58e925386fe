'use client';
import React from 'react';
import { Dialog, DialogContent, DialogTitle, IconButton } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import Image from 'next/image';
import './previewmodal.scss';
export default function PreviewModal({ open, setOpen, previewMedia }) {
  return (
    <Dialog
      className="dialog-wrap"
      fullWidth
      maxWidth="sm"
      open={open}
      onClose={() => setOpen(false)}
    >
      <DialogTitle className="dialog-title-wrap">
        Media Preview
        <IconButton
          aria-label="close"
          onClick={() => setOpen(false)}
          className="close-icon"
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent className="dialog-content-wrap">
        {previewMedia && previewMedia?.type === 'video' ? (
          <video
            width="100%"
            className="video-preview-wrap"
            controls
            src={previewMedia?.preview}
          ></video>
        ) : (
          <Image
            className="image-preview"
            src={previewMedia}
            alt="Preview"
            width={545}
            height={300}
          />
        )}
      </DialogContent>
    </Dialog>
  );
}
