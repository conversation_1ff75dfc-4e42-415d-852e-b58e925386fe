@import '@/styles/variable.scss';

.assign-emp-section.assign-employee-section {
  height: calc(100% - 65px); //- 42px - 16px - 24px
  position: relative;
  padding-top: 40px;
  @media (max-width: 768px) {
    padding-top: 0;
  }

  .assign-emp {
    height: calc(100% - 46px);
    overflow: scroll;
    @media (max-width: 575px) {
      height: calc(100% - 46px) !important;
    }
    .assign-emp-grid {
      display: grid;
      grid-template-columns: calc(50% - 4px) calc(50% - 4px);
      gap: 0px;
      height: 100%;
      .left-grid {
        border-right: var(--normal-sec-border);
      }

      @media (max-width: 768px) {
        grid-template-columns: calc(100%);
        gap: 16px;
        .left-grid {
          border-right: 0;
        }
      }
    }
  }

  .assign-role-title {
    border-bottom: var(--normal-sec-border);
    border-top: var(--normal-sec-border);
    padding: 8px 0;
    position: absolute;
    top: 0;
    z-index: 9999;
    background-color: var(--color-white);
    width: 100%;
    @media (max-width: 768px) {
      position: inherit;
    }
  }
  .assign-role-title-grid {
    width: calc(50% - 4px);
    @media (max-width: 768px) {
      width: calc(100%);
    }
  }
  .left-grid {
    .assign-role-title-grid {
      width: calc(50% - 6px);
      border-right: var(--normal-sec-border);
      @media (max-width: 768px) {
        width: calc(100%);
      }
    }
  }
  .assign-user-details {
    margin-top: 8px;
  }
  .svg-icon {
    fill: var(--icon-color-primary);
  }
}
