import CustomCheckbox from '@/components/UI/CustomCheckbox';
import CustomDatePicker from '@/components/UI/CustomDatePicker';
import CustomSelect from '@/components/UI/CustomSelect';
import CustomTextField from '@/components/UI/CustomTextField';
import { Box, Tooltip, Typography } from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import dayjs from 'dayjs';
import React from 'react';

export default function BasicInfoSection({
  values,
  handleChange,
  handleBlur,
  touched,
  errors,
  setFieldValue,
  leaveTypeList,
  leavePolicySubmit,
  setLeavePolicySubmit,
  Inputype,
  isHour,
}) {
  return (
    <>
      <Box className="leave-accrual-fields pt16">
        <Box>
          <CustomTextField
            fullWidth
            id="policyName"
            name="policyName"
            label="Name"
            placeholder="Name"
            value={values?.policyName}
            onChange={(e) => {
              handleChange(e);
              setLeavePolicySubmit({
                ...leavePolicySubmit,
                leaveAccrual: false,
              });
            }}
            onBlur={handleBlur}
            required
            error={Boolean(touched?.policyName && errors?.policyName)}
            helperText={touched?.policyName && errors?.policyName}
          />
        </Box>
        <Box>
          <CustomSelect
            fullWidth
            id="leaveType"
            name="leaveType"
            label="Leave Type"
            placeholder="Leave Type"
            options={leaveTypeList}
            value={
              leaveTypeList?.find((opt) => {
                return opt?.value === Number(values.leaveType);
              }) || ''
            }
            onChange={(e) => {
              setFieldValue('leaveType', e?.value);
              setLeavePolicySubmit({
                ...leavePolicySubmit,
                leaveAccrual: false,
              });
            }}
            error={Boolean(touched?.leaveType && errors?.leaveType)}
            helperText={touched?.leaveType && errors?.leaveType}
            required
            isDisabled
          />
        </Box>
        <Box>
          <CustomDatePicker
            fullWidth
            label="Leave Calendar Start Year From"
            required
            format="M/YYYY"
            name="leaveCalendarStartYear"
            value={dayjs(values?.leaveCalendarStartYear)}
            onChange={(date) => {
              setFieldValue('leaveCalendarStartYear', date);
              setFieldValue('leaveCalendarEndYear', null);
              setLeavePolicySubmit({
                ...leavePolicySubmit,
                leaveAccrual: false,
              });
            }}
            onBlur={handleBlur}
            error={Boolean(
              touched?.leaveCalendarStartYear && errors?.leaveCalendarStartYear
            )}
            helperText={
              touched?.leaveCalendarStartYear && errors?.leaveCalendarStartYear
            }
            views={['month', 'year']}
          />
        </Box>
        <Box>
          <CustomDatePicker
            fullWidth
            label="Policy End Date"
            name="leaveCalendarEndYear"
            value={dayjs(values?.leaveCalendarEndYear)}
            onChange={(date) => {
              setFieldValue('leaveCalendarEndYear', date);
              setLeavePolicySubmit({
                ...leavePolicySubmit,
                leaveAccrual: false,
              });
            }}
            onBlur={handleBlur}
            error={Boolean(
              touched?.leaveCalendarEndYear && errors?.leaveCalendarEndYear
            )}
            helperText={
              touched?.leaveCalendarEndYear && errors?.leaveCalendarEndYear
            }
            minDate={
              values?.leaveCalendarStartYear
                ? dayjs(values?.leaveCalendarStartYear).startOf('month')
                : undefined
            }
          />
        </Box>
        <Box>
          <CustomTextField
            fullWidth
            id="description"
            name="description"
            label="Description"
            placeholder="Description"
            value={values?.description}
            onChange={(e) => {
              handleChange(e);
              setLeavePolicySubmit({
                ...leavePolicySubmit,
                leaveAccrual: false,
              });
            }}
            error={Boolean(touched.description && errors.description)}
          />
        </Box>
        <Box className="d-flex align-center gap-sm">
          <CustomCheckbox
            checked={values?.defaultLeavePolicy}
            onChange={(e) => {
              setFieldValue('defaultLeavePolicy', e?.target?.checked);
              setLeavePolicySubmit({
                ...leavePolicySubmit,
                leaveAccrual: false,
              });
            }}
            disableRipple
            label={<Typography>Default Leave Policy</Typography>}
          />
          <Tooltip
            arrow
            title={
              <Typography>
                If you select this check box leave policy will assigned to new
                employee.
              </Typography>
            }
            classes={{ tooltip: 'info-tooltip-container' }}
          >
            <InfoIcon className="info-icon cursor-pointer" />
          </Tooltip>
        </Box>
      </Box>
      <Box className="pt16 d-flex align-center gap-sm">
        <Typography className="title-text">Salary Deduction</Typography>
        <Box>
          <CustomTextField
            fullWidth
            id="salaryDed"
            name="salaryDed"
            placeholder="Days"
            value={values?.salaryDed}
            onChange={(e) => {
              handleChange(e);
              setLeavePolicySubmit({
                ...leavePolicySubmit,
                leaveAccrual: false,
              });
            }}
            onBlur={handleBlur}
            error={Boolean(touched?.salaryDed && errors?.salaryDed)}
            helperText={touched?.salaryDed && errors?.salaryDed}
            onInput={Inputype}
          />
        </Box>
        <Typography className="title-text">
          {isHour ? 'Hours' : 'Days'}
        </Typography>
      </Box>
    </>
  );
}
