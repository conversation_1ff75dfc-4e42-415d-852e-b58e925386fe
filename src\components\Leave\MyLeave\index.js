'use client';
import React, { useState } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Tooltip,
  Popover,
} from '@mui/material';
import { LeavePaid, LeaveUnPaid } from '@/helper/common/images';
import Slider from 'react-slick';
import InfoIcon from '@mui/icons-material/Info';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import SettingsIcon from '@mui/icons-material/Settings';
import { useRouter } from 'next/navigation';
import CustomSelect from '@/components/UI/CustomSelect';
import { generateYearsFromJoiningDate } from '@/helper/common/commonFunctions';
import NoDataView from '@/components/UI/NoDataView';
import './myleave.scss';
const MyLeave = ({
  userDetails,
  yearFilterdata,
  setYearFilterData,
  UserDetails,
}) => {
  const [anchorEl, setAnchorEl] = useState(null);

  let router = useRouter();
  const open = Boolean(anchorEl);

  const id = open ? 'simple-popper' : undefined;

  const handleClose = () => {
    setAnchorEl(null);
  };

  const settings = {
    dots: false,
    infinite: false,
    arrows: userDetails?.length > 2 ? true : false,
    // arrows: true,
    speed: 500,
    slidesToShow: 4,
    slidesToScroll: 1,
    centerMode: false,
    responsive: [
      {
        breakpoint: 1600,
        settings: {
          slidesToShow: 4,
        },
      },
      {
        breakpoint: 1480,
        settings: {
          slidesToShow: 3.6,
        },
      },
      {
        breakpoint: 1400,
        settings: {
          slidesToShow: 3.4,
        },
      },
      {
        breakpoint: 1200,
        settings: {
          slidesToShow: 2.85,
        },
      },
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 2.4,
        },
      },
      {
        breakpoint: 992,
        settings: {
          slidesToShow: 2.3,
        },
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 1.85,
        },
      },
      {
        breakpoint: 576,
        settings: {
          slidesToShow: 1.4,
        },
      },
      {
        breakpoint: 425,
        settings: {
          slidesToShow: 1,
        },
      },
      {
        breakpoint: 375,
        settings: {
          slidesToShow: 1,
        },
      },
      {
        breakpoint: 320,
        settings: {
          slidesToShow: 1,
        },
      },
    ],
  };

  return (
    <Box className="my-leave-card-wrap">
      <Box className="d-flex justify-end">
        <Box>
          <CustomSelect
            placeholder="Year"
            options={generateYearsFromJoiningDate(
              UserDetails?.user_joining_date
                ? UserDetails?.user_joining_date
                : UserDetails?.createdAt
                  ? UserDetails?.createdAt
                  : ''
            )}
            value={
              generateYearsFromJoiningDate(
                UserDetails?.user_joining_date
                  ? UserDetails?.user_joining_date
                  : UserDetails?.createdAt
                    ? UserDetails?.createdAt
                    : ''
              )?.find((opt) => {
                return opt?.value === yearFilterdata;
              }) || ''
            }
            onChange={(e) => {
              const selectedYear = e?.value;
              setYearFilterData(selectedYear); // Update state with selected year
            }}
          />
        </Box>
      </Box>
      {userDetails?.length > 0 ? (
        <Box className="my-leave-card">
          <Slider {...settings}>
            {userDetails?.map((leave) => {
              return (
                <Box>
                  <Card className="card-wrap">
                    <CardContent className="card-content-wrap">
                      <Box className="d-flex align-center justify-space-between leave-wrap">
                        <Box className="d-flex align-center">
                          <Box
                            className="leave-icon-wrap"
                            sx={{
                              backgroundColor: leave?.leave_type_color,
                            }}
                          ></Box>
                          <Box className="d-flex align-center">
                            <Box className="d-flex align-center">
                              <Tooltip
                                title={<Typography>{leave?.name}</Typography>}
                                arrow
                                classes={{ tooltip: 'info-tooltip-container' }}
                              >
                                <Typography className="title-text fw600 pl8 text-ellipsis-line">
                                  {leave?.name}
                                </Typography>
                              </Tooltip>
                              <Typography className="title-text fw600">
                                (
                                {leave?.has_leave_unlimited === true
                                  ? 'Unlimited'
                                  : leave?.leave_balance}
                                )
                              </Typography>
                            </Box>
                            <Tooltip
                              arrow
                              title={leave?.leave_deduction_type}
                              classes={{ tooltip: 'info-tooltip-container' }}
                            >
                              <span className="leave-type-wrap d-flex align-center cursor-pointer">
                                {leave?.leave_deduction_type === 'unpaid' ? (
                                  <LeaveUnPaid />
                                ) : (
                                  <LeavePaid />
                                )}
                              </span>
                            </Tooltip>
                          </Box>
                        </Box>
                        {/* <Box className="leave-type-wrap">
                    <MoreHorizIcon
                      className="cursor-pointer"
                      onClick={handleClick}
                    />
                  </Box> */}
                      </Box>
                      <Box className="d-flex align-center">
                        <Box className="balance-wrap text-align">
                          <Typography className="title-text">
                            Balance
                          </Typography>
                          <Box className="d-flex align-center gap-10 justify-center">
                            <Typography className="title-text text-wrap">
                              {leave?.has_leave_unlimited === true ? (
                                <span className="unlimited-text-wrap">
                                  Unlimited
                                </span>
                              ) : (
                                leave?.user_remaining_leave
                              )}
                            </Typography>
                            <Tooltip
                              arrow
                              title={
                                <Typography className="sub-title-text">
                                  The Planned leave balance will be debited once
                                  the specified date has passed.
                                </Typography>
                              }
                              //open
                              classes={{ tooltip: 'info-tooltip-container' }}
                            >
                              <InfoIcon className="info-icon-wrap cursor-pointer" />
                            </Tooltip>
                          </Box>
                        </Box>
                        <Box className="booked-wrap text-align">
                          <Typography className="title-text">Booked</Typography>
                          <Box className="d-flex align-center gap-10 justify-center">
                            <Typography className="title-text text-wrap">
                              {leave?.used_leave?.total}
                            </Typography>
                            <Tooltip
                              arrow
                              title={
                                <Box>
                                  <Typography className="sub-title-text">
                                    Used: {leave?.used_leave?.used_leaves}
                                  </Typography>
                                  <Typography className="sub-title-text">
                                    Planned: {leave?.used_leave?.planned_leaves}
                                  </Typography>
                                </Box>
                              }
                              placement="right"
                              classes={{ tooltip: 'info-tooltip-container' }}
                            >
                              <InfoIcon className="info-icon-wrap cursor-pointer" />
                            </Tooltip>
                          </Box>
                        </Box>
                      </Box>
                      <Typography
                        className="apply-leave fw600 title-text text-align cursor-pointer"
                        onClick={() =>
                          router.push(
                            `/apply-leave?leaveId=${leave?.id}&leavePeriod=${leave?.leave_period_type}`
                          )
                        }
                      >
                        Apply Leave
                      </Typography>
                    </CardContent>
                  </Card>
                </Box>
              );
            })}
          </Slider>
          <Popover
            className="leave-balance-popover"
            id={id}
            open={open}
            anchorEl={anchorEl}
            onClose={handleClose}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'left',
            }}
          >
            <Box className="d-flex align-center gap-5 settings-icon-wrap  cursor-pointer">
              <SettingsIcon className="settings-icon" />
              <Typography className="sub-title-text text-wrap">
                Accrual Details
              </Typography>
            </Box>
          </Popover>
          {/* <DialogBox
        open={handleClickOpen}
        handleClose={() => {
          handleCloseDialog();
        }}
        title=""
        content={
          <>
            <LeaveDetails />
          </>
        }
      /> */}
        </Box>
      ) : (
        <Box className="no-data-show-wrap d-flex align-center justify-center mt32 mb32">
          <NoDataView
            title="No Leave Balance Found"
            description="There is no Leave Balance data available at the moment."
          />
        </Box>
      )}
    </Box>
  );
};

export default MyLeave;
