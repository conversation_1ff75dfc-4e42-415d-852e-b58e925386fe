'use client';

import React from 'react';
import StorageLimitDialog from './StorageLimit/index';
import UserLimitDialog from './UserLimit/index';
import ProfileInCompleteDialog from './ProfileInComplete/index';
import PlanExpiredDialog from './PlanExpired/index';
import './restrictedModal.scss';

export default function RestrictedModal({
  // handleConfirm,
  restrictedModal,
  limit = false,
  setRestrictedModal,
  restrictedLimitModal,
}) {
  return (
    <>
      {limit && !restrictedLimitModal?.storageLimit ? (
        <UserLimitDialog
          isOrgView={restrictedLimitModal?.isOrgView}
          setRestrictedModal={setRestrictedModal}
          restrictedLimitModal={restrictedLimitModal}
        />
      ) : restrictedModal?.purchase_plan === false ? (
        <PlanExpiredDialog
          isOrgView={restrictedModal?.isOrgView}
          setRestrictedModal={setRestrictedModal}
        />
      ) : (restrictedModal?.isOrgView && restrictedModal?.user_status) ||
        restrictedModal?.profile_status === false ? (
        <ProfileInCompleteDialog
          isOrgView={restrictedModal?.isOrgView}
          setRestrictedModal={setRestrictedModal}
        />
      ) : (
        <StorageLimitDialog
          restrictedLimitModal={restrictedLimitModal}
          setRestrictedModal={setRestrictedModal}
          isOrgView={restrictedLimitModal?.isOrgView}
        />
      )}
    </>
  );
}
