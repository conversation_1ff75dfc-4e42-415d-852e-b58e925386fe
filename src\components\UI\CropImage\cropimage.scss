.crop-image {
  // margin-top: 20px;

  .cropper-modal {
    background-color: transparent;
  }

  .crop-save-btn {
    display: flex;
    justify-content: space-between;
    padding: 12px;

    .btn-gray {
      color: var(--color-black) !important;
    }

    @media (max-width: 768px) {
      display: flex;
      flex-direction: column;

      .btn {
        min-width: 100%;
      }

      .b-btn-blue {
        margin-top: 18px;
      }

      .ml-18 {
        margin-top: 18px;
        margin-left: 0 !important;
      }
    }
  }

  .crop-image-top {
    width: 60%;
    margin: 0 auto;
    margin-top: 20px;
    margin-bottom: 20px;
  }

  .without-crop {
    width: 70%;
    overflow: hidden;
    margin: 0 auto;

    .cropper-container {
      margin: 0 auto !important;
    }
  }

  .cropped-image {
    width: 300px;
    overflow: hidden;
    margin: 0 auto;

    .cropper-crop-box {
      transform: none !important;
    }

    .cropper-container {
      max-width: 100% !important;

      .cropper-canvas {
        display: none !important;
      }

      .cropper-crop-box {
        left: 50% !important;
        top: 50% !important;
        transform: translate(-50%, -50%) !important;
      }
    }
  }

  .zoom-plus-minus {
    display: flex;

    .disabled-btn {
      cursor: default;
      background-color: var(--color-white) !important;
    }

    .icon-btn {
      width: 20%;
      padding: 0;
    }

    .zoom-bar {
      width: 80%;
      align-self: center;

      input {
        width: 100%;
      }
    }
  }
}

.profile-cropper {
  .cropper-view-box {
    border-radius: 50%;
    overflow: hidden;
  }

  .cropper-face {
    border-radius: 50%;
  }
}
