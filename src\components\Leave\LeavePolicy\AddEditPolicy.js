'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Box, Divider, IconButton, Typography, Tooltip } from '@mui/material';
import { Formik, Form, FieldArray } from 'formik';
import * as Yup from 'yup';
import CustomSelect from '@/components/UI/selectbox';
import CustomButton from '@/components/UI/button';
import { CustomTextField } from '@/components/UI/CommonField/index';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import { useParams, useRouter } from 'next/navigation';
import _ from 'lodash';
import { identifiers } from '@/helper/constants/identifier';
import PreLoader from '@/components/UI/Loader';
import DeleteIcon from '@/components/ActionIcons/DeleteIcon';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';

const AddEditPolicy = ({
  isEdit,
  PolicyName,
  handleClose,
  getLeavePolicyDetailss,
}) => {
  const router = useRouter();
  const { policyId } = useParams();
  const formikRef = useRef(null);
  const [loader, setLoader] = useState(false);
  const [leavePolicyData, setLeavePolicyData] = useState('');
  const [leaveTypeList, setLeaveTypeList] = useState([]);

  // GET LEAVE POLICY DETAILS
  const getLeavePolicyDetails = async () => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_LEAVE_POLICY + `?id=${policyId}`
      );

      if (status === 200) {
        setLoader(false);
        const tempData = data?.data;
        const updatedLeavePolicyData = {
          ...tempData?.[0],
          leave_types: tempData?.[0]?.leave_types?.map((leaveType) => ({
            ...leaveType,
            type_id: leaveType?.id,
            type: leaveType?.duration_type,
            unlimited: leaveType?.has_unlimited ? true : false,
          })),
        };

        setLeavePolicyData(updatedLeavePolicyData);
      }
    } catch (error) {
      setLoader(false);
      setLeavePolicyData('');
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const getLeaveTypeList = async () => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_LEAVE_TYPE + `?status=active`
      );
      if (status === 200) {
        setLoader(false);

        let filterUserList = data?.data?.map((opt) => ({
          label: opt?.name,
          value: opt?.id,
        }));
        setLeaveTypeList(filterUserList ? filterUserList : []);
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  useEffect(() => {
    getLeaveTypeList();
    if (isEdit) {
      getLeavePolicyDetails();
    }
  }, [isEdit]);

  return (
    <>
      {loader && <PreLoader />}
      <Formik
        innerRef={formikRef}
        initialValues={{
          policyName: PolicyName
            ? PolicyName
            : leavePolicyData?.name
              ? leavePolicyData?.name
              : '',
          policyStatus: leavePolicyData ? leavePolicyData?.status : '',
          remark: leavePolicyData?.remark ? leavePolicyData?.remark : '',
          leave_types: leavePolicyData?.leave_types?.length
            ? leavePolicyData?.leave_types
            : [{ type_id: '', days: '', type: 'Days', unlimited: false }],
        }}
        enableReinitialize={true}
        validationSchema={Yup.object().shape({
          policyName: Yup.string().trim().required('This field is required'),
          policyStatus: Yup.string().trim().required('This field is required'),
          leave_types: Yup.array().of(
            Yup.object().shape({
              type_id: Yup.string().required('This field is required'),
              type: Yup.string().required('This field is required'),
              days: Yup.number()
                .nullable()
                .test(
                  'is-required-if-not-unlimited',
                  'This field is required',
                  function (value) {
                    const { unlimited } = this.parent; // Get the value of 'type'
                    // Only require 'days' if type is not 'Unlimited'
                    if (!unlimited) {
                      return (
                        value !== undefined && value !== null && value !== ''
                      ); // Validate that value is provided
                    }
                    return true; // No validation needed if type is 'Unlimited'
                  }
                )
                .typeError('Must be a number'),
            })
          ),
        })}
        onSubmit={async (requestData) => {
          const leaveType = requestData?.leave_types?.map((item) => ({
            type_id: item?.type_id,
            ...(!item?.unlimited && { days: item?.days }),
            has_unlimited: item?.unlimited,
            duration_type: item?.type ? item?.type : 'Days',
          }));
          const sendData = {
            name: requestData?.policyName,
            status: requestData?.policyStatus,
            remark: requestData?.remark,
            leave_types: leaveType,
          };
          let url = '';
          if (isEdit) {
            url = URLS.LEAVE_POLICY + `/${policyId}`;
          } else {
            url = URLS.LEAVE_POLICY;
          }
          try {
            setLoader(true);
            const { status, data } = await axiosInstance.post(url, sendData);

            if (status === 200 || status === 201) {
              if (data?.status) {
                setApiMessage('success', data?.message);
                if (PolicyName) {
                  handleClose();
                  getLeavePolicyDetailss(requestData?.policyName);
                }
                setTimeout(() => {
                  if (PolicyName) {
                  } else {
                    router.push('/leave-policy');
                  }
                }, 4000);
              } else {
                setApiMessage('error', data?.message);
              }
              setLoader(false);
            }
          } catch (error) {
            setLoader(false);
            setApiMessage('error', error?.response?.data?.message);
          }
        }}
      >
        {({
          errors,
          touched,
          handleBlur,
          values,
          handleSubmit,
          handleChange,
          setFieldValue,
        }) => (
          <Form onSubmit={handleSubmit}>
            <Box className={PolicyName === undefined ? 'pt24 pb24' : 'pb24'}>
              {PolicyName === undefined && <Divider className="mb8" />}

              <Box className="display-grid">
                <Box className="pt24">
                  <CustomTextField
                    InputLabelProps={{
                      shrink: true,
                    }}
                    fullWidth
                    id="policyName"
                    name="policyName"
                    value={values?.policyName}
                    label="Name *"
                    variant="filled"
                    placeholder="Name"
                    error={Boolean(touched.policyName && errors.policyName)}
                    helperText={touched.policyName && errors.policyName}
                    onBlur={handleBlur}
                    onChange={handleChange}
                  />
                </Box>
                <Box className="select-box pt24">
                  <CustomSelect
                    placeholder="Status"
                    options={identifiers?.CARD_STATUS}
                    value={values?.policyStatus}
                    className={
                      touched.policyStatus && errors.policyStatus
                        ? 'textfeild-error'
                        : ''
                    }
                    name="policyStatus"
                    error={touched.policyStatus && errors.policyStatus}
                    onChange={handleChange}
                    label={<span>Status *</span>}
                  />
                  {touched.policyStatus && errors.policyStatus && (
                    <Typography
                      variant="body2"
                      color="error"
                      className="field-error"
                    >
                      {errors.policyStatus}
                    </Typography>
                  )}
                </Box>
              </Box>

              <Box className="pt16">
                <FieldArray name="leave_types">
                  {({ push, remove }) => (
                    <Box>
                      {values?.leave_types?.map((field, index) => {
                        const selectedLeaveTypes = values.leave_types
                          .map((leaveType, i) =>
                            i !== index ? leaveType.type_id : null
                          )
                          .filter(Boolean);

                        const availableLeaveTypes = leaveTypeList.filter(
                          (leaveType) =>
                            !selectedLeaveTypes.includes(leaveType.value)
                        );
                        return (
                          <Box key={index} className="display-grid pt8">
                            <Box className="select-box pt24">
                              <CustomSelect
                                placeholder="Leave Type"
                                options={availableLeaveTypes}
                                value={field?.type_id}
                                name={`leave_types.${index}.type_id`}
                                className={
                                  touched.leave_types?.[index]?.type_id &&
                                  errors.leave_types?.[index]?.type_id
                                    ? 'textfeild-error'
                                    : ''
                                }
                                error={Boolean(
                                  touched.leave_types?.[index]?.type_id &&
                                    errors.leave_types?.[index]?.type_id
                                )}
                                onChange={handleChange}
                                label={<span>Leave Type *</span>}
                              />
                              {touched.leave_types?.[index]?.type_id &&
                                errors.leave_types?.[index]?.type_id && (
                                  <Typography
                                    variant="body2"
                                    color="error"
                                    className="field-error"
                                  >
                                    {errors.leave_types?.[index]?.type_id}
                                  </Typography>
                                )}
                            </Box>
                            <Box
                              className="select-box pt24"
                              alignItems="baseline"
                            >
                              <CustomSelect
                                placeholder="Leave Per Hours/Days"
                                options={identifiers.Leave_TYPE}
                                value={field?.type}
                                name={`leave_types.${index}.type`}
                                className={
                                  touched.leave_types?.[index]?.type &&
                                  errors.leave_types?.[index]?.type
                                    ? 'textfeild-error w100'
                                    : 'w100'
                                }
                                error={Boolean(
                                  touched.leave_types?.[index]?.type &&
                                    errors.leave_types?.[index]?.type
                                )}
                                onChange={(e) => {
                                  handleChange(e);
                                  setFieldValue(
                                    `leave_types.${index}.days`,
                                    ''
                                  );
                                }}
                                label={<span>Leave Per Hours/Days *</span>}
                              />
                              {touched.leave_types?.[index]?.type &&
                                errors.leave_types?.[index]?.type && (
                                  <Typography
                                    variant="body2"
                                    color="error"
                                    className="field-error"
                                  >
                                    {errors.leave_types?.[index]?.type}
                                  </Typography>
                                )}
                            </Box>

                            <Box
                              className="d-flex pt24 ml8"
                              alignItems="baseline"
                            >
                              <CustomTextField
                                InputLabelProps={{
                                  shrink: true,
                                }}
                                fullWidth
                                id={`leave_types.${index}.days`}
                                name={`leave_types.${index}.days`}
                                value={field?.days}
                                label={`Number of ${
                                  field?.type ? field?.type : 'Days'
                                } *`}
                                variant="filled"
                                placeholder={`Number of ${
                                  field?.type ? field?.type : 'Days'
                                }`}
                                error={Boolean(
                                  touched.leave_types?.[index]?.days &&
                                    errors.leave_types?.[index]?.days
                                )}
                                helperText={
                                  touched.leave_types?.[index]?.days &&
                                  errors.leave_types?.[index]?.days
                                }
                                // className={
                                //   field?.unlimited ? 'textfield-error' : ''
                                // }
                                disabled={field?.unlimited}
                                onBlur={handleBlur}
                                onChange={handleChange}
                                onInput={(e) => {
                                  e.target.value = e.target.value.replace(
                                    /[^0-9.]/g,
                                    ''
                                  );
                                }}
                              />
                              <IconButton
                                onClick={() => {
                                  setFieldValue(
                                    `leave_types.${index}.unlimited`,
                                    field?.unlimited ? false : true
                                  );
                                  setFieldValue(
                                    `leave_types.${index}.days`,
                                    ''
                                  );
                                }}
                                className="ml16 mr8 d-flex cursor-pointer button-unlimited"
                              >
                                <Tooltip title="Unlimited Leave">
                                  {field?.unlimited ? (
                                    <CheckBoxIcon />
                                  ) : (
                                    <CheckBoxOutlineBlankIcon />
                                  )}
                                </Tooltip>
                                <Typography className="p14">
                                  {' '}
                                  Unlimited Leave
                                </Typography>
                              </IconButton>
                              <IconButton
                                onClick={() => remove(index)}
                                color="error"
                                className="ml8"
                              >
                                <DeleteIcon />
                              </IconButton>
                            </Box>
                          </Box>
                        );
                      })}
                      <CustomButton
                        variant="contained"
                        fontWeight="600"
                        className="p16 mt16"
                        background="#39596e"
                        backgroundhover="#FFFFFF"
                        colorhover="#000000"
                        title="Add"
                        disabled={loader}
                        fullWidth={false}
                        onClick={() =>
                          push({
                            type_id: '',
                            days: '',
                            type: 'Days',
                            unlimited: false,
                          })
                        }
                      />
                    </Box>
                  )}
                </FieldArray>
                <Box className="pt32">
                  <CustomTextField
                    InputLabelProps={{
                      shrink: true,
                    }}
                    fullWidth
                    id="remark"
                    name="remark"
                    value={values?.remark}
                    label="Remark"
                    variant="filled"
                    placeholder="Remark"
                    onBlur={handleBlur}
                    onChange={handleChange}
                  />
                </Box>
              </Box>
            </Box>

            <Box className="save-btn-box create-cancel-button">
              <CustomButton
                variant="contained"
                fontWeight="600"
                className="p16 secondary-button "
                background="#FFFFFF"
                backgroundhover="#39596e"
                colorhover="#FFFFFF"
                title={`${loader ? 'Saving...' : 'Save'}`}
                disabled={loader}
                fullWidth={false}
                type="submit"
              />
            </Box>
          </Form>
        )}
      </Formik>
    </>
  );
};

export default AddEditPolicy;
