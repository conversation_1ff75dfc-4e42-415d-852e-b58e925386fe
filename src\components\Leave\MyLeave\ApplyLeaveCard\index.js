'use client';
import React, { useContext, useState } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Tooltip,
  Popover,
} from '@mui/material';
import { LeavePaid, LeaveUnPaid } from '@/helper/common/images';
import Slider from 'react-slick';
import InfoIcon from '@mui/icons-material/Info';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import SettingsIcon from '@mui/icons-material/Settings';
import CustomSelect from '@/components/UI/CustomSelect';
import { generateYearsFromJoiningDate } from '@/helper/common/commonFunctions';
import AuthContext from '@/helper/authcontext';
import NoDataView from '@/components/UI/NoDataView';
import '../myleave.scss';

const ApplyLeaveCard = ({
  userDetails,
  yearFilterdata,
  setYearFilterData,
  getSingleUserBalance,
  authId,
}) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const { authState } = useContext(AuthContext);

  const open = Boolean(anchorEl);

  const id = open ? 'simple-popper' : undefined;

  const handleClose = () => {
    setAnchorEl(null);
  };

  const settings = {
    dots: false,
    infinite: false,
    arrows: true,
    speed: 500,
    slidesToShow: 4,
    slidesToScroll: 1,
    centerMode: false,
    responsive: [
      {
        breakpoint: 1800,
        settings: {
          slidesToShow: 3,
        },
      },
      {
        breakpoint: 1600,
        settings: {
          slidesToShow: 3,
        },
      },
      {
        breakpoint: 1480,
        settings: {
          slidesToShow: 2.7,
        },
      },
      {
        breakpoint: 1440,
        settings: {
          slidesToShow: 2.6,
        },
      },
      {
        breakpoint: 1400,
        settings: {
          slidesToShow: 2.5,
        },
      },
      {
        breakpoint: 1200,
        settings: {
          slidesToShow: 2.1,
        },
      },
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 1.7,
        },
      },
      {
        breakpoint: 992,
        settings: {
          slidesToShow: 1.62,
        },
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 2.15,
        },
      },
      {
        breakpoint: 576,
        settings: {
          slidesToShow: 1.6,
        },
      },
      {
        breakpoint: 425,
        settings: {
          slidesToShow: 1.2,
        },
      },
      {
        breakpoint: 375,
        settings: {
          slidesToShow: 1.05,
        },
      },
      {
        breakpoint: 320,
        settings: {
          slidesToShow: 1,
        },
      },
    ],
  };

  return (
    <Box className="my-leave-card-wrap">
      <Box className="d-flex justify-end">
        <Box>
          <CustomSelect
            placeholder="Year"
            options={generateYearsFromJoiningDate(
              authState?.user_joining_date
                ? authState?.user_joining_date
                : authState?.createdAt
                  ? authState?.createdAt
                  : ''
            )}
            value={
              generateYearsFromJoiningDate(
                authState?.user_joining_date
                  ? authState?.user_joining_date
                  : authState?.createdAt
                    ? authState?.createdAt
                    : ''
              )?.find((opt) => {
                return opt?.value === yearFilterdata;
              }) || ''
            }
            onChange={(e) => {
              const selectedYear = e?.value;
              setYearFilterData(selectedYear); // Update state with selected year
              if (authId && selectedYear) {
                getSingleUserBalance(authId, selectedYear); // Call API with authId and selected year
              }
            }}
          />
        </Box>
      </Box>
      {userDetails?.length > 0 ? (
        <Box className={'my-leave-card'}>
          <Slider {...settings}>
            {userDetails?.map((leave) => {
              return (
                <Card className="card-wrap leave-card">
                  <CardContent className="card-content-wrap card-details-wrap">
                    <Box className="d-flex align-center justify-space-between leave-wrap">
                      <Box className="d-flex align-center">
                        <Box
                          className="leave-icon-wrap"
                          sx={{
                            backgroundColor: leave?.leave_type_color,
                          }}
                        >
                          {/* {leave?.name === 'Privileged leaves' ? (
                            <PrevillageLeaveIcon />
                          ) : leave?.name === 'Sick Leave' ? (
                            <SickLeaveIcon />
                          ) : leave?.name === 'Comp Off' ? (
                            <CampOffIcon />
                          ) : (
                            <LwpIcon />
                          )} */}
                        </Box>
                        <Box className="d-flex align-center">
                          <Box variant="h6" className="d-flex align-center">
                            <Tooltip
                              title={<Typography>{leave?.name}</Typography>}
                              arrow
                              classes={{ tooltip: 'info-tooltip-container' }}
                            >
                              <Typography
                                variant="h6"
                                className="title-text pl8 fw600 text-ellipsis-line"
                              >
                                {leave?.name}
                              </Typography>
                            </Tooltip>
                            <Typography
                              variant="h6"
                              className="fw600 title-text"
                            >
                              (
                              {leave?.has_leave_unlimited === true ? (
                                <span className="unlimited-text-wrap">
                                  Unlimited
                                </span>
                              ) : (
                                leave?.leave_balance
                              )}
                              )
                            </Typography>
                          </Box>
                          <Tooltip
                            arrow
                            title={leave?.leave_deduction_type}
                            classes={{ tooltip: 'info-tooltip-container' }}
                          >
                            <span className="leave-type-wrap d-flex align-center cursor-pointer">
                              {leave?.leave_deduction_type === 'unpaid' ? (
                                <LeaveUnPaid />
                              ) : (
                                <LeavePaid />
                              )}
                            </span>
                          </Tooltip>
                        </Box>
                      </Box>
                      {/* <Box className="leave-type-wrap">
                    <MoreHorizIcon
                      className="cursor-pointer"
                      onClick={handleClick}
                    />
                  </Box> */}
                    </Box>
                    <Box className="d-flex align-center">
                      <Box className="balance-wrap text-align">
                        <Typography component="p" className="title-text">
                          Balance
                        </Typography>
                        <Box className="d-flex align-center gap-10 justify-center">
                          <Typography className="title-text fw600 text-wrap">
                            {leave?.has_leave_unlimited === true ? (
                              <span className="unlimited-text-wrap">
                                Unlimited
                              </span>
                            ) : leave?.user_remaining_leave === null ? (
                              '0'
                            ) : (
                              leave?.user_remaining_leave
                            )}
                          </Typography>
                          <Tooltip
                            arrow
                            title={
                              <Typography>
                                The Planned leave balance will be debited once
                                the specified date has passed.
                              </Typography>
                            }
                            //open
                            classes={{ tooltip: 'info-tooltip-container' }}
                          >
                            <InfoIcon className="info-icon-wrap cursor-pointer" />
                          </Tooltip>
                        </Box>
                      </Box>
                      <Box className="booked-wrap text-align">
                        <Typography component="p" className="title-text">
                          Booked
                        </Typography>
                        <Box className="d-flex align-center gap-10 justify-center">
                          <Typography
                            variant="body1"
                            className="title-text fw600 text-wrap"
                          >
                            {leave?.used_leave?.total}
                          </Typography>
                          <Tooltip
                            arrow
                            title={
                              <Box>
                                <Typography className="sub-title-text">
                                  Used: {leave?.used_leave?.used_leaves}
                                </Typography>
                                <Typography className="sub-title-text">
                                  Planned: {leave?.used_leave?.planned_leaves}
                                </Typography>
                              </Box>
                            }
                            placement="right"
                            classes={{ tooltip: 'info-tooltip-container' }}
                          >
                            <InfoIcon className="info-icon-wrap cursor-pointer" />
                          </Tooltip>
                        </Box>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              );
            })}
          </Slider>
          <Popover
            className="leave-balance-popover"
            id={id}
            open={open}
            anchorEl={anchorEl}
            onClose={handleClose}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'left',
            }}
          >
            <Box className="d-flex align-center gap-5 settings-icon-wrap  cursor-pointer">
              <SettingsIcon className="settings-icon" />
              <Typography className="sub-title-text text-wrap">
                Accrual Details
              </Typography>
            </Box>
          </Popover>
          {/* <DialogBox
        open={handleClickOpen}
        handleClose={() => {
          handleCloseDialog();
        }}
        title=""
        content={
          <>
            <LeaveDetails />
          </>
        }
      /> */}
        </Box>
      ) : (
        <Box className="no-data-show-wrap d-flex align-center justify-center mt32 mb32">
          <NoDataView
            title="No Leave Balance Found"
            description="There is no Leave Balance data available at the moment."
          />
        </Box>
      )}
    </Box>
  );
};

export default ApplyLeaveCard;
