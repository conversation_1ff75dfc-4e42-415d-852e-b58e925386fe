'use client';

import React from 'react';
import { Box, Typography } from '@mui/material';
import { DateFormat } from '@/helper/common/commonFunctions';
import NoDataView from '@/components/UI/NoDataView';
import './recenttickets.scss';

const RecentTickets = ({
  tickets = [],
  title = 'Recent Tickets',
  emptyMessage = 'No recent tickets found',
  showPriority = true,
  className = 'recent-tickets',
}) => {
  const getStatusClass = (status) => {
    const statusMap = {
      open: 'status-yellow',
      'in progress': 'draft',
      in_progress: 'draft', // Handle new API format
      resolved: 'active-onboarding',
      closed: 'success',
    };
    return statusMap[status] || 'success';
  };

  const getPriorityClass = (priority) => {
    const priorityMap = {
      urgent: 'failed',
      high: 'failed',
      medium: 'draft',
      low: 'active-onboarding',
    };
    return priorityMap[priority] || 'success';
  };

  const formatStatus = (status) => {
    // Convert API status format to display format
    const statusMap = {
      in_progress: 'In Progress',
      open: 'Open',
      resolved: 'Resolved',
      closed: 'Closed',
    };
    return statusMap[status] || status;
  };

  const formatPriority = (priority) => {
    // Convert API priority format to display format
    const priorityMap = {
      high: 'High',
      medium: 'Medium',
      low: 'Low',
      urgent: 'Urgent',
    };
    return priorityMap[priority] || priority;
  };

  return (
    <Box className={className}>
      <Box className={`${className}__header`}>
        <Typography className="sub-header-text">{title}</Typography>
      </Box>

      <Box className={`${className}__content`}>
        {tickets.length > 0 ? (
          <Box className={`${className}__list`}>
            {tickets.map((ticket) => (
              <Box key={ticket.id} className={`${className}__item`}>
                <Box className={`${className}__item-header`}>
                  <Typography className="title-text">{ticket.title}</Typography>
                  <Typography className="sub-title-text">
                    {DateFormat(ticket.date, 'datesWithhour')}
                  </Typography>
                </Box>

                <Box className={`${className}__item-footer`}>
                  {showPriority ? (
                    <Box className={`${className}__item-badges`}>
                      <Typography
                        className={`sub-title-text fw600 ${getStatusClass(ticket.status)}`}
                      >
                        {formatStatus(ticket.status)}
                      </Typography>
                      <Typography
                        className={`sub-title-text fw600 ${getPriorityClass(ticket.priority)}`}
                      >
                        {formatPriority(ticket.priority)}
                      </Typography>
                    </Box>
                  ) : (
                    <Typography
                      className={`sub-title-text fw600 ${getStatusClass(ticket.status)}`}
                    >
                      {formatStatus(ticket.status)}
                    </Typography>
                  )}
                </Box>
              </Box>
            ))}
          </Box>
        ) : (
          <Box className={`${className}__empty`}>
            <NoDataView
              title={emptyMessage}
              description="There is no tickets available at the moment."
            />
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default RecentTickets;
