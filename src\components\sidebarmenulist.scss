/* SideBarMenuList.css */

.menu-container {
  width: 240px;
  background-color: #fff;
  height: 100vh;
  position: fixed;
  top: 0;
  right: -240px;
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  transition: right 300ms ease-in-out;
}

.menu-container.open {
  right: 0;
}

.header-close-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xxl);
  //   padding: 16px;
  .sidebar-logo-wrap {
    width: 100%;
    .side-menu-logo-wrap {
      .side-menu-logo {
        width: 80px;
        height: auto;
      }
    }
  }
}

.menu-ul-list {
  padding: 0;
  margin: 0;
}
