'use client';
// import CustomSelect from '@/components/UI/selectbox';
import { useContext, useEffect, useState } from 'react';
import CustomButton from '@/components/UI/CustomButton';
import KeyboardArrowLeftIcon from '@mui/icons-material/KeyboardArrowLeft';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import RotaDashboardCalendar from './RotaDashboardCalendar';
import moment from 'moment';
import { DesktopDatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import RotaDashboardRightSideFilter from './RotaDashboardRightSideFilter';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { setApiMessage } from '@/helper/common/commonFunctions';
// import PreLoader from '@/components/UI/Loader';
import { ROTA_URLS } from '@/helper/constants/urls';
import AuthContext from '@/helper/authcontext';
import TodayIcon from '@mui/icons-material/Today';
import { Box, Tooltip, Typography } from '@mui/material';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { useRouter } from 'next/navigation';
import CustomSelect from '@/components/UI/CustomSelect';
import ContentLoader from '@/components/UI/ContentLoader';

const RotaDashboardFilter = () => {
  const navigate = useRouter();
  const { authState, AllListsData } = useContext(AuthContext);
  const [filterData, setFilterData] = useState({
    currentDate: moment(new Date()).format('YYYY-MM-DD'),
    location: 'all',
    dayMonth: 'month',
    sortBy: '',
    selectedRoles: [],
  });
  const [anchorEl, setAnchorEl] = useState(null);
  const [openDatePicker, setOpenDatePicker] = useState(false);
  const [isLoader, setIsLoader] = useState(false);
  const [shiftList, setShiftList] = useState([]);
  const [locationList, setLocationList] = useState([]);

  const viewAccessOnly = authState?.web_user_active_role_id
    ? authState.web_user_active_role_id === 1 ||
      authState.web_user_active_role_id === 7 ||
      authState.web_user_active_role_id === 14
    : '';

  // List of dashboard shifts
  const getDashboardShiftList = async () => {
    let filterList = '';

    if (filterData?.dayMonth === 'day') {
      const selectedDate = moment(filterData?.currentDate).format('YYYY-MM-DD');
      filterList = `?from=${selectedDate}&to=${selectedDate}&sortBy=${filterData?.sortBy}`;
    } else if (filterData?.dayMonth === 'month') {
      const firstDay = moment(filterData?.currentDate)
        .startOf('month')
        .format('YYYY-MM-DD');
      const lastDay = moment(filterData?.currentDate)
        .endOf('month')
        .format('YYYY-MM-DD');

      filterList = `?from=${firstDay}&to=${lastDay}`;
    }

    filterList += `&isAdmin=true&isDashboard=true&branchId=${filterData?.location === 'all' ? '' : filterData?.location}`;

    try {
      setIsLoader(true);
      const { status, data } = await axiosInstance.get(
        ROTA_URLS?.SHIFT_URL + filterList
      );
      if (status === 200) {
        setIsLoader(false);

        const isDayView = filterData?.dayMonth === 'day';

        const dayShiftEventsUpdate =
          data?.data?.length > 0
            ? data?.data
                ?.find((event) => event?.date === filterData?.currentDate)
                ?.shifts?.map((event) => {
                  const StartDate = event?.startTime;
                  const EndDate = event?.endTime;
                  return {
                    ...event,
                    start: StartDate,
                    end: EndDate,
                    // allDay: true, // Ensures the event shows in all views
                  };
                })
            : [];
        const monthShiftEventsUpdate = data?.data?.map((event) => {
          return {
            ...event,
            start: event?.date,
            allDay: true,
          };
        });

        setShiftList(isDayView ? dayShiftEventsUpdate : monthShiftEventsUpdate);
      }
    } catch (err) {
      setApiMessage('error', err?.response?.data?.message);
      setIsLoader(false);
    }
  };
  useEffect(() => {
    getDashboardShiftList();
  }, [
    filterData?.dayMonth,
    filterData?.currentDate,
    filterData?.location,
    filterData?.sortBy,
  ]);

  useEffect(() => {
    if (AllListsData?.ActiveBranchList && authState.web_user_active_role_id) {
      if (
        authState.web_user_active_role_id === 7 ||
        authState.web_user_active_role_id === 14
      ) {
        setFilterData({
          ...filterData,
          location: [...AllListsData.ActiveBranchList]?.[0]?.value,
        });
        setLocationList(AllListsData.ActiveBranchList);
      } else {
        setLocationList([
          { label: 'All Branches', value: 'all' },
          ...AllListsData.ActiveBranchList,
        ]);
      }
    }
  }, [AllListsData?.ActiveBranchList, authState.web_user_active_role_id]);

  const SORT_OPTIONS = [
    { label: 'Sort By', value: '' },
    { label: 'Start Time', value: 'startTime' },
    { label: 'Role', value: 'roleId' },
  ];

  const handlePrev = () => {
    setFilterData((prev) => ({
      ...prev,
      currentDate: moment(prev.currentDate || new Date())
        .subtract(1, prev.dayMonth === 'day' ? 'days' : 'months')
        .format('YYYY-MM-DD'),
    }));
  };

  const handleNext = () => {
    setFilterData((prev) => ({
      ...prev,
      currentDate: moment(prev.currentDate || new Date())
        .add(1, prev.dayMonth === 'day' ? 'days' : 'months')
        .format('YYYY-MM-DD'),
    }));
  };

  const handleRolesSelection = (eventId) => {
    setFilterData((prevState) => {
      const isSelected = prevState.selectedRoles.includes(eventId);
      return {
        ...prevState,
        selectedRoles: isSelected
          ? prevState.selectedRoles.filter((id) => id !== eventId)
          : [...prevState.selectedRoles, eventId],
      };
    });
  };

  const handleDateClick = (selectedDate, isView) => {
    if (viewAccessOnly) {
      setFilterData({
        ...filterData,
        dayMonth: isView, // Switch to day view
        currentDate: moment(selectedDate).format('YYYY-MM-DD'), // Update date
      });
    } else {
      navigate.push(
        `/rotas?is_view_type=month&is_current_date=${moment(selectedDate).format('YYYY-MM-DD')}`
      );
    }
  };

  return (
    <div>
      {/* {isLoader && <PreLoader />} */}
      <div className="dashboard-filter-section">
        <div className="filter-wrap">
          <div className="select-box location-select-field">
            {viewAccessOnly && (
              <CustomSelect
                placeholder="All Branches"
                options={locationList}
                value={locationList?.find(
                  (item) => item?.value === filterData?.location
                )}
                onChange={(e) => {
                  setFilterData({
                    ...filterData,
                    location: e?.value,
                  });
                }}
                menuPosition="fixed"
                isClearable={false}
              />
            )}
          </div>
          <div className="datepicker-filter">
            <CustomButton
              variant="outlined"
              isIconOnly
              startIcon={
                <Tooltip
                  title={
                    <Typography className="sub-title-text">
                      {filterData?.dayMonth === 'day'
                        ? 'Go to current day'
                        : 'Go to current month'}
                    </Typography>
                  }
                  classes={{
                    tooltip: 'info-tooltip-container',
                  }}
                  arrow
                >
                  <TodayIcon />
                </Tooltip>
              }
              onClick={() => {
                handleDateClick(new Date(), filterData?.dayMonth);
              }}
            />

            {/* <div className="today-icon">
              <Tooltip
                title={
                  <div>
                    <p className="p12">
                      {filterData?.dayMonth === 'day'
                        ? 'Go to current day'
                        : 'Go to current month'}
                    </p>
                  </div>
                }
                arrow
                classes={{
                  tooltip:
                    'calendar-event-list-tooltip-custom calendar-event-other-details-tooltip',
                }}
              >
                <TodayIcon
                  onClick={() =>
                    handleDateClick(new Date(), filterData?.dayMonth)
                  }
                />
              </Tooltip>
            </div> */}
            <div className="calendar-header">
              <KeyboardArrowLeftIcon
                className="cursor-pointer"
                onClick={handlePrev}
              />
              <div
                onClick={(event) => {
                  setAnchorEl(event.currentTarget);
                  setOpenDatePicker(true);
                }}
                className="cursor-pointer current-month-year-text"
              >
                {
                  filterData.dayMonth === 'day'
                    ? moment(filterData.currentDate || new Date()).format(
                        'dddd DD MMMM YYYY'
                      ) // Example: Wednesday 19 February 2025
                    : moment(filterData.currentDate || new Date()).format(
                        'MMMM YYYY'
                      ) // Example: February 2025
                }

                <ExpandMoreIcon size="small" />
              </div>
              <KeyboardArrowRightIcon
                className="cursor-pointer"
                onClick={handleNext}
              />
            </div>
            <LocalizationProvider
              dateAdapter={AdapterDayjs}
              dateFormats={{ monthShort: 'MMMM' }}
            >
              <DesktopDatePicker
                open={openDatePicker}
                onClose={() => setOpenDatePicker(false)}
                // className="calendar-year-month-select-field"
                value={
                  filterData?.currentDate
                    ? dayjs(filterData?.currentDate, 'YYYY-MM-DD')
                    : null
                }
                onChange={(newValue) => {
                  if (newValue) {
                    setFilterData((prev) => ({
                      ...prev,
                      currentDate: dayjs(newValue).format('YYYY-MM-DD'),
                    }));
                  }
                  setOpenDatePicker(false);
                }}
                slotProps={{
                  textField: { style: { display: 'none' } }, // Hide input field
                  popper: {
                    anchorEl,
                    placement: 'bottom',
                    className: 'calendar-year-month-select-field',
                  },
                }}
                views={
                  filterData.dayMonth === 'day'
                    ? ['day', 'month', 'year']
                    : ['month', 'year']
                }
              />
            </LocalizationProvider>
          </div>
          {viewAccessOnly && (
            <div className="calender-filter">
              {filterData.dayMonth === 'day' && (
                <div className="select-box sort-by-select-field">
                  <CustomSelect
                    placeholder="Sort By"
                    options={SORT_OPTIONS}
                    value={SORT_OPTIONS?.find(
                      (item) => item?.value === filterData?.sortBy
                    )}
                    onChange={(e) => {
                      setFilterData({
                        ...filterData,
                        sortBy: e?.value,
                      });
                    }}
                    menuPosition="fixed"
                    isClearable={false}
                  />
                </div>
              )}
              <CustomButton
                className={`calender-day-month-btn ${filterData?.dayMonth === 'day' ? 'active' : ''}`}
                variant="outlined"
                title="Day"
                onClick={() => {
                  setFilterData({
                    ...filterData,
                    dayMonth: 'day',
                  });
                }}
              />

              <CustomButton
                className={`calender-day-month-btn ${filterData?.dayMonth === 'month' ? 'active' : ''}`}
                variant="outlined"
                title="Month"
                onClick={() => {
                  setFilterData({
                    ...filterData,
                    dayMonth: 'month',
                  });
                }}
              />
            </div>
          )}
        </div>
      </div>
      <div className="dashboard-calendar-section">
        {isLoader ? (
          <Box className="w100">
            <ContentLoader />
          </Box>
        ) : (
          <>
            <div
              className="dashboard-calendar"
              style={{
                width:
                  filterData?.dayMonth === 'month' && viewAccessOnly
                    ? '80%'
                    : '100%',
              }}
            >
              <RotaDashboardCalendar
                filterData={filterData}
                handleDateClick={handleDateClick}
                shiftList={shiftList}
                viewAccessOnly={viewAccessOnly}
              />
            </div>
            {filterData?.dayMonth === 'month' && viewAccessOnly && (
              <div className="dashboard-right-side-filter-section">
                <RotaDashboardRightSideFilter
                  filterData={filterData}
                  handleRolesSelection={handleRolesSelection}
                  shiftList={shiftList}
                />
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};
export default RotaDashboardFilter;
