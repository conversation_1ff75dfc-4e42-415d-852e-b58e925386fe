'use client';
import React, { useEffect, useState, useContext } from 'react';
import {
  Box,
  Checkbox,
  Divider,
  TextareaAutosize,
  Typography,
} from '@mui/material';
import { Form, Formik } from 'formik';
import * as Yup from 'yup';
import CustomSelect from '@/components/UI/CustomSelect';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomButton from '@/components/UI/CustomButton';
import { CustomTextareaAutosize } from '@/components/UI/CustomAdressField';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { ORG_URLS } from '@/helper/constants/urls';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { identifiers } from '@/helper/constants/identifier';
import { useRouter } from 'next/navigation';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import AuthContext from '@/helper/authcontext';
import { saveToStorage, fetchFromStorage } from '@/helper/context';
import CustomEditor from '@/components/UI/CustomEditor';
import './createpla.scss';

export default function CreatePlan({ planId }) {
  const router = useRouter();
  const [subPlanData, setSubPlanData] = useState([]);
  const { userdata, setUserdata } = useContext(AuthContext);

  // Fetches the details of a subscription plan by its ID.
  const getSubPlanDetailsById = async (id) => {
    // setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        ORG_URLS.GET_SUBSCRIPTION_PLAN_BY_ID + `/${id}`
      );
      if (status === 200) {
        setSubPlanData(data?.data);
      }
    } catch (error) {
      setApiMessage(
        'error',
        error?.response?.data?.message || 'Error fetching data'
      );
    }
  };

  // useEffect hook that triggers when the `params?.id` changes.
  useEffect(() => {
    if (planId) {
      getSubPlanDetailsById(planId);
    }
  }, [planId]);

  useEffect(() => {
    if (
      fetchFromStorage(identifiers?.RedirectData) &&
      fetchFromStorage(identifiers?.RedirectData)?.planEdit &&
      fetchFromStorage(identifiers?.RedirectData)?.isCreate
    ) {
      const plandata = fetchFromStorage(identifiers?.RedirectData);
      saveToStorage(identifiers?.RedirectData, {
        ...plandata,
        isCreate: false,
      });
      setUserdata({
        ...plandata,
        isCreate: false,
      });
    } else if (userdata && userdata?.planEdit && userdata?.isCreate) {
      setUserdata({
        ...userdata,
        isCreate: false,
      });
    }
  }, [
    fetchFromStorage(identifiers?.RedirectData)?.planEdit,
    userdata?.planEdit,
  ]);

  return (
    <Box className="create-plan-wrap">
      <Formik
        enableReinitialize={true}
        initialValues={{
          name: subPlanData ? subPlanData?.subs_plan_name : '',
          discription: subPlanData ? subPlanData?.subs_plan_description : '',
          duration: subPlanData ? subPlanData?.subs_total_days : '',
          plan_cost: subPlanData ? subPlanData?.subs_plan_cost : '',
          plan_storage: subPlanData ? subPlanData?.sub_storage_value : '',
          status: subPlanData?.subs_plan_status || 'active',
          visibility: subPlanData?.subs_plan_visibility || 'public',
          min_limit: subPlanData ? subPlanData?.subs_limit_min : 1,
          max_limit: subPlanData ? subPlanData?.subs_limit_max : '',
          category: subPlanData?.subs_plan_category || 'core',
          plan_type: subPlanData?.subs_plan_type || 'recursive',
          plan_content:
            subPlanData && subPlanData?.subs_plan_content
              ? subPlanData?.subs_plan_content
              : '',
          is_repeated_plan: subPlanData
            ? subPlanData?.is_creation_repeated === 1
              ? 'yes'
              : 'no'
            : 'yes',
          createdAt: new Date().toLocaleString(),
          free_trial:
            subPlanData?.subs_is_free_trial === 1 ? true : false || false,
        }}
        validationSchema={Yup.object({
          name: Yup.string().required('Name is required'),
          duration: Yup.string().when('plan_type', {
            is: 'recursive',
            then: (schema) => schema.trim().required('Duration is required'),
          }),
          is_repeated_plan: Yup.string().when('plan_type', {
            is: 'one_time',
            then: (schema) =>
              schema.trim().required('Repeated plan is required'),
          }),
          plan_cost: Yup.number()
            .typeError('Plan Cost must be a number')
            .required('Plan Cost is required'),
          status: Yup.string().required('Status is required'),
          visibility: Yup.string().required('Visibility is required'),
          category: Yup.string().required('Category is required'),
          plan_type: Yup.string().required('Plan Type is required'),
          plan_storage: Yup.lazy((value, context) => {
            return context.parent.category === 'core'
              ? Yup.string().required('Plan Storage is required')
              : Yup.string().notRequired();
          }),
          min_limit: Yup.number()
            .typeError('Min limit must be a number')
            .required('Min limit is required')
            .positive('Min limit must be a positive number')
            .integer('Min limit must be a whole number'),
          max_limit: Yup.number()
            .typeError('Max limit must be a number')
            .required('Max limit is required')
            .positive('Max limit must be a positive number')
            .integer('Max limit must be a whole number'),
        })}
        onSubmit={async (requestedData) => {
          let sendData = {
            subs_plan_name: requestedData?.name,
            subs_plan_description: requestedData?.discription,
            subs_limit_min: Number(requestedData?.min_limit),
            subs_limit_max: Number(requestedData?.max_limit),
            subs_plan_cost: Number(requestedData?.plan_cost),
            ...(requestedData?.category === 'core' && {
              sub_storage_value: Number(requestedData?.plan_storage),
            }),
            subs_plan_visibility: requestedData?.visibility,
            subs_plan_status: requestedData?.status,
            subs_total_days:
              requestedData?.plan_type === 'one_time'
                ? 0
                : Number(requestedData?.duration),
            is_creation_repeated:
              requestedData?.plan_type === 'one_time'
                ? requestedData?.is_repeated_plan === 'yes'
                  ? true
                  : false
                : false,
            subs_is_free_trial: requestedData?.free_trial,
            subs_plan_type: requestedData?.plan_type,
            subs_plan_category: requestedData?.category,
            ...(requestedData?.plan_content && {
              subs_plan_content: requestedData?.plan_content,
            }),
          };
          try {
            const { status, data } = planId
              ? await axiosInstance.put(
                  `${ORG_URLS.UPDATE_SUBSCRIPTION_PLAN}/${planId}`,
                  sendData
                )
              : await axiosInstance.post(
                  ORG_URLS.CREATE_SUBSCRIPTION_PLAN,
                  sendData
                );
            if (status === 200) {
              router.push('/sorg/organization');
              setApiMessage('success', data?.message);
            } else {
              setApiMessage('error', data?.message);
            }
          } catch (error) {
            setApiMessage('error', error?.response?.data?.message);
          }
        }}
      >
        {({
          values,
          handleChange,
          handleBlur,
          handleSubmit,
          errors,
          touched,
          setFieldValue,
        }) => {
          return (
            <Form onSubmit={handleSubmit} className="create-sub-plan-form">
              <Box className="d-flex align-center mb8">
                <ArrowBackIosIcon
                  className="cursor-pointer mt4"
                  onClick={() => {
                    router.push(`/sorg/organization`);
                  }}
                />
                <Typography className="p16 fw600 pr8">
                  {planId ? ' Update plan' : 'Create plan'}
                </Typography>
              </Box>
              <Divider />
              <Box className="pt16 create-form-grid">
                <Box>
                  <CustomTextField
                    fullWidth
                    name="name"
                    label="Plan Name"
                    placeholder="Enter plan name"
                    value={values?.name}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched?.name && Boolean(errors?.name)}
                    helperText={touched?.name && errors?.name}
                    required
                  />
                </Box>
                <Box>
                  <CustomTextField
                    fullWidth
                    name="plan_cost"
                    label="Plan Cost"
                    placeholder="Enter Plan Cost"
                    value={values?.plan_cost}
                    onBlur={handleBlur}
                    onChange={handleChange}
                    error={touched?.plan_cost && Boolean(errors?.plan_cost)}
                    helperText={touched?.plan_cost && errors?.plan_cost}
                    required
                    onInput={(e) => {
                      e.target.value = e?.target?.value.replace(/[^0-9]/g, '');
                    }}
                  />
                </Box>
                <Box>
                  <CustomSelect
                    fullWidth
                    name="status"
                    label="Status"
                    placeholder="Select Status"
                    options={identifiers?.PLAN_STATUS}
                    value={
                      identifiers?.PLAN_STATUS?.find((opt) => {
                        return opt?.value === values?.status;
                      }) || ''
                    }
                    onChange={(selectedOption) =>
                      setFieldValue('status', selectedOption?.value)
                    }
                    onBlur={handleBlur}
                    error={touched?.status && errors?.status}
                    helperText={touched?.status && errors?.status}
                    isClearable={false}
                    required
                  />
                </Box>
                <Box>
                  <CustomSelect
                    fullWidth
                    name="visibility"
                    label="Plan Visibility"
                    placeholder="Select Visibility"
                    options={identifiers?.PLAN_VISIBILITY_STATUS}
                    value={
                      identifiers?.PLAN_VISIBILITY_STATUS?.find((opt) => {
                        return opt?.value === values?.visibility;
                      }) || ''
                    }
                    onChange={(selectedOption) =>
                      setFieldValue('visibility', selectedOption?.value)
                    }
                    onBlur={handleBlur}
                    error={touched?.visibility && errors?.visibility}
                    helperText={touched?.visibility && errors?.visibility}
                    isClearable={false}
                    required
                  />
                </Box>

                <Box>
                  <CustomSelect
                    fullWidth
                    name="category"
                    label="Category"
                    placeholder="Select Category"
                    options={identifiers?.SUBSCRIPTION_CATEGORY}
                    value={
                      identifiers?.SUBSCRIPTION_CATEGORY?.find((opt) => {
                        return opt?.value === values?.category;
                      }) || ''
                    }
                    onChange={(selectedOption) =>
                      setFieldValue('category', selectedOption?.value)
                    }
                    onBlur={handleBlur}
                    error={touched?.category && errors?.category}
                    helperText={touched?.category && errors?.category}
                    isClearable={false}
                    required
                  />
                </Box>
                <Box>
                  <CustomSelect
                    fullWidth
                    name="plan_type"
                    label="Plan Type"
                    placeholder="Select Type"
                    options={identifiers?.SUBSCRIPTION_TYPE}
                    value={
                      identifiers?.SUBSCRIPTION_TYPE?.find((opt) => {
                        return opt?.value === values?.plan_type;
                      }) || ''
                    }
                    onChange={(selectedOption) =>
                      setFieldValue('plan_type', selectedOption?.value)
                    }
                    onBlur={handleBlur}
                    error={touched?.plan_type && errors?.plan_type}
                    helperText={touched?.plan_type && errors?.plan_type}
                    isClearable={false}
                    required
                  />
                </Box>
                {values?.category === 'core' && (
                  <Box>
                    <CustomTextField
                      fullWidth
                      name="plan_storage"
                      label="Plan Storage"
                      placeholder="Enter Plan Storage"
                      value={values?.plan_storage}
                      onBlur={handleBlur}
                      onChange={handleChange}
                      error={
                        touched?.plan_storage && Boolean(errors?.plan_storage)
                      }
                      helperText={touched?.plan_storage && errors?.plan_storage}
                      required
                      onInput={(e) => {
                        e.target.value = e?.target?.value.replace(
                          /[^0-9]/g,
                          ''
                        );
                      }}
                    />
                  </Box>
                )}

                <Box className="range-input-wrap">
                  <CustomTextField
                    fullWidth
                    name="min_limit"
                    label="Limit Range"
                    placeholder="Min Limit"
                    value={values?.min_limit}
                    onBlur={handleBlur}
                    onChange={handleChange}
                    error={touched?.min_limit && Boolean(errors?.min_limit)}
                    helperText={touched?.min_limit && errors?.min_limit}
                    required
                    onInput={(e) => {
                      e.target.value = e?.target?.value.replace(/[^0-9]/g, '');
                    }}
                  />

                  <CustomTextField
                    fullWidth
                    name="max_limit"
                    label="&nbsp;"
                    placeholder="Max Limit"
                    value={values?.max_limit}
                    onBlur={handleBlur}
                    onChange={handleChange}
                    error={touched?.max_limit && Boolean(errors?.max_limit)}
                    helperText={touched?.max_limit && errors?.max_limit}
                    onInput={(e) => {
                      e.target.value = e?.target?.value.replace(/[^0-9]/g, '');
                    }}
                  />
                </Box>
                {values?.plan_type === 'recursive' ? (
                  <Box>
                    <CustomTextField
                      fullWidth
                      name="duration"
                      label="Duration (In Days)"
                      placeholder="Enter duration"
                      value={values?.duration}
                      onBlur={handleBlur}
                      onChange={handleChange}
                      error={touched?.duration && Boolean(errors?.duration)}
                      helperText={touched?.duration && errors?.duration}
                      required
                      onInput={(e) => {
                        e.target.value = e?.target?.value.replace(
                          /[^0-9]/g,
                          ''
                        );
                      }}
                    />
                  </Box>
                ) : (
                  <Box>
                    <CustomSelect
                      fullWidth
                      name="is_repeated_plan"
                      label="Repeated Plan"
                      placeholder="Select Repeated Plan"
                      options={identifiers?.YES_NO_OPTIONS}
                      value={
                        identifiers?.YES_NO_OPTIONS?.find((opt) => {
                          return opt?.value === values?.is_repeated_plan;
                        }) || ''
                      }
                      onChange={(selectedOption) =>
                        setFieldValue('is_repeated_plan', selectedOption?.value)
                      }
                      onBlur={handleBlur}
                      error={
                        touched?.is_repeated_plan && errors?.is_repeated_plan
                      }
                      helperText={
                        touched?.is_repeated_plan && errors?.is_repeated_plan
                      }
                      isClearable={false}
                      required
                    />
                  </Box>
                )}
              </Box>

              <Box className="pt16">
                <Box className="custom-textfield-wrapper">
                  <Typography className="field-label" variant="body2">
                    Plan Description
                  </Typography>
                </Box>

                <CustomTextareaAutosize
                  fullWidth
                  name="discription"
                  placeholder="Enter Plan Description"
                  value={values?.discription}
                  onBlur={handleBlur}
                  onChange={handleChange}
                  InputProps={{
                    inputComponent: TextareaAutosize,
                  }}
                  minRows={5}
                  maxRows={10}
                />
              </Box>
              <Box className="pt16">
                <Box className="custom-textfield-wrapper">
                  <Typography className="field-label" variant="body2">
                    Plan Content
                  </Typography>
                </Box>
                <CustomEditor
                  content={values.plan_content}
                  setContent={(content) =>
                    setFieldValue('plan_content', content)
                  }
                  height="300px"
                />
                {/* <CustomTextareaAutosize
                fullWidth
                name="content"
                placeholder="Enter Plan Content"
                value={values?.content}
                onBlur={handleBlur}
                onChange={handleChange}
                InputProps={{
                  inputComponent: TextareaAutosize,
                }}
                minRows={5}
                maxRows={10}
              /> */}
              </Box>
              <Box className="free-trial-wrap pt16">
                <Box className="checkbox-wrap d-flex align-center">
                  <Checkbox
                    className="checkbox-wrap"
                    checked={values?.free_trial}
                    // onChange={handleChange}
                    onChange={(e) =>
                      setFieldValue('free_trial', e?.target?.checked)
                    }
                    name="free_trial"
                    color="primary"
                  />
                  <Typography className="set-as-default" component="p">
                    Set as default for free trial
                  </Typography>
                </Box>
              </Box>
              <Box className="pt32 d-flex justify-end">
                <CustomButton
                  type="submit"
                  variant="contained"
                  title={planId ? ' Update plan' : 'Create plan'}
                />
              </Box>
            </Form>
          );
        }}
      </Formik>
    </Box>
  );
}
